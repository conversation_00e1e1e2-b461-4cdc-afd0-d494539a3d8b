package io.wyden.oems.ordergateway.service.tracking;

import io.wyden.cloudutils.rabbitmq.destination.OemsTarget;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.oems.ordergateway.service.referencedata.InstrumentsRepository;
import io.wyden.oems.ordergateway.service.state.ClientOrderContext;
import io.wyden.published.client.ClientExecRestatementReason;
import io.wyden.published.client.ClientInstrumentType;
import io.wyden.published.client.ClientOrderCategory;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.published.client.FeeBasis;
import io.wyden.published.client.FeeData;
import io.wyden.published.client.FeeType;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsExecRestatementReason;
import io.wyden.published.oems.OemsInstrumentType;
import io.wyden.published.oems.OemsOrderCategory;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.oems.OemsTIF;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.VenueType;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Nullable;

import static io.wyden.oems.ordergateway.service.client.inbound.ClientRequestUtils.isStreetSideSOROrder;
import static org.apache.commons.lang3.StringUtils.EMPTY;

public class ClientOemsMapper {

    private static final String ORDER_GATEWAY = "OrderGateway";

    private final InstrumentsRepository instrumentsRepository;

    public ClientOemsMapper(InstrumentsRepository instrumentsRepository) {
        this.instrumentsRepository = instrumentsRepository;
    }

    public OemsRequest toOemsOrderSingle(ClientOrderState orderState) {
        OemsRequest.Builder builder = OemsRequest.newBuilder();

        ClientRequest clientOrder = orderState.getOrder();

        BigDecimal clientOrderQty = new BigDecimal(clientOrder.getQuantity());
        BigDecimal alreadyFilled = new BigDecimal(orderState.getFilledQuantity());

        Instrument instrument = instrumentsRepository.find(clientOrder.getInstrumentId());
        if (instrument != null) {
            String baseCurrency = instrument.getForexSpotProperties().getBaseCurrency();
            String quoteCurrency = instrument.getBaseInstrument().getQuoteCurrency();
            builder
                .setBaseCurrency(baseCurrency)
                .setQuoteCurrency(quoteCurrency);
        }

        Metadata metadata = clientOrder.getMetadata().toBuilder()
            .setSource(ORDER_GATEWAY)
            .setTarget(resolveTarget(clientOrder, instrument))
            .setTargetType(resolveTargetType(clientOrder, instrument))
            .setRequestId(UUID.randomUUID().toString())
            .setRequesterId(ORDER_GATEWAY)
            .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .build();

        builder.setOrderCategory(resolveOrderCategory(metadata.getTargetType()));

        OemsRequest.OemsPTCStatus ptc = resolvePtc(instrument);

        String venueAccount = resolveVenueAccount(clientOrder, instrument);

        List<String> venueAccounts = resolveVenueAccounts(clientOrder);

        String orderId = clientOrder.getOrderId();

        return builder
            .setMetadata(metadata)
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setRootOrderId(orderId)
            .setClientRootOrderId(clientOrder.getClOrderId())
            .setVenueAccount(venueAccount)
            .addAllVenueAccounts(venueAccounts)
            .setInstrumentType(toOems(clientOrder.getInstrumentType()))
            .setInstrumentId(clientOrder.getInstrumentId())
            .setSymbol(clientOrder.getSymbol())
            .setClientId(clientOrder.getClientId())
            .setOrderType(toOems(clientOrder.getOrderType()))
            .setSide(toOems(clientOrder.getSide()))
            .setQuantity(clientOrderQty.subtract(alreadyFilled).toString())
            .setPrice(clientOrder.getPrice())
            .setStopPrice(clientOrder.getStopPrice())
            .setTif(toOems(clientOrder.getTif()))
            .setExpireTime(clientOrder.getExpireTime())
            .setPostOnly(clientOrder.getPostOnly())
            .setPortfolioId(clientOrder.getPortfolioId())
            .setPtc(ptc)
            .setPreReplaceOrderId(ObjectUtils.firstNonNull(orderState.getPreReplaceOrderId(), EMPTY))
            .setCurrency(clientOrder.getCurrency())
            .setAvailableBalanceAmount(clientOrder.getAvailableBalanceAmount())
            .setAvailableBalanceCurrency(clientOrder.getAvailableBalanceCurrency())
            .addAllExcludedAccountId(clientOrder.getExcludedAccountIdList())
            .build();
    }

    public OemsRequest toOemsCancel(ClientRequest cancelOrder, String orderId, ClientOrderContext ctx) {
        OemsRequest.Builder builder = OemsRequest.newBuilder();

        ClientRequest originalOrder = ctx.getPersistentState().getOrder();
        Instrument instrument = instrumentsRepository.find(ctx.getPersistentState().getOrder().getInstrumentId());
        if (instrument != null) {
            String baseCurrency = instrument.getForexSpotProperties().getBaseCurrency();
            String quoteCurrency = instrument.getBaseInstrument().getQuoteCurrency();
            builder
                .setBaseCurrency(baseCurrency)
                .setQuoteCurrency(quoteCurrency);
        }

        // TODO AC-2029
        //  - as soon as Order submit confirmation comes back in OemsResponse, we should get the target resolved and save it in the context
        //  - then we should set it on cancel requests so that we can omit config service and go to target directly.
        //  It may happen that client sends cancel early (before OemsResponse comes back) - in that case this logic is still needed
        String target = resolveTarget(originalOrder, instrument);

        Metadata metadata = originalOrder.getMetadata().toBuilder()
            .setRequestId(UUID.randomUUID().toString())
            .setRequesterId(ORDER_GATEWAY)
            .setSource(ORDER_GATEWAY)
            .setTarget(target)
            .setTargetType(resolveTargetType(originalOrder, instrument))
            .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .build();

        String venueAccount = resolveVenueAccount(originalOrder, instrument);

        return builder
            .setRequestType(OemsRequest.OemsRequestType.CANCEL)
            .setMetadata(metadata)
            .setOrderId(orderId)
            .setRootOrderId(orderId)
            .setVenueAccount(venueAccount)
            .setPortfolioId(originalOrder.getPortfolioId())
            .setInstrumentType(toOems(originalOrder.getInstrumentType()))
            .setInstrumentId(originalOrder.getInstrumentId())
            .setSymbol(originalOrder.getSymbol())
            .setClientId(cancelOrder.getClientId())
            .setForceCancel(cancelOrder.getForceCancel())
            .setOrderType(toOems(originalOrder.getOrderType()))
            .setSide(toOems(originalOrder.getSide()))
            .setPtc(OemsRequest.OemsPTCStatus.NOT_REQUIRED)
            .build();
    }

    private static String resolveVenueAccount(ClientRequest clientOrder, Instrument instrument) {
        if (isClientSideTrading(instrument)) {
            return EMPTY;
        }

        if (isStreetSideSOROrder(clientOrder)) {
            return EMPTY;
        }

        if (clientOrder.getVenueAccountsCount() == 1) {
            return clientOrder.getVenueAccounts(0);
        }

        // TODO - fallback to legacy value, remove in AC-5028
        return clientOrder.getTarget();
    }

    private static List<String> resolveVenueAccounts(ClientRequest clientOrder) {
        if (isStreetSideSOROrder(clientOrder)) {
            return clientOrder.getVenueAccountsList();
        }

        return List.of();
    }

    private static OemsRequest.OemsPTCStatus resolvePtc(Instrument instrument) {
        if (isClientSideTrading(instrument) || isClobTrading(instrument)) {
            return OemsRequest.OemsPTCStatus.NOT_REQUIRED; // has to enter config service first
        } else {
            return OemsRequest.OemsPTCStatus.REQUIRED;
        }
    }

    private static String resolveTarget(ClientRequest order, Instrument instrument) {
        if (isClientSideTrading(instrument) || isClobTrading(instrument)) {
            return OemsTarget.NOT_CONFIGURED.getTarget(); // specific target will be configured after it goes through broker config service
        } else if (isStreetSideSOROrder(order)) {
            return OemsTarget.SOR.getTarget();
        } else if (order.getVenueAccountsCount() == 1) {
            return order.getVenueAccounts(0);
        } else {
            // TODO - fallback to legacy field to be removed in AC-5028
            return order.getTarget();
        }
    }

    private static Metadata.ServiceType resolveTargetType(ClientRequest clientOrder, Instrument instrument) {
        if (isClientSideTrading(instrument) || isClobTrading(instrument)) {
            return Metadata.ServiceType.BROKER_DESK;
        } else if (isStreetSideSOROrder(clientOrder)) {
            return Metadata.ServiceType.SOR;
        } else {
            return Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT;
        }
    }

    private OemsOrderCategory resolveOrderCategory(Metadata.ServiceType targetType) {
        return switch (targetType) {
            case BROKER_DESK -> OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED; // resolved to AGENCY/PRINCIPAL by broker config service later
            case SOR -> OemsOrderCategory.SOR_ORDER;
            case EXTERNAL_VENUE_ACCOUNT -> OemsOrderCategory.DIRECT_MARKET_ACCESS_ORDER;

            case ALGO -> OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED; // unused
            case CLOB -> OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED; // it's not possible to route directly to clob (only through agency), unused

            case AUTO_HEDGER -> OemsOrderCategory.AUTO_HEDGING_ORDER;
            case QUOTING_ORDER_SERVICE -> OemsOrderCategory.CLOB_QUOTING_ORDER;
            case TARGET_TYPE_UNSPECIFIED, UNRECOGNIZED -> OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED;
        };
    }

    private static boolean isClientSideTrading(@Nullable Instrument instrument) {
        return instrument != null && instrument.getBaseInstrument().getVenueType().equals(VenueType.CLIENT);
    }

    private static boolean isClobTrading(@Nullable Instrument instrument) {
        return instrument != null && instrument.getBaseInstrument().getVenueType().equals(VenueType.CLOB);
    }

    public static OemsSide toOems(ClientSide side) {
        return OemsSide.forNumber(side.getNumber());
    }

    public static OemsTIF toOems(ClientTIF tif) {
        return OemsTIF.forNumber(tif.getNumber());
    }

    public static OemsInstrumentType toOems(ClientInstrumentType instrumentType) {
        return OemsInstrumentType.forNumber(instrumentType.getNumber());
    }

    public static OemsOrderType toOems(ClientOrderType orderType) {
        return OemsOrderType.forNumber(orderType.getNumber());
    }

    public static Iterable<FeeData> asClientSide(List<io.wyden.published.oems.FeeData> feeDataList) {
        return feeDataList.stream()
            .map(oemsData -> FeeData.newBuilder()
                .setAmount(oemsData.getAmount())
                .setCurrency(oemsData.getCurrency())
                .setBasis(FeeBasis.valueOf(oemsData.getBasis().name()))
                .setType(FeeType.valueOf(oemsData.getType().name()))
                .setDescription(oemsData.getDescription())
                .build())
            .collect(Collectors.toList());
    }

    public static ClientExecRestatementReason asClientSide(OemsExecRestatementReason restatementReason) {
        return ClientExecRestatementReason.forNumber(restatementReason.getNumber());
    }

    public static @NotNull ClientOrderCategory asClientSide(OemsOrderCategory category) {
        return switch (category) {
            case AUTO_HEDGING_ORDER -> ClientOrderCategory.AUTO_HEDGER_STREET_ORDER;
            case ORDER_CATEGORY_UNSPECIFIED, UNRECOGNIZED -> ClientOrderCategory.ORDER_CATEGORY_UNSPECIFIED;
            case DIRECT_MARKET_ACCESS_ORDER -> ClientOrderCategory.DIRECT_MARKET_ACCESS_ORDER;
            case SOR_ORDER -> ClientOrderCategory.SOR_ORDER;
            case SOR_CHILD_ORDER -> ClientOrderCategory.SOR_CHILD_ORDER;
            case AGENCY_ORDER -> ClientOrderCategory.AGENCY_ORDER;
            case AGENCY_STREET_ORDER -> ClientOrderCategory.AGENCY_STREET_ORDER;
            case AGENCY_SOR_ORDER -> ClientOrderCategory.AGENCY_SOR_ORDER;
            case AGENCY_CLOB_ORDER -> ClientOrderCategory.AGENCY_CLOB_ORDER;
            case CLOB_QUOTING_ORDER -> ClientOrderCategory.CLOB_QUOTING_ORDER;
            case CLOB_EXTERNAL_HEDGE_ORDER -> ClientOrderCategory.CLOB_EXTERNAL_HEDGE_ORDER;
        };
    }

    public static boolean isKnownRestatementReason(OemsExecRestatementReason restatementReason) {
        return restatementReason != OemsExecRestatementReason.UNRECOGNIZED && restatementReason != OemsExecRestatementReason.EXEC_RESTATEMENT_REASON_UNSPECIFIED;
    }
}
