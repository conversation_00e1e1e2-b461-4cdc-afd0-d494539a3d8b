package io.wyden.oems.ordergateway.service.client.inbound.processor;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.oems.ordergateway.service.tracking.ClientOrderCache;
import io.wyden.oems.ordergateway.service.tracking.OrderService;
import io.wyden.published.client.ClientRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static io.wyden.oems.ordergateway.infrastructure.telemetry.Meters.AccessType.TRADE;
import static io.wyden.oems.ordergateway.infrastructure.telemetry.Meters.securityCheckLatencyTimer;
import static io.wyden.oems.ordergateway.service.client.outbound.ExecutionReportFactory.ACCESS_DENIED;
import static io.wyden.published.client.ClientRequestType.CANCEL_REPLACE;

@Component
public class CancelReplaceProcessor extends RequestProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(CancelReplaceProcessor.class);

    private final OrderService orderService;
    private final MeterRegistry meterRegistry;

    protected CancelReplaceProcessor(OrderService orderService,
                                     ClientOrderCache clientOrderCache,
                                     Telemetry telemetry) {
        super(orderService, CANCEL_REPLACE, clientOrderCache);
        this.orderService = orderService;
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    @Override
    protected void process(ClientRequest request) {
        orderService.onCancelReplaceRequest(request);
    }

    @Override
    protected boolean hasPermissions(ClientRequest request) {
        return recordLatencyIn(latencyTimer()).of(() -> {
            return true;
        });
    }

    @Override
    protected void noAccessHandler(ClientRequest request) {
        orderService.onCancelReject(request, ACCESS_DENIED);
    }

    @Override
    protected boolean isValid(ClientRequest request) {
        ClientOrderState orderState = getOrderState(request.getOrigOrderId());
        return request.getPortfolioId().equals(orderState.getOrder().getPortfolioId()) &&
            request.getVenueAccountsList().equals(orderState.getOrder().getVenueAccountsList()) &&
            request.getInstrumentId().equals(orderState.getOrder().getInstrumentId()) &&
            request.getSide().equals(orderState.getOrder().getSide());
    }

    @Override
    protected void isNotValidHandler(ClientRequest request) {
        ClientOrderState orderState = getOrderState(request.getOrigOrderId());
        orderService.onCancelReject(request, orderState, "Invalid request - portfolioId, venueAccount, instrumentId and side must match replaced order");
    }

    private Timer latencyTimer() {
        try {
            return securityCheckLatencyTimer(this.meterRegistry, TRADE);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}
