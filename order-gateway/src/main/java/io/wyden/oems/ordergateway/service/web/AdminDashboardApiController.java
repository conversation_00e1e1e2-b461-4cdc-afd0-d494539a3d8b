package io.wyden.oems.ordergateway.service.web;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.ParametersAreNonnullByDefault;

@Tag(name = "Order gateway api")
@RestController
@RequestMapping("/admin/api")
@ParametersAreNonnullByDefault
public class AdminDashboardApiController {

    private static final String RESPONSE_CANCELLED = """
        {
          "metadata": {
            "created_at": "2025-03-24T13:41:19.528980Z",
            "response_id": "1f3279f7-1a66-4108-a192-5828b0be8f25",
            "source": "agency",
            "source_type": "BROKER_DESK",
            "target": "OrderGateway",
            "in_response_to_request_id": "f09fac08-df33-4da2-9bd8-d2912c81ad46",
            "in_response_to_requester_id": "OrderGateway"
          },
          "order_id": "8ae7ded5-0ec3-4e24-83b7-87097ee8a9a2",
          "response_type": "EXECUTION_REPORT",
          "counter_portfolio_id": "666f319f-f072-4d91-ae7d-95e39a4b35df",
          "execution_id": "3e6b610c-d2a7-4b65-a5fc-7c4079c97c59",
          "exec_type": "REJECTED",
          "order_status": "STATUS_REJECTED",
          "instrument_type": "FOREX",
          "side": "SELL",
          "instrumentId": "BTCUSDT@FOREX@Bank",
          "order_qty": "0.00010",
          "last_qty": "0",
          "leaves_qty": "0",
          "cum_qty": "0",
          "last_price": "0",
          "avg_price": "0",
          "client_id": "admin",
          "base_currency": "BTC",
          "quote_currency": "USDT",
          "portfolio_id": "221532ba-ac90-4b0c-8435-a2364277a1ba",
          "system_timestamp": "2025-03-24T13:41:19.528928Z",
          "request_result": "OEMS_ORDER_REGISTRATION_ERROR_VALIDATION",
          "root_order_id": "8ae7ded5-0ec3-4e24-83b7-87097ee8a9a2",
          "client_root_order_id": "a6378abc-3350-4081-bc70-d4b0cd17f542",
          "tif": "IOC",
          "order_category": "AGENCY_ORDER"
        }
        """;
    private static final String RESPONSE_FILLED = """
        {
           "metadata": {
             "created_at": "2025-03-24T13:45:58.755841Z",
             "response_id": "32bd4d76-bdf9-43fb-8978-43a70e701a97",
             "source": "agency",
             "source_type": "BROKER_DESK",
             "target": "OrderGateway",
             "in_response_to_request_id": "c7c3a706-c4ed-4ddd-b634-f6096cec335d",
             "in_response_to_requester_id": "OrderGateway"
           },
           "order_id": "1317d960-e3de-48ea-906a-873cc46d5f09",
           "response_type": "EXECUTION_REPORT",
           "counter_portfolio_id": "666f319f-f072-4d91-ae7d-95e39a4b35df",
           "execution_id": "6a389eeb-3e17-4b8b-a5ed-4abbcd460235",
           "exec_type": "FILL",
           "order_status": "STATUS_FILLED",
           "instrument_type": "FOREX",
           "side": "SELL",
           "instrumentId": "BTCUSDT@FOREX@Bank",
           "order_qty": "0.00010",
           "last_qty": "0.0001",
           "leaves_qty": "0.00000",
           "cum_qty": "0.0001",
           "last_price": "88500.0000",
           "avg_price": "88500.0000",
           "client_id": "admin",
           "base_currency": "BTC",
           "quote_currency": "USDT",
           "portfolio_id": "221532ba-ac90-4b0c-8435-a2364277a1ba",
           "fee_currency": "USDT",
           "venue_execution_id": "********-006d-1000-0000-00009a716754",
           "system_timestamp": "2025-03-24T13:45:58.464739Z",
           "venue_timestamp": "2025-03-24T13:45:59.337000Z",
           "root_order_id": "1317d960-e3de-48ea-906a-873cc46d5f09",
           "client_root_order_id": "edf5f443-d286-43ce-8828-b632682cfb39",
           "underlying_execution": {
             "order_id": "********-524f-4e11-a7df-4543c39b0dfe",
             "execution_id": "41925f2e-823d-49c2-b59f-50d3f61d105f",
             "venue_account": "bitmex-testnet1",
             "instrument_id": "BTCUSDT@FOREX@BitMEX"
           },
           "root_execution": {
             "order_id": "********-524f-4e11-a7df-4543c39b0dfe",
             "execution_id": "41925f2e-823d-49c2-b59f-50d3f61d105f",
             "venue_account": "bitmex-testnet1",
             "instrument_id": "BTCUSDT@FOREX@BitMEX"
           },
           "tif": "IOC",
           "order_category": "AGENCY_ORDER"
        }
        """;
    private static final String RESPONSE_NEW = """
        {
            "metadata": {
              "created_at": "2025-03-24T12:22:22.950673Z",
              "response_id": "de03d87e-4d93-48f9-9d32-7d92f8986dd5",
              "source": "agency",
              "source_type": "BROKER_DESK",
              "target": "OrderGateway",
              "in_response_to_request_id": "984474e7-ccc9-45b2-8602-1dc685021d26",
              "in_response_to_requester_id": "OrderGateway"
            },
            "order_id": "c4aff364-7427-4ceb-bfaa-c727bd627820",
            "response_type": "EXECUTION_REPORT",
            "counter_portfolio_id": "666f319f-f072-4d91-ae7d-95e39a4b35df",
            "execution_id": "ec5f4a1c-be7e-4b2e-af0d-55c3f9e34a7a",
            "exec_type": "NEW",
            "order_status": "STATUS_NEW",
            "instrument_type": "FOREX",
            "side": "SELL",
            "instrumentId": "BTCUSDT@FOREX@Bank",
            "order_qty": "0.00010",
            "last_qty": "0",
            "leaves_qty": "0.00010",
            "cum_qty": "0",
            "last_price": "0",
            "avg_price": "0",
            "client_id": "admin",
            "base_currency": "BTC",
            "quote_currency": "USDT",
            "portfolio_id": "221532ba-ac90-4b0c-8435-a2364277a1ba",
            "system_timestamp": "2025-03-24T12:22:22.883597Z",
            "venue_timestamp": "2025-03-24T12:22:23.618000Z",
            "root_order_id": "c4aff364-7427-4ceb-bfaa-c727bd627820",
            "client_root_order_id": "6b853fa0-6571-462d-a496-6c3f22bc3f1e",
            "tif": "GTC",
            "order_category": "AGENCY_ORDER"
          }
        """;

    static final String RESPONSE_CANCEL_REJECTED = """
        {
          "metadata": {
            "created_at": "2025-03-24T14:08:15.984305Z",
            "response_id": "11a198c7-d3a2-4df8-8b05-d93cf16656f5",
            "source": "e2e-ftx-f9c46126",
            "source_type": "EXTERNAL_VENUE_ACCOUNT",
            "target": "OrderGateway",
            "in_response_to_request_id": "4efbdcf7-ab34-4707-84d1-649ce3bd7f85"
          },
          "order_id": "52b1bb0f-e0e6-4827-8fc7-07b9a4ba0ee6",
          "response_type": "CANCEL_REJECT",
          "venue_account": "e2e-ftx-f9c46126",
          "execution_id": "f59f18a1-7076-49a1-ad04-cb6ec0818c53",
          "order_status": "STATUS_NEW",
          "instrumentId": "BTCUSD@FOREX@WydenMock",
          "client_id": "e2e-maxwell_stroman_d7d8",
          "request_result": "CONNECTOR_ERROR",
          "reason": "cancel timeout",
          "root_order_id": "52b1bb0f-e0e6-4827-8fc7-07b9a4ba0ee6",
          "client_root_order_id": "4afd869c-7400-44f6-af97-b2ef14d92f53",
          "tif": "GTC"
        }
        """;
    private final AdminDashboardApiService orderGatewayApiService;

    public AdminDashboardApiController(AdminDashboardApiService orderGatewayApiService) {
        this.orderGatewayApiService = orderGatewayApiService;
    }

    @Operation(summary = "Resend last OemsRequest")
    @PostMapping("/order/{orderId}/resend")
    public void resendOrder(@PathVariable String orderId) {
        orderGatewayApiService.resendClientOrder(orderId);
    }

    @Operation(summary = "Resend last execution report / cancel reject")
    @PostMapping("/order/{orderId}/execution-report/resend-last")
    public void resendLastExecutionReport(@PathVariable String orderId) {
        orderGatewayApiService.resendLastExecutionReport(orderId);
    }

    @Operation(summary = "Cancel order by orderId")
    @PostMapping("/order/{orderId}/cancel")
    public void cancelOrder(@PathVariable String orderId) {
        orderGatewayApiService.cancelOrder(orderId, false);
    }

    @Operation(summary = "Force cancel order by orderId")
    @PostMapping("/order/{orderId}/force-cancel")
    public void forceCancelOrder(@PathVariable String orderId) {
        orderGatewayApiService.cancelOrder(orderId, true);
    }

    @Operation(summary = "Emulate incoming OemsResponse (as JSON)")
    @PostMapping("/execution-report/emulate")
    public void emulateExecutionReport(@io.swagger.v3.oas.annotations.parameters.RequestBody(
        content = @Content(
            examples = {
                @ExampleObject(
                    name = "OemsResponse - new",
                    summary = "NEW",
                    value = RESPONSE_NEW
                ),
                @ExampleObject(
                    name = "OemsResponse - filled",
                    summary = "FILLED",
                    value = RESPONSE_FILLED
                ),
                @ExampleObject(
                    name = "OemsResponse - cancelled",
                    summary = "CANCELLED",
                    value = RESPONSE_CANCELLED
                ),
                @ExampleObject(
                    name = "OemsResponse - cancel rejected",
                    summary = "CANCEL_REJECTED",
                    value = RESPONSE_CANCEL_REJECTED
                )
            }))
    @RequestBody String oemsResponse) {
        orderGatewayApiService.emulateOemsResponse(oemsResponse);
    }

    @Operation(summary = "Emulate cancel reject for orders in PENDING_CANCEL state")
    @PostMapping("/order/{orderId}/reject-pending-cancel")
    public void rejectPendingCancelOrder(@PathVariable String orderId) {
        orderGatewayApiService.rejectPendingCancelOrder(orderId);
    }

}
