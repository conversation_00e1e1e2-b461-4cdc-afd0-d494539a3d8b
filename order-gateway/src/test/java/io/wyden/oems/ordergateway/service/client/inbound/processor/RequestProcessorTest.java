package io.wyden.oems.ordergateway.service.client.inbound.processor;

import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.oems.ordergateway.service.tracking.ClientOrderCache;
import io.wyden.oems.ordergateway.service.tracking.OrderService;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientRequestType;
import io.wyden.published.client.ClientSide;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RequestProcessorTest {

    private static final String PORTFOLIO_ID_1 = "portfolioId_1";
    private static final String PORTFOLIO_ID_2 = "portfolioId_2";
    private static final String VENUE_ACCOUNT_1 = "target_1";
    private static final String VENUE_ACCOUNT_2 = "target_2";
    private static final String INSTRUMENT_ID_1 = "instrumentId_1";
    private static final String INSTRUMENT_ID_2 = "instrumentId_2";
    private static final String SYMBOL = "symbol";
    private static final List<String> VENUE_ACCOUNTS_LIST = List.of("va1", "va2");
    private static final String ORDER_ID = "orderId";

    private final OrderService orderService = mock(OrderService.class);
    private final ClientOrderCache clientOrderCache = mock(ClientOrderCache.class);
    private final RequestProcessor singleOrderProcessor = new SingleOrderProcessor(orderService, clientOrderCache, mock());
    private final RequestProcessor cancelReplaceProcessor = new CancelReplaceProcessor(orderService, clientOrderCache, mock());

    @Test
    public void getVenueAccountsAndPortfolio_streetSideSorOrder() {
        ClientRequest clientRequest = ClientRequest.newBuilder()
            .setSymbol(SYMBOL)
            .addAllVenueAccounts(VENUE_ACCOUNTS_LIST)
            .setPortfolioId(PORTFOLIO_ID_1)
            .build();

        List<String> venueAccounts = singleOrderProcessor.getVenueAccounts(clientRequest);

        assertThat(venueAccounts).hasSize(2);
        assertThat(venueAccounts).hasSameElementsAs(VENUE_ACCOUNTS_LIST);
    }

    @Test
    public void getVenueAccountsAndPortfolio_blankTarget() {
        ClientRequest clientRequest = ClientRequest.newBuilder()
            .setInstrumentId(INSTRUMENT_ID_1)
            .setPortfolioId(PORTFOLIO_ID_1)
            .build();

        List<String> venueAccounts = singleOrderProcessor.getVenueAccounts(clientRequest);

        assertThat(venueAccounts).hasSize(0);
    }

    @Test
    public void getVenueAccountsAndPortfolio_nonBlankTarget() {
        ClientRequest clientRequest = ClientRequest.newBuilder()
            .setInstrumentId(INSTRUMENT_ID_1)
            .addVenueAccounts(VENUE_ACCOUNT_1)
            .setPortfolioId(PORTFOLIO_ID_1)
            .build();

        List<String> venueAccounts = singleOrderProcessor.getVenueAccounts(clientRequest);

        assertThat(venueAccounts).hasSize(1);
        assertThat(venueAccounts.get(0)).isEqualTo(VENUE_ACCOUNT_1);
    }

    @Test
    public void getVenueAccountsAndPortfolio_byOrderId_streetSideSorOrder() {
        when(clientOrderCache.find(ORDER_ID)).thenReturn(Optional.of(ClientOrderState.newBuilder()
            .setClientRequest(ClientRequest.newBuilder().addAllVenueAccounts(VENUE_ACCOUNTS_LIST).build())
            .setOrder(ClientRequest.newBuilder().setSymbol(SYMBOL).setPortfolioId(PORTFOLIO_ID_1).addAllVenueAccounts(VENUE_ACCOUNTS_LIST).build()).build()));

        List<String> venueAccounts = singleOrderProcessor.getVenueAccounts(ORDER_ID);

        assertThat(venueAccounts).hasSize(2);
        assertThat(venueAccounts).hasSameElementsAs(VENUE_ACCOUNTS_LIST);
    }

    @Test
    public void getVenueAccountsAndPortfolio_byOrderId_absentVenueAccount() {
        when(clientOrderCache.find(ORDER_ID)).thenReturn(Optional.of(ClientOrderState.newBuilder()
            .setClientRequest(ClientRequest.newBuilder()
                .setInstrumentId(INSTRUMENT_ID_1)
                .build())
            .setOrder(ClientRequest.newBuilder().setPortfolioId(PORTFOLIO_ID_1).build()).build()));

        List<String> venueAccounts = singleOrderProcessor.getVenueAccounts(ORDER_ID);

        assertThat(venueAccounts).hasSize(0);
    }

    @Test
    public void getVenueAccountsAndPortfolio_byOrderId_nonBlankVenueAccount() {
        when(clientOrderCache.find(ORDER_ID)).thenReturn(Optional.of(ClientOrderState.newBuilder()
            .setClientRequest(ClientRequest.newBuilder()
                .setInstrumentId(INSTRUMENT_ID_1)
                .setPortfolioId(PORTFOLIO_ID_1)
                .addVenueAccounts(VENUE_ACCOUNT_1)
                .build())
            .setOrder(ClientRequest.newBuilder()
                .setInstrumentId(INSTRUMENT_ID_1)
                .setPortfolioId(PORTFOLIO_ID_1)
                .addVenueAccounts(VENUE_ACCOUNT_1)
                .build())
            .build()));

        List<String> venueAccounts = singleOrderProcessor.getVenueAccounts(ORDER_ID);

        assertThat(venueAccounts).hasSize(1);
        assertThat(venueAccounts.get(0)).isEqualTo(VENUE_ACCOUNT_1);
    }

    @Test
    public void shouldReturnInvalidCancelReplaceRequest_portfolioIdChanged() {
        // given
        ClientRequest cancelReplaceRequest = ClientRequest.newBuilder()
            .setRequestType(ClientRequestType.CANCEL_REPLACE)
            .setOrigOrderId(ORDER_ID)
            .setInstrumentId(INSTRUMENT_ID_1)
            .setPortfolioId(PORTFOLIO_ID_2)
            .addVenueAccounts(VENUE_ACCOUNT_1)
            .setSide(ClientSide.BUY)
            .build();

        when(clientOrderCache.find(ORDER_ID)).thenReturn(Optional.of(ClientOrderState.newBuilder()
            .setClientRequest(ClientRequest.newBuilder()
                .setInstrumentId(INSTRUMENT_ID_1)
                .build())
            .setOrder(ClientRequest.newBuilder()
                .setInstrumentId(INSTRUMENT_ID_1)
                .setPortfolioId(PORTFOLIO_ID_1)
                .addVenueAccounts(VENUE_ACCOUNT_1)
                .setSide(ClientSide.BUY)
                .build())
            .build()));

        // when
        boolean valid = cancelReplaceProcessor.isValid(cancelReplaceRequest);

        // then
        assertThat(valid).isFalse();
    }

    @Test
    public void shouldReturnInvalidCancelReplaceRequest_venueAccountChanged() {
        // given
        ClientRequest cancelReplaceRequest = ClientRequest.newBuilder()
            .setRequestType(ClientRequestType.CANCEL_REPLACE)
            .setOrigOrderId(ORDER_ID)
            .setInstrumentId(INSTRUMENT_ID_1)
            .setPortfolioId(PORTFOLIO_ID_1)
            .addVenueAccounts(VENUE_ACCOUNT_2)
            .setSide(ClientSide.BUY)
            .build();

        when(clientOrderCache.find(ORDER_ID)).thenReturn(Optional.of(ClientOrderState.newBuilder()
            .setClientRequest(ClientRequest.newBuilder()
                .setInstrumentId(INSTRUMENT_ID_1)
                .build())
            .setOrder(ClientRequest.newBuilder()
                .setInstrumentId(INSTRUMENT_ID_1)
                .setPortfolioId(PORTFOLIO_ID_1)
                .addVenueAccounts(VENUE_ACCOUNT_1)
                .setSide(ClientSide.BUY)
                .build())
            .build()));

        // when
        boolean valid = cancelReplaceProcessor.isValid(cancelReplaceRequest);

        // then
        assertThat(valid).isFalse();
    }

    @Test
    public void shouldReturnInvalidCancelReplaceRequest_instrumentIdChanged() {
        // given
        ClientRequest cancelReplaceRequest = ClientRequest.newBuilder()
            .setRequestType(ClientRequestType.CANCEL_REPLACE)
            .setOrigOrderId(ORDER_ID)
            .setInstrumentId(INSTRUMENT_ID_2)
            .setPortfolioId(PORTFOLIO_ID_1)
            .addVenueAccounts(VENUE_ACCOUNT_1)
            .setSide(ClientSide.BUY)
            .build();

        when(clientOrderCache.find(ORDER_ID)).thenReturn(Optional.of(ClientOrderState.newBuilder()
            .setClientRequest(ClientRequest.newBuilder()
                .setInstrumentId(INSTRUMENT_ID_1)
                .build())
            .setOrder(ClientRequest.newBuilder()
                .setInstrumentId(INSTRUMENT_ID_1)
                .setPortfolioId(PORTFOLIO_ID_1)
                .addVenueAccounts(VENUE_ACCOUNT_1)
                .setSide(ClientSide.BUY)
                .build())
            .build()));

        // when
        boolean valid = cancelReplaceProcessor.isValid(cancelReplaceRequest);

        // then
        assertThat(valid).isFalse();
    }

    @Test
    public void shouldReturnInvalidCancelReplaceRequest_sideChanged() {
        // given
        ClientRequest cancelReplaceRequest = ClientRequest.newBuilder()
            .setRequestType(ClientRequestType.CANCEL_REPLACE)
            .setOrigOrderId(ORDER_ID)
            .setInstrumentId(INSTRUMENT_ID_1)
            .setPortfolioId(PORTFOLIO_ID_1)
            .addVenueAccounts(VENUE_ACCOUNT_1)
            .setSide(ClientSide.SELL)
            .build();

        when(clientOrderCache.find(ORDER_ID)).thenReturn(Optional.of(ClientOrderState.newBuilder()
            .setClientRequest(ClientRequest.newBuilder()
                .setInstrumentId(INSTRUMENT_ID_1)
                .build())
            .setOrder(ClientRequest.newBuilder()
                .setInstrumentId(INSTRUMENT_ID_1)
                .setPortfolioId(PORTFOLIO_ID_1)
                .addVenueAccounts(VENUE_ACCOUNT_1)
                .setSide(ClientSide.BUY)
                .build())
            .build()));

        // when
        boolean valid = cancelReplaceProcessor.isValid(cancelReplaceRequest);

        // then
        assertThat(valid).isFalse();
    }
}
