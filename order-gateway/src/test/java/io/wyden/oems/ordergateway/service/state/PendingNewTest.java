package io.wyden.oems.ordergateway.service.state;

import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.oems.ordergateway.service.referencedata.InstrumentsRepository;
import io.wyden.oems.ordergateway.service.tracking.ClientOrderCache;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientRequestType;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

@ExtendWith(MockitoExtension.class)
class PendingNewTest {

    private static final String VENUE = "Test";

    private State underTest;

    private ClientRequest originalClientRequest;

    private ClientOrderContext ctx;

    private ClientOrderCache clientOrderCache;

    private InstrumentsRepository instrumentsRepository;

    @BeforeEach
    void setUp() {
        originalClientRequest = ClientRequest.newBuilder()
            .setRequestType(ClientRequestType.ORDER_SINGLE)
            .setInstrumentId("BTCUSD")
            .setQuantity("10")
            .setOrderId("1")
            .setClientId("client")
            .setClOrderId("c1")
            .setOrderType(ClientOrderType.MARKET)
            .build();

        underTest = new PendingNew();

        ClientOrderState persistentState = PendingNew.createPersistentState(originalClientRequest, InstrumentsRepository.buildInstrument("BTC", "USD", VENUE));

        clientOrderCache = mock(ClientOrderCache.class);
        instrumentsRepository = mock(InstrumentsRepository.class);
        doReturn(InstrumentsRepository.buildInstrument("BTC", "USD", VENUE)).when(instrumentsRepository).find(any());

        ctx = ClientOrderContext.rebuild(underTest, clientOrderCache, persistentState, instrumentsRepository);
    }

    @Nested
    class FromClientRequest {
        @Test
        void shouldPersistClientRequestDetails() {
            ClientOrderState persistentState = PendingNew.createPersistentState(originalClientRequest, InstrumentsRepository.buildInstrument("BTC", "USD", VENUE));
            assertThat(persistentState.getOrder()).isEqualTo(originalClientRequest);
        }
    }

    @Nested
    class OnOemsRequestSendingFailure {
        @Test
        void shouldMoveToRejected() {
            underTest.onOemsRequestSendingFailure(ctx);

            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.REJECTED);
        }
    }

    @Nested
    class OnOemsRequestSent {

        private State transitionedState;

        @BeforeEach
        void setUp() {
            transitionedState = underTest.onOemsRequestSent(OemsRequest.newBuilder()
                .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
                .setOrderId("oems-1")
                .build(), ctx);
        }

        @Test
        void shouldStayAsPendingNew() {

            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.PENDING_NEW);
        }

        @Test
        void shouldRememberOemsOrderId() {
            assertThat(ctx.getPersistentState().getOemsOrderId()).isEqualTo("oems-1");
        }
    }

    @Nested
    class OnOemsResponse {

        private OemsResponse.Builder oemsResponse;

        @BeforeEach
        void setup() {

            oemsResponse = OemsResponse.newBuilder();
        }

        @Test
        void oemsNewThrows() {
            // arrange
            oemsResponse
                .setExecType(OemsExecType.NEW)
                .setOrderStatus(OemsOrderStatus.STATUS_NEW)
                .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT);

            // act, assert
            assertThatThrownBy(() -> underTest.onOemsResponse(oemsResponse.build(), ctx));

        }

        @Test
        void oemsFillThrows() {
            // arrange
            oemsResponse
                .setExecType(OemsExecType.FILL)
                .setOrderStatus(OemsOrderStatus.STATUS_FILLED)
                .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT);

            // act, assert
            assertThatThrownBy(() -> underTest.onOemsResponse(oemsResponse.build(), ctx));
        }

        @Test
        void oemsPartialFillThrows() {
            // arrange
            oemsResponse
                .setExecType(OemsExecType.PARTIAL_FILL)
                .setOrderStatus(OemsOrderStatus.STATUS_PARTIALLY_FILLED)
                .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT);

            // act, assert
            assertThatThrownBy(() -> underTest.onOemsResponse(oemsResponse.build(), ctx));
        }

        @Test
        void oemsRejectMovesToRejected() {
            // arrange
            oemsResponse
                .setExecType(OemsExecType.REJECTED)
                .setOrderStatus(OemsOrderStatus.STATUS_REJECTED)
                .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT);

            // act, assert
            assertThatThrownBy(() -> underTest.onOemsResponse(oemsResponse.build(), ctx));

        }
    }

    @Nested
    class WhenCheckingIfHasToBeBackedByNewOemsOrder {
        @Test
        void returnsTrue() {
            assertThat(underTest.hasToBeBackedByNewOemsOrder(ctx)).isTrue();
        }
    }

    @Nested
    class OnClientCancelRequest {
        @Test
        void shouldMoveToCancel() {
            State result = underTest.onClientCancelRequest(ClientRequest.newBuilder()
                .setRequestType(ClientRequestType.CANCEL)
                .build(), ctx);

            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.CANCELED);
        }
    }

    @Nested
    class OnClientForceCancelRequest {
        @Test
        void shouldMoveToCancel() {
            underTest.onClientCancelRequest(ClientRequest.newBuilder()
                .setRequestType(ClientRequestType.CANCEL)
                .setForceCancel(true)
                .build(), ctx);

            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.CANCELED);
        }
    }

    @Nested
    class OnClientCancelReplaceRequest {
        @Test
        void shouldMoveToCancel() {
            State result = underTest.onClientCancelReplaceRequest(ClientRequest.newBuilder()
                .setRequestType(ClientRequestType.CANCEL_REPLACE)
                .build(), ctx);

            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.CANCELED);
        }
    }
}
