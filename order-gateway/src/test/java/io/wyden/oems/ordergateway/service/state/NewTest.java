package io.wyden.oems.ordergateway.service.state;

import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.oems.ordergateway.service.referencedata.InstrumentsRepository;
import io.wyden.oems.ordergateway.service.referencedata.VenueAccountsRepository;
import io.wyden.oems.ordergateway.service.tracking.ClientOrderCache;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientRequestType;
import io.wyden.published.client.ClientSide;
import io.wyden.published.oems.FeeBasis;
import io.wyden.published.oems.FeeData;
import io.wyden.published.oems.FeeType;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

class NewTest {

    public static final String OEMS_ORDER_ID = "oems1";
    public static final String ORDER_ID = "1";
    private static final String TARGET = "target1";
    private static final String VENUE = "Test";
    private static final String INSTRUMENT_ID = "BTCUSD@Forex@Test";
    private static final String VENUE_ACCOUNT = "TestAccount1";
    public static final io.wyden.published.oems.FeeData OEMS_FEE1 = FeeData.newBuilder()
        .setType(FeeType.EXCHANGE_FEE)
        .setCurrency("USD")
        .setAmount("1")
        .setBasis(FeeBasis.ABSOLUTE)
        .setDescription("test fee 1")
        .build();
    public static final io.wyden.published.oems.FeeData OEMS_FEE2 = FeeData.newBuilder()
        .setType(FeeType.TRANSACTION_FEE)
        .setCurrency("EUR")
        .setAmount("0.05")
        .setBasis(FeeBasis.ABSOLUTE)
        .setDescription("test fee 2")
        .build();
    public static final io.wyden.published.oems.FeeData OEMS_FEE3 = FeeData.newBuilder()
        .setType(FeeType.FIXED_FEE)
        .setCurrency("EUR")
        .setAmount("0.25")
        .setBasis(FeeBasis.ABSOLUTE)
        .setDescription("test fee 3")
        .build();
    public static final io.wyden.published.client.FeeData CLIENT_FEE1 = io.wyden.published.client.FeeData.newBuilder()
        .setType(io.wyden.published.client.FeeType.EXCHANGE_FEE)
        .setCurrency("USD")
        .setAmount("1")
        .setBasis(io.wyden.published.client.FeeBasis.ABSOLUTE)
        .setDescription("test fee 1")
        .build();
    public static final io.wyden.published.client.FeeData CLIENT_FEE2 = io.wyden.published.client.FeeData.newBuilder()
        .setType(io.wyden.published.client.FeeType.TRANSACTION_FEE)
        .setCurrency("EUR")
        .setAmount("0.05")
        .setBasis(io.wyden.published.client.FeeBasis.ABSOLUTE)
        .setDescription("test fee 2")
        .build();
    public static final io.wyden.published.client.FeeData CLIENT_FEE3 = io.wyden.published.client.FeeData.newBuilder()
        .setType(io.wyden.published.client.FeeType.FIXED_FEE)
        .setCurrency("EUR")
        .setAmount("0.25")
        .setBasis(io.wyden.published.client.FeeBasis.ABSOLUTE)
        .setDescription("test fee 3")
        .build();

    private State pendingNew;
    private State stateNew;
    private OemsRequest oemsRequest;
    private OemsResponse oemsResponseNew;
    private OemsResponse partialFill;
    private OemsResponse lastFill;
    private OemsResponse oemsReject;
    private ClientRequest clientRequest;
    private ClientOrderContext ctx;
    private ClientOrderCache clientOrderCache;
    private InstrumentsRepository instrumentsRepository;
    private VenueAccountsRepository venueAccountsRepository;

    @BeforeEach
    void setUp() {
        clientRequest = ClientRequest.newBuilder()
            .setRequestType(ClientRequestType.ORDER_SINGLE)
            .setOrderId(ORDER_ID)
            .setInstrumentId(INSTRUMENT_ID)
            .setQuantity("10")
            .setRequestType(ClientRequestType.ORDER_SINGLE)
            .setClOrderId("a")
            .setClientId("client")
            .setSide(ClientSide.BUY)
            .setTarget(TARGET)
            .setOrderType(ClientOrderType.MARKET)
            .addVenueAccounts(VENUE_ACCOUNT)
            .build();

        pendingNew = new PendingNew();

        oemsRequest = OemsRequest.newBuilder()
            .setOrderId(OEMS_ORDER_ID)
            .build();

        oemsResponseNew = OemsResponse.newBuilder()
            .setOrderId(OEMS_ORDER_ID)
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setOrderStatus(OemsOrderStatus.STATUS_NEW)
            .setExecType(OemsExecType.NEW)
            .build();

        oemsReject = OemsResponse.newBuilder()
            .setOrderId(OEMS_ORDER_ID)
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setOrderStatus(OemsOrderStatus.STATUS_REJECTED)
            .setExecType(OemsExecType.REJECTED)
            .build();

        stateNew = new New();

        partialFill = OemsResponse.newBuilder()
            .setOrderId(OEMS_ORDER_ID)
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setExecType(OemsExecType.PARTIAL_FILL)
            .setVenueExecutionId(UUID.randomUUID().toString())
            .setOrderStatus(OemsOrderStatus.STATUS_PARTIALLY_FILLED)
            .setLastQty("1")
            .setOrderQty("10")
            .setLeavesQty("9")
            .setCumQty("1")
            .setLastPrice("20000")
            .setAvgPrice("20000")
            .setCumQty("1")
            .addFeeData(OEMS_FEE1)
            .addFeeData(OEMS_FEE2)
            .build();

        lastFill = OemsResponse.newBuilder()
            .setOrderId(OEMS_ORDER_ID)
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setExecType(OemsExecType.FILL)
            .setVenueExecutionId(UUID.randomUUID().toString())
            .setOrderStatus(OemsOrderStatus.STATUS_FILLED)
            .setLastQty("9")
            .setOrderQty("10")
            .setLastPrice("20000")
            .setAvgPrice("20000")
            .setCumQty("10")
            .addFeeData(OEMS_FEE3)
            .build();

        clientOrderCache = mock(ClientOrderCache.class);

        instrumentsRepository = mock(InstrumentsRepository.class);
        venueAccountsRepository  = mock(VenueAccountsRepository.class);
        doReturn(InstrumentsRepository.buildInstrument("BTC", "USD", VENUE)).when(instrumentsRepository).find(any());
        doReturn(VenueAccountsRepository.buildVenueAccount(VENUE_ACCOUNT, VENUE)).when(venueAccountsRepository).find(any());

        ctx = ClientOrderContext.initialize(clientRequest, clientOrderCache, instrumentsRepository, venueAccountsRepository);

    }

    @Test
    void shouldPersistAsNew_FromPendingNew_AfterOemsResponse() {
        // given
        State underTest = new PendingNewAndPendingOemsNew();
        ctx = ClientOrderContext.rebuild(underTest, clientOrderCache, ctx.getPersistentState()
            .toBuilder().setOemsOrderId(oemsRequest.getOrderId()).build(), instrumentsRepository);

        // when
        underTest.onOemsResponse(oemsResponseNew, ctx);

        // then
        ArgumentCaptor<ClientOrderState> captor = ArgumentCaptor.forClass(ClientOrderState.class);
        verify(clientOrderCache).update(captor.capture());
        assertThat(captor.getValue().getCurrentStatus()).isEqualTo(ClientOrderStatus.NEW);
        assertThat(captor.getValue().getOemsOrderId()).isEqualTo(oemsRequest.getOrderId());
    }

    @Test
    void shouldPersistAsPartiallyFilled_FromPendingNew_AfterOemsResponse() {
        // when
        State underTest = pendingNew.onOemsRequestSent(oemsRequest, ctx);

        // then
        ArgumentCaptor<ClientOrderState> captor = ArgumentCaptor.forClass(ClientOrderState.class);
        verify(clientOrderCache).update(captor.capture());
        assertThat(captor.getValue().getCurrentStatus()).isEqualTo(ClientOrderStatus.PENDING_NEW);
        assertThat(captor.getValue().getOemsOrderId()).isEqualTo(oemsRequest.getOrderId());

        // when
        underTest.onOemsResponse(partialFill, ctx);

        // then
        verify(clientOrderCache, times(2)).update(captor.capture());
        assertThat(captor.getValue().getCurrentStatus()).isEqualTo(ClientOrderStatus.PARTIALLY_FILLED);
        assertThat(captor.getValue().getOemsOrderId()).isEqualTo(oemsRequest.getOrderId());
        assertThat(captor.getValue().getOemsResponse().getFeeData(0)).isEqualTo(OEMS_FEE1);
        assertThat(captor.getValue().getOemsResponse().getFeeData(1)).isEqualTo(OEMS_FEE2);
    }

    @Test
    void shouldPersistAsPartiallyFilled_FromNew_AfterOemsResponse() {
        // when
        State underTest = stateNew;
        ctx = ClientOrderContext.rebuild(underTest, clientOrderCache, ctx.getPersistentState()
            .toBuilder().setOemsOrderId(oemsRequest.getOrderId()).build(), instrumentsRepository);
        underTest.onOemsResponse(partialFill, ctx);

        // then
        ArgumentCaptor<ClientOrderState> captor = ArgumentCaptor.forClass(ClientOrderState.class);
        verify(clientOrderCache).update(captor.capture());
        assertThat(captor.getValue().getCurrentStatus()).isEqualTo(ClientOrderStatus.PARTIALLY_FILLED);
        assertThat(captor.getValue().getOemsOrderId()).isEqualTo(oemsRequest.getOrderId());
        assertThat(captor.getValue().getOemsResponse().getFeeData(0)).isEqualTo(OEMS_FEE1);
        assertThat(captor.getValue().getOemsResponse().getFeeData(1)).isEqualTo(OEMS_FEE2);
    }

    @Test
    void shouldPersistAsFilledFromNew_AfterOemsResponse() {
        // arrange
        State underTest = stateNew;

        OemsResponse fullFill = partialFill.toBuilder()
            .setCumQty("10")
            .setExecType(OemsExecType.FILL)
            .setOrderStatus(OemsOrderStatus.STATUS_FILLED)
            .setLastPrice("20000")
            .setLastQty("10")
            .build();

        // act
        underTest.onOemsResponse(fullFill, ctx);

        // assert
        ArgumentCaptor<ClientOrderState> captor = ArgumentCaptor.forClass(ClientOrderState.class);
        verify(clientOrderCache).update(captor.capture());
        assertThat(captor.getValue().getCurrentStatus()).isEqualTo(ClientOrderStatus.FILLED);
        assertThat(captor.getValue().getOemsResponse().getFeeData(0)).isEqualTo(OEMS_FEE1);
        assertThat(captor.getValue().getOemsResponse().getFeeData(1)).isEqualTo(OEMS_FEE2);
    }

    @Test
    void shouldPersistAsFilledFromPartiallyFilled_AfterOemsResponse() {
        // arrange
        State underTest = stateNew;

        // act
        State partiallyFilled = underTest.onOemsResponse(partialFill, ctx);
        partiallyFilled.onOemsResponse(lastFill, ctx);

        // assert
        assertThat(ctx.getPersistentState().getCurrentStatus()).isEqualTo(ClientOrderStatus.FILLED);
    }

    @Test
    void onOemsResponse_WithoutExecutionId_IsBackfilledArtificially() {
        // when
        State underTest = stateNew;
        ctx = ClientOrderContext.rebuild(underTest, clientOrderCache, ctx.getPersistentState(), instrumentsRepository);
        OemsResponse.Builder partialFillBuilder = partialFill.toBuilder().clearExecutionId();
        underTest.onOemsResponse(partialFillBuilder.build(), ctx);

        // then
        assertThat(ctx.getPersistentState().getVenueExecutionId()).isNotBlank();
    }

    @Test
    void onOemsResponse_ExecutionId_IsTaken() {
        // when
        State underTest = stateNew;
        OemsResponse.Builder partialFillBuilder = partialFill.toBuilder().setExecutionId("0001");
        underTest.onOemsResponse(partialFillBuilder.build(), ctx);

        // then
        assertThat(ctx.getPersistentState().getVenueExecutionId()).isEqualTo("0001");
    }

    @Test
    void onOemsRejectedShouldMoveToRejected() {
        // when
        State underTest = stateNew;
        ctx = ClientOrderContext.rebuild(underTest, clientOrderCache,
                ctx.getPersistentState().toBuilder().setOemsOrderId(oemsRequest.getOrderId()).build(), instrumentsRepository);

        underTest.onOemsResponse(oemsReject, ctx);

        // then
        assertThat(ctx.getPersistentState().getCurrentStatus()).isEqualTo(ClientOrderStatus.REJECTED);
        assertThat(ctx.getPersistentState().getOemsOrderId()).isEqualTo(oemsRequest.getOrderId());
    }

    @Test
    void onClientCancelRequest_WithEqualQtyToAlreadyFilled_ShouldMoveOrderToFilled() {
        // arrange
        State underTest = new New();
        ctx = ClientOrderContext.rebuild(underTest, clientOrderCache, ctx.getPersistentState().toBuilder()
            .setFilledQuantity("1")
            .build(), instrumentsRepository);

        // act
        underTest.onClientCancelReplaceRequest(ClientRequest.newBuilder()
            .setOrderId("1.1")
            .setQuantity("1")
            .setOrigOrderId(ORDER_ID)
            .build(), ctx);

        // assert
        assertThat(ctx.getPersistentState().getCurrentStatus()).isEqualTo(ClientOrderStatus.FILLED);
    }

    @Test
    void onClientCancelRequest_WithSmallerQtyThanAlreadyFilled_ShouldMoveOrderToFilled() {
        // arrange
        State underTest = new New();
        ctx = ClientOrderContext.rebuild(underTest, clientOrderCache, ctx.getPersistentState(), instrumentsRepository);
        ctx.onOemsResponse(OemsResponse.newBuilder()
            .setExecType(OemsExecType.PARTIAL_FILL)
            .setOrderStatus(OemsOrderStatus.STATUS_PARTIALLY_FILLED)
            .setLastPrice("10000")
            .setLastQty("1")
            .setCumQty("1")
            .setLeavesQty("9")
            .setAvgPrice("10000")
            .setOrderQty("10")
            .build());

        // act
        underTest.onClientCancelReplaceRequest(ClientRequest.newBuilder()
            .setOrderId("1.1")
            .setQuantity("0.5")
            .setOrigOrderId(ORDER_ID)
            .build(), ctx);

        // assert
        assertThat(ctx.getPersistentState().getCurrentStatus()).isEqualTo(ClientOrderStatus.FILLED);
    }

    @Test
    void onClientCancelReplaceRequest_WithGreaterQtyThanFilled_ShouldMoveOrderToPendingReplace() {
        // arrange
        State underTest = new New();
        ctx = ClientOrderContext.rebuild(underTest, clientOrderCache, ctx.getPersistentState().toBuilder()
            .setFilledQuantity("1")
            .build(), instrumentsRepository);

        // act
        underTest.onClientCancelReplaceRequest(ClientRequest.newBuilder()
            .setOrderId("1.1")
            .setQuantity("2")
            .setOrigOrderId(ORDER_ID)
            .build(), ctx);

        // assert
        ArgumentCaptor<ClientOrderState> captor = ArgumentCaptor.forClass(ClientOrderState.class);
        verify(clientOrderCache).update(captor.capture());
        assertThat(captor.getValue().getCurrentStatus()).isEqualTo(ClientOrderStatus.PENDING_REPLACE);
    }

    @Test
    void shouldNotRequireOemsBackingOrder() {
        // act, assert
        assertThat(stateNew.hasToBeBackedByNewOemsOrder(ctx)).isFalse();
    }

    @Test
    void shouldNotRequireOemsBackingCancel() {
        // act, assert
        assertThat(stateNew.hasToBeBackedByOemsCancel()).isFalse();
    }


    @Test
    void onClientForceCancelRequest_ShouldMoveOrderToCancelled() {
        // arrange
        State underTest = new New();
        ctx = ClientOrderContext.rebuild(underTest, clientOrderCache, ctx.getPersistentState().toBuilder()
            .build(), instrumentsRepository);

        // act
        underTest.onClientCancelRequest(ClientRequest.newBuilder()
            .setForceCancel(true)
            .setOrderId("1.1")
            .setQuantity("0")
            .setOrigOrderId(ORDER_ID)
            .build(), ctx);

        // assert
        assertThat(ctx.getPersistentState().getCurrentStatus()).isEqualTo(ClientOrderStatus.CANCELED);
    }
}
