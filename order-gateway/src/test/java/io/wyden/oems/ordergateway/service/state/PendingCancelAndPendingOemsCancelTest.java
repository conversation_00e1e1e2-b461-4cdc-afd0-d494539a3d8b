package io.wyden.oems.ordergateway.service.state;

import io.wyden.published.oems.OemsRequest;
import org.jetbrains.annotations.NotNull;

class PendingCancelAndPendingOemsCancelTest extends PendingCancelTest {

    @Override
    protected @NotNull ClientOrderContext prepareCtxUnderTest() {
        ClientOrderContext clientOrderContext = super.prepareCtxUnderTest();
        clientOrderContext.onOemsRequestSent(OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.CANCEL)
            .build());
        return clientOrderContext;
    }

    protected State prepareStateUnderTest() {
        return new PendingCancelAndPendingOemsCancel();
    }
}
