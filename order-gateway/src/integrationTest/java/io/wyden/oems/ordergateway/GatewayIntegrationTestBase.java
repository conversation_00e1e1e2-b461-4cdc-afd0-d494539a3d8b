package io.wyden.oems.ordergateway;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.test.TestHazelcastInstanceFactory;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.oems.ordergateway.infrastructure.rabbit.RabbitDestinations;
import io.wyden.oems.ordergateway.util.Client;
import io.wyden.oems.ordergateway.util.Collider;
import io.wyden.oems.ordergateway.util.TestingData;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.ForexSpotProperties;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.Comparator;

import static io.wyden.oems.ordergateway.util.TestingData.INTEGRATION_TEST_INSTRUMENT_ID;
import static io.wyden.oems.ordergateway.util.TestingData.INTEGRATION_TEST_VENUE_ACCOUNT;
import static io.wyden.oems.ordergateway.util.TestingData.INTEGRATION_TEST_VENUE_NAME;
import static org.mockito.Mockito.mock;

@Testcontainers
@ContextConfiguration(initializers = GatewayIntegrationTestBase.Initializer.class,
                      classes = {GatewayIntegrationTestBase.TestHazelcastConfiguration.class})
@SpringBootTest(properties = {"spring.main.allow-bean-definition-overriding=true", "logging.level.root=debug"})
@TestPropertySource(locations = "classpath:integration-test.properties")
@ExtendWith(SpringExtension.class)
public abstract class GatewayIntegrationTestBase {

    @Container
    public static final RabbitMQContainer RABBIT_MQ = new RabbitMQContainer("rabbitmq:3.12-management")
        .withNetwork(Network.newNetwork())
        .withReuse(true);

    private static final Instrument INSTRUMENT = Instrument.newBuilder()
        .setForexSpotProperties(ForexSpotProperties.newBuilder()
            .setBaseCurrency("BTC")
            .build())
        .setBaseInstrument(BaseInstrument.newBuilder()
            .setQuoteCurrency("USD")
            .setVenueName(INTEGRATION_TEST_VENUE_NAME)
            .build())
        .build();

    private static final VenueAccount VENUE_ACCOUNT = VenueAccount.newBuilder()
        .setId(INTEGRATION_TEST_VENUE_ACCOUNT)
        .setVenueName(INTEGRATION_TEST_VENUE_NAME)
        .build();

    @Autowired
    RabbitIntegrator rabbitIntegrator;

    @Autowired
    RabbitDestinations destinations;

    @Autowired
    HazelcastInstance hz;

    String orderId;
    String venueOrderId;

    Collider collider;
    Client client;
    TestingData testingData;

    String venueAccount;

    @BeforeEach
    void resetExternalServices() {
        testingData = new TestingData();

        collider = new Collider(testingData, destinations, rabbitIntegrator);
        client = new Client(testingData, destinations, rabbitIntegrator);

        venueAccount = INTEGRATION_TEST_VENUE_ACCOUNT;
    }

    @AfterEach
    void generalTearDown() {
        collider.cleanup();
        collider = null;

        client.cleanup();
        client = null;
    }

    public static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {

            var values = TestPropertyValues.of(
                "rabbitmq.host=" + RABBIT_MQ.getHost(),
                "rabbitmq.port=" + RABBIT_MQ.getMappedPort(5672),
                "rabbitmq.username=" + RABBIT_MQ.getAdminUsername(),
                "rabbitmq.password=" + RABBIT_MQ.getAdminPassword()
            );

            values.applyTo(applicationContext);
        }
    }

    @TestConfiguration
    public static class TestHazelcastConfiguration {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance createHazelcastInstance() {
            return new TestHazelcastInstanceFactory().newHazelcastInstance();
        }

        @Primary
        @Bean
        InstrumentsCacheFacade instrumentsCacheFacade(HazelcastInstance hazelcastInstance) {
            IMap<String, Instrument> map = hazelcastInstance.getMap("instruments");
            map.put(INTEGRATION_TEST_INSTRUMENT_ID, INSTRUMENT);
            return new InstrumentsCacheFacade(map, mock(Tracing.class));
        }

        @Primary
        @Bean
        VenueAccountCacheFacade venueAccountCacheFacade(HazelcastInstance hazelcastInstance) {
            IMap<String, VenueAccount> map = hazelcastInstance.getMap("venue_accounts");
            map.put(TestingData.INTEGRATION_TEST_VENUE_ACCOUNT, VENUE_ACCOUNT);
            return new VenueAccountCacheFacade(map, mock(Tracing.class));
        }
    }

    public static Comparator<ClientResponse> ignoreTimestampComparator() {
        return (o1, o2) -> {
            ClientResponse o1Sanitized = o1.toBuilder()
                .clearTimestamp()
                .clearRequest()
                .build();
            ClientResponse o2Sanitized = o2.toBuilder()
                .clearTimestamp()
                .clearRequest()
                .build();
            if (o1Sanitized.equals(o2Sanitized)) {
                return 0;
            }
            
            return Integer.compare(o1Sanitized.hashCode(), o2Sanitized.hashCode());
        };
    }
}
