package io.wyden.apiserver.rest.apiui;

public final class SharedModel {
    private SharedModel() {/*empty*/}

    public enum InstrumentType {
        INSTRUMENT_TYPE_UNDETERMINED,
        FOREX,
        FUTURE,
        PERPETUAL_SWAP,
        OPTION
    }

    public enum OrderType {
        ORDER_TYPE_UNSPECIFIED,
        MARKET,
        LIMIT,
        STOP,
        STOP_LIMIT
    }

    public enum RequestType {
        REQUEST_TYPE_UNSPECIFIED,
        ORDER_SINGLE,
        CANCEL
    }

    public enum Side {
        SIDE_UNDETERMINED,
        BUY,
        SELL,
        SELL_SHORT,
        REDUCE_SHORT
    }

    public enum TIF {
        TIF_UNSPECIFIED,
        DAY,
        GTC,
        GTD,
        IOC,
        FOK
    }

    public enum OrderStatus {
        ORDER_STATUS_UNSPECIFIED,
        NEW,
        PARTIALLY_FILLED,
        FILLED,
        DONE_FOR_DAY,
        CANCELED,
        REP<PERSON>CE<PERSON>,
        PENDING_CANCEL,
        STOPPED,//@Deprecated
        REJECTED,
        SUSPENDED,//@Deprecated
        PENDING_NEW,
        CALCULATED,
        EXPIRED,
        ACCEPTED_FOR_BIDDING,
        PENDING_REPLACE
    }

    public enum OrderCategory {
        ORDER_CATEGORY_UNSPECIFIED,
        DIRECT_MARKET_ACCESS_ORDER,
        SOR_ORDER,
        SOR_CHILD_ORDER,
        AGENCY_ORDER,
        AGENCY_STREET_ORDER,
        AGENCY_SOR_ORDER,
        AGENCY_CLOB_ORDER,
        CLOB_QUOTING_ORDER,
        CLOB_EXTERNAL_HEDGE_ORDER,
        AUTO_HEDGER_EXTERNAL_HEDGE_ORDER
    }

    public enum AssetClass {
        FOREX
    }

    public enum SortingOrder {
        ASC,
        DESC
    }

    public static io.wyden.published.common.SortingOrder map(SortingOrder sortingOrder) {
        if (sortingOrder == null) {
            return io.wyden.published.common.SortingOrder.SORTING_ORDER_UNDEFINED;
        }

        return switch (sortingOrder) {
            case ASC -> io.wyden.published.common.SortingOrder.SORTING_ORDER_ASC;
            case DESC -> io.wyden.published.common.SortingOrder.SORTING_ORDER_DESC;
        };
    }
}
