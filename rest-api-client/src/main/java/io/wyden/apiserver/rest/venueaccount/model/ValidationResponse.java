package io.wyden.apiserver.rest.venueaccount.model;

import java.util.List;

public record ValidationResponse(boolean isValid, List<FieldError> errors) {

    public static final ValidationResponse OK = new ValidationResponse(true, List.of());
    public static ValidationResponse violation(String name, String message) {
        return new ValidationResponse(false, List.of(new FieldError(name, message)));
    }

    private record FieldError(String field, String message){}
}