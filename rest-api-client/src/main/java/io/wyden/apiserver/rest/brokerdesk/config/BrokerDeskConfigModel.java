package io.wyden.apiserver.rest.brokerdesk.config;

import io.wyden.apiserver.rest.referencedata.instruments.model.dto.AssetClassDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountDesc;
import io.wyden.published.brokerdesk.ValidationError;
import io.wyden.published.brokerdesk.ValidationResult;
import io.wyden.published.brokerdesk.Validations;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public final class BrokerDeskConfigModel {

    private BrokerDeskConfigModel() {
    }

    public record PortfolioConfiguration(String id, String name,
                                         ExecutionConfiguration executionConfiguration,
                                         PricingConfiguration pricingConfiguration,
                                         HedgingConfiguration hedgingConfiguration,
                                         PortfolioGroupConfiguration portfolioGroupConfiguration,
                                         Collection<InstrumentConfiguration> instrumentConfiguration) {
    }

    public record PortfolioConfigurationFlat(String id, String name, String portfolioGroupConfigurationId) {
    }

    public record PortfolioConfigurationInput(String id, String name,
                                              ExecutionConfigurationInput executionConfiguration,
                                              PricingConfigurationInput pricingConfiguration,
                                              HedgingConfigurationInput hedgingConfiguration,
                                              String portfolioGroupConfigurationId,
                                              Collection<InstrumentConfigurationInput> instrumentConfiguration) {
    }

    public record PortfolioGroupConfiguration(String id, String name, PortfolioType portfolioType,
                                              ExecutionConfiguration executionConfiguration,
                                              PricingConfiguration pricingConfiguration,
                                              HedgingConfiguration hedgingConfiguration,
                                              Collection<InstrumentGroupConfiguration> instrumentConfiguration) {
    }

    public record PortfolioGroupConfigurationInput(String id, String name, PortfolioType portfolioType,
                                                   ExecutionConfigurationInput executionConfiguration,
                                                   PricingConfigurationInput pricingConfiguration,
                                                   HedgingConfigurationInput hedgingConfiguration,
                                                   Collection<InstrumentGroupConfigurationInput> instrumentConfiguration) {
    }

    public record PortfolioGroupConfigurationFlat(String id, String name, PortfolioType portfolioType) {
    }

    public record ExecutionConfiguration(TradingMode tradingMode, String counterPortfolioId, String counterPortfolioName,
                                         BigDecimal fixedFee, String fixedFeeCurrency,
                                         BigDecimal percentageFee, String percentageFeeCurrency, CurrencyType percentageFeeCurrencyType,
                                         BigDecimal minFee, String minFeeCurrency,
                                         String agencyTradingAccount, String agencyTradingAccountName, InstrumentResponseDto agencyTargetInstrument,
                                         Boolean chargeExchangeFee, Boolean discloseTradingVenue,
                                         ExecutionMode executionMode,
                                         @Deprecated Collection<String> sorTradingAccounts,
                                         Collection<VenueAccountDesc> sorTradingAccountDescs,
                                         SorTarget sorTarget) {
    }

    public record ExecutionConfigurationInput(TradingMode tradingMode, String counterPortfolioId,
                                              BigDecimal fixedFee, String fixedFeeCurrency,
                                              BigDecimal percentageFee, String percentageFeeCurrency, CurrencyType percentageFeeCurrencyType,
                                              BigDecimal minFee, String minFeeCurrency,
                                              String agencyTradingAccount, String agencyTargetInstrumentId,
                                              Boolean chargeExchangeFee, Boolean discloseTradingVenue,
                                              ExecutionMode executionMode, Collection<String> sorTradingAccounts, SorTarget sorTarget) {
    }

    public record PricingConfiguration(Collection<VenueAccountDesc> venueAccounts,
                                       BigDecimal markup) {
    }

    public record PricingConfigurationInput(Collection<String> venueAccount,
                                            BigDecimal markup) {
    }

    public record InstrumentPricingConfiguration(Collection<InstrumentKey> pricingSource, BigDecimal markup) {
    }

    public record InstrumentPricingConfigurationInput(Collection<InstrumentKeyInput> pricingSource, BigDecimal markup) {
    }

    public record HedgingConfiguration(Boolean autoHedging,
                                       String targetAccountId,
                                       String targetAccountName,
                                       Collection<ThresholdConfiguration> thresholdConfiguration) {
    }

    public record HedgingConfigurationInput(Boolean autoHedging,
                                            String targetAccountId,
                                            Collection<ThresholdConfigurationInput> thresholdConfiguration) {
    }

    public record InstrumentConfiguration(String instrumentId, Boolean tradeable, InstrumentGroupConfiguration instrumentGroupConfiguration,
                                          ExecutionConfiguration executionConfiguration, InstrumentPricingConfiguration pricingConfiguration) {
    }

    public record InstrumentConfigurationInput(String instrumentId, Boolean tradeable, ExecutionConfigurationInput executionConfiguration,
                                               InstrumentPricingConfigurationInput pricingConfiguration) {
    }

    public record InstrumentGroupConfiguration(String instrumentId, Boolean tradeable, ExecutionConfiguration executionConfiguration,
                                               InstrumentPricingConfiguration pricingConfiguration) {

    }

    public record InstrumentGroupConfigurationInput(String instrumentId, Boolean tradeable, ExecutionConfigurationInput executionConfiguration,
                                                    InstrumentPricingConfigurationInput pricingConfiguration) {
    }

    public record InstrumentKey(String venueAccount, String venueAccountName, InstrumentResponseDto instrument) {
    }

    public record InstrumentKeyInput(String venueAccount, String instrumentId) {
    }

    public record ThresholdConfiguration(String asset, String highThreshold, String lowThreshold, String targetExposure,
                                         InstrumentResponseDto hedgeInstrument) {
    }

    public record ThresholdConfigurationInput(String asset, String highThreshold, String lowThreshold, String targetExposure,
                                         String hedgeInstrumentId) {
    }

    public record SorTarget(String symbol, AssetClassDto assetClass) {
    }

    public record ResetConfigurationInput(ConfigurationLevel configurationLevel, ConfigurationType configurationType, String resourceId, String instrumentId) {
    }

    public enum PortfolioType {
        NOSTRO,
        VOSTRO
    }

    public enum TradingMode {
        AGENCY,
        PRINCIPAL
    }

    public enum ExecutionMode {
        SIMPLE,
        SOR
    }

    public enum CurrencyType {
        BASE_CURRENCY,
        QUOTE_CURRENCY,
        SPECIFIC_CURRENCY
    }

    public enum ConfigurationLevel {
        PORTFOLIO_GROUP,
        PORTFOLIO_GROUP_INSTRUMENT,
        PORTFOLIO,
        PORTFOLIO_INSTRUMENT
    }

    public enum ConfigurationType {
        EXECUTION,
        PRICING,
        HEDGING
    }

    public enum ErrorType {
        MISSING,
        INVALID
    }

    // type ConfigValidationResult {
    //  resultsPerInstrument: [ConfigInstrumentValidationResult!]!
    //}
    public record ConfigValidationResult(List<ConfigInstrumentValidationResult> resultsPerInstrument) {

        public ConfigValidationResult(Validations validations) {
            this(validations.getResultPerInstrumentMap().entrySet().stream()
                .map(entry -> new ConfigInstrumentValidationResult(
                    entry.getKey(),
                    isValid(entry.getValue()),
                    preventsTrading(entry.getValue()),
                    preventsMarketData(entry.getValue()),
                    asGqlDto(entry.getValue().getErrorsList())
                ))
                .collect(Collectors.toList()));

        }

        private static List<ConfigValidationError> asGqlDto(List<ValidationError> errorsList) {
            return errorsList.stream()
                .map(ve -> new ConfigValidationError(
                    ve.getFieldName(),
                    ErrorType.valueOf(ve.getErrorType().name()),
                    ve.getPreventsTrading(),
                    ve.getPreventsMarketData(),
                    ve.getDescription()
                    )
                )
                .toList();
        }

        private static boolean preventsMarketData(ValidationResult value) {
            return value.getErrorsList().stream().anyMatch(ValidationError::getPreventsMarketData);
        }

        private static boolean preventsTrading(ValidationResult value) {
            return value.getErrorsList().stream().anyMatch(ValidationError::getPreventsTrading);
        }

        private static boolean isValid(ValidationResult validationResult) {
            return validationResult.getErrorsCount() == 0;
        }
    }

    // type ConfigInstrumentValidationResult {
    //  instrumentId: String!
    //
    //  # True, if effective configuration is complete and valid. Only true if preventsTrading = false and preventsMarketData = false
    //  isValid: Boolean!
    //  # True, if there are errors present that affect trading capabilities.
    //  preventsTrading: Boolean!
    //  # True, if there are errors present that affect market data streaming capabilities.
    //  preventsMarketData: Boolean!
    //
    //  errors: [ConfigValidationError!]!
    //}
    public record ConfigInstrumentValidationResult(String instrumentId,
                                                   boolean isValid,
                                                   boolean preventsTrading,
                                                   boolean preventsMarketData,
                                                   List<ConfigValidationError> errors) {

    }

    // type ConfigValidationError {
    //  errorMessage: String @deprecated(reason: "Use errorType instead")
    //  errorType: ErrorType!
    //  # True, if effective configuration is not complete for trading.
    //  preventsTrading: Boolean!
    //  # True, if effective configuration is not complete for market data.
    //  preventsMarketData: Boolean!
    //  fieldName: String!
    //}
    public record ConfigValidationError(String fieldName,
                                        ErrorType errorType,
                                        boolean preventsTrading,
                                        boolean preventsMarketData,
                                        String errorMessage) {

    }
}
