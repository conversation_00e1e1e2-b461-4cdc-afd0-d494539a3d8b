package io.wyden.apiserver.rest.risk.model;

import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioResponseDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.TagDto;
import java.util.List;

public final class PreTradeCheckModel {

    private PreTradeCheckModel() {}

    public enum PreTradeCheckChannel {
        UI,
        API,
    }

    public enum PreTradeCheckLevel {
        WARN,
        BLOCK,
    }

    public enum PreTradeCheckPropertyType {
        STRING,
        NUMBER,
        STRING_LIST,
    }

    public enum PreTradeCheckPropertyFormat {
        FORMAT_UNSPECIFIED,
        CURRENCY_PAIR,
        CURRENCY,
    }

    public record PreTradeCheckInputDto(
        String id,
        String type,
        PreTradeCheckLevel level,
        List<String> portfolios,
        List<TagDto> portfolioTags,
        List<PreTradeCheckChannel> channels,
        List<PreTradeCheckPropertyDto> configuration
    ) {}

    public record PreTradeCheckResponseDto(
        String id,
        String type,
        PreTradeCheckLevel level,
        List<PortfolioResponseDto> portfolios,
        List<TagDto> portfolioTags,
        List<PreTradeCheckChannel> channels,
        List<PreTradeCheckPropertyDto> configuration
    ) {}

    public record PreTradeCheckPropertyDto(
        PreTradeCheckPropertyType type,
        String name,
        List<String> values
    ) {}

    public record PreTradeCheckSchemaDto(
        String type,
        List<PreTradeCheckPropertySchemaDto> configuration
    ) {}

    public record PreTradeCheckPropertySchemaDto(
        String name,
        PreTradeCheckPropertyType type,
        boolean required,
        boolean isEnum,
        List<String> options,
        PreTradeCheckPropertyFormat format
    ) {}
}
