package io.wyden.apiserver.rest.security.model;

import io.wyden.accessgateway.client.permission.Permission;

public enum Scope {
    READ,
    TRADE,
    MANAGE,
    CREATE;

    public static Scope parse(String scope) {
        return switch (scope) {
            case Permission.Scopes.READ -> READ;
            case Permission.Scopes.TRADE -> TRADE;
            case Permission.Scopes.MANAGE -> MANAGE;
            case Permission.Scopes.CREATE -> CREATE;

            default -> throw new IllegalStateException("Unexpected value: " + scope);
        };
    }

    public String toAgDomain() {
        return switch(this) {
            case READ -> Permission.Scopes.READ;
            case TRADE -> Permission.Scopes.TRADE;
            case MANAGE -> Permission.Scopes.MANAGE;
            case CREATE -> Permission.Scopes.CREATE;
        };
    }
}
