package io.wyden.apiserver.rest.orderhistory.model;

import java.util.Collection;

public record CollectionPredicateInput(PredicateType method, Field field, Collection<String> value) implements WydenPredicate {

    public enum PredicateType {
        IN
    }

    public enum Field {
        INSTRUMENT_ID,
        ORDER_ID,
        CL_ORDER_ID,
        ORDER_STATUS,
        ORDER_CATEGORY,
        PORTFOLIO_ID,
        @Deprecated TARGET, // use VENUE_ACCOUNT_ID instead
        VENUE_ACCOUNT_ID
    }

    @Override
    public String fieldName() {
        return field.name();
    }

    @Override
    public String toString() {
        return "CollectionPredicateInput{" +
               "method=" + method +
               ", field=" + field +
               ", value=" + value +
               '}';
    }
}
