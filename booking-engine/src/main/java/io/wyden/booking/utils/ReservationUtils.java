package io.wyden.booking.utils;

import io.wyden.booking.domain.reservation.ReservationFee;
import io.wyden.booking.domain.transaction.TransactionFeeEntity;

import java.util.Collection;

public class ReservationUtils {

    private ReservationUtils() {
    }

    public static Collection<? extends TransactionFeeEntity> negate(Collection<ReservationFee> fees) {
        return fees.stream()
            .map(fee -> new ReservationFee(fee.getAmount().negate(), fee.getCurrency(), fee.getDescription(), fee.getFeeType()))
            .toList();
    }
}
