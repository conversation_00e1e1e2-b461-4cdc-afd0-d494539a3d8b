package io.wyden.booking.interfaces.rabbit.volumereporting;

import com.rabbitmq.client.AMQP;
import io.wyden.booking.domain.volumereporting.VolumeReportModel;
import io.wyden.booking.domain.volumereporting.VolumeReportingService;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.published.booking.VolumeReportingRequest;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

import static org.slf4j.LoggerFactory.getLogger;

@Component
public class VolumeReportingRequestConsumer implements MessageConsumer<VolumeReportingRequest> {

    private static final Logger LOGGER = getLogger(VolumeReportingRequestConsumer.class);

    private final VolumeReportingService volumeReportingService;
    private final RabbitExchange<VolumeReportingRequest> volumeReportingRequestExchange;
    private final String queueName;
    private final String consumerName;
    private final RabbitIntegrator rabbitIntegrator;

    public VolumeReportingRequestConsumer(VolumeReportingService volumeReportingService,
                                          RabbitExchange<VolumeReportingRequest> volumeReportingRequestExchange,
                                          @Value("${rabbitmq.message-scheduler.volume-reporting-request}") String queueName,
                                          @Value("${spring.application.name}") String consumerName,
                                          RabbitIntegrator rabbitIntegrator) {
        this.volumeReportingService = volumeReportingService;
        this.volumeReportingRequestExchange = volumeReportingRequestExchange;
        this.queueName = queueName;
        this.consumerName = consumerName;
        this.rabbitIntegrator = rabbitIntegrator;
    }

    @PostConstruct
    void init() {
        declareQueue();
    }

    @Override
    public ConsumptionResult consume(VolumeReportingRequest volumeReportingRequest, AMQP.BasicProperties basicProperties) {
        LOGGER.info("Consuming volume reporting request: {}", volumeReportingRequest);

        Collection<VolumeReportModel.VolumeReport> volumeReports = volumeReportingService.generateVolumeReportsDaily();

        LOGGER.info("Finished creating volume report for request: {}. Results: {}", volumeReportingRequest, volumeReports);

        return ConsumptionResult.consumed();
    }

    private void declareQueue() {
        RabbitQueue<VolumeReportingRequest> queue = new RabbitQueueBuilder<VolumeReportingRequest>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .declare();

        queue.bindWithHeaders(volumeReportingRequestExchange, MatchingCondition.ALL, Map.of());
        LOGGER.info("Binding exchange {} and queue {}", volumeReportingService, queue);
        queue.attachConsumer(VolumeReportingRequest.parser(), this);
    }
}
