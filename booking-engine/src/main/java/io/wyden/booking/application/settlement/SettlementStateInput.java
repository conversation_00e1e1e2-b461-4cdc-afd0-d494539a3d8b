package io.wyden.booking.application.settlement;

import io.wyden.booking.application.state.StateInput;
import io.wyden.booking.domain.instrument.Instrument;
import io.wyden.booking.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.domain.ledgerentry.SimpleReference;
import io.wyden.booking.domain.position.GenericReference;
import io.wyden.booking.domain.position.Position;
import io.wyden.booking.domain.position.PositionFactory;
import io.wyden.booking.domain.settlement.Settlement;
import io.wyden.booking.domain.transaction.Transaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class SettlementStateInput implements StateInput {

    private static final Logger LOGGER = LoggerFactory.getLogger(SettlementStateInput.class);

    private final Map<SimpleReference, Position> positions;
    private final Map<String, Transaction> transactionPerExecutionId;
    private final Map<String, Settlement> settlementPerClientSettlementId;
    private final Map<String, Collection<LedgerEntry>> ledgerEntriesPerExecutionId;

    public SettlementStateInput(Map<SimpleReference, Position> positions,
                                Map<String, Transaction> transactionPerExecutionId,
                                Map<String, Settlement> settlementPerClientSettlementId,
                                Map<String, Collection<LedgerEntry>> ledgerEntriesPerExecutionId) {
        this.positions = new HashMap<>(positions);
        this.transactionPerExecutionId = new HashMap<>(transactionPerExecutionId);
        this.settlementPerClientSettlementId = settlementPerClientSettlementId;
        this.ledgerEntriesPerExecutionId = new HashMap<>(ledgerEntriesPerExecutionId);
    }

    public Map<LedgerEntry, Position> getPositionsForLedgerEntries(Collection<LedgerEntry> rawLedgerEntries) {
        if (rawLedgerEntries.isEmpty()) {
            return Map.of();
        }

        Map<LedgerEntry, Position> result = new HashMap<>();
        Map<SimpleReference, Position> createdPositions = new HashMap<>();

        for (LedgerEntry entry : rawLedgerEntries) {
            Instrument instrument = entry.getInstrument();
            GenericReference reference = entry.getReference();
            SimpleReference key = new SimpleReference(reference, instrument);

            Position position = positions.computeIfAbsent(key,
                k ->
                    createdPositions.computeIfAbsent(k, unused -> {
                        LOGGER.info("Cannot find position for ({}). New position will be created.", k);
                        return PositionFactory.createPosition(k.getReference(), k.getInstrument());
                    })
            );

            result.put(entry, position);
        }

        return result;
    }

    public Optional<Transaction> findTransactionByExecutionId(String executionId) {
        return Optional.ofNullable(transactionPerExecutionId.get(executionId));
    }

    public Collection<LedgerEntry> findLedgerEntries(String executionId) {
        return Optional.ofNullable(ledgerEntriesPerExecutionId.get(executionId)).orElse(List.of());
    }

    public Optional<Settlement> findSettlementByClientSettlementId(String clientSettlementId) {
        return Optional.ofNullable(settlementPerClientSettlementId.get(clientSettlementId));
    }

    public Collection<Transaction> getTransactions() {
        return transactionPerExecutionId.values();
    }
}
