package io.wyden.booking.application.settlement;

import io.wyden.booking.application.transaction.TransactionFromProtoMapper;
import io.wyden.booking.domain.common.Identifiers;
import io.wyden.booking.domain.settlement.Settlement;
import io.wyden.booking.domain.settlement.SettlementModel;
import io.wyden.booking.interfaces.rest.RequestModel;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.settlement.SettlementLeg;
import io.wyden.published.booking.settlement.SettlementRequest;
import io.wyden.published.booking.settlement.SettlementSearch;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;

public final class SettlementFromProtoMapper {

    private SettlementFromProtoMapper() {}

    public static Settlement map(SettlementRequest settlementRequest) {
        String settlementDateTime = settlementRequest.getSettlementDateTime();
        ZonedDateTime dateTime = DateUtils.isoUtcTimeToZonedDateTime(settlementDateTime);

        Settlement settlement = new Settlement(
            Identifiers.randomIdentifier(),
            dateTime,
            settlementRequest.getDescription(),
            settlementRequest.getClientSettlementId(),
            settlementRequest.getSettledTransactionExecutionIdsList());

        if (dateTime != null) {
            // settled date should be equal to requested settlement date
            settlement.setSettledDateTime(dateTime);
        }

        return settlement;
    }

    public static RequestModel.SettlementSearch map(SettlementSearch settlementSearch) {
        return new RequestModel.SettlementSearch(
            settlementSearch.getTransactionExecutionId(),
            settlementSearch.getClientSettlementId(),
            settlementSearch.getClientId(),
            settlementSearch.getFrom(),
            settlementSearch.getTo(),
            TransactionFromProtoMapper.map(settlementSearch.getSortingOrder()),
            settlementSearch.getFirst(),
            settlementSearch.getAfter());
    }

    public static Collection<SettlementModel.SettlementLeg> map(Collection<SettlementLeg> settlementLegs) {
        if (settlementLegs == null) {
            return null;
        }

        return settlementLegs.stream()
            .map(settlementLeg -> {
                String sourceId = settlementLeg.getSourceId();
                String targetId = settlementLeg.getTargetId();
                String asset = settlementLeg.getAsset();
                BigDecimal quantity = bd(settlementLeg.getQuantity());
                SettlementModel.SettlementLegId legId = new SettlementModel.SettlementLegId(sourceId, targetId, asset);
                return new SettlementModel.SettlementLeg(legId, quantity);
            })
            .toList();
    }
}
