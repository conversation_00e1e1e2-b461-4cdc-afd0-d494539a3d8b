package io.wyden.booking.domain.transaction;

import io.wyden.booking.domain.common.AuditedEntity;
import io.wyden.booking.domain.instrument.Currency;
import io.wyden.booking.domain.position.GenericReference;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;

import java.math.BigDecimal;

@Entity
@Table(name = "transaction_fee")
public class TransactionFee extends AuditedEntity implements TransactionFeeEntity {

    /**
     * amount of fee charged
     */
    private BigDecimal amount;

    /**
     * currency fee is charged in
     */
    @Embedded
    private Currency currency;

    /**
     * the portfolio the fee will be charged to
     */
    @ManyToOne
    protected GenericReference leftReference;

    /**
     * the counter portfolio the fee will be charged to
     */
    @ManyToOne
    protected GenericReference rightReference;

    /**
     * In double entry bookkeeping each transaction consists of two sides (left, right),
     * which have different meaning depending on type of transaction.
     * In the same way, each transaction fee to be fully processed need to know two sides of transaction if refers to:
     * <br>
     * TRADE has portfolio and counter-portfolio or portfolio and account
     * <br>
     * PAYMENT has portfolio and account
     * <br>
     * TRANSFER has portfolio and portfolio or account and account
     * <br>
     * {@code leftReferencePrice} is a rate of <i>left reference</i> and fee currency.
     */
    protected BigDecimal leftReferencePrice;

    /**
     * Similarly to {@link #leftReferencePrice}, it is a rate of <i>right reference</i> and fee currency.
     */
    protected BigDecimal rightReferencePrice;

    /**
     * arbitrary description of the fee
     */
    private String description;

    @Enumerated(EnumType.STRING)
    private TransactionFeeType feeType;

    public TransactionFee(BigDecimal amount, Currency currency, String description, TransactionFeeType feeType) {
        this(amount, currency, null, null, BigDecimal.ONE, BigDecimal.ONE, description, feeType);
    }

    public TransactionFee(BigDecimal amount,
                          Currency currency,
                          GenericReference leftReference,
                          GenericReference rightReference,
                          BigDecimal leftReferencePrice,
                          BigDecimal rightReferencePrice,
                          String description,
                          TransactionFeeType feeType) {
        this.amount = amount;
        this.currency = currency;
        this.leftReference = leftReference;
        this.rightReference = rightReference;
        this.leftReferencePrice = leftReferencePrice;
        this.rightReferencePrice = rightReferencePrice;
        this.description = description;
        this.feeType = feeType;
    }

    public TransactionFee() {
        // JPA
    }

    @Override
    public BigDecimal getAmount() {
        return amount;
    }

    @Override
    public Currency getCurrency() {
        return currency;
    }

    public GenericReference getLeftReference() {
        return leftReference;
    }

    public GenericReference getRightReference() {
        return rightReference;
    }

    public BigDecimal getLeftReferencePrice() {
        return leftReferencePrice;
    }

    public BigDecimal getRightReferencePrice() {
        return rightReferencePrice;
    }

    public String getDescription() {
        return description;
    }

    public TransactionFeeType getFeeType() {
        return feeType;
    }

    @Transient
    public AuditedEntity snapshot() {
        return new TransactionFee(amount, currency, leftReference, rightReference, leftReferencePrice, rightReferencePrice, description, feeType);
    }

    @Override
    public String toString() {
        return "TransactionFee{" +
            "amount=" + amount +
            ", currency=" + currency +
            ", description='" + description + '\'' +
            ", feeType=" + feeType +
            '}';
    }
}
