package io.wyden.booking.domain.transaction;

import io.wyden.booking.application.StateInputSource;
import io.wyden.booking.domain.common.AuditedEntity;
import io.wyden.booking.domain.instrument.Currency;
import io.wyden.booking.domain.instrument.Instrument;
import io.wyden.booking.domain.ledgerentry.LedgerEntrySource;
import io.wyden.booking.domain.ledgerentry.LedgerEntryType;
import io.wyden.booking.domain.ledgerentry.RawLedgerEntry;
import io.wyden.booking.domain.position.GenericReference;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Transient;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * The transaction object is the main object in the Wyden internal accounting system.
 * Transactions are immutable, a transaction can never be adjusted subsequently.
 * It is however possible to void/nullify/reverse a transaction be creating an inverse transaction.
 * Also, transactions need to be created with increasing timestamps (our nonces).
 */
@Entity
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "transaction_type")
@DiscriminatorValue("null")
public abstract class Transaction extends AuditedEntity implements LedgerEntrySource, StateInputSource {

    public static final Collection<TransactionFee> NO_FEES = List.of();

    /**
     * unique identifier of the transaction
     */
    protected String uuid;

    /**
     * reference identifier of the related reservation
     */
    protected String reservationRef;

    /**
     * date of transaction
     */
    protected ZonedDateTime dateTime;

    /**
     * internal ID of the execution
     */
    protected String executionId;

    /**
     * external / custody / venue ID of the execution
     */
    protected String venueExecutionId;

    /**
     * the portfolio the payment was received or sent
     */
    @ManyToOne
    protected GenericReference leftReference;

    /**
     * the custody account the payment was received or sent
     */
    @ManyToOne
    protected GenericReference rightReference;

    /**
     * In double entry bookkeeping each transaction consists of two sides (left, right),
     * which have different meaning depending on type of transaction:
     * <br>
     * TRADE has portfolio and counter-portfolio or portfolio and account
     * <br>
     * PAYMENT has portfolio and account
     * <br>
     * TRANSFER has portfolio and portfolio or account and account
     * <br>
     * {@code leftReferencePrice} is a rate of <i>left reference</i> and transaction currency.
     */
    protected BigDecimal leftReferencePrice;

    /**
     * Similiarily to {@link #leftReferencePrice}, it is a rate of <i>right reference</i> and transaction currency.
     */
    protected BigDecimal rightReferencePrice;

    /**
     * arbitrary description of the transaction, i.e. description received from custodians or venues
     */
    protected String description;

    protected boolean settled;

    protected ZonedDateTime settledDateTime;

    protected Long settlementId;

    protected boolean isLive;

    @Enumerated(EnumType.STRING)
    protected SettlementType settlementType;

    @Column(name = "transaction_type", insertable = false, updatable = false)
    @Enumerated(EnumType.STRING)
    protected TransactionType transactionType;

    @OneToMany(
        cascade = CascadeType.ALL,
        fetch = FetchType.EAGER,
        orphanRemoval = true
    )
    @JoinColumn(name = "transaction_id")
    protected Collection<TransactionFee> fees = new ArrayList<>();

    public Transaction(String uuid,
                       String reservationRef,
                       ZonedDateTime dateTime,
                       String executionId,
                       String venueExecutionId,
                       GenericReference leftReference,
                       GenericReference rightReference,
                       BigDecimal leftReferencePrice,
                       BigDecimal rightReferencePrice,
                       Collection<TransactionFee> fees,
                       String description,
                       boolean isLive,
                       SettlementType settlementType) {
        this.uuid = uuid;
        this.reservationRef = reservationRef;
        this.dateTime = dateTime;
        this.executionId = executionId;
        this.venueExecutionId = venueExecutionId;
        this.leftReference = leftReference;
        this.rightReference = rightReference;
        this.leftReferencePrice = leftReferencePrice;
        this.rightReferencePrice = rightReferencePrice;
        this.description = description;
        this.settlementType = settlementType;
        this.isLive = isLive;

        settleIfPossible();

        fees.forEach(this::addTransactionFee);
    }

    public Transaction() {
        // jpa
    }

    public void addTransactionFee(TransactionFee transactionFee) {
        fees.add(transactionFee);
    }

    @Transient
    @Override
    public abstract Collection<RawLedgerEntry> getLedgerEntries();

    @Transient
    protected abstract Set<String> getDirectlyAffectedAccountIds();

    @Transient
    protected abstract Set<String> getDirectlyAffectedPortfolioIds();

    @Transient
    public Set<String> getAffectedAccountIds() {
        Set<String> accountIds = new HashSet<>(getDirectlyAffectedAccountIds());
        accountIds.addAll(getAccountIdsAffectedByFees());
        return accountIds;
    }

    @Transient
    public Set<String> getAffectedPortfolioIds() {
        Set<String> portfolioIds = new HashSet<>(getDirectlyAffectedPortfolioIds());
        portfolioIds.addAll(getPortfolioIdsAffectedByFees());
        return portfolioIds;
    }

    @Transient
    protected Set<String> getPortfolioIdsAffectedByFees() {
        return getFees().stream()
            .flatMap(f -> {
                String leftPortfolioId = f.getLeftReference().getPortfolioId();
                String rightPortfolioId = f.getRightReference().getPortfolioId();
                return Stream.of(leftPortfolioId, rightPortfolioId);
            })
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
    }

    @Transient
    protected Set<Currency> getFeeCurrencies() {
        return getFees().stream()
            .map(TransactionFee::getCurrency)
            .collect(Collectors.toSet());
    }

    @Transient
    @Override
    public Set<Instrument> getInstrumentsAndFeeCurrencies() {
        HashSet<Instrument> result = new HashSet<>(getInstruments());
        result.addAll(getFeeCurrencies());
        return result;
    }

    @Transient
    protected Set<String> getAccountIdsAffectedByFees() {
        return getFees().stream()
            .flatMap(f -> {
                String leftAccountId = f.getLeftReference().getAccountId();
                String rightAccountId = f.getRightReference().getAccountId();
                return Stream.of(leftAccountId, rightAccountId);
            })
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
    }

    public SettlementType getSettlementType() {
        return settlementType;
    }

    public void settleIfPossible() {
        boolean isSettledByDefault = getSettlementType() == SettlementType.INSTANT_SETTLEMENT;

        if (isSettledByDefault) {
            settled = true;
            settledDateTime = ZonedDateTime.now();
        }
    }

    public String getUuid() {
        return uuid;
    }

    public String getReservationRef() {
        return reservationRef;
    }

    public ZonedDateTime getDateTime() {
        return dateTime;
    }

    public String getExecutionId() {
        return executionId;
    }

    public String getVenueExecutionId() {
        return venueExecutionId;
    }

    public Collection<TransactionFee> getFees() {
        return fees;
    }

    public String getDescription() {
        return description;
    }

    public boolean isLive() {
        return isLive;
    }

    public boolean isSettled() {
        return settled;
    }

    public void setSettled(boolean settled) {
        this.settled = settled;
    }

    public ZonedDateTime getSettledDateTime() {
        return settledDateTime;
    }

    public void setSettledDateTime(ZonedDateTime settledDateTime) {
        this.settledDateTime = settledDateTime;
    }

    public Long getSettlementId() {
        return settlementId;
    }

    public Transaction setSettlementId(Long settlementId) {
        this.settlementId = settlementId;
        return this;
    }

    protected Collection<TransactionFee> negated(Collection<TransactionFee> fees) {
        return fees.stream()
            .map(fee -> new TransactionFee(fee.getAmount().negate(), fee.getCurrency(), fee.getDescription(), fee.getFeeType()))
            .toList();
    }

    protected RawLedgerEntry ledgerEntry(LedgerEntryType ledgerEntryType, BigDecimal quantity, BigDecimal price, Collection<TransactionFee> fees, GenericReference reference, Instrument instrument) {
        return new RawLedgerEntry(ledgerEntryType, quantity, price, fees, reference, instrument, reservationRef, getUuid(), isSettled());
    }

    protected RawLedgerEntry reservation(LedgerEntryType ledgerEntryType, BigDecimal quantity, BigDecimal price, GenericReference reference, Instrument instrument) {
        return new RawLedgerEntry(ledgerEntryType, quantity, price, NO_FEES, reference, instrument, reservationRef, getUuid(), true);
    }

    protected RawLedgerEntry releaseRemaining(BigDecimal price, GenericReference reference, Instrument instrument) {
        return ledgerEntry(LedgerEntryType.RESERVATION_RELEASE_REMAINING, null, price, NO_FEES, reference, instrument);
    }

    @Transient
    public Transaction snapshot() {
        Transaction snapshot = doSnapshot();
        snapshot.setSettled(settled);
        snapshot.setSettledDateTime(settledDateTime);
        return snapshot;
    }

    @Transient
    protected abstract Transaction doSnapshot();
}
