package io.wyden.booking.domain.instrument;

import io.wyden.booking.domain.common.AuditedEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;

import java.math.BigDecimal;
import java.util.Objects;

@Entity
public class Security extends AuditedEntity implements Instrument {

    private String symbol;

    @Column(name = "asset")
    private String baseCurrency;

    @Column(name = "quote_currency")
    private String quoteCurrency;

    private BigDecimal contractSize;

    private boolean inverseContract;

    public Security(String symbol, String baseCurrency, String quoteCurrency, BigDecimal contractSize, boolean inverseContract) {
        this.symbol = symbol;
        this.baseCurrency = baseCurrency;
        this.quoteCurrency = quoteCurrency;
        this.contractSize = contractSize;
        this.inverseContract = inverseContract;
    }

    public Security(String symbol) {
        this.symbol = symbol;
        if (symbol.contains("/")) {
            String[] split = symbol.split("/");
            this.baseCurrency = split[0];
            this.quoteCurrency = split[1];
        } else {
            this.baseCurrency = symbol.substring(0, 3);
            this.quoteCurrency = symbol.substring(3);
        }
        this.contractSize = BigDecimal.ONE;
        this.inverseContract = false;
    }

    public Security() {
        // jpa
    }

    @Override
    public String getSymbol() {
        return symbol;
    }

    @Override
    public String getCurrency() {
        return baseCurrency;
    }

    public String getQuoteCurrency() {
        return quoteCurrency;
    }

    @Override
    public boolean isInverseContract() {
        return inverseContract;
    }

    @Override
    public BigDecimal getContractSize() {
        return contractSize;
    }

    @Override
    public String toString() {
        return symbol;
    }

    @Transient
    public AuditedEntity snapshot() {
        return new Security(symbol, baseCurrency, quoteCurrency, contractSize, inverseContract);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Security security = (Security) o;
        return inverseContract == security.inverseContract
            && Objects.equals(symbol, security.symbol)
            && Objects.equals(baseCurrency, security.baseCurrency)
            && Objects.equals(quoteCurrency, security.quoteCurrency)
            && Objects.equals(contractSize, security.contractSize);
    }

    @Override
    public int hashCode() {
        return Objects.hash(symbol, baseCurrency, quoteCurrency, contractSize, inverseContract);
    }
}
