alter table pending_transaction
    add column fee_currency text;

alter table pending_transaction
    add column currency text;

alter table pending_transaction
    add column base_currency text;

update pending_transaction t
set fee_currency = (select i.symbol from instrument i where t.fee_currency_id = i.id and i.asset_type = 'currency');

update pending_transaction t
set currency = (select i.symbol from instrument i where t.currency_id = i.id and i.asset_type = 'currency');

update pending_transaction t
set base_currency = (select i.symbol from instrument i where t.base_currency_id = i.id and i.asset_type = 'currency');

alter table pending_transaction
    drop column fee_currency_id;

alter table pending_transaction
    drop column currency_id;

alter table pending_transaction
    drop column base_currency_id;
