package io.wyden.booking.common;

import org.jbehave.core.model.ExamplesTable;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static io.wyden.booking.common.TestUtils.parseBigDecimal;
import static org.apache.commons.lang3.StringUtils.isBlank;

public class Table {

    private static final Collection<String> POSITION_HEADERS = List.of(
        "position",
        "portfolioId",
        "accountId",
        "instrument",
        "qty",
        "settledQty",
        "unsettledQty",
        "availableForTradingQty",
        "availableForWithdrawalQty",
        "pendingQty",
        "cost",
        "marketValue",
        "realizedPnL",
        "unrealizedPnL",
        "averagePrice"
    );

    private static final Collection<String> LEDGER_ENTRY_HEADERS = List.of(
        "ledgerEntryType",
        "price",
        "fee",
        "reservationRef",
        "balanceBefore",
        "balanceAfter"
    );

    private static final Collection<String> SETTLEMENT_HEADERS = List.of(
        "direction",
        "type",
        "sourceId",
        "targetId",
        "targetType"
    );

    private static final Collection<String> KNOWN_HEADERS = Stream.of(
            POSITION_HEADERS.stream(),
            LEDGER_ENTRY_HEADERS.stream(),
            SETTLEMENT_HEADERS.stream())
        .flatMap(stream -> stream)
        .toList();

    private final ExamplesTable underlying;

    public Table(ExamplesTable underlying) {
        this.underlying = underlying;

        List<String> unknownHeaders = underlying.getHeaders().stream()
            .filter(header -> !KNOWN_HEADERS.contains(header))
            .toList();

        if (!unknownHeaders.isEmpty()) {
            throw new IllegalArgumentException("Unknown headers: %s. Should be one of: %s".formatted(unknownHeaders, KNOWN_HEADERS));
        }
    }

    public Collection<Row> getRows() {
        return underlying.getRows().stream()
            .map(Row::new)
            .toList();
    }

    public record Row(Map<String, String> row) {

        public String getString(String key) {
            return row.get(key);
        }

        public BigDecimal getBigDecimal(String key) {
            String value = getString(key);
            if (isBlank(value)) {
                return null;
            }

            return parseBigDecimal(value);
        }
    }
}
