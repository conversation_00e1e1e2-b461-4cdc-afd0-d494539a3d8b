package io.wyden.booking.common;

import io.wyden.booking.domain.portfolio.PortfolioProvider;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;

import java.util.Map;

import static org.apache.commons.lang3.StringUtils.isBlank;

public class Mock<PERSON>ortfolioProvider implements PortfolioProvider {

    private final Map<String, Portfolio> portfolios;

    public MockPortfolioProvider(Map<String, Portfolio> portfolios) {
        this.portfolios = portfolios;
    }

    @Override
    public String getPortfolioCurrency(String portfolioId) {
        String currency = portfolios.get(portfolioId).getPortfolioCurrency();
        if (isBlank(currency)) {
            throw new IllegalArgumentException("Portfolio currency not found for: " + portfolioId);
        }

        return currency;
    }

    @Override
    public Portfolio getPortfolio(String portfolioId) {
        return portfolios.get(portfolioId);
    }

    public void setPortfolio(String portfolioId, String currency, PortfolioType portfolioType) {
        portfolios.put(portfolioId, Portfolio.newBuilder()
            .setId(portfolioId)
            .setPortfolioCurrency(currency)
            .setPortfolioType(portfolioType)
            .build());
    }
}
