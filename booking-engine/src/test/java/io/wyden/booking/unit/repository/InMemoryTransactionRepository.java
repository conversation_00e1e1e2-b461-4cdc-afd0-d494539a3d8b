package io.wyden.booking.unit.repository;

import io.wyden.booking.domain.settlement.Settlement;
import io.wyden.booking.domain.transaction.Transaction;
import io.wyden.booking.domain.transaction.TransactionRepository;
import io.wyden.booking.domain.transaction.trade.ClientAssetTrade;
import io.wyden.booking.domain.transaction.trade.ClientCashTrade;
import io.wyden.booking.domain.transaction.trade.StreetAssetTrade;
import io.wyden.booking.domain.transaction.trade.StreetCashTrade;
import io.wyden.booking.interfaces.rest.RequestModel;
import io.wyden.booking.utils.CollectionUtils;
import io.wyden.cloudutils.tools.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static io.wyden.booking.utils.CollectionUtils.containsAnyOrEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;

public class InMemoryTransactionRepository extends InMemoryRepositoryBase<Transaction, Long> implements TransactionRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(InMemoryTransactionRepository.class);

    public InMemoryTransactionRepository(Map<Long, Transaction> transactions) {
        super(transactions);
    }

    @Override
    public long countByProperties(RequestModel.AuthorizedTransactionSearch request) {
        return findByProperties(request).size();
    }

    @Override
    public long countByProperties(RequestModel.AuthorizedTransactionSearch request, String after) {
        return findByProperties(request).stream()
            .filter(transaction -> {
                Timestamp cursor = DateUtils.epochMicrosToSqlTimestamp(after);
                if (request.sortingOrder() == RequestModel.SortingOrder.ASC) {
                    return transaction.getUpdatedAt().after(cursor);
                }

                return transaction.getUpdatedAt().before(cursor);
            }).count();
    }

    @Override
    public Collection<Transaction> findByProperties(RequestModel.AuthorizedTransactionSearch request) {
        Collection<String> orderIdList = CollectionUtils.wrap(request.orderId());
        Collection<String> reservationRefList = CollectionUtils.wrap(request.reservationRef());

        return cache.values().stream()
            .filter(transaction -> {
                if (transaction instanceof ClientCashTrade trade) {
                    return containsAnyOrEmpty(request.symbol(), trade.getBaseCurrency().getSymbol(), trade.getCurrency().getSymbol())
                        && containsAnyOrEmpty(request.currency(), trade.getBaseCurrency().getSymbol(), trade.getCurrency().getSymbol())
                        && containsAnyOrEmpty(request.requestedAuthorizedPortfolioIds(), trade.getPortfolio().getReferenceId(), trade.getCounterPortfolio().getReferenceId())
                        && containsAnyOrEmpty(orderIdList, trade.getOrderId())
                        && containsAnyOrEmpty(reservationRefList, trade.getReservationRef());
                }

                if (transaction instanceof StreetCashTrade trade) {
                    return containsAnyOrEmpty(request.symbol(), trade.getBaseCurrency().getSymbol(), trade.getCurrency().getSymbol())
                        && containsAnyOrEmpty(request.currency(), trade.getBaseCurrency().getSymbol(), trade.getCurrency().getSymbol())
                        && containsAnyOrEmpty(request.requestedAuthorizedAccountIds(), trade.getAccount().getReferenceId())
                        && containsAnyOrEmpty(request.requestedAuthorizedPortfolioIds(), trade.getPortfolio().getReferenceId())
                        && containsAnyOrEmpty(orderIdList, trade.getOrderId())
                        && containsAnyOrEmpty(reservationRefList, trade.getReservationRef());
                }

                if (transaction instanceof ClientAssetTrade trade) {
                    return containsAnyOrEmpty(request.symbol(), trade.getSecurity().getSymbol())
                        && containsAnyOrEmpty(request.requestedAuthorizedPortfolioIds(), trade.getPortfolio().getReferenceId(), trade.getCounterPortfolio().getReferenceId())
                        && containsAnyOrEmpty(orderIdList, trade.getOrderId())
                        && containsAnyOrEmpty(reservationRefList, trade.getReservationRef());
                }

                if (transaction instanceof StreetAssetTrade trade) {
                    return containsAnyOrEmpty(request.symbol(), trade.getSecurity().getSymbol())
                        && containsAnyOrEmpty(request.requestedAuthorizedAccountIds(), trade.getAccount().getReferenceId())
                        && containsAnyOrEmpty(request.requestedAuthorizedPortfolioIds(), trade.getPortfolio().getReferenceId())
                        && containsAnyOrEmpty(orderIdList, trade.getOrderId())
                        && containsAnyOrEmpty(reservationRefList, trade.getReservationRef());
                }

                return false;
            })
            .toList();
    }

    @Override
    public Optional<Transaction> findByUuid(String transactionUuid) {
        return cache.values().stream()
            .filter(transaction -> Objects.equals(transactionUuid, transaction.getUuid()))
            .findFirst();
    }

    @Override
    public Collection<Transaction> findByExecutionIdIn(Collection<String> executionIds) {
        return cache.values().stream()
            .filter(transaction -> executionIds.contains(transaction.getExecutionId()))
            .toList();
    }

    @Override
    public void deleteByProperties(RequestModel.AuthorizedTransactionSearch request) {
        Collection<Transaction> transactionsToRemove = findByProperties(request);
        LOGGER.debug("Removing {} transactions", transactionsToRemove.size());
        transactionsToRemove.forEach(transaction -> {
            cache.remove(transaction.getId());
        });
    }

    @Override
    public Collection<Settlement> findSettlements(RequestModel.SettlementSearch request) {
        return cache.values().stream()
            .filter(Settlement.class::isInstance)
            .map(Settlement.class::cast)
            .filter(settlement -> isBlank(request.clientSettlementId()) || request.clientSettlementId().equalsIgnoreCase(settlement.getDescription()))
            .toList();
    }

    @Override
    public long countSettlements(RequestModel.SettlementSearch request) {
        return findSettlements(request).size();
    }

    @Override
    public long countSettlements(RequestModel.SettlementSearch request, String after) {
        return findSettlements(request).stream()
            .filter(settlement -> {
                ZonedDateTime cursor = DateUtils.epochMicrosToZonedDateTime(after);
                return settlement.getDateTime().isAfter(cursor);
            })
            .count();
    }
}
