package io.wyden.booking.unit.repository;

import io.wyden.booking.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.domain.ledgerentry.LedgerEntryRepository;
import io.wyden.booking.domain.position.AccountReference;
import io.wyden.booking.domain.position.PortfolioReference;
import io.wyden.booking.interfaces.rest.RequestModel;
import io.wyden.booking.interfaces.rest.RequestModel.LedgerEntrySearch;
import io.wyden.cloudutils.tools.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class InMemoryLedgerEntryRepository extends InMemoryRepositoryBase<LedgerEntry, Long> implements LedgerEntryRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(InMemoryPositionRepository.class);

    public InMemoryLedgerEntryRepository(Map<Long, LedgerEntry> ledgerEntries) {
        super(ledgerEntries);
    }

    @Override
    public long countByProperties(LedgerEntrySearch request) {
        return findByProperties(request).size();
    }

    @Override
    public long countByProperties(LedgerEntrySearch request, String after) {
        return findByProperties(request).stream()
            .filter(ledgerEntry -> {
                Timestamp cursor = DateUtils.epochMicrosToSqlTimestamp(after);
                if (request.sortingOrder() == RequestModel.SortingOrder.ASC) {
                    return ledgerEntry.getUpdatedAt().after(cursor);
                }

                return ledgerEntry.getUpdatedAt().before(cursor);
            })
            .count();
    }

    @Override
    public Collection<LedgerEntry> findByProperties(LedgerEntrySearch request) {
        return cache.values().stream()
            .filter(ledgerEntry -> request.symbol().isEmpty() || request.symbol().contains(ledgerEntry.getInstrument().getSymbol()))
            .filter(ledgerEntry -> request.currency().isEmpty() || request.currency().contains(ledgerEntry.getInstrument().getCurrency()))
            .filter(ledgerEntry -> request.accountId().isEmpty() || (ledgerEntry.getReference() instanceof AccountReference && request.accountId().contains(ledgerEntry.getReference().getAccountId())))
            .filter(ledgerEntry -> request.portfolio().isEmpty() || (ledgerEntry.getReference() instanceof PortfolioReference && request.portfolio().contains(ledgerEntry.getReference().getPortfolioId())))
            .filter(ledgerEntry -> request.ledgerEntryType().isEmpty() || (request.ledgerEntryType().contains(ledgerEntry.getType().name())))
            .filter(ledgerEntry -> isBlank(request.orderId()) || request.orderId().equals(ledgerEntry.getReservationRef()))
            .filter(ledgerEntry -> isBlank(request.transactionId()) || request.transactionId().equals(ledgerEntry.getTransactionId()))
            .toList();
    }

    @Override
    public Collection<LedgerEntry> findByReservationRef(String reservationRef) {
        return cache.values().stream()
            .filter(l -> Objects.equals(l.getReservationRef(), reservationRef))
            .toList();
    }

    @Override
    public BigDecimal sumReservationsByInstrumentAndReferenceAndReservationRef(String currency, String portfolioId, String accountId, String reservationRef) {
        return cache.values().stream()
            .filter(ledgerEntry -> currency.equals(ledgerEntry.getInstrument().getSymbol()))
            .filter(ledgerEntry -> (isNotBlank(portfolioId) && portfolioId.equals(ledgerEntry.getReference().getPortfolioId())) || (isNotBlank(accountId) && accountId.equals(ledgerEntry.getReference().getAccountId())))
            .filter(ledgerEntry -> reservationRef.equals(ledgerEntry.getReservationRef()))
            .filter(ledgerEntry -> ledgerEntry.getType().isReservationOrRelease())
            .map(LedgerEntry::getQuantity)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public void deleteByProperties(LedgerEntrySearch request) {
        Collection<LedgerEntry> ledgerEntriesToRemove = findByProperties(request);
        LOGGER.debug("Removing {} ledger entries", ledgerEntriesToRemove.size());
        ledgerEntriesToRemove.forEach(ledgerEntry -> {
            cache.remove(ledgerEntry.getId());
        });
    }
}
