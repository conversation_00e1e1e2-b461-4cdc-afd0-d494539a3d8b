package io.wyden.booking.spring;

import io.wyden.booking.common.OemsRequestFactory;
import io.wyden.booking.domain.instrument.Currency;
import io.wyden.booking.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.domain.position.CashPosition;
import io.wyden.booking.domain.position.PortfolioReference;
import io.wyden.booking.domain.reservation.Reservation;
import io.wyden.published.booking.OemsRequestWithVolatilityBuffer;
import io.wyden.published.booking.command.Command;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.referencedata.Portfolio;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;
import java.util.UUID;

import static io.wyden.booking.assertions.BookingEngineAssertions.assertThat;
import static io.wyden.booking.spring.SpringTestConfiguration.getRate;
import static io.wyden.booking.spring.SpringTestConfiguration.getRateKey;
import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.awaitility.Awaitility.await;

@Sql(scripts = "/clear-database.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
public class BuyingPowerCheckTest extends AbstractSpringTest {

    public static final String BTC_USD_RATE = "60000";

    @Override
    @Before
    public void setUp() {
        super.setUp();
        ratesMap.put(getRateKey("BTC", "USD"), getRate("BTC", "USD", BTC_USD_RATE));
        stateManager.clear();
        walCommandsRepository.deleteAll();
        snapshotterTrackingRepository.deleteAll();
    }

    @Test
    public void shouldRejectReservationIfNotEnoughBuyingPower() throws Exception {
        OemsRequest oemsRequest = OemsRequestFactory.createOemsRequestLimit(bd(BTC_USD_RATE), bd("0.5"))
            .setBaseCurrency("BTC")
            .setQuoteCurrency("USD")
            .build();

        portfoliosMap.put(oemsRequest.getPortfolioId(), Portfolio.newBuilder().setPortfolioCurrency("USD").build());
        portfoliosMap.put(oemsRequest.getExecutionConfig().getCounterPortfolio(), Portfolio.newBuilder().setPortfolioCurrency("USD").build());
        stateManager.findOrCreatePortfolio(oemsRequest.getPortfolioId());
        stateManager.findOrCreatePortfolio(oemsRequest.getExecutionConfig().getCounterPortfolio());

        String reservationRequestId = UUID.randomUUID().toString();
        String oemsRequestId = oemsRequest.getMetadata().getRequestId();
        Command requestWithVolatilityBuffer = asReservationCommandWithBuffer(reservationRequestId, oemsRequest, "0.01");
        commandConsumer.consume(requestWithVolatilityBuffer);

        await().until(() -> messageHistoryService.isCompleted(reservationRequestId));

        // no reservation was processed
        List<Reservation> reservations = reservationRepository.findAll();
        assertThat(reservations).isEmpty();

        // no ledger entries are created
        List<LedgerEntry> ledgerEntries = ledgerEntryRepository.findAll();
        assertThat(ledgerEntries).isEmpty();

        // no reservations are marked as completed
        boolean completed = messageHistoryService.isCompleted(oemsRequestId);
        assertThat(completed).isFalse();
    }

    @Test
    public void shouldRejectReservationFromRiskEngineButPermitReservationFromOrderInFlight() throws Exception {
        OemsRequest oemsRequest = OemsRequestFactory.createOemsRequestLimit(bd(BTC_USD_RATE), bd("0.5"))
            .setBaseCurrency("BTC")
            .setQuoteCurrency("USD")
            .build();

        String portfolioId = oemsRequest.getPortfolioId();
        String counterPortfolioId = oemsRequest.getExecutionConfig().getCounterPortfolio();

        portfoliosMap.put(portfolioId, Portfolio.newBuilder().setPortfolioCurrency("USD").build());
        portfoliosMap.put(counterPortfolioId, Portfolio.newBuilder().setPortfolioCurrency("USD").build());
        stateManager.findOrCreatePortfolio(oemsRequest.getPortfolioId());
        stateManager.findOrCreatePortfolio(oemsRequest.getExecutionConfig().getCounterPortfolio());

        // reservation requests sent from Risk Engine are guarded by buying power check - reservations will be rejected, if balance goes negative
        String reservationRequestId = UUID.randomUUID().toString();
        Command requestWithVolatilityBuffer = asReservationCommandWithBuffer(reservationRequestId, oemsRequest, "0.01");
        commandConsumer.consume(requestWithVolatilityBuffer);
        await().until(() -> messageHistoryService.isCompleted(reservationRequestId));

        // reservation requests created based on Order in flight are not guarded - reservations will be created even if balance goes negative
        oemsRequestProcessor.enqueue(oemsRequest);

        await().until(() -> messageHistoryService.isCompleted(oemsRequest.getMetadata().getRequestId()));
        statePersistenceScheduler.flushAll();

        // only one reservation was processed
        List<Reservation> reservations = reservationRepository.findAll();
        assertThat(reservations).hasSize(1);

        // four ledger entries are created: two per each side of reservation (BTC reservation, USD reservation)
        List<LedgerEntry> ledgerEntries = ledgerEntryRepository.findAll();
        assertThat(ledgerEntries).hasSize(4);
    }

    @Test
    public void shouldPermitReservationIfEnoughBuyingPower() throws Exception {
        OemsRequest oemsRequest = OemsRequestFactory.createOemsRequestLimit(bd(BTC_USD_RATE), bd("0.5"))
            .setBaseCurrency("BTC")
            .setQuoteCurrency("USD")
            .build();

        String portfolioId = oemsRequest.getPortfolioId();
        String counterPortfolioId = oemsRequest.getExecutionConfig().getCounterPortfolio();

        portfoliosMap.put(portfolioId, Portfolio.newBuilder().setPortfolioCurrency("USD").build());
        portfoliosMap.put(counterPortfolioId, Portfolio.newBuilder().setPortfolioCurrency("USD").build());

        PortfolioReference portfolio = stateManager.findOrCreatePortfolio(portfolioId);
        PortfolioReference counterPortfolio = stateManager.findOrCreatePortfolio(counterPortfolioId);

        // make sure client has enough balance to buy
        stateManager.store(new CashPosition(bd("50000"), portfolio, new Currency("USD")));

        // make sure bank has enough balance to sell
        stateManager.store(new CashPosition(bd("10"), counterPortfolio, new Currency("BTC")));

        String reservationRequestId = UUID.randomUUID().toString();
        Command requestWithVolatilityBuffer = asReservationCommandWithBuffer(reservationRequestId, oemsRequest, "0.01");
        commandConsumer.consume(requestWithVolatilityBuffer);

        await().until(() -> messageHistoryService.isCompleted(reservationRequestId));
        statePersistenceScheduler.flushAll();

        // one reservation is created
        List<Reservation> reservations = reservationRepository.findAll();
        assertThat(reservations).hasSize(1);

        // six ledger entries are created: three per each side of reservation (BTC reservation, USD reservation, USD buffer reservation)
        List<LedgerEntry> ledgerEntries = ledgerEntryRepository.findAll();
        assertThat(ledgerEntries).hasSize(6);
    }

    @Test
    public void shouldPermitReservationIfEnoughBuyingPowerThenRejectDuplicates() throws Exception {
        OemsRequest oemsRequest = OemsRequestFactory.createOemsRequestLimit(bd(BTC_USD_RATE), bd("0.5"))
            .setBaseCurrency("BTC")
            .setQuoteCurrency("USD")
            .build();

        String portfolioId = oemsRequest.getPortfolioId();
        String counterPortfolioId = oemsRequest.getExecutionConfig().getCounterPortfolio();

        portfoliosMap.put(portfolioId, Portfolio.newBuilder().setPortfolioCurrency("USD").build());
        portfoliosMap.put(counterPortfolioId, Portfolio.newBuilder().setPortfolioCurrency("USD").build());

        PortfolioReference portfolio = stateManager.findOrCreatePortfolio(portfolioId);
        PortfolioReference counterPortfolio = stateManager.findOrCreatePortfolio(counterPortfolioId);

        // make sure client has enough balance to buy
        stateManager.store(new CashPosition(bd("50000"), portfolio, new Currency("USD")));

        // make sure bank has enough balance to sell
        stateManager.store(new CashPosition(bd("10"), counterPortfolio, new Currency("BTC")));

        String reservationRequestId = UUID.randomUUID().toString();
        Command requestWithVolatilityBuffer = asReservationCommandWithBuffer(reservationRequestId, oemsRequest, "0.01");
        commandConsumer.consume(requestWithVolatilityBuffer);

        await().until(() -> messageHistoryService.isCompleted(reservationRequestId));

        // reservation requests sent via rabbitmq is duplicate and should not be processed
        oemsRequestProcessor.enqueue(oemsRequest);
        await().until(() -> messageHistoryService.isCompleted(oemsRequest.getMetadata().getRequestId()));

        statePersistenceScheduler.flushAll();

        // one reservation is created
        List<Reservation> reservations = reservationRepository.findAll();
        assertThat(reservations).hasSize(1);

        // six ledger entries are created: three per each side of reservation (BTC reservation, USD reservation, USD buffer reservation)
        List<LedgerEntry> ledgerEntries = ledgerEntryRepository.findAll();
        assertThat(ledgerEntries).hasSize(6);
    }

    private Command asReservationCommandWithBuffer(String requestId, OemsRequest oemsRequest, String value) {
        OemsRequestWithVolatilityBuffer request = OemsRequestWithVolatilityBuffer.newBuilder()
            .setMetadata(Metadata.newBuilder().setRequestId(requestId).build())
            .setOemsRequest(oemsRequest)
            .setVolatilityBuffer(value)
            .build();

        // use preprocessor on the inbound to assign market rate on arrival
        OemsRequestWithVolatilityBuffer preprocessed = buyingPowerReservationWithBufferCommandProcessor.preProcessAndSave(request);

        return Command.newBuilder().setOemsRequestWithBuffer(preprocessed).build();
    }
}
