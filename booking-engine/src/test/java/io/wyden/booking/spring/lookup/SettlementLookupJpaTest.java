package io.wyden.booking.spring.lookup;

import io.wyden.booking.application.transaction.TransactionQueryService;
import io.wyden.booking.common.MockPortfolioProvider;
import io.wyden.booking.domain.settlement.Settlement;
import io.wyden.booking.interfaces.rest.RequestModel;
import io.wyden.booking.spring.AbstractSpringTest;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.CursorConnection;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static io.wyden.booking.assertions.BookingEngineAssertions.assertThat;
import static io.wyden.booking.domain.common.Identifiers.randomIdentifier;
import static io.wyden.booking.domain.common.Identifiers.randomSuffix;
import static io.wyden.cloudutils.tools.DateUtils.instantToEpochMillis;
import static org.springframework.test.annotation.DirtiesContext.MethodMode.AFTER_METHOD;

@Sql(scripts = "/clear-database.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
public class SettlementLookupJpaTest extends AbstractSpringTest {

    @Override
    @Before
    public void setUp() {
        super.setUp();
        stateManager.clear();
        transactionQueryService = new TransactionQueryService(transactionRepository, accessGatewayService, new MockPortfolioProvider(new HashMap<>()), accountProvider);
    }

    @Test
    @DirtiesContext(methodMode = AFTER_METHOD)
    public void shouldReturnEmptyConnectionWhenNoSettlements() {
        CursorConnection cursorConnection = transactionQueryService.lookupSettlements(RequestModel.SettlementSearch.all());

        assertThat(cursorConnection.getEdgesCount()).isEqualTo(0);
        assertThat(cursorConnection.getPageInfo().getEndCursor()).isBlank();
        assertThat(cursorConnection.getPageInfo().getHasNextPage()).isFalse();
    }

    @Test
    @DirtiesContext(methodMode = AFTER_METHOD)
    public void shouldReturnSettlement() {
        createSettlement("2024-01-01", "e1", "e2", "e3");

        CursorConnection cursorConnection = transactionQueryService.lookupSettlements(RequestModel.SettlementSearch.all());

        System.out.println(cursorConnection);
        assertThat(cursorConnection.getEdgesCount()).isEqualTo(1);
        assertThat(cursorConnection.getPageInfo().getEndCursor()).isNotBlank();
        assertThat(cursorConnection.getPageInfo().getHasNextPage()).isFalse();
    }

    @Test
    @DirtiesContext(methodMode = AFTER_METHOD)
    public void shouldReturnSettlementByExecutionId() {
        Settlement s1 = createSettlement("2024-01-01", "e1", "e2", "e3");
        Settlement s2 = createSettlement("2024-01-02", "findMe");

        RequestModel.SettlementSearch search = RequestModel.SettlementSearch
            .limit(1)
            .withExecutionId("findMe");

        CursorConnection cursorConnection = transactionQueryService.lookupSettlements(search);

        System.out.println(cursorConnection);
        assertThat(cursorConnection.getEdgesCount()).isEqualTo(1);
        assertThat(cursorConnection.getPageInfo().getEndCursor()).isNotBlank();
        assertThat(cursorConnection.getPageInfo().getHasNextPage()).isFalse();
    }

    @Test
    @DirtiesContext(methodMode = AFTER_METHOD)
    public void shouldReturnPagedSettlements() {
        Settlement s1 = createSettlement("2024-01-01", "e1");
        Settlement s2 = createSettlement("2024-01-02", "e2");
        Settlement s3 = createSettlement("2024-01-03", "e3");
        Settlement s4 = createSettlement("2024-01-04", "e4");
        Settlement s5 = createSettlement("2024-01-05", "e5");
        Settlement s6 = createSettlement("2024-01-06", "e6");

        RequestModel.SettlementSearch search = RequestModel.SettlementSearch
            .limit(5);

        CursorConnection cursorConnection = transactionQueryService.lookupSettlements(search);
        System.out.println(cursorConnection);
        String endCursor = cursorConnection.getPageInfo().getEndCursor();
        assertThat(endCursor).isNotBlank();
        assertThat(cursorConnection.getEdgesCount()).isEqualTo(5);
        assertThat(cursorConnection.getPageInfo().getHasNextPage()).isTrue();

        CursorConnection nextConnection = transactionQueryService.lookupSettlements(search.after(endCursor));
        System.out.println(nextConnection);
        assertThat(nextConnection.getEdgesCount()).isEqualTo(1);
        assertThat(nextConnection.getPageInfo().getEndCursor()).isNotBlank();
        assertThat(nextConnection.getPageInfo().getHasNextPage()).isFalse();
    }

    @Test
    @DirtiesContext(methodMode = AFTER_METHOD)
    public void shouldReturnSettlementsByDate() {
        Settlement s1 = createSettlement("2024-01-01", "e1");
        Settlement s2 = createSettlement("2024-01-02", "e2");
        Settlement s3 = createSettlement("2024-01-03", "e3");
        Settlement s4 = createSettlement("2024-01-04", "e3");
        Settlement s5 = createSettlement("2024-01-05", "e3");

        // from s2 to s4 (including both ends)
        String from = instantToEpochMillis(s2.getDateTime().toInstant());
        String to = instantToEpochMillis(s4.getDateTime().toInstant());

        RequestModel.SettlementSearch search = RequestModel.SettlementSearch
            .all()
            .withinRange(from, to);

        CursorConnection cursorConnection = transactionQueryService.lookupSettlements(search);
        System.out.println(cursorConnection);
        assertThat(cursorConnection.getEdgesCount()).isEqualTo(3);
        assertThat(cursorConnection.getPageInfo().getEndCursor()).isNotBlank();
        assertThat(cursorConnection.getPageInfo().getHasNextPage()).isFalse();

        // after s5 (including s5) - only one
        String last = instantToEpochMillis(s5.getDateTime().toInstant());
        RequestModel.SettlementSearch lastSearch = search
            .withinRange(last, null);

        CursorConnection lastConnection = transactionQueryService.lookupSettlements(lastSearch);
        System.out.println(lastConnection);
        assertThat(lastConnection.getEdgesCount()).isEqualTo(1);
        assertThat(lastConnection.getPageInfo().getEndCursor()).isNotBlank();
        assertThat(lastConnection.getPageInfo().getHasNextPage()).isFalse();

        // before s2 (including s2) - two results, one in page
        String second = instantToEpochMillis(s2.getDateTime().toInstant());
        RequestModel.SettlementSearch secondSearch = RequestModel.SettlementSearch
            .limit(1)
            .withinRange(null, second);

        CursorConnection secondConnection = transactionQueryService.lookupSettlements(secondSearch);
        System.out.println(secondConnection);
        assertThat(secondConnection.getEdgesCount()).isEqualTo(1);
        assertThat(secondConnection.getPageInfo().getEndCursor()).isNotBlank();
        assertThat(secondConnection.getPageInfo().getHasNextPage()).isTrue();
    }

    private Settlement createSettlement(String dateStr, String... executionId) {
        ZonedDateTime zonedDateTime = toZonedDateTime(dateStr);

        List<String> settledTransactionIds = Arrays.asList(executionId);
        Settlement settlement = new Settlement(randomIdentifier(), zonedDateTime, "", randomSuffix("settlement"), settledTransactionIds);
        stateManager.store(settlement);
        return settlement;
    }

    private static ZonedDateTime toZonedDateTime(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
        return LocalDateTime.of(date, LocalTime.NOON).atZone(DateUtils.UTC);
    }
}
