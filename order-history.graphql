
extend type Query {
    transferStates(input: TransferStateSearchInput): TransferStateConnection
}

extend type Subscription {
    transferStatesSubscription: TransferStateResponse!
}

type TransferStateConnection {
    edges: [TransferStateEdge!]!
    pageInfo: PageInfo!
}

type TransferStateEdge {
    node: TransferStateResponse!
    cursor: String!
}

input TransferStateSearchInput {
    transferStatePredicateInput: [TransferStatePredicateInput!]
    transferStateNumberPredicateInput: [TransferStateNumberPredicateInput!]
    transferStateDateTimePredicateInput: [TransferStateDateTimePredicateInput!]
    sortingOrder: SortingOrder
    first: Int
    after: String
}

input TransferStatePredicateInput {
    method: TransferStateCollectionPredicateType!
    field: TransferStateCollectionPredicateField!
    value: [String]!
}

input TransferStateNumberPredicateInput {
    method: TransferStateNumberPredicateType!
    field: TransferStateNumberPredicateField!
    value: [String]!
}

input TransferStateDateTimePredicateInput {
    method: TransferStateDateTimePredicateType!
    field: TransferStateDateTimePredicateField!
    value: [String]!
}


enum TransferStateCollectionPredicateField {
    FROM, TO, ASSET, STATUS, INITIATOR, INTERNAL_ID, CUSTODY_TRX, TRX_HASH
}

enum TransferStateCollectionPredicateType {
    IN
}

enum TransferStateNumberPredicateField {
    AMOUNT, FEE
}

enum TransferStateNumberPredicateType {
    EQUAL, GREATER, LESS
}

enum TransferStateDateTimePredicateField {
    INITIATE_DATETIME, EFFECTIVE_DATETIME, LAST_UPDATE_DATETIME
}

enum TransferStateDateTimePredicateType {
    EQUAL, GREATER, LESS
}

type TransferStateResponse {
    id: String!
    source: String!
    destination: String!
    quantity: String!
    asset: String!
    status: TransferStatus!
    initiator: String
    initiateDateTime: String  #Epoch Unix Timestamp
    effectiveDateTime: String #Epoch Unix Timestamp
    lastUpdateDateTime: String #Epoch Unix Timestamp
    internalId: String
    custodyTrx: String
    trxHash: String
    fee: String
}

enum TransferStatus {
    PENDING,
    PROCESSING,
    BROADCAST,
    CONFIRMED,
    COMPLETED,
    REJECTED,
    CANCELLED,
    FAILED
}