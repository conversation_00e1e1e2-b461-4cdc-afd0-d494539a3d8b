package io.wyden.rest.management;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.wyden.cloud.utils.rest.errorhandling.Error;
import io.wyden.rest.management.common.Identifiers;
import io.wyden.rest.management.security.authentication.AuthenticationService;
import io.wyden.rest.management.security.authentication.Headers;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.server.ResponseStatusException;

import java.nio.charset.StandardCharsets;
import java.time.Instant;

import static org.hamcrest.Matchers.blankOrNullString;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

public class ErrorHandlingTest extends SpringTestBase {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void shouldHandleServerErrors() throws Exception {
        when(bookingEngineHttpClient.getTransaction(anyString()))
            .thenThrow(RuntimeException.class);

        String transactionUuid = Identifiers.randomIdentifier();
        String path = "/api/v1/transactions/" + transactionUuid;
        String signature = AuthenticationService.calculateSignature(nonce, apiSecret, "GET", path, "");

        // There will be exception while processing the GET request, resulting in server-side error
        this.mockMvc
            .perform(get(path)
                .header(Headers.API_KEY, apiKey)
                .header(Headers.API_NONCE, nonce)
                .header(Headers.API_SIGNATURE, signature))
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isInternalServerError())
            .andExpect(jsonPath("$.timestamp").value(not(blankOrNullString())))
            .andExpect(jsonPath("$.status").value(HttpStatus.INTERNAL_SERVER_ERROR.value()))
            .andExpect(jsonPath("$.error").value(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase()))
            .andExpect(jsonPath("$.path").value(path))
            .andExpect(jsonPath("$.exception").value(is(blankOrNullString())))
            .andExpect(jsonPath("$.stackTrace").value(is(blankOrNullString())));
    }

    @Test
    void shouldHandleClientErrors() throws Exception {
        when(bookingEngineHttpClient.getTransaction(anyString()))
            .thenThrow(RuntimeException.class);

        String path = "/api/v1/transactions";
        String signature = AuthenticationService.calculateSignature(nonce, apiSecret, "POST", path, "");

        // POST request with missing / invalid body will cause client-side error
        this.mockMvc
            .perform(post(path)
                .contentType(MediaType.APPLICATION_JSON)
                .header(Headers.API_KEY, apiKey)
                .header(Headers.API_NONCE, nonce)
                .header(Headers.API_SIGNATURE, signature))
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isBadRequest())
            .andExpect(jsonPath("$.timestamp").value(not(blankOrNullString())))
            .andExpect(jsonPath("$.status").value(HttpStatus.BAD_REQUEST.value()))
            .andExpect(jsonPath("$.error").value(HttpStatus.BAD_REQUEST.getReasonPhrase()))
            .andExpect(jsonPath("$.path").value(path))
            .andExpect(jsonPath("$.exception").value(is(blankOrNullString())))
            .andExpect(jsonPath("$.stackTrace").value(is(blankOrNullString())));
    }

    @Test
    void shouldHandleNotFoundErrors() throws Exception {
        when(bookingEngineHttpClient.getTransaction(anyString()))
            .thenReturn(null);

        String transactionUuid = Identifiers.randomIdentifier();
        String path = "/api/v1/transactions/" + transactionUuid;
        String signature = AuthenticationService.calculateSignature(nonce, apiSecret, "GET", path, "");

        // Internal call to Booking Engine will return empty response, resulting in 404 error
        this.mockMvc
            .perform(get(path)
                .header(Headers.API_KEY, apiKey)
                .header(Headers.API_NONCE, nonce)
                .header(Headers.API_SIGNATURE, signature))
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isNotFound())
            .andExpect(jsonPath("$.timestamp").value(not(blankOrNullString())))
            .andExpect(jsonPath("$.status").value(HttpStatus.NOT_FOUND.value()))
            .andExpect(jsonPath("$.error").value(HttpStatus.NOT_FOUND.getReasonPhrase()))
            .andExpect(jsonPath("$.path").value(path))
            .andExpect(jsonPath("$.exception").value(is(blankOrNullString())))
            .andExpect(jsonPath("$.stackTrace").value(is(blankOrNullString())));
    }

    @Test
    void shouldHandleNestedErrors() throws Exception {
        // this exception is thrown inside booking-engine
        Error error = new Error(Instant.now(),
            400,
            "Bad request",
            "Error inside booking-engine",
            "/api/v1/transactions",
            HttpClientErrorException.BadRequest.class.getName(),
            "stacktrace");

        String body = objectMapper.writeValueAsString(error);
        HttpClientErrorException ex = new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Bad request", HttpHeaders.EMPTY, body.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);

        when(bookingEngineHttpClient.getTransaction(anyString()))
            .thenThrow(ex);

        String transactionUuid = Identifiers.randomIdentifier();
        String path = "/api/v1/transactions/" + transactionUuid;
        String signature = AuthenticationService.calculateSignature(nonce, apiSecret, "GET", path, "");

        // Internal call to Booking Engine will be successful, but processing inside Booking Engine will fail, resulting in HttpClientErrorException
        // thrown by RestTemplate
        this.mockMvc
            .perform(get(path)
                .header(Headers.API_KEY, apiKey)
                .header(Headers.API_NONCE, nonce)
                .header(Headers.API_SIGNATURE, signature))
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isBadRequest())
            .andExpect(jsonPath("$.timestamp").value(not(blankOrNullString())))
            .andExpect(jsonPath("$.status").value(HttpStatus.BAD_REQUEST.value()))
            .andExpect(jsonPath("$.error").value(HttpStatus.BAD_REQUEST.getReasonPhrase()))
            .andExpect(jsonPath("$.path").value(path))
            .andExpect(jsonPath("$.exception").value(is(blankOrNullString())))
            .andExpect(jsonPath("$.stackTrace").value(is(blankOrNullString())));
    }
}
