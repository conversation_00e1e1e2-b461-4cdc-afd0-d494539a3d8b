
spring.websecurity.debug=true

# logging
logging.level.root = info
logging.level.io.wyden = debug
logging.level.io.wyden.cloudutils.telemetry = warn
#logging.level.io.wyden.cloud.utils.spring.health = trace

logging.level.org.springframework=info
#logging.level.org.testcontainers=debug
#logging.level.org.springframework.web.reactive.function.client.ExchangeFunctions=TRACE
#logging.level.org.springframework.web=debug
#logging.level.org.springframework.http=debug
#logging.level.org.springframework.graphql=debug
#logging.level.org.springframework.security=debug
#logging.level.reactor.netty=debug

booking.reporting.balances.enabled=true