package io.wyden.rest.management.account;

import com.rabbitmq.client.AMQP;
import io.wyden.cloud.utils.spring.util.ExclusiveNameGenerator;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.ExpiringRabbitQueueBuilder;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.published.referencedata.account.AccountOnboardingAccessGrantedEvent;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.util.Arrays;
import java.util.Objects;

import static org.slf4j.LoggerFactory.getLogger;

@Component
public class AccountOnboardingEventConsumer implements MessageConsumer<AccountOnboardingAccessGrantedEvent> {

    private static final Logger LOGGER = getLogger(AccountOnboardingEventConsumer.class);

    private final String queueName;
    private final String consumerName;
    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<AccountOnboardingAccessGrantedEvent> accountOnboardingAccessGrantedEventExchange;
    private final Sinks.Many<AccountOnboardingAccessGrantedEvent> onboardingEventsSink;

    public AccountOnboardingEventConsumer(@Value("${rabbitmq.access-gateway-rest-management-queue.account-onboarding-event}") final String queueNameFormat,
                                          ExclusiveNameGenerator exclusiveNameGenerator,
                                          @Value("${spring.application.name}") String consumerName,
                                          RabbitIntegrator rabbitIntegrator,
                                          RabbitExchange<AccountOnboardingAccessGrantedEvent> accountOnboardingAccessGrantedEventExchange) {
        this.queueName = exclusiveNameGenerator.getQueueName(queueNameFormat);
        this.consumerName = consumerName;
        this.rabbitIntegrator = rabbitIntegrator;
        this.accountOnboardingAccessGrantedEventExchange = accountOnboardingAccessGrantedEventExchange;
        this.onboardingEventsSink = Sinks.many().replay().limit(100, Duration.ofMinutes(1));
    }

    @PostConstruct
    void init() {
        declareQueue();
        cleanupOldQueues("rest-management-queue.booking-engine.ACCOUNT-ONBOARDING-EVENT");
    }

    private void cleanupOldQueues(String... legacyQueueNames) {
        Arrays.stream(legacyQueueNames).forEach(rabbitIntegrator::tryDeleteQueue);
    }

    @Override
    public ConsumptionResult consume(AccountOnboardingAccessGrantedEvent accountOnboardingEvent, AMQP.BasicProperties basicProperties) {
        try {
            LOGGER.info("Consuming AccountOnboardingAccessGrantedEvent message, {}", accountOnboardingEvent);

            Sinks.EmitResult emitResult = onboardingEventsSink.tryEmitNext(accountOnboardingEvent);
            if (emitResult.isFailure()) {
                LOGGER.error("Failed to emit consumed onboarding event: {}, re-queueing event: {}", emitResult, accountOnboardingEvent);
                return ConsumptionResult.failureNeedsRequeue();
            }

        } catch (Exception e) {
            LOGGER.error("Failed to consume incoming: %s, re-queueing...".formatted(accountOnboardingEvent), e);
            return ConsumptionResult.failureNeedsRequeue();
        }

        return ConsumptionResult.consumed();
    }

    public Mono<AccountOnboardingAccessGrantedEvent> getOnboardingEvent(String correlationId) {
        return onboardingEventsSink.asFlux()
            .filter(event -> Objects.equals(event.getMetadata().getCorrelationObject(), correlationId))
            .next();
    }

    private void declareQueue() {
        RabbitQueue<AccountOnboardingAccessGrantedEvent> queue = new ExpiringRabbitQueueBuilder<AccountOnboardingAccessGrantedEvent>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .declare();

        queue.bindWithRoutingKey(accountOnboardingAccessGrantedEventExchange, StringUtils.EMPTY);
        LOGGER.info("Binding exchange {} and queue {}", accountOnboardingAccessGrantedEventExchange, queue);
        queue.attachConsumer(AccountOnboardingAccessGrantedEvent.parser(), this);
    }
}
