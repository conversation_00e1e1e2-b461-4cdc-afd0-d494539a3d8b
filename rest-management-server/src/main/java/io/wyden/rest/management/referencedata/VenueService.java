package io.wyden.rest.management.referencedata;

import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.published.referencedata.VenueCreateRequest;
import io.wyden.published.referencedata.VenueType;
import io.wyden.rest.management.domain.VenueModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

@Service
public class VenueService {

    private final VenueRequestEmitter venueRequestEmitter;
    private final LicenseService licenseService;
    private final String wydenExchangeEntitlements;

    public VenueService(VenueRequestEmitter venueRequestEmitter,
                        LicenseService licenseService,
                        @Value("${entitlement.wyden-exchange}") String wydenExchangeEntitlements) {
        this.venueRequestEmitter = venueRequestEmitter;
        this.licenseService = licenseService;
        this.wydenExchangeEntitlements = wydenExchangeEntitlements;
    }

    public void create(VenueCreateRequest request) {
        if (request.getVenueType().equals(VenueType.CLOB)) {
            if (!licenseService.hasEntitlement(wydenExchangeEntitlements)) {
                throw new AccessDeniedException("Access Denied: no (%s) entitlement for this license".formatted(wydenExchangeEntitlements));
            }
        }
        venueRequestEmitter.emit(request);
    }
}
