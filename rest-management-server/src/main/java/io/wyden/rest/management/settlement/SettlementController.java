package io.wyden.rest.management.settlement;

import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.rest.management.booking.BookingEngineService;
import io.wyden.rest.management.domain.SettlementModel;
import io.wyden.rest.management.domain.TransactionModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import io.wyden.rest.management.security.documentation.SecurityNote;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
@Validated
@RequestMapping(path = "${rest.management.context-path}")
public class SettlementController {

    private static final Logger LOGGER = getLogger(SettlementController.class);

    private final BookingEngineService bookingEngineService;

    public SettlementController(BookingEngineService bookingEngineService) {
        this.bookingEngineService = bookingEngineService;
    }

    @PostMapping("/settlements")
    @SecurityNote("Filtered by accessible data")
    public SettlementModel.SettlementResponse createSettlement(@Valid @RequestBody SettlementModel.SettlementRequest request,
                                                               @RequestParam(required = false, defaultValue = "false") boolean forceSettlement,
                                                               WydenAuthenticationToken authentication) {
        SettlementModel.SettlementResponse response = bookingEngineService.createSettlement(request, forceSettlement);

        LOGGER.info("Settlement created for request {}. Response: {}", request, response);

        return response;
    }

    @GetMapping("/settlements")
    public PaginationModel.CursorConnection<TransactionModel.Transaction> getSettlements(SettlementModel.SettlementSearch search, WydenAuthenticationToken authentication) {
        LOGGER.info("Getting settlements for {}", search);
        return bookingEngineService.searchSettlements(search, authentication.getClientId());
    }
}
