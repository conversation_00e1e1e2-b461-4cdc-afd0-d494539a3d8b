{"info": {"_postman_id": "f91a27d5-1982-4174-b7bd-630d710b6fac", "name": "Storage", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health check", "id": "fa698ee6-1c4f-42a4-aa3c-083016d0431c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{storage.url}}/actuator/health", "host": ["{{storage.url}}"], "path": ["actuator", "health"]}}, "response": []}]}