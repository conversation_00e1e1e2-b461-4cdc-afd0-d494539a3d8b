{"info": {"_postman_id": "73803e3e-5edf-489f-9f11-4714931f3a6a", "name": "Storage", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Clear map (including db)", "id": "ebb84853-97ce-488b-bd49-845f3ecd8903", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:8070/api/hz/reference-data-currencies", "protocol": "http", "host": ["localhost"], "port": "8070", "path": ["api", "hz", "reference-data-currencies"]}}, "response": []}, {"name": "Get all maps", "id": "5a969020-9bd0-4e56-850a-92d386e430b4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:62229/api/hz", "protocol": "http", "host": ["localhost"], "port": "62229", "path": ["api", "hz"]}}, "response": []}, {"name": "Get map", "id": "62b6dba6-8e04-4b76-b029-1d9deaa3d2b0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:62229/api/hz/permission-per-group", "protocol": "http", "host": ["localhost"], "port": "62229", "path": ["api", "hz", "permission-per-group"]}}, "response": []}, {"name": "Evict map (db is not affected)", "id": "e60e28f9-2c0b-4ae5-8f7c-1671c4e5f8bf", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:52652/api/hz/permissions-snapshot-per-groups/evict", "protocol": "http", "host": ["localhost"], "port": "52652", "path": ["api", "hz", "permissions-snapshot-per-groups", "evict"]}}, "response": []}, {"name": "Reload map (db is not affected)", "id": "1a606d14-83c0-443d-b3bf-58b88de171ab", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:52652/api/hz/permissions-snapshot-per-groups/reload", "protocol": "http", "host": ["localhost"], "port": "52652", "path": ["api", "hz", "permissions-snapshot-per-groups", "reload"]}}, "response": []}]}