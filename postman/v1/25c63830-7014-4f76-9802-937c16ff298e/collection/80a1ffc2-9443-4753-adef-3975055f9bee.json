{"info": {"_postman_id": "80a1ffc2-9443-4753-adef-3975055f9bee", "name": "Rest Management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "V1", "item": [{"name": "Get accounts", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "826bdead-416f-41cd-bcd1-f6e305128b7b"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "ba4b085f-3076-4001-8cef-df6e840e2e2e"}}], "id": "4ca1cefd-50ef-4cac-be67-c276082e17ed", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/accounts", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "accounts"]}}, "response": []}, {"name": "Get accounts positions", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "0dbdd5d1-a810-4bbe-bfb5-f09fe467a4fe"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "a9851ad2-bd12-4c26-bec9-4cc021184c5d"}}], "id": "b57f9da6-568e-4aa6-9e88-5f7b18383580", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/accounts/positions", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "accounts", "positions"]}}, "response": []}, {"name": "Get accounts balances", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "12c0cbc0-703f-4aba-b037-f6ceed619945"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "ab1e4e54-25e8-4bf4-bc03-9162a7d8e362"}}], "id": "1f5a36e7-a5d5-4e03-a412-732a375caa45", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/accounts/balances", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "accounts", "balances"]}}, "response": []}, {"name": "Get portfolio positions", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "52403b6c-0569-4975-b090-d5fd3de80b5a"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "65fedb4c-441f-4148-85e2-17adf15577ce"}}], "id": "b5752515-3437-4096-a2d7-f30c60222e75", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/portfolios/positions", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "portfolios", "positions"]}}, "response": []}, {"name": "Get portfolio positions", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "780c666e-b0e0-47e4-afc9-e23e633af9b0"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "2c1e1144-94d6-4967-84e2-a0ff96327e68"}}], "id": "82e1cfde-646b-4f32-a019-950a426ebc14", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/portfolios/BANK_Portfolio/positions", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "portfolios", "BANK_Portfolio", "positions"]}}, "response": []}, {"name": "Get portfolio balances", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "4eff08d7-37da-496f-81d3-59fe2b292ac5"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "e5b048a3-6696-40aa-923f-2d7ab8f1c3e8"}}], "id": "b18c9d0d-b7de-4d81-a97c-fdcbad2adf2a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/portfolios/BANK_Portfolio/balances", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "portfolios", "BANK_Portfolio", "balances"]}}, "response": []}, {"name": "Get portfolios", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "0a0c3f22-9d36-493d-88bb-84b6b88757fb"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "a55706b7-ec04-4fdf-a109-dd0b0a419433"}}], "id": "b6701e99-67ef-4806-9523-137a73474b61", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/portfolios", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "portfolios"]}}, "response": []}, {"name": "Get reservations", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "95899c1c-f3bd-4029-af43-5500c8141ca5"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "c19cfea6-2289-4207-8d17-c2569b14adf4"}}], "id": "733ce05d-0251-448a-b9d2-df4b73d96be0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/reservations?portfolioId=Retail2", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "reservations"], "query": [{"key": "portfolioId", "value": "Retail2"}]}}, "response": []}, {"name": "Get reservations by reservationRef", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "d5fe216b-d5c9-4378-bf08-5065886b871f"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "2cbc68bc-e7d5-4266-a547-19ef6830f78f"}}], "id": "352d6df9-f6b9-4e8b-bebc-c955c7911225", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/reservations/25d333e1-3e05-454d-9406-236b7b978bqe", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "reservations", "25d333e1-3e05-454d-9406-236b7b978bqe"]}}, "response": []}, {"name": "Get reservations balance by reservationRef", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "9a12d0db-b4cc-4ad6-b80c-af5c7b1549c1"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "0a96c7c1-5d5b-4d55-b517-b373cdddc0ce"}}], "id": "0c555576-8184-4ae7-9443-334a28a09435", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/reservations/25d333e1-3e05-454d-9406-236b7b978bqe/balances", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "reservations", "25d333e1-3e05-454d-9406-236b7b978bqe", "balances"]}}, "response": []}, {"name": "Get transactions", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "e07603f9-f8ae-423e-9080-8b73e3b61600"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "7702a6d3-a411-4f82-abd5-fbc8291f5be8"}}], "id": "87c98aab-2062-4671-8e19-b82553294b72", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/transactions", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "transactions"]}}, "response": []}, {"name": "Get transactions by id", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "ebe1256a-7237-473a-a18b-4f2c2ee31fc5"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "c5fa2107-613a-4f18-b6a9-60840e73772c"}}], "id": "9ce7ff0e-04ae-42d0-81f4-17015ab92b05", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/transactions/be5888ad-2cb3-4feb-b648-abc86f2800fc", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "transactions", "be5888ad-2cb3-4feb-b648-abc86f2800fc"]}}, "response": []}, {"name": "Get portfolio by ID", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "584340ff-ff5a-41df-8a0e-71a4a4ab32da"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "6c56cc70-2a98-48bb-b5e6-0c2cbc9d4809"}}], "id": "7e09e759-d666-4d82-b8b5-5671ad2cd47f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/portfolios/BANK_Portfolio", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "portfolios", "BANK_Portfolio"]}}, "response": []}, {"name": "Get permissions", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "c2ae630c-a1b0-4771-845b-8335f5a8869d"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "********-7f7b-4479-8d1d-fc8db58be7f9"}}], "id": "5c663e81-2984-4259-afa8-4c9df6eb8823", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{rest.mgmt.url}}/api/v1/permissions", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "permissions"]}}, "response": []}, {"name": "Create portfolio", "id": "ce020d21-d8f9-47df-9094-24e158c2175a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"testqw123123\",\r\n    \"portfolioType\": \"VOSTRO\",\r\n    \"portfolioCurrency\": \"EUR\",\r\n    \"tags\": []\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.mgmt.url}}/api/v1/portfolios", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "portfolios"]}}, "response": []}, {"name": "Onboarding portfolio", "id": "6e7205ea-9606-4b94-99fa-7f9d4fbc50f1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "[\r\n    {\r\n        \"portfolioName\": \"testqw123123\",\r\n        \"portfolioCurrency\": \"EUR\",\r\n        \"grants\": [\r\n            {\r\n                \"scope\": \"read\",\r\n                \"groups\": [\r\n                    \"administrators\",\r\n                    \"trader\"\r\n                ]\r\n            }\r\n        ]\r\n    }\r\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.mgmt.url}}/api/v1/portfolios/onboarding", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "portfolios", "onboarding"]}}, "response": []}, {"name": "Onboarding account", "id": "f33a903c-6443-420b-bcc0-50ce9df2b7c5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "[\r\n    {\r\n        \"name\": \"testqwe1234\",\r\n        \"grants\": [\r\n            {\r\n                \"scope\": \"read\",\r\n                \"groups\": [\r\n                    \"administrators\",\r\n                    \"trader\"\r\n                ]\r\n            },\r\n            {\r\n                \"scope\": \"trade\",\r\n                \"groups\": [\r\n                    \"trader\"\r\n                ]\r\n            }\r\n        ]\r\n    }\r\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.mgmt.url}}/api/v1/accounts/onboarding", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "accounts", "onboarding"]}}, "response": []}, {"name": "Create reservation", "id": "997920ab-2da0-4aab-bf3e-c1cb33efdd32", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"reservationRef\": \"25d333e1-3e05-454d-9406-236b7b978bab\",\r\n  \"transactionType\": \"DEPOSIT\",\r\n  \"dateTime\": **********,\r\n  \"currency\": \"USD\",\r\n  \"quantity\": 100000,\r\n  \"portfolioId\": \"qwerty\",\r\n  \"accountId\": \"BITMEX-testnet1\",\r\n  \"feeAccountId\": \"BITMEX-testnet1\",\r\n  \"feePortfolioId\": \"qwerty\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.mgmt.url}}/api/v1/reservations", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "reservations"]}}, "response": []}, {"name": "Create transaction - portfolio cash transfer", "id": "7d3bb8ed-fae1-4e16-b7aa-d398e0345445", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"reservationRef\": \"qwe12345\",\r\n  \"extTransactionId\": \"*********\",\r\n  \"transactionType\": \"PORTFOLIO_CASH_TRANSFER\",\r\n  \"dateTime\": **********,\r\n  \"currency\": \"USDT\",\r\n  \"desciption\": \"POR<PERSON><PERSON><PERSON>_CASH_TRANSFER\",\r\n  \"quantity\": 5,\r\n  \"sourcePortfolioId\": \"Retail1_simple\",\r\n  \"targetPortfolioId\": \"BANK_Portfolio\",\r\n    \"fees\": [\r\n        {\r\n            \"amount\": 5,\r\n            \"currency\": \"USD\",\r\n            \"feeType\": \"FIXED_FEE\"\r\n        }\r\n    ]\r\n}\r\n\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.mgmt.url}}/api/v1/transactions", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "transactions"]}}, "response": []}, {"name": "Create transaction - deposit", "id": "ee73bf3a-3094-417b-a58e-86cf041e332b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"reservationRef\": \"qwe12345\",\r\n  \"extTransactionId\": \"*********\",\r\n  \"transactionType\": \"WITHDRAWAL\",\r\n  \"dateTime\": **********,\r\n  \"currency\": \"USDT\",\r\n  \"quantity\": 2,\r\n  \"portfolioId\": \"qwerty\",\r\n  \"accountId\": \"simulator\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.mgmt.url}}/api/v1/transactions", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "transactions"]}}, "response": []}, {"name": "Create transaction - client cash trade", "id": "7fb5e273-0b46-4a9e-b8af-ca6dcfb61b97", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"reservationRef\": \"qwe12345\",\r\n  \"executionId\": \"*********\",\r\n  \"venueExecutionId\": \"ve_*********\",\r\n  \"baseCurrency\": \"BTC\",\r\n  \"price\": \"1234.00\",\r\n  \"settled\": \"true\",\r\n  \"transactionType\": \"CLIENT_CASH_TRADE\",\r\n  \"dateTime\": **********,\r\n  \"currency\": \"USDT\",\r\n  \"leavesQuantity\": \"0\",\r\n  \"quantity\": 2,\r\n  \"portfolioId\": \"Retail1\",\r\n  \"counterPortfolioId\": \"BANK_Portfolio\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.mgmt.url}}/api/v1/transactions", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "transactions"]}}, "response": []}, {"name": "Create transaction - withdrawal", "id": "be936961-f4ec-4ec1-bfe0-ed3735281344", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"reservationRef\": \"qwe12345\",\r\n  \"extTransactionId\": \"*********\",\r\n  \"transactionType\": \"WITHDRAWAL\",\r\n  \"dateTime\": **********,\r\n  \"currency\": \"USDT\",\r\n  \"quantity\": 2,\r\n  \"portfolioId\": \"qwerty\",\r\n  \"accountId\": \"simulator\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.mgmt.url}}/api/v1/transactions", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "transactions"]}}, "response": []}, {"name": "Create transaction - settlement", "id": "3cb50003-f7a1-48af-a07a-dd4b41c4f712", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"reservationRef\": \"qwe12345\",\r\n  \"extTransactionId\": \"*********\",\r\n  \"transactionType\": \"WITHDRAWAL\",\r\n  \"dateTime\": **********,\r\n  \"currency\": \"USDT\",\r\n  \"quantity\": 2,\r\n  \"portfolioId\": \"qwerty\",\r\n  \"accountId\": \"simulator\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.mgmt.url}}/api/v1/transactions", "host": ["{{rest.mgmt.url}}"], "path": ["api", "v1", "transactions"]}}, "response": []}], "id": "dcbda467-8ef4-4215-b6c2-3b9fcb7de75d"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": ["const cryptoJs = require('crypto-js');\r", "\r", "var apiKey = pm.environment.get('apiKey');\r", "var apiSecret = pm.environment.get('apiSecret');\r", "var timestamp = Date.now().toString();\r", "\r", "var method = pm.request.method;\r", "var queryString = pm.request.url.getQueryString();\r", "var path = pm.request.url.getPath();\r", "\r", "if (queryString != '') {\r", "    path += '?' + queryString;\r", "}\r", "\r", "var body = pm.request.body.toString();\r", "\r", "console.log('body: ' + body)\r", "\r", "var combined = timestamp + method + path + body;\r", "\r", "var hmac = cryptoJs.HmacSHA256(cryptoJs.enc.Utf8.parse(combined,apiSecret), apiSecret).toString();\r", "\r", "console.log('path: ' + path);\r", "console.log('combined: ' + combined);\r", "// console.log('hmac: ' + hmac);\r", "\r", "pm.request.headers.add(\"X-API-NONCE:\" + timestamp);\r", "pm.request.headers.add(\"X-API-KEY:\" + api<PERSON>ey);\r", "pm.request.headers.add(\"X-API-SIGNATURE:\" + hmac);"], "id": "c43f199a-0d77-4384-a232-fa8dee34607c"}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""], "id": "ccdfc3d4-4512-421e-be47-88ee4f542054"}}]}