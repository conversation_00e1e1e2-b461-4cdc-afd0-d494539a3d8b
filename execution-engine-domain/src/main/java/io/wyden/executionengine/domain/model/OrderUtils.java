package io.wyden.executionengine.domain.model;

import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsSide;

import java.math.BigDecimal;

public class OrderUtils {

    private OrderUtils() {
        // Empty
    }

    public static boolean isAwaitingOrder(OemsOrderType orderType) {
        return switch(orderType) {
            case MARKET, LIMIT -> true;
            case STOP, STOP_LIMIT -> false;
            default -> throw new UnsupportedOperationException("OrderType %s not supported".formatted(orderType));
        };
    }

    public static boolean isBuyOrder(OemsSide side) {
        return switch (side) {
            case BUY, REDUCE_SHORT -> true;
            case SELL, SELL_SHORT -> false;
            default -> throw new UnsupportedOperationException("Order side %s not supported".formatted(side));
        };
    }

    public static BigDecimal getPrice(String price, OemsOrderType orderType) {
        try {
            return new BigDecimal(price);
        } catch (Exception ex) {
            if (orderType == OemsOrderType.MARKET) {
                return BigDecimal.ZERO;
            }
            throw ex;
        }
    }
}
