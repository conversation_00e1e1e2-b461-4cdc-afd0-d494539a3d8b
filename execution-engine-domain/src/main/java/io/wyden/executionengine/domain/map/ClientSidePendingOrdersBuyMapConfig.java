package io.wyden.executionengine.domain.map;

import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.executionengine.domain.model.BookEntry;
import io.wyden.published.oems.OemsRequest;

import java.util.Set;

import static io.wyden.cloudutils.hazelcast.Serializers.protobufSerializer;

public final class ClientSidePendingOrdersBuyMapConfig extends HazelcastMapConfig {

    private static final String MAP_NAME = "execution-engine-client-order-book-buys_0.2";

    public static IMap<String, Set<BookEntry>> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
        if (findSerializerConfig(serializationConfig, OemsRequest.class).isEmpty()) {
            serializationConfig.addSerializerConfig(protobufSerializer(OemsRequest.class, OemsRequest.parser()));
        }
    }
}
