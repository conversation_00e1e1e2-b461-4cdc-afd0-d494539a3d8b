include:
  - project: infrastructure/components
    ref: main
    file: components.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

stages:
  - version
  - publish

version-verify:
  stage: version
  script:
    - echo "Validation stage"

publish-lib:
  extends: .publish-lib
  dependencies: []
  script:
    - echo "Running task 'publishMavenJavaPublicationToNexus-snapshotsRepository'"
    - ./gradlew publishMavenJavaPublicationToNexus-snapshotsRepository
