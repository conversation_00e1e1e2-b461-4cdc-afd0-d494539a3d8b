include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.9.9-eclipse-temurin-21

variables:
  IMAGE_NAME: booking-reporting
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: booking-reporting

stages:
  - version
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification

changes-verify:
  extends: .verify-locked-files

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

sonarqube-check:
  extends: .sonar-gradle

unit-test:
  extends: .unit_test

integration-test:
  extends: .integration_test

build:
  extends: .build-gradle

publish:
  extends: .publish_image

container-scan:
  extends: .docker_scan

retag:
  extends: .retag_image

deploy-dev:
  extends: .deploy_helm

tag_rc:
  extends: .tag_repository

tag_rc_scheduled:
  extends: .tag_repository_scheduled
