include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

variables:
  IMAGE_NAME: rest-api
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: rest-api

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.9.9-eclipse-temurin-21

stages:
  - version
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification

changes-verify:
  extends: .verify-locked-files

version-check-domain:
  variables:
    VERSION_FILE: "rest-api-domain/version.properties"
    SOURCE_FILES: "rest-api-domain/src/**/*"
  extends: .lib-version-check

version-check-client:
  variables:
    VERSION_FILE: "rest-api-client/version.properties"
    SOURCE_FILES: "rest-api-client/src/**/*"
  extends: .lib-version-check

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

sonarqube-check:
  extends: .sonar-gradle

unit-test:
  extends: .unit_test

integration-test:
  extends: .integration_test

build:
  extends: .build-gradle

publish-lib:
  extends: .publish-lib

publish:
  variables:
    DOCKERFILE_PATH: rest-api-server/Dockerfile
    DOCKER_BUILD_CONTEXT: rest-api-server/.
  extends: .publish_image

container-scan:
  extends: .docker_scan

retag:
  extends: .retag_image

audit:
  extends: .audit_trails

deploy-dev:
  extends: .deploy_helm

tag_rc:
  extends: .tag_repository

tag_rc_scheduled:
  extends: .tag_repository_scheduled
