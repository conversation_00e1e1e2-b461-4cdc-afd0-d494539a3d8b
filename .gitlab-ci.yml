include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

variables:
  IMAGE_NAME: settlement-server
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: settlement-server

stages:
  - version
  - test
  - build
  - publish-lib
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  variables:
    VERSION_FILE: "settlement-server/build.gradle"
  extends: .pipeline-verification

changes-verify:
  extends: .verify-locked-files

version-check-domain:
  variables:
    VERSION_FILE: "settlement-domain/version.properties"
    SOURCE_FILES: "settlement-domain/src/**/*"
  extends: .lib-version-check

version-check-client:
  variables:
    VERSION_FILE: "settlement-client/version.properties"
    SOURCE_FILES: "settlement-client/src/**/*"
  extends: .lib-version-check

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

#sonarqube-check:
#  extends: .sonar-gradle

unit-test:
  extends: .unit_test

integration-test:
  extends: .integration_test

build:
  extends: .build-gradle

publish:
  variables:
    DOCKERFILE_PATH: settlement-server/Dockerfile
    DOCKER_BUILD_CONTEXT: settlement-server/.
  extends: .publish_image

container-scan:
  extends: .docker_scan

publish-lib:
  extends: .publish-lib

retag:
  extends: .retag_image

audit:
  extends: .audit_trails

deploy_dev:
  extends: .deploy_helm

tag_rc:
  variables:
    VERSION_FILE: "settlement-server/build.gradle"
  extends: .tag_repository

tag_rc_scheduled:
  variables:
    VERSION_FILE: "settlement-server/build.gradle"
  extends: .tag_repository_scheduled
