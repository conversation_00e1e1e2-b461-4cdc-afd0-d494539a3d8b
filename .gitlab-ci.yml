image: node:latest

stages:
  - validate
  - deploy

validate:
  stage: validate
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: always
      allow_failure: false
  script:
    - npm ci
    - npm run validate-schema
    - npm run check-version-update

compatibility-check:
  stage: validate
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: always
      allow_failure: true
  before_script:
    - apk update && apk add git
    - git --version
    - npm i --global @graphql-inspector/cli graphql
  script:
    # generate current schema from MR
    - mkdir -p new
    - cat *.graphql > new/schema.graphql

    # fetch schema from main branch
    - git fetch origin

    # save main schema
    - git checkout origin/main -- *.graphql
    - mkdir -p old
    - cat *.graphql > old/schema.graphql

    # restore MR changes (since we checked out main files)
    - git checkout $CI_COMMIT_SHA -- *.graphql

    # compare schemas
    - graphql-inspector diff "old/schema.graphql" "new/schema.graphql" --rule suppressRemovalOfDeprecatedField

deploy:
  stage: deploy
  only:
    - main
  script:
    - npm run check-schema-touched
    - echo "//repo.wyden.io/nexus/repository/npm-snapshots/:_authToken=************************************" > .npmrc
    - npm publish
