package io.wyden.rate.it;

import io.wyden.published.rate.RateEvent;
import io.wyden.published.rate.RateSubscription;
import io.wyden.published.rate.RateSubscriptionCreateRequest;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;

import java.time.Duration;

import static io.wyden.published.rate.RateSubscriptionHealthStatus.DOWN;
import static io.wyden.published.rate.RateSubscriptionHealthStatus.PENDING;
import static io.wyden.published.rate.RateSubscriptionHealthStatus.UP;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class RateSubscriptionIntegrationTest extends RateServiceIntegrationTestBase {

    @Test
    public void whenCreateSubscription_thenRateInCache() throws Exception {
        conversionSourceRepository.put(getConversionSource());
        when(rateSubscriptionEnrichmentService.enrichSubscriptionWithVenueParameters(eq(BASE_CURRENCY), eq(QUOTE_CURRENCY), eq(VENUE_ACCOUNT), any())).thenReturn(getRateSubscription());

        //Request creation of rate subscription
        sendCreateSubscription();

        //Await rate request, emit rate event in response
        connectorWrapper.awaitRateRequest();
        RateEvent rateEvent = getRateEvent();
        connectorWrapper.emit(rateEvent);

        //Assert that rate appeared in cache
        await()
            .atMost(TIMEOUT)
            .untilAsserted(() -> {
                assertThat(ratesCacheFacade.find(BASE_CURRENCY, QUOTE_CURRENCY)).satisfies(rate -> {
                    assertThat(rate).isPresent();
                    assertThat(rate.get().getBaseCurrency()).isEqualTo(BASE_CURRENCY);
                    assertThat(rate.get().getQuoteCurrency()).isEqualTo(QUOTE_CURRENCY);
                    assertThat(rate.get().getValue()).isEqualTo(rateEvent.getValue());
                });
                assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(UP);
            });
    }

    @Test
    public void whenCreateSubscriptionWhenNoMatchingInstrument_thenRateNotInCache() throws Exception {
        conversionSourceRepository.put(getConversionSource());
        when(rateSubscriptionEnrichmentService.enrichSubscriptionWithVenueParameters(eq(BASE_CURRENCY), eq(QUOTE_CURRENCY), eq(VENUE_ACCOUNT), any())).thenReturn(null);

        //Request creation of rate subscription
        sendCreateSubscription();

        //Assert that rate does not appear in cache
        await()
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(2))
            .untilAsserted(() -> {
                assertThat(ratesCacheFacade.find(BASE_CURRENCY, QUOTE_CURRENCY)).isEmpty();
                assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(DOWN);
            });
    }

    @Test
    public void whenCreateSubscriptionWhenNoConversionSource_thenRateNotInCache() throws Exception {
        when(rateSubscriptionEnrichmentService.enrichSubscriptionWithVenueParameters(eq(BASE_CURRENCY), eq(QUOTE_CURRENCY), eq(VENUE_ACCOUNT), any())).thenReturn(getRateSubscription());

        //Request creation of rate subscription
        sendCreateSubscription();

        //Assert that rate does not appear in cache
        await()
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(2))
            .untilAsserted(() -> {
                assertThat(ratesCacheFacade.find(BASE_CURRENCY, QUOTE_CURRENCY)).isEmpty();
                assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(DOWN);
            });
    }

    @Test
    public void whenCreateSubscription_andSecondConversionSourceIsValid_thenRateInCache() throws Exception {
        conversionSourceRepository.put(getConversionSource());
        conversionSourceRepository.put(getInvalidConversionSource());
        when(rateSubscriptionEnrichmentService.enrichSubscriptionWithVenueParameters(eq(BASE_CURRENCY), eq(QUOTE_CURRENCY), eq(VENUE_ACCOUNT), any())).thenReturn(getRateSubscription());

        //Request creation of rate subscription
        sendCreateSubscription();

        //Await rate request, emit rate event in response
        connectorWrapper.awaitRateRequest();
        RateEvent rateEvent = getRateEvent();
        connectorWrapper.emit(rateEvent);

        //Assert that rate appeared in cache
        await()
            .atMost(TIMEOUT)
            .untilAsserted(() -> {
                assertThat(ratesCacheFacade.find(BASE_CURRENCY, QUOTE_CURRENCY)).satisfies(rate -> {
                    assertThat(rate).isPresent();
                    assertThat(rate.get().getBaseCurrency()).isEqualTo(BASE_CURRENCY);
                    assertThat(rate.get().getQuoteCurrency()).isEqualTo(QUOTE_CURRENCY);
                    assertThat(rate.get().getValue()).isEqualTo(rateEvent.getValue());
                });
                assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(UP);
            });
    }

    @Test
    public void whenCreateSubscription_thenEmitRateRequestFromHeartbeat() throws Exception {
        conversionSourceRepository.put(getConversionSource());
        when(rateSubscriptionEnrichmentService.enrichSubscriptionWithVenueParameters(eq(BASE_CURRENCY), eq(QUOTE_CURRENCY), eq(VENUE_ACCOUNT), any())).thenReturn(getRateSubscription());

        //Request creation of rate subscription
        sendCreateSubscription();

        //Await rate request, emit rate event in response
        connectorWrapper.awaitRateRequest();
        RateEvent rateEvent = getRateEvent();
        connectorWrapper.emit(rateEvent);

        //Await rate request emitted by heartbeat
        connectorWrapper.awaitRateRequest();

        //Assert that rate appeared in cache
        await()
            .atMost(TIMEOUT)
            .untilAsserted(() -> {
                assertThat(ratesCacheFacade.find(BASE_CURRENCY, QUOTE_CURRENCY)).satisfies(rate -> {
                    assertThat(rate).isPresent();
                    assertThat(rate.get().getBaseCurrency()).isEqualTo(BASE_CURRENCY);
                    assertThat(rate.get().getQuoteCurrency()).isEqualTo(QUOTE_CURRENCY);
                    assertThat(rate.get().getValue()).isEqualTo(rateEvent.getValue());
                });
                assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(UP);
            });
    }

    @Test
    public void whenCreateSubscription_andSubscriptionTimesOut_thenSubscriptionHealthIsDown() throws Exception {
        conversionSourceRepository.put(getConversionSource());
        when(rateSubscriptionEnrichmentService.enrichSubscriptionWithVenueParameters(eq(BASE_CURRENCY), eq(QUOTE_CURRENCY), eq(VENUE_ACCOUNT), any())).thenReturn(getRateSubscription());

        //Request creation of rate subscription
        sendCreateSubscription();

        //Await rate request, emit rate event in response
        connectorWrapper.awaitRateRequest();
        RateEvent rateEvent = getRateEvent();
        connectorWrapper.emit(rateEvent);

        //Assert that rate appeared in cache
        await()
            .atMost(TIMEOUT)
            .untilAsserted(() -> {
                assertThat(ratesCacheFacade.find(BASE_CURRENCY, QUOTE_CURRENCY)).satisfies(rate -> {
                    assertThat(rate).isPresent();
                    assertThat(rate.get().getBaseCurrency()).isEqualTo(BASE_CURRENCY);
                    assertThat(rate.get().getQuoteCurrency()).isEqualTo(QUOTE_CURRENCY);
                    assertThat(rate.get().getValue()).isEqualTo(rateEvent.getValue());
                });
                assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(UP);
            });

        //Assert that after inactive timeout subscription health is DOWN
        await()
            .atMost(SUBSCRIPTION_INACTIVE_TIMEOUT)
            .untilAsserted(() -> assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(DOWN));
    }

    @Test
    public void whenCreateSubscription_andSubscriptionTimesOutThenRateDiscoveryRepeats_thenSubscriptionHealthIsUp() throws Exception {
        conversionSourceRepository.put(getConversionSource());
        when(rateSubscriptionEnrichmentService.enrichSubscriptionWithVenueParameters(eq(BASE_CURRENCY), eq(QUOTE_CURRENCY), eq(VENUE_ACCOUNT), any())).thenReturn(getRateSubscription());

        //Request creation of rate subscription
        sendCreateSubscription();

        //Await rate request, emit rate event in response
        connectorWrapper.awaitRateRequest();
        RateEvent rateEvent = getRateEvent();
        connectorWrapper.emit(rateEvent);

        //Assert that rate appeared in cache
        await()
            .atMost(TIMEOUT)
            .untilAsserted(() -> {
                assertThat(ratesCacheFacade.find(BASE_CURRENCY, QUOTE_CURRENCY)).satisfies(rate -> {
                    assertThat(rate).isPresent();
                    assertThat(rate.get().getBaseCurrency()).isEqualTo(BASE_CURRENCY);
                    assertThat(rate.get().getQuoteCurrency()).isEqualTo(QUOTE_CURRENCY);
                    assertThat(rate.get().getValue()).isEqualTo(rateEvent.getValue());
                });
                assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(UP);
            });

        //Assert that after inactive timeout subscription health is DOWN
        await()
            .atMost(SUBSCRIPTION_INACTIVE_TIMEOUT)
            .untilAsserted(() -> assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(DOWN));

        //Repeat rate discovery is triggered, await rate request, emit rate event in response
        connectorWrapper.clearMessages();
        connectorWrapper.awaitRateRequest();
        RateEvent rateEvent2 = getRateEvent();
        connectorWrapper.emit(rateEvent2);

        //Assert that rate appeared in cache
        await()
            .atMost(TIMEOUT)
            .untilAsserted(() -> {
                assertThat(ratesCacheFacade.find(BASE_CURRENCY, QUOTE_CURRENCY)).satisfies(rate -> {
                    assertThat(rate).isPresent();
                    assertThat(rate.get().getBaseCurrency()).isEqualTo(BASE_CURRENCY);
                    assertThat(rate.get().getQuoteCurrency()).isEqualTo(QUOTE_CURRENCY);
                    assertThat(rate.get().getValue()).isEqualTo(rateEvent2.getValue());
                });
                assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(UP);
            });
    }

    @Test
    public void whenCreateSubscription_andRateIsNotDiscovered_andRateDiscoveryRepeatsForSecondConversionSource_thenRateInCache() throws Exception {
        conversionSourceRepository.put(getConversionSource());
        conversionSourceRepository.put(getSecondConversionSource());
        when(rateSubscriptionEnrichmentService.enrichSubscriptionWithVenueParameters(eq(BASE_CURRENCY), eq(QUOTE_CURRENCY), eq(VENUE_ACCOUNT), any(RateSubscription.class))).thenAnswer(invocation -> {
            RateSubscription rateSubscription = getRateSubscription();
            RateSubscription argument = invocation.getArgument(3);
            return rateSubscription.toBuilder().setHealth(argument.getHealth()).build();
        });
        when(rateSubscriptionEnrichmentService.enrichSubscriptionWithVenueParameters(eq(BASE_CURRENCY), eq(QUOTE_CURRENCY), eq(VENUE_ACCOUNT_2), any(RateSubscription.class))).thenAnswer(invocation -> {
            RateSubscription rateSubscription = getRateSubscriptionWithVA2();
            RateSubscription argument = invocation.getArgument(3);
            return rateSubscription.toBuilder().setHealth(argument.getHealth()).build();
        });

        //Request creation of rate subscription
        sendCreateSubscription();

        //Await rate request, emit rate event in response
        connectorWrapper.awaitRateRequest();

        //Assert that rate subscription is PENDING
        await()
            .atMost(TIMEOUT_RATE_NOT_DISCOVERED)
            .untilAsserted(() -> {
                assertThat(ratesCacheFacade.find(BASE_CURRENCY, QUOTE_CURRENCY)).isEmpty();
                assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(PENDING);
            });

        //Await rate request triggered by second rate discovery
        secondConnectorWrapper.awaitRateRequest();
        RateEvent rateEvent2 = getRateEvent();
        secondConnectorWrapper.emit(rateEvent2);

        //Assert that rate appeared in cache
        await()
            .atMost(TIMEOUT)
            .untilAsserted(() -> {
                assertThat(ratesCacheFacade.find(BASE_CURRENCY, QUOTE_CURRENCY)).satisfies(rate -> {
                    assertThat(rate).isPresent();
                    assertThat(rate.get().getBaseCurrency()).isEqualTo(BASE_CURRENCY);
                    assertThat(rate.get().getQuoteCurrency()).isEqualTo(QUOTE_CURRENCY);
                    assertThat(rate.get().getValue()).isEqualTo(rateEvent2.getValue());
                });
                assertThat(rateSubscriptionRepository.find(BASE_CURRENCY, QUOTE_CURRENCY).getHealth()).isEqualTo(UP);
            });
    }

    private void sendCreateSubscription() throws Exception {
        mockMvc.perform(post("/subscriptions")
                .contentType(MediaType.APPLICATION_PROTOBUF)
                .content(createRequest().toByteArray()))
            .andExpect(status().isCreated());
    }

    private RateSubscriptionCreateRequest createRequest() {
        return RateSubscriptionCreateRequest.newBuilder()
            .setBaseCurrency(BASE_CURRENCY)
            .setQuoteCurrency(QUOTE_CURRENCY)
            .build();
    }
}
