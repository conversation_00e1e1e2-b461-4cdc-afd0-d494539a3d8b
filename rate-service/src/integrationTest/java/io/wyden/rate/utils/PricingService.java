package io.wyden.rate.utils;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.published.marketdata.MarketDataEvent;

import java.util.Map;

public class PricingService {
    private static final Map<String, String> HEADERS = Map.of();

    private final RabbitExchange<MarketDataEvent> marketDataExchange;

    public PricingService(RabbitExchange<MarketDataEvent> marketDataExchange) {
        this.marketDataExchange = marketDataExchange;
    }

    public void emitMarketDataEvent(MarketDataEvent marketDataEvent) {
        marketDataExchange.publishWithHeaders(marketDataEvent, HEADERS);
    }
}
