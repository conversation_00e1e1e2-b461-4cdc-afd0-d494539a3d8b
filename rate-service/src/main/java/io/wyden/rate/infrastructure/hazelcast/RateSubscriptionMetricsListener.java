package io.wyden.rate.infrastructure.hazelcast;

import com.hazelcast.core.EntryEvent;
import com.hazelcast.map.IMap;
import com.hazelcast.map.MapEvent;
import com.hazelcast.map.listener.EntryAddedListener;
import com.hazelcast.map.listener.EntryLoadedListener;
import com.hazelcast.map.listener.EntryRemovedListener;
import com.hazelcast.map.listener.EntryUpdatedListener;
import com.hazelcast.map.listener.MapClearedListener;
import com.hazelcast.map.listener.MapEvictedListener;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.rate.RateSubscription;
import io.wyden.published.rate.RateSubscriptionHealthStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;

import static io.wyden.rate.infrastructure.telemetry.Meters.RATE_SUBSCRIPTION_DOWN_GAUGE;
import static io.wyden.rate.infrastructure.telemetry.Meters.RATE_SUBSCRIPTION_PENDING_GAUGE;
import static io.wyden.rate.infrastructure.telemetry.Meters.RATE_SUBSCRIPTION_UP_GAUGE;

@Component
public class RateSubscriptionMetricsListener implements
    EntryAddedListener<String, RateSubscription>,
    EntryUpdatedListener<String, RateSubscription>,
    EntryRemovedListener<String, RateSubscription>,
    EntryLoadedListener<String, RateSubscription>,
    MapClearedListener,
    MapEvictedListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(RateSubscriptionMetricsListener.class);

    private final AtomicInteger upCount = new AtomicInteger(0);
    private final AtomicInteger pendingCount = new AtomicInteger(0);
    private final AtomicInteger downCount = new AtomicInteger(0);

    public RateSubscriptionMetricsListener(Telemetry telemetry) {
        telemetry.getMeterRegistry().gauge(RATE_SUBSCRIPTION_UP_GAUGE, upCount);
        telemetry.getMeterRegistry().gauge(RATE_SUBSCRIPTION_PENDING_GAUGE, pendingCount);
        telemetry.getMeterRegistry().gauge(RATE_SUBSCRIPTION_DOWN_GAUGE, downCount);
    }

    public void initialize(IMap<String, RateSubscription> map) {
        for (RateSubscription subscription : map.values()) {
            increment(subscription.getHealth());
        }
    }

    @Override
    public void entryAdded(EntryEvent<String, RateSubscription> event) {
        LOGGER.trace("entryAdded: {}", event);
        increment(event.getValue().getHealth());
    }

    @Override
    public void entryUpdated(EntryEvent<String, RateSubscription> event) {
        LOGGER.trace("entryUpdated: {}", event);
        RateSubscription oldValue = event.getOldValue();
        RateSubscription newValue = event.getValue();
        if (updateDidNotChangeHealth(oldValue, newValue)) {
            return;
        }

        decrement(oldValue.getHealth());
        increment(newValue.getHealth());
    }

    @Override
    public void entryRemoved(EntryEvent<String, RateSubscription> event) {
        LOGGER.trace("entryRemoved: {}", event);
        decrement(event.getOldValue().getHealth());
    }

    @Override
    public void mapCleared(MapEvent event) {
        LOGGER.trace("mapCleared: {}", event);
        clear();
    }

    @Override
    public void mapEvicted(MapEvent event) {
        LOGGER.trace("mapEvicted: {}", event);
        clear();
    }

    @Override
    public void entryLoaded(EntryEvent<String, RateSubscription> event) {
        LOGGER.trace("entryLoaded: {}", event);
        increment(event.getValue().getHealth());
    }

    private void increment(RateSubscriptionHealthStatus health) {
        LOGGER.trace("Counter increment - up: {}, down: {}, pending: {}", upCount.get(), downCount.get(), pendingCount.get());
        switch (health) {
            case UP -> upCount.incrementAndGet();
            case DOWN -> downCount.incrementAndGet();
            case PENDING -> pendingCount.incrementAndGet();
        }
    }

    private void decrement(RateSubscriptionHealthStatus status) {
        LOGGER.trace("Counter decrement - up: {}, down: {}, pending: {}", upCount.get(), downCount.get(), pendingCount.get());
        switch (status) {
            case UP -> upCount.decrementAndGet();
            case DOWN -> downCount.decrementAndGet();
            case PENDING -> pendingCount.decrementAndGet();
        }
    }

    private void clear() {
        upCount.set(0);
        downCount.set(0);
        pendingCount.set(0);
    }

    private boolean updateDidNotChangeHealth(RateSubscription oldValue, RateSubscription newValue) {
        return oldValue.getBaseCurrency().equals(newValue.getBaseCurrency()) &&
            oldValue.getQuoteCurrency().equals(newValue.getQuoteCurrency()) &&
            oldValue.getHealth().equals(newValue.getHealth());
    }
}
