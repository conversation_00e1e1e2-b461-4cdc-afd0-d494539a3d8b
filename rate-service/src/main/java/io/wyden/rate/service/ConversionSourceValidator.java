package io.wyden.rate.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Objects;

@Service
public class ConversionSourceValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConversionSourceValidator.class);

    private final ConversionSourceRepository conversionSourceRepository;

    public ConversionSourceValidator(ConversionSourceRepository conversionSourceRepository) {
        this.conversionSourceRepository = conversionSourceRepository;
    }

    public void validateCreate(String venueAccount, Integer priority) {
        validateNotExists(venueAccount);
        if (priority != 0) {
            validatePriorityInRangeCreate(priority);
        }
    }

    public void validateUpdate(String venueAccount, Integer priority) {
        validateExists(venueAccount);
        validatePriorityInRangeUpdate(priority);
    }

    public void validateDelete(String venueAccount) {
        validateExists(venueAccount);
    }

    private void validateNotExists(String venueAccount) {
        if (Objects.nonNull(conversionSourceRepository.get(venueAccount))) {
            String log = "Conversion source for venue account %s already exists".formatted(venueAccount);
            LOGGER.info(log);
            throw new ResponseStatusException(HttpStatus.CONFLICT, log);
        }
    }

    private void validateExists(String venueAccount) {
        if (Objects.isNull(conversionSourceRepository.get(venueAccount))) {
            String log = "Conversion source for venue account %s does not exist".formatted(venueAccount);
            LOGGER.info(log);
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, log);
        }
    }

    private void validatePriorityInRangeCreate(Integer priority) {
        int highestPriority = conversionSourceRepository.getHighestOccupiedPriority();
        if (priority > highestPriority + 1) {
            String log = "Cannot set priority higher than: %d".formatted(highestPriority + 1);
            LOGGER.info(log);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, log);
        }
        if (priority < 1) {
            String log = "Priority has to be 1 or more";
            LOGGER.info(log);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, log);
        }
    }

    private void validatePriorityInRangeUpdate(Integer priority) {
        int highestPriority = conversionSourceRepository.getHighestOccupiedPriority();
        if (priority > highestPriority) {
            String log = "Cannot set priority higher than: %d".formatted(highestPriority);
            LOGGER.info(log);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, log);
        }
        if (priority < 1) {
            String log = "Priority has to be 1 or more";
            LOGGER.info(log);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, log);
        }
    }
}
