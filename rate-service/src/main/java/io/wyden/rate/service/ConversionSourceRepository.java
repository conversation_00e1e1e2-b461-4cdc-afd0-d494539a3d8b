package io.wyden.rate.service;

import io.wyden.published.rate.ConversionSource;
import io.wyden.published.rate.ConversionSourceList;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Repository
public class ConversionSourceRepository {

    private static final String CONVERSION_SOURCE_KEY = "CONVERSION_SOURCE_KEY";
    private final Map<String, ConversionSourceList> conversionSourceMap;

    public ConversionSourceRepository(Map<String, ConversionSourceList> conversionSourceMap) {
        this.conversionSourceMap = conversionSourceMap;
    }

    public List<ConversionSource> values() {
        ConversionSourceList conversionSourceList = conversionSourceMap.get(CONVERSION_SOURCE_KEY);
        if (Objects.isNull(conversionSourceList)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(conversionSourceList.getConversionSourceList());
    }

    public void put(ConversionSource conversionSource) {
        List<ConversionSource> conversionSources = values();
        conversionSources.add(conversionSource);
        replaceAll(conversionSources);
    }

    public void replaceAll(List<ConversionSource> conversionSources) {
        ConversionSourceList build = buildConversionSourceList(conversionSources);
        conversionSourceMap.put(CONVERSION_SOURCE_KEY, build);
    }

    public ConversionSource get(String key) {
        return values()
            .stream()
            .filter(conversionSource -> conversionSource.getVenueAccount().equals(key))
            .findFirst()
            .orElse(null);
    }

    public void remove(String key) {
        List<ConversionSource> values = values();
        boolean wasRemoved = values.removeIf(conversionSource -> conversionSource.getVenueAccount().equals(key));
        if (wasRemoved) {
            replaceAll(values);
        }
    }

    public Integer getHighestOccupiedPriority() {
        List<ConversionSource> values = values();
        if (CollectionUtils.isEmpty(values)) {
            return 0;
        }
        return values.size();
    }

    private static @NotNull ConversionSourceList buildConversionSourceList(List<ConversionSource> values) {
        return ConversionSourceList.newBuilder()
            .addAllConversionSource(values)
            .build();
    }
}
