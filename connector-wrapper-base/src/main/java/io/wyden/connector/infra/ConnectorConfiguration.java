package io.wyden.connector.infra;

import ch.algotrader.api.connector.application.ExternalApplicationWiring;
import ch.algotrader.api.connector.ops.InitContext;
import ch.algotrader.api.connector.ops.InitContextBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.connector.configuration.DefaultTIFsHolder;
import io.wyden.connector.vault.ApiKeySource;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.StandardEnvironment;

@Configuration
@ComponentScan(basePackages = ConnectorConfiguration.ALGOTRADER_EXTERNAL_WIRING_PACKAGE)
class ConnectorConfiguration<C extends InitContext, B extends InitContextBuilder<C, B>> {

    static final String ALGOTRADER_EXTERNAL_WIRING_PACKAGE = "ch.algotrader.external";

    @Bean
    ConnectorBuilder connectorBuilder(ExternalApplicationWiring<C, B> wiring,
                                      RabbitIntegrator rabbitIntegrator,
                                      ObjectMapper objectMapper,
                                      StandardEnvironment environment,
                                      ApiKeySource apiKeySource,
                                      DefaultTIFsHolder defaultTIFsHolder) {
        ConnectorConfigurer<C, B> connectorConfigurer = new ConnectorConfigurer<>(wiring, rabbitIntegrator, environment, apiKeySource, defaultTIFsHolder, objectMapper);
        return new ConnectorBuilder(connectorConfigurer);
    }

    @Bean
    @ConditionalOnMissingBean
    ExternalApplicationWiring<C, B> externalApplicationWiring() {
        return () -> {
            throw new NotImplementedException("Connector not found on classpath!");
        };
    }
}
