package io.wyden.connector.utils;

import ch.algotrader.api.connector.trading.domain.Status;
import ch.algotrader.api.domain.Side;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.venue.VenueExecType;
import io.wyden.published.venue.VenueResponse;
import io.wyden.published.venue.VenueSide;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import static io.wyden.connector.utils.ProtoTranslator.toSide;
import static io.wyden.connector.utils.ProtoTranslator.toStatus;

public final class ProtobufUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProtobufUtils.class);

    private ProtobufUtils() {
        // Empty
    }

    public static void copyField(String value, Consumer<String> consumer) {
        if (StringUtils.isNotEmpty(value)) {
            consumer.accept(value);
        }
    }

    public static void copyField(VenueExecType execType, Consumer<Status> consumer) {
        Status result = toStatus(execType);
        if (result != null) {
            consumer.accept(result);
        }
    }

    public static void copyField(VenueSide side, Consumer<Side> consumer) {
        Side result = toSide(side);
        if (result != null) {
            consumer.accept(result);
        }
    }

    public static void copyField(String feeAmount, String feeCurrency, Consumer<Map<String, BigDecimal>> consumer) {
        BigDecimal feeAmountDec = toBigDecimalOrNull(feeAmount);
        if (StringUtils.isNotBlank(feeCurrency) && feeAmountDec != null) {
            consumer.accept(Map.of(feeCurrency, feeAmountDec));
        }
    }

    public static void copyDateTimeField(String value, Consumer<ZonedDateTime> consumer) {
        ZonedDateTime zdt = DateUtils.isoUtcTimeToZonedDateTime(value);
        if (zdt != null) {
            consumer.accept(zdt);
        }
    }

    public static void copyDecimalField(String value, Consumer<BigDecimal> consumer) {
        if (StringUtils.isEmpty(value)) return;
        BigDecimal result = toBigDecimalOrNull(value);
        if (result != null) {
            consumer.accept(result);
        }
    }

    public static BigDecimal toBigDecimalOrNull(String value) {
        try {
            return new BigDecimal(value);
        } catch (Exception ex) {
            LOGGER.error("Cannot convert to BigDecimal: {}", value);
            return null;
        }
    }

    public static class ProtobufSerializer<T extends Message> extends JsonSerializer<T> {

        @Override
        public void serialize(T value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeBinary(value.toByteArray());
        }
    }

    public static class ProtobufDeserializer<T extends Message> extends JsonDeserializer<T> {

        private final Parser<T> parser;

        public ProtobufDeserializer(Parser<T> parser) {
            this.parser = parser;
        }

        @Override
        public T deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
            return parser.parseFrom(p.getBinaryValue());
        }
    }

    public static class VenueResponseListSerializer extends JsonSerializer<List<VenueResponse>> {
        @Override
        public void serialize(List<VenueResponse> value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeStartArray();
            for (VenueResponse v : value) {
                gen.writeBinary(v.toByteArray());
            }
            gen.writeEndArray();
        }
    }

    public static class VenueResponseListDeserializer extends JsonDeserializer<List<VenueResponse>> {

        private static final Parser<VenueResponse> parser = VenueResponse.parser();

        @Override
        public List<VenueResponse> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
            List<VenueResponse> result = new ArrayList<>();
            ObjectCodec oc = p.getCodec();
            JsonNode node = oc.readTree(p);
            Iterator<JsonNode> elements = node.elements();
            while (elements.hasNext()) {
                result.add(parser.parseFrom(elements.next().binaryValue()));
            }
            return result;
        }
    }
}
