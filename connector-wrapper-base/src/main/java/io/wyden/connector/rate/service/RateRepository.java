package io.wyden.connector.rate.service;

import io.wyden.published.rate.Rate;
import io.wyden.published.rate.RateKey;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

public interface RateRepository {

    Map<RateKey, Rate> rates();

    Optional<Rate> find(String baseCurrency, String quoteCurrency);

    void save(String baseCurrency, String quoteCurrency, BigDecimal price);
}
