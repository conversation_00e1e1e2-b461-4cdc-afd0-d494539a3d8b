package io.wyden.connector.marketdata.service;

import ch.algotrader.api.connector.RequestResult;
import ch.algotrader.api.connector.application.Connector;
import ch.algotrader.api.connector.marketdata.MarketDataController;
import ch.algotrader.api.connector.marketdata.SubscriptionResult;
import ch.algotrader.api.connector.marketdata.domain.AbstractOrderBookDTO;
import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;
import ch.algotrader.api.connector.metadata.Capability;
import ch.algotrader.api.connector.ops.diagnostics.DiagnosticEvent;
import ch.algotrader.api.connector.ops.diagnostics.HealthStatus;
import ch.algotrader.api.domain.exchange.ExchangeDTO;
import ch.algotrader.api.domain.security.ForexDTO;

import com.google.common.collect.Sets;
import com.google.protobuf.Message;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.connector.marketdata.model.MarketDataEventType;
import io.wyden.connector.marketdata.model.MarketDataSubscription;
import io.wyden.connector.marketdata.model.MarketDataSubscriptionKey;
import io.wyden.connector.referencedata.service.ReferenceDataService;
import io.wyden.connector.utils.FailureNonRecoverableException;
import io.wyden.published.marketdata.MarketDataRequest;
import org.apache.commons.collections4.map.UnmodifiableMap;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static io.wyden.connector.utils.RequestResultUtils.block;
import static io.wyden.connector.utils.RequestResultUtils.handleRequestResult;

public class MarketDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataService.class);
    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(5);

    private final ReferenceDataService referenceDataService;
    private final Connector connector;
    private final ScheduledExecutorService executorService;
    private final MeterRegistry meterRegistry;
    private final int heartbeatInterval;
    private final int mdReconnectInterval;
    private final Map<String, String> tickerInstrumentIdMapping = new ConcurrentHashMap<>();
    private final Map<MarketDataSubscriptionKey, MarketDataSubscription> marketDataSubscriptionsMap = new ConcurrentHashMap<>();

    private final AtomicBoolean isAlive = new AtomicBoolean(false);

    public MarketDataService(ReferenceDataService referenceDataService,
                             Connector connector,
                             Telemetry telemetry,
                             int heartbeatInterval,
                             int mdReconnectInterval) {
        Assert.notNull(referenceDataService, "Reference data service cannot be null");
        Assert.notNull(connector, "Connector cannot be null");
        this.referenceDataService = referenceDataService;
        this.connector = connector;
        this.executorService = Executors.newSingleThreadScheduledExecutor();
        this.meterRegistry = telemetry.getMeterRegistry();
        this.heartbeatInterval = heartbeatInterval;
        this.mdReconnectInterval = mdReconnectInterval;
        meterRegistry.gaugeMapSize("wyden.market-data.subscriptions", List.of(), marketDataSubscriptionsMap);
        startHeartBeat();
        monitorSessionHealth(connector);
    }

    private void monitorSessionHealth(Connector connector) {
        connector.getDiagnosticController().getEventFlux()
            .filter(e -> e.getCapabilitiesAffected().contains(Capability.MARKET_DATA))
            .subscribe(health -> {
                boolean movedToAlive = updateHealth(health);
                if (movedToAlive) {
                    resubscribe();
                }
            });
    }

    /**
     * @param health health event reported by connector, triggers on every market data session state change
     * @return true, if health has changed to alive for the first time
     */
    private boolean updateHealth(DiagnosticEvent health) {
        boolean currentAlive = health.getCurrentEffectiveHealth().getStatus().equals(HealthStatus.ALIVE);
        boolean previousAlive = isAlive.getAndSet(currentAlive);
        LOGGER.info("Market data state transitioned from alive={} to alive={}", previousAlive, currentAlive);
        return currentAlive && !previousAlive;
    }

    public void startHeartBeat() {
        LOGGER.info("Starting MDM heart beating with interval: {} seconds", heartbeatInterval);
        try {
            executorService.scheduleWithFixedDelay(createHeartBeatTask(), 1, heartbeatInterval, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.error("Error when trying to start heart beating: {}", e.toString());
        }
    }

    @NotNull
    private Runnable createHeartBeatTask() {
        return () -> marketDataSubscriptionsMap.forEach((marketDataSubscriptionKey, marketDataSubscription) -> {
            if (!marketDataSubscription.marketDataRequests().isEmpty()) {
                handleHeartBeat(marketDataSubscriptionKey, marketDataSubscription);
            } else {
                unsubscribe(marketDataSubscriptionKey);
            }
        });
    }

    private void handleHeartBeat(MarketDataSubscriptionKey marketDataSubscriptionKey, MarketDataSubscription marketDataSubscription) {
        try {
            LOGGER.trace("Handling heart beat for marketDataSubscriptionKey: {}", marketDataSubscriptionKey);
            marketDataSubscription.marketDataRequests().clear();
            marketDataSubscriptionsMap.put(marketDataSubscriptionKey, marketDataSubscription);
        } catch (Exception e) {
            LOGGER.error("Error in heartbeat task: {}", e.toString());
        }
    }

    public void handleMarketDataRequest(MarketDataRequest request) {
        LOGGER.info("Received MarketDataRequest({})", request);
        storeTickerInstrumentId(request);
        storeRequest(request);
    }

    private void storeRequest(MarketDataRequest request) {
        MarketDataSubscriptionKey marketDataSubscriptionKey = new MarketDataSubscriptionKey(request.getInstrumentKey(), request.getTicker(), request.getMarketDepth());
        updateMetrics("wyden.market-data.subscribe-incoming", subscriptionTags(marketDataSubscriptionKey));
        MarketDataSubscription marketDataSubscription = marketDataSubscriptionsMap.computeIfAbsent(marketDataSubscriptionKey, key -> createNewSubscription(request, marketDataSubscriptionKey));
        marketDataSubscription.marketDataRequests().add(request);
        marketDataSubscriptionsMap.put(marketDataSubscriptionKey, marketDataSubscription);
        LOGGER.trace("MarketDataSubscriptionsMap entrySet: {}", marketDataSubscriptionsMap.entrySet());
    }

    private MarketDataSubscription createNewSubscription(MarketDataRequest request, MarketDataSubscriptionKey marketDataSubscriptionKey) {
        LOGGER.debug("Sending subscribe{} request({})", resolveSubscriptionLevel(request.getMarketDepth()), request);
        updateMetrics("wyden.market-data.subscribe-outgoing", subscriptionTags(marketDataSubscriptionKey));
        sendMarketDataRequest(request, resolveSubscribeControllerHandler(request.getMarketDepth()));
        return new MarketDataSubscription(UUID.randomUUID().toString(), Sets.newHashSet());
    }

    public void unsubscribe(MarketDataSubscriptionKey marketDataSubscriptionKey) {
        LOGGER.info("Unsubscribe for: {}", marketDataSubscriptionKey);
        MarketDataRequest request = buildRequest(marketDataSubscriptionKey);
        LOGGER.debug("Sending unsubscribe{} request({})", resolveSubscriptionLevel(request.getMarketDepth()), request);
        updateMetrics("wyden.market-data.unsubscribe-outgoing", subscriptionTags(marketDataSubscriptionKey));
        sendMarketDataRequest(request, resolveUnsubscribeControllerHandler(request.getMarketDepth()));
        marketDataSubscriptionsMap.remove(marketDataSubscriptionKey);
    }

    private MarketDataRequest buildRequest(MarketDataSubscriptionKey subscriptionKey) {
        return MarketDataRequest.newBuilder()
            .setMarketDepth(subscriptionKey.depth())
            .setInstrumentKey(subscriptionKey.instrumentKey())
            .setTicker(subscriptionKey.ticker())
            .build();
    }

    public Flux<Message> getMarketDataFlux(String venueAccount) {
        if (connector.getMarketDataController() == null) {
            return Flux.empty();
        }

        return connector.getMarketDataController().getEventFlux()
            .mapNotNull(event -> {
                String instrumentId = getInstrumentIdForTicker(event.getTickerId());

                if (instrumentId == null) {
                    LOGGER.error("Skipping event - received MD event but no ticker-instrumentId mapping is present! " +
                        "Subscription is missing. VenueTicker: %s, VenueAccount: %s".formatted(event.getTickerId(), venueAccount));
                    return null;
                }

                updateMetrics(instrumentId, toMarketDataEventType(event));

                try {
                    return MarketDataMessageFactory.createMessage(event, venueAccount, instrumentId);
                } catch (RuntimeException e) {
                    LOGGER.warn("Skipping event {} because of error: {}", event, e.getMessage(), e);
                    return null;
                }
            });
    }

    private void storeTickerInstrumentId(MarketDataRequest request) {
        String ticker = request.getTicker();
        String instrumentId = request.getInstrumentKey().getInstrumentId();
        LOGGER.trace("ADDING ticker <-> instrumentId mapping: {} <-> {}", ticker, instrumentId);
        LOGGER.trace("TickerInstrumentIdMapping map size before: {}", tickerInstrumentIdMapping.size());
        tickerInstrumentIdMapping.putIfAbsent(ticker, instrumentId);
        LOGGER.trace("TickerInstrumentIdMapping map size after: {}", tickerInstrumentIdMapping.size());
    }

    public String getInstrumentIdForTicker(String ticker) {
        String instrumentId = tickerInstrumentIdMapping.get(ticker);
        LOGGER.trace("RETURNING instrumentId: {} for ticker: {} from ticker <-> instrumentId map", instrumentId, ticker);
        return instrumentId;
    }

    public void resubscribe() {
        try {
            LOGGER.info("Resubscribing existing market data subscriptions ({})", marketDataSubscriptionsMap.size());
            marketDataSubscriptionsMap.forEach((marketDataSubscriptionKey, marketDataSubscription) -> {
                updateMetrics("wyden.market-data.resubscribe-outgoing", subscriptionTags(marketDataSubscriptionKey));
                MarketDataRequest request = buildRequest(marketDataSubscriptionKey);
                LOGGER.debug("Sending resubscribe{} request({})", resolveSubscriptionLevel(request.getMarketDepth()), request);
                sendMarketDataRequest(request, resolveSubscribeControllerHandler(request.getMarketDepth()));
            });
        } catch (Exception e) {
            LOGGER.error("Error when trying to resubscribe market data: {}", e.getMessage(), e);
            scheduleMarketDataResubscribeRetry();
            updateMetrics("wyden.market-data.resubscribe-retry-after-exception", Tags.of("exceptionClass", e.getClass().getSimpleName()));
            LOGGER.info("Market data resubscribe retry scheduled successfully");
        }
    }

    public Map<MarketDataSubscriptionKey, MarketDataSubscription> getMarketDataSubscriptionsMap() {
        return UnmodifiableMap.unmodifiableMap(marketDataSubscriptionsMap);
    }

    void scheduleMarketDataResubscribeRetry() {
        LOGGER.info("Scheduling market data resubscribe retry with interval: {} seconds", mdReconnectInterval);
        try {
            executorService.schedule(this::resubscribe, mdReconnectInterval, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.error("Error when trying to schedule market data resubscribe retry: {}", e.toString());
        }
    }

    private void sendMarketDataRequest(MarketDataRequest request, MarketDataControllerHandler handler) {
        if (!isAlive.get()) {
            throw new FailureNonRecoverableException("Market data session is not active yet - request cannot be executed: %s".formatted(request));
        }

        ExchangeDTO exchangeDTO = referenceDataService.getExchange();
        ForexDTO forexDTO = referenceDataService.findInstrument(exchangeDTO.getName(), request.getTicker());
        MarketDataController controller = connector.getMarketDataController();
        Mono<SubscriptionResult> resultMono = handler.apply(controller, MarketDataRequestFactory.build(forexDTO, exchangeDTO));
        LOGGER.debug("Sending subscription request to connector for ticker: {}", request.getTicker());
        RequestResult requestResult = block(resultMono, DEFAULT_TIMEOUT);
        handleRequestResult(requestResult);
    }

    @NotNull
    private String resolveSubscriptionLevel(int marketDepth) {
        return (marketDepth == 1) ? "L1" : "L2";
    }

    @NotNull
    private MarketDataControllerHandler resolveSubscribeControllerHandler(int marketDepth) {
        if (marketDepth == 1) {
            return MarketDataController::subscribeL1;
        } else {
            return MarketDataController::subscribeL2;
        }
    }

    @NotNull
    private MarketDataControllerHandler resolveUnsubscribeControllerHandler(int marketDepth) {
        if (marketDepth == 1) {
            return MarketDataController::unsubscribeL1;
        } else {
            return MarketDataController::unsubscribeL2;
        }
    }

    private MarketDataEventType toMarketDataEventType(MarketDataEventDTO marketDataEventDTO) {
        if (marketDataEventDTO instanceof AbstractOrderBookDTO) {
            return MarketDataEventType.L2;
        } else {
            return MarketDataEventType.L1;
        }
    }

    private void updateMetrics(String instrumentId, MarketDataEventType depth) {
        try {
            meterRegistry.counter("wyden.market-data.events-incoming", Tags.of("instrumentId", instrumentId, "depth", depth.name()))
                .increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    private void updateMetrics(String name, Tags tags) {
        try {
            this.meterRegistry.counter(name, tags).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    private Tags subscriptionTags(MarketDataSubscriptionKey marketDataSubscriptionKey) {
        return Tags.of("instrumentId", marketDataSubscriptionKey.instrumentKey().getInstrumentId(), "depth", marketDataSubscriptionKey.depth() == 1 ? "L1" : "L2");
    }
}
