package io.wyden.connector.trading.service;

import ch.algotrader.api.connector.application.Connector;
import ch.algotrader.api.connector.ops.diagnostics.DiagnosticController;
import ch.algotrader.api.connector.trading.TradingController;
import ch.algotrader.api.connector.trading.domain.Status;
import ch.algotrader.api.connector.trading.domain.response.OrderExecutionReport;
import ch.algotrader.api.connector.trading.domain.response.OrderIdentifier;
import ch.algotrader.api.connector.trading.domain.response.OrderStatusEvent;

import io.wyden.cloud.utils.test.TelemetryMock;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.connector.infra.ConnectorBuilder;
import io.wyden.connector.referencedata.service.ReferenceDataService;
import io.wyden.connector.trading.io.VenueResponseEmitter;
import io.wyden.published.venue.VenueEnvironmentType;
import io.wyden.published.venue.VenueExecType;
import io.wyden.published.venue.VenueResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Sinks;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class TradingServiceReportTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradingServiceReportTest.class);

    static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(1);

    static final String VENUE_ACCOUNT = "VenueAccount";

    static final String EXECUTION_ID = UUID.randomUUID().toString();

    @Mock
    ConnectorBuilder connectorBuilder;

    @Mock
    Connector connector;

    @Mock
    DiagnosticController diagnosticController;

    @Mock
    TradingController tradingController;

    @Mock
    ReferenceDataService referenceDataService;

    @Mock
    VenueResponseEmitter venueResponseEmitter;

    @Captor
    ArgumentCaptor<VenueResponse> venueExecutionReportCaptor;

    TradingService tradingService;

    Sinks.Many<OrderStatusEvent> orderStatusEvents = Sinks.many().unicast().onBackpressureError();
    Sinks.Many<OrderExecutionReport> orderExecReports = Sinks.many().unicast().onBackpressureError();
    Sinks.Many<OrderStatusEvent> orderFillEvents = Sinks.many().unicast().onBackpressureError();
    Sinks.Many<OrderStatusEvent> orderOpenedEvents = Sinks.many().unicast().onBackpressureError();
    Sinks.Many<OrderStatusEvent> diagnosticEvents = Sinks.many().unicast().onBackpressureError();

    @SuppressWarnings("ReactiveStreamsUnusedPublisher")
    @BeforeEach
    void beforeEach() {
        Telemetry telemetryMock = TelemetryMock.createMock();

        doReturn(orderStatusEvents.asFlux()).when(tradingController).getOrderStatusFlux();
        doReturn(orderExecReports.asFlux()).when(tradingController).getOrderExecutionReportFlux();
        doReturn(orderFillEvents.asFlux()).when(tradingController).getOrderFillFlux();
        doReturn(orderOpenedEvents.asFlux()).when(tradingController).getOpenedOrdersFlux();
        doReturn(tradingController).when(connector).getTradingController();

        doReturn(diagnosticEvents.asFlux()).when(diagnosticController).getEventFlux();
        doReturn(diagnosticController).when(connector).getDiagnosticController();

        tradingService = new TradingService(referenceDataService, venueResponseEmitter, connector, telemetryMock);
        tradingService.attachConsumer(VENUE_ACCOUNT);
    }

    @ParameterizedTest(name = "givenOrderStatusEventTradingServiceSendsExecutionReport({0},{1})")
    @CsvSource({
        "SUBMITTED,VENUE_EXEC_TYPE_NEW",
        "PARTIALLY_EXECUTED,VENUE_EXEC_TYPE_PARTIAL_FILL",
        "EXECUTED,VENUE_EXEC_TYPE_FILL",
        "CANCELED,VENUE_EXEC_TYPE_CANCELED",
        "RESTATED,VENUE_EXEC_TYPE_RESTATED",
        "EXPIRED,VENUE_EXEC_TYPE_EXPIRED",
        "REJECTED,VENUE_EXEC_TYPE_REJECTED"
    })
    void givenEmptyOrderStatusEventTradingServiceSendsExecutionReport(Status status, VenueExecType execType) {
        OrderExecutionReport orderExecutionReport = emptyOrderExecutionReportIntId(status);
        assertThat(orderExecReports.tryEmitNext(orderExecutionReport).isSuccess()).isTrue();
        verify(venueResponseEmitter, timeout(DEFAULT_TIMEOUT.toMillis()).times(1))
            .emitExecReport(venueExecutionReportCaptor.capture());
        VenueResponse executionReport = venueExecutionReportCaptor.getValue();
        assertThat(executionReport.getExecType()).isEqualTo(execType);
        assertThat(executionReport.getExecutionId()).isNotBlank();
        assertThat(executionReport.getVenueExecutionId()).isEqualTo(EXECUTION_ID);
        assertThat(executionReport.getVenueAccount()).isEqualTo(VENUE_ACCOUNT);
        assertThat(executionReport.getIntId()).isEqualTo(orderExecutionReport.getIntOrderId());
        assertThat(executionReport.getExtId()).isEmpty();
        assertThat(executionReport.getReason()).isEmpty();
        assertThat(executionReport.getLastQty()).isEmpty();
        assertThat(executionReport.getLeavesQty()).isEmpty();
        assertThat(executionReport.getCumQty()).isEmpty();
        assertThat(executionReport.getLastPrice()).isEmpty();
        assertThat(executionReport.getAvgPrice()).isEmpty();
        assertThat(executionReport.getSystemTimestamp()).isEmpty();
        assertThat(executionReport.getVenueTimestamp()).isEmpty();
    }

    @Test
    void givenIntIdPassedAsIdentifierTradingServiceSendsExecutionReport() {
        OrderExecutionReport orderExecutionReport = intIdPassedAsIdentifier();
        assertThat(orderExecReports.tryEmitNext(orderExecutionReport).isSuccess()).isTrue();
        verify(venueResponseEmitter, timeout(DEFAULT_TIMEOUT.toMillis()).times(1))
            .emitExecReport(venueExecutionReportCaptor.capture());
        VenueResponse executionReport = venueExecutionReportCaptor.getValue();
        assertThat(executionReport.getIntId()).isNotBlank();
        assertThat(executionReport.getExtId()).isNotBlank();
        assertThat(executionReport.getIntId()).isNotEqualTo(executionReport.getExtId());
    }

    @Test
    void givenExtIdPassedAsIdentifierTradingServiceSendsExecutionReport() {
        OrderExecutionReport orderExecutionReport = extIdPassedAsIdentifier();
        assertThat(orderExecReports.tryEmitNext(orderExecutionReport).isSuccess()).isTrue();
        verify(venueResponseEmitter, timeout(DEFAULT_TIMEOUT.toMillis()).times(1))
            .emitExecReport(venueExecutionReportCaptor.capture());
        VenueResponse executionReport = venueExecutionReportCaptor.getValue();
        assertThat(executionReport.getIntId()).isNotBlank();
        assertThat(executionReport.getExtId()).isNotBlank();
        assertThat(executionReport.getIntId()).isNotEqualTo(executionReport.getExtId());
    }

    @Test
    void givenInternalFailureOrderStatusEventTradingServiceSupressesExecutionReport() {
        OrderExecutionReport orderExecutionReport = emptyOrderExecutionReportIntId(Status.INTERNAL_FAILURE);
        assertThat(orderExecReports.tryEmitNext(orderExecutionReport).isSuccess()).isTrue();
        verify(venueResponseEmitter, timeout(DEFAULT_TIMEOUT.toMillis()).times(0))
            .emitExecReport(any());
    }

    @Test
    void givenOrderStatusEventWithoutIntIdTradingServiceSendsExecutionReport() {
        OrderExecutionReport orderExecutionReport = emptyOrderExecutionReportExtId(Status.SUBMITTED);
        assertThat(orderExecReports.tryEmitNext(orderExecutionReport).isSuccess()).isTrue();
        verify(venueResponseEmitter, timeout(DEFAULT_TIMEOUT.toMillis()).times(1))
            .emitExecReport(venueExecutionReportCaptor.capture());
        VenueResponse executionReport = venueExecutionReportCaptor.getValue();
        assertThat(executionReport.getExecType()).isEqualTo(VenueExecType.VENUE_EXEC_TYPE_NEW);
        assertThat(executionReport.getExecutionId()).isNotBlank();
        assertThat(executionReport.getVenueExecutionId()).isEqualTo(EXECUTION_ID);
        assertThat(executionReport.getVenueAccount()).isEqualTo(VENUE_ACCOUNT);
        assertThat(executionReport.getExtId()).isEqualTo(orderExecutionReport.getExtOrderId());
        assertThat(executionReport.getIntId()).isEmpty();
    }

    @Test
    void givenOrderStatusEventTradingServiceCorrectlyEncodesFieldsInExecutionReport() {
        OrderExecutionReport orderStatusEvent = defaultOrderExecutionReport(Status.PARTIALLY_EXECUTED);
        assertThat(orderExecReports.tryEmitNext(orderStatusEvent).isSuccess()).isTrue();
        verify(venueResponseEmitter, timeout(DEFAULT_TIMEOUT.toMillis()).times(1))
            .emitExecReport(venueExecutionReportCaptor.capture());
        VenueResponse executionReport = venueExecutionReportCaptor.getValue();
        assertThat(executionReport.getExecType()).isEqualTo(VenueExecType.VENUE_EXEC_TYPE_PARTIAL_FILL);
        assertThat(executionReport.getExecutionId()).isNotBlank();
        assertThat(executionReport.getVenueExecutionId()).isEqualTo(EXECUTION_ID);
        assertThat(executionReport.getVenueAccount()).isEqualTo(VENUE_ACCOUNT);
        assertThat(executionReport.getIntId()).isEqualTo(orderStatusEvent.getIntOrderId());
        assertThat(executionReport.getExtId()).isEqualTo(orderStatusEvent.getExtOrderId());
        assertThat(executionReport.getReason()).isEqualTo(orderStatusEvent.getReason());
        assertThat(orderStatusEvent.getLastQuantity()).isEqualByComparingTo(executionReport.getLastQty());
        assertThat(orderStatusEvent.getRemainingQuantity()).isEqualByComparingTo(executionReport.getLeavesQty());
        assertThat(orderStatusEvent.getFilledQuantity()).isEqualByComparingTo(executionReport.getCumQty());
        assertThat(orderStatusEvent.getLastPrice()).isEqualByComparingTo(executionReport.getLastPrice());
        assertThat(orderStatusEvent.getAvgPrice()).isEqualByComparingTo(executionReport.getAvgPrice());
        assertThat(executionReport.getSystemTimestamp()).isEqualTo("1918-11-11T05:20:00.000000Z");
        assertThat(executionReport.getVenueTimestamp()).isEqualTo("1918-11-11T05:20:00.000000Z");

    }

    @Test
    void givenOrderStatusEventTradingServiceCorrectlyEncodesEnvironmentTypeLive() {
        doReturn(true).when(tradingController).isLive();

        OrderExecutionReport orderStatusEvent = defaultOrderExecutionReport(Status.PARTIALLY_EXECUTED);
        assertThat(orderExecReports.tryEmitNext(orderStatusEvent).isSuccess()).isTrue();
        verify(venueResponseEmitter, timeout(DEFAULT_TIMEOUT.toMillis()).times(1))
                .emitExecReport(venueExecutionReportCaptor.capture());
        VenueResponse executionReport = venueExecutionReportCaptor.getValue();
        assertThat(executionReport.getExecType()).isEqualTo(VenueExecType.VENUE_EXEC_TYPE_PARTIAL_FILL);

        assertThat(executionReport.getEnvironmentType()).isEqualTo(VenueEnvironmentType.LIVE);
    }

    @Test
    void givenOrderStatusEventTradingServiceCorrectlyEncodesEnvironmentTypeNotLive() {
        doReturn(false).when(tradingController).isLive();

        OrderExecutionReport orderStatusEvent = defaultOrderExecutionReport(Status.PARTIALLY_EXECUTED);
        assertThat(orderExecReports.tryEmitNext(orderStatusEvent).isSuccess()).isTrue();
        verify(venueResponseEmitter, timeout(DEFAULT_TIMEOUT.toMillis()).times(1))
                .emitExecReport(venueExecutionReportCaptor.capture());
        VenueResponse executionReport = venueExecutionReportCaptor.getValue();
        assertThat(executionReport.getExecType()).isEqualTo(VenueExecType.VENUE_EXEC_TYPE_PARTIAL_FILL);

        assertThat(executionReport.getEnvironmentType()).isEqualTo(VenueEnvironmentType.NOT_LIVE);
    }

    @Test
    void givenIsLiveIsNullTradingServiceEncodesEnvironmentTypeUnspecified() {
        doReturn(null).when(tradingController).isLive();

        OrderExecutionReport orderStatusEvent = defaultOrderExecutionReport(Status.PARTIALLY_EXECUTED);
        assertThat(orderExecReports.tryEmitNext(orderStatusEvent).isSuccess()).isTrue();
        verify(venueResponseEmitter, timeout(DEFAULT_TIMEOUT.toMillis()).times(1))
            .emitExecReport(venueExecutionReportCaptor.capture());
        VenueResponse executionReport = venueExecutionReportCaptor.getValue();
        assertThat(executionReport.getExecType()).isEqualTo(VenueExecType.VENUE_EXEC_TYPE_PARTIAL_FILL);

        assertThat(executionReport.getEnvironmentType()).isEqualTo(VenueEnvironmentType.UNSPECIFIED);
    }

    OrderExecutionReport intIdPassedAsIdentifier() {
        String intId = UUID.randomUUID().toString();
        String extId = UUID.randomUUID().toString();
        return OrderExecutionReport.builder()
            .setOrderIdentifier(new OrderIdentifier(intId))
            .setExtId(extId)
            .setTransactionId(EXECUTION_ID)
            .setStatus(Status.SUBMITTED)
            .build();
    }

    OrderExecutionReport extIdPassedAsIdentifier() {
        String intId = UUID.randomUUID().toString();
        String extId = UUID.randomUUID().toString();
        return OrderExecutionReport.builder()
            .setOrderIdentifier(new OrderIdentifier(extId))
            .setIntId(intId)
            .setTransactionId(EXECUTION_ID)
            .setStatus(Status.SUBMITTED)
            .build();
    }

    OrderExecutionReport emptyOrderExecutionReportIntId(Status status) {
        String intId = UUID.randomUUID().toString();
        return OrderExecutionReport.builder()
            .setOrderIdentifier(new OrderIdentifier(intId))
            .setIntId(intId)
            .setTransactionId(EXECUTION_ID)
            .setStatus(status)
            .build();
    }

    @SuppressWarnings("SameParameterValue")
    OrderExecutionReport emptyOrderExecutionReportExtId(Status status) {
        String extId = UUID.randomUUID().toString();
        return OrderExecutionReport.builder()
            .setOrderIdentifier(new OrderIdentifier(extId))
            .setExtId(extId)
            .setTransactionId(EXECUTION_ID)
            .setStatus(status)
            .build();
    }

    @SuppressWarnings("SameParameterValue")
    OrderExecutionReport defaultOrderExecutionReport(Status status) {
        String intId = UUID.randomUUID().toString();
        String extId = UUID.randomUUID().toString();
        ZonedDateTime dateTime = ZonedDateTime.of(1918, 11, 11, 5, 20, 0, 0, ZoneId.of("Europe/Paris"));
        return OrderExecutionReport.builder()
            .setOrderIdentifier(new OrderIdentifier(UUID.randomUUID().toString()))
            .setIntId(intId)
            .setExtId(extId)
            .setTransactionId(EXECUTION_ID)
            .setStatus(status)
            .setReason(UUID.randomUUID().toString())
            .setLastQuantity(randomInt(0, 1000))
            .setRemainingQuantity(randomInt(0, 2000))
            .setFilledQuantity(randomInt(1000, 10000))
            .setLastPrice(randomDouble(0, 1000))
            .setAvgPrice(randomDouble(1000, 2000))
            .setDateTime(dateTime)
            .setExtDateTime(dateTime)
            .build();
    }

    BigDecimal randomInt(int min, int max) {
        int number = ThreadLocalRandom.current().nextInt(min, max + 1);
        return BigDecimal.valueOf(number);
    }

    BigDecimal randomDouble(double min, double max) {
        int scale = 3;
        int minInt = (int) Math.round(min * Math.pow(10, scale));
        int maxInt = (int) Math.round(max * Math.pow(10, scale));
        BigDecimal number = randomInt(minInt, maxInt);
        return number.divide(BigDecimal.valueOf(Math.pow(10, scale)), scale, RoundingMode.CEILING);
    }
}
