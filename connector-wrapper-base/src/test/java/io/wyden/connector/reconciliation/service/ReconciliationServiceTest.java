package io.wyden.connector.reconciliation.service;

import ch.algotrader.api.connector.account.AccountController;
import ch.algotrader.api.connector.account.ByExtIdReconciliationRequest;
import ch.algotrader.api.connector.application.Connector;
import ch.algotrader.api.connector.metadata.AccountCapabilities;
import ch.algotrader.api.connector.metadata.ConnectorCapabilities;
import ch.algotrader.api.connector.trading.domain.FillDTO;
import ch.algotrader.api.connector.trading.domain.OrderReconciliationDTO;
import ch.algotrader.api.connector.trading.domain.OrderReconciliationWithFillsDTO;

import io.wyden.connector.trading.io.VenueResponseEmitter;
import io.wyden.published.venue.VenueReconciliationRequest;
import io.wyden.published.venue.VenueRequest;
import io.wyden.published.venue.VenueResponse;
import io.wyden.published.venue.VenueResponseType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ReconciliationServiceTest {

    private static final String EXT_ID = "extId";

    Connector connector = mock(Connector.class);
    VenueResponseEmitter venueResponseEmitter = mock(VenueResponseEmitter.class);
    AccountController accountController = mock(AccountController.class);
    ConnectorCapabilities connectorCapabilities = mock(ConnectorCapabilities.class);
    AccountCapabilities accountCapabilities = mock(AccountCapabilities.class);

    VenueReconciliationService venueReconciliationService;

    @BeforeEach
    void beforeEach() {
        when(connector.getAccountController()).thenReturn(accountController);
        when(connector.getCapabilities()).thenReturn(connectorCapabilities);
        // TODO: Test for Optional, test for various capabilities
        when(connectorCapabilities.getAccountCapabilities()).thenReturn(Optional.of(accountCapabilities));
        when(accountCapabilities.getSupportedReconciliationRequests()).thenReturn(Set.of(ByExtIdReconciliationRequest.class));
        venueReconciliationService = new VenueReconciliationService(connector, venueResponseEmitter);
    }

    @Test
    void givenFillsUnsupportedRequestIsForwardedToConnector() {
        when(accountController.isOrderReconciliationWithFillsSupported()).thenReturn(false);
        when(accountController.retrieveOrderReconciliations(any())).thenReturn(Mono.just(List.of()));
        venueReconciliationService.handleVenueReconciliationRequest(defaultReconciliationRequest());

        ArgumentCaptor<ByExtIdReconciliationRequest> captor = ArgumentCaptor.forClass(ByExtIdReconciliationRequest.class);
        verify(accountController).retrieveOrderReconciliations(captor.capture());
        verify(accountController, never()).retrieveOrderReconciliationsWithFills(any());

        ByExtIdReconciliationRequest request = captor.getValue();
        assertThat(request.getExtIds()).hasSize(1);
        assertThat(request.getExtIds().get(0)).isEqualTo(EXT_ID);
    }

    @Test
    void givenFillsUnsupportedResponsesAreEmitted() {
        when(accountController.isOrderReconciliationWithFillsSupported()).thenReturn(false);
        when(accountController.retrieveOrderReconciliations(any())).thenReturn(Mono.just(List.of(defaultOrderReconciliationDto(), defaultOrderReconciliationDto())));
        venueReconciliationService.handleVenueReconciliationRequest(defaultReconciliationRequest());

        ArgumentCaptor<VenueResponse> captor = ArgumentCaptor.forClass(VenueResponse.class);
        verify(venueResponseEmitter, times(2)).emitExecReport(captor.capture());

        VenueResponse response = captor.getAllValues().get(1);
        assertThat(response.getResponseType()).isEqualByComparingTo(VenueResponseType.VENUE_RECONCILIATION_RESULT);
        assertThat(response.getExtId()).isEqualTo(EXT_ID);
    }

    @Test
    void givenFillsSupportedRequestIsForwardedToConnector() {
        when(accountController.isOrderReconciliationWithFillsSupported()).thenReturn(true);
        when(accountController.retrieveOrderReconciliationsWithFills(any())).thenReturn(Mono.just(List.of()));
        venueReconciliationService.handleVenueReconciliationRequest(defaultReconciliationRequest());

        ArgumentCaptor<ByExtIdReconciliationRequest> captor = ArgumentCaptor.forClass(ByExtIdReconciliationRequest.class);
        verify(accountController).retrieveOrderReconciliationsWithFills(captor.capture());
        verify(accountController, never()).retrieveOrderReconciliations(any());

        ByExtIdReconciliationRequest request = captor.getValue();
        assertThat(request.getExtIds()).hasSize(1);
        assertThat(request.getExtIds().get(0)).isEqualTo(EXT_ID);
    }

    @Test
    void givenFillsSupportedResponsesAreEmitted() {
        List<OrderReconciliationWithFillsDTO> result = List.of(defaultOrderReconciliationWithFillsDto(), defaultOrderReconciliationWithFillsDto());
        when(accountController.isOrderReconciliationWithFillsSupported()).thenReturn(true);
        when(accountController.retrieveOrderReconciliationsWithFills(any())).thenReturn(Mono.just(result));
        venueReconciliationService.handleVenueReconciliationRequest(defaultReconciliationRequest());

        ArgumentCaptor<VenueResponse> captor = ArgumentCaptor.forClass(VenueResponse.class);
        verify(venueResponseEmitter, times(2)).emitExecReport(captor.capture());

        VenueResponse response = captor.getAllValues().get(1);
        assertThat(response.getResponseType()).isEqualByComparingTo(VenueResponseType.VENUE_RECONCILIATION_RESULT);
        assertThat(response.getExtId()).isEqualTo(EXT_ID);
    }

    VenueReconciliationRequest defaultReconciliationRequest() {
        VenueRequest venueRequest = VenueRequest.newBuilder()
            .setExtId(EXT_ID)
            .build();
        return VenueReconciliationRequest.newBuilder()
            .addVenueOrders(venueRequest)
            .build();
    }

    OrderReconciliationDTO defaultOrderReconciliationDto() {
        return OrderReconciliationDTO.builder()
            .setExtId(EXT_ID)
            .build();
    }

    OrderReconciliationWithFillsDTO defaultOrderReconciliationWithFillsDto() {
        FillDTO fillDto = FillDTO.builder()
            .withTransactionId(EXT_ID)
            .build();
        return OrderReconciliationWithFillsDTO.builder()
            .withOrderReconciliation(defaultOrderReconciliationDto())
            .withFills(Set.of(fillDto))
            .build();
    }
}
