query($id: ID!) {
    portfolioGroupConfiguration(id: $id) {
        id
        name
        portfolioType
        instrumentConfiguration {
            instrumentId
            tradeable
            pricingConfiguration {
                markup
                pricingSource {
                    instrument {
                        archivedAt
                        baseInstrument {
                            venueName
                            venueType
                            assetClass
                            description
                            quoteCurrency
                            inverseContract
                            symbol
                        }
                        instrumentIdentifiers {
                            adapterTicker
                            tradingViewId
                            venueTradingViewId
                            instrumentId
                        }
                        tradingConstraints {
                            minQty
                            maxQty
                            qtyIncr
                            minPrice
                            maxPrice
                            priceIncr
                            minNotional
                            contractSize
                            tradeable
                        }
                        forexSpotProperties {
                            baseCurrency
                        }
                    }
                }
            }
        }
        executionConfiguration {
            tradingMode
            counterPortfolioId
            fixedFee
            fixedFeeCurrency
            percentageFee
            percentageFeeCurrency
            percentageFeeCurrencyType
            minFee
            minFeeCurrency
            agencyTradingAccount
            chargeExchangeFee
            discloseTradingVenue
        }
        pricingConfiguration {
            venueAccounts {
                id
                name
            }
            markup
        }
    }
}
