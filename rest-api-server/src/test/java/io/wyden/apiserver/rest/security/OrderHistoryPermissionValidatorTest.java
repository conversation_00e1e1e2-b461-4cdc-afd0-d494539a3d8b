package io.wyden.apiserver.rest.security;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.apiserver.rest.orderhistory.model.CollectionPredicateInput;
import io.wyden.apiserver.rest.orderhistory.model.OrderHistorySearchInput;
import io.wyden.apiserver.rest.orderhistory.model.SimplePredicateInput;
import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioService;
import io.wyden.apiserver.rest.referencedata.portfolio.service.SecuredPortfolioService;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.apiserver.rest.venueaccount.SecuredVenueAccountService;
import io.wyden.apiserver.rest.venueaccount.VenueAccountService;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.referencedata.VenueAccount;

import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

class OrderHistoryPermissionValidatorTest {

    Set<PermissionDto> permissions = Set.of(
        new PermissionDto("venue.account", "read", "simulator"),
        new PermissionDto("venue.account", "read", "bitmex-testnet1"),
        new PermissionDto("venue.account", "read", "simulator2"),
        new PermissionDto("venue.account", "read", "bitmex-testnet2"),
        new PermissionDto("venue.account", "read", "simulator3"),
        new PermissionDto("venue.account", "read", "bitmex-testnet3"),
        new PermissionDto("portfolio", "read", "Client 1"),
        new PermissionDto("portfolio", "read", "Client 2"),
        new PermissionDto("portfolio", "read", "Client 3"),
        new PermissionDto("portfolio", "read", "Client 4"),
        new PermissionDto("portfolio", "read", "Client 5"),
        new PermissionDto("portfolio", "read", "Bank"),
        new PermissionDto("portfolio", "read", "e2e_BANK_Portfolio"),
        new PermissionDto("portfolio", "read", "e2e_RETAIL_portfolio")
    );

    Set<String> vostroIds = Set.of("Client 1", "Client 2", "Client 3");

    Collection<String> venueAccountsIdsInPermissions = getVenueIdsFromPermissions();
    Collection<String> portfolioIdsInPermissions = getPortfolioIdsFromPermissions();
    Collection<String> portfolioVostroIdsInPermissions = getPortfolioVostroIdsFromPermissions();
    Collection<String> portfolioNostroIdsInPermissions = getPortfolioNostroIdsFromPermissions();

    VenueAccountService venueAccountService = Mockito.mock(VenueAccountService.class);
    PortfolioService portfolioService = Mockito.mock(PortfolioService.class);

    AccessGatewayMockClient accessGatewayClient = new AccessGatewayMockClient(permissions);
    AccessService accessService = new AccessService(accessGatewayClient);
    SecuredVenueAccountService securedVenueAccountService = new SecuredVenueAccountService(accessService, venueAccountService);
    SecuredPortfolioService securedPortfolioService = new SecuredPortfolioService(accessService, portfolioService);
    OrderHistoryPermissionValidator sut = new OrderHistoryPermissionValidator(accessService, securedVenueAccountService, securedPortfolioService);

    @BeforeEach
    public void setup() {
        accessGatewayClient.grantPermissions(permissions);

        when(venueAccountService.findAll()).thenReturn(permissions.stream()
            .filter(p -> p.getResource().equals("venue.account"))
            .map(p -> VenueAccount.newBuilder().setId(p.getResourceId()).setVenueAccountName(p.getResourceId()).build()));

        when(portfolioService.findAll()).thenReturn(permissions.stream()
            .filter(p -> p.getResource().equals("portfolio"))
            .map(p -> Portfolio.newBuilder()
                .setId(p.getResourceId())
                .setName(p.getResourceId())
                .setPortfolioType(vostroIds.contains(p.getResourceId()) ? PortfolioType.VOSTRO : PortfolioType.NOSTRO)
                .build())
            .collect(Collectors.toSet()));
    }

    @Test
    void emptyRequestWithoutPermissions_authorizedRequestShouldBeNull() {
        accessGatewayClient.revokePermissions();

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNull();
    }

    @Test
    void emptyRequestWithOnlyDynamicPermissions_shouldContainsAllResourcesWithDynamicPermission() {

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.collectionPredicates()).isNotEmpty();

    }

    @Test
    void emptyRequestWithStaticReadPermissions_shouldReturnEmptyRequest() {
        accessGatewayClient.grantStaticPortfolioPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.collectionPredicates()).isEmpty();
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void emptyRequestWithStaticReadVostroNostroPermissions_shouldReturnEmptyRequest() {
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.collectionPredicates()).isEmpty();
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void emptyRequestWithPortfolioStaticReadPermissions_shouldReturnRequestWithVenueIds() {
        accessGatewayClient.grantStaticPortfolioPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).containsAll(venueAccountsIdsInPermissions);
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).isEmpty();

    }

    @Test
    void emptyRequestWithVenueStaticReadPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).isEmpty();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsAll(portfolioIdsInPermissions);

    }

    @Test
    void emptyRequestWithVenueStaticReadPermissionsAndPortfolioVostroPermissions_shouldReturnRequestWithPredicateForPortfolioType() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).isEmpty();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsAll(portfolioVostroIdsInPermissions);
        assertThat(authorized.simplePredicates()).hasSize(1);
        assertThat(authorized.simplePredicates().stream()
            .filter(p -> p.field().equals(SimplePredicateInput.Field.PORTFOLIO_TYPE))
            .map(SimplePredicateInput::value)
            .toList()
        ).contains("VOSTRO");
    }

    @Test
    void emptyRequestWithVenueStaticReadPermissionsAndPortfolioNostroPermissions_shouldReturnRequestWithPredicateForPortfolioType() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).isEmpty();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsAll(portfolioNostroIdsInPermissions);
        assertThat(authorized.simplePredicates()).hasSize(1);
        assertThat(authorized.simplePredicates().stream()
            .filter(p -> p.field().equals(SimplePredicateInput.Field.PORTFOLIO_TYPE))
            .map(SimplePredicateInput::value)
            .toList()
        ).contains("NOSTRO");
    }

    @Test
    void requestedAccountsWithOnlyDynamicPermissions_shouldContainsRequestedResourcesWithDynamicPermission() {

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, "simulator")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).containsOnly("simulator");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedPortfolioWithOnlyDynamicPermissions_shouldContainsRequestedResourcesWithDynamicPermission() {

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, "Client 1")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsOnly("Client 1");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedAccountsWithStaticPermissions_shouldContainsOnlyRequestedResources() {
        accessGatewayClient.grantStaticPortfolioPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, "simulator")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).containsOnly("simulator");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedPortfolioWithOnlyDynamicPermissions_shouldContainsOnlyRequestedResources() {
        accessGatewayClient.grantStaticPortfolioPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, "Client 1")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsOnly("Client 1");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedPortfolioIdWithoutDynamicAccessWithStaticAccountAndStaticVostroNostro_shouldContainsOnlyRequestedResources() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, "Client 1")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsOnly("Client 1");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedAccountsWithPortfolioStaticPermissions_shouldReturnRequestWithVenueIds() {
        accessGatewayClient.grantStaticPortfolioPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, "simulator")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).containsOnly("simulator");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedPortfolioWithPortfolioStaticPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticPortfolioPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, "Client 1")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsOnly("Client 1");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedPortfolioWithPortfolioContainsPredicate_shouldThrow() {

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.CONTAINS, SimplePredicateInput.Field.PORTFOLIO_ID, "Client")));
        assertThatThrownBy(() -> sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER")))
            .hasMessage("Only simple predicates with method EQUALS are supported");
    }

    @Test
    void requestedSimpleContainsPredicateOtherThatPortfolio_shouldNotThrow() {

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.CONTAINS, SimplePredicateInput.Field.ORDER_ID, "xxx")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, new WydenAuthenticationToken("TEST_USER"));
        assertThat(authorized).isNotNull();
    }


    private static @NotNull OrderHistorySearchInput emptyRequest() {
        return new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, null);
    }

    private Collection<String> getPortfolioIdsFromPermissions() {
        return permissions.stream()
            .filter(p -> p.getResource().equals("portfolio"))
            .map(PermissionDto::getResourceId)
            .collect(Collectors.toSet());
    }

    private Collection<String> getPortfolioVostroIdsFromPermissions() {
        return permissions.stream()
            .filter(p -> p.getResource().equals("portfolio"))
            .filter(p -> this.vostroIds.contains(p.getResourceId()))
            .map(PermissionDto::getResourceId)
            .collect(Collectors.toSet());
    }

    private Collection<String> getPortfolioNostroIdsFromPermissions() {
        return permissions.stream()
            .filter(p -> p.getResource().equals("portfolio"))
            .filter(p -> !this.vostroIds.contains(p.getResourceId()))
            .map(PermissionDto::getResourceId)
            .collect(Collectors.toSet());
    }

    private Collection<String> getVenueIdsFromPermissions() {
        return permissions.stream()
            .filter(p -> p.getResource().equals("venue.account"))
            .map(PermissionDto::getResourceId)
            .collect(Collectors.toSet());
    }

    Collection<String> collectionPredicatesToPortfolioIds(Collection<CollectionPredicateInput> predicates) {
        return predicates.stream()
            .filter(p -> p.field().equals(CollectionPredicateInput.Field.PORTFOLIO_ID))
            .map(CollectionPredicateInput::value)
            .flatMap(Collection::stream)
            .collect(Collectors.toSet());
    }

    Collection<String> collectionPredicatesToAccountIds(Collection<CollectionPredicateInput> predicates) {
        return predicates.stream()
            .filter(p -> p.field().equals(CollectionPredicateInput.Field.VENUE_ACCOUNT_ID))
            .map(CollectionPredicateInput::value)
            .flatMap(Collection::stream)
            .collect(Collectors.toSet());
    }


}