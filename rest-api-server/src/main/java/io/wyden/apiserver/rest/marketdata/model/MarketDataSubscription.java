package io.wyden.apiserver.rest.marketdata.model;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

public class MarketDataSubscription {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataSubscription.class);

    private final Sinks.Many<MarketDataEvent> sink = Sinks.many().replay().latest();
    private final MarketDataSubscriptionKey marketDataSubscriptionKey;
    private final Set<MdClientRequest> clientRequests = new HashSet<>();

    private Disposable underlyingSubscription;
    private String requestId;
    private String streamId;

    public MarketDataSubscription(MarketDataSubscriptionKey marketDataSubscriptionKey,
                                  MdClientRequest clientRequest) {
        this.marketDataSubscriptionKey = marketDataSubscriptionKey;
        this.requestId = UUID.randomUUID().toString();
        this.clientRequests.add(clientRequest);
    }

    public synchronized void addClientRequest(MdClientRequest clientRequest) {
        this.clientRequests.add(clientRequest);
    }

    public synchronized void removeClientRequest(MdClientRequest clientRequest) {
        this.clientRequests.remove(clientRequest);
    }

    public int getClientRequestsCount() {
        return this.clientRequests.size();
    }

    public void forward(MarketDataEvent marketDataEvent) {
        sink.tryEmitNext(marketDataEvent);
    }

    public void terminate(Throwable error) {
        sink.tryEmitError(error);
    }

    public Flux<MarketDataEvent> getFlux() {
        LOGGER.debug("Getting event flux for subscriptionKey: {}", marketDataSubscriptionKey);
        return sink.asFlux();
    }

    public MarketDataSubscriptionKey getMarketDataSubscriptionKey() {
        return marketDataSubscriptionKey;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean hasUnderlying() {
        return underlyingSubscription != null;
    }

    public void dispose() {
        if (underlyingSubscription != null) {
            underlyingSubscription.dispose();
            underlyingSubscription = null;
        }

        sink.tryEmitComplete();
    }

    public Set<MdClientRequest> getClientRequests() {
        return clientRequests;
    }

    public void setUnderlyingSubscription(Disposable underlyingSubscription) {
        this.underlyingSubscription = underlyingSubscription;
    }

    public void setStreamId(String streamId) {
        this.streamId = streamId;
    }

    public String getStreamId() {
        return streamId;
    }

    public boolean isMatchingEvent(MarketDataIdentifierDTO identifier) {
        if (isNotEmpty(streamId) && isNotEmpty(identifier.streamId())) {
            return Objects.equals(streamId, identifier.streamId());
        } else if (isStreetSubscription()) {
            return Objects.equals(marketDataSubscriptionKey.instrumentId(), identifier.instrumentId())
                && Objects.equals(marketDataSubscriptionKey.venueAccount(), identifier.venueAccount());
        } else {
            return false;
        }
    }

    private boolean isStreetSubscription() {
        return marketDataSubscriptionKey.venueAccount() != null && !marketDataSubscriptionKey.venueAccount().isBlank();
    }

    @Override
    public String toString() {
        return "MarketDataSubscription{" +
            "marketDataSubscriptionKey=" + marketDataSubscriptionKey +
            ", requestId='" + requestId + '\'' +
            ", clientRequests=" + clientRequests +
            ", streamId='" + streamId + '\'' +
            '}';
    }
}
