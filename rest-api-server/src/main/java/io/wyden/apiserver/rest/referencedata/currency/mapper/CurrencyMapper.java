package io.wyden.apiserver.rest.referencedata.currency.mapper;

import io.wyden.apiserver.rest.referencedata.currency.CurrencyInput;
import io.wyden.apiserver.rest.referencedata.currency.CurrencyResponseDto;
import io.wyden.apiserver.rest.referencedata.currency.CurrencyCategoryDto;
import io.wyden.published.referencedata.Currency;
import io.wyden.published.referencedata.CurrencyCategory;

import java.util.Objects;

public class CurrencyMapper {

    private CurrencyMapper() {
    }

    public static CurrencyCategory map(CurrencyCategoryDto currencyCategoryDto) {
        if (Objects.isNull(currencyCategoryDto)) {
            return null;
        }

        return switch (currencyCategoryDto) {
            case FIAT -> CurrencyCategory.CURRENCY_CATEGORY_FIAT;
            case CRYPTO -> CurrencyCategory.CURRENCY_CATEGORY_CRYPTO;
        };
    }

    public static CurrencyCategoryDto map(CurrencyCategory currencyCategory) {
        return switch (currencyCategory) {
            case CURRENCY_CATEGORY_FIAT -> CurrencyCategoryDto.FIAT;
            case CURRENCY_CATEGORY_CRYPTO -> CurrencyCategoryDto.CRYPTO;
            case CURRENCY_CATEGORY_UNSPECIFIED -> null;
            case UNRECOGNIZED -> null;
        };
    }

    public static CurrencyResponseDto map(Currency currency) {
        return new CurrencyResponseDto(currency.getSymbol(), map(currency.getCurrencyCategory()), currency.getPrecision(), currency.getDisplayPrecision());
    }

    public static Currency map(CurrencyInput currencyInput) {
        return Currency.newBuilder()
            .setSymbol(currencyInput.symbol())
            .setCurrencyCategory(map(currencyInput.type()))
            .setPrecision(currencyInput.precision())
            .setDisplayPrecision(currencyInput.displayPrecision())
            .build();
    }
}
