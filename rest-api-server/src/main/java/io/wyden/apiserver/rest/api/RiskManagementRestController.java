package io.wyden.apiserver.rest.api;

import io.wyden.apiserver.rest.risk.RiskManagementService;
import io.wyden.apiserver.rest.utils.ProtobufUtils;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.Metadata;
import io.wyden.published.risk.PreTradeCheck;
import io.wyden.published.risk.PreTradeChecksList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.time.ZonedDateTime;
import java.util.UUID;

@RestController
@RequestMapping("/api/risk/pretradechecks")
public class RiskManagementRestController {
    private static final Logger LOGGER = LoggerFactory.getLogger(RiskManagementRestController.class);

    private final RiskManagementService riskManagementService;

    public RiskManagementRestController(RiskManagementService riskManagementService) {
        this.riskManagementService = riskManagementService;
    }

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("hasPermission('risk', 'manage')")
    public Mono<String> savePreTradeCheck(@RequestBody String json) {
        PreTradeCheck.Builder requestBuilder = PreTradeCheck.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .build());
        PreTradeCheck request = ProtobufUtils.fromJson(json, requestBuilder).build();
        return riskManagementService.savePreTradeCheck(request)
            .map(ProtobufUtils::toJson);
    }

    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("hasPermission('risk', 'read')")
    public Mono<String> getPreTradeChecks() {
        return riskManagementService.getPreTradeChecks()
            .map(PreTradeChecksList::getItemsList)
            .map(ProtobufUtils::toJson)
            .defaultIfEmpty("[]");
    }

    @DeleteMapping("/{preTradeCheckId}")
    @PreAuthorize("hasPermission('risk', 'manage')")
    public Mono<Void> deletePreTradeCheck(@PathVariable String preTradeCheckId) {
        return riskManagementService.deletePreTradeCheck(preTradeCheckId);
    }

}
