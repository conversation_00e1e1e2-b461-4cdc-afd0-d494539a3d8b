package io.wyden.apiserver.rest.clob;

import com.google.protobuf.ProtocolStringList;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel;
import io.wyden.apiserver.rest.brokerdesk.config.ProtoMappings;
import io.wyden.apiserver.rest.referencedata.instruments.mapper.InstrumentReferenceDataMapper;
import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioRepository;
import io.wyden.apiserver.rest.utils.PortfolioUtils;
import io.wyden.apiserver.rest.venueaccount.VenueAccountService;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.brokerdesk.CalendarEntry;
import io.wyden.published.brokerdesk.ErrorType;
import io.wyden.published.brokerdesk.InstrumentQuotingConfig;
import io.wyden.published.brokerdesk.QuotingConfig;
import io.wyden.published.brokerdesk.QuotingConfigValidation;
import io.wyden.published.brokerdesk.QuotingSource;
import io.wyden.published.brokerdesk.QuotingSourceAccountConfig;
import io.wyden.published.brokerdesk.ValidationError;
import io.wyden.published.brokerdesk.ValidationResult;
import io.wyden.published.brokerdesk.Validations;
import io.wyden.published.common.DayOfTheWeek;
import io.wyden.published.common.TimeInAWeek;
import io.wyden.published.common.TimeUnit;
import jakarta.annotation.Nullable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Map;

import static java.math.BigDecimal.ZERO;
import static java.util.Objects.requireNonNull;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;

@Component
public class QuotingConfigMapper {

    public static final BigDecimal MINUS_ONE = new BigDecimal("-1");
    private final InstrumentReferenceDataMapper instrumentReferenceDataMapper;
    private final PortfolioRepository portfolioRepository;
    private final VenueAccountService venueAccountService;

    public QuotingConfigMapper(InstrumentReferenceDataMapper instrumentReferenceDataMapper,
                               PortfolioRepository portfolioRepository,
                               VenueAccountService venueAccountService) {
        this.instrumentReferenceDataMapper = instrumentReferenceDataMapper;
        this.portfolioRepository = portfolioRepository;
        this.venueAccountService = venueAccountService;
    }

    public List<QuotingModel.QuotingConfiguration> fromProto(List<QuotingConfig> configs) {
        if (isEmpty(configs)) {
            return List.of();
        }

        return configs.stream()
            .map(config -> {
                List<String> sourceVenueAccounts = config.getSourceVenueAccountsList().stream().toList();
                ProtocolStringList hedgingAccounts = config.getHedgingAccountsList();
                return new QuotingModel.QuotingConfiguration(
                    String.valueOf(config.getClobUid()),
                    !config.getQuotingEnabled(),
                    DateUtils.isoUtcTimeToEpochMillis(config.getUpdatedAt()),
                    config.getDisplayName(),
                    config.getAskMarkup().isEmpty() ? null : new BigDecimal(config.getAskMarkup()),
                    config.getBidMarkup().isEmpty() ? null : new BigDecimal(config.getBidMarkup()),
                    config.getNostroPortfolio(),
                    PortfolioUtils.getPortfolioName(config.getNostroPortfolio(), portfolioRepository),
                    sourceVenueAccounts,
                    fromProtoSources(config.getSourcesList()),
                    venueAccountService.getVenueAccountDescs(sourceVenueAccounts),
                    new BigDecimal(config.getMinQuantityFactor()),
                    new BigDecimal(config.getMaxQuantityFactor()),
                    config.getMaximumDepth().isEmpty() ? null : new BigDecimal(config.getMaximumDepth()),
                    config.getThrottlingPeriod() == 0 ? null : Math.toIntExact(config.getThrottlingPeriod()),
                    config.getQuoteTtl() == 0 ? null : config.getQuoteTtl(),
                    hedgingAccounts,
                    venueAccountService.getVenueAccountDescs(hedgingAccounts),
                    config.getHedgingSafetyMargin().isBlank() ? ZERO : new BigDecimal(config.getHedgingSafetyMargin()),
                    fromProtoInstrumentConfigs(config.getInstrumentQuotingConfigsList()));
            })
            .toList();
    }

    private List<QuotingModel.QuotingSourceAccountConfig> fromProtoSources(List<QuotingSourceAccountConfig> sourcesList) {
        return sourcesList.stream()
            .map(proto -> new QuotingModel.QuotingSourceAccountConfig(
                proto.getVenueAccountId(),
                venueAccountService.getVenueAccountName(proto.getVenueAccountId()),
                proto.getIsDroppingQuotesOnDisconnection(),
                proto.getDefaultQuoteTtl(),
                fromProto(proto.getDefaultQuoteTtlUnit()),
                fromProtoCalendarEntries(proto.getCalendarEntriesList())
            ))
            .toList();
    }

    private List<QuotingModel.QuotingCalendar> fromProtoCalendarEntries(List<CalendarEntry> protos) {
        return protos.stream()
            .map(proto -> {
                return new QuotingModel.QuotingCalendar(
                    fromProto(proto.getFrom().getDay()),
                    fromProto(proto.getTo().getDay()),
                    proto.getFrom().getTime(),
                    proto.getTo().getTime(),
                    proto.getQuoteTtl(),
                    fromProto(proto.getQuoteTtlUnit()),
                    new BigDecimal(proto.getAdditionalMarkup())
                );
            })
            .toList();
    }

    private QuotingModel.DayOfTheWeek fromProto(DayOfTheWeek day) {
        return switch (day) {
            case MONDAY -> QuotingModel.DayOfTheWeek.MONDAY;
            case TUESDAY -> QuotingModel.DayOfTheWeek.TUESDAY;
            case WEDNESDAY -> QuotingModel.DayOfTheWeek.WEDNESDAY;
            case THURSDAY -> QuotingModel.DayOfTheWeek.THURSDAY;
            case FRIDAY -> QuotingModel.DayOfTheWeek.FRIDAY;
            case SATURDAY -> QuotingModel.DayOfTheWeek.SATURDAY;
            case SUNDAY -> QuotingModel.DayOfTheWeek.SUNDAY;
            case UNRECOGNIZED, DAY_OF_THE_WEEK_UNSPECIFIED -> throw new IllegalArgumentException("Unsupported day of week: " + day);
        };
    }

    private QuotingModel.QuoteTTLUnit fromProto(TimeUnit defaultQuoteTtlUnit) {
        return switch (defaultQuoteTtlUnit) {
            case MILLISECONDS -> QuotingModel.QuoteTTLUnit.MILLISECONDS;
            case SECONDS -> QuotingModel.QuoteTTLUnit.SECONDS;
            case MINUTES -> QuotingModel.QuoteTTLUnit.MINUTES;
            case HOURS -> QuotingModel.QuoteTTLUnit.HOURS;
            case DAYS -> QuotingModel.QuoteTTLUnit.DAYS;
            case UNRECOGNIZED, TIME_UNIT_UNSPECIFIED -> throw new IllegalArgumentException("Unsupported time unit: " + defaultQuoteTtlUnit);
        };
    }

    public List<QuotingModel.QuotingConfigValidationResult> fromProtoValidations(List<QuotingConfigValidation> validations) {
        if (isEmpty(validations)) {
            return List.of();
        }

        return validations.stream()
            .map(v -> new QuotingModel.QuotingConfigValidationResult(v.getClobUid(), fromProto(v.getValidations())))
            .toList();
    }

    private List<QuotingModel.InstrumentQuotingConfiguration> fromProtoInstrumentConfigs(List<InstrumentQuotingConfig> instrumentQuotingConfigsList) {
        return instrumentQuotingConfigsList.stream()
            .map(proto -> new QuotingModel.InstrumentQuotingConfiguration(
                proto.getInstrumentId(),
                !proto.getQuotingEnabled(),
                fromProtoQuotingSource(proto.getQuotingSourcesList()),
                ProtoMappings.map(proto.getPriceIncrement()),
                ProtoMappings.map(proto.getQuantityIncrement()),
                ProtoMappings.map(proto.getMaximumDepth()),
                proto.getQuoteTtl() == 0 ? null : proto.getQuoteTtl(),
                ProtoMappings.map(proto.getAskMarkup()),
                ProtoMappings.map(proto.getBidMarkup()),
                ProtoMappings.map(proto.getMinQuantityFactor()),
                ProtoMappings.map(proto.getMaxQuantityFactor()),
                ProtoMappings.map(proto.getHedgingSafetyMargin())
            ))
            .toList();
    }

    private List<QuotingModel.SourceConfiguration> fromProtoQuotingSource(List<QuotingSource> quotingSourcesList) {
        return quotingSourcesList.stream()
            .map(proto -> new QuotingModel.SourceConfiguration(
                instrumentReferenceDataMapper.mapToInstrumentDto(proto.getSourceInstrumentId()),
                instrumentReferenceDataMapper.mapToInstrumentDto(proto.getConversionSourceInstrumentId()),
                proto.getInverse()
            ))
            .toList();
    }

    public InstrumentQuotingConfig toProto(QuotingModel.InstrumentQuotingConfigurationInput request) {
        return InstrumentQuotingConfig.newBuilder()
            .setInstrumentId(request.instrumentId())
            .setQuotingEnabled(!request.deactivated())
            .addAllQuotingSources(toProtoQuotingSources(request.sourceConfigurations()))
            .setPriceIncrement(withMinusOneAsNull(request.priceLevelIncrement()))
            .setQuantityIncrement(withMinusOneAsNull(request.quantityIncrement()))
            .setMaximumDepth(withMinusOneAsNull(request.maximumDepth()))
            .setQuoteTtl(request.quoteTTL() != null ? request.quoteTTL() : 0)
            .setAskMarkup(withMinusOneAsNull(request.askMarkup()))
            .setBidMarkup(withMinusOneAsNull(request.bidMarkup()))
            .setMinQuantityFactor(withMinusOneAsNull(request.minQuantityFactor()))
            .setMaxQuantityFactor(withMinusOneAsNull(request.maxQuantityFactor()))
            .setHedgingSafetyMargin(withMinusOneAsNull(request.hedgingSafetyMargin()))
            .build();
    }

    // TODO-MD should be removed after UI stops sending -1 for values not set
    private static String withMinusOneAsNull(BigDecimal value) {
        if (value == null) {
            return "";
        }

        if (value.compareTo(MINUS_ONE) == 0) {
            return "";
        }
        return ProtoMappings.mapNotNull(value);
    }

    private static List<QuotingSource> toProtoQuotingSources(List<QuotingModel.SourceConfigurationInput> sourceConfigurations) {
        return sourceConfigurations.stream()
            .map(dto -> QuotingSource.newBuilder()
                .setSourceInstrumentId(dto.sourceInstrumentId())
                .setConversionSourceInstrumentId(dto.conversionSourceInstrumentId() != null ? dto.conversionSourceInstrumentId() : EMPTY)
                .setInverse(dto.inverse())
                .build())
            .toList();
    }

    public QuotingConfig toProto(QuotingModel.QuotingConfigurationInput configurationInput, @Nullable String clobUid) {
        QuotingConfig.Builder builder = QuotingConfig.newBuilder()
            .setQuotingEnabled(!configurationInput.deactivated())
            .setDisplayName(configurationInput.displayName())
            .setNostroPortfolio(configurationInput.nostroPortfolioId())
            .setMinQuantityFactor(withMinusOneAsNull(configurationInput.minQuantityFactor()))
            .setMaxQuantityFactor(withMinusOneAsNull(configurationInput.maxQuantityFactor()));

        // for backward-compatibility:
        if (configurationInput.sourceAccounts() != null) {
            builder.addAllSourceVenueAccounts(configurationInput.sourceAccounts());
        }

        if (clobUid != null) {
            builder.setClobUid(Long.parseLong(clobUid));
        }

        if (configurationInput.sources() != null && !configurationInput.sources().isEmpty()) {
            List<QuotingSourceAccountConfig> sources = configurationInput.sources().stream()
                .map(input -> {
                    Integer defaultQuoteTTL = requireNonNull(input.defaultQuoteTTL());
                    if (defaultQuoteTTL <= 0) {
                        throw new IllegalArgumentException(
                            String.format("DefaultQuoteTTL cannot be negative, but was %s for source %s", defaultQuoteTTL, input));
                    }
                    return QuotingSourceAccountConfig.newBuilder()
                        .setVenueAccountId(requireNonNull(input.accountId()))
                        .setIsDroppingQuotesOnDisconnection(requireNonNull(input.isDroppingQuotesOnDisconnection()))
                        .setDefaultQuoteTtl(input.defaultQuoteTTL())
                        .setDefaultQuoteTtlUnit(toProto(input.quoteTTLUnit()))
                        .addAllCalendarEntries(toProtoCalendarEntries(input.calendarEntries()))
                        .build();
                })
                .toList();
            builder.addAllSources(sources);
            // for backward compatibility:
            builder.clearSourceVenueAccounts();
            builder.addAllSourceVenueAccounts(sources.stream().map(QuotingSourceAccountConfig::getVenueAccountId).toList());
        }

        builder.setAskMarkup(withMinusOneAsNull(configurationInput.askMarkup()));
        builder.setBidMarkup(withMinusOneAsNull(configurationInput.bidMarkup()));

        builder.setMaximumDepth(withMinusOneAsNull(configurationInput.maximumDepth()));

        if (configurationInput.throttlingPeriod() != null) {
            builder.setThrottlingPeriod(configurationInput.throttlingPeriod());
        }

        if (configurationInput.quoteTTL() != null) {
            builder.setQuoteTtl(configurationInput.quoteTTL());
        }

        builder.setHedgingSafetyMargin(withMinusOneAsNull(configurationInput.hedgingSafetyMargin()));

        if (!configurationInput.hedgingAccounts().isEmpty()) {
            builder.addAllHedgingAccounts(configurationInput.hedgingAccounts());
        }

        return builder.build();
    }

    private TimeUnit toProto(QuotingModel.QuoteTTLUnit input) {
        return switch (input) {
            case MILLISECONDS -> TimeUnit.MILLISECONDS;
            case SECONDS -> TimeUnit.SECONDS;
            case MINUTES -> TimeUnit.MINUTES;
            case HOURS -> TimeUnit.HOURS;
            case DAYS -> TimeUnit.DAYS;
        };
    }

    private List<CalendarEntry> toProtoCalendarEntries(List<QuotingModel.QuotingCalendarInput> quotingCalendarInputs) {
        return quotingCalendarInputs.stream()
            .map(input -> CalendarEntry.newBuilder()
                .setFrom(toProto(input.fromDayOfTheWeek(), input.startTime()))
                .setTo(toProto(input.toDayOfTheWeek(), input.endTime()))
                .setQuoteTtl(input.quoteTTL())
                .setQuoteTtlUnit(toProto(input.quoteTTLUnit()))
                .setAdditionalMarkup(input.additionalMarkup().toString())
                .build())
            .toList();
    }

    private TimeInAWeek toProto(QuotingModel.DayOfTheWeek dayOfTheWeek, String time) {
        return TimeInAWeek.newBuilder()
            .setDay(toProto(dayOfTheWeek))
            .setTime(time)
            .build();
    }

    private DayOfTheWeek toProto(QuotingModel.DayOfTheWeek dayOfTheWeek) {
        return switch (dayOfTheWeek) {
            case MONDAY -> DayOfTheWeek.MONDAY;
            case TUESDAY -> DayOfTheWeek.TUESDAY;
            case WEDNESDAY -> DayOfTheWeek.WEDNESDAY;
            case THURSDAY -> DayOfTheWeek.THURSDAY;
            case FRIDAY -> DayOfTheWeek.FRIDAY;
            case SATURDAY -> DayOfTheWeek.SATURDAY;
            case SUNDAY -> DayOfTheWeek.SUNDAY;
        };
    }

    public List<BrokerDeskConfigModel.ConfigInstrumentValidationResult> fromProto(Validations validations) {
        return validations.getResultPerInstrumentMap().entrySet().stream()
            .map(QuotingConfigMapper::getConfigInstrumentValidationResult)
            .toList();
    }

    private static BrokerDeskConfigModel.ConfigInstrumentValidationResult getConfigInstrumentValidationResult(Map.Entry<String, ValidationResult> entry) {
        String instrumentId = entry.getKey();
        List<ValidationError> errorsList = entry.getValue().getErrorsList();

        return new BrokerDeskConfigModel.ConfigInstrumentValidationResult(
            instrumentId,
            errorsList.isEmpty(),
            false,
            false,
            fromProtoErrorList(errorsList)
        );
    }

    private static List<BrokerDeskConfigModel.ConfigValidationError> fromProtoErrorList(List<ValidationError> errorsList) {
        return errorsList.stream()
            .map(proto -> new BrokerDeskConfigModel.ConfigValidationError(
                proto.getFieldName(),
                fromProto(proto.getErrorType()),
                false,
                false,
                proto.getDescription()
            ))
            .toList();
    }

    private static BrokerDeskConfigModel.ErrorType fromProto(ErrorType errorType) {
        return switch (errorType) {
            case ERROR_MESSAGE_UNSPECIFIED, UNRECOGNIZED, INVALID -> BrokerDeskConfigModel.ErrorType.INVALID;
            case MISSING -> BrokerDeskConfigModel.ErrorType.MISSING;
        };
    }
}
