package io.wyden.apiserver.rest.settlement.transaction;

import java.util.Collection;

public record SelectAllTransactionInput(

    String settlementId,
    boolean selected,
    Collection<String> symbol,
    Collection<String> currency,
    Collection<String> accountId,
    Collection<String> portfolioId,
    Collection<String> transactionType,
    String orderId,
    String parentOrderId,
    String rootOrderId,
    String underlyingExecutionId,
    String venueExecutionId,
    String executionId,
    String rootExecutionId,
//    from, to : Epoch Unix Timestamp
    String from,
    String to


) {
}