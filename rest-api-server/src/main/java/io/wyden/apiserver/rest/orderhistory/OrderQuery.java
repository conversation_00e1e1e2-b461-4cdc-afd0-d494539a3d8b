package io.wyden.apiserver.rest.orderhistory;

import jakarta.annotation.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.ZonedDateTime;
import java.util.Set;

public record OrderQuery(@Nullable String instrumentId,
                         @Nullable String orderId,
                         @Nullable String clOrderId,
                         @Nullable Set<String> portfolioIds,
                         @Nullable Set<String> venueAccountIds,
                         @Nullable ZonedDateTime fromTime,
                         @Nullable ZonedDateTime toTime,
                         int limit,
                         @Nullable Boolean isOpen) {

    public OrderQueryBuilder toBuilder() {
        return new OrderQueryBuilder()
            .setInstrumentId(instrumentId)
            .setOrderId(orderId)
            .setClOrderId(clOrderId)
            .setPortfolioIds(portfolioIds)
            .setVenueAccountIds(venueAccountIds)
            .setFromTime(fromTime)
            .setToTime(toTime)
            .setLimit(limit)
            .setOpen(isOpen);
    }

    public static class OrderQueryBuilder {

        private final Logger LOGGER = LoggerFactory.getLogger(OrderQueryBuilder.class);
        @Nullable
        String instrumentId;
        @Nullable
        String orderId;
        @Nullable
        String clOrderId;
        @Nullable
        ZonedDateTime fromTime;
        @Nullable
        ZonedDateTime toTime;
        @Nullable
        Integer limit;
        @Nullable
        Boolean isOpen;
        @Nullable
        Set<String> portfolioIds;
        @Nullable
        Set<String> venueAccountIds;

        public OrderQueryBuilder setInstrumentId(@Nullable String instrumentId) {
            this.instrumentId = instrumentId;
            return this;
        }

        public OrderQueryBuilder setOrderId(@Nullable String orderId) {
            this.orderId = orderId;
            return this;
        }

        public OrderQueryBuilder setClOrderId(@Nullable String clOrderId) {
            this.clOrderId = clOrderId;
            return this;
        }

        public OrderQueryBuilder setPortfolioIds(@Nullable Set<String> portfolioIds) {
            this.portfolioIds = portfolioIds;
            return this;
        }

        public OrderQueryBuilder setVenueAccountIds(@Nullable Set<String> venueAccountIds) {
            this.venueAccountIds = venueAccountIds;
            return this;
        }

        public OrderQueryBuilder setFromTime(@Nullable ZonedDateTime fromTime) {
            this.fromTime = fromTime;
            return this;
        }

        public OrderQueryBuilder setToTime(@Nullable ZonedDateTime toTime) {
            this.toTime = toTime;
            return this;
        }

        public OrderQueryBuilder setLimit(@Nullable Integer limit) {
            this.limit = limit;
            return this;
        }

        public OrderQueryBuilder setOpen(@Nullable Boolean open) {
            isOpen = open;
            return this;
        }

        public OrderQuery build() {
            if (limit == null) {
                LOGGER.warn("Limit not provided but mandatory. Defaulting to 1000 entries.");
                limit = 1000;
            }

            return new OrderQuery(
                instrumentId,
                orderId,
                clOrderId,
                portfolioIds,
                venueAccountIds,
                fromTime,
                toTime,
                limit,
                isOpen
            );
        }
    }
}

