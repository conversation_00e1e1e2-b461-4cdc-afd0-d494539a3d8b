package io.wyden.apiserver.rest.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.OAuthFlow;
import io.swagger.v3.oas.models.security.OAuthFlows;
import io.swagger.v3.oas.models.security.Scopes;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Configuration
@OpenAPIDefinition
public class SwaggerConfig {
    private static final String OPEN_ID_SCHEME_NAME = "openId";

    @Value("${openapi.server.url:}")
    String serverUrl;

    @Value("${spring.security.oauth2.client.provider.keycloak.issuer-uri}")
    String issuerUri;

    @Value("${keycloak.realm}")
    String realmName;

    @Value("${swagger.keycloak.host}")
    String swaggerKeycloakHost;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info())
            .components(new Components()
                .addSecuritySchemes(OPEN_ID_SCHEME_NAME, createOAuthScheme()))
            .addSecurityItem(new SecurityRequirement().addList(OPEN_ID_SCHEME_NAME))
            .servers(getServers());
    }

    private SecurityScheme createOAuthScheme() {
        OAuthFlows oauth = new OAuthFlows()
            .authorizationCode(new OAuthFlow()
                .authorizationUrl(swaggerKeycloakHost + "/realms/"+ realmName + "/protocol/openid-connect/auth")
                .tokenUrl(swaggerKeycloakHost + "/realms/"+ realmName + "/protocol/openid-connect/token")
                .scopes(new Scopes().addString("openid", "")));
        return new SecurityScheme()
            .type(SecurityScheme.Type.OAUTH2)
            .flows(oauth);
    }

    private List<Server> getServers() {
        if (isNotBlank(serverUrl)) {
            Server server = new Server();
            server.setUrl(serverUrl);
            return List.of(server);
        } else {
            return null;
        }
    }
}
