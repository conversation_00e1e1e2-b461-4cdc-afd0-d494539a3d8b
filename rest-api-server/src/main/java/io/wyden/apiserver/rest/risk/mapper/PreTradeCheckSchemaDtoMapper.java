package io.wyden.apiserver.rest.risk.mapper;

import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckPropertyFormat;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckPropertySchemaDto;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckPropertyType;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckSchemaDto;
import io.wyden.published.risk.PreTradeCheckPropertySchema;
import io.wyden.published.risk.PreTradeCheckSchema;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PreTradeCheckSchemaDtoMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(
        PreTradeCheckSchemaDtoMapper.class
    );

    public static PreTradeCheckSchemaDto map(PreTradeCheckSchema schema) {
        return new PreTradeCheckSchemaDto(
            schema.getType(),
            mapProperties(schema.getPropertiesMap())
        );
    }

    private static List<PreTradeCheckPropertySchemaDto> mapProperties(
        Map<String, PreTradeCheckPropertySchema> properties
    ) {
        List<PreTradeCheckPropertySchemaDto> list = new ArrayList<>();
        for (Map.Entry<
            String,
            PreTradeCheckPropertySchema
        > entry : properties.entrySet()) {
            PreTradeCheckPropertyType type = mapType(
                entry.getValue().getType()
            );
            if (type == null) {
                LOGGER.warn(
                    "Unable to bind schema type for property {} type {}, excluding property from schema",
                    entry.getKey(),
                    entry.getValue().getType()
                );
                continue;
            }

            PreTradeCheckPropertySchemaDto schema =
                new PreTradeCheckPropertySchemaDto(
                    entry.getKey(),
                    type,
                    entry.getValue().getRequired(),
                    entry.getValue().getEnumValuesCount() > 0,
                    entry.getValue().getEnumValuesCount() > 0
                        ? entry.getValue().getEnumValuesList()
                        : null,
                    mapFormat(entry.getValue().getFormat())
                );
            list.add(schema);
        }
        return list;
    }

    @Nullable
    private static PreTradeCheckPropertyType mapType(
        io.wyden.published.risk.PreTradeCheckPropertyType type
    ) {
        return switch (type) {
            case STRING -> PreTradeCheckPropertyType.STRING;
            case DECIMAL -> PreTradeCheckPropertyType.NUMBER;
            case STRING_ARRAY -> PreTradeCheckPropertyType.STRING_LIST;
            case UNRECOGNIZED, PTC_PROPERTY_TYPE_UNSPECIFIED -> null;
        };
    }

    @Nullable
    private static PreTradeCheckPropertyFormat mapFormat(
        io.wyden.published.risk.PreTradeCheckPropertyFormat format
    ) {
        return switch (format) {
            case CURRENCY_PAIR -> PreTradeCheckPropertyFormat.CURRENCY_PAIR;
            case CURRENCY -> PreTradeCheckPropertyFormat.CURRENCY;
            case UNRECOGNIZED, FORMAT_UNSPECIFIED -> null;
        };
    }
}
