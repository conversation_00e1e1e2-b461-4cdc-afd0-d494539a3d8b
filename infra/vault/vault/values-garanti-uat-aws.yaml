server:
  enabled: true
  updateStrategyType: "RollingUpdate"
  extraLabels:
    azure.workload.identity/use: "true"
  serviceAccount:
    extraLabels:
      azure.workload.identity/use: "true"

  ingress:
    enabled: true
    ingressClassName: "nginx"
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    hosts:
      - host: vault-garanti-uat-aws.wyden.io
        paths: []
    tls:
      - hosts:
          - vault-garanti-uat.wyden.io
        secretName: vault-garanti-uat-tls

  extraSecretEnvironmentVars:
    - envName: AWS_SECRET_ACCESS_KEY
      secretName: vault-aws-unseal
      secretKey: AWS_SECRET_ACCESS_KEY
    - envName: AWS_ACCESS_KEY_ID
      secretName: vault-aws-unseal
      secretKey: AWS_ACCESS_KEY_ID

  image:
    repository: ************.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/hashicorp/vault
    tag: 1.14.8

  resources:
    requests:
      memory: 256Mi
      cpu: 500m
    limits:
      memory: 512Mi
      cpu: 500m

  ha:
    enabled: false
  standalone:
    enabled: "-"
    config: |
      ui = true

      listener "tcp" {
        tls_disable = 1
        address = "[::]:8200"
        cluster_address = "[::]:8201"
      }

      seal "awskms" {
        region = "eu-central-1"
        kms_key_id = "3f6f59e0-85ab-46df-8696-d6e4e3f15f34"
      }

      storage "postgresql" {
        connection_url = "postgresql://wyden:2q%23uTkH!<EMAIL>:5432/vault"
        table = "vault_kv_store"
      }

  dataStorage:
    enabled: false

  #extraContainers:
  #  - name: init
  #    image: ************.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/hashicorp/vault:1.14.8
  #    command: ["/bin/sh", "-c"]
  #    args:
  #      - |
  #        export VAULT_CLIENT_TIMEOUT=120
  #        export VAULT_MAX_RETRIES=10
  #        export VAULT_ADDR='http://127.0.0.1:8200'
  #        OUTPUT=/tmp/output.txt
  #        echo "Checking status";
  #        if vault status -tls-skip-verify;
  #        then
  #          echo "Vault is already initialized!";
  #        else
  #          while ! nc -z localhost 8200;
  #          do
  #            echo "Waiting Vault to start";
  #            sleep 1;
  #          done;
  #          echo "Vault started but not initialised!";
  #          vault operator init > ${OUTPUT?};
  #          mkdir -p /vault/data
  #          cp ${OUTPUT?} /vault/data/init.txt
  #          while ! vault status -tls-skip-verify;
  #          do
  #            echo "Waiting Vault to initialized";
  #            sleep 1;
  #          done;
  #          echo "Fetching the token";
  #          root=$(cat ${OUTPUT?} | grep "Initial Root Token:" | sed -e "s/Initial Root Token: //g");
  #          echo "Login to the Valut";
  #          vault login -no-print ${root?};
  #          echo "Enable Secrets engine";
  #          vault secrets enable -path=secret -version=2 kv;
  #          echo "Enable Kubernetes engine";
  #          vault auth enable kubernetes;
  #          vault write auth/kubernetes/config kubernetes_host=https://$KUBERNETES_PORT_443_TCP_ADDR:443;
  #          echo "Create Kubernetes policy";
  #          wget https://algotraderfiles.blob.core.windows.net/public-files/wyden-vault-policy.hcl -O /tmp/policy.hcl
  #          cat /tmp/policy.hcl
  #          vault policy write wyden-all /tmp/policy.hcl
  #          echo "Create Wyden Kubernetes role";
  #          vault write auth/kubernetes/role/wyden \
  #              bound_service_account_names="*" \
  #              bound_service_account_namespaces="*" \
  #              token_policies=wyden-all \
  #              ttl=1h;
  #        fi
  #        echo "continues sleep"
  #        sleep infinity

  nodeSelector:
    environment: garantiuat
  tolerations:
    - key: "garantiuat"
      operator: "Equal"
      value: "true"
      effect: "NoSchedule"
    - key: "client"
      operator: "Equal"
      value: "garanti"
      effect: "NoSchedule"
injector:
  enabled: false
