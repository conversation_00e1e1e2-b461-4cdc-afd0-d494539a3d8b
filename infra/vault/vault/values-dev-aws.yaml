server:
  enabled: true
  updateStrategyType: "RollingUpdate"
  ingress:
    enabled: true
    ingressClassName: "nginx"
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    hosts:
      - host: vault-dev.wyden.io
        paths: []
    tls:
      - hosts:
          - vault-dev.wyden.io
        secretName: vault-dev-tls

  image:
    repository: ************.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/hashicorp/vault
    tag: 1.14.8

  resources:
    requests:
      memory: 256Mi
      cpu: 500m
    limits:
      memory: 512Mi
      cpu: 500m

  standalone:
    enabled: false
  ha:
    enabled: true
    replicas: 3
    config: |
      ui = true

      listener "tcp" {
        tls_disable = 1
        address = "[::]:8200"
        cluster_address = "[::]:8201"
      }

      disable_mlock = true

      seal "awskms" {
        region = "eu-central-1"
        kms_key_id = "725206f6-33dc-4a13-b180-25289dcccfbe"
      }

      service_registration "kubernetes" {}

      storage "raft" {
        path = "/vault/data"
      }

    raft:
      enabled: true
      setNodeId: true
      config: |
        ui = true
        disable_mlock = true

        listener "tcp" {
          tls_disable = 1
          address = "[::]:8200"
          cluster_address = "[::]:8201"
        }

        seal "awskms" {
          region = "eu-central-1"
          kms_key_id = "725206f6-33dc-4a13-b180-25289dcccfbe"
        }

        service_registration "kubernetes" {}

        storage "raft" {
          path = "/vault/data"
          retry_join {
            leader_api_addr = "http://{{ .Release.Name }}-active:8200"
          }
        }

  extraContainers:
    - name: init
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/hashicorp/vault:1.14.8
      volumeMounts:
        - mountPath: /vault/data
          name: data
      command: ["/bin/sh", "-c"]
      args:
        - |
          export VAULT_CLIENT_TIMEOUT=120
          export VAULT_MAX_RETRIES=10
          export VAULT_ADDR='http://127.0.0.1:8200'
          OUTPUT=/tmp/output.txt
          echo "Checking status";
          if vault status -tls-skip-verify;
          then
            echo "Vault is already initialized!";
          else
            podname=`hostname`;
            if [ "${podname: -1}" == "0" ];
            then
              while ! nc -z localhost 8200;
              do
                echo "Waiting Vault to start";
                sleep 1;
              done;
              echo "Vault started but not initialised!";
              vault operator init > ${OUTPUT?};
              cp ${OUTPUT?} /vault/data/init.txt
              while ! vault status -tls-skip-verify;
              do
                echo "Waiting Vault to initialized";
                sleep 1;
              done;
              echo "Fetching the token";
              root=$(cat ${OUTPUT?} | grep "Initial Root Token:" | sed -e "s/Initial Root Token: //g");
              echo "Login to the Valut";
              vault login -no-print ${root?};
              echo "Enable Secrets engine";
              vault secrets enable -path=secret -version=2 kv;
              echo "Enable Kubernetes engine";
              vault auth enable kubernetes;
              vault write auth/kubernetes/config kubernetes_host=https://$KUBERNETES_PORT_443_TCP_ADDR:443;
              echo "Create Kubernetes policy";
              wget https://algotraderfiles.blob.core.windows.net/public-files/wyden-vault-policy.hcl -O /tmp/policy.hcl
              cat /tmp/policy.hcl
              vault policy write wyden-all /tmp/policy.hcl
              echo "Create Wyden Kubernetes role";
              vault write auth/kubernetes/role/wyden \
                bound_service_account_names="*" \
                bound_service_account_namespaces="*" \
                token_policies=wyden-all \
                ttl=1h;
            fi
          fi
          echo "continues sleep"
          sleep infinity

  dataStorage:
    enabled: true
    size: 10Gi

  nodeSelector:
    environment: dev
  tolerations:
    - key: "dev"
      operator: "Equal"
      value: "true"
      effect: "NoSchedule"

  serviceAccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/vault
injector:
  enabled: false
