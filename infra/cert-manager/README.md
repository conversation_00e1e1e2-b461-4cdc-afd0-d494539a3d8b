# Add chart
```sh
helm repo add jetstack https://charts.jetstack.io
helm repo update
```
# Deploy chart
```sh
helm diff upgrade cert-manager jetstack/cert-manager --namespace cert-manager --version v1.10.1 -f infra/cert-manager/values-prod.yaml
```
```sh
helm upgrade -i cert-manager jetstack/cert-manager --namespace cert-manager --create-namespace  --version v1.10.1 -f infra/cert-manager/values-prod.yaml --atomic --timeout 5m
```

# Deploy chart AWS
```sh
helm diff upgrade cert-manager jetstack/cert-manager --namespace cert-manager --version v1.16.2 -f infra/cert-manager/values-prod.yaml
```
```sh
helm upgrade -i cert-manager jetstack/cert-manager --namespace cert-manager --create-namespace  --version v1.16.2 -f infra/cert-manager/values-prod-aws.yaml --atomic --timeout 5m
```