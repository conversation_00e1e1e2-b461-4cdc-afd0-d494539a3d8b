global:
  imageRegistry: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub

auth:
  adminUser: admin
  adminPassword: "Efjgas21izdd7ZReBC20T"

extraEnvVars:
  - name: PROXY_ADDRESS_FORWARDING
    value: "true"

replicaCount: 2

cache:
  enabled: false

ingress:
  enabled: true
  ingressClassName: nginx
  tls: true
  hostname: "keycloak-sopra-mvp.wyden.io"
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"

externalDatabase:
  host: sopra-sandbox.cluster-cx2cemkicqia.eu-central-1.rds.amazonaws.com
  user: wyden
  password: zOOk8!FtP5U3dyGz
  database: keycloak

nodeSelector:
  environment: sandbox
  client: sopra
tolerations:
  - key: "client"
    operator: "Equal"
    value: "sopra"
    effect: "NoSchedule"
  - key: "sandbox"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

resources:
  requests:
    cpu: 250m
    memory: 512Mi
  limits:
    cpu: 1000m
    memory: 2Gi
