auth:
  username: "whiterabbit"
  password: "Gb55HehqRPV8iPaXmg"
  erlangCookie: "W37DnTN5sq8iTRdb7gjsF4ECA6PP4mg7"

replicaCount: 3

clustering:
  rebalance: true

resources:
  requests:
    cpu: 1
    memory: 2Gi
  limits:
    cpu: 2
    memory: 2Gi

extraPlugins: "rabbitmq_consistent_hash_exchange"

extraConfiguration: |-
  prometheus.return_per_object_metrics = true

memoryHighWatermark:
  enabled: true
  type: "relative"
  value: 0.9

metrics:
  enabled: true
  serviceMonitor:
    enabled: true

loadDefinition:
  enabled: true
  existingSecret: load-definition

readinessProbe:
  enabled: true
  initialDelaySeconds: 120

nodeSelector:
  environment: dev
tolerations:
  - key: "dev"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
  - key: "kubernetes.azure.com/scalesetpriority"
    operator: "Equal"
    value: "spot"
    effect: "NoSchedule"

affinity: |
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: "{{ .Release.Name }}"
        topologyKey: kubernetes.io/hostname
