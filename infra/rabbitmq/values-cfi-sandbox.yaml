global:
  imageRegistry: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub

auth:
  username: "whiterabbit"
  password: "4D*o4maKUgj8"
  erlangCookie: "FjvDaiEYiPZgnSBM6sS2pp8SJDQhzMftztSDG7rP"

replicaCount: 1

clustering:
  enabled: false

resources:
  requests:
    cpu: 1
    memory: 2Gi
  limits:
    cpu: 2
    memory: 2Gi

extraPlugins: "rabbitmq_consistent_hash_exchange"

extraConfiguration: |-
  prometheus.return_per_object_metrics = true

memoryHighWatermark:
  enabled: true
  type: "relative"
  value: 0.9

metrics:
  enabled: true
  serviceMonitor:
    enabled: true

loadDefinition:
  enabled: true
  existingSecret: load-definition

readinessProbe:
  enabled: true
  initialDelaySeconds: 60

nodeSelector:
  environment: sandbox
  client: cfi
tolerations:
  - key: "client"
    operator: "Equal"
    value: "cfi"
    effect: "NoSchedule"
  - key: "sandbox"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
