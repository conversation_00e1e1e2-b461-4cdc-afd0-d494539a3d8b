package io.wyden.connector.rate;

import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.connector.rate.service.RateRepository;
import io.wyden.published.rate.Rate;
import io.wyden.published.rate.RateKey;
import io.wyden.rate.client.RatesCacheFacade;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Repository
public class CoinApiRateRepository implements RateRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(CoinApiRateRepository.class);

    private final RatesCacheFacade ratesCacheFacade;

    private final Map<RateKey, Rate> rateMap = new HashMap<>();

    public CoinApiRateRepository(Tracing otlTracing) {
        this.ratesCacheFacade = new RatesCacheFacade(rateMap, otlTracing);
    }

    public void save(String baseCurrency, String quoteCurrency, BigDecimal price) {
        LOGGER.trace("Updating rate: {} / {} = {}", baseCurrency, quoteCurrency, price);
        RateKey rateKey = buildRateKey(baseCurrency, quoteCurrency);
        Rate rate = buildRate(baseCurrency, quoteCurrency, price.toPlainString());
        rateMap.put(rateKey, rate);
    }

    public Map<RateKey, Rate> rates() {
        return rateMap;
    }

    @NotNull
    private static RateKey buildRateKey(String baseCurrency, String quoteCurrency) {
        return RateKey.newBuilder()
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .build();
    }

    @NotNull
    private static Rate buildRate(String baseCurrency, String quoteCurrency, String value) {
        return Rate.newBuilder()
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .setValue(value)
            .setTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .build();
    }

    @Override
    public Optional<Rate> find(String baseCurrency, String quoteCurrency) {
        return ratesCacheFacade.find(baseCurrency, quoteCurrency);
    }
}
