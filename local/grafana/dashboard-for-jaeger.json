{"annotations": {"list": []}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 2, "links": [], "liveNow": false, "panels": [{"datasource": "$datasource", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"bucketOffset": 0, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "targets": [{"datasource": "$datasource", "queryType": "search", "refId": "A", "service": "fix-api-server"}], "title": "Panel Title", "type": "histogram"}], "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": [{"current": {"text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "hide": 0, "label": null, "name": "datasource", "options": [], "query": "jaeger", "refresh": 1, "regex": "", "type": "datasource"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Tracing dashboard", "uid": "cUF2oBEVk", "version": 2, "weekStart": ""}