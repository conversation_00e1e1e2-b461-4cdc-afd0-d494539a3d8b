package io.wyden.risk.engine.domain.map;

import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.published.risk.PreTradeCheck;

import static io.wyden.cloudutils.hazelcast.Serializers.protobufSerializer;

public class PreTradeCheckMapConfig extends HazelcastMapConfig {
    private static final String MAP_NAME = "pre-trade-checks_0.1";

    public static IMap<String, PreTradeCheck> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
        serializationConfig.addSerializerConfig(protobufSerializer(PreTradeCheck.class, PreTradeCheck.parser()));
    }
}
