package io.wyden.apiserver.fix.infrastructure.hazelcast;

import com.hazelcast.client.HazelcastClient;
import com.hazelcast.client.config.ClientConfig;
import com.hazelcast.client.config.ClientNetworkConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.apiserver.fix.domain.FixDropCopySessionLockMapConfig;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.client.ReferenceDataProvider;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.referencedata.domain.InstrumentMapConfig;
import io.wyden.referencedata.domain.PortfolioMapConfig;
import io.wyden.referencedata.domain.VenueAccountMapConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

@Configuration
public class HazelcastConfig {

    @Bean
    IMap<String, String> fixSessionLockMap(HazelcastInstance hazelcastInstance) {
        return FixDropCopySessionLockMapConfig.getMap(hazelcastInstance);
    }

    @Bean
    FixDropCopySessionLockMapConfig fixDropCopySessionLockMapConfig() {
        return new FixDropCopySessionLockMapConfig();
    }

    @Bean
    ReferenceDataProvider referenceDataProvider() {
        return new ReferenceDataProvider();
    }

    @Bean
    public InstrumentsCacheFacade instrumentsCacheFacade(HazelcastInstance hazelcast,
                                                         ReferenceDataProvider referenceDataProvider,
                                                         Tracing otlTracing) {
        return referenceDataProvider.getInstrumentsCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public InstrumentMapConfig instrumentMapConfig() {
        return new InstrumentMapConfig();
    }

    @Bean
    public VenueAccountCacheFacade venueAccountCacheFacade(HazelcastInstance hazelcast,
                                                           ReferenceDataProvider referenceDataProvider,
                                                           Tracing otlTracing) {
        return referenceDataProvider.getVenueAccountCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public VenueAccountMapConfig venueAccountMapConfig() {
        return new VenueAccountMapConfig();
    }

    @Bean
    public PortfoliosCacheFacade portfoliosCacheFacade(HazelcastInstance hazelcast,
                                                       ReferenceDataProvider referenceDataProvider,
                                                       Tracing otlTracing) {
        return referenceDataProvider.getPortfoliosCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public PortfolioMapConfig portfolioMapConfig() {
        return new PortfolioMapConfig();
    }

    @Bean
    public ClientConfig createClientConfig(@Value("${hz.addressList}") String addressList,
                                           @Value("${hz.outboundPortDefinition}") String outboundPortDefinition,
                                           List<HazelcastMapConfig> hazelcastMaps) {

        ClientConfig clientConfig = new ClientConfig();
        clientConfig.getConnectionStrategyConfig().getConnectionRetryConfig().setMaxBackoffMillis(5000);

        ClientNetworkConfig networkConfig = clientConfig.getNetworkConfig();
        Arrays.stream(addressList.split(",")).forEach(networkConfig::addAddress);
        networkConfig.setSmartRouting(true);

        if (!outboundPortDefinition.isBlank()) {
            networkConfig.addOutboundPortDefinition(outboundPortDefinition);
        }

        networkConfig.setRedoOperation(true);
        networkConfig.setConnectionTimeout(5000);

        hazelcastMaps.forEach(m -> m.applyConfig(clientConfig));

        return clientConfig;
    }

    @Bean("hazelcast")
    public HazelcastInstance createHazelcastInstance(ClientConfig clientConfig, Telemetry telemetry, List<HazelcastMapConfig> hazelcastMaps) {
        HazelcastInstance hz = HazelcastClient.newHazelcastClient(clientConfig);
        hazelcastMaps.forEach(m -> m.setupClientInstance(hz));
        hazelcastMaps.forEach(m -> m.setupMetrics(hz, telemetry.getMeterRegistry()));
        return hz;
    }
}
