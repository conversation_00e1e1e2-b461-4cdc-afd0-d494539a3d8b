package io.wyden.quotingorderservice.service.oems.outbound;

import com.google.protobuf.Message;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.quotingorderservice.infrastructure.rabbit.RabbitDestinations;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.cloudutils.telemetry.tracing.otl.TracingConv;
import io.wyden.published.oems.OemsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
public class OemsResponseEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(OemsResponseEmitter.class);

    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;
    private final RabbitExchange<Message> tradingIngressExchange;

    public OemsResponseEmitter(Telemetry telemetry, RabbitExchange<Message> tradingIngressExchange) {
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
        this.tradingIngressExchange = tradingIngressExchange;
    }

    public CompletableFuture<Void> emit(OemsResponse response) {
        Map<String, String> baggage = Map.of(
            TracingConv.RESPONSE_ID, response.getMetadata().getResponseId(),
            TracingConv.REQUEST_ID, response.getMetadata().getRequestId()
        );
        try (var ignored = otlTracing.createBaggage(baggage)) {
            try (var ignored2 = otlTracing.createSpan("oemsresponse.emit", SpanKind.PRODUCER)) {
                updateMetrics(response);
                return emitInner(response);
            }
        }
    }

    private CompletableFuture<Void> emitInner(OemsResponse response) {
        RabbitExchange<Message> destination = tradingIngressExchange;

        Map<String, String> routingHeaders = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName(),
            OemsHeader.OEMS_RESPONSE_TYPE.getHeaderName(), response.getResponseType().toString(),
            OemsHeader.OEMS_EXEC_TYPE.getHeaderName(), response.getExecType().toString(),
            OemsHeader.INSTRUMENT_ID.getHeaderName(), response.getInstrumentId(),
            OemsHeader.SOURCE.getHeaderName(), response.getMetadata().getSource(),
            OemsHeader.SOURCE_TYPE.getHeaderName(), response.getMetadata().getSourceType().name(),
            OemsHeader.TARGET.getHeaderName(), response.getMetadata().getTarget(),
            OemsHeader.TARGET_TYPE.getHeaderName(), response.getMetadata().getTargetType().name()
        );
        Map<String, String> headers = otlTracing.saveContext(RabbitHeadersPropagator.create(routingHeaders), RabbitHeadersPropagator.setter())
            .getHeaders();

        LOGGER.info("Emitting {} to {}, headers: {}\n{}",
            response.getClass().getSimpleName(), destination.getName(), headers, response);

        return destination.publishWithHeaders(response, headers);
    }

    private void updateMetrics(OemsResponse oemsResponse) {
        try {
            String instrumentId = oemsResponse.getInstrumentId();
            String responseType = oemsResponse.getResponseType().name();
            String execType = oemsResponse.getExecType().name();
            this.meterRegistry.counter("wyden.response.count",
                "instrumentId", instrumentId,
                "venueType", "street",
                "responseType", responseType,
                "execType", execType).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
