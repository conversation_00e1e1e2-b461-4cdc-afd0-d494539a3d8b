# Refernce Data

## Overview

**Description:**
Reference data management: instruments, accounts, portfolios

**Owner/Team:**
- Team/Name: `Blue Team`

## Architecture

### High-Level Architecture

```plantuml
rectangle "OEMS Services" as oems
rectangle "Reference Data" as rf
rectangle "Storage" as st
rectangle "Connector Wrapper" as cw
rectangle "Client" as cl

rf <-u-> cl: REST: Currency, Instrument,\nVenueAccount, Portfolio
rf <-d-> st
rf <-l--> oems : async reference data\n requests/response
rf <-r-> cw : async reference data\n requests/response

```

### Data Flow

**Instrument flow:**
```mermaid
sequenceDiagram
participant OEMS-Service
participant RabbitMQ-Oems
participant Reference-Data
participant Storage
participant RabbitMQ-Venue
participant Connector-Wrapper

Note over OEMS-Service,Connector-Wrapper: Get instruments
OEMS-Service-->>RabbitMQ-Oems : GetInstrumentsRequest
RabbitMQ-Oems -->> Reference-Data : GetInstrumentsRequest
Reference-Data -->> RabbitMQ-Venue: GetInstrumentsRequest
RabbitMQ-Venue -->> Connector-Wrapper: GetInstrumentsRequest
Connector-Wrapper -->> RabbitMQ-Venue : StreetSideInstrumentListSnapshot
RabbitMQ-Venue -->> Reference-Data: StreetSideInstrumentListSnapshot
Reference-Data ->> Storage : persist instruments
Reference-Data -->> RabbitMQ-Oems : InstrumentChangeEvent(created/updated)

Note over OEMS-Service,Connector-Wrapper: Instrument modification
OEMS-Service-->>RabbitMQ-Oems : InstrumentModificationRequest
RabbitMQ-Oems -->> Reference-Data : InstrumentModificationRequest
Reference-Data ->> Storage : persist instrument
Reference-Data -->> RabbitMQ-Oems : InstrumentChangeEvent(created/updated)
```

**Portfolio flow:**
```mermaid
sequenceDiagram
participant OEMS-Service
participant RabbitMQ-Oems
participant Reference-Data
participant Storage
participant RabbitMQ-Venue
participant Connector-Wrapper

Note over OEMS-Service,Connector-Wrapper: Portfolio onboarding
OEMS-Service-->>RabbitMQ-Oems : PortfolioOnboardingRequest
RabbitMQ-Oems -->> Reference-Data : PortfolioOnboardingRequest
Reference-Data ->> Storage : persist portfolio
Reference-Data -->> RabbitMQ-Oems : PortfolioChangeEvent
Reference-Data -->> RabbitMQ-Oems : PortfolioOnboardingCreatedEvent

Note over OEMS-Service,Connector-Wrapper: Portfolio change
OEMS-Service-->>RabbitMQ-Oems : PortfolioChangeRequest
RabbitMQ-Oems -->> Reference-Data : PortfolioChangeRequest
Reference-Data ->> Storage : persist portfolio
Reference-Data -->> RabbitMQ-Oems : PortfolioChangeEvent

```

**Venue flow:**
```mermaid
sequenceDiagram
participant REST-Management
participant RabbitMQ
participant Reference-Data
participant Storage

REST-Management-->>RabbitMQ : VenueCreateRequest
RabbitMQ -->> Reference-Data : VenueCreateRequest
Reference-Data ->> Storage : persist venue
```

**VenueAccount flow:**
```mermaid
sequenceDiagram
participant Oems-Service
participant RabbitMQ-Oems
participant Reference-Data
participant Storage


Note over Oems-Service,Connector-Wrapper: VenueAccount onboarding
Oems-Service-->>RabbitMQ-Oems : AccountOnboardingRequest
RabbitMQ-Oems -->> Reference-Data : AccountOnboardingRequest
Reference-Data ->> Storage : persist VenueAccount
Reference-Data -->> RabbitMQ-Oems : VenueAccountChangeEvent
Reference-Data -->> RabbitMQ-Oems : ConnectorRequest

Note over Oems-Service,Connector-Wrapper: VenueAccount activate/deactivate
Oems-Service-->>RabbitMQ-Oems : VenueAccountActivateRequest / VenueAccountDeactivationRequest
RabbitMQ-Oems -->> Reference-Data : VenueAccountActivateRequest / VenueAccountDeactivationRequest
Reference-Data ->> Storage : persist VenueAccount
```

### Domain and API

Domain objects:
- `Instrument`
- `Portfolio`
- `VenueAccount`

## Functional Description

- **Primary Responsibilities:** Reference data management: instruments, accounts, portfolios
- **APIs Exposed:**
    - Reference Data Client - java client
    - Currency REST - get currencies, save currency
    - Instruments REST -  get instruments, refresh street side instrumets, delete e2e instrument
    - Portfolio REST - get portfolios, get portfolio tags, delete e2e portfolios 
    - VenueAccount REST - get / remove venue accounts, delete e2e venue accounts

- **Event-Driven Behavior:**
    - Consume: 
      - GetInstrumentsRequest, InstrumentModificationRequest
      - PortfolioChangeRequest
      - VenueCreateRequest,
      - AccountOnboardingRequest, VenueAccountActivateRequest, VenueAccountCreateRequest, VenueAccountDeactivationRequest
    - Emits: 
      - GetInstrumentsRequest, InstrumentChangeEvent, StreetSideInstrumentListSnapshot
      - PortfolioChangeEvent, PortfolioOnboardingCreatedEvent, PortfolioOnboardingRequest
      - AccountOnboardingCreatedEvent, VenueAccount, VenueAccountChangeEvent
- **Dependencies:**
    - Hard dependency on: Rabbit, Storage, Connector-Wrapper-\<NAME>
    - Upstream: Access Gateway, Booking Engine, FIX API trading, FIX API Market Data, FIX API Custom OHLC, REAST API Server, 
    REST Management, Market Data Manage, Target Registry 

## Technical Description

- **Service Ports:**
    - `HTTP: 8098`

- **Configuration:**
    - Instrument refreshing:
      ```
      instruments.street.refresh.enabled=true
      instruments.street.refresh.cron=0 0 6 * * *
      ```
    - Currency pair priority:
      ```
      currency-pair.priority[0].symbol=USDCHF
      currency-pair.priority[0].base-currency=USD
      currency-pair.priority[0].quote-currency=CHF
      currency-pair.priority[1].symbol=USDCAD
      currency-pair.priority[1].base-currency=USD
      currency-pair.priority[1].quote-currency=CAD
      ```
    - Prefills:
      ```
      initial.config.instrument.prefill = false
      initial.config.portfolio.prefill = false
      initial.config.venueaccount.prefill = false
      initial.config.venue.client.prefill = true
      initial.config.venue.client.names = {'Bank'}
      initial.config.venue.street.prefill = true
      initial.config.venue.street.names = {'Generic','B2C2','Binance Global' ... }
      initial.config.currency.prefill = true
      initial.config.currency=BTC,CRYPTO,8,8;\
          ETH,CRYPTO,18,18; \
          USDT,CRYPTO,6,6; \
          BNB,CRYPTO,8,8; \
          ...
      ```

## Service Dashboard

- **Dashboard URL:**
    `NONE`

## Deployment & Operations

### HA/DR Mode
`HA` - Service is Highly Available. Supports Multiple Replicas. Traffic is shared between replicas with Round Robin.

### Resource Requests & Limits
| Resource  | Request  | Limit    |
|-----------|----------|----------|
| CPU       | `100m`  | `250m`   |
| Memory    | `1024Mi` | `1024Mi` |

### Deployment Notes
- **Helm Chart:** `<HELM_CHART_NAME>`
- **Kubernetes Namespace:** `<NAMESPACE>`
- **Secrets Required:** `NONE`
- **Persistent Volume Claims (PVCs):** `NONE`
- **Rolling Update Strategy:** `Rollig update`

## Building the Service

### Prerequisites
- Install `Gradle`
- Clone the repository: `ssh://*******************:2222/atcloud/reference-data.git`

### Build Commands
- Build the application: `/gradlew assemble`
- Run tests: `/gradlew check`
- Create Docker image: `docker build -t reference-data .`

## Monitoring & Logging

- **Metrics:**
    - Prometheus Endpoint: `<METRICS_ENDPOINT>`
    - Key Metrics: `<CPU Usage, Latency>`

- **Logging:**
    - Log Format: `<JSON/Plain>`
    - Log Storage: `<ELK/Splunk/Loki>`
    - Log Levels: `<DEBUG/INFO/WARN/ERROR>`

## Troubleshooting & FAQs

### Common/known Issues

