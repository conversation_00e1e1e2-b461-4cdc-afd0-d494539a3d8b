package io.wyden.accessgateway.domain.license;

import java.util.List;

public class LicenseState {

    /**
     * License key is set by the client in properties, for example: F39389-5F2133-71B1CA-C5AE83-A8846E-V3
     */
    private final String licenseKey;

    /**
     * License id is matched by ResourceManager, for example: fa194dd5-01e3-404a-bd41-4e815840d999
     * It might be null, if licenseKey is incorrect or ResourceManager cannot map licenseKey to licenseId.
     * License id is required to make calls to LicenseServer.
     */
    private String licenseId;

    private LicenseStatus status;

    private List<String> entitlements;

    public LicenseState(String licenseKey) {
        this.licenseKey = licenseKey;
    }

    public LicenseStatus getStatus() {
        return status;
    }

    public void setStatus(LicenseStatus status) {
        this.status = status;
    }

    public String getLicenseId() {
        return licenseId;
    }

    public LicenseState setLicenseId(String licenseId) {
        this.licenseId = licenseId;
        return this;
    }

    public String getLicenseKey() {
        return licenseKey;
    }

    public void setEntitlements(List<String> entitlements) {
        this.entitlements = entitlements;
    }

    public List<String> getEntitlements() {
        return entitlements;
    }

    public enum LicenseStatus {
        VALID, NOT_VALID
    }
}
