# Process run: 2025.8.04, Release 2025.6.0
# Code Freeze date: 2025.08.28
# Planned release date: 2025.09.04

| Team         | Dev Capacity | QA Capacity | Requested Tech Debt |
|--------------|--------------|-------------|---------------------|
| UI           | 70.0 MD      | -           | -                   |
| Blue         | 22.8 MD      | 10.5 MD      | -                   |
| Red          | 18.7 MD      | 12.6 MD      | -                   |
| Yellow       | 18.9 MD      | 10.5 MD      | -                   |
| **Total BE** | **60.4 MD**  | -           | -                   |
| **Total FE** | **70.0 MD**  | -           | -                   |
| **Total QA** | -            | **33.6 MD** | -                   |

## Decision Records
**Release Master**
Ka<PERSON><PERSON>

**Sprint Goals**
Tracked here: https://home.atlassian.com/o/************************6dde9dfacc10/goals?viewUuid=05ff0331-5b54-4713-b3b1-ae38cc229550&cloudId=939c2b60-3add-49f7-b6be-ed11869ac5f9
25.6:25.7: Ripple Connector
25.6: Readme's progress
25.6: Important for BPCE
25.6: High Availability part 4/5
25.6: Finish and test order history refactor
25.6: Custody DEMO (UI & US12 US7 US8 US9 tickets)
25.6: BPCE Settlements & New Settlements Integration
25.5:25.6: Booking - settlements integration
25.5:25.6: Rest Performance Improvements
25.5:25.6: Booking Split (query path and others)
25.6: Wygen UI Agent
25.6: UI/UX Improvements
25.6: Better Reconciliation POC (a.k.a. "Needs attention orders")