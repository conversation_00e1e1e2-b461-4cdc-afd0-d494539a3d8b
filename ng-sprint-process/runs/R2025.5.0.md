# Process run: 2025.7.01, Release 2025.5.0
# Code Freeze date: 2025.07.15
# Planned release date: 2025.07.22
# Real relase date: 2025.08.04

| Team         | Dev Capacity | QA Capacity | Requested Tech Debt |
|--------------|--------------|-------------|---------------------|
| UI           | 23.0 MD      | -           | -                   |
| Blue         | 21.0 MD      | 7.0 MD      | -                   |
| Red          | 16.0 MD      | 6.0 MD      | -                   |
| Yellow       | 14.0 MD      | 6.0 MD      | -                   |
| **Total BE** | **51.0 MD**  | -           | -                   |
| **Total FE** | **23.0 MD**  | -           | -                   |
| **Total QA** | -            | **20.0 MD** | -                   |

## Decision Records
**Release Master**
Dzia<PERSON>

**Sprint Goals**
- Custody Integration - (AC-6805, AC-6806, AC-6807,…)
- Settlement Engine - UI improvements after initial feedback
- BPCE - New fee type (AC-5344) - SPILLOVER
- Garanti - 2025.3 hotfix, including AC-6241
- Booking split query path
- High Availability - Part (3/4)
- Technical Documentation (a.k.a. Read.me's)
- BPCE - REST performance improvement (AC-5834) - SPILLOVER
- Trading Constrains validation (AC-6071) - SPILLOVER

- OPTIONAL: BSDEX (Support team) - provided the SOW is signed (AC-5674) 


**Retrospective**
- It's not good when there's a dependency between Hotfix and Relase version - that's why this release slipped so much
- We should apply patches into all version between patched version and current version in development
- We could catch up in 2025.6 sprint in updating above (i.e. patch 25.4 with all that has been patched in 25.3) BUT we will skip this here, but plan for it for consecutive hotfixes, but Marek Zganiacz should make sure that project managers confirm.
- Train ourselves on patch environment, we could not use it this time. 