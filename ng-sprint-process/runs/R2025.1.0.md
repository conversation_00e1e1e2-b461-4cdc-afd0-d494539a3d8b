# Process run: 2025.03.17, Release 2025.1.0
# Code Freeze date: 2025.04.01
# Planned release date: 2025.04.09

| Team         | Dev Capacity | QA Capacity | Requested Tech Debt |
|--------------|--------------|-------------|---------------------|
| UI           | 20.0 MD      | -           | -                   |
| Blue         | 14.7 MD      | 4.9 MD      | -                   |
| Red          | 15.0 MD      | 7.0 MD      | -                   |
| Yellow       | 21.0 MD      | 5.0 MD      | -                   |
| **Total BE** | **51.0 MD**  | -           | -                   |
| **Total FE** | **20.0 MD**  | -           | -                   |
| **Total QA** | -            | **17.0 MD** | -                   |

## Decision Records

**Release Master**
Sultan Al-Jana<PERSON>

**Sprint Goals**
* Settlement Engine - New microservice, Dashboard & Schema
* Garanti - Advanced Calendar Quoting
* Static portfolio permissions - Nostro vs. Vostro - Initial work for wallets
* Add CoinAPI as source in the rate service
* Service matrix
* Service dashboards (CLOB)
* Aeron and CLOB vs. Hedging improvements 
* Booking Split (blocks Settlement Engine)
* *Support for OTC Orderbooks in SOR*

## Retrospective
* Lacking specifications for:
  * SOR/OTC
  * Vostro/nostro wallets
  * Vostro/nostro portfolios
* Too late "in-bo/in-test" status (sometime on the deadline)
  * Business owners are lacking knowledge to validate them properly
  * We should have clear definition, which ticket should go to in BO status (for example exclude technical tickets)
* We still have problems with estimating sprints
  * QA Activity is not planned / estimated
* During workshops people are not available
  * Communicate better before workshops / plan capacity accordingly
