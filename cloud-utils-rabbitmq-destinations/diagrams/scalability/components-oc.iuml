' OC:

    component "OrderCollider" as oc_consumer {
        portout "task consumer" as Consumer_OC
    }

    queue VenueOrderStateMachineQueue#line:red [
        ===Venue Order State Machine Queue
        ===
        Single Active Consumer
        ===
        ====Header bindings:
        1) type=Oems.REQUEST && venueOrderId=$venueOrderId
        2) type=Venue.RESPONSE && venueOrderId=$venueOrderId
        ---
        ====SAC queue:
        * 1 queue per venueOrderId
        * multiple consumers allowed, but only one can be active
        * two bindings - has to receive all messages that concern the Venue Order
        (oems requests and venue responses that affect the Order)
    ]

    component "OrderCollider" as oc_registrar {
        portin "registration consumer" as Registrar_OC
        card registration_logic_oc [
            - declare Qualified queue
            - refresh / become a consumer (SAC)
            ]
    }

    component "OrderCollider" as oc_resender {
        portin "discover consumer" as Discover_OC
        card resend_logic_oc [
            - declare Qualified queue
            - send msg to that queue
            - bind that queue with Trading State machines exchange
            - publish Open and Close messages to Announce Broadcast Exchange
        ]
        portout "announcer" as Announcer_OC
        portout "resender" as <PERSON>sen<PERSON>_OC
    }
