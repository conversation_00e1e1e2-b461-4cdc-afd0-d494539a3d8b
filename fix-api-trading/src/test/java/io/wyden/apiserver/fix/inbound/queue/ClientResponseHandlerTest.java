package io.wyden.apiserver.fix.inbound.queue;

import com.rabbitmq.client.AMQP;
import io.wyden.apiserver.fix.common.fix.FixSessionWrapper;
import io.wyden.apiserver.fix.inbound.registry.ClientRequestRegistry;
import io.wyden.apiserver.fix.inbound.registry.ClientToOrderIdRegistry;
import io.wyden.cloud.utils.test.TelemetryMock;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientResponseType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static org.junit.jupiter.params.provider.EnumSource.Mode.EXCLUDE;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

class ClientResponseHandlerTest {

    private static final int TTL_SEC = 1;
    private final String CLIENT_ID = "GEORGE";
    private final String CLIENT_ORDER_ID = "GEORGIES_ORDER_1";
    private final String INSTRUMENT_ID = "BTCUSD@FOREX@Bitmex";

    private ClientResponseHandler consumer;

    private ClientToOrderIdRegistry registry;

    @BeforeEach
    void setUp() {
        registry = mock(ClientToOrderIdRegistry.class);
        ClientRequestRegistry clientRequestRegistry = mock(ClientRequestRegistry.class);
        Telemetry telemetryMock = TelemetryMock.createMock();
        FixSessionWrapper fixSession = mock(FixSessionWrapper.class);
        consumer = new ClientResponseHandler(fixSession, registry, clientRequestRegistry, telemetryMock, TTL_SEC);
    }

    @ParameterizedTest(name = "closedOrderShouldBeMarkedForLaterEviction({0})")
    @EnumSource(names = {"FILLED", "CANCELED", "REPLACED", "REJECTED", "EXPIRED"})
    void closedOrderShouldBeMarkedForLaterEviction(ClientOrderStatus clientOrderStatus) {
        // given
        ClientResponse msg = ClientResponse.newBuilder()
            .setResponseType(ClientResponseType.EXECUTION_REPORT)
            .setExecType(ClientExecType.CLIENT_EXEC_TYPE_FILL)
            .setClientId(CLIENT_ID)
            .setClOrderId(CLIENT_ORDER_ID)
            .setInstrumentId(INSTRUMENT_ID)
            .setOrderStatus(clientOrderStatus)
            .build();

        // when
        consumer.consume(msg, mock(AMQP.BasicProperties.class));

        // then
        verify(registry).markForEviction(CLIENT_ID, CLIENT_ORDER_ID, TTL_SEC);
    }

    @ParameterizedTest(name = "openOrderShouldNotBeMarkedForLaterEviction({0})")
    @EnumSource(names = {"FILLED", "CANCELED", "REPLACED", "REJECTED", "EXPIRED",
        "UNRECOGNIZED", "ORDER_STATUS_UNSPECIFIED"}, // exclude not supported or unknown types
                mode = EXCLUDE)
    void openOrderShouldNotBeMarkedForLaterEviction(ClientOrderStatus clientOrderStatus) {
        // given
        ClientResponse msg = ClientResponse.newBuilder()
            .setResponseType(ClientResponseType.EXECUTION_REPORT)
            .setExecType(ClientExecType.CLIENT_EXEC_TYPE_NEW)
            .setClientId(CLIENT_ID)
            .setClOrderId(CLIENT_ORDER_ID)
            .setOrderStatus(clientOrderStatus)
            .build();

        // when
        consumer.consume(msg, mock(AMQP.BasicProperties.class));

        // then
        verify(registry, never()).markForEviction(CLIENT_ID, CLIENT_ORDER_ID, TTL_SEC);
    }

}
