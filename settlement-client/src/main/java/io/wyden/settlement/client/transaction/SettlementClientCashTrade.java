
package io.wyden.settlement.client.transaction;

import java.math.BigDecimal;

public class SettlementClientCashTrade extends AbstractSettlementCashTrade {
    protected String counterPortfolio;
    protected String counterPortfolioName;

    public String getCounterPortfolio() { return counterPortfolio; }
    public void setCounterPortfolio(String counterPortfolio) { this.counterPortfolio = counterPortfolio; }

    public String getCounterPortfolioName() { return counterPortfolioName; }
    public void setCounterPortfolioName(String counterPortfolioName) { this.counterPortfolioName = counterPortfolioName; }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private final SettlementClientCashTrade instance = new SettlementClientCashTrade();

        public Builder id(String val) { instance.setId(val); return this; }
        public Builder selected(Boolean val) { instance.setSelected(val); return this; }
        public Builder dateTime(String val) { instance.setDateTime(val); return this; }
        public Builder uuid(String val) { instance.setUuid(val); return this; }
        public Builder executionId(String val) { instance.setExecutionId(val); return this; }
        public Builder venueExecutionId(String val) { instance.setVenueExecutionId(val); return this; }
        public Builder description(String val) { instance.setDescription(val); return this; }
        public Builder quantity(float val) { instance.setQuantity(val); return this; }
        public Builder price(float val) { instance.setPrice(val); return this; }
        public Builder currency(String val) { instance.setCurrency(val); return this; }
        public Builder intOrderId(String val) { instance.setIntOrderId(val); return this; }
        public Builder extOrderId(String val) { instance.setExtOrderId(val); return this; }
        public Builder settled(String val) { instance.setSettled(val); return this; }
        public Builder settlementRunId(String val) { instance.setSettlementRunId(val); return this; }
        public Builder settledDateTime(String val) { instance.setSettledDateTime(val); return this; }
        public Builder orderId(String val) { instance.setOrderId(val); return this; }
        public Builder portfolioId(String val) { instance.setPortfolioId(val); return this; }
        public Builder portfolioName(String val) { instance.setPortfolioName(val); return this; }
        public Builder baseCurrency(String val) { instance.setBaseCurrency(val); return this; }
        public Builder counterPortfolio(String val) { instance.setCounterPortfolio(val); return this; }
        public Builder counterPortfolioName(String val) { instance.setCounterPortfolioName(val); return this; }
        public Builder rootExecutionId(String val) { instance.setRootExecutionId(val); return this; }
        public Builder rootOrderId(String val) { instance.setRootOrderId(val); return this; }
        public Builder underlyingExecutionId(String val) { instance.setUnderlyingExecutionId(val); return this; }
        public Builder parentOrderId(String val) { instance.setParentOrderId(val); return this; }
        public Builder feeAmount(BigDecimal val) { instance.setFeeAmount(val); return this; }
        public Builder feeCurrency(String val) { instance.setFeeCurrency(val); return this; }

        public SettlementClientCashTrade build() {
            return instance;
        }
    }
}
