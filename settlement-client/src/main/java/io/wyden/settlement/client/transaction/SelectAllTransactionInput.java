package io.wyden.settlement.client.transaction;

import java.util.List;

public record SelectAllTransactionInput(
    String settlementRunId,
    SelectAllTransactionFiltersInput filters
) {
    public record SelectAllTransactionFiltersInput(
        boolean selected,
        List<String> currency,
        List<String> accountId,
        List<String> portfolioId,
        String orderId,
        String parentOrderId,
        String rootOrderId,
        String underlyingExecutionId,
        String venueExecutionId,
        String executionId,
        String rootExecutionId,
        //    from, to : Epoch Unix Timestamp
        String from,
        String to) {
    }
}