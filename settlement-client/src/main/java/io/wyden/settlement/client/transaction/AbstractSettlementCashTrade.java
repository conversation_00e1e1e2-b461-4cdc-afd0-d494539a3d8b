
package io.wyden.settlement.client.transaction;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.math.BigDecimal;

public abstract class AbstractSettlementCashTrade implements Serializable {
    @JsonIgnore protected String id;
    protected Boolean selected;
    protected String dateTime;
    protected String uuid;
    protected String executionId;
    protected String venueExecutionId;
    protected String description;
    protected float quantity;
    protected float price;
    protected String currency;
    protected String intOrderId;
    protected String extOrderId;
    protected String settled;
    @JsonIgnore protected String settlementRunId;
    protected String settledDateTime;
    protected String orderId;
    protected String portfolioId;
    protected String portfolioName;
    protected String baseCurrency;
    protected String rootExecutionId;
    protected String rootOrderId;
    protected String underlyingExecutionId;
    protected String parentOrderId;
    protected BigDecimal feeAmount;
    protected String feeCurrency;

    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public Boolean getSelected() { return selected; }
    public void setSelected(Boolean selected) { this.selected = selected; }

    public String getDateTime() { return dateTime; }
    public void setDateTime(String dateTime) { this.dateTime = dateTime; }

    public String getUuid() { return uuid; }
    public void setUuid(String uuid) { this.uuid = uuid; }

    public String getExecutionId() { return executionId; }
    public void setExecutionId(String executionId) { this.executionId = executionId; }

    public String getVenueExecutionId() { return venueExecutionId; }
    public void setVenueExecutionId(String venueExecutionId) { this.venueExecutionId = venueExecutionId; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public float getQuantity() { return quantity; }
    public void setQuantity(float quantity) { this.quantity = quantity; }

    public float getPrice() { return price; }
    public void setPrice(float price) { this.price = price; }

    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }

    public String getIntOrderId() { return intOrderId; }
    public void setIntOrderId(String intOrderId) { this.intOrderId = intOrderId; }

    public String getExtOrderId() { return extOrderId; }
    public void setExtOrderId(String extOrderId) { this.extOrderId = extOrderId; }

    public String getSettled() { return settled; }
    public void setSettled(String settled) { this.settled = settled; }

    public String getSettlementRunId() { return settlementRunId; }
    public void setSettlementRunId(String settlementRunId) { this.settlementRunId = settlementRunId; }

    public String getSettledDateTime() { return settledDateTime; }
    public void setSettledDateTime(String settledDateTime) { this.settledDateTime = settledDateTime; }

    public String getOrderId() { return orderId; }
    public void setOrderId(String orderId) { this.orderId = orderId; }

    public String getPortfolioId() { return portfolioId; }
    public void setPortfolioId(String portfolioId) { this.portfolioId = portfolioId; }

    public String getPortfolioName() { return portfolioName; }
    public void setPortfolioName(String portfolioName) { this.portfolioName = portfolioName; }

    public String getBaseCurrency() { return baseCurrency; }
    public void setBaseCurrency(String baseCurrency) { this.baseCurrency = baseCurrency; }

    public String getRootExecutionId() { return rootExecutionId; }
    public void setRootExecutionId(String rootExecutionId) { this.rootExecutionId = rootExecutionId; }

    public String getRootOrderId() { return rootOrderId; }
    public void setRootOrderId(String rootOrderId) { this.rootOrderId = rootOrderId; }

    public String getUnderlyingExecutionId() { return underlyingExecutionId; }
    public void setUnderlyingExecutionId(String underlyingExecutionId) { this.underlyingExecutionId = underlyingExecutionId; }

    public String getParentOrderId() { return parentOrderId; }
    public void setParentOrderId(String parentOrderId) { this.parentOrderId = parentOrderId; }

    public BigDecimal getFeeAmount() { return feeAmount; }
    public void setFeeAmount(BigDecimal feeAmount) { this.feeAmount = feeAmount; }

    public String getFeeCurrency() { return feeCurrency; }
    public void setFeeCurrency(String feeCurrency) { this.feeCurrency = feeCurrency; }
}
