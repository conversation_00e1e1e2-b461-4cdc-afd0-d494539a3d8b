
package io.wyden.settlement.client.transaction;

import java.math.BigDecimal;

public class SettlementStreetCashTrade extends AbstractSettlementCashTrade {
    protected String venueAccount;
    protected String venueAccountName;

    public String getVenueAccount() { return venueAccount; }
    public void setVenueAccount(String venueAccount) { this.venueAccount = venueAccount; }

    public String getVenueAccountName() { return venueAccountName; }
    public void setVenueAccountName(String venueAccountName) { this.venueAccountName = venueAccountName; }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private final SettlementStreetCashTrade instance = new SettlementStreetCashTrade();

        public Builder id(String val) { instance.setId(val); return this; }
        public Builder selected(Boolean val) { instance.setSelected(val); return this; }
        public Builder dateTime(String val) { instance.setDateTime(val); return this; }
        public Builder uuid(String val) { instance.setUuid(val); return this; }
        public Builder executionId(String val) { instance.setExecutionId(val); return this; }
        public Builder venueExecutionId(String val) { instance.setVenueExecutionId(val); return this; }
        public Builder description(String val) { instance.setDescription(val); return this; }
        public Builder quantity(float val) { instance.setQuantity(val); return this; }
        public Builder price(float val) { instance.setPrice(val); return this; }
        public Builder currency(String val) { instance.setCurrency(val); return this; }
        public Builder intOrderId(String val) { instance.setIntOrderId(val); return this; }
        public Builder extOrderId(String val) { instance.setExtOrderId(val); return this; }
        public Builder settled(String val) { instance.setSettled(val); return this; }
        public Builder settlementRunId(String val) { instance.setSettlementRunId(val); return this; }
        public Builder settledDateTime(String val) { instance.setSettledDateTime(val); return this; }
        public Builder orderId(String val) { instance.setOrderId(val); return this; }
        public Builder portfolioId(String val) { instance.setPortfolioId(val); return this; }
        public Builder portfolioName(String val) { instance.setPortfolioName(val); return this; }
        public Builder baseCurrency(String val) { instance.setBaseCurrency(val); return this; }
        public Builder venueAccount(String val) { instance.setVenueAccount(val); return this; }
        public Builder venueAccountName(String val) { instance.setVenueAccountName(val); return this; }
        public Builder rootExecutionId(String val) { instance.setRootExecutionId(val); return this; }
        public Builder rootOrderId(String val) { instance.setRootOrderId(val); return this; }
        public Builder underlyingExecutionId(String val) { instance.setUnderlyingExecutionId(val); return this; }
        public Builder parentOrderId(String val) { instance.setParentOrderId(val); return this; }
        public Builder feeAmount(BigDecimal val) { instance.setFeeAmount(val); return this; }
        public Builder feeCurrency(String val) { instance.setFeeCurrency(val); return this; }

        public SettlementStreetCashTrade build() {
            return instance;
        }
    }
}
