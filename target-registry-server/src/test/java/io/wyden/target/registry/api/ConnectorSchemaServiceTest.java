package io.wyden.target.registry.api;

import io.wyden.published.targetregistry.ConnectorTemplateField;
import io.wyden.target.registry.connectorschema.ConnectorSchemaService;
import io.wyden.target.registry.connectorschema.VenueDataNotFoundException;
import org.junit.jupiter.api.Test;

import java.util.Collection;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ConnectorSchemaServiceTest {

    @Test
    void testGetConnectorSchema_ValidVenue() {
        Collection<ConnectorTemplateField> schema = ConnectorSchemaService.getConnectorTemplate("Binance Global");

        assertNotNull(schema);
        assertThat(schema)
            .extracting(ConnectorTemplateField::getPropertyKey)
            .contains("binance.restUrl", "binance.apiSecret");

        ConnectorTemplateField restUrlConfig = findField(schema, "binance.restUrl");
        assertEquals("REST URL", restUrlConfig.getDescription());
        assertEquals("string", restUrlConfig.getType());
        assertEquals("https://api.binance.com", restUrlConfig.getDefaultValue());
        assertFalse(restUrlConfig.getSecret());
        assertTrue(restUrlConfig.getRequired());
    }

    @Test
    void testGetConnectorSchema_InvalidVenue() {
        Exception exception = assertThrows(VenueDataNotFoundException.class, () ->
            ConnectorSchemaService.getConnectorTemplate("invalidVenue"));

        assertEquals("Venue data not found: invalidVenue", exception.getMessage());
    }

    @Test
    void testFillVenueConfig_OptionalAndSecretFields() {
        Collection<ConnectorTemplateField> schema = ConnectorSchemaService.getConnectorTemplate("Binance Global");

        ConnectorTemplateField apiSecretConfig = findField(schema, "binance.apiSecret");
        assertNotNull(apiSecretConfig);
        assertTrue(apiSecretConfig.getSecret());
        assertTrue(apiSecretConfig.getRequired());

        ConnectorTemplateField webSocketTimeoutConfig =  findField(schema, "binance.webSocketTimeoutSeconds");
        assertNotNull(webSocketTimeoutConfig);
        assertEquals("Seconds between heartbeats", webSocketTimeoutConfig.getDescription());
        assertEquals("number", webSocketTimeoutConfig.getType());
        assertEquals("60", webSocketTimeoutConfig.getDefaultValue());
        assertFalse(webSocketTimeoutConfig.getRequired());
    }

    @Test
    void testFillVenueConfig_DefaultFieldConfigurations() {
        Collection<ConnectorTemplateField> schema = ConnectorSchemaService.getConnectorTemplate("Binance Global");

        ConnectorTemplateField defaultTIFMarketConfig = findField(schema, "binance.defaultTIFMarket");
        assertNotNull(defaultTIFMarketConfig);
        assertEquals("Default TIF for Market orders", defaultTIFMarketConfig.getDescription());
        assertEquals("string", defaultTIFMarketConfig.getType());
        assertFalse(defaultTIFMarketConfig.getSecret());
        assertTrue(defaultTIFMarketConfig.getRequired());
        assertEquals(7, defaultTIFMarketConfig.getEnumValuesCount());
    }

    @Test
    void testFillVenueConfig_HandlesConfigDefault() {
        Collection<ConnectorTemplateField> schema = ConnectorSchemaService.getConnectorTemplate("Binance Global");

        ConnectorTemplateField defaultTIFStopLimit = findField(schema, "binance.defaultTIFStopLimit");
        assertNotNull(defaultTIFStopLimit);
        assertEquals("Default TIF for Stop Limit orders", defaultTIFStopLimit.getDescription());
        assertEquals("string", defaultTIFStopLimit.getType());
        assertFalse(defaultTIFStopLimit.getSecret());
        assertTrue(defaultTIFStopLimit.getRequired());
    }

    private static ConnectorTemplateField findField(Collection<ConnectorTemplateField> schema, String fieldName) {
        return schema.stream().filter(p -> p.getPropertyKey().equalsIgnoreCase(fieldName)).findFirst().get();
    }
}