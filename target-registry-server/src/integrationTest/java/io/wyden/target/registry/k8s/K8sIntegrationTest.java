package io.wyden.target.registry.k8s;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.test.TestHazelcastInstanceFactory;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.util.Config;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.published.common.KeyValue;
import io.wyden.published.common.Metadata;
import io.wyden.published.diagnostic.Capability;
import io.wyden.published.diagnostic.Health;
import io.wyden.published.diagnostic.HealthStatus;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.VenueAccountActivateRequest;
import io.wyden.published.referencedata.VenueAccountDeactivationRequest;
import io.wyden.published.targetregistry.ConnectorRedeployRequest;
import io.wyden.published.targetregistry.ConnectorRequest;
import io.wyden.published.targetregistry.ConnectorState;
import io.wyden.target.registry.connectorstate.ConnectorStateCache;
import io.wyden.target.registry.diagnosticevents.DiagnosticEventsService;
import io.wyden.target.registry.infra.rabbit.TargetMessageConsumer;
import io.wyden.target.registry.statemachine.TargetStateContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.output.Slf4jLogConsumer;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.k3s.K3sContainer;
import org.testcontainers.shaded.org.awaitility.Awaitility;
import org.testcontainers.utility.DockerImageName;
import org.testcontainers.vault.VaultContainer;

import java.io.File;
import java.io.IOException;
import java.io.StringReader;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static io.wyden.published.diagnostic.HealthStatus.ALIVE;

@Testcontainers
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {
    K8sIntegrationTest.K8sApiClient.class,
    K8sIntegrationTest.TestHazelcastConfiguration.class
})
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
                properties = {"spring.main.allow-bean-definition-overriding=true"})
@TestPropertySource(locations = "classpath:integration-test.properties")
public class K8sIntegrationTest extends RabbitMqIntegrationTest {

    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(K8sIntegrationTest.class);

    private static final String VAULT_TOKEN = "my-token";

    @Autowired
    protected K8SConnectorService connectorService;

    @Autowired
    protected K8sOpsHelper k8sOpsHelper;

    @Autowired
    protected MockTargetStateConsumer mockTargetStateConsumer;

    @Autowired
    protected ConnectorStateCache connectorStateCache;

    @Autowired
    protected TargetStateContext targetStateContext;

    @Autowired
    protected TargetMessageConsumer targetMessageConsumer;

    @Autowired
    protected DiagnosticEventsService diagnosticEventsService;

    @Autowired
    IMap<String, VenueAccount> venueAccountMap;

    @TestConfiguration
    static class TestHazelcastConfiguration {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance createHazelcastInstance(List<HazelcastMapConfig> hazelcastMaps) {
            HazelcastInstance hazelcastInstance = new TestHazelcastInstanceFactory().newHazelcastInstance();
            hazelcastMaps.forEach(m -> m.setupClientInstance(hazelcastInstance));
            return hazelcastInstance;
        }

        @Bean
        IMap<String, VenueAccount> venueAccountIMap(HazelcastInstance hazelcastInstance) {
            return io.wyden.referencedata.domain.VenueAccountMapConfig.getMap(hazelcastInstance);
        }

    }

    protected static K3sContainer k3s = new K3sContainer(DockerImageName
        .parse("rancher/k3s:v1.21.3-k3s1"))
        .withFileSystemBind(getConnectorAbsolutePath(), "/connector-wrapper-mock.tar")
        .withLogConsumer(new Slf4jLogConsumer(LOGGER));

    protected static VaultContainer<?> vaultContainer = new VaultContainer<>("hashicorp/vault:1.14.8")
        .withVaultToken(VAULT_TOKEN);

    protected static String CONNECTOR_ID = "wydenmock-account";

    @DynamicPropertySource
    static void registerDynamicProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.cloud.vault.port", () -> vaultContainer.getFirstMappedPort());
        registry.add("spring.cloud.vault.host", () -> vaultContainer.getHost());
        registry.add("spring.cloud.vault.token", () -> "my-token");
        registry.add("spring.cloud.vault.authentication", () -> "token");

    }

    @BeforeEach
    void mockReferenceData() {
        venueAccountMap.put(CONNECTOR_ID, VenueAccount.newBuilder().setId(CONNECTOR_ID).build());
    }

    @BeforeAll
    static void init() throws IOException, InterruptedException {
        if (!k3s.isRunning()) {
            k3s.start();
            k3s.execInContainer("ctr", "i", "import", "/connector-wrapper-mock.tar");
            setupConfigMap(Config.fromConfig(new StringReader(k3s.getKubeConfigYaml())));
        }
        vaultContainer.start();
    }

    private static void setupConfigMap(ApiClient apiClient) {
        new K8sOpsHelper(apiClient).applyConfigMap("/target-registry-connector-configmap.yml");
    }

    @AfterEach
    void cleanup() {
        LOGGER.debug("Removing deployments and pods");
        k8sOpsHelper.removeDeployments();
        k8sOpsHelper.removePods();
        connectorStateCache.clearAllStates();
        venueAccountMap.clear();
        diagnosticEventsService.removeConnectorState(CONNECTOR_ID);
        awaitZeroPods();
    }

    @TestConfiguration
    static class K8sApiClient {
        @Bean
        @Primary
        public ApiClient kubernetesApiClient() {
            String kubeConfigYaml = k3s.getKubeConfigYaml();
            LOGGER.info("To use Lens support, please import this config: {}", kubeConfigYaml);
            try {
                return Config.fromConfig(new StringReader(kubeConfigYaml));
            } catch (IOException e) {
                e.printStackTrace();
            }
            return null;
        }
    }

    private static String getConnectorAbsolutePath() {
        String path = "build/resources/integrationTest/resources/connector-wrapper-mock.tar";
        return new File(path).getAbsolutePath();
    }

    protected boolean checkTargetRegistryStatus(String venueAccount, HealthStatus alive) {
        ConnectorState connectorState = targetStateContext.retrieveConnectorState(venueAccount);
        return anyHasStatus(connectorState, alive);
    }

    private static boolean anyHasStatus(ConnectorState connectorState, HealthStatus healthStatus) {
        return connectorState.getDiagnosticEventsList().stream().anyMatch(event -> event.getHealth().getStatus().equals(healthStatus));
    }

    // TODO: Send messages to RabbitMq instead
    protected void deactivateConnector() {
        VenueAccountDeactivationRequest request = VenueAccountDeactivationRequest.newBuilder()
            .setDeactivationReason("Test")
            .setVenueAccountId(CONNECTOR_ID)
            .build();
        targetMessageConsumer.consume(request, null);
        venueAccountMap.put(CONNECTOR_ID, VenueAccount.newBuilder().setId(CONNECTOR_ID).setSuspendedAt("now").build());
    }

    protected void activateConnector() {
        VenueAccountActivateRequest request = VenueAccountActivateRequest.newBuilder()
            .setVenueAccountId("wydenmock-account")
            .setRequester("owner")
            .build();
        targetMessageConsumer.consume(request, null);
        // Simulates race-condition between reference-data and target-registry
        venueAccountMap.put(CONNECTOR_ID, VenueAccount.newBuilder().setId(CONNECTOR_ID).build());
    }

    protected void requestDefaultConnector() {
        List<KeyValue> params = List.of(KeyValue.newBuilder().setKey("testKey").setValue("testValue").build());
        requestConnector(params);
    }

    protected void requestConnector(Collection<KeyValue> parameters) {
        ConnectorRequest.Builder builder = ConnectorRequest.newBuilder()
            .setVenueAccountId(CONNECTOR_ID)
            .setVenueName("wydenmock")
            .setOwner("owner");
        if (!parameters.isEmpty()) {
            parameters.forEach(builder::addParameters);
        }
        targetMessageConsumer.consume(builder.build(), null);
        venueAccountMap.put(CONNECTOR_ID, VenueAccount.newBuilder().setId(CONNECTOR_ID).build());
    }

    protected void requestRedeployOfConnector() {
        ConnectorRedeployRequest.Builder builder = ConnectorRedeployRequest.newBuilder()
            .setVenueAccountId(CONNECTOR_ID)
            .setReason("Vault")
            .setMetadata(Metadata.newBuilder().setRequesterId("TEST").build());
        targetMessageConsumer.consume(builder.build(), null);
    }

    protected void awaitZeroPods() {
        Awaitility.await().atMost(100, TimeUnit.SECONDS).until(() -> k8sOpsHelper.getPods().isEmpty());
    }

    protected void awaitConnectorIsNotAlive() {
        Awaitility.await().atMost(100, TimeUnit.SECONDS).until(() -> !checkTargetRegistryStatus(CONNECTOR_ID, ALIVE));
    }

    protected void awaitConnectorIsAlive() {
        Awaitility.await().atMost(100, TimeUnit.SECONDS).until(() -> checkTargetRegistryStatus(CONNECTOR_ID, ALIVE));
    }

    void awaitHealthyMessageEmitted() {
        Awaitility.await().atMost(100, TimeUnit.SECONDS).until(() -> {
            Map<Capability, Health> capabilityState = mockTargetStateConsumer.getSentCapabilityState();
            return capabilityState.values().stream().anyMatch(health -> health.getStatus() == ALIVE);
        });
    }

    protected boolean assertLastEmittedStatus(HealthStatus expectedStatus) {
        Health health = mockTargetStateConsumer.getSentCapabilityState().get(Capability.MARKET_DATA);
        if (health == null) {
            LOGGER.warn("*** Health status: null");
            return false;
        }
        HealthStatus marketDataState = health.getStatus();
        LOGGER.warn("*** Health status: {}", marketDataState);
        return marketDataState == expectedStatus;
    }
}
