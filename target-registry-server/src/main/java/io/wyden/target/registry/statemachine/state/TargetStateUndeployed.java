package io.wyden.target.registry.statemachine.state;

import io.wyden.published.referencedata.VenueAccountActivateRequest;
import io.wyden.published.targetregistry.ConnectorRequest;
import io.wyden.target.registry.statemachine.ActivationService;
import io.wyden.target.registry.statemachine.DiagnosticEventsReceiveService;
import io.wyden.target.registry.statemachine.TargetState;
import io.wyden.target.registry.statemachine.TargetStateContext;
import io.wyden.target.registry.statemachine.UpdateConnectorService;
import io.wyden.target.registry.domain.model.ConnectorStatus;

public class TargetStateUndeployed extends TargetState {

    public TargetStateUndeployed(TargetStateContext context) {
        super(context);
    }

    @Override
    public ConnectorStatus getStatus() {
        return ConnectorStatus.UNDEPLOYED;
    }

    @Override
    public String getMessage() {
        return "No information about capabilities since last deactivation";
    }

    @Override
    public TargetState onActivate(VenueAccountActivateRequest request, ActivationService service) {
        service.activate(request, request.getVenueAccountId());
        return new TargetStateDeployed(context);
    }

    @Override
    public TargetState onCreateOrUpdate(ConnectorRequest request, UpdateConnectorService service) {
        service.update(request);
        return new TargetStateDeployed(context);
    }

    public TargetState onDiagnosticEvent(io.wyden.published.targetregistry.ConnectorState newState, TargetState prevState, String connectorId, String venue, DiagnosticEventsReceiveService service) {
        // no need to log it as exception, diagnostic events can come after deactivation
        return new TargetStateUndeployed(context);
    }
}
