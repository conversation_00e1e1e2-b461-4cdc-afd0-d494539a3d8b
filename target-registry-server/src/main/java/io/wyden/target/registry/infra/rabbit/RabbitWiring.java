package io.wyden.target.registry.infra.rabbit;

import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
class RabbitWiring {

    @Bean
    RabbitIntegrator rabbitIntegrator(@Value("${rabbitmq.username}") String userName,
                                      @Value("${rabbitmq.password}") String password,
                                      @Value("${rabbitmq.virtualHost}") String virtualHost,
                                      @Value("${rabbitmq.host}") String host,
                                      @Value("${rabbitmq.tls}") String tls,
                                      @Value("${rabbitmq.port}") int port) {
        return new RabbitIntegrator(userName, password, virtualHost, host, port, tls);
    }
}
