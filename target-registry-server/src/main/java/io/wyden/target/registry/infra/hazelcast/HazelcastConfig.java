package io.wyden.target.registry.infra.hazelcast;

import com.hazelcast.client.HazelcastClient;
import com.hazelcast.client.config.ClientConfig;
import com.hazelcast.client.config.ClientNetworkConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.targetregistry.ConnectorCapabilities;
import io.wyden.published.targetregistry.ConnectorState;
import io.wyden.referencedata.client.ReferenceDataProvider;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.referencedata.domain.VenueAccountMapConfig;
import io.wyden.target.registry.domain.ConnectorStateSnapshotMapConfig;
import io.wyden.target.registry.domain.DiagnosticEventsMapConfig;
import io.wyden.target.registry.domain.VenueCapabilitiesMapConfig;
import io.wyden.target.registry.domain.model.ConnectorStateSnapshot;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

@Configuration
class HazelcastConfig {

    @Bean
    ConnectorStateSnapshotMapConfig connectorStateMapConfig() {
        return new ConnectorStateSnapshotMapConfig();
    }

    @Bean
    DiagnosticEventsMapConfig diagnosticEventsMapConfig() {
        return new DiagnosticEventsMapConfig();
    }

    @Bean
    VenueCapabilitiesMapConfig venueCapabilitiesMapConfig() {
        return new VenueCapabilitiesMapConfig();
    }

    @Bean
    VenueAccountMapConfig venueAccountMapConfig() {
        return new VenueAccountMapConfig();
    }

    @Bean
    IMap<String, ConnectorStateSnapshot> stateSnapshotMap(HazelcastInstance hazelcast) {
        return ConnectorStateSnapshotMapConfig.getMap(hazelcast);
    }

    @Bean
    IMap<String, ConnectorCapabilities> venueCapabilitiesMap(HazelcastInstance hazelcast) {
        return VenueCapabilitiesMapConfig.getMap(hazelcast);
    }

    @Bean
    IMap<String, ConnectorState> expiringDiagnosticMap(HazelcastInstance hazelcast, DiagnosticEventsMapConfig diagnosticEventsMapConfig) {
        return diagnosticEventsMapConfig.getMap(hazelcast);
    }

    @Bean
    VenueAccountCacheFacade venueAccountCacheFacade(HazelcastInstance hazelcast,
                                                    Tracing otlTracing) {
        return ReferenceDataProvider.getVenueAccountCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    ClientConfig createClientConfig(@Value("${hz.addressList}") String addressList,
                                    @Value("${hz.outboundPortDefinition}") String outboundPortDefinition,
                                    List<HazelcastMapConfig> hazelcastMaps) {
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.getConnectionStrategyConfig().getConnectionRetryConfig().setMaxBackoffMillis(5000);

        ClientNetworkConfig networkConfig = clientConfig.getNetworkConfig();
        Arrays.stream(addressList.split(",")).forEach(networkConfig::addAddress);
        networkConfig.setSmartRouting(true);

        if (!outboundPortDefinition.isBlank()) {
            networkConfig.addOutboundPortDefinition(outboundPortDefinition);
        }

        networkConfig.setRedoOperation(true);
        networkConfig.setConnectionTimeout(5000);

        hazelcastMaps.forEach(m -> m.applyConfig(clientConfig));

        return clientConfig;
    }

    @Bean("hazelcast")
    HazelcastInstance createHazelcastInstance(ClientConfig clientConfig, Telemetry telemetry, List<HazelcastMapConfig> hazelcastMaps) {
        HazelcastInstance hz = HazelcastClient.newHazelcastClient(clientConfig);
        hazelcastMaps.forEach(m -> m.setupClientInstance(hz));
        hazelcastMaps.forEach(m -> m.setupMetrics(hz, telemetry.getMeterRegistry()));
        return hz;
    }
}
