package io.wyden.target.registry.statemachine;

import io.micrometer.common.util.StringUtils;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.referencedata.VenueAccountActivateRequest;
import io.wyden.published.referencedata.VenueAccountDeactivationRequest;
import io.wyden.published.targetregistry.ConnectorRedeployRequest;
import io.wyden.published.targetregistry.ConnectorRequest;
import io.wyden.published.targetregistry.ConnectorState;
import io.wyden.target.registry.capabilities.CapabilitiesService;
import io.wyden.target.registry.capabilities.TargetStateChangedEventEmitter;
import io.wyden.target.registry.connectorstate.ConnectorStateCache;
import io.wyden.target.registry.diagnosticevents.DiagnosticEventsService;
import io.wyden.target.registry.statemachine.state.TargetStateAlive;
import io.wyden.target.registry.statemachine.state.TargetStateDeployed;
import io.wyden.target.registry.statemachine.state.TargetStateUndefined;
import io.wyden.target.registry.statemachine.state.TargetStateUndeployed;
import io.wyden.target.registry.domain.model.ConnectorStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
public class TargetStateContext {

    private static final Logger LOGGER = LoggerFactory.getLogger(TargetStateContext.class);

    private final ActivationService activationService;
    private final DeactivationService deactivationService;
    private final DiagnosticEventsReceiveService diagnosticEventsReceiveService;
    private final DiagnosticEventExpireService diagnosticEventExpireService;
    private final RedeployConnectorService redeployConnectorService;
    private final UpdateConnectorService updateConnectorService;
    private final ConnectorStateCache connectorStateCache;
    private final DiagnosticEventsService diagnosticEventsService;
    private final TargetStateChangedEventEmitter targetStateChangedEventEmitter;
    private final CapabilitiesService capabilitiesService;

    public TargetStateContext(ActivationService activationService,
                              DeactivationService deactivationService,
                              DiagnosticEventsReceiveService diagnosticEventsReceiveService,
                              DiagnosticEventExpireService diagnosticEventExpireService,
                              RedeployConnectorService redeployConnectorService,
                              UpdateConnectorService updateConnectorService,
                              ConnectorStateCache connectorStateCache,
                              DiagnosticEventsService diagnosticEventsService,
                              TargetStateChangedEventEmitter targetStateChangedEventEmitter,
                              CapabilitiesService capabilitiesService) {
        this.activationService = activationService;
        this.deactivationService = deactivationService;
        this.diagnosticEventsReceiveService = diagnosticEventsReceiveService;
        this.diagnosticEventExpireService = diagnosticEventExpireService;
        this.redeployConnectorService = redeployConnectorService;
        this.updateConnectorService = updateConnectorService;
        this.connectorStateCache = connectorStateCache;
        this.diagnosticEventsService = diagnosticEventsService;
        this.targetStateChangedEventEmitter = targetStateChangedEventEmitter;
        this.capabilitiesService = capabilitiesService;
    }

    public void onRedeploy(ConnectorRedeployRequest request) {
        String connectorId = request.getVenueAccountId();
        TargetState prevState = retrieve(connectorId);
        ConnectorState prevConnectorState = diagnosticEventsService.getConnectorState(connectorId, prevState);
        TargetState newState = prevState.onRedeploy(request, redeployConnectorService);
        persistAndNotify(newState, prevState, prevConnectorState, request.getMetadata().getCreatedAt(), request.getVenueAccountId());
    }

    public void onDiagnosticEvent(ConnectorState message, String connectorId, String venue) {
        TargetState prevState = retrieve(connectorId);
        ConnectorState prevConnectorState = diagnosticEventsService.getConnectorState(connectorId, prevState);
        TargetState newState = prevState.onDiagnosticEvent(message, prevState, connectorId, venue, diagnosticEventsReceiveService);
        persistAndNotify(newState, prevState, prevConnectorState, DateUtils.toIsoUtcTime(ZonedDateTime.now()), connectorId);
    }

    public void onActivate(VenueAccountActivateRequest message) {
        String connectorId = message.getVenueAccountId();
        TargetState prevState = retrieve(connectorId);
        ConnectorState prevConnectorState = diagnosticEventsService.getConnectorState(connectorId, prevState);
        TargetState newState = prevState.onActivate(message, activationService);
        persistAndNotify(newState, prevState, prevConnectorState, DateUtils.toIsoUtcTime(ZonedDateTime.now()), connectorId);
    }

    public void onDeactivate(VenueAccountDeactivationRequest message) {
        String connectorId = message.getVenueAccountId();
        TargetState prevState = retrieve(connectorId);
        TargetState newState = prevState.onDeactivate(message, deactivationService);
        persist(newState, DateUtils.toIsoUtcTime(ZonedDateTime.now()), connectorId);
    }

    public void onCreateOrUpdate(ConnectorRequest request) {
        String connectorId = request.getVenueAccountId();
        TargetState prevState = retrieve(connectorId);
        ConnectorState prevConnectorState = diagnosticEventsService.getConnectorState(connectorId, prevState);
        TargetState newState = prevState.onCreateOrUpdate(request, updateConnectorService);
        persistAndNotify(newState, prevState, prevConnectorState, DateUtils.toIsoUtcTime(ZonedDateTime.now()), connectorId);
    }

    public void onExpire(String connectorId, ConnectorState prevConnectorState) {
        TargetState prevState = retrieve(connectorId);
        TargetState newState = prevState.onExpire(connectorId, diagnosticEventExpireService);
        persistAndNotify(newState, prevState, prevState.getConnectorState(connectorId, prevConnectorState), DateUtils.toIsoUtcTime(ZonedDateTime.now()), connectorId);
    }

    public CapabilitiesService getCapabilitiesService() {
        return capabilitiesService;
    }

    public ConnectorState retrieveConnectorState(String connectorId) {
        TargetState targetState = retrieve(connectorId);
        return diagnosticEventsService.getConnectorState(connectorId, targetState);
    }

    private TargetState retrieve(String connectorId) {
        return toTargetState(connectorStateCache.retrieve(connectorId));
    }

    private TargetState toTargetState(ConnectorStatus connectorStatus) {
        return switch (connectorStatus) {
            case UNDEFINED -> new TargetStateUndefined(this);
            case DEPLOYED -> new TargetStateDeployed(this);
            case UNDEPLOYED -> new TargetStateUndeployed(this);
            case ALIVE -> new TargetStateAlive(this);
            case UNRECOGNIZED -> throw new IllegalArgumentException("Unrecognized connector status: " + connectorStatus.getNumber());
        };
    }

    private void persistAndNotify(TargetState newState, TargetState oldState, ConnectorState oldConnectorState, String timestamp, String connectorId) {
        if (oldState.getStatus() != newState.getStatus()) {
            LOGGER.info("Target: {}, Switched state from {} to {} ", connectorId, oldState.getStatus(), newState.getStatus());
            persist(newState, timestamp, connectorId);
        }
        notifyUser(newState, oldConnectorState, connectorId);
    }

    private void persist(TargetState newState, String timestamp, String connectorId) {
        connectorStateCache.persist(newState.getStatus(), timestamp, connectorId);
    }

    private void notifyUser(TargetState newState, ConnectorState oldConnectorState, String connectorId) {
        ConnectorState newConnectorState = diagnosticEventsService.getConnectorState(connectorId, newState);
        targetStateChangedEventEmitter.emitTargetStateChangedEvents(connectorId, oldConnectorState, newConnectorState);
    }
}
