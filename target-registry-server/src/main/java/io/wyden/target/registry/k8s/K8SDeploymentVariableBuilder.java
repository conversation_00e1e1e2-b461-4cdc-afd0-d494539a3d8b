package io.wyden.target.registry.k8s;

import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1Deployment;
import io.kubernetes.client.openapi.models.V1EnvVar;
import io.wyden.published.common.KeyValue;
import io.wyden.published.targetregistry.ConnectorRequest;
import io.wyden.target.registry.vault.ConnectorVaultRepository;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

@Component
class K8SDeploymentVariableBuilder {

    private final ConnectorPropertiesConfiguration connectorPropertiesConfiguration;
    private final ConnectorVaultRepository connectorVaultRepository;

    K8SDeploymentVariableBuilder(ConnectorPropertiesConfiguration connectorPropertiesConfiguration,
                                 ConnectorVaultRepository connectorVaultRepository) {
        this.connectorPropertiesConfiguration = connectorPropertiesConfiguration;
        this.connectorVaultRepository = connectorVaultRepository;
    }

    List<V1EnvVar> build(ConnectorRequest request, V1Deployment k8sDeployment) {
        // Target registry properties starting with: connector.properties
        Map<String, V1EnvVar> targetRegistryConnectorVars = getTargetRegistryConnectorProperties();
        Map<String, V1EnvVar> hardcodedVars = getHardcodedVariables(request);
        // K8s Config Map properties
        Map<String, V1EnvVar> configMapVars = getK8sDeploymentProperties(k8sDeployment);
        // UI passed EnvVars
        Map<String, V1EnvVar> requestVars = getRequestParametersStartingWithEnv(request.getParametersList());

        Map<String, V1EnvVar> result = new HashMap<>();
        result.putAll(targetRegistryConnectorVars);
        result.putAll(configMapVars);
        result.putAll(requestVars);
        result.putAll(hardcodedVars);

        return new ArrayList<>(result.values());
    }

    private Map<String, V1EnvVar> getHardcodedVariables(ConnectorRequest request) {
        String vaultToken = generateVaultToken(request.getVenueAccountId());

        return Map.of(
            "ACCOUNT_NAME", toEnv("ACCOUNT_NAME", request.getVenueAccountId()),
            "ACCOUNT_OWNER", toEnv("ACCOUNT_OWNER", request.getOwner()),
            "VENUE", toEnv("VENUE", request.getVenueName()),
            "SERVER_PORT", toEnv("SERVER_PORT", "8099"),
            "SPRING_CLOUD_VAULT_TOKEN", toEnv("SPRING_CLOUD_VAULT_TOKEN", vaultToken)
        );
    }

    private String generateVaultToken(String venueAccountId) {
        String policyName = connectorVaultRepository.createPolicy(venueAccountId);
        return connectorVaultRepository.createToken(policyName);
    }

    private static Map<String, V1EnvVar> getRequestParametersStartingWithEnv(Collection<KeyValue> parametersList) {
        return parametersList.stream()
            .filter(parameter -> parameter.getKey().startsWith("ENV_"))
            .map(parameter -> new V1EnvVar().name(parameter.getKey().replaceFirst("ENV_", "")).value(parameter.getValue()))
            .collect(Collectors.toMap(V1EnvVar::getName, Function.identity()));
    }

    private Map<String, V1EnvVar> getK8sDeploymentProperties(V1Deployment deployment) {
        V1Container containerTemplate = K8SApiUtils.getConnectorContainerTemplate(deployment);
        if (isNotEmpty(containerTemplate.getEnv())) {
            return containerTemplate.getEnv()
                .stream()
                .collect(Collectors.toMap(V1EnvVar::getName, Function.identity()));
        }
        return Map.of();
    }

    private Map<String, V1EnvVar> getTargetRegistryConnectorProperties() {
        return connectorPropertiesConfiguration.getConnectorProperties().entrySet()
            .stream()
            .map(entry -> toEnv(entry.getKey(), entry.getValue()))
            .collect(Collectors.toMap(V1EnvVar::getName, Function.identity()));
    }

    private static V1EnvVar toEnv(String key, String value) {
        V1EnvVar v1EnvVar = new V1EnvVar();
        v1EnvVar.setName(key);
        v1EnvVar.setValue(value);
        return v1EnvVar;
    }
}
