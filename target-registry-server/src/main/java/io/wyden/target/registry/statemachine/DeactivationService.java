package io.wyden.target.registry.statemachine;

import io.wyden.published.audit.EventLogStatus;
import io.wyden.published.referencedata.VenueAccountDeactivationRequest;
import io.wyden.target.registry.diagnosticevents.DiagnosticEventsService;
import io.wyden.target.registry.eventlog.EventLogEmitter;
import io.wyden.target.registry.k8s.KubernetesService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class DeactivationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeactivationService.class);

    private final KubernetesService kubernetesService;
    private final EventLogEmitter eventLogEmitter;
    private final DiagnosticEventsService diagnosticEventsService;

    public DeactivationService(KubernetesService kubernetesService,
                               EventLogEmitter eventLogEmitter,
                               DiagnosticEventsService diagnosticEventsService) {
        this.kubernetesService = kubernetesService;
        this.eventLogEmitter = eventLogEmitter;
        this.diagnosticEventsService = diagnosticEventsService;
    }

    public void deactivate(VenueAccountDeactivationRequest message, String connectorId) {
        try {
            LOGGER.info("Deactivating connector for venue account: {}", connectorId);

            kubernetesService.deleteDeployment(connectorId);
            diagnosticEventsService.removeConnectorState(connectorId);

            eventLogEmitter.emit(message.getMetadata().getRequesterId(),
                message.getDeactivationReason(),
                EventLogStatus.SUCCESS,
                "deactivateVenueAccount",
                message.getMetadata().getCorrelationObject());
        } catch (Exception e) {
            eventLogEmitter.emit(message.getMetadata().getRequesterId(),
                "Venue Account " + connectorId + " can't be deactivated.",
                EventLogStatus.FAILURE,
                "deactivateVenueAccount",
                message.getMetadata().getCorrelationObject());
            throw new RuntimeException("Unable to deactivate connector " + connectorId, e);
        }
    }
}
