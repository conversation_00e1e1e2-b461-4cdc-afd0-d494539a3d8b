package io.wyden.referencedata.client;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.hazelcast.query.Predicate;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.domain.PortfolioMapConfig;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record PortfolioCursor(String createdAt, String name, String id) {

    @JsonIgnore
    public boolean isSortByPortfolioName() {
        return Objects.nonNull(createdAt) && Objects.nonNull(name) && Objects.isNull(id);
    }

    @JsonIgnore
    public boolean isSortByPortfolioId() {
        return Objects.isNull(createdAt) && Objects.isNull(name) && Objects.nonNull(id);
    }

    @JsonIgnore
    public boolean isSortByCreatedAt() {
        return Objects.nonNull(createdAt) && Objects.isNull(name) && Objects.isNull(id);
    }

    public Predicate<String, Portfolio> ascPredicate() {
        if (isSortByPortfolioName()) {
            return PortfolioMapConfig.filterByNameAfterAndThenCreatedAtAfter(name, createdAt);
        } else if (isSortByPortfolioId()) {
            return PortfolioMapConfig.filterByPortfolioIdAfter(id);
        } else if (isSortByCreatedAt()) {
            return PortfolioMapConfig.filterByCreatedAtAfter(createdAt);
        }
        return null;
    }

    public Predicate<String, Portfolio> descPredicate() {
        if (isSortByPortfolioName()) {
            return PortfolioMapConfig.filterByNameBeforeAndThenCreatedAtBefore(name, createdAt);
        } else if (isSortByPortfolioId()) {
            return PortfolioMapConfig.filterByPortfolioIdBefore(id);
        } else if (isSortByCreatedAt()) {
            return PortfolioMapConfig.filterByCreatedAtBefore(createdAt);
        }
        return null;
    }
}
