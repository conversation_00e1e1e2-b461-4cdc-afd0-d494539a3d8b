package io.wyden.referencedata.client;

import java.util.List;

public class ReferenceDataValidationException extends RuntimeException {
    private final List<String> fieldNames;

    public ReferenceDataValidationException(String message, List<String> fieldNames) {
        super(message);
        this.fieldNames = fieldNames;
    }

    public List<String> getFieldNames() {
        return fieldNames;
    }

    @Override
    public String getMessage() {
        return super.getMessage() + String.join(", ", fieldNames);
    }
}
