{"name": "@algotrader/schema-graphql", "version": "1.1.14", "description": "GraphQL schema which defines the graphql contract between UI and Backend for Frontend service.", "main": "index.js", "files": ["*.graphql"], "scripts": {"validate-schema": "npx eslint ./*.graphql", "test": "echo \"Error: no test specified\" && exit 1", "check-version-update": "node compare.js", "check-schema-touched": "node check-schema-touched.js"}, "repository": {"type": "git", "url": "*******************:atcloud/schema-graphql.git"}, "publishConfig": {"@algotrader:registry": "https://repo.wyden.io/nexus/repository/npm-snapshots/"}, "author": "", "license": "ISC", "devDependencies": {"@graphql-eslint/eslint-plugin": "3.10.6", "eslint": "8.20.0"}, "dependencies": {"compare-versions": "^4.1.3"}}