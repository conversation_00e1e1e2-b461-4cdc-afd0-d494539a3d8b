package io.wyden.accessgateway.client.rest;

import io.micrometer.core.instrument.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.ResponseErrorHandler;

import java.io.IOException;
import java.util.NoSuchElementException;

class RestTemplateErrorHandling implements ResponseErrorHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(RestTemplateErrorHandling.class);

    @Override
    public boolean hasError(ClientHttpResponse httpResponse) throws IOException {
        HttpStatusCode httpStatus = httpResponse.getStatusCode();
        return  httpStatus.is1xxInformational() || httpStatus.is3xxRedirection() || httpStatus.is4xxClientError() || httpStatus.is5xxServerError();
    }

    @Override
    public void handleError(ClientHttpResponse httpResponse) throws IOException {
        HttpStatusCode statusCode = httpResponse.getStatusCode();
        if (statusCode.value() == 401) {
            throw new AccessDeniedException("Access Gateway response 'UNAUTHORIZED'");
        } else if (statusCode.value() == 403) {
            throw new AccessForbiddenException("Access Gateway response 'FORBIDDEN'");
        } else if (statusCode.value() == 404) {
            throw new NoSuchElementException("Access Gateway response 'NOT_FOUND'");
        } else {
            String body = IOUtils.toString(httpResponse.getBody());
            LOGGER.error("Unsuccessful request to Access Gateway. Code: {}, Body: {}", httpResponse.getStatusCode(), body);
            throw new RuntimeException("Access Gateway response with code: " + httpResponse.getStatusCode() + " and body: " + body);
        }
    }
}
