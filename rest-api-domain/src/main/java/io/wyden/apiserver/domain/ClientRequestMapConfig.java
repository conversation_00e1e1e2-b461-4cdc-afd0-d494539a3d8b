package io.wyden.apiserver.domain;

import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.published.client.ClientRequest;

import static io.wyden.cloudutils.hazelcast.Serializers.protobufSerializer;

public class ClientRequestMapConfig extends HazelcastMapConfig {

    private static final String MAP_NAME = "client-request_0.1";

    public static IMap<String, ClientRequest> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
        serializationConfig.addSerializerConfig(protobufSerializer(ClientRequest.class, ClientRequest.parser()));
    }
}
