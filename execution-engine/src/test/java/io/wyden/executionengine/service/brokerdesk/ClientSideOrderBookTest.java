package io.wyden.executionengine.service.brokerdesk;

import io.wyden.executionengine.domain.model.BookEntry;
import io.wyden.executionengine.domain.model.Execution;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatIllegalArgumentException;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class ClientSideOrderBookTest {

    public static final String ORDER_ID_1 = "abc1.0";
    public static final String ORDER_ID_2 = "abc2.0";
    public static final String ORDER_ID_3 = "abc3.0";
    public static final String ORDER_ID_4 = "abc4.0";
    public static final String INSTRUMENT_ID = "BTCUSD";
    public static final String LIMIT_PRICE = "20000";
    public static final String HALF_QUANTITY = "5";
    public static final String QUANTITY = "10";
    public static final String DOUBLE_QUANTITY = "20";
    public static final String TWO_AND_HALF_QUANTITY = "25";
    public static final String TRIPLE_QUANTITY = "30";
    public static final String PARTIAL_QUANTITY = "3";

    @Test
    void emptyBookReturnsNullTop() {
        ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
        assertThat(book.top()).isNull();
    }

    @Test
    void testInsertAndGetTop() {
        ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
        OemsRequest order = buyLimitOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1);

        book.accept(order);
        assertThat(book.top()).isNotNull();
        assertThat(book.top().getOrder()).isEqualTo(order);
    }

    @Test
    void testInEmptyMethod() {
        ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();
        assertThat(book.isEmpty()).isTrue();
        book.accept(sellMarketOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1));
        assertThat(book.isEmpty()).isFalse();
        book.remove(ORDER_ID_1);
        assertThat(book.isEmpty()).isTrue();
    }

    @Test
    void buysNotAllowedInSellBook() {
        ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();
        assertThatThrownBy(() -> book.accept(buyLimitOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1)));
    }

    @Test
    void sellsNotAllowedInBuyBook() {
        ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
        assertThatThrownBy(() -> book.accept(sellLimitOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1)));
    }

    @Test
    void stopOrderNotAllowedInEitherBook() {
        ClientSideOrderBook buyBook = ClientSideOrderBook.emptyBuyBook();
        ClientSideOrderBook sellBook = ClientSideOrderBook.emptySellBook();
        assertThatThrownBy(() -> buyBook.accept(buyStopOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1)));
        assertThatThrownBy(() -> sellBook.accept(sellStopOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1)));
    }

    @Test
    void buysBookMarketInFIFOOrderBeforeLimitOrder() throws Exception {
        ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
        OemsRequest marketFirst = buyMarketOrder("10000", QUANTITY, ORDER_ID_1);
        OemsRequest marketSecond = buyMarketOrder("20000", QUANTITY, ORDER_ID_2);
        OemsRequest limitOrder = buyLimitOrder("30000", QUANTITY, ORDER_ID_3);

        book.accept(limitOrder);
        Thread.sleep(10);
        book.accept(marketFirst);
        Thread.sleep(10);
        book.accept(marketSecond);

        assertThat(book.top().getOrder()).isEqualTo(marketFirst);
    }

    @Test
    void sellsBookMarketInFIFOOrderBeforeLimitOrder() throws Exception {
        ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();
        OemsRequest marketFirst = sellMarketOrder("30000", QUANTITY, ORDER_ID_1);
        OemsRequest marketSecond = sellMarketOrder("20000", QUANTITY, ORDER_ID_2);
        OemsRequest limitOrder = sellLimitOrder("10000", QUANTITY, ORDER_ID_3);

        book.accept(limitOrder);
        Thread.sleep(10);
        book.accept(marketFirst);
        Thread.sleep(10);
        book.accept(marketSecond);

        assertThat(book.top().getOrder()).isEqualTo(marketFirst);
    }

    @Test
    void buysBookTopShouldBeHighestPrice() {
        ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
        OemsRequest good = buyLimitOrder("20000", QUANTITY, ORDER_ID_1);
        OemsRequest secondBest = buyLimitOrder("30000", QUANTITY, ORDER_ID_2);
        OemsRequest best = buyLimitOrder("30001", QUANTITY, ORDER_ID_3);

        book.accept(good);
        book.accept(best);
        book.accept(secondBest);

        assertThat(book.top().getOrder()).isEqualTo(best);
    }

    @Test
    void sellsBookTopShouldBeLowestPrice() {
        ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();
        OemsRequest good = sellLimitOrder("50000", QUANTITY, ORDER_ID_1);
        OemsRequest secondBest = sellLimitOrder("20000", QUANTITY, ORDER_ID_2);
        OemsRequest best = sellLimitOrder("19999", QUANTITY, ORDER_ID_3);

        book.accept(good);
        book.accept(best);
        book.accept(secondBest);

        assertThat(book.top().getOrder()).isEqualTo(best);
    }

    @Test
    void samePriceLevelInFIFO_Buys() throws Exception {
        ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();

        OemsRequest first = buyLimitOrder("20000", QUANTITY, ORDER_ID_1);
        OemsRequest theSameButSecond = buyLimitOrder("20000", QUANTITY, ORDER_ID_2);

        book.accept(first);
        Thread.sleep(10);
        book.accept(theSameButSecond);

        assertThat(book.top().getOrder()).isEqualTo(first);
    }

    @Test
    void samePriceLevelInFIFO_Sells() throws Exception {
        ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();

        OemsRequest first = sellLimitOrder("20000", QUANTITY, ORDER_ID_1);
        OemsRequest theSameButSecond = sellLimitOrder("20000", QUANTITY, ORDER_ID_2);

        book.accept(first);
        Thread.sleep(10);
        book.accept(theSameButSecond);

        assertThat(book.top().getOrder()).isEqualTo(first);
    }

    @Test
    void shouldRemoveById() {
        ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();

        OemsRequest first = sellLimitOrder("20000", QUANTITY, ORDER_ID_1);
        OemsRequest theSameButSecond = sellLimitOrder("20000", QUANTITY, ORDER_ID_2);

        book.accept(first);
        book.accept(theSameButSecond);

        book.remove(ORDER_ID_2);

        Set<BookEntry> priceLevel = book.getAll().stream()
            .filter(f -> f.getPrice().equals(new BigDecimal("20000")))
            .collect(Collectors.toSet());
        assertThat(priceLevel).noneMatch(entry -> entry.getOrder().getOrderId().equals(ORDER_ID_2));
        assertThat(priceLevel).hasSize(1);
    }

    @Test
    void shouldThrowWhenRemovingUnknownOrder() {
        ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
        assertThat(book.remove(ORDER_ID_1)).isNull();
    }

    @Test
    void shouldReturnExecutableBuyAmountConsideringPartialOrderAndLimit() {
        ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
        OemsRequest partial = buyMarketOrder("19000", QUANTITY, ORDER_ID_1);
        OemsRequest market = buyMarketOrder("", QUANTITY, ORDER_ID_2);
        OemsRequest good = buyLimitOrder("21000", QUANTITY, ORDER_ID_3);
        OemsRequest bad = buyLimitOrder("19000", QUANTITY, ORDER_ID_4);

        book.accept(partial);
        book.accept(market);
        book.accept(good);
        book.accept(bad);

        book.execute(decimal(HALF_QUANTITY), decimal("20000"));
        assertThat(book.getExecutableAmount(decimal("20000"))).isEqualByComparingTo(decimal(TWO_AND_HALF_QUANTITY));
    }

    @Test
    void shouldReturnExecutableSellAmountConsideringPartialOrderAndLimit() {
        ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();
        OemsRequest partial = sellMarketOrder("21000", QUANTITY, ORDER_ID_1);
        OemsRequest market = sellMarketOrder("", QUANTITY, ORDER_ID_2);
        OemsRequest good = sellLimitOrder("19000", QUANTITY, ORDER_ID_3);
        OemsRequest bad = sellLimitOrder("21000", QUANTITY, ORDER_ID_4);

        book.accept(partial);
        book.accept(market);
        book.accept(good);
        book.accept(bad);

        book.execute(decimal(HALF_QUANTITY), decimal("20000"));
        assertThat(book.getExecutableAmount(decimal("20000"))).isEqualByComparingTo(decimal(TWO_AND_HALF_QUANTITY));
    }

    @Nested
    class ExecutableBuyExecutionsTest {

        private BigDecimal getLimit(String limit) {
            return limit.isBlank() ? null : new BigDecimal(limit);
        }

        @ParameterizedTest(name = "shouldExecuteTopOrderPartially({0})")
        @ValueSource(strings = {"20000", "19999", "0"})
        void shouldExecuteTopOrderPartially(String limitString) {
            ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
            OemsRequest good = buyLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = buyLimitOrder("30001", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            BigDecimal limit = getLimit(limitString);
            List<Execution> executions = book.execute(new BigDecimal(PARTIAL_QUANTITY), limit);
            assertThat(executions).containsExactly(execution(best, PARTIAL_QUANTITY, limitString));

            assertThat(book.getAll()).hasSize(2);
        }

        @ParameterizedTest(name = "shouldExecuteTopOrderFully({0})")
        @ValueSource(strings = {"20000", "19999", "0"})
        void shouldExecuteTopOrderFully(String limitString) {
            ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
            OemsRequest good = buyLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = buyLimitOrder("30001", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            BigDecimal limit = getLimit(limitString);
            List<Execution> executions = book.execute(new BigDecimal(QUANTITY), limit);
            assertThat(executions).containsExactly(execution(best, QUANTITY, limitString));

            assertThat(book.getAll()).hasSize(1);
        }

        @ParameterizedTest(name = "shouldExecuteMultipleOrdersIfNecessary({0})")
        @ValueSource(strings = {"20000", "19999", "0"})
        void shouldExecuteMultipleOrdersIfNecessary(String limitString) {
            ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
            OemsRequest good = buyLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = buyLimitOrder("30001", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            BigDecimal limit = getLimit(limitString);
            List<Execution> executions = book.execute(new BigDecimal(DOUBLE_QUANTITY), limit);
            assertThat(executions).containsExactly(
                execution(best, QUANTITY, limitString),
                execution(good, QUANTITY, limitString));

            assertThat(book.getAll()).hasSize(0);
        }

        @ParameterizedTest(name = "onlyExecutesUpToBookDepth({0})")
        @ValueSource(strings = {"20000", "19999", "0"})
        void onlyExecutesUpToBookDepth(String limitString) {
            ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
            OemsRequest good = buyLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = buyLimitOrder("30001", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            BigDecimal limit = getLimit(limitString);
            assertThatIllegalArgumentException().isThrownBy(() ->
                book.execute(new BigDecimal(TRIPLE_QUANTITY), limit)
            );
        }

        @Test
        void onlyExecutesUpGivenLimit() {
            ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
            OemsRequest good = buyLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = buyLimitOrder("30001", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            BigDecimal limit = getLimit("25000");
            assertThatIllegalArgumentException().isThrownBy(() ->
                book.execute(new BigDecimal(DOUBLE_QUANTITY), limit)
            );
        }
    }


    @Nested
    class ExecutableSellExecutionsTest {

        private BigDecimal getLimit(String limit) {
            return limit.isBlank() ? null : new BigDecimal(limit);
        }

        @ParameterizedTest(name = "shouldExecuteTopOrderPartially({0})")
        @ValueSource(strings = {"20000", "20001", "999999999"})
        void shouldExecuteTopOrderPartially(String limitString) {
            ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();
            OemsRequest good = sellLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = sellLimitOrder("19000", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            BigDecimal limit = getLimit(limitString);
            List<Execution> executions = book.execute(new BigDecimal(PARTIAL_QUANTITY), limit);
            assertThat(executions).containsExactly(execution(best, PARTIAL_QUANTITY, limitString));

            assertThat(book.getAll()).hasSize(2);
        }

        @ParameterizedTest(name = "shouldExecuteTopOrderFully({0})")
        @ValueSource(strings = {"20000", "20001", "999999999"})
        void shouldExecuteTopOrderFully(String limitString) {
            ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();
            OemsRequest good = sellLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = sellLimitOrder("19000", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            BigDecimal limit = getLimit(limitString);
            List<Execution> executions = book.execute(new BigDecimal(QUANTITY), limit);
            assertThat(executions).containsExactly(execution(best, QUANTITY, limitString));

            assertThat(book.getAll()).hasSize(1);
        }

        @ParameterizedTest(name = "shouldExecuteMultipleOrdersIfNecessary({0})")
        @ValueSource(strings = {"20000", "20001", "999999999"})
        void shouldExecuteMultipleOrdersIfNecessary(String limitString) {
            ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();
            OemsRequest good = sellLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = sellLimitOrder("19000", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            BigDecimal limit = getLimit(limitString);
            List<Execution> executions = book.execute(new BigDecimal(DOUBLE_QUANTITY), limit);
            assertThat(executions).containsExactly(
                execution(best, QUANTITY, limitString),
                execution(good, QUANTITY, limitString));

            assertThat(book.getAll()).hasSize(0);
        }

        @ParameterizedTest(name = "onlyExecutesUpToBookDepth({0})")
        @ValueSource(strings = {"20000", "20001", "999999999"})
        void onlyExecutesUpToBookDepth(String limitString) {
            ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();
            OemsRequest good = sellLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = sellLimitOrder("19000", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            BigDecimal limit = getLimit(limitString);
            assertThatIllegalArgumentException().isThrownBy(() ->
                book.execute(new BigDecimal(TRIPLE_QUANTITY), limit)
            );
        }

        @Test
        void onlyExecutesUpGivenLimit() {
            ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();
            OemsRequest good = sellLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = sellLimitOrder("19000", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            BigDecimal limit = getLimit("19200");
            assertThatIllegalArgumentException().isThrownBy(() ->
                book.execute(new BigDecimal(DOUBLE_QUANTITY), limit)
            );
        }
    }


    @Nested
    class NonExecutablesTest {

        private BigDecimal getLimit(String limit) {
            return limit.isBlank() ? null : new BigDecimal(limit);
        }

        @Test
        void shouldNotExecuteBuysIfPriceIsBad() {
            ClientSideOrderBook book = ClientSideOrderBook.emptyBuyBook();
            OemsRequest good = buyLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = buyLimitOrder("30001", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            assertThatIllegalArgumentException().isThrownBy(() ->
                    book.execute(new BigDecimal(PARTIAL_QUANTITY), getLimit("30002"))
            );
        }

        @Test
        void shouldNotExecuteSellsIfPriceIsBad() {
            ClientSideOrderBook book = ClientSideOrderBook.emptySellBook();
            OemsRequest good = sellLimitOrder("20000", QUANTITY, ORDER_ID_1);
            OemsRequest best = sellLimitOrder("19000", QUANTITY, ORDER_ID_3);

            book.accept(good);
            book.accept(best);

            assertThatIllegalArgumentException().isThrownBy(() ->
                book.execute(new BigDecimal(PARTIAL_QUANTITY), getLimit("18999"))
            );
        }
    }


    private OemsRequest buyMarketOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.BUY)
            .setOrderType(OemsOrderType.MARKET)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private OemsRequest sellMarketOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.SELL)
            .setOrderType(OemsOrderType.MARKET)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private OemsRequest buyLimitOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.BUY)
            .setOrderType(OemsOrderType.LIMIT)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private OemsRequest sellLimitOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.SELL)
            .setOrderType(OemsOrderType.LIMIT)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private OemsRequest buyStopOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.BUY)
            .setOrderType(OemsOrderType.STOP)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private OemsRequest sellStopOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.SELL)
            .setOrderType(OemsOrderType.STOP)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private Execution execution(OemsRequest order, String lastQtyStr, String lastPriceStr) {
        BigDecimal lastQty = new BigDecimal(lastQtyStr);
        BigDecimal lastPrice = new BigDecimal(lastPriceStr);
        BigDecimal orderQty = new BigDecimal(order.getQuantity());
        BigDecimal leavesQty = orderQty.subtract(lastQty);

        return new Execution(order, lastQty, leavesQty, lastPrice, lastPrice);
    }

    private BigDecimal decimal(String value) {
        return new BigDecimal(value);
    }
}
