package io.wyden.executionengine.service.referencedata;

import io.wyden.executionengine.infrastructure.webclient.WebClientInstance;
import io.wyden.published.referencedata.PortfolioListSnapshot;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;

import static org.springframework.util.CollectionUtils.isEmpty;

@Component
public class ReferenceDataHttpClient {

    private final WebClient webClient;
    private final String getPortfoliosByIdUrl;
    private final RestTemplate restTemplate;

    public ReferenceDataHttpClient(final WebClientInstance webClientInstance,
                                   final @Value("${reference.data.get.portfolios.by.id.url}") String getPortfoliosByIdUrl,
                                   RestTemplate restTemplate) {
        this.webClient = webClientInstance.getWebClient();
        this.getPortfoliosByIdUrl = getPortfoliosByIdUrl;
        this.restTemplate = restTemplate;
    }

    public PortfolioListSnapshot getPortfolioByIdFlat(List<String> portfolioIds) {
        if (isEmpty(portfolioIds)) {
            return PortfolioListSnapshot.newBuilder().build();
        }

        GetPortfoliosRequestDto request = new GetPortfoliosRequestDto("", portfolioIds, List.of(), null, "");

        return restTemplate.postForObject(getPortfoliosByIdUrl, request, PortfolioListSnapshot.class);
    }

    public Mono<PortfolioListSnapshot> getPortfolioById(List<String> portfolioIds) {
        if (isEmpty(portfolioIds)) {
            return Mono.empty();
        }

        GetPortfoliosRequestDto request = new GetPortfoliosRequestDto("", portfolioIds, List.of(), null, "");

        return webClient
            .post()
            .uri(getPortfoliosByIdUrl)
            .bodyValue(request)
            .accept(MediaType.APPLICATION_PROTOBUF)
            .retrieve()
            .bodyToMono(PortfolioListSnapshot.class);
    }
}
