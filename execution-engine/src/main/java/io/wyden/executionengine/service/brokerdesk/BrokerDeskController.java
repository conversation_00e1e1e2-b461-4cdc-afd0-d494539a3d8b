package io.wyden.executionengine.service.brokerdesk;

import io.wyden.executionengine.service.utils.ProtobufUtils;
import io.wyden.published.brokerdesk.TradingEngineConfig;
import org.slf4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.Collection;

import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.http.MediaType.APPLICATION_PROTOBUF_VALUE;

@RestController
public class BrokerDeskController {

    private static final Logger LOGGER = getLogger(BrokerDeskController.class);

    private final BrokerDeskConfigService brokerDeskConfigService;
    private final BrokerDeskFactory brokerDeskFactory;

    public BrokerDeskController(BrokerDeskConfigService brokerDeskConfigService, BrokerDeskFactory brokerDeskFactory) {
        this.brokerDeskConfigService = brokerDeskConfigService;
        this.brokerDeskFactory = brokerDeskFactory;
    }

    @GetMapping(value = "/trading-engine", produces = APPLICATION_PROTOBUF_VALUE)
    public Flux<TradingEngineConfig> getBrokerDeskConfig() {
        LOGGER.info("Retrieving trading engine config");
        Collection<TradingEngineConfig> tradingEngineConfig = brokerDeskConfigService.getConfigs();
        return Flux.fromIterable(tradingEngineConfig);
    }

    @PutMapping("/trading-engine")
    public ResponseEntity<String> updateBrokerDeskConfig(@RequestBody TradingEngineConfig config) {
        LOGGER.info("Updating trading engine config: {}", ProtobufUtils.shortDebugString(config));
        try {
            brokerDeskConfigService.updateConfig(config);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            LOGGER.error("Failed to update trading engine using config: ({}). Reason: ({})", config, e.getMessage(), e);
            return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(e.getMessage());
        }
    }

    @PostMapping("/trading-engine")
    public ResponseEntity<String> createBrokerDeskTradingEngine(@RequestBody TradingEngineConfig config) {
        LOGGER.info("Creating new trading engine using config: {}", ProtobufUtils.shortDebugString(config));
        try {
            brokerDeskFactory.createBrokerDesk(config);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            LOGGER.error("Failed to create new trading engine using config: ({}). Reason: ({})", config, e.getMessage(), e);
            return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(e.getMessage());
        }
    }

    @DeleteMapping("/trading-engine")
    public ResponseEntity<String> deleteBrokerDeskTradingEngine(@RequestParam String instrumentId, @RequestParam String counterPortfolioId, @RequestParam(required = false) String id) {
        LOGGER.info("Removing trading engine: instrumentId={}, counterPortfolioId={}, id={}", instrumentId, counterPortfolioId, id);
        try {
            if (id != null) {
                brokerDeskFactory.deleteBrokerDesk(id);
            } else {
                brokerDeskFactory.deleteBrokerDesk(instrumentId, counterPortfolioId);
            }
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            LOGGER.error("Failed to remove trading engine for: instrumentId({}). Reason: ({})", instrumentId, e.getMessage(), e);
            return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(e.getMessage());
        }
    }
}
