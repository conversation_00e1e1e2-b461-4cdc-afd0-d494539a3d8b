package io.wyden.quoting.engine.io.aeron;

import io.aeron.Publication;
import io.aeron.cluster.client.AeronCluster;
import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.quoting.engine.service.aeron.BulkOrderLeg;
import io.wyden.quoting.engine.service.aeron.IdGenerator;
import io.wyden.sbe.ApiPlaceBulkOrderDecoder;
import io.wyden.sbe.BulkOrderType;
import io.wyden.sbe.MessageHeaderDecoder;
import io.wyden.sbe.OrderType;
import org.agrona.ExpandableDirectByteBuffer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ExchangeCoreClientTest {

    @Mock
    private AeronCluster cluster;
    
    @Mock
    private IdGenerator idGenerator;
    
    @Mock
    private Telemetry telemetry;
    
    @Mock
    private MeterRegistry meterRegistry;
    
    @Captor
    private ArgumentCaptor<ExpandableDirectByteBuffer> bufferCaptor;
    
    @Captor
    private ArgumentCaptor<Integer> offsetCaptor;
    
    @Captor
    private ArgumentCaptor<Integer> lengthCaptor;

    private ExchangeCoreClient exchangeCoreClient;
    private static final String INSTRUMENT_ID = "BTCUSD@FOREX@ZiutexExchange-quoted";
    private static final int MATCHING_ENGINE_ID = 123;
    private static final long CLOB_UID = 9999L;

    private final MessageHeaderDecoder headerDecoder = new MessageHeaderDecoder();
    private final ApiPlaceBulkOrderDecoder bulkOrderDecoder = new ApiPlaceBulkOrderDecoder();

    @BeforeEach
    void setUp() {
        when(telemetry.getMeterRegistry()).thenReturn(meterRegistry);
        when(idGenerator.nextId()).thenReturn(1L);
        when(cluster.offer(any(ExpandableDirectByteBuffer.class), anyInt(), anyInt())).thenReturn(1L);
        
        exchangeCoreClient = new ExchangeCoreClient(cluster, idGenerator, telemetry);
    }

    @Test
    void concurrentBulkOrderCommandsShouldNotCorruptMessages() throws InterruptedException {
        // given
        int numThreads = 5;
        int numIterations = 20;
        CountDownLatch latch = new CountDownLatch(numThreads);
        ExecutorService executor = Executors.newFixedThreadPool(numThreads);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        // Create different order sets for each thread
        List<List<BulkOrderLeg>> bidsList = new ArrayList<>();
        List<List<BulkOrderLeg>> asksList = new ArrayList<>();
        
        for (int i = 0; i < numThreads; i++) {
            final int threadId = i;
            List<BulkOrderLeg> bids = List.of(
                new BulkOrderLeg(50000 + threadId, 50),
                new BulkOrderLeg(55000 + threadId, 30),
                new BulkOrderLeg(60000 + threadId, 10)
            );
            List<BulkOrderLeg> asks = List.of(
                new BulkOrderLeg(65000 + threadId, 20),
                new BulkOrderLeg(70000 + threadId, 100)
            );
            bidsList.add(bids);
            asksList.add(asks);
        }

        // when
        for (int i = 0; i < numThreads; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < numIterations; j++) {
                        exchangeCoreClient.sendPlaceBulkOrderCommand(
                            INSTRUMENT_ID,
                            MATCHING_ENGINE_ID,
                            CLOB_UID,
                            0.1f,
                            BulkOrderType.REPLACE,
                            OrderType.GTC,
                            bidsList.get(threadId),
                            asksList.get(threadId)
                        );
                        Thread.sleep(5);
                    }
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                } finally {
                    latch.countDown();
                }
            });
        }

        // Wait for all threads to complete
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();

        // then
        assertThat(successCount.get()).isEqualTo(numThreads);
        assertThat(failureCount.get()).isEqualTo(0);

        // Verify that all messages were sent correctly
        int expectedMessages = numThreads * numIterations;
        
        // Capture all sent buffers
        verify(cluster, times(expectedMessages)).offer(bufferCaptor.capture(), offsetCaptor.capture(), lengthCaptor.capture());
        List<ExpandableDirectByteBuffer> buffers = bufferCaptor.getAllValues();
        List<Integer> offsets = offsetCaptor.getAllValues();
        List<Integer> lengths = lengthCaptor.getAllValues();

        // Decode and verify each message
        for (int i = 0; i < buffers.size(); i++) {
            ExpandableDirectByteBuffer buffer = buffers.get(i);
            int offset = offsets.get(i);
            int length = lengths.get(i);

            // Decode header
            headerDecoder.wrap(buffer, offset);
            assertThat(headerDecoder.blockLength()).isGreaterThan(0);
            assertThat(headerDecoder.templateId()).isEqualTo(ApiPlaceBulkOrderDecoder.TEMPLATE_ID);

            // Decode message
            bulkOrderDecoder.wrap(buffer, offset + headerDecoder.encodedLength(), 
                headerDecoder.blockLength(), headerDecoder.version());

            // Verify message content
            assertThat(bulkOrderDecoder.symbol()).isEqualTo(MATCHING_ENGINE_ID);
            assertThat(bulkOrderDecoder.uid()).isEqualTo(CLOB_UID);
            assertThat(bulkOrderDecoder.bulkOrderType()).isEqualTo(BulkOrderType.REPLACE);
            assertThat(bulkOrderDecoder.orderType()).isEqualTo(OrderType.GTC);

            // Verify bids
            List<BulkOrderLeg> decodedBids = new ArrayList<>();
            ApiPlaceBulkOrderDecoder.BidsDecoder bidsDecoder = bulkOrderDecoder.bids();
            while (bidsDecoder.hasNext()) {
                bidsDecoder.next();
                decodedBids.add(new BulkOrderLeg(bidsDecoder.price(), bidsDecoder.size()));
            }
            assertThat(decodedBids).hasSize(3);

            // Verify asks
            List<BulkOrderLeg> decodedAsks = new ArrayList<>();
            ApiPlaceBulkOrderDecoder.AsksDecoder asksDecoder = bulkOrderDecoder.asks();
            while (asksDecoder.hasNext()) {
                asksDecoder.next();
                decodedAsks.add(new BulkOrderLeg(asksDecoder.price(), asksDecoder.size()));
            }
            assertThat(decodedAsks).hasSize(2);

            // Find the original order set that matches this message
            int threadId = -1;
            for (int t = 0; t < numThreads; t++) {
                if (decodedBids.equals(bidsList.get(t)) && decodedAsks.equals(asksList.get(t))) {
                    threadId = t;
                    break;
                }
            }
            assertThat(threadId).isNotEqualTo(-1);
        }
    }
} 