package io.wyden.quoting.engine.service.gapmonitor;

import io.wyden.published.brokerdesk.CalendarEntry;
import io.wyden.published.common.TimeInAWeek;
import io.wyden.published.common.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.Disposable;
import reactor.core.publisher.Sinks;

import java.time.Clock;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public class GapMonitor {

    private static final Logger LOGGER = LoggerFactory.getLogger(GapMonitor.class);
    private enum Tick { TICK }

    private final Runnable runOnGap;
    private final Clock clock;
    private final Sinks.Many<Tick> sink;
    private final List<Disposable> runningMonitors = new ArrayList<>();
    private final AtomicBoolean gapTriggered = new AtomicBoolean(false);

    public GapMonitor(Runnable runOnGap, List<CalendarEntry> calendarEntries, Clock clock) {
        this.runOnGap = runOnGap;
        this.clock = clock;
        this.sink = Sinks.many().multicast().directBestEffort();
        initialize(calendarEntries);
    }

    private void initialize(List<CalendarEntry> calendarEntries) {
        calendarEntries
            .stream()
            .filter(this::isValid)
            .forEach(entry -> {
                    Duration ttl = getDuration(entry);
                    Disposable calendarEntryMonitor = sink.asFlux()
                        .window(ttl)
                        .flatMap(timeframe -> timeframe
                            .collectList()
                            .doOnNext(collectedInTimeframe -> {
                                if (collectedInTimeframe.isEmpty() && isActive(entry)) {
                                    handleGap(entry);
                                }
                            }))
                        .subscribe();
                    runningMonitors.add(calendarEntryMonitor);
                    LOGGER.info("Registered GapMonitor for calendar entry: {}", entry);
                }
            );
    }

    private boolean isValid(CalendarEntry ce) {
        return ce.getQuoteTtl() != 0 && isValid(ce.getQuoteTtlUnit());
    }

    private boolean isValid(TimeUnit quoteTtlUnit) {
        return quoteTtlUnit != TimeUnit.UNRECOGNIZED && quoteTtlUnit != TimeUnit.TIME_UNIT_UNSPECIFIED;
    }

    private void handleGap(CalendarEntry entry) {
        if (gapTriggered.compareAndSet(false, true)) {
            LOGGER.warn("Refresh gap detected for QuoteTTL calendar entry: {}", entry);
            runOnGap.run();
        }
    }

    private boolean isActive(CalendarEntry entry) {
        TimeInAWeek from = entry.getFrom();
        TimeInAWeek to = entry.getTo();
        return CalendarUtil.isEntryActive(from, to, ZonedDateTime.now(clock));
    }

    private Duration getDuration(CalendarEntry entry) {
        return switch (entry.getQuoteTtlUnit()) {
            case MILLISECONDS -> Duration.of(entry.getQuoteTtl(), ChronoUnit.MILLIS);
            case SECONDS -> Duration.of(entry.getQuoteTtl(), ChronoUnit.SECONDS);
            case MINUTES -> Duration.of(entry.getQuoteTtl(), ChronoUnit.MINUTES);
            case HOURS -> Duration.of(entry.getQuoteTtl(), ChronoUnit.HOURS);
            case DAYS -> Duration.of(entry.getQuoteTtl(), ChronoUnit.DAYS);
            case UNRECOGNIZED, TIME_UNIT_UNSPECIFIED -> throw new IllegalArgumentException("Unknown TTL Unit: " + entry.getQuoteTtlUnit());
        };
    }

    public void tick() {
        sink.tryEmitNext(Tick.TICK);
        gapTriggered.set(false);
    }

    public void dispose() {
        runningMonitors.forEach(Disposable::dispose);
    }
}
