package io.wyden.accessgateway.config;

import org.keycloak.admin.client.Keycloak;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.autoconfigure.health.ConditionalOnEnabledHealthIndicator;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnEnabledHealthIndicator("realm")
public class RealmHealthIndicator extends AbstractHealthIndicator {

    private final Keycloak keycloak;
    private final String realmName;
    private final String clientId;

    public RealmHealthIndicator(Keycloak keycloak,
                                @Value("${keycloak.realm}") String realmName,
                                @Value("${spring.security.oauth2.client.registration.keycloak.client-id}") String clientId) {
        this.keycloak = keycloak;
        this.realmName = realmName;
        this.clientId = clientId;
    }

    @Override
    protected void doHealthCheck(Health.Builder builder) {
        try {
            keycloak.realm(realmName).clients().findByClientId(clientId);
            builder.up();
        } catch (Exception ex) {
            builder.down()
                .withDetail("realmHealthIndicator", false)
                .withException(ex);
        }
    }
}
