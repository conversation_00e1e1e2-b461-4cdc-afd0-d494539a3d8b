package io.wyden.referencedata.graphql;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.referencedata.utils.AccessGatewayMockClient;
import io.wyden.referencedata.utils.WithMockCustomUser;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.test.tester.GraphQlTester;

import java.util.List;
import java.util.Map;

class ReferenceDataGqlTest extends GraphQLTestBase {

    @Autowired
    AccessGatewayMockClient accessGatewayMockClient;


    @BeforeEach
    void clean() {
        permissions.clear();
    }

    @WithMockCustomUser
    @Test
    void getVenueList_shouldWorkWithoutPermission() {
        accessGatewayMockClient.revokePermissions();
        GraphQlTester.Response response = executeFile("queryVenueList", Map.of());
        response.errors().verify();
    }

    @WithMockCustomUser
    @Test
    void createVenue_shouldSucceed() {
        accessGatewayMockClient.grantPermissions(new PermissionDto("venue", "create"));
        GraphQlTester.Response response = executeFile("mutationCreateVenue", Map.of("name", "v1", "type", "STREET"));
        response.errors().verify();
    }

    @WithMockCustomUser
    @Test
    void getVenueAccountList_shouldSucceed() {
        accessGatewayMockClient.grantPermissions(new PermissionDto("venue.account", "read"));

        Map<String, Object> input = Map.of(
            "venueAccountId", "venue-1"
        );
        GraphQlTester.Response response = executeFile("queryVenueAccountList", Map.of("input", input));
        response.errors().verify();
    }

    @WithMockCustomUser
    @Test
    void getVenueAccount_shouldSucceed() {
        accessGatewayMockClient.grantPermissions(new PermissionDto("venue.account", "read"));

        GraphQlTester.Response response = executeFile("queryVenueAccount", Map.of("id", "account-123"));
        response.errors().verify();
    }

    @WithMockCustomUser
    @Test
    void getVenueAccountPositions_shouldSucceed() {
        accessGatewayMockClient.grantPermissions(new PermissionDto("venue.account", "read"));

        GraphQlTester.Response response = executeFile("queryVenueAccountPositions", Map.of("id", "account-123"));
        response.errors().verify();
    }

    @WithMockCustomUser
    @Test
    void venueAccountNameCheck_shouldSucceed() {
        GraphQlTester.Response response = executeFile("queryVenueAccountNameCheck", Map.of("name", "Test Account"));
        response.errors().verify();
    }

    @WithMockCustomUser
    @Test
    void venueAccountCreate_shouldSucceed() {
        accessGatewayMockClient.grantPermissions(new PermissionDto("venue.account", "create"));

        Map<String, Object> input = Map.of(
            "venueAccountId", "account-123",
            "venueAccountName", "Updated Account",
            "connectorId", "connector-xyz",
            "venueAccountType", "EXCHANGE",
            "vostroNostro", "VOSTRO",
            "correlationObject", "corr-001"
        );
        GraphQlTester.Response response = executeFile("mutationVenueAccountCreate", Map.of("input", input));
        response.errors().verify();
    }

    @WithMockCustomUser
    @Test
    void venueAccountUpdate_shouldSucceed() {
        accessGatewayMockClient.grantPermissions(new PermissionDto("venue.account", "manage"));
        Map<String, Object> input = Map.of(
            "venueAccountId", "account-123",
            "venueAccountName", "Updated Account",
            "connectorId", "connector-xyz",
            "venueAccountType", "EXCHANGE",
            "vostroNostro", "VOSTRO",
            "correlationObject", "corr-001"
        );

        GraphQlTester.Response response = executeFile("mutationVenueAccountUpdate", Map.of("input", input));
        response.errors().verify();
    }

    @WithMockCustomUser
    @Test
    void venueAccountAction_shouldSucceed() {
        accessGatewayMockClient.grantPermissions(new PermissionDto("venue.account", "manage"));

        GraphQlTester.Response response = executeFile("mutationVenueAccountAction", Map.of(
            "id", "account-123",
            "type", "ARCHIVE"
        ));
        response.errors().verify();
    }

    @WithMockCustomUser
    @Test
    void venueAccountPositionCreate_shouldSucceed() {
        accessGatewayMockClient.grantPermissions(new PermissionDto("venue.account", "manage"));

        Map<String, Object> input = Map.of(
            "id", "account-123",
            "asset", "BTC",
            "connectorId", "1"
        );
        GraphQlTester.Response response = executeFile("mutationVenueAccountPositionCreate", Map.of("venueAccountId", "account-123", "input", input));
        response.errors().verify();
    }

    @WithMockCustomUser
    @Test
    void venueAccountPositionUpdate_shouldSucceed() {
        accessGatewayMockClient.grantPermissions(new PermissionDto("venue.account", "manage"));

        Map<String, Object> input = Map.of(
            "id", "account-123",
            "asset", "BTC",
            "connectorId", "connector-xyz",
            "metadata", List.of(
                Map.of("key", "source", "value", "manual"),
                Map.of("key", "region", "value", "EU")
            )
        );

        GraphQlTester.Response response = executeFile("mutationVenueAccountPositionUpdate", Map.of("venueAccountId", "account-123", "input", input));
        response.errors().verify();
    }

    @WithMockCustomUser
    @Test
    void venueAccountPositionDelete_shouldSucceed() {
        accessGatewayMockClient.grantPermissions(new PermissionDto("venue.account", "manage"));

        GraphQlTester.Response response = executeFile("mutationVenueAccountPositionDelete", Map.of("venueAccountId", "account-123", "asset", "BTC"));
        response.errors().verify();
    }


    @WithMockCustomUser
    @Test
    void createVenue_needsPermission_shouldDenyAccess() {
        accessGatewayMockClient.revokePermissions();
        GraphQlTester.Response response = executeFile("mutationCreateVenue", Map.of("name", "v1", "type", "STREET"));
        assertExpectedForbiddenError(response);
    }

    private static void assertExpectedForbiddenError(GraphQlTester.Response response) {
        response.errors().expect(e -> e.getErrorType().toString().equals("UNAUTHORIZED"));
    }
}

