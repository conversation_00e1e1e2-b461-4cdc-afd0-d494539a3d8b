package io.wyden.referencedata.permission;

import io.wyden.accessgateway.client.permission.WydenRole;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.HashSet;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.EMPTY;

public class WydenAuthenticationToken extends JwtAuthenticationToken {

    private static final String PREFERRED_USERNAME_FIELD_NAME = "preferred_username";

    private final String userId;
    private final String clientId;
    private final Set<String> groups = new HashSet<>();
    private final Set<WydenRole> roles = new HashSet<>();

    public WydenAuthenticationToken(Jwt jwt) {
        super(jwt, Set.of());
        this.userId = jwt.getSubject();
        this.clientId = jwt.getClaimAsString(PREFERRED_USERNAME_FIELD_NAME);
        this.groups.addAll(getGroups(jwt));
        this.roles.addAll(getRoles(jwt));
    }

    public WydenAuthenticationToken(String clientId) {
        this(clientId, Set.of(), Set.of());
    }

    public WydenAuthenticationToken(String clientId, Set<String> groups, Set<WydenRole> roles) {
        super(empty(clientId), Set.of());
        this.userId = clientId;
        this.clientId = clientId;
        this.groups.addAll(groups);
        this.roles.addAll(roles);
    }

    public String getUserId() {
        return userId;
    }

    public String getClientId() {
        if (clientId == null) {
            throw new NoSuchElementException("Cannot find 'preferred_username' field in jwt token");
        }
        return clientId;
    }

    public String getClientIdOrEmpty() {
        return ObjectUtils.firstNonNull(clientId, EMPTY);
    }

    public Set<String> getGroups() {
        return groups;
    }

    public Set<WydenRole> getRoles() {
        return roles;
    }

    @SuppressWarnings("unchecked")
    private Set<String> getGroups(Jwt token) {
        return Optional.ofNullable(token)
            .map(Jwt::getClaims)
            .map(m -> (Collection<String>) m.get("groups"))
            .map(HashSet::new)
            .orElse(new HashSet<>());
    }

    private Set<WydenRole> getRoles(Jwt token) {
        return getRolesAsString(token).stream()
            .filter(WydenRole::isWydenRole)
            .map(WydenRole::valueOf)
            .collect(Collectors.toSet());
    }

    @SuppressWarnings("unchecked")
    private Collection<String> getRolesAsString(Jwt token) {
        return Optional.ofNullable(token)
            .map(Jwt::getClaims)
            .map(m -> (Map<String, Collection<String>>) m.get("realm_access"))
            .map(m -> m.get("roles"))
            .orElse(new HashSet<>());
    }

    private static Jwt empty(String username) {
        return Jwt.withTokenValue("test-token")
            .issuedAt(Instant.now())
            .expiresAt(Instant.now().plus(Duration.ofHours(1)))
            .header(PREFERRED_USERNAME_FIELD_NAME, username)
            .claim(PREFERRED_USERNAME_FIELD_NAME, username)
            .build();
    }
}
