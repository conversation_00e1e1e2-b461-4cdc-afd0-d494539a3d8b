package io.wyden.referencedata.util;

import io.wyden.published.referencedata.AssetClass;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.Venue;
import io.wyden.published.referencedata.instrumentmodification.InstrumentToModify;

public class InstrumentsUtil {

    private InstrumentsUtil() {
    }

    public static String createIdentifier(InstrumentToModify instrument) {
        return instrument.getForexSpotProperties().getBaseCurrency() +
            instrument.getBaseInstrument().getQuoteCurrency() +
            "@" +
            instrument.getBaseInstrument().getAssetClass() +
            "@" +
            instrument.getBaseInstrument().getVenueName();
    }

    public static String createIdentifier(Instrument instrument, Venue venue) {
        return instrument.getForexSpotProperties().getBaseCurrency() +
            instrument.getBaseInstrument().getQuoteCurrency() +
            "@" +
            instrument.getBaseInstrument().getAssetClass() +
            "@" +
            (!venue.getName().isBlank() ? venue.getName() : instrument.getBaseInstrument().getVenueName());
    }

    public static String createSymbol(InstrumentToModify instrument) {
        if (instrument.getBaseInstrument().getAssetClass() == AssetClass.FOREX) {
            return createForexSymbol(instrument.getForexSpotProperties().getBaseCurrency(), instrument.getBaseInstrument().getQuoteCurrency());
        } else {
            return createIdentifier(instrument);
        }
    }

    public static String createSymbol(Instrument instrument, Venue venue) {
        if (instrument.getBaseInstrument().getAssetClass() == AssetClass.FOREX) {
            return createForexSymbol(instrument.getForexSpotProperties().getBaseCurrency(), instrument.getBaseInstrument().getQuoteCurrency());
        } else {
            return createIdentifier(instrument, venue);
        }
    }

    private static String createForexSymbol(String baseCurrency, String quoteCurrency) {
        return baseCurrency + quoteCurrency;
    }
}
