package io.wyden.referencedata.infrastructure.security;

import io.wyden.accessgateway.client.apikey.dto.AuthResponseDto;
import io.wyden.accessgateway.client.permission.WydenRole;
import io.wyden.referencedata.permission.WydenAuthenticationToken;

import java.util.Set;

public record User(String clientId, Set<String> groups, Set<WydenRole> roles) {

    public static User user(WydenAuthenticationToken token) {
        return new User(token.getClientId(), token.getGroups(), token.getRoles());
    }

    public static User user(AuthResponseDto authResponseDto) {
        return new User(authResponseDto.username(), authResponseDto.groups(), authResponseDto.roles());
    }

    public static User user(String clientId) {
        return new User(clientId, Set.of(), Set.of());
    }
}
