package io.wyden.referencedata.mapper;

import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.instrumentmodification.BaseInstrumentToModify;
import io.wyden.published.referencedata.instrumentmodification.InstrumentIdentifiersToModify;
import io.wyden.published.referencedata.instrumentmodification.InstrumentToModify;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.Instant;

class InstrumentToModifyMapperTest {

    private static final String INSTRUMENT_ID_2 = "instrumentId2";
    private static final String SYMBOL_2 = "symbol2";
    private static final Instant ARCHIVED_AT = Instant.parse("2020-01-01T00:00:00.00Z");
    private static final String SYMBOL = "symbol";
    private static final String VENUE_NAME = "venueName";
    private static final String INSTRUMENT_ID = "instrumentId";
    private static final String ADAPTER_TICKER = "adapterTicker";

    @Test
    void map_parametersInInstrumentToModifyShouldTakePrecedenceOverOnesProvidedInMethod() {
        //given
        InstrumentToModify instrumentToModify = InstrumentToModify.newBuilder()
            .setBaseInstrument(BaseInstrumentToModify.newBuilder()
                .setSymbol(SYMBOL)
                .setVenueName(VENUE_NAME)
                .build())
            .setInstrumentIdentifiers(InstrumentIdentifiersToModify.newBuilder()
                .setInstrumentId(INSTRUMENT_ID)
                .setAdapterTicker(ADAPTER_TICKER)
                .build())
            .build();

        //when
        Instrument instrument = InstrumentToModifyMapper.map(instrumentToModify, ARCHIVED_AT, INSTRUMENT_ID_2, SYMBOL_2);

        //then
        Assertions.assertThat(instrument.getBaseInstrument().getSymbol()).isEqualTo(SYMBOL);
        Assertions.assertThat(instrument.getBaseInstrument().getVenueName()).isEqualTo(VENUE_NAME);
        Assertions.assertThat(instrument.getInstrumentIdentifiers().getInstrumentId()).isEqualTo(INSTRUMENT_ID);
        Assertions.assertThat(instrument.getInstrumentIdentifiers().getAdapterTicker()).isEqualTo(ADAPTER_TICKER);
    }

    @Test
    void map_whenParametersInInstrumentToModifyAreMissingTheOnesInMethodShouldBeUser() {
        //given
        InstrumentToModify instrumentToModify = InstrumentToModify.newBuilder()
            .setBaseInstrument(BaseInstrumentToModify.newBuilder()
                .setVenueName(VENUE_NAME)
                .build())
            .setInstrumentIdentifiers(InstrumentIdentifiersToModify.newBuilder()
                .setAdapterTicker(ADAPTER_TICKER)
                .build())
            .build();

        //when
        Instrument instrument = InstrumentToModifyMapper.map(instrumentToModify, ARCHIVED_AT, INSTRUMENT_ID_2, SYMBOL_2);

        //then
        Assertions.assertThat(instrument.getBaseInstrument().getSymbol()).isEqualTo(SYMBOL_2);
        Assertions.assertThat(instrument.getBaseInstrument().getVenueName()).isEqualTo(VENUE_NAME);
        Assertions.assertThat(instrument.getInstrumentIdentifiers().getInstrumentId()).isEqualTo(INSTRUMENT_ID_2);
        Assertions.assertThat(instrument.getInstrumentIdentifiers().getAdapterTicker()).isEqualTo(ADAPTER_TICKER);
    }
}