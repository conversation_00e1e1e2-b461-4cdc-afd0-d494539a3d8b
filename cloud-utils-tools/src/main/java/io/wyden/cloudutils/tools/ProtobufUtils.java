package io.wyden.cloudutils.tools;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.TextFormat;
import com.google.protobuf.util.JsonFormat;

import java.math.BigDecimal;

/**
 * Utility class providing helper methods for working with Protocol Buffers,
 * including conversion to and from string formats as well as other utility
 * operations specific to Protocol Buffers.
 *
 * This class is not intended to be instantiated.
 */
public final class ProtobufUtils {

    private ProtobufUtils() {
        // Empty
    }

    /**
     * Returns a short debug string representation for the provided Protocol Buffer message.
     * The string representation is designed to be human-readable, compact, and useful for logging or debugging.
     *
     * @param message a {@code MessageOrBuilder} object representing the Protocol Buffer message or builder to be converted into a short debug string.
     *                If {@code null}, the method will return the string "null".
     * @return a {@code String} containing the short debug string representation of the provided Protocol Buffer message,
     *         or "null" if the input message is null.
     */
    public static String shortDebugString(MessageOrBuilder message) {
        return message != null ? TextFormat.shortDebugString(message) : "null";
    }

    /**
     * Converts a {@code BigDecimal} into its Protocol Buffer-compatible string representation.
     * The conversion removes trailing zeros and utilizes the plain string format of the {@code BigDecimal}.
     *
     * @param bigDecimal the {@code BigDecimal} object to be converted. If {@code null}, the method will return {@code null}.
     * @return a {@code String} representing the Protocol Buffer-compatible representation of the {@code BigDecimal},
     *         or {@code null} if the input is {@code null}.
     */
    public static String toProtoString(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return null;
        }

        return bigDecimal.stripTrailingZeros().toPlainString();
    }

    /**
     * Converts the given Protocol Buffer message into its JSON string representation.
     *
     * The JSON string is generated with the following characteristics:
     * - Preserves Protocol Buffer field names without converting them to camelCase.
     * - Ensures that map fields within the message are sorted by their keys.
     *
     * @param message the Protocol Buffer {@code Message} object to be converted.
     *                Must not be null.
     * @return a {@code String} containing the JSON representation of the provided message.
     * @throws RuntimeException if the message cannot be converted to a JSON string due to an internal error.
     */
    public static String messageToString(Message message) {
        try {
            return JsonFormat.printer()
                .preservingProtoFieldNames()
                .sortingMapKeys()
                .print(message);
        } catch (InvalidProtocolBufferException ex) {
            throw new RuntimeException("Unable to convert protobuf to string", ex);
        }
    }

    /**
     * Converts a JSON string representation of a Protocol Buffer message into
     * an updated Protocol Buffer message instance, based on the input message.
     *
     * This method parses the provided JSON string and merges its contents into
     * the builder of the given Protocol Buffer message. Unknown fields in the
     * JSON string are ignored during parsing.
     *
     * @param message the Protocol Buffer {@code Message} that serves as the base
     *                for merging the contents of the JSON string. This message
     *                must not be {@code null}.
     * @param updateJson a {@code String} containing the JSON representation of
     *                   the Protocol Buffer to merge into the provided message
     *                   builder. This string must not be {@code null}.
     * @return the updated Protocol Buffer {@code Message} after merging the
     *         contents of the provided JSON string.
     * @throws RuntimeException if the JSON string cannot be parsed or merged
     *         into the Protocol Buffer message due to invalid formatting or
     *         other errors.
     */
    public static Message stringToMessage(Message message, String updateJson) {
        try {
            Message.Builder builder = message.toBuilder();
            JsonFormat.parser()
                .ignoringUnknownFields()
                .merge(updateJson, builder);
            return builder.build();
        } catch (InvalidProtocolBufferException ex) {
            throw new RuntimeException("Unable to convert string to protobuf", ex);
        }
    }
}
