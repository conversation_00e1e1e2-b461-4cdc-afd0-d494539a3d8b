package io.wyden.apiserver.fix;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;

@SpringBootApplication(exclude = {
	DataSourceAutoConfiguration.class,
	DataSourceTransactionManagerAutoConfiguration.class,
	HibernateJpaAutoConfiguration.class
})
@Import(value = {io.wyden.cloud.utils.spring.fluentd.FluentdAppender.class})
public class FixApiMarketDataApplication {

	private static final Logger LOGGER = LoggerFactory.getLogger(FixApiMarketDataApplication.class);


	public static void main(String[] args) {
		printMemorySettings();
		SpringApplication.run(FixApiMarketDataApplication.class, args);
	}

	private static void printMemorySettings() {
		int mb = 1024 * 1024;
		MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
		long xmx = memoryMXBean.getHeapMemoryUsage().getMax() / mb;
		long xms = memoryMXBean.getHeapMemoryUsage().getInit() / mb;
		LOGGER.info("Initial Memory : {}mb", xms);
		LOGGER.info("Max Memory: {} mb", xmx);
	}

}
