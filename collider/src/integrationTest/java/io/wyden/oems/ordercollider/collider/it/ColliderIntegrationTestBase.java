package io.wyden.oems.ordercollider.collider.it;

import com.hazelcast.config.Config;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.oems.ordercollider.collider.infrastructure.rabbit.RabbitDestinations;
import io.wyden.oems.ordercollider.collider.it.util.Client;
import io.wyden.oems.ordercollider.collider.it.util.Connector;
import io.wyden.oems.ordercollider.collider.it.util.TestingData;
import io.wyden.oems.ordercollider.collider.service.connector.inbound.VenueCancelDLHandler;
import io.wyden.oems.ordercollider.collider.service.connector.inbound.VenueOrderDLHandler;
import io.wyden.oems.ordercollider.collider.service.referencedata.InstrumentsRepository;
import io.wyden.oems.ordercollider.domain.map.VenueOrderStateMapConfig;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.InstrumentIdentifiers;
import io.wyden.published.referencedata.VenueType;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import org.awaitility.Awaitility;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.RabbitMQContainer;

import java.time.Duration;
import java.util.Map;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

@ContextConfiguration(initializers = ColliderIntegrationTestBase.Initializer.class,
                      classes = ColliderIntegrationTestBase.TestHazelcastConfiguration.class)
@SpringBootTest(properties = {"spring.main.allow-bean-definition-overriding=true", "logging.level.root=debug"})
@TestPropertySource(locations = "classpath:integration-test.properties")
@ExtendWith(SpringExtension.class)
@AutoConfigureWebTestClient
public abstract class ColliderIntegrationTestBase {

    static final String BAADER = "BAADER";
    static final String BITMEX = "BITMEX";
    static final String BTCUSD_BAADER = "BTCUSD@BAADER";
    public static final String BTCUSD_BITMEX_INSTRUMENT_ID = "BTCUSD@FOREX@BitMEX";
    public static final String BTCUSD_BITMEX_TICKER = "btcusd";

    static final Map<String, Instrument> INSTRUMENTS = Map.of(
            BTCUSD_BAADER, Instrument.newBuilder()
                    .setBaseInstrument(BaseInstrument.newBuilder()
                            .setVenueType(VenueType.CLIENT)
                            .setVenueName(BAADER))
                    .setInstrumentIdentifiers(InstrumentIdentifiers.newBuilder()
                            .setInstrumentId(BTCUSD_BAADER)
                            .setAdapterTicker(BTCUSD_BAADER)
                    )
                    .build(),
        BTCUSD_BITMEX_INSTRUMENT_ID, Instrument.newBuilder()
                    .setBaseInstrument(BaseInstrument.newBuilder()
                            .setVenueType(VenueType.STREET)
                            .setVenueName(BITMEX))
                    .setInstrumentIdentifiers(InstrumentIdentifiers.newBuilder()
                            .setInstrumentId(BTCUSD_BITMEX_INSTRUMENT_ID)
                            .setAdapterTicker(BTCUSD_BITMEX_TICKER)
                    )
                    .build()
    );

    static final RabbitMQContainer RABBIT_MQ = new RabbitMQContainer("rabbitmq:3.12-management")
        .withNetwork(Network.newNetwork())
        .withReuse(true);

    static {
        RABBIT_MQ.start();
        Awaitility.setDefaultPollDelay(Duration.ZERO);
        Awaitility.setDefaultPollInterval(Duration.ofMillis(10));
        Awaitility.setDefaultTimeout(Duration.ofMinutes(5));
    }

    @Autowired
    RabbitIntegrator rabbitIntegrator;

    @Autowired
    RabbitDestinations destinations;

    @Autowired
    HazelcastInstance hz;

    @Autowired
    VenueCancelDLHandler venueCancelDLHandler;

    @Autowired
    VenueOrderDLHandler venueOrderDLHandler;

    @Autowired
    InstrumentsRepository instrumentsRepository;

    Connector connector;
    Client client;
    TestingData testingData;

    String venueAccount;

    @BeforeEach
    void resetExternalServices() {
        testingData = new TestingData();
        connector = new Connector(destinations, rabbitIntegrator, testingData);
        client = new Client(destinations, rabbitIntegrator, testingData);

        venueAccount = testingData.venueAccount;
    }

    @AfterEach
    void generalTearDown() {
        VenueOrderStateMapConfig.getMap(hz).clear();

        connector.cleanup();
        connector = null;

        client.cleanup();
        client = null;
    }

    static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {

            var values = TestPropertyValues.of(
                "rabbitmq.host=" + RABBIT_MQ.getHost(),
                "rabbitmq.port=" + RABBIT_MQ.getMappedPort(5672),
                "rabbitmq.username=" + RABBIT_MQ.getAdminUsername(),
                "rabbitmq.password=" + RABBIT_MQ.getAdminPassword()
            );

            values.applyTo(applicationContext);
        }
    }

    @TestConfiguration
    static class TestHazelcastConfiguration {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance createHazelcastInstance() {
            Config config = new Config();
            config.setClusterName(UUID.randomUUID().toString());
            return Hazelcast.newHazelcastInstance(config);
        }

        @Primary
        @Bean
        InstrumentsCacheFacade instrumentsCacheFacade(HazelcastInstance hazelcastInstance) {
            IMap<String, Instrument> instruments = hazelcastInstance.getMap("instruments");
            instruments.putAll(INSTRUMENTS);
            return new InstrumentsCacheFacade(instruments, mock(Tracing.class));
        }
    }

    static void assertExecReportPendingNewFor(OemsResponse pendingNewResponse, OemsRequest order) {
        assertThat(pendingNewResponse).satisfies(response -> {
            assertThat(response.getOrderId()).isEqualTo(order.getOrderId());
            assertThat(response.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.EXECUTION_REPORT);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PENDING_NEW);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PENDING_NEW);
        });
    }

    static void assertExecReportWithStatus(OemsResponse oemsResponse, OemsRequest order, OemsOrderStatus orderStatus) {
        assertThat(oemsResponse).satisfies(response -> {
            assertThat(response.getOrderId()).isEqualTo(order.getOrderId());
            assertThat(response.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.EXECUTION_REPORT);
            assertThat(response.getOrderStatus()).isEqualTo(orderStatus);
        });
    }
}
