package io.wyden.clob.gateway.service.tracking;

public class FailureNonRecoverableException extends RuntimeException {

    public FailureNonRecoverableException() {
        super();
    }

    public FailureNonRecoverableException(String message) {
        super(message);
    }

    public FailureNonRecoverableException(String message, Throwable cause) {
        super(message, cause);
    }

    public FailureNonRecoverableException(Throwable cause) {
        super(cause);
    }
}
