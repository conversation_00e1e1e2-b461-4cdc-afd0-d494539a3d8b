package io.wyden.clob.gateway.service.audit;

import io.opentelemetry.api.trace.SpanKind;
import io.wyden.audit.client.AuditEventsClient;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.audit.AuditEventPayload;
import io.wyden.published.audit.MatchPayload;
import io.wyden.published.audit.PayloadType;
import io.wyden.published.referencedata.Instrument;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class AuditService {
    private final AuditEventsClient auditEventsClient;
    private final String applicationName;
    private final Tracing otlTracing;

    public AuditService(RabbitIntegrator rabbitIntegrator,
                        Telemetry telemetry,
                        @Value("${spring.application.name}") String applicationName) {
        this.auditEventsClient = new AuditEventsClient(rabbitIntegrator);
        this.applicationName = applicationName;
        this.otlTracing = telemetry.getTracing();
    }

    public void sendMatchAuditEvent(MatchAuditContext matchAuditContext, Instrument instrument) {
        if (matchAuditContext.taker == null && matchAuditContext.makers.isEmpty()) {
            return;
        }

        try (var ignored = otlTracing.createSpan("audit.sendmatchevent", SpanKind.INTERNAL)) {
            MatchPayload payload = MatchAuditMapper.toMatchAuditEventPayload(matchAuditContext, instrument);
            sendAuditEvent(AuditEventPayload.newBuilder()
                .setType(PayloadType.MATCH)
                .setMatchPayload(payload));
        }
    }

    private void sendAuditEvent(AuditEventPayload.Builder payload) {
        auditEventsClient.emitAuditEvent(applicationName, payload.build());
    }
}
