package io.wyden.clob.gateway.service.fsm;

import io.wyden.clob.gateway.service.aeron.model.MatchingEngineEvent;
import io.wyden.clob.gateway.service.oems.outbound.OemsResponseFactory;
import io.wyden.clob.gateway.utils.MathUtils;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.referencedata.Instrument;
import io.wyden.sbe.CommandResultCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

import static io.wyden.clob.gateway.utils.TradingMessageUtils.isCashOrder;

/**
 * Intermediate state between Start and New.
 * Order moves to PendingNew after handling new order OemsRequest and emitting VenueOrder.
 * Can transition to:
 *  - New - on NEW ExecutionReport
 *  - Rejected - on REJECTED ExecutionReport
 *  - PartialFill, Filled - on trade ExecutionReport
 *  - PendingCancel - on CANCEL OemsRequest
 */

class StatusPendingNew extends StatusNew {
    private static final Logger LOGGER = LoggerFactory.getLogger(StatusPendingNew.class);

    private static final OrderStatus instance = new StatusPendingNew();

    static OrderStatus create() {
        return instance;
    }

    @Override
    void onPlaceOrder(OrderContext context, MatchingEngineEvent.PlaceOrder placeOrder, Instrument instrument) {
        OrderState orderState = context.getOrderState();
        orderState.setOrderStatus(StatusNew.create());

        BigDecimal acceptedQuantity;
        BigDecimal placedQuantity;
        OemsRequest oemsRequest = orderState.getOemsRequest();
        if (isCashOrder(oemsRequest)) {
            BigDecimal amount = MathUtils.convertCashVolume(placeOrder.amount(), instrument);
            acceptedQuantity = amount;

            placedQuantity = MathUtils.convertCashVolume(context.getOrderState().getPlaceOrderCommand().getAmount(), instrument);

            if (placeOrder.size() > 0) {
                BigDecimal amountIncr = MathUtils.convertPrice(placeOrder.amount() / placeOrder.size(), instrument);
                orderState.setMinAmountIncrement(amountIncr);
            }
        } else {
            BigDecimal size = MathUtils.convertVolume(placeOrder.size(), instrument);
            acceptedQuantity = size;

            placedQuantity = MathUtils.convertVolume(context.getOrderState().getPlaceOrderCommand().getSize(), instrument);
        }

        orderState.setPlacedQuantity(placedQuantity);

        BigDecimal rejectedQuantity = placedQuantity.subtract(acceptedQuantity);
        LOGGER.debug("Order {} requested quantity: {}, placed: {}, accepted: {}, rejected: {}, minAmountIncrement: {}",
            oemsRequest.getOrderId(), orderState.getQuantity().toPlainString(), placedQuantity.toPlainString(), acceptedQuantity.toPlainString(), rejectedQuantity.toPlainString(), orderState.getMinAmountIncrement().toPlainString());
        if (rejectedQuantity.compareTo(BigDecimal.ZERO) > 0) {
            orderState.setRejectedQuantity(rejectedQuantity);
        }

        context.emit(OemsResponseFactory.createNew(context.getOrderState(), placeOrder.orderId()));

        super.onPlaceOrder(context, placeOrder, instrument);
    }

    @Override
    void onRejected(OrderContext context, MatchingEngineEvent.PlaceOrder placeOrder) {
        OrderState orderState = context.getOrderState();
        String reason = placeOrder.commandResultCode().name();
        orderState.setDescription(reason);
        orderState.setErrorCode(reason);
        orderState.setStatusRejected();
        context.emit(OemsResponseFactory.createRejectViaExternalRejectReport(orderState, placeOrder.orderId(), reason, placeOrder.commandResultCode() == CommandResultCode.SELF_MATCH));
    }

    @Override
    Precedence getPrecedence() {
        return Precedence.PENDING_NEW;
    }

    @Override
    OemsOrderStatus toOemsOrderStatus() {
        return OemsOrderStatus.STATUS_PENDING_NEW;
    }
}
