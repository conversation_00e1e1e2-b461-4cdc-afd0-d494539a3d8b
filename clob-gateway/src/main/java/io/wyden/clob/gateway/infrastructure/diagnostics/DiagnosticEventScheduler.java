package io.wyden.clob.gateway.infrastructure.diagnostics;

import io.wyden.clob.gateway.infrastructure.diagnostics.capabilities.WydenExchangeCapabilities;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.published.diagnostic.Capability;
import io.wyden.published.diagnostic.ConnectionState;
import io.wyden.published.diagnostic.DiagnosticEvent;
import io.wyden.published.diagnostic.Health;
import io.wyden.published.diagnostic.HealthStatus;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
public class DiagnosticEventScheduler {
    private static final Logger LOGGER = LoggerFactory.getLogger(DiagnosticEventScheduler.class);

    private final ScheduledExecutorService executorService;
    private final ConnectorStateEmitter connectorStateEmitter;
    private final ClobHealthController clobHealthController;
    private final String venueName;
    private final String venueAccountId;
    private final int heartbeatInterval;

    public DiagnosticEventScheduler(RabbitIntegrator rabbitIntegrator,
                                    ClobHealthController clobHealthController,
                                    @Value("${clob.venue.name}") String venueName,
                                    @Value("${clob.venue.account.id}") String venueAccountId,
                                    @Value("${clob.diagnostics.heartbeat}") int heartbeatInterval) {
        this.connectorStateEmitter = new ConnectorStateEmitter(rabbitIntegrator);
        this.clobHealthController = clobHealthController;
        this.venueName = venueName;
        this.venueAccountId = venueAccountId;
        this.executorService = Executors.newSingleThreadScheduledExecutor();
        this.heartbeatInterval = heartbeatInterval;
    }

    @PostConstruct
    void init() {
        startScheduler();
    }

    public void startScheduler() {
        LOGGER.info("Starting Diagnostic event heart beating with interval: {} seconds.", heartbeatInterval);
        try {
            executorService.scheduleWithFixedDelay(heartbeatTask(), 0, heartbeatInterval, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.error("Error when trying to start heart beating: {}", e.toString());
        }
    }

    private Runnable heartbeatTask() {
        return () -> {
            List<DiagnosticEvent> diagnosticEvents = getDiagnosticEvents();
            connectorStateEmitter.emit(diagnosticEvents);
        };
    }

    private List<DiagnosticEvent> getDiagnosticEvents() {
        if (clobHealthController.isApplicationHealthy()) {
            return getDiagnosticEventsListUp();
        } else {
            return getDiagnosticEventsListDown();
        }
    }

    private List<DiagnosticEvent> getDiagnosticEventsListUp() {
        List<DiagnosticEvent> diagnosticEvents = new ArrayList<>();
        WydenExchangeCapabilities.getCapabilities().getConnectorCapabilitiesList().forEach(
                capability -> diagnosticEvents.add(getDiagnosticEventForCapability(capability, "Full capability available.", HealthStatus.ALIVE)));
        return diagnosticEvents;
    }

    private List<DiagnosticEvent> getDiagnosticEventsListDown() {
        List<DiagnosticEvent> diagnosticEvents = new ArrayList<>();
        WydenExchangeCapabilities.getCapabilities().getConnectorCapabilitiesList().forEach(
                capability -> diagnosticEvents.add(getDiagnosticEventForCapability(capability, "Capability down.", HealthStatus.DEAD)));
        return diagnosticEvents;
    }

    private DiagnosticEvent getDiagnosticEventForCapability(Capability capability, String message, HealthStatus healthStatus) {
        return DiagnosticEvent.newBuilder()
                .setCapability(capability)
                .setHealth(Health.newBuilder()
                        .setMsg(message)
                        .setStatus(healthStatus)
                        .putAllConnections(mapConnectionsStateMap(capability, healthStatus))
                        .build())
                .setTimestamp(ZonedDateTime.now().toString())
                .setVenueAccount(venueAccountId)
                .setVenue(venueName)
                .build();
    }

    private Map<String, ConnectionState> mapConnectionsStateMap(Capability capability, HealthStatus healthStatus) {
        Map<String, ConnectionState> connectionStateMap = new HashMap<>();
        connectionStateMap.put(capability.name() + " session", mapHealthStatusToConnectionState(healthStatus));
        return connectionStateMap;
    }

    private ConnectionState mapHealthStatusToConnectionState(HealthStatus healthStatus) {
        return switch (healthStatus) {
            case HEALTH_STATUS_UNSPECIFIED, UNHEALTHY, DEAD, UNRECOGNIZED -> ConnectionState.DISCONNECTED;
            case ALIVE -> ConnectionState.CONNECTED;
        };
    }
}
