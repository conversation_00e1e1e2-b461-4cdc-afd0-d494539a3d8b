package io.wyden.clob.gateway.service.marketdata;

import io.wyden.sbe.MessageHeaderDecoder;
import io.wyden.sbe.MessageHeaderEncoder;
import io.wyden.sbe.OrderBookDecoder;
import io.wyden.sbe.OrderBookEncoder;
import org.agrona.ExpandableDirectByteBuffer;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class OrderBookReaderTest {
    @Test
    void shouldReadOrderBooksProperly() {
        ExpandableDirectByteBuffer returnBuffer = new ExpandableDirectByteBuffer(128);
        OrderBookEncoder orderBookEncoder = new OrderBookEncoder();
        MessageHeaderEncoder messageHeaderEncoder = new MessageHeaderEncoder();

        messageHeaderEncoder.wrap(returnBuffer, 0);
        orderBookEncoder.wrapAndApplyHeader(returnBuffer, 0, messageHeaderEncoder)
            .symbol(1)
            .timestamp(1000);

        orderBookEncoder
            .asksCount(4)
            .next().price(393017).volume(1007).orders(4)
            .next().price(500000).volume(10).orders(1)
            .next().price(510000).volume(10).orders(1)
            .next().price(530000).volume(20).orders(1);

        orderBookEncoder
            .bidsCount(3)
            .next().price(360000).volume(10).orders(1)
            .next().price(350000).volume(120).orders(5)
            .next().price(310000).volume(19).orders(1);

        orderBookEncoder.wrapAndApplyHeader(returnBuffer, 0, messageHeaderEncoder);


        OrderBookDecoder orderBookDecoder = new OrderBookDecoder();
        MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();
        orderBookDecoder.wrapAndApplyHeader(returnBuffer, 0, messageHeaderDecoder);
        OrderBookDto read = OrderBookReader.read(orderBookDecoder);

        assertThat(read.symbol()).isEqualTo(1);
        assertThat(read.timestamp()).isEqualTo(1000);
        assertThat(read.asks()).containsExactly(
            new OrderBookLevelDto(393017, 1007, 4),
            new OrderBookLevelDto(500000, 10, 1),
            new OrderBookLevelDto(510000, 10, 1),
            new OrderBookLevelDto(530000, 20, 1)
        );
        assertThat(read.bids()).containsExactly(
            new OrderBookLevelDto(360000, 10, 1),
            new OrderBookLevelDto(350000, 120, 5),
            new OrderBookLevelDto(310000, 19, 1)
        );

    }
}