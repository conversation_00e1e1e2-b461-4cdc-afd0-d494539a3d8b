package io.wyden.clob.gateway.service.aeron;

import io.wyden.clob.gateway.model.MakerTrade;
import io.wyden.clob.gateway.model.TakerTradeEvent;
import io.wyden.clob.gateway.service.aeron.model.MatchingEngineEvent;
import io.wyden.clob.gateway.service.aeron.model.ProtoMapper;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

public class ProtoMapperTest {
    @Test
    void takerTradeEvent() {
        // given
        MatchingEngineEvent.TakerTradeEvent takerTradeEvent = new MatchingEngineEvent.TakerTradeEvent(
            1,
            2,
            3,
            4,
            UUID.randomUUID().toString(),
            5,
            6,
            7,
            true,
            1234,
            List.of(
                new MatchingEngineEvent.TakerTradeEvent.MakerTrade(
                    8,
                    UUID.randomUUID().toString(),
                    9,
                    10,
                    true,
                    11,
                    12
                )
            )
        );

        // when
        TakerTradeEvent mappedToProto = ProtoMapper.toProto(takerTradeEvent);
        MatchingEngineEvent.TakerTradeEvent mappedBackFromProto = ProtoMapper.fromProto(mappedToProto);

        // then
        assertThat(mappedBackFromProto).isEqualTo(takerTradeEvent);
    }

    @Test
    void makerTrade() {
        // given
        MatchingEngineEvent.TakerTradeEvent.MakerTrade makerTrade = new MatchingEngineEvent.TakerTradeEvent.MakerTrade(1, UUID.randomUUID().toString(), 2, 3, true, 4, 5);

        // when
        MakerTrade mappedToProto = ProtoMapper.toProto(makerTrade);
        MatchingEngineEvent.TakerTradeEvent.MakerTrade mappedBackFromProto = ProtoMapper.fromProto(mappedToProto);

        // then
        assertThat(mappedBackFromProto).isEqualTo(makerTrade);
    }
}
