package io.wyden.clob.gateway.it.utils;

import io.wyden.clob.gateway.it.TestingData;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.assertj.core.api.Assertions.assertThat;

public class QuotingOrderRequestExpectations {
    public static OemsRequest expectQuotingRequest(QuotingOrdersObserver quotingOrdersObserver, OemsRequest clientRequest, TestingData.Instruments clobInstrument, TestingData.QuotingConfigs quotingConfig, String limitPrice, String quantity) {
        OemsRequest reverseRequest = quotingOrdersObserver.awaitQuotingRequest(clientRequest.getOrderId(), quantity);
        if (clientRequest.getSide() == OemsSide.BUY) {
            assertThat(reverseRequest.getSide()).isEqualTo(OemsSide.SELL);
        } else {
            assertThat(reverseRequest.getSide()).isEqualTo(OemsSide.BUY);
        }
        assertThat(reverseRequest.getOrderId()).isNotBlank();
        assertThat(reverseRequest.getParentOrderId()).isEqualTo(clientRequest.getOrderId());
        assertThat(reverseRequest.getRequestType()).isEqualTo(OemsRequest.OemsRequestType.ORDER_SINGLE);
        assertThat(reverseRequest.getInstrumentId()).isEqualTo(clobInstrument.instrument.getInstrumentIdentifiers().getInstrumentId());
        assertThat(reverseRequest.getBaseCurrency()).isEqualTo(clobInstrument.instrument.getForexSpotProperties().getBaseCurrency());
        assertThat(reverseRequest.getQuoteCurrency()).isEqualTo(clobInstrument.instrument.getBaseInstrument().getQuoteCurrency());
        assertThat(reverseRequest.getPortfolioId()).isEqualTo(quotingConfig.quotingConfig.getNostroPortfolio());
        assertThat(reverseRequest.getOrderType()).isEqualTo(OemsOrderType.LIMIT);
        assertThat(bd(reverseRequest.getQuantity())).isEqualByComparingTo(quantity);
        assertThat(bd(reverseRequest.getPrice())).isEqualByComparingTo(limitPrice);

        return reverseRequest;
    }
}
