package io.wyden.clob.gateway.it;

import com.google.protobuf.InvalidProtocolBufferException;
import io.wyden.published.brokerdesk.ClobConfig;
import io.wyden.published.brokerdesk.OrderType;
import io.wyden.published.brokerdesk.TIF;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

public class ClobConfigControllerIntegrationTest extends ClobGatewayIntegrationTestBase {

    @Autowired
    private WebTestClient webClient;

    @Test
    void shouldPutUpdateGet() {
        ClobConfig clobConfig = ClobConfig.newBuilder()
            .setAccount("maciek")
            .addAllSupportedOrderType(List.of(OrderType.MARKET, OrderType.LIMIT))
            .addSupportedTif(TIF.IOC)
            .build();

        // create
        webClient.post()
            .uri("/configuration/{account}", Map.of("account", "maciek"))
            .bodyValue(clobConfig)
            .accept(MediaType.APPLICATION_PROTOBUF)
            .exchange()
            .expectStatus().isOk();

        // get
        webClient.get()
            .uri("/configuration/{account}", Map.of("account", "maciek"))
            .accept(MediaType.APPLICATION_PROTOBUF)
            .exchange()
            .expectStatus().isOk()
            .expectBody().consumeWith(bytes -> {
                try {
                    ClobConfig retrieved = ClobConfig.parseFrom(bytes.getResponseBody());
                    assertThat(retrieved).isEqualTo(clobConfig);
                } catch (InvalidProtocolBufferException e) {
                    throw new RuntimeException(e);
                }
            });

        // update
        ClobConfig clobConfigUpdate = ClobConfig.newBuilder()
            .setAccount("maciek")
            .addAllSupportedOrderType(List.of())
            .addAllSupportedTif(List.of(TIF.IOC, TIF.FOK))
            .build();

        webClient.post()
            .uri("/configuration/{account}", Map.of("account", "maciek"))
            .bodyValue(clobConfigUpdate)
            .accept(MediaType.APPLICATION_PROTOBUF)
            .exchange()
            .expectStatus().isOk();

        // get
        webClient.get()
            .uri("/configuration/{account}", Map.of("account", "maciek"))
            .accept(MediaType.APPLICATION_PROTOBUF)
            .exchange()
            .expectStatus().isOk()
            .expectBody().consumeWith(bytes -> {
                try {
                    ClobConfig retrieved = ClobConfig.parseFrom(bytes.getResponseBody());
                    assertThat(retrieved).isEqualTo(clobConfigUpdate);
                } catch (InvalidProtocolBufferException e) {
                    throw new RuntimeException(e);
                }
            });
    }

    @Test
    void absentConfigReturns404() {
        // get
        webClient.get()
            .uri("/configuration/{account}", Map.of("account", "zdzisiek"))
            .accept(MediaType.APPLICATION_PROTOBUF)
            .exchange()
            .expectStatus().isNotFound();
    }
}
