package io.wyden.clob.gateway.it.utils;

import io.wyden.clob.gateway.infrastructure.rabbit.RabbitDestinations;
import io.wyden.cloud.utils.test.ExchangeObserver;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.published.referencedata.VenueAccountCreateRequest;
import org.apache.commons.lang3.StringUtils;

public class ReferenceDataServer {
    private final ExchangeObserver<VenueAccountCreateRequest> venueAccountCreateObserver;

    public ReferenceDataServer(RabbitDestinations destinations, RabbitIntegrator rabbitIntegrator) {
        venueAccountCreateObserver = ExchangeObserver.newBuilder(
            rabbitIntegrator,
            destinations.venueAccountCreateRequestRabbitExchange(rabbitIntegrator),
            ((bytes, s) -> VenueAccountCreateRequest.parseFrom(bytes)),
            "it-"
        ).withRoutingKey(StringUtils.EMPTY).build();
        venueAccountCreateObserver.attach();
    }

    public void cleanup() {
        venueAccountCreateObserver.detach();
        venueAccountCreateObserver.clearMessages();
    }

    public VenueAccountCreateRequest awaitVenueAccountCreateRequest() {
        return venueAccountCreateObserver.awaitMessage($ -> true);
    }
}
