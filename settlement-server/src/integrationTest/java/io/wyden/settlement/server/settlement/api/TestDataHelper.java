package io.wyden.settlement.server.settlement.api;

import io.wyden.published.booking.TransactionType;
import io.wyden.published.settlement.SettlementStatus;
import io.wyden.settlement.client.run.leg.SettlementLegDirection;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.run.SettlementRunVenueAccountRepository;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegEntity;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Component
public class TestDataHelper {

    @Autowired
    private SettlementRunRepository settlementRunRepository;

    @Autowired
    private SettlementLegRepository settlementLegRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private SettlementRunVenueAccountRepository venueAccountRepository;

    public SettlementRunEntity createSettlementRun(String accountId, SettlementStatus status) {
        SettlementRunEntity run = SettlementRunEntity.builder()
            .status(status)
            .startAt(Date.valueOf(LocalDate.now()))
            .venueAccountIds(List.of(accountId))
            .build();

        long runId = settlementRunRepository.persist(run);
        return run.toBuilder().id(runId).build();
    }

    public SettlementRunEntity createSettlementRun(List<String> accountIds, SettlementStatus status) {
        SettlementRunEntity run = SettlementRunEntity.builder()
            .status(status)
            .startAt(Date.valueOf(LocalDate.now()))
            .venueAccountIds(accountIds)
            .build();

        long runId = settlementRunRepository.persist(run);
        return run.toBuilder().id(runId).build();
    }

    public SettlementLegEntity createSettlementLeg(Long runId, String accountId, String asset, BigDecimal quantity) {
        SettlementLegEntity leg = SettlementLegEntity.builder()
            .runId(runId)
            .venueAccountId(accountId)
            .venue("test-venue")
            .direction(SettlementLegDirection.SEND)
            .asset(asset)
            .quantity(quantity)
            .status(SettlementStatus.PENDING)
            .auto(false)
            .build();

        return settlementLegRepository.persist(leg);
    }

    public TransactionEntity createTransaction(Long runId, String transactionUuidStr, String currency,
                                             BigDecimal quantity, boolean selected) {
        SettlementRunEntity run = settlementRunRepository.findById(runId);
        String venueAccountId = run.venueAccountIds().get(0); // Use the first venue account ID

        UUID transactionUuid;
        try {
            transactionUuid = UUID.fromString(transactionUuidStr);
        } catch (IllegalArgumentException e) {
            // If not a valid UUID, generate a random one
            transactionUuid = UUID.randomUUID();
        }

        TransactionEntity transaction = TransactionEntity.builder()
            .transactionUuid(transactionUuid)
            .orderId(UUID.randomUUID())
            .transactionType(TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE)
            .portfolioId(UUID.randomUUID())
            .createdAt(Timestamp.from(Instant.now()))
            .updatedAt(Timestamp.from(Instant.now()))
            .transactionDatetime(Timestamp.from(Instant.now()))
            .venueExecutionId(UUID.randomUUID())
            .description("Test transaction")
            .quantity(quantity)
            .leavesQuantity(BigDecimal.ZERO)
            .price(BigDecimal.valueOf(50000))
            .currency(currency)
            .intOrderId(UUID.randomUUID())
            .extOrderId(UUID.randomUUID())
            .baseCurrency("USD")
            .venueAccount(venueAccountId) // Use the venue account ID from settlement run
            .parentOrderId(UUID.randomUUID())
            .rootOrderId(UUID.randomUUID())
            .rootExecutionId(UUID.randomUUID())
            .executionId(UUID.randomUUID())
            .settlementRunId(runId)
            .build();

        long transactionDbId = transactionRepository.save(transaction);
        return transaction.toBuilder().id(transactionDbId).build();
    }

    public TransactionEntity createTransaction(UUID transactionUuid) {

        TransactionEntity transaction = TransactionEntity.builder()
            .transactionUuid(transactionUuid)
            .orderId(UUID.randomUUID())
            .transactionType(TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE)
            .portfolioId(UUID.randomUUID())
            .createdAt(Timestamp.from(Instant.now()))
            .updatedAt(Timestamp.from(Instant.now()))
            .transactionDatetime(Timestamp.from(Instant.now()))
            .venueExecutionId(UUID.randomUUID())
            .description("Test transaction")
            .quantity(BigDecimal.valueOf(Math.random()))
            .leavesQuantity(BigDecimal.ZERO)
            .price(BigDecimal.valueOf(50000))
            .currency("BTC")
            .intOrderId(UUID.randomUUID())
            .extOrderId(UUID.randomUUID())
            .baseCurrency("USD")
            .venueAccount("randomVenueAccountId")
            .parentOrderId(UUID.randomUUID())
            .rootOrderId(UUID.randomUUID())
            .rootExecutionId(UUID.randomUUID())
            .executionId(UUID.randomUUID())
            .build();

        long transactionDbId = transactionRepository.save(transaction);
        return transaction.toBuilder().id(transactionDbId).build();
    }

    public void createTestData() {
        // Create settlement run
        SettlementRunEntity run = createSettlementRun("test-account", SettlementStatus.PENDING);
        
        // Create settlement legs
        createSettlementLeg(run.id(), "test-account", "BTC", new BigDecimal("1.5"));
        createSettlementLeg(run.id(), "test-account", "ETH", new BigDecimal("10.0"));

        // Create transactions with unique UUIDs
        createTransaction(run.id(), UUID.randomUUID().toString(), "BTC", new BigDecimal("0.5"), true);
        createTransaction(run.id(), UUID.randomUUID().toString(), "BTC", new BigDecimal("1.0"), false);
        createTransaction(run.id(), UUID.randomUUID().toString(), "ETH", new BigDecimal("5.0"), true);
        createTransaction(run.id(), UUID.randomUUID().toString(), "ETH", new BigDecimal("5.0"), false);
    }

    public SettlementRunEntity createRunWithTransactions(SettlementStatus status, int transactionCount) {
        SettlementRunEntity run = createSettlementRun("test-account", status);
        
        for (int i = 0; i < transactionCount; i++) {
            createTransaction(run.id(), UUID.randomUUID().toString(), "BTC", new BigDecimal("0.1"), i % 2 == 0);
        }
        
        return run;
    }

    public SettlementRunEntity createRunWithLegs(SettlementStatus status, int legCount) {
        SettlementRunEntity run = createSettlementRun("test-account", status);

        for (int i = 0; i < legCount; i++) {
            String asset = i % 2 == 0 ? "BTC" : "ETH";
            SettlementLegEntity leg = createSettlementLeg(run.id(), "test-account", asset,
                              new BigDecimal("1.0").multiply(new BigDecimal(i + 1)));
        }

        // Reload the run to get the legs
        return settlementRunRepository.findById(run.id());
    }

    public void createMultipleRuns(int count) {
        SettlementStatus[] statuses = {SettlementStatus.PENDING, SettlementStatus.IN_PROGRESS, 
                                     SettlementStatus.COMPLETED, SettlementStatus.CANCELED};
        
        for (int i = 0; i < count; i++) {
            SettlementStatus status = statuses[i % statuses.length];
            SettlementRunEntity run = createSettlementRun("account-" + i, status);
            
            // Add some transactions to each run
            createTransaction(run.id(), UUID.randomUUID().toString(), "BTC", new BigDecimal("0.1"), true);
            createTransaction(run.id(), UUID.randomUUID().toString(), "ETH", new BigDecimal("1.0"), false);
        }
    }

    public void cleanupTestData() {
        // Clean up in the correct order to avoid foreign key constraint violations
        transactionRepository.clear();
        settlementLegRepository.clear();
        venueAccountRepository.clear();
        settlementRunRepository.clear();
    }
}
