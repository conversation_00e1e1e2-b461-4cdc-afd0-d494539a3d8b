package io.wyden.settlement.server.settlement.run;

import io.wyden.published.booking.TransactionType;
import io.wyden.settlement.server.settlement.PostgresTestcontainer;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegRepository;
import io.wyden.settlement.server.settlement.transaction.CurrencyAmount;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.JdbcTest;
import org.springframework.context.annotation.Import;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(SpringExtension.class)
@JdbcTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Import({TransactionRepository.class, SettlementRunRepository.class, SettlementLegRepository.class, SettlementRunVenueAccountRepository.class})
class TransactionRepositoryIT extends PostgresTestcontainer {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private SettlementRunRepository runRepository;

    private UUID transactionUuid;
    private long runId;

    @BeforeEach
    void setUp() {
        jdbcTemplate.update("DELETE FROM transactions");

        transactionUuid = UUID.randomUUID();
        runId = runRepository.persist(SettlementRunEntity.builder()
            .legs(Set.of()) // not needed for this test
            .startAt(Date.valueOf(LocalDate.now()))
            .build());

        jdbcTemplate.update("""
                    INSERT INTO transactions (transaction_uuid, order_id, portfolio_id,
                    created_at, updated_at, transaction_datetime, venue_execution_id, description,
                    quantity, leaves_quantity, price, currency, int_order_id, ext_order_id, base_currency,
                    venue_account, parent_order_id, root_order_id, root_execution_id, execution_id, settlement_run_id,
                    transaction_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, transactionUuid, UUID.randomUUID(), UUID.randomUUID(),
            Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()), UUID.randomUUID(), "Test Transaction", BigDecimal.TEN,
            BigDecimal.ONE, BigDecimal.valueOf(100), "USD", UUID.randomUUID(), UUID.randomUUID(),
            "BTC", "Venue123", UUID.randomUUID(),
            UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID(), runId, TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE.name());
    }

    @Test
    void save_ShouldPersistTransaction() {
        // Given
        TransactionEntity newTransaction = new TransactionEntity(
            null,
            UUID.randomUUID(),
            TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE,
            UUID.randomUUID(),
            UUID.randomUUID(),
            Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()),
            UUID.randomUUID(),
            "Test Insert",
            BigDecimal.valueOf(50),
            BigDecimal.valueOf(10),
            BigDecimal.valueOf(500),
            "EUR",
            UUID.randomUUID(),
            UUID.randomUUID(),
            "BTC",
            "VenueABC",
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            null,
            null,
            null
        );

        // When
        transactionRepository.save(newTransaction);

        // Then
        Optional<TransactionEntity> savedTransaction = transactionRepository.findByUUID(newTransaction.transactionUuid());
        assertTrue(savedTransaction.isPresent());
        assertEquals(newTransaction.transactionType(), savedTransaction.get().transactionType());
        assertEquals(newTransaction.currency(), savedTransaction.get().currency());
    }

    @Test
    void findAll_ShouldReturnSavedTransactions() {
        // When
        List<TransactionEntity> transactions = transactionRepository.findAll();

        // Then
        assertFalse(transactions.isEmpty());
        assertEquals(1, transactions.size());
        TransactionEntity retrievedTransaction = transactions.get(0);
        assertNotNull(retrievedTransaction);
        assertEquals(transactionUuid, retrievedTransaction.transactionUuid());
        assertEquals(TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE, retrievedTransaction.transactionType());
    }

    @Test
    void findById_ShouldReturnCorrectTransaction() {
        // When
        Optional<TransactionEntity> transaction = transactionRepository.findByUUID(transactionUuid);

        // Then
        assertTrue(transaction.isPresent());
        assertEquals(transactionUuid, transaction.get().transactionUuid());
        assertEquals(TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE, transaction.get().transactionType());
    }

    @Test
    void findUnsettledTransactions_ShouldReturnOnlyTransactionsWithoutSettlement() {
        // Given
        UUID secondTransactionUuid = UUID.randomUUID();
        jdbcTemplate.update("""
                    INSERT INTO transactions (transaction_uuid, order_id, portfolio_id,
                    created_at, updated_at, transaction_datetime, venue_execution_id, description,
                    quantity, leaves_quantity, price, currency, int_order_id, ext_order_id, base_currency,
                    venue_account, parent_order_id, root_order_id, root_execution_id, execution_id, settlement_run_id,
                    transaction_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, secondTransactionUuid, UUID.randomUUID(), UUID.randomUUID(),
            Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()), UUID.randomUUID(), "Second Transaction", BigDecimal.valueOf(5),
            BigDecimal.ONE, BigDecimal.valueOf(200), "USD", UUID.randomUUID(), UUID.randomUUID(),
            "BTC", "Venue123", UUID.randomUUID(),
            UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID(), null, TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE.name());

        // When
        List<TransactionEntity> unsettledTransactions = transactionRepository.findUnsettledTransactions(List.of("Venue123"));

        // Then
        assertThat(unsettledTransactions)
            .isNotEmpty()
            .hasSize(1);

        TransactionEntity retrievedTransaction = unsettledTransactions.get(0);
        assertThat(retrievedTransaction)
            .isNotNull();
        assertThat(retrievedTransaction.transactionUuid())
            .isEqualTo(secondTransactionUuid);
        assertThat(retrievedTransaction.settlementRunId())
            .isNull();
    }

    @Test
    void findCurrencyAmount_ShouldReturnCorrectAmounts() {
        // Given
        UUID secondTransactionUuid = UUID.randomUUID();
        jdbcTemplate.update("""
                    INSERT INTO transactions (transaction_uuid, order_id, portfolio_id,
                    created_at, updated_at, transaction_datetime, venue_execution_id, description,
                    quantity, leaves_quantity, price, currency, int_order_id, ext_order_id, base_currency,
                    venue_account, parent_order_id, root_order_id, root_execution_id, execution_id, settlement_run_id,
                    transaction_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, secondTransactionUuid, UUID.randomUUID(), UUID.randomUUID(),
            Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()), UUID.randomUUID(), "Second Transaction", BigDecimal.valueOf(5),
            BigDecimal.ONE, BigDecimal.valueOf(200), "USD", UUID.randomUUID(), UUID.randomUUID(),
            "BTC", "Venue123", UUID.randomUUID(),
            UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID(), runId, TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE.name());

        // When
        List<CurrencyAmount> currencyAmounts = transactionRepository.findCurrencyAmounts(runId);

        // Then
        assertThat(currencyAmounts)
            .isNotEmpty()
            .hasSize(2);

        CurrencyAmount usdAmount = currencyAmounts.stream()
            .filter(ca -> "USD".equals(ca.currency()))
            .findFirst()
            .orElseThrow(() -> new AssertionError("No USD currency found in results"));

        BigDecimal expectedAmount = new BigDecimal("-2000");

        assertThat(usdAmount.amount().stripTrailingZeros())
            .isEqualByComparingTo(expectedAmount.stripTrailingZeros());

        CurrencyAmount btcAmount = currencyAmounts.stream()
            .filter(ca -> "BTC".equals(ca.currency()))
            .findFirst()
            .orElseThrow(() -> new AssertionError("No BTC currency found in results"));

        assertThat(btcAmount.amount().stripTrailingZeros())
            .isEqualByComparingTo(new BigDecimal("15").stripTrailingZeros());
    }

    @Test
    void findCurrencyAmount_WithDifferentRunId_ShouldReturnCorrectAmounts() {
        // Given
        long differentRunId = runRepository.persist(SettlementRunEntity.builder()
            .legs(Set.of())
            .startAt(Date.valueOf(LocalDate.now()))
            .build());

        UUID secondTransactionUuid = UUID.randomUUID();
        jdbcTemplate.update("""
                    INSERT INTO transactions (transaction_uuid, order_id, portfolio_id,
                    created_at, updated_at, transaction_datetime, venue_execution_id, description,
                    quantity, leaves_quantity, price, currency, int_order_id, ext_order_id, base_currency,
                    venue_account, parent_order_id, root_order_id, root_execution_id, execution_id, settlement_run_id,
                    transaction_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, secondTransactionUuid, UUID.randomUUID(), UUID.randomUUID(),
            Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()), UUID.randomUUID(), "Second Transaction", BigDecimal.valueOf(5),
            BigDecimal.ONE, BigDecimal.valueOf(200), "USD", UUID.randomUUID(), UUID.randomUUID(),
            "BTC", "Venue123", UUID.randomUUID(),
            UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID(), differentRunId, TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE.name());

        // When
        List<CurrencyAmount> currencyAmounts = transactionRepository.findCurrencyAmounts(differentRunId);

        // Then
        assertThat(currencyAmounts)
            .isNotEmpty()
            .hasSize(2);

        CurrencyAmount btcAmount = currencyAmounts.stream()
            .filter(ca -> "BTC".equals(ca.currency()))
            .findFirst()
            .orElseThrow(() -> new AssertionError("Nie znaleziono waluty BTC w wynikach"));
        assertThat(btcAmount.amount().stripTrailingZeros())
            .isEqualByComparingTo(new BigDecimal("5").stripTrailingZeros());

        CurrencyAmount usdAmount = currencyAmounts.stream()
            .filter(ca -> "USD".equals(ca.currency()))
            .findFirst()
            .orElseThrow(() -> new AssertionError("Nie znaleziono waluty USD w wynikach"));

        BigDecimal expectedAmount = new BigDecimal("-1000");

        assertThat(usdAmount.amount().stripTrailingZeros())
            .isEqualByComparingTo(expectedAmount.stripTrailingZeros());
    }

    @Test
    void setSettlementId_ForUUID_ShouldUpdateSettlementRunId() {
        // Given
        UUID unsettledTransactionUuid = UUID.randomUUID();
        jdbcTemplate.update("""
                    INSERT INTO transactions (transaction_uuid, order_id, portfolio_id,
                    created_at, updated_at, transaction_datetime, venue_execution_id, description,
                    quantity, leaves_quantity, price, currency, int_order_id, ext_order_id, base_currency,
                    venue_account, parent_order_id, root_order_id, root_execution_id, execution_id, settlement_run_id,
                    transaction_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, unsettledTransactionUuid, UUID.randomUUID(), UUID.randomUUID(),
            Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()), UUID.randomUUID(), "Unsettled Transaction", BigDecimal.valueOf(5),
            BigDecimal.ONE, BigDecimal.valueOf(200), "USD", UUID.randomUUID(), UUID.randomUUID(),
            "BTC", "Venue123", UUID.randomUUID(),
            UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID(), null, TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE.name());

        long newSettlementRunId = runRepository.persist(SettlementRunEntity.builder()
            .legs(Set.of())
            .startAt(Date.valueOf(LocalDate.now()))
            .build());

        // When
        transactionRepository.setSettlementId(unsettledTransactionUuid, newSettlementRunId);

        // Then
        Optional<TransactionEntity> updatedTransaction = transactionRepository.findByUUID(unsettledTransactionUuid);
        assertThat(updatedTransaction)
            .isPresent();
        assertThat(updatedTransaction.get().settlementRunId())
            .isNotNull()
            .isEqualTo(newSettlementRunId);
    }

    @Test
    void setSettlementId_ForAccountList_ShouldUpdateOnlyUnsettledTransactions() {
        // Given
        UUID unsettledTransactionUuid = UUID.randomUUID();
        jdbcTemplate.update("""
                    INSERT INTO transactions (transaction_uuid, order_id, portfolio_id,
                    created_at, updated_at, transaction_datetime, venue_execution_id, description,
                    quantity, leaves_quantity, price, currency, int_order_id, ext_order_id, base_currency,
                    venue_account, 
                    parent_order_id, root_order_id, root_execution_id, execution_id, settlement_run_id,
                    transaction_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, unsettledTransactionUuid, UUID.randomUUID(), UUID.randomUUID(),
            Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()), UUID.randomUUID(), "Unsettled Transaction", BigDecimal.valueOf(5),
            BigDecimal.ONE, BigDecimal.valueOf(200), "EUR", UUID.randomUUID(), UUID.randomUUID(),
            "BTC", "Venue123", UUID.randomUUID(),
            UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID(), null, TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE.name());

        long newSettlementRunId = runRepository.persist(SettlementRunEntity.builder()
            .legs(Set.of())
            .startAt(Date.valueOf(LocalDate.now()))
            .build());

        // When
        transactionRepository.setSettlementId(List.of("Venue123"), newSettlementRunId);

        // Then
        Optional<TransactionEntity> updatedUnsettledTransaction = transactionRepository.findByUUID(unsettledTransactionUuid);
        assertThat(updatedUnsettledTransaction)
            .isPresent();
        assertThat(updatedUnsettledTransaction.get().settlementRunId())
            .isNotNull()
            .isEqualTo(newSettlementRunId);

        Optional<TransactionEntity> existingSettledTransaction = transactionRepository.findByUUID(transactionUuid);
        assertThat(existingSettledTransaction)
            .isPresent();
        assertThat(existingSettledTransaction.get().settlementRunId())
            .isNotNull()
            .isEqualTo(runId);
    }

    @Test
    void unpinTransactionsFromRun_ShouldUnpinOnlyTransactionByRunId() {
        // Given
        UUID unsettledTransactionUuid = UUID.randomUUID();
        long newSettlementRunId = runRepository.persist(SettlementRunEntity.builder()
            .legs(Set.of())
            .startAt(Date.valueOf(LocalDate.now()))
            .build());

        jdbcTemplate.update("""
                    INSERT INTO transactions (transaction_uuid, order_id, portfolio_id,
                    created_at, updated_at, transaction_datetime, venue_execution_id, description,
                    quantity, leaves_quantity, price, currency, int_order_id, ext_order_id, base_currency,
                    venue_account, 
                    parent_order_id, root_order_id, root_execution_id, execution_id, settlement_run_id,
                    transaction_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, unsettledTransactionUuid, UUID.randomUUID(), UUID.randomUUID(),
            Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()), UUID.randomUUID(), "Unsettled Transaction", BigDecimal.valueOf(5),
            BigDecimal.ONE, BigDecimal.valueOf(200), "EUR", UUID.randomUUID(), UUID.randomUUID(),
            "BTC", "Venue123", UUID.randomUUID(),
            UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID(), newSettlementRunId, TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE.name());

        // When
        transactionRepository.setSettlementRunAsNull(newSettlementRunId);

        // Then
        Optional<TransactionEntity> updatedUnsettledTransaction = transactionRepository.findByUUID(unsettledTransactionUuid);
        assertThat(updatedUnsettledTransaction)
            .isPresent();
        assertThat(updatedUnsettledTransaction.get().settlementRunId())
            .isNull();

        Optional<TransactionEntity> existingSettledTransaction = transactionRepository.findByUUID(transactionUuid);
        assertThat(existingSettledTransaction)
            .isPresent();
        assertThat(existingSettledTransaction.get().settlementRunId())
            .isNotNull()
            .isEqualTo(runId);
    }
}