package io.wyden.settlement.server.settlement.acceptance.config;

import com.hazelcast.map.IMap;
import io.cucumber.java.After;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.settlement.server.settlement.acceptance.SharedTestState;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegRepository;
import io.wyden.settlement.server.settlement.transaction.BookingSequenceNumberRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionFeeRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;

public class DbCleaner {
    private final IMap<String, VenueAccount> venueAccountsMap;
    private final SettlementRunRepository runRepository;
    private final SettlementLegRepository legRepository;
    private final TransactionFeeRepository feeRepository;
    private final TransactionRepository transactionRepository;
    private final BookingSequenceNumberRepository bookingSequenceNumberRepository;

    public DbCleaner(IMap<String, VenueAccount> venueAccountsMap, SettlementRunRepository runRepository, SettlementLegRepository legRepository, TransactionFeeRepository feeRepository, TransactionRepository transactionRepository, BookingSequenceNumberRepository bookingSequenceNumberRepository) {


        this.venueAccountsMap = venueAccountsMap;
        this.runRepository = runRepository;
        this.legRepository = legRepository;
        this.feeRepository = feeRepository;
        this.transactionRepository = transactionRepository;
        this.bookingSequenceNumberRepository = bookingSequenceNumberRepository;
    }

    @After
    public void cleanupTestState() {
        SharedTestState.clearAll();
        venueAccountsMap.clear();
        runRepository.clear();
        legRepository.clear();
        feeRepository.clear();
        transactionRepository.clear();
        bookingSequenceNumberRepository.clear();
    }
}