package io.wyden.settlement.server.settlement.schedule;

import com.hazelcast.map.IMap;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.published.booking.TransactionType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.settlement.server.settlement.TestContainersIntegrationBase;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.client.settlementconfiguration.DayOfTheWeek;
import io.wyden.settlement.client.settlementconfiguration.SettlementAccountConfigurationInput;
import io.wyden.settlement.client.settlementconfiguration.SettlementConfigurationInput;
import io.wyden.settlement.server.settlement.settlementconfiguration.SettlementConfigurationService;
import io.wyden.settlement.client.settlementconfiguration.SettlementDirectionPriority;
import io.wyden.settlement.client.settlementconfiguration.SettlementLegPriority;
import io.wyden.settlement.client.settlementconfiguration.SettlementSchedulePoint;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import io.wyden.settlement.server.settlement.utils.MessageSchedulerMock;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.is;

@Disabled  //doesnt work on pipeline
class ScheduleSettlementRunIT extends TestContainersIntegrationBase {

    @Autowired
    RabbitIntegrator integrator;
    @Autowired
    SettlementRunRepository runRepository;
    @Autowired
    TransactionRepository transactionRepository;
    @Autowired
    private SettlementConfigurationService settlementService;
    @Autowired
    IMap<String, VenueAccount> venueAccountsMap;

    MessageSchedulerMock messageScheduler;

    @BeforeEach
    void setup() {
        messageScheduler = new MessageSchedulerMock(integrator);
    }

    @AfterEach
    void tearDown() {
        messageScheduler.cleanup();
        runRepository.clear();
    }

    @Test
    void shouldRegisterRunSchedule_consumeItOnTime_createRunAndAssignTransactionToRunId() {
        String accountId = "accountId1";
        DayOfTheWeek dayOfTheWeek = DayOfTheWeek.valueOf(LocalDate.now().getDayOfWeek().name());
        String timeStr = LocalDateTime.now().plusSeconds(10).format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        List<SettlementConfigurationInput> cfgs = List.of(
            new SettlementConfigurationInput(accountId,
                new SettlementAccountConfigurationInput(
                    true,
                    "Europe/Warsaw",
                    List.of(new SettlementSchedulePoint(dayOfTheWeek, timeStr)),
                    List.of(),
                    List.of(),
                    SettlementDirectionPriority.NONE,
                    SettlementLegPriority.NONE,
                    true,
                    List.of()
                ))
        );
        UUID transactionUuid = UUID.randomUUID();
        TransactionEntity newTransaction = new TransactionEntity(
            null,
            UUID.randomUUID(),
            TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE,
            UUID.randomUUID(),
            transactionUuid,
            Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()),
            UUID.randomUUID(),
            "Test Insert",
            BigDecimal.valueOf(50),
            BigDecimal.valueOf(10),
            BigDecimal.valueOf(500),
            "EUR",
            UUID.randomUUID(),
            UUID.randomUUID(),
            "BTC",
            accountId,
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            null,
            null,
            null
        );

        transactionRepository.save(newTransaction);
        venueAccountsMap.put(accountId, VenueAccount.newBuilder().setId(accountId).build());
        settlementService.settlementConfigurationUpdate(cfgs);

        Awaitility.await()
            .atMost(25, TimeUnit.SECONDS)
            .until(() -> !runRepository.findAll().isEmpty());

        assertThat(transactionRepository.findUnsettledTransactions(List.of(accountId))).isEmpty();
    }

    @Test
    void shouldRegisterRunSchedule_butConsumeShouldBeIgnoredDueToExcludedDays_runShouldNotBeCreated() {
        String accountId = "accountId1";
        DayOfTheWeek dayOfTheWeek = DayOfTheWeek.valueOf(LocalDate.now().getDayOfWeek().name());
        LocalDateTime startTime = LocalDateTime.now();
        String timeStr = startTime.plusSeconds(2).format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        List<SettlementConfigurationInput> cfgs = List.of(
            new SettlementConfigurationInput(accountId,
                new SettlementAccountConfigurationInput(
                    true,
                    "Europe/Warsaw",
                    List.of(new SettlementSchedulePoint(dayOfTheWeek, timeStr)),
                    List.of(startTime.format(DateTimeFormatter.ofPattern("MM-dd"))),
                    List.of(),
                    SettlementDirectionPriority.NONE,
                    SettlementLegPriority.NONE,
                    true,
                    List.of()
                ))
        );

        venueAccountsMap.put(accountId, VenueAccount.newBuilder().setId(accountId).build());
        settlementService.settlementConfigurationUpdate(cfgs);

        Awaitility.await()
            .atMost(10, TimeUnit.SECONDS)
            .until(() -> runRepository.findAll().isEmpty(), is(true));

    }

}
