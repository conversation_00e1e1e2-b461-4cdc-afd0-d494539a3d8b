package io.wyden.settlement.server.settlement;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

@Testcontainers
@DirtiesContext
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
                properties = {
                    "tracing.collector.endpoint=disabled",
                    "coinapi.enabled=false",
                    "logging.level.org.testcontainers.containers=debug",
                    "logging.level.io.wyden.cloudutils.telemetry = warn",
                    "logging.level.root=info",
                    "logging.level.io.wyden=debug",
                    "spring.main.allow-bean-definition-overriding=true",
                    "cache.transaction.enabled=true"
                })
public class TestContainersIntegrationBase {
    public static final Network SHARED_NETWORK = Network.newNetwork();

    @Container
    protected static RabbitMQContainer rabbitmq =  new RabbitMQContainer(DockerImageName.parse("docker.wyden.io/mirror/rabbitmq:3.12-management")
        .asCompatibleSubstituteFor("rabbitmq:management"))
        .withExposedPorts(5672)
        .withReuse(true)
        .withNetwork(SHARED_NETWORK);

    @Container
    protected static GenericContainer<?> hazelcast = new GenericContainer<>(DockerImageName.parse("docker.wyden.io/mirror/hazelcast/hazelcast:5.2.0"))
        .withExposedPorts(5701)
        .withReuse(true)
        .withNetwork(SHARED_NETWORK);

    @Container
    protected static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>(DockerImageName.parse("docker.wyden.io/mirror/postgres:14.9")
        .asCompatibleSubstituteFor("postgres"))
        .withExposedPorts(5432)
        .withReuse(true)
        .withUsername("settlement")
        .withPassword("password")
        .withDatabaseName("settlement")
        .withNetwork(SHARED_NETWORK);

    static {
        postgres.start();
        rabbitmq.start();
        hazelcast.start();
    }

    @LocalServerPort
    protected int port;

    @DynamicPropertySource
    static void registerDynamicProperties(DynamicPropertyRegistry registry) {
        registry.add("rabbitmq.host", rabbitmq::getHost);
        registry.add("rabbitmq.port", () -> rabbitmq.getMappedPort(5672));
        registry.add("rabbitmq.username", rabbitmq::getAdminUsername);
        registry.add("rabbitmq.password", rabbitmq::getAdminPassword);

        registry.add("hz.addressList", () -> hazelcast.getHost() + ":" + hazelcast.getFirstMappedPort());
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "org.postgresql.Driver");
        registry.add("spring.jpa.database-platform", () -> "org.hibernate.dialect.PostgreSQLDialect");
    }
}
