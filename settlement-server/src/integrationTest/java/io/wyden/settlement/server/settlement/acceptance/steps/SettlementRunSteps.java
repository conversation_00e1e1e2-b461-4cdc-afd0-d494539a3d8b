package io.wyden.settlement.server.settlement.acceptance.steps;

import io.cucumber.datatable.DataTable;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.wyden.published.settlement.SettlementStatus;
import io.wyden.settlement.client.run.leg.SettlementLegDirection;
import io.wyden.settlement.server.settlement.acceptance.SharedTestState;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.run.fsm.state.RunState;
import io.wyden.settlement.server.settlement.run.fsm.state.RunStateContext;
import io.wyden.settlement.server.settlement.run.fsm.transitions.create.CreateSettlementRunService;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegEntity;
import io.wyden.settlement.server.settlement.run.leg.fsm.state.LegStateContext;
import io.wyden.settlement.server.settlement.transaction.TransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

public class SettlementRunSteps {
    private Logger LOGGER = LoggerFactory.getLogger(SettlementRunSteps.class);
    private final LegStateContext legStateContext;
    private final RunStateContext runStateContext;
    private final TransactionService transactionService;
    private final SettlementRunRepository runRepository;
    private final CreateSettlementRunService createSettlementRunService;

    public SettlementRunSteps(LegStateContext legStateContext, RunStateContext runStateContext, TransactionService transactionService, SettlementRunRepository runRepository, CreateSettlementRunService createSettlementRunService) {
        this.legStateContext = legStateContext;
        this.runStateContext = runStateContext;
        this.transactionService = transactionService;
        this.runRepository = runRepository;
        this.createSettlementRunService = createSettlementRunService;
    }

    @Given("Assume current date is {string}")
    public void setSettlementStartDate(String startDate) {
        SharedTestState.setCurrentStartDate(startDate);
        LOGGER.info("Set current start date to: {}", startDate);
    }

    @When("Create settlement run with current start date")
    public void createSettlementRunWithCurrentStartDate(DataTable dataTable) {
        List<String> accountIds = dataTable.asMaps().stream()
            .map(row -> row.get("accountIds"))
            .collect(Collectors.toList());

        LOGGER.info("Create settlement run with startDate {}", SharedTestState.getCurrentStartDate());
        RunState state = createSettlementRunService.create(accountIds);
        SharedTestState.addSettlementRun(SharedTestState.getCurrentStartDate(), state.getRunEntity());
    }

    @When("Create settlement run with startDate: {string}")
    public void createSettlementRunWithStartDate(String startDate, DataTable dataTable) {
        List<String> accountIds = dataTable.asMaps().stream()
            .map(row -> row.get("accountIds"))
            .collect(Collectors.toList());

        LOGGER.info("Create settlement run with startDate {}", startDate);
        RunState state = createSettlementRunService.create(accountIds);
        SharedTestState.addSettlementRun(startDate, state.getRunEntity());
    }

    @Given("Settlement Run")
    public void settlementRun(DataTable dataTable) {
        List<String> accountIds = dataTable.asMaps().stream()
            .map(row -> row.get("accountIds"))
            .collect(Collectors.toList());

        RunState state = createSettlementRunService.create(accountIds);
        SharedTestState.addSettlementRun(state.getRunEntity().startAt().toString(), state.getRunEntity());
    }

    @Then("Assert one run with no legs")
    public void assertSettlementRunLegs() {
        List<SettlementRunEntity> all = runRepository.findAll();
        assertThat(all).hasSize(1).allMatch(run -> run.legs().isEmpty());
    }

    @Then("Assert settlement run status: {string}")
    public void assertSettlementRunStatus(String status) {
        SettlementRunEntity run = SharedTestState.getSettlementRuns(SharedTestState.getCurrentStartDate());
        assertThat(run.status()).isEqualTo(SettlementStatus.valueOf(status));
    }

    @When("Start settlement run")
    public void startSettlementRun() {
        SettlementRunEntity run = SharedTestState.getSettlementRuns(SharedTestState.getCurrentStartDate());
        runStateContext.onStart(run.id());
        SharedTestState.addSettlementRun(SharedTestState.getCurrentStartDate(), runRepository.findById(run.id()));
    }

    @When("Cancel settlement run")
    public void cancelSettlementRun() {
        SettlementRunEntity run = SharedTestState.getSettlementRuns(SharedTestState.getCurrentStartDate());
        runStateContext.onCancel(run.id());
        SharedTestState.addSettlementRun(SharedTestState.getCurrentStartDate(), runRepository.findById(run.id()));
    }

    @Then("Assert settlement run legs")
    public void assertSettlementRunLegs(DataTable dataTable) {

        List<Map<String, String>> rowMap = dataTable.asMaps();

        SettlementRunEntity first = runRepository.findAll().stream().findFirst().orElseThrow();

        Collection<SettlementLegEntity> legs = first.legs();
        assertThat(legs).hasSize(rowMap.size());

        for (Map<String, String> row : rowMap) {
            String direction = row.get("direction");
            String currency = row.get("currency");
            SettlementStatus status = SettlementStatus.valueOf(row.get("status"));
            BigDecimal quantity = new BigDecimal(row.get("quantity"));

            boolean legFound = legs.stream().anyMatch(leg ->
                leg.direction() == SettlementLegDirection.valueOf(direction) &&
                leg.status() == status &&
                leg.asset().equals(currency) &&
                leg.quantity().stripTrailingZeros().equals(quantity.stripTrailingZeros())
            );

            assertThat(legFound)
                .withFailMessage("No matching leg found for: direction=%s, currency=%s, quantity=%s, status=%s, instead found: %s",
                    direction, currency, quantity, status, legs)
                .isTrue();
        }
    }

    @When("Initiate settlement leg for: {string}")
    public void initiateSettlementLeg(String currency) {
        SettlementRunEntity run = SharedTestState.getSettlementRuns(SharedTestState.getCurrentStartDate());
        run.legs().stream()
            .filter(leg -> leg.asset().equals(currency))
            .findFirst()
            .ifPresent(leg -> legStateContext.onInitiate(leg.id()));
        SharedTestState.addSettlementRun(SharedTestState.getCurrentStartDate(), runRepository.findById(run.id()));
    }

    @When("Initiate settlement leg for: {string} and startDate: {string}")
    public void initiateSettlementLeg(String currency, String startDate) {
        SettlementRunEntity run = SharedTestState.getSettlementRuns(startDate);
        run.legs().stream()
            .filter(leg -> leg.asset().equals(currency))
            .findFirst()
            .ifPresent(leg -> legStateContext.onInitiate(leg.id()));
        SharedTestState.addSettlementRun(startDate, runRepository.findById(run.id()));
    }

    @When("Complete settlement leg for: {string}")
    public void completeSettlementLeg(String currency) {
        SettlementRunEntity run = SharedTestState.getSettlementRuns(SharedTestState.getCurrentStartDate());
        run.legs().stream()
            .filter(leg -> leg.asset().equals(currency))
            .findFirst()
            .ifPresent(leg -> legStateContext.onComplete(leg.id()));
        SharedTestState.addSettlementRun(SharedTestState.getCurrentStartDate(), runRepository.findById(run.id()));
    }

    @When("Complete settlement leg for: {string} and startDate: {string}")
    public void completeSettlementLeg(String currency, String startDate) {
        SettlementRunEntity run = SharedTestState.getSettlementRuns(startDate);
        run.legs().stream()
            .filter(leg -> leg.asset().equals(currency))
            .findFirst()
            .ifPresent(leg -> legStateContext.onComplete(leg.id()));
        SharedTestState.addSettlementRun(startDate, runRepository.findById(run.id()));
    }

    @Then("Assert settlement run completedAt is null")
    public void assertSettlementRunCompletedAtIsNull() {
        SettlementRunEntity settlementRunEntity = SharedTestState.getSettlementRuns(SharedTestState.getCurrentStartDate());
        assertThat(settlementRunEntity.settledAt()).isNull();
    }

    @Then("Assert settlement run completedAt is not null")
    public void assertSettlementRunCompletedAtIsNotNull() {
        SettlementRunEntity settlementRunEntity = SharedTestState.getSettlementRuns(SharedTestState.getCurrentStartDate());
        assertThat(settlementRunEntity.settledAt()).isNotNull();
    }

    @Given("Assume we have settlement runs")
    public void assumeWeHaveSettlementRuns(DataTable dataTable) {
        dataTable.asMaps()
            .forEach(row -> {
                String accountIds = row.get("accountId");
                long startDate = Long.parseLong(row.get("startDate"));
                SettlementRunEntity entity = SettlementRunEntity.builder()
                    .venueAccountIds(List.of(accountIds))
                    .startAt(new java.sql.Date(startDate))
                    .build();
                long persist = runRepository.persist(entity);
                SharedTestState.addSettlementRun(row.get("startDate"), entity.toBuilder().id(persist).build());
            });
    }

    @Then("Assert number of settlement legs: {int}")
    public void assertNumberOfSettlementLegs(int legNumbers) {
        SettlementRunEntity settlementRunEntity = SharedTestState.getSettlementRuns(SharedTestState.getCurrentStartDate());
        assertThat(settlementRunEntity.legs()).size().isEqualTo(legNumbers);
    }
}