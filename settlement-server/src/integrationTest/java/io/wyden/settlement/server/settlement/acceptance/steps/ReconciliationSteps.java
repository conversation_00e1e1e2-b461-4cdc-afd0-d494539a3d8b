package io.wyden.settlement.server.settlement.acceptance.steps;

import io.cucumber.datatable.DataTable;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.Metadata;
import io.wyden.published.common.PageInfo;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.settlement.server.settlement.acceptance.SharedTestState;
import io.wyden.settlement.server.settlement.booking.BookingSnapshotterHttpClient;
import io.wyden.settlement.server.settlement.reconciliation.ReconciliationService;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.run.fsm.transitions.create.CreateSettlementRunService;
import io.wyden.settlement.server.settlement.transaction.BookingSequenceNumberRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionService;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.PlatformTransactionManager;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ReconciliationSteps {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReconciliationSteps.class);

    private final BookingSnapshotterHttpClient bookingSnapshotterHttpClient;
    private final BookingSequenceNumberRepository bookingSequenceNumberRepository;
    private final ReconciliationService reconciliationService;
    private final TransactionRepository transactionRepository;
    private final SettlementRunRepository runRepository;
    private long seqNum = 0;

    private final List<BookingCompleted> clientCashTradeEvents = new ArrayList<>();
    private final List<BookingCompleted> streetCashTradeEvents = new ArrayList<>();

    public ReconciliationSteps(TransactionRepository transactionRepository,
                               CreateSettlementRunService createSettlementRunService,
                               SettlementRunRepository settlementRunRepository,
                               TransactionService transactionService,
                               BookingSequenceNumberRepository bookingSequenceNumberRepository,
                               PlatformTransactionManager transactionManager) {
        this.transactionRepository = transactionRepository;
        this.runRepository = settlementRunRepository;
        this.bookingSnapshotterHttpClient = mock();
        this.bookingSequenceNumberRepository = bookingSequenceNumberRepository;
        this.reconciliationService = new ReconciliationService(transactionRepository, mock(), createSettlementRunService, bookingSequenceNumberRepository, bookingSnapshotterHttpClient, transactionService,
            settlementRunRepository, transactionManager);

    }

    @Given("Assume following street side booking transaction")
    public void assumeFollowingStreetBookingTransaction(DataTable dataTable) {
        Map<String, String> row = dataTable.asMap();
        UUID uuid = UUID.fromString(row.get("uuid"));
        String quantity = row.get("quantity");
        String price = row.get("price");
        String baseCurrency = row.get("baseCurrency");
        String currency = row.get("currency");
        String accountId = row.get("accountId");
        boolean settled = Boolean.parseBoolean(row.get("settled"));
        String settledDate = row.get("settledDate");
        String clientSettlementRunId = row.get("clientSettlementRunId");

        StreetCashTradeSnapshot streetCashTradeSnapshot = StreetCashTradeSnapshot.newBuilder()
            .setUuid(uuid.toString())
            .setMetadata(Metadata.newBuilder()
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setUpdatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .build())
            .setBaseCurrency(baseCurrency)
            .setCurrency(currency)
            .setPrice(price)
            .setVenueAccount(accountId)
            .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setQuantity(quantity)
            .setSettled(settled)
            .setSettledDateTime(settledDate)
            .setClientSettlementId(clientSettlementRunId)
            .addAllTransactionFee(mapFees(row))
            .build();

        streetCashTradeEvents.add(
            BookingCompleted.newBuilder()
                .setTransactionCreated(TransactionSnapshot.newBuilder()
                    .setStreetCashTrade(streetCashTradeSnapshot)
                    .build())
                .setSequenceNumber(++seqNum)
                .setOrderStatus(OemsOrderStatus.STATUS_FILLED)
                .build());

        setupMocks();
    }

    @Given("Assume following client side booking transaction")
    public void assumeFollowingBookingTransaction(DataTable dataTable) {
        Map<String, String> row = dataTable.asMap();
        UUID uuid = UUID.fromString(row.get("uuid"));
        String quantity = row.get("quantity");
        String price = row.get("price");
        String baseCurrency = row.get("baseCurrency");
        String currency = row.get("currency");
        String counterPortfolio = row.get("counterPortfolio");
        boolean settled = Boolean.parseBoolean(row.get("settled"));
        String settledDate = row.get("settledDate");
        String clientSettlementRunId = row.get("clientSettlementRunId");

        ClientCashTradeSnapshot clientCashTradeSnapshot = ClientCashTradeSnapshot.newBuilder()
            .setUuid(uuid.toString())
            .setMetadata(Metadata.newBuilder()
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setUpdatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .build())
            .setBaseCurrency(baseCurrency)
            .setCurrency(currency)
            .setPrice(price)
            .setCounterPortfolio(counterPortfolio)
            .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setQuantity(quantity)
            .setSettled(settled)
            .setSettledDateTime(settledDate)
            .setClientSettlementId(clientSettlementRunId)
            .addAllTransactionFee(mapFees(row))
            .build();

        clientCashTradeEvents.add(
            BookingCompleted.newBuilder()
                .setTransactionCreated(TransactionSnapshot.newBuilder()
                    .setClientCashTrade(clientCashTradeSnapshot)
                    .build())
                .setSequenceNumber(++seqNum)
                .setOrderStatus(OemsOrderStatus.STATUS_FILLED)
                .build());

        setupMocks();
    }

    private static List<Fee> mapFees(Map<String, String> row) {
        List<Fee> fees = new ArrayList<>();
        if (isNotBlank(row.get("fee1Amount"))) {
            Fee build = Fee.newBuilder()
                .setFeeType(FeeType.EXCHANGE_FEE)
                .setAmount(row.get("fee1Amount"))
                .setCurrency(row.get("fee1Currency"))
                .build();
            fees.add(build);
        }
        if (isNotBlank(row.get("fee2Amount"))) {
            Fee build = Fee.newBuilder()
                .setFeeType(FeeType.EXCHANGE_FEE)
                .setAmount(row.get("fee2Amount"))
                .setCurrency(row.get("fee2Currency"))
                .build();
            fees.add(build);
        }
        return fees;
    }

    @When("Perform reconciliation")
    public void performReconciliation() {
        reconciliationService.reconcile();
        runRepository.findAll().forEach(run ->
            SharedTestState.addSettlementRun(run.startAt().toString(), run));
    }

    @Then("Assert transaction is persisted in settlement database")
    public void assertTransactionIsPersistedInSettlementDatabase(DataTable dataTable) {
        Map<String, String> row = dataTable.asMap();
        UUID uuid = UUID.fromString(row.get("uuid"));
        String quantity = row.get("quantity");
        String price = row.get("price");
        String baseCurrency = row.get("baseCurrency");
        String currency = row.get("currency");
        UUID counterPortfolio = isBlank(row.get("counterPortfolio")) ? null : UUID.fromString(row.get("counterPortfolio"));
        boolean settled = Boolean.parseBoolean(row.get("settled"));
        Timestamp settledDate = isBlank(row.get("settledDate")) ? null : Timestamp.from(Instant.parse(row.get("settledDate")));

        TransactionEntity entity = transactionRepository.findByUUID(uuid)
            .get();

        if (isNotBlank(quantity)) {
            assertThat(entity)
                .extracting(TransactionEntity::quantity, InstanceOfAssertFactories.BIG_DECIMAL).isEqualByComparingTo(new BigDecimal(quantity));
        }
        if (isNotBlank(price)) {
            assertThat(entity)
                .extracting(TransactionEntity::price, InstanceOfAssertFactories.BIG_DECIMAL).isEqualByComparingTo(new BigDecimal(price));
        }
        if (isNotBlank(baseCurrency)) {
            assertThat(entity)
                .extracting(TransactionEntity::baseCurrency).isEqualTo(baseCurrency);
        }
        if (isNotBlank(currency)) {
            assertThat(entity)
                .extracting(TransactionEntity::currency).isEqualTo(currency);
        }
        if (nonNull(counterPortfolio)) {
            assertThat(entity)
                .extracting(TransactionEntity::counterPortfolioId).isEqualTo(counterPortfolio);
        }
        if (isNotBlank(row.get("settledDate"))) {
            assertThat(entity)
                .extracting(TransactionEntity::settlementDate).isEqualTo(settledDate);
        }

    }

    @Then("Assert seq number is persisted in settlement database")
    public void assertSeqNumberIsPersistedInSettlementDatabase() {
        assertThat(bookingSequenceNumberRepository.findMaxSequenceNumber()).isEqualTo(seqNum);
    }

    private void setupMocks() {
        List<CursorEdge> clientCursorEdges = clientCashTradeEvents.stream()
            .map(bookingCompleted -> CursorEdge.newBuilder().setNode(CursorNode.newBuilder()
                .setBookingCompleted(bookingCompleted)
                .build()).build())
            .toList();
        List<CursorEdge> streetCursorEdges = streetCashTradeEvents.stream()
            .map(bookingCompleted -> CursorEdge.newBuilder().setNode(CursorNode.newBuilder()
                .setBookingCompleted(bookingCompleted)
                .build()).build())
            .toList();

        CursorConnection cursorConnection = CursorConnection.newBuilder()
            .addAllEdges(clientCursorEdges)
            .addAllEdges(streetCursorEdges)
            .setPageInfo(PageInfo.newBuilder()
                .setTotalSize(clientCursorEdges.size() + streetCursorEdges.size())
                .setHasNextPage(false)
                .build())
            .build();

        when(bookingSnapshotterHttpClient.fetchBookingCompletedEvents(any(), eq(100)))
            .thenReturn(cursorConnection);
    }
}
