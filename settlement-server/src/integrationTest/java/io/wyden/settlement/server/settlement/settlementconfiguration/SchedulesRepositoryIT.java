package io.wyden.settlement.server.settlement.settlementconfiguration;

import io.wyden.settlement.server.settlement.PostgresTestcontainer;
import io.wyden.settlement.server.settlement.settlementconfiguration.schedule.ScheduleRepository;
import io.wyden.settlement.server.settlement.settlementconfiguration.schedule.ScheduledMessageEntity;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.JdbcTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ExtendWith(SpringExtension.class)
@JdbcTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Import({ScheduleRepository.class})
class SchedulesRepositoryIT extends PostgresTestcontainer {

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Test
    void save_ShouldInsertSchedule() {
        ScheduledMessageEntity schedule = new ScheduledMessageEntity("1", "* * * * * *", "Europe/Warsaw");
        scheduleRepository.insert(schedule);
        Optional<ScheduledMessageEntity> found = scheduleRepository.findById("1");

        assertThat(found).isPresent().contains(schedule);

    }

    @Test
    void delete_ShouldSaveReadAndDeleteEntity() {
        ScheduledMessageEntity schedule = new ScheduledMessageEntity("1", "* * * * * *", "Europe/Warsaw");
        scheduleRepository.insert(schedule);
        Optional<ScheduledMessageEntity> found = scheduleRepository.findById("1");

        scheduleRepository.deleteById("1");
        Optional<ScheduledMessageEntity> found2 = scheduleRepository.findById("1");

        assertThat(found).isPresent().contains(schedule);
        assertThat(found2).isNotPresent();
    }

    @Test
    void findConfigurationsByIdIn_ShouldFindConfigurations() {
        ScheduledMessageEntity schedule1 = new ScheduledMessageEntity("1", "1 * * * * *", "Europe/Warsaw");
        ScheduledMessageEntity schedule2 = new ScheduledMessageEntity("2", "2 * * * * *", "Europe/Warsaw");
        ScheduledMessageEntity schedule3 = new ScheduledMessageEntity("3", "3 * * * * *", "Europe/Warsaw");

        scheduleRepository.insert(schedule1);
        scheduleRepository.insert(schedule2);
        scheduleRepository.insert(schedule3);
        List<ScheduledMessageEntity> found = scheduleRepository.findByIdIn(Set.of("1", "2", "3"));

        assertThat(found).contains(schedule1, schedule2, schedule3);
    }

    @Test
    void findConfigurationsByCronAndTZ_ShouldFindConfigurations() {
        ScheduledMessageEntity schedule1 = new ScheduledMessageEntity("1", "* * * * * *", "Europe/Warsaw");
        ScheduledMessageEntity schedule2 = new ScheduledMessageEntity("2", "* * * * * 1", "Europe/Warsaw");
        ScheduledMessageEntity schedule3 = new ScheduledMessageEntity("3", "* * * * * *", "Europe/Lisbon");

        scheduleRepository.insert(schedule1);
        scheduleRepository.insert(schedule2);
        scheduleRepository.insert(schedule3);
        List<ScheduledMessageEntity> found = scheduleRepository.findByCronExpressionAndTimeZoneId("* * * * * *", "Europe/Warsaw");

        assertThat(found).contains(schedule1);
    }

    @Test
    void addDuplicatedSchedulePoint_ShouldThrow() {
        ScheduledMessageEntity schedule1 = new ScheduledMessageEntity("1", "* * * * * *", "Europe/Warsaw");
        ScheduledMessageEntity schedule2 = new ScheduledMessageEntity("2", "* * * * * *", "Europe/Warsaw");

        scheduleRepository.insert(schedule1);

        assertThatThrownBy(() -> scheduleRepository.insert(schedule2)).isInstanceOf(SettlementConfigurationException.class);
    }

}
