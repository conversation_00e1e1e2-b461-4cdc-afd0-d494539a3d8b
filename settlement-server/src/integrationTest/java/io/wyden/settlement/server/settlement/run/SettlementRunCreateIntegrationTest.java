package io.wyden.settlement.server.settlement.run;

import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.settlement.SettlementStatus;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.settlement.client.run.leg.SettlementLegDirection;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.sink.StateChangeRabbitEmitter;
import io.wyden.settlement.server.settlement.run.fsm.transitions.create.CreateSettlementRunService;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegEntity;
import io.wyden.settlement.server.settlement.transaction.CurrencyAmount;
import io.wyden.settlement.server.settlement.transaction.TransactionFeeRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SettlementRunCreateIntegrationTest {

    @Mock
    private TransactionRepository transactionRepository;
    @Mock
    private TransactionFeeRepository transactionFeeRepository;

    @Mock
    private SettlementRunRepository runRepository;

    @Mock
    private VenueAccountCacheFacade venueAccountCacheFacade;

    @Mock
    private StateChangeRabbitEmitter stateChangeRabbitEmitter;

    @InjectMocks
    private CreateSettlementRunService createSettlementRunService;

    @Captor
    private ArgumentCaptor<SettlementRunEntity> runEntityCaptor;

    private static final String START_DATE = String.valueOf(Instant.now().toEpochMilli());
    private static final List<String> ACCOUNT_IDS = List.of("account1", "account2");

    @Test
    void shouldCreateSettlementRunWithCorrectLegs() {
        // Given
        when(runRepository.persist(any())).thenReturn(1L);

        VenueAccount venueAccount = VenueAccount.newBuilder().setVenueName("TestExchange").build();
        when(venueAccountCacheFacade.find("account1")).thenReturn(Optional.of(venueAccount));

        CurrencyAmount currencyAmount = new CurrencyAmount("account1", "BTC", new BigDecimal("1.0"));
        when(transactionRepository.findCurrencyAmounts(eq(1L)))
            .thenReturn(List.of(currencyAmount));

        doNothing().when(transactionRepository).setSettlementId(ACCOUNT_IDS, 1L);

        // When
        createSettlementRunService.create(ACCOUNT_IDS);

        // Then
        // 1. Verify transactions were marked with settlement ID
        verify(transactionRepository).setSettlementId(ACCOUNT_IDS, 1L);

        // 2. Capture the settlement run entities passed to repository
        verify(runRepository, times(2)).persist(runEntityCaptor.capture());
        List<SettlementRunEntity> capturedRunEntities = runEntityCaptor.getAllValues();

        // 3. Verify first call was with empty legs
        assertThat(capturedRunEntities.get(0).legs()).isEmpty();

        // 4. Verify second call contains legs with correct data
        SettlementRunEntity updatedRun = capturedRunEntities.get(1);
        Collection<SettlementLegEntity> legs = updatedRun.legs();

        assertThat(legs).hasSize(1);

        SettlementLegEntity leg = legs.iterator().next();
        assertThat(leg.venueAccountId()).isEqualTo("account1");
        assertThat(leg.venue()).isEqualTo("TestExchange");
        assertThat(leg.asset()).isEqualTo("BTC");
        assertThat(leg.quantity()).isEqualTo(new BigDecimal("1.0"));
        assertThat(leg.direction()).isEqualTo(SettlementLegDirection.RECEIVE);
        assertThat(leg.status()).isEqualTo(SettlementStatus.PENDING);
        assertThat(leg.runId()).isEqualTo(1L);
        assertThat(leg.auto()).isFalse();
    }

    @Test
    void shouldHandleEmptyCurrencyAmounts() {
        // Given
        when(runRepository.persist(any())).thenReturn(1L);

        when(transactionRepository.findCurrencyAmounts(eq(1L)))
            .thenReturn(List.of());

        doNothing().when(transactionRepository).setSettlementId(ACCOUNT_IDS, 1L);

        // When
        createSettlementRunService.create(ACCOUNT_IDS);

        // Then
        verify(runRepository, times(2)).persist(runEntityCaptor.capture());

        // No legs should be created
        assertThat(runEntityCaptor.getAllValues().get(1).legs()).isEmpty();
    }

    @Test
    void shouldHandleMultipleCurrencyAmounts() {
        // Given
        when(runRepository.persist(any())).thenReturn(1L);

        VenueAccount venueAccount = VenueAccount.newBuilder().setVenueName("TestExchange").build();
        when(venueAccountCacheFacade.find("account1")).thenReturn(Optional.of(venueAccount));

        CurrencyAmount btcAmount = new CurrencyAmount("account1", "BTC", new BigDecimal("1.0"));
        CurrencyAmount ethAmount = new CurrencyAmount("account1", "ETH", new BigDecimal("-2.5"));

        when(transactionRepository.findCurrencyAmounts(eq(1L)))
            .thenReturn(List.of(btcAmount, ethAmount));

        doNothing().when(transactionRepository).setSettlementId(ACCOUNT_IDS, 1L);

        // When
        createSettlementRunService.create(ACCOUNT_IDS);

        // Then
        verify(runRepository, times(2)).persist(runEntityCaptor.capture());

        // Two legs should be created
        Collection<SettlementLegEntity> legs = runEntityCaptor.getAllValues().get(1).legs();
        assertThat(legs).hasSize(2);

        // Verify leg properties
        boolean foundBtcLeg = false;
        boolean foundEthLeg = false;

        for (SettlementLegEntity leg : legs) {
            if (leg.asset().equals("BTC")) {
                foundBtcLeg = true;
                assertThat(leg.direction()).isEqualTo(SettlementLegDirection.RECEIVE);
                assertThat(leg.quantity()).isEqualTo(new BigDecimal("1.0"));
            } else if (leg.asset().equals("ETH")) {
                foundEthLeg = true;
                assertThat(leg.direction()).isEqualTo(SettlementLegDirection.SEND);
                assertThat(leg.quantity()).isEqualTo(new BigDecimal("2.5"));
            }
        }

        assertThat(foundBtcLeg).isTrue();
        assertThat(foundEthLeg).isTrue();
    }

    @Test
    void shouldUpdateAllTransactionsWithSettlementId() {
        // Given
        when(runRepository.persist(any())).thenReturn(1L);

        VenueAccount venueAccount = VenueAccount.newBuilder().setVenueName("TestExchange").build();
        when(venueAccountCacheFacade.find("account1")).thenReturn(Optional.of(venueAccount));

        CurrencyAmount currencyAmount = new CurrencyAmount("account1", "BTC", new BigDecimal("1.0"));
        when(transactionRepository.findCurrencyAmounts(eq(1L)))
            .thenReturn(List.of(currencyAmount));

        doNothing().when(transactionRepository).setSettlementId(ACCOUNT_IDS, 1L);

        // When
        createSettlementRunService.create(ACCOUNT_IDS);

        // Then
        verify(transactionRepository).setSettlementId(ACCOUNT_IDS, 1L);
    }
}