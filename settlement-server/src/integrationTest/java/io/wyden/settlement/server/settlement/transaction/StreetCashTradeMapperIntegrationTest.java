package io.wyden.settlement.server.settlement.transaction;

import io.wyden.published.booking.TransactionType;
import io.wyden.settlement.client.transaction.SettlementStreetCashTrade;
import io.wyden.settlement.server.settlement.TestContainersIntegrationBase;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class StreetCashTradeMapperIntegrationTest extends TestContainersIntegrationBase {

    @Autowired
    private StreetCashTradeMapper streetCashTradeMapper;

    @Autowired
    private TransactionFeeRepository transactionFeeRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        // Clean up before each test
        transactionFeeRepository.clear();
        jdbcTemplate.update("DELETE FROM transactions");
    }

    @Test
    void shouldIncludeFeeForFirstTransactionInSettlementRun() {
        // Given
        // Create first transaction (earlier timestamp)
        Long firstTransactionId = createTestTransactionWithTimestamp(null,
            Timestamp.from(Instant.parse("2023-01-01T10:00:00Z")));
        
        // Create second transaction (later timestamp)
        Long secondTransactionId = createTestTransactionWithTimestamp(null,
            Timestamp.from(Instant.parse("2023-01-01T11:00:00Z")));

        // Add fees to both transactions
        TransactionFeeEntity firstFee = TransactionFeeEntity.builder()
            .transactionId(firstTransactionId)
            .transactionFeeAmount(new BigDecimal("25.50"))
            .transactionFeeCurrency("USD")
            .transactionFeeType("EXCHANGE_FEE")
            .build();

        TransactionFeeEntity secondFee = TransactionFeeEntity.builder()
            .transactionId(secondTransactionId)
            .transactionFeeAmount(new BigDecimal("15.75"))
            .transactionFeeCurrency("EUR")
            .transactionFeeType("SECOND_TRANSACTION_FEE")
            .build();

        transactionFeeRepository.save(firstFee);
        transactionFeeRepository.save(secondFee);

        // Get transaction entities
        TransactionEntity firstTransaction = getTransactionById(firstTransactionId);

        // When
        SettlementStreetCashTrade firstDto = streetCashTradeMapper.mapToDto(firstTransaction);

        // Then
        assertNotNull(firstDto.getFeeAmount());
        assertNotNull(firstDto.getFeeCurrency());
        assertEquals(new BigDecimal("25.50").compareTo(firstDto.getFeeAmount()), 0);
        assertEquals("USD", firstDto.getFeeCurrency());
    }

    @Test
    void shouldHandleFirstTransactionWithoutFees() {
        // Given
        Long firstTransactionId = createTestTransactionWithTimestamp(null,
            Timestamp.from(Instant.parse("2023-01-01T08:00:00Z")));

        TransactionEntity transaction = getTransactionById(firstTransactionId);

        // When
        SettlementStreetCashTrade dto = streetCashTradeMapper.mapToDto(transaction);

        // Then
        assertEquals(0, dto.getFeeAmount().compareTo(BigDecimal.ZERO));
        assertEquals("", dto.getFeeCurrency());
    }

    @Test
    void shouldIncludeExchangeFeeForTransaction() {
        // Given
        Long transactionId = createTestTransactionWithTimestamp(null,
            Timestamp.from(Instant.parse("2023-01-01T12:00:00Z")));

        // Add EXCHANGE_FEE to the transaction
        TransactionFeeEntity exchangeFee = TransactionFeeEntity.builder()
            .transactionId(transactionId)
            .transactionFeeAmount(new BigDecimal("12.75"))
            .transactionFeeCurrency("BTC")
            .transactionFeeType("EXCHANGE_FEE")
            .build();

        transactionFeeRepository.save(exchangeFee);

        // Get transaction entity
        TransactionEntity transaction = getTransactionById(transactionId);

        // When
        SettlementStreetCashTrade dto = streetCashTradeMapper.mapToDto(transaction);

        // Then
        assertNotNull(dto.getFeeAmount());
        assertNotNull(dto.getFeeCurrency());
        assertEquals(new BigDecimal("12.75").compareTo(dto.getFeeAmount()), 0);
        assertEquals("BTC", dto.getFeeCurrency());
    }

    @Test
    void shouldPrioritizeExchangeFeeWhenMultipleFeeTypesExist() {
        // Given
        Long transactionId = createTestTransactionWithTimestamp(null,
            Timestamp.from(Instant.parse("2023-01-01T13:00:00Z")));

        // Add multiple fee types including EXCHANGE_FEE
        TransactionFeeEntity networkFee = TransactionFeeEntity.builder()
            .transactionId(transactionId)
            .transactionFeeAmount(new BigDecimal("5.00"))
            .transactionFeeCurrency("ETH")
            .transactionFeeType("TRANSACTION_FEE")
            .build();

        TransactionFeeEntity exchangeFee = TransactionFeeEntity.builder()
            .transactionId(transactionId)
            .transactionFeeAmount(new BigDecimal("8.50"))
            .transactionFeeCurrency("USD")
            .transactionFeeType("EXCHANGE_FEE")
            .build();

        TransactionFeeEntity otherFee = TransactionFeeEntity.builder()
            .transactionId(transactionId)
            .transactionFeeAmount(new BigDecimal("2.25"))
            .transactionFeeCurrency("BTC")
            .transactionFeeType("FIXED_FEE")
            .build();

        // Save fees in different order to test prioritization
        transactionFeeRepository.save(networkFee);
        transactionFeeRepository.save(otherFee);
        transactionFeeRepository.save(exchangeFee);

        // Get transaction entity
        TransactionEntity transaction = getTransactionById(transactionId);

        // When
        SettlementStreetCashTrade dto = streetCashTradeMapper.mapToDto(transaction);

        // Then - should prioritize EXCHANGE_FEE over other fee types
        assertNotNull(dto.getFeeAmount());
        assertNotNull(dto.getFeeCurrency());
        assertEquals(0, new BigDecimal("8.50").compareTo(dto.getFeeAmount()));
        assertEquals("USD", dto.getFeeCurrency());
    }

    private Long createTestTransactionWithTimestamp(Long settlementRunId, Timestamp timestamp) {
        TransactionEntity transaction = TransactionEntity.builder()
            .orderId(UUID.randomUUID())
            .transactionType(TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE)
            .portfolioId(UUID.randomUUID())
            .transactionUuid(UUID.randomUUID())
            .createdAt(timestamp)
            .updatedAt(timestamp)
            .transactionDatetime(timestamp)
            .venueExecutionId(UUID.randomUUID())
            .description("Test transaction")
            .quantity(new BigDecimal("100.00"))
            .leavesQuantity(new BigDecimal("0.00"))
            .price(new BigDecimal("50.00"))
            .currency("BTC")
            .intOrderId(UUID.randomUUID())
            .extOrderId(UUID.randomUUID())
            .baseCurrency("USD")
            .venueAccount("test-venue")
            .parentOrderId(UUID.randomUUID())
            .rootOrderId(UUID.randomUUID())
            .rootExecutionId(UUID.randomUUID())
            .executionId(UUID.randomUUID())
            .settlementRunId(settlementRunId)
            .settlementDate(null)
            .build();

        return transactionRepository.save(transaction);
    }

    private TransactionEntity getTransactionById(Long transactionId) {
        String sql = """
            SELECT id, order_id, transaction_type, portfolio_id, transaction_uuid, created_at, updated_at,
                   transaction_datetime, venue_execution_id, description, quantity, leaves_quantity, price,
                   currency, int_order_id, ext_order_id, base_currency, venue_account, parent_order_id,
                   root_order_id, root_execution_id, execution_id, settlement_run_id, settlement_date
            FROM transactions WHERE id = ?
            """;

        return jdbcTemplate.queryForObject(sql, (rs, rowNum) -> TransactionEntity.builder()
            .id(rs.getLong("id"))
            .orderId((UUID) rs.getObject("order_id"))
            .transactionType(TransactionType.valueOf(rs.getString("transaction_type")))
            .portfolioId((UUID) rs.getObject("portfolio_id"))
            .transactionUuid((UUID) rs.getObject("transaction_uuid"))
            .createdAt(rs.getTimestamp("created_at"))
            .updatedAt(rs.getTimestamp("updated_at"))
            .transactionDatetime(rs.getTimestamp("transaction_datetime"))
            .venueExecutionId((UUID) rs.getObject("venue_execution_id"))
            .description(rs.getString("description"))
            .quantity(rs.getBigDecimal("quantity"))
            .leavesQuantity(rs.getBigDecimal("leaves_quantity"))
            .price(rs.getBigDecimal("price"))
            .currency(rs.getString("currency"))
            .intOrderId((UUID) rs.getObject("int_order_id"))
            .extOrderId((UUID) rs.getObject("ext_order_id"))
            .baseCurrency(rs.getString("base_currency"))
            .venueAccount(rs.getString("venue_account"))
            .parentOrderId((UUID) rs.getObject("parent_order_id"))
            .rootOrderId((UUID) rs.getObject("root_order_id"))
            .rootExecutionId((UUID) rs.getObject("root_execution_id"))
            .executionId((UUID) rs.getObject("execution_id"))
            .settlementRunId(rs.getObject("settlement_run_id") != null ? rs.getLong("settlement_run_id") : null)
            .settlementDate(rs.getTimestamp("settlement_date"))
            .build(), transactionId);
    }
}
