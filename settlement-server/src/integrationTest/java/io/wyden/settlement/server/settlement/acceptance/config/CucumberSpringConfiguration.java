package io.wyden.settlement.server.settlement.acceptance.config;

import io.cucumber.spring.CucumberContextConfiguration;
import io.wyden.settlement.server.settlement.TestContainersIntegrationBase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.testcontainers.junit.jupiter.Testcontainers;

@CucumberContextConfiguration
@Testcontainers
@DirtiesContext
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
                properties = {
                    "tracing.collector.endpoint=disabled",
                    "reconciliation.enabled=false",
                    "logging.level.org.testcontainers.containers=debug",
                    "logging.level.io.wyden.cloudutils.telemetry = warn",
                    "logging.level.root=info",
                    "logging.level.io.wyden=debug"
                })
public class CucumberSpringConfiguration extends TestContainersIntegrationBase {

}
