package io.wyden.settlement.server.settlement.acceptance.steps;

import io.cucumber.datatable.DataTable;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.wyden.published.booking.TransactionType;
import io.wyden.published.settlement.SettlementStatus;
import io.wyden.settlement.client.run.SortingOrder;
import io.wyden.settlement.client.transaction.SettlementStreetCashTrade;
import io.wyden.settlement.client.transaction.TransactionSearchInput;
import io.wyden.settlement.server.settlement.acceptance.SharedTestState;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionService;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static java.lang.Boolean.parseBoolean;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.assertj.core.api.Assertions.assertThat;

public class TransactionSearchStepDefinitionsSteps {

    private final JdbcTemplate jdbcTemplate;

    private List<SettlementStreetCashTrade> searchResults;

    private final TransactionRepository transactionRepository;
    private final TransactionService transactionService;

    private final SettlementRunRepository runRepository;

    private final Map<Integer, TransactionEntity> transactionMap = new HashMap<>();
    private Long runId;

    public TransactionSearchStepDefinitionsSteps(JdbcTemplate jdbcTemplate, TransactionRepository transactionRepository, TransactionService transactionService, SettlementRunRepository runRepository) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionRepository = transactionRepository;
        this.transactionService = transactionService;
        this.runRepository = runRepository;
    }

    @When("Query Transactions")
    public void queryTransactionsWithoutSettlementRunId(DataTable dataTable) {

        Map<String, String> row = dataTable.asMaps().get(0);
        TransactionSearchInput input = toTransactionSearchInput(row, null);
        this.searchResults = getSearchResults(input);
    }

    @When("Query Transactions with settlement run id")
    public void queryTransactionsWithSettlementRunId(DataTable dataTable) {

        Map<String, String> row = dataTable.asMaps().get(0);
        TransactionSearchInput input = toTransactionSearchInput(row, runId);
        this.searchResults = getSearchResults(input);
    }

    @Given("Transactions")
    public void transactionsInState(DataTable dataTable) {

        try {
            runId = runRepository.persist(SettlementRunEntity.builder()
                .status(SettlementStatus.PENDING)
                .createdAt(Timestamp.from(Instant.now()))
                .startAt(Date.valueOf(LocalDate.now()))
                    .venueAccountIds(List.of("account1FromSettlement", "account2FromSettlement"))
                .build());
            // Process data from the table
            List<Map<String, String>> rows = dataTable.asMaps(String.class, String.class);

            for (Map<String, String> row : rows) {
                int index = Integer.parseInt(row.get("i"));

                UUID uuid = UUID.fromString(row.get("uuid"));
                boolean hasAssignedSettlement = nonNull(row.get("hasSettlementRun"));

                String dateTime = row.get("dateTime");
                String orderId = row.get("orderId");
                String portfolioId = row.get("portfolioId");
                String baseCurrency = row.get("baseCurrency");
                String currency = row.get("currency");
                String account = row.get("accountId");
                String rootExecutionId = row.get("rootExecutionId");
                String rootOrderId = row.get("rootOrderId");
                String underlyingExecutionId = row.get("underlyingExecutionId");
                String parentOrderId = row.get("parentOrderId");
                String executionId = row.get("executionId");
                String intOrderId = row.get("intOrderId");
                String extOrderId = row.get("extOrderId");
                String venueExecutionId = row.get("venueExecutionId");
                String description = row.get("description");
                BigDecimal quantity = new BigDecimal(row.get("quantity"));
                BigDecimal price = new BigDecimal(row.get("price"));
                String settledDateTime = row.get("settledDateTime");

                TransactionEntity transaction = TransactionEntity.builder()
                    .orderId(orderId != null ? UUID.fromString(orderId) : null)
                    .transactionType(TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE)
                    .portfolioId(portfolioId != null ? UUID.fromString(portfolioId) : null)
                    .transactionUuid(uuid)
                    .createdAt(Timestamp.from(Instant.now()))
                    .updatedAt(Timestamp.from(Instant.now()))
                    .transactionDatetime(dateTime != null ? Timestamp.valueOf(dateTime) : Timestamp.from(Instant.now()))
                    .venueExecutionId(venueExecutionId != null ? UUID.fromString(venueExecutionId) : null)
                    .description(description)
                    .quantity(quantity)
                    .leavesQuantity(BigDecimal.ZERO)
                    .price(price)
                    .currency(currency)
                    .intOrderId(intOrderId != null ? UUID.fromString(intOrderId) : null)
                    .extOrderId(extOrderId != null ? UUID.fromString(extOrderId) : null)
                    .baseCurrency(baseCurrency)
                    .venueAccount(account)
                    .parentOrderId(parentOrderId != null ? UUID.fromString(parentOrderId) : null)
                    .rootOrderId(rootOrderId != null ? UUID.fromString(rootOrderId) : null)
                    .rootExecutionId(rootExecutionId != null ? UUID.fromString(rootExecutionId) : null)
                    .executionId(executionId != null ? UUID.fromString(executionId) : null)
                    .settlementRunId(hasAssignedSettlement ? runId : null)
                    .settlementDate(settledDateTime != null ? Timestamp.valueOf(settledDateTime) : null)
                    .build();
                long id = transactionRepository.save(transaction);
                TransactionEntity storedEntity = transaction.toBuilder().id(id).build();
                transactionMap.put(index, storedEntity);
            }
        } catch (Exception e) {
            System.err.println("Error initializing test data: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    @Then("Assert returned transactions: {word}")
    public void assertTransactionUuids(String expectedRows) {
        List<String> transactionUUIDs = Arrays.stream(StringUtils.split(expectedRows, ","))
            .map(i -> transactionMap.get(Integer.parseInt(i)))
            .map(TransactionEntity::transactionUuid)
            .map(UUID::toString)
            .toList();
        List<String> responseUUIDS = searchResults.stream()
            .map(SettlementStreetCashTrade::getUuid)
            .toList();
        assertThat(responseUUIDS).containsExactlyElementsOf(transactionUUIDs);
    }

    private TransactionSearchInput toTransactionSearchInput(Map<String, String> row, Long settlementRunId) {
        TransactionSearchInput input = TransactionSearchInput.builder()
            .uuid(row.get("uuid"))
            .currency(toList(row.get("currency")))
            .accountId(toList(row.get("accountId")))
            .portfolioId(toList(row.get("portfolioId")))
            .transactionType(toList(row.get("transactionType")))
            .orderId(row.get("orderId"))
            .parentOrderId(row.get("parentOrderId"))
            .rootOrderId(row.get("rootOrderId"))
            .underlyingExecutionId(row.get("underlyingExecutionId"))
            .executionId(row.get("executionId"))
            .venueExecutionId(row.get("venueExecutionId"))
            .rootExecutionId(row.get("rootExecutionId"))
            .settlementRunId(isNull(settlementRunId) ? null :settlementRunId.toString())
            .isSettled(isNull(row.get("settled")) ? null :parseBoolean(row.get("settled")))
            .selected(isNull(row.get("selected")) ? null :parseBoolean(row.get("selected")))
            .from(row.get("from"))
            .to(row.get("to"))
            .first(isNull(row.get("first")) ? null : Integer.parseInt(row.get("first")))
            .after(row.get("after"))
            .sortingOrder(SortingOrder.valueOf(row.get("sortingOrder")))
            .build();
        return input;
    }

    private List<String> toList(String currency) {
        return isBlank(currency) ? new ArrayList<>() : Arrays.asList(currency.split(","));
    }

    private @NotNull ArrayList<SettlementStreetCashTrade> getSearchResults(TransactionSearchInput input) {
        return new ArrayList<>(transactionService.queryTransactions(input).getAllNodes());
    }
}