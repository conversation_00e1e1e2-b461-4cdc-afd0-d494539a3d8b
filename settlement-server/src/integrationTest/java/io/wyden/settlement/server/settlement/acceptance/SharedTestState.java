package io.wyden.settlement.server.settlement.acceptance;

import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

public class SharedTestState {
    private static final Map<UUID, StreetCashTradeSnapshot> streetCashTrades = new TreeMap<>();
    private static final Map<UUID, ClientCashTradeSnapshot> clientCashTrades = new TreeMap<>();
    private static final Map<String, VenueAccount> venueAccounts = new TreeMap<>();
    private static final Map<String, SettlementRunEntity> settlementRuns = new TreeMap<>();

    private static String currentStartDate;

    public static void addStreetCashTrade(UUID uuid, StreetCashTradeSnapshot trade) {
        streetCashTrades.put(uuid, trade);
    }

    public static StreetCashTradeSnapshot getStreetCashTrade(String uuid) {
        return streetCashTrades.get(uuid);
    }

    public static Map<UUID, StreetCashTradeSnapshot> getAllStreetCashTrades() {
        return new HashMap<>(streetCashTrades);
    }

    public static void addVenueAccount(String accountId, VenueAccount venueAccount) {
        venueAccounts.put(accountId, venueAccount);
    }

    public static String getCurrentStartDate() {
        return currentStartDate;
    }

    public static void setCurrentStartDate(String currentStartDate) {
        SharedTestState.currentStartDate = currentStartDate;
    }

    public static VenueAccount getVenueAccount(String accountId) {
        return venueAccounts.get(accountId);
    }

    public static Map<String, VenueAccount> getAllVenueAccounts() {
        return new HashMap<>(venueAccounts);
    }

    public static void clearStreetCashTrades() {
        streetCashTrades.clear();
    }

    public static void clearVenueAccounts() {
        venueAccounts.clear();
    }

    public static void clearSettlementRuns() {
        settlementRuns.clear();
    }

    public static void clearAll() {
        clearStreetCashTrades();
        clearVenueAccounts();
        clearSettlementRuns();
    }

    public static void addSettlementRun(String startDate, SettlementRunEntity run) {
        settlementRuns.put(startDate, run);
    }

    public static Collection<SettlementRunEntity> getSettlementRuns() {
        return settlementRuns.values();
    }

    public static SettlementRunEntity getSettlementRuns(String startDate) {
        return settlementRuns.get(startDate);
    }
}