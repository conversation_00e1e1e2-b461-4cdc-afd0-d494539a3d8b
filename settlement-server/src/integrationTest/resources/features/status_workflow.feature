Feature: Settlement status from PENDING to COMPLETED

  Background: Generate 3 settlement legs
    Given Subscribe to settlement runs
    Given Venue Accounts
      | accountId | venueName |
      | simulator | Simulator |
    And Assume current date is '*************'

  Scenario: Create settlement run without transactions
    Given Create settlement run with current start date
      | accountIds |
      | simulator  |
    Then Assert number of settlement legs: 0
    Then Assert settlement run status: 'PENDING'

  Scenario: Complete settlement run by processing all legs sequentially
    Given Assume following transaction
      | uuid         | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | quantity     | 1                                    |
      | price        | 70000                                |
      | baseCurrency | BTC                                  |
      | currency     | USD                                  |
      | venueAccount | simulator                            |
    And Assume transaction fees for transaction: '50f90caa-660e-41f4-adfa-cf2031a9109f'
      | amount | currency |
      | 40     | EUR      |
      | 70     | USD      |
    When Create settlement run with current start date
      | accountIds |
      | simulator  |
    # Initial state verification
    Then Assert settlement run status: 'PENDING'
    And Assert settlement run legs
      | direction | currency | quantity | status  |
      | RECEIVE   | BTC      | 1        | PENDING |
      | SEND      | USD      | 70070    | PENDING |
      | SEND      | EUR      | 40       | PENDING |
    And Assert settlement run completedAt is null

    # Process BTC leg
    When Initiate settlement leg for: 'BTC'
    Then Assert settlement run status: 'IN_PROGRESS'
    And Assert settlement run legs
      | direction | currency | quantity | status      |
      | RECEIVE   | BTC      | 1        | IN_PROGRESS |
      | SEND      | USD      | 70070    | PENDING     |
      | SEND      | EUR      | 40       | PENDING     |
    And Assert settlement run completedAt is null

    When Complete settlement leg for: 'BTC'
    Then Assert settlement run status: 'IN_PROGRESS'
    And Assert settlement run legs
      | direction | currency | quantity | status    |
      | RECEIVE   | BTC      | 1        | COMPLETED |
      | SEND      | USD      | 70070    | PENDING   |
      | SEND      | EUR      | 40       | PENDING   |
    And Assert settlement run completedAt is null

    # Process remaining legs
    When Initiate settlement leg for: 'USD'
    And Initiate settlement leg for: 'EUR'
    And Complete settlement leg for: 'USD'
    And Complete settlement leg for: 'EUR'

    # Final state verification
    Then Assert settlement run status: 'COMPLETED'
    And Assert settlement run legs
      | direction | currency | quantity | status    |
      | RECEIVE   | BTC      | 1        | COMPLETED |
      | SEND      | USD      | 70070    | COMPLETED |
      | SEND      | EUR      | 40       | COMPLETED |
    And Assert settlement run completedAt is not null

    Scenario: Try to cancel empty settlement run
      Given Create settlement run with current start date
        | accountIds |
        | simulator  |
      When Cancel settlement run
      Then Assert settlement run status: 'CANCELED'
