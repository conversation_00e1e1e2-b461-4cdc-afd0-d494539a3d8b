Feature: Settlement Reconciliation
  As a settlement system
  I want to reconcile transactions between booking and settlement services
  So that I can ensure data consistency and handle discrepancies

  Scenario: Reconciliation: Booking Transactions ✅ -> Settlement Database ❌
    Given Assume following client side booking transaction
      | uuid                  | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | quantity              | 1                                    |
      | price                 | 70000                                |
      | baseCurrency          | BTC                                  |
      | currency              | USD                                  |
      | counterPortfolio      | 60f90caa-660e-41f4-adfa-cf2031a9109f |
      | settled               | true                                 |
      | settledDate           | 2025-07-24T14:30:00Z                 |
      | clientSettlementRunId | 1                                    |
      | fee1Amount            | 40                                   |
      | fee1Currency          | EUR                                  |
      | fee2Amount            | 70                                   |
      | fee2Currency          | USD                                  |
    When Perform reconciliation
    Then Assert transaction is persisted in settlement database
      | uuid             | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | quantity         | 1                                    |
      | price            | 70000                                |
      | baseCurrency     | BTC                                  |
      | currency         | USD                                  |
      | counterPortfolio | 60f90caa-660e-41f4-adfa-cf2031a9109f |
      | settled          | true                                 |
      | settledDate      | 2025-07-24T14:30:00Z                 |
    Then Assert seq number is persisted in settlement database
    Then Assert settlement run legs
      | direction | currency | quantity | status    |
      | RECEIVE   | BTC      | 1        | COMPLETED |
      | SEND      | USD      | 70070    | COMPLETED |
      | SEND      | EUR      | 40       | COMPLETED |


  Scenario: Reconciliation: Both Street/Client cash trades Booking Transactions ✅ -> Settlement Database ❌
    Given Assume following client side booking transaction
      | uuid                  | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | quantity              | 1                                    |
      | price                 | 70000                                |
      | baseCurrency          | BTC                                  |
      | currency              | USD                                  |
      | counterPortfolio      | 60f90caa-660e-41f4-adfa-cf2031a9109f |
      | settled               | true                                 |
      | settledDate           | 2025-07-24T14:30:00Z                 |
      | clientSettlementRunId | 1                                    |
      | fee1Amount            | 40                                   |
      | fee1Currency          | EUR                                  |
      | fee2Amount            | 70                                   |
      | fee2Currency          | USD                                  |
    Given Assume following street side booking transaction
      | uuid                  | 70f90caa-660e-41f4-adfa-cf2031a9109f |
      | quantity              | 2                                    |
      | price                 | 50000                                |
      | baseCurrency          | BTC                                  |
      | currency              | USD                                  |
      | accountId             | account                              |
      | settled               | true                                 |
      | settledDate           | 2025-07-24T14:31:00Z                 |
      | clientSettlementRunId | 1                                    |
      | fee1Amount            | 60                                   |
      | fee1Currency          | EUR                                  |
      | fee2Amount            | 60                                   |
      | fee2Currency          | USD                                  |
    When Perform reconciliation
    Then Assert transaction is persisted in settlement database
      | uuid         | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | quantity     | 1                                    |
      | price        | 70000                                |
      | baseCurrency | BTC                                  |
      | currency     | USD                                  |
      | settled      | true                                 |
      | settledDate  | 2025-07-24T14:30:00Z                 |
    Then Assert seq number is persisted in settlement database
    Then Assert settlement run legs
      | direction | currency | quantity | status    |
      | RECEIVE   | BTC      | 1        | COMPLETED |
      | SEND      | USD      | 70070    | COMPLETED |
      | SEND      | EUR      | 40       | COMPLETED |
      | RECEIVE   | BTC      | 1        | COMPLETED |
      | SEND      | USD      | 100060   | COMPLETED |
      | SEND      | EUR      | 60       | COMPLETED |
