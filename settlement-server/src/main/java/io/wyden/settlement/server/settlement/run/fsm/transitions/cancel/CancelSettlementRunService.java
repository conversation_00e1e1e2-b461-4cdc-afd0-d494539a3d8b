package io.wyden.settlement.server.settlement.run.fsm.transitions.cancel;

import io.wyden.published.settlement.SettlementOperation;
import io.wyden.published.settlement.SettlementStatus;
import io.wyden.settlement.client.transaction.TransactionSearchInput;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.sink.StateChangeRabbitEmitter;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.run.fsm.state.RunStateCompleted;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CancelSettlementRunService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CancelSettlementRunService.class);

    private final SettlementRunRepository runRepository;
    private final TransactionRepository transactionRepository;
    private final StateChangeRabbitEmitter stateChangeRabbitEmitter;

    public CancelSettlementRunService(SettlementRunRepository runRepository,
                                      TransactionRepository transactionRepository,
                                      StateChangeRabbitEmitter stateChangeRabbitEmitter) {
        this.runRepository = runRepository;
        this.transactionRepository = transactionRepository;
        this.stateChangeRabbitEmitter = stateChangeRabbitEmitter;
    }

    public RunStateCompleted cancelSettlementRun(SettlementRunEntity entity) {

        validateRunState(entity);
        validateAllLegsInPendingState(entity);
        validateAllTransactionUnsettled(entity);

        //1. Reset settlement run id
        transactionRepository.setSettlementRunAsNull(entity.id());
        //2. Set statuses
        List<SettlementLegEntity> canceledLegs = entity.legs()
            .stream()
            .map(leg -> leg.toBuilder().status(SettlementStatus.CANCELED).build())
            .toList();
        SettlementRunEntity updatedEntity = entity.toBuilder()
            .status(SettlementStatus.CANCELED)
            .legs(canceledLegs)
            .build();
        runRepository.persist(updatedEntity);
        //3. Emit changes
        stateChangeRabbitEmitter.emitRunStateChange(entity.id(), SettlementOperation.MOVED_TO_CANCELED);
        LOGGER.info("Settlement run cancelled.");
        return new RunStateCompleted(updatedEntity);
    }

    private void validateRunState(SettlementRunEntity entity) {

        if (!SettlementStatus.PENDING.equals(entity.status())) {
            LOGGER.error("Settlement Run cannot be canceled as it is not in PENDING status");
            throw new CancelSettlementRunException("Settlement Run cannot be canceled as it is not in PENDING status");
        }
    }

    private void validateAllLegsInPendingState(SettlementRunEntity runEntity) {
        boolean allMatch = runEntity.legs()
            .stream().allMatch(leg -> SettlementStatus.PENDING.equals(leg.status()));

        if (!allMatch) {
            LOGGER.error("Settlement Run cannot be canceled as its Legs are not in PENDING status");
            throw new CancelSettlementRunException("Settlement Run cannot be canceled as its Legs are not in PENDING status");
        }
    }

    private void validateAllTransactionUnsettled(SettlementRunEntity runEntity) {
        boolean allMatch = transactionRepository.searchTransactions(TransactionSearchInput.builder().isSettled(false).settlementRunId("" + runEntity.id()).build(), false)
            .stream().allMatch(t -> t.settlementDate() == null);

        if (!allMatch) {
            LOGGER.error("Settlement Run cannot be canceled as its transactions are settled!");
            throw new CancelSettlementRunException("Settlement Run cannot be canceled due to some transactions are settled!");
        }
    }
}