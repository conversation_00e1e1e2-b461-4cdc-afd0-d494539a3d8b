package io.wyden.settlement.server.settlement.transaction;

import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;

import java.math.BigDecimal;
import java.util.List;

public class FeeMapper {

    public static List<TransactionFeeEntity> mapToTransactionFeesEntity(List<Fee> fees, long transactionId) {
            return fees
                .stream()
                .filter(fee -> fee.getFeeType() == FeeType.EXCHANGE_FEE)
                .map(protoFee -> TransactionFeeEntity.builder()
                    .transactionId(transactionId)
                    .transactionFeeAmount(parseBigDecimal(protoFee.getAmount()))
                    .transactionFeeCurrency(protoFee.getCurrency())
                    .transactionFeeType(protoFee.getFeeType().name())
                    .build())
                .toList();
    }

    private static BigDecimal parseBigDecimal(String str) {
        if (str == null || str.isEmpty()) {
            return null;
        }
        try {
            return new BigDecimal(str);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}