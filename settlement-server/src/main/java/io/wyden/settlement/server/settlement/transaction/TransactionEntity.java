package io.wyden.settlement.server.settlement.transaction;

import io.wyden.published.booking.TransactionType;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.UUID;

public record TransactionEntity(
    Long id,
    UUID orderId,
    TransactionType transactionType,
    UUID portfolioId,
    UUID transactionUuid,
    Timestamp createdAt,
    Timestamp updatedAt,
    Timestamp transactionDatetime,
    UUID venueExecutionId,
    String description,
    BigDecimal quantity,
    BigDecimal leavesQuantity,
    BigDecimal price,
    String currency,
    UUID intOrderId,
    UUID extOrderId,
    String baseCurrency,
    String venueAccount,
    UUID counterPortfolioId,
    UUID parentOrderId,
    UUID rootOrderId,
    UUID rootExecutionId,
    UUID executionId,
    Long settlementRunId,
    String clientSettlementRunId,
    Timestamp settlementDate
) {
    public static Builder builder() {
        return new Builder();
    }

    public Builder toBuilder() {
        return new Builder()
            .id(this.id)
            .orderId(this.orderId)
            .transactionType(this.transactionType)
            .portfolioId(this.portfolioId)
            .transactionUuid(this.transactionUuid)
            .createdAt(this.createdAt)
            .updatedAt(this.updatedAt)
            .transactionDatetime(this.transactionDatetime)
            .venueExecutionId(this.venueExecutionId)
            .description(this.description)
            .quantity(this.quantity)
            .leavesQuantity(this.leavesQuantity)
            .price(this.price)
            .currency(this.currency)
            .intOrderId(this.intOrderId)
            .extOrderId(this.extOrderId)
            .baseCurrency(this.baseCurrency)
            .venueAccount(this.venueAccount)
            .counterPortfolioId(this.counterPortfolioId)
            .parentOrderId(this.parentOrderId)
            .rootOrderId(this.rootOrderId)
            .rootExecutionId(this.rootExecutionId)
            .executionId(this.executionId)
            .settlementRunId(this.settlementRunId)
            .clientSettlementRunId(this.clientSettlementRunId)
            .settlementDate(this.settlementDate)
            .counterPortfolioId(this.counterPortfolioId);
    }

    public static class Builder {
        private Long id;
        private UUID orderId;
        private TransactionType transactionType;
        private UUID portfolioId;
        private UUID transactionUuid;
        private Timestamp createdAt;
        private Timestamp updatedAt;
        private Timestamp transactionDatetime;
        private UUID venueExecutionId;
        private String description;
        private BigDecimal quantity;
        private BigDecimal leavesQuantity;
        private BigDecimal price;
        private String currency;
        private UUID intOrderId;
        private UUID extOrderId;
        private String baseCurrency;
        private String venueAccount;
        private UUID parentOrderId;
        private UUID rootOrderId;
        private UUID rootExecutionId;
        private UUID executionId;
        private Long settlementRunId;
        private String clientSettlementRunId;
        private Timestamp settlementDate;
        private UUID counterPortfolioId;

        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        public Builder orderId(UUID orderId) {
            this.orderId = orderId;
            return this;
        }

        public Builder transactionType(TransactionType transactionType) {
            this.transactionType = transactionType;
            return this;
        }

        public Builder portfolioId(UUID portfolioId) {
            this.portfolioId = portfolioId;
            return this;
        }

        public Builder transactionUuid(UUID transactionUuid) {
            this.transactionUuid = transactionUuid;
            return this;
        }

        public Builder createdAt(Timestamp createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public Builder updatedAt(Timestamp updatedAt) {
            this.updatedAt = updatedAt;
            return this;
        }

        public Builder transactionDatetime(Timestamp transactionDatetime) {
            this.transactionDatetime = transactionDatetime;
            return this;
        }

        public Builder venueExecutionId(UUID venueExecutionId) {
            this.venueExecutionId = venueExecutionId;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder quantity(BigDecimal quantity) {
            this.quantity = quantity;
            return this;
        }

        public Builder leavesQuantity(BigDecimal leavesQuantity) {
            this.leavesQuantity = leavesQuantity;
            return this;
        }

        public Builder price(BigDecimal price) {
            this.price = price;
            return this;
        }

        public Builder currency(String currency) {
            this.currency = currency;
            return this;
        }

        public Builder intOrderId(UUID intOrderId) {
            this.intOrderId = intOrderId;
            return this;
        }

        public Builder extOrderId(UUID extOrderId) {
            this.extOrderId = extOrderId;
            return this;
        }

        public Builder baseCurrency(String baseCurrency) {
            this.baseCurrency = baseCurrency;
            return this;
        }

        public Builder venueAccount(String venueAccount) {
            this.venueAccount = venueAccount;
            return this;
        }

        public Builder parentOrderId(UUID parentOrderId) {
            this.parentOrderId = parentOrderId;
            return this;
        }

        public Builder rootOrderId(UUID rootOrderId) {
            this.rootOrderId = rootOrderId;
            return this;
        }

        public Builder rootExecutionId(UUID rootExecutionId) {
            this.rootExecutionId = rootExecutionId;
            return this;
        }

        public Builder executionId(UUID executionId) {
            this.executionId = executionId;
            return this;
        }

        public Builder settlementRunId(Long settlementRunId) {
            this.settlementRunId = settlementRunId;
            return this;
        }

        public Builder clientSettlementRunId(String clientSettlementRunId) {
            this.clientSettlementRunId = clientSettlementRunId;
            return this;
        }

        public Builder settlementDate(Timestamp settlementDate) {
            this.settlementDate = settlementDate;
            return this;
        }

        public Builder counterPortfolioId(UUID counterPortfolioId) {
            this.counterPortfolioId = counterPortfolioId;
            return this;
        }

        public TransactionEntity build() {
            return new TransactionEntity(
                id, orderId, transactionType, portfolioId, transactionUuid, createdAt, updatedAt,
                transactionDatetime, venueExecutionId, description, quantity, leavesQuantity, price,
                currency, intOrderId, extOrderId, baseCurrency, venueAccount, counterPortfolioId, parentOrderId,
                rootOrderId, rootExecutionId, executionId, settlementRunId, clientSettlementRunId, settlementDate
            );
        }
    }
}