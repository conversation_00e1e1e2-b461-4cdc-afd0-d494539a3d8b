package io.wyden.settlement.server.settlement.api;

import io.wyden.settlement.client.settlementconfiguration.SettlementConfigurationInput;
import io.wyden.settlement.client.settlementconfiguration.SettlementConfigurationResponse;
import io.wyden.settlement.server.settlement.MutationSubmittedResponse;
import io.wyden.settlement.server.settlement.settlementconfiguration.SettlementConfigurationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/settlement/configuration")
public class SettlementConfigurationRestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SettlementConfigurationRestController.class);

    private final SettlementConfigurationService configurationService;

    public SettlementConfigurationRestController(SettlementConfigurationService configurationService) {
        this.configurationService = configurationService;
    }

    @GetMapping
    @PreAuthorize("hasPermission('settlement', 'read')")
    public Mono<Collection<SettlementConfigurationResponse>> getAllSettlementConfigurations(
            @RequestParam(required = false) String timeZone) {

        LOGGER.info("Getting all settlement configurations");
        
        Collection<SettlementConfigurationResponse> configurations = 
            configurationService.getSettlementConfigurations(timeZone);
        
        return Mono.just(configurations);
    }

    @GetMapping("/{accountId}")
    @PreAuthorize("hasPermission('settlement', 'read')")
    public Mono<SettlementConfigurationResponse> getSettlementConfiguration(
            @PathVariable String accountId,
            @RequestParam(required = false) String timeZone) {

        LOGGER.info("Getting settlement configuration for account: {}", accountId);
        
        Collection<SettlementConfigurationResponse> configurations = 
            configurationService.getSettlementConfigurations(timeZone);
        
        Optional<SettlementConfigurationResponse> accountConfig = configurations.stream()
            .filter(config -> accountId.equals(config.accountId()))
            .findFirst();

        return accountConfig.map(Mono::just).orElse(Mono.empty());
    }

    @PutMapping("/{accountId}")
    @PreAuthorize("hasPermission('settlement', 'manage')")
    public Mono<MutationSubmittedResponse> updateSettlementConfiguration(
            @PathVariable String accountId,
            @RequestBody SettlementConfigurationUpdateRequest request) {

        LOGGER.info("Updating settlement configuration for account: {}", accountId);

        SettlementConfigurationInput configInput = new SettlementConfigurationInput(accountId, request.config());
        List<SettlementConfigurationInput> configurations = List.of(configInput);
        
        MutationSubmittedResponse response = configurationService.settlementConfigurationUpdate(configurations);
        return Mono.just(response);
    }

    @PutMapping
    @PreAuthorize("hasPermission('settlement', 'manage')")
    public Mono<MutationSubmittedResponse> updateMultipleSettlementConfigurations(
            @RequestBody List<SettlementConfigurationInput> configurations) {

        LOGGER.info("Updating multiple settlement configurations, count: {}", configurations.size());
        
        MutationSubmittedResponse response = configurationService.settlementConfigurationUpdate(configurations);
        return Mono.just(response);
    }

    public record SettlementConfigurationUpdateRequest(
        io.wyden.settlement.client.settlementconfiguration.SettlementAccountConfigurationInput config
    ) {}
}
