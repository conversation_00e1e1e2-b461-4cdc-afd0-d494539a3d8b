package io.wyden.settlement.server.settlement.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.LockedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ResponseStatusException;

import java.util.NoSuchElementException;

@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler({AccessDeniedException.class})
    public ResponseEntity<String> accessDeniedExceptionHandler(AccessDeniedException exception) {
        LOGGER.error("AccessDeniedException: {}", exception.getMessage());
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(exception.getMessage());
    }

    @ExceptionHandler({NoSuchElementException.class})
    public ResponseEntity<String> noSuchElementExceptionHandler(NoSuchElementException exception) {
        LOGGER.warn("New NoSuchElementException {}", exception.getMessage(), exception.getCause());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(exception.getMessage());
    }

    @ExceptionHandler({Exception.class})
    public ResponseEntity<String> exceptionHandler(Exception ex) {
        LOGGER.error("Exception: {}", ex.getMessage(), ex);
        return ResponseEntity.status(500).body(ex.getMessage());
    }

    @ExceptionHandler({IllegalArgumentException.class})
    public ResponseEntity<String> illegalArgumentExceptionHandler(IllegalArgumentException exception) {
        LOGGER.error("IllegalArgumentException: {}", exception.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exception.getMessage());
    }

    @ExceptionHandler({WebClientResponseException.class})
    public ResponseEntity<String> webClientResponseExceptionHandler(WebClientResponseException exception) {
        LOGGER.warn("WebClientResponseException {}, {}", exception.getMessage(), exception.getResponseBodyAsString());
        return ResponseEntity.status(exception.getStatusCode()).body(exception.getMessage());
    }

    @ExceptionHandler({ResponseStatusException.class})
    public ResponseEntity<String> responseStatusExceptionHandler(ResponseStatusException exception) {
        LOGGER.warn("Response status exception {}, {}", exception.getMessage(), exception.getMessage());
        return ResponseEntity.status(exception.getStatusCode()).body(exception.getMessage());
    }

    @ResponseStatus(HttpStatus.LOCKED)
    public void lockedExceptionHandler(LockedException exception) {
        LOGGER.warn("LockedException: {}", exception.getMessage());
    }

    @ExceptionHandler({RuntimeException.class})
    public ResponseEntity<String> runtimeExceptionHandler(RuntimeException exception) {
        LOGGER.error("RuntimeException: {}", exception.toString());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(exception.getMessage());
    }

}
