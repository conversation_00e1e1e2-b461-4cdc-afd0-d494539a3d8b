package io.wyden.settlement.server.settlement.transaction;

import io.micrometer.common.util.StringUtils;
import io.wyden.published.booking.TransactionType;
import io.wyden.settlement.client.run.SortingOrder;
import io.wyden.settlement.client.transaction.TransactionSearchInput;
import io.wyden.settlement.server.settlement.SqlBuilder;
import io.wyden.settlement.server.settlement.SqlBuilder.SqlFilterBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import static java.util.Objects.isNull;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
public class TransactionRepository {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionRepository.class);

    private final NamedParameterJdbcTemplate namedJdbcTemplate;
    private final JdbcTemplate jdbcTemplate;
    private final TransactionEntityRowMapper transactionRowMapper = new TransactionEntityRowMapper();

    private static final String BASE_SELECT = "SELECT * FROM transactions";
    private static final String BASE_COUNT = "SELECT COUNT(*) FROM transactions";

    public TransactionRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.namedJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
    }

    public List<TransactionEntity> searchTransactions(TransactionSearchInput searchInput, boolean addUnassignedTransactions) {
        MapSqlParameterSource params = new MapSqlParameterSource();
        SqlBuilder sqlBuilder = new SqlBuilder("");

        if (!isNull(searchInput)) {
            parseFilters(params, sqlBuilder, searchInput);
        }

        if (!isNull(searchInput) && searchInput.settlementRunId() != null && addUnassignedTransactions) {
            // we need a new builder because of union
            String subquery = unionSettledAndUnsettledTransactions(params, sqlBuilder, Long.parseLong(searchInput.settlementRunId())).build();
            sqlBuilder = new SqlBuilder("SELECT * FROM (%s) as t ".formatted(subquery));
        } else {
            // Standard query for general search
            sqlBuilder = new SqlBuilder(BASE_SELECT)
                .where(sqlBuilder.whereConditions);
        }

        sqlBuilder.orderBy("updated_at", searchInput.sortingOrder() == SortingOrder.ASC)
            .orderBy("settlement_run_id", true);

        applyPagination(sqlBuilder, searchInput, params);
        return namedJdbcTemplate.query(sqlBuilder.build(), params, transactionRowMapper);
    }

    public int countTransactions(TransactionSearchInput searchInput) {
        MapSqlParameterSource params = new MapSqlParameterSource();
        SqlBuilder sqlBuilder = new SqlBuilder("");
        parseFilters(params, sqlBuilder, searchInput);

        return count(searchInput, params, sqlBuilder);
    }

    private int count(TransactionSearchInput searchInput, MapSqlParameterSource params, SqlBuilder sqlBuilder) {
        String sql;
        if (searchInput.settlementRunId() != null) {
            String subquery = unionSettledAndUnsettledTransactions(params, sqlBuilder, Long.parseLong(searchInput.settlementRunId())).build();
            sql = "SELECT COUNT(*) FROM (%s) as t".formatted(subquery);
        } else {
            // Standard query for general search
            sql = new SqlBuilder(BASE_COUNT).where(sqlBuilder.whereConditions).build();
        }

        return namedJdbcTemplate.queryForObject(sql, params, Integer.class);
    }

    public int countRemaining(TransactionSearchInput searchInput) {
        if (searchInput.after() == null) {
            return countTransactions(searchInput) - (isNull(searchInput.first()) ? 0 : searchInput.first());
        }

        MapSqlParameterSource params = new MapSqlParameterSource();
        SqlBuilder sqlBuilder = new SqlBuilder("");

        parseFilters(params, sqlBuilder, searchInput);
        applyPagination(sqlBuilder, searchInput, params);

        return count(searchInput, params, sqlBuilder);
    }

    public int setSettlementIdForCriteria(TransactionSearchInput searchInput, Long runId) {
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("runId", runId);

        SqlBuilder sqlBuilder = new SqlBuilder("UPDATE transactions SET settlement_run_id = :runId");
        sqlBuilder.whereConditions.add(" settlement_run_id IS NULL ");
        parseFilters(params, sqlBuilder, searchInput);

        return namedJdbcTemplate.update(sqlBuilder.build(), params);
    }

    public int unsetSettlementIdForCriteria(TransactionSearchInput searchInput, Long runId) {
        MapSqlParameterSource params = new MapSqlParameterSource();

        SqlBuilder sqlBuilder = new SqlBuilder("UPDATE transactions SET settlement_run_id = NULL");
        sqlBuilder.whereConditions.add("settlement_run_id = :runId");

        params.addValue("runId", runId);
        parseFilters(params, sqlBuilder, searchInput);

        return namedJdbcTemplate.update(sqlBuilder.build(), params);
    }

    public void setSettlementDate(Long runId) {
        String sql = "UPDATE transactions SET settlement_date = now() WHERE settlement_run_id = ?";
        jdbcTemplate.update(sql, runId);
    }

    public void setSettlementRunAsNull(long parsedRunId) {
        String sql = "UPDATE transactions SET settlement_run_id = null WHERE settlement_run_id = ?";
        jdbcTemplate.update(sql, parsedRunId);
    }

    public void setSettlementRunAsNull(long parsedRunId, UUID transactionUUID) {
        String sql = "UPDATE transactions SET settlement_run_id = null WHERE settlement_run_id = ? AND transaction_uuid = ?";
        jdbcTemplate.update(sql, parsedRunId, transactionUUID);
    }

    public List<CurrencyAmount> findCurrencyAmounts(long runId) {
        MapSqlParameterSource params = new MapSqlParameterSource()
            .addValue("runId", runId);

        // Base currency query
        SqlBuilder baseCurrencyQuery = new SqlBuilder(
            "SELECT venue_account as ACCOUNT_ID, base_currency as CURRENCY, SUM(quantity) AS AMOUNT FROM transactions"
        );
        baseCurrencyQuery.where("settlement_run_id = :runId")
            .groupBy("venue_account")
            .groupBy("base_currency");

        // Opposite currency query
        SqlBuilder oppositeCurrencyQuery = new SqlBuilder(
            "SELECT venue_account as ACCOUNT_ID, currency, SUM(quantity * price) AS AMOUNT FROM transactions"
        );
        oppositeCurrencyQuery.where("settlement_run_id = :runId")
            .groupBy("venue_account")
            .groupBy("currency");

        // Execute both queries
        List<CurrencyAmount> main = namedJdbcTemplate.query(
            baseCurrencyQuery.build(),
            params,
            (rs, rowNum) -> new CurrencyAmount(
                rs.getString("ACCOUNT_ID"),
                rs.getString("CURRENCY"),
                rs.getBigDecimal("AMOUNT")
            )
        );

        List<CurrencyAmount> opposite = namedJdbcTemplate.query(
            oppositeCurrencyQuery.build(),
            params,
            (rs, rowNum) -> new CurrencyAmount(
                rs.getString("ACCOUNT_ID"),
                rs.getString("CURRENCY"),
                rs.getBigDecimal("AMOUNT").negate()
            )
        );

        return Stream.concat(main.stream(), opposite.stream()).toList();
    }

    public long save(TransactionEntity transaction) {
        String sql = """
                INSERT INTO transactions (
                    order_id, portfolio_id, transaction_uuid, created_at, updated_at,
                    transaction_datetime, venue_execution_id, description, quantity,
                    leaves_quantity, price, currency, int_order_id, ext_order_id, base_currency,
                    venue_account, counter_portfolio_id, parent_order_id, root_order_id, root_execution_id,
                    execution_id, settlement_run_id, client_settlement_run_id, transaction_type, settlement_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        KeyHolder keyHolder = new GeneratedKeyHolder();

        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, new String[]{"id"});
            ps.setObject(1, transaction.orderId());
            ps.setObject(2, transaction.portfolioId());
            ps.setObject(3, transaction.transactionUuid());
            ps.setTimestamp(4, transaction.createdAt());
            ps.setTimestamp(5, transaction.updatedAt());
            ps.setTimestamp(6, transaction.transactionDatetime());
            ps.setObject(7, transaction.venueExecutionId());
            ps.setString(8, transaction.description());
            ps.setBigDecimal(9, transaction.quantity());
            ps.setBigDecimal(10, transaction.leavesQuantity());
            ps.setBigDecimal(11, transaction.price());
            ps.setString(12, transaction.currency());
            ps.setObject(13, transaction.intOrderId());
            ps.setObject(14, transaction.extOrderId());
            ps.setString(15, transaction.baseCurrency());
            ps.setString(16, transaction.venueAccount());
            ps.setObject(17, transaction.counterPortfolioId());
            ps.setObject(18, transaction.parentOrderId());
            ps.setObject(19, transaction.rootOrderId());
            ps.setObject(20, transaction.rootExecutionId());
            ps.setObject(21, transaction.executionId());
            ps.setObject(22, transaction.settlementRunId());
            ps.setObject(23, transaction.clientSettlementRunId());
            ps.setString(24, transaction.transactionType() != null ? transaction.transactionType().name() : null);
            ps.setTimestamp(25, transaction.settlementDate() != null ? transaction.settlementDate() : null);
            return ps;
        }, keyHolder);

        return keyHolder.getKey().longValue();
    }

    public List<TransactionEntity> findAll() {
        return jdbcTemplate.query(BASE_SELECT, transactionRowMapper);
    }

    public Optional<TransactionEntity> findByUUID(UUID transactionUuid) {
        String sql = BASE_SELECT + " WHERE transaction_uuid = ?";
        return jdbcTemplate.query(sql, ps -> ps.setObject(1, transactionUuid), rs -> {
            if (rs.next()) {
                return Optional.of(map(rs));
            } else {
                return Optional.empty();
            }
        });
    }

    public List<TransactionEntity> findUnsettledTransactions(List<String> accountIds) {
        if (accountIds == null || accountIds.isEmpty()) {
            return Collections.emptyList();
        }

        String placeholders = String.join(",", Collections.nCopies(accountIds.size(), "?"));
        String sql = BASE_SELECT + " WHERE settlement_run_id IS NULL AND venue_account IN (" + placeholders + ")";

        Object[] params = accountIds.toArray(new Object[0]);
        return jdbcTemplate.query(sql, transactionRowMapper, params);
    }

    public void setSettlementId(UUID transactionUuid, Long settlementRunId) {
        String sql = "UPDATE transactions SET settlement_run_id = ? WHERE transaction_uuid = ?";
        jdbcTemplate.update(sql, settlementRunId, transactionUuid);
    }

    public void setSettlementId(List<String> accountIds, Long settlementRunId) {
        if (accountIds == null || accountIds.isEmpty()) {
            return;
        }

        String placeholders = String.join(",", Collections.nCopies(accountIds.size(), "?"));
        String sql = "UPDATE transactions SET settlement_run_id = ? WHERE venue_account IN (" + placeholders + ") AND settlement_run_id IS NULL " +
                     "AND settlement_date IS NULL";

        Object[] allParams = Stream.concat(
            Stream.of(settlementRunId),
            accountIds.stream()
        ).toArray();

        jdbcTemplate.update(sql, allParams);
    }

    public void setSettlementRunId(long runId, UUID uuid) {
        String sql = "UPDATE transactions SET settlement_run_id = ? WHERE transaction_uuid = ? AND settlement_run_id IS NULL";
        jdbcTemplate.update(sql, runId, uuid);
    }

    public void markClientTransactionWithSettlementRunId(long runId, List<UUID> transactionUUIDs) {
        String placeholders = String.join(",", Collections.nCopies(transactionUUIDs.size(), "?"));
        Object[] allParams = Stream.concat(
            Stream.of(runId),
            transactionUUIDs.stream()
        ).toArray();

        String sql = "UPDATE transactions SET settlement_run_id = ? WHERE transaction_uuid IN (" + placeholders + ") AND settlement_run_id IS NULL";
        jdbcTemplate.update(sql, allParams);

    }

    public List<Long> saveAllAndReturnIds(List<TransactionEntity> transactions) {
        if (transactions.isEmpty()) {
            return List.of();
        }

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("""
            INSERT INTO transactions (
                order_id, portfolio_id, transaction_uuid, created_at, updated_at,
                transaction_datetime, venue_execution_id, description, quantity,
                leaves_quantity, price, currency, int_order_id, ext_order_id, base_currency,
                venue_account, counter_portfolio_id, parent_order_id, root_order_id, root_execution_id,
                execution_id, settlement_run_id, client_settlement_run_id, transaction_type, settlement_date
            ) VALUES
            """);

        for (int i = 0; i < transactions.size(); i++) {
            if (i > 0) sqlBuilder.append(", ");
            sqlBuilder.append("(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        }
        sqlBuilder.append(" RETURNING id");

        return jdbcTemplate.query(sqlBuilder.toString(), ps -> {
            int paramIndex = 1;
            for (TransactionEntity transaction : transactions) {
                ps.setObject(paramIndex++, transaction.orderId());
                ps.setObject(paramIndex++, transaction.portfolioId());
                ps.setObject(paramIndex++, transaction.transactionUuid());
                ps.setTimestamp(paramIndex++, transaction.createdAt());
                ps.setTimestamp(paramIndex++, transaction.updatedAt());
                ps.setTimestamp(paramIndex++, transaction.transactionDatetime());
                ps.setObject(paramIndex++, transaction.venueExecutionId());
                ps.setString(paramIndex++, transaction.description());
                ps.setBigDecimal(paramIndex++, transaction.quantity());
                ps.setBigDecimal(paramIndex++, transaction.leavesQuantity());
                ps.setBigDecimal(paramIndex++, transaction.price());
                ps.setString(paramIndex++, transaction.currency());
                ps.setObject(paramIndex++, transaction.intOrderId());
                ps.setObject(paramIndex++, transaction.extOrderId());
                ps.setString(paramIndex++, transaction.baseCurrency());
                ps.setString(paramIndex++, transaction.venueAccount());
                ps.setObject(paramIndex++, transaction.counterPortfolioId());
                ps.setObject(paramIndex++, transaction.parentOrderId());
                ps.setObject(paramIndex++, transaction.rootOrderId());
                ps.setObject(paramIndex++, transaction.rootExecutionId());
                ps.setObject(paramIndex++, transaction.executionId());
                ps.setObject(paramIndex++, transaction.settlementRunId());
                ps.setObject(paramIndex++, transaction.clientSettlementRunId());
                ps.setString(paramIndex++, transaction.transactionType() != null ? transaction.transactionType().name() : null);
                ps.setTimestamp(paramIndex++, transaction.settlementDate() != null ? transaction.settlementDate() : null);
            }
        }, (rs, rowNum) -> rs.getLong("id"));
    }

    public void clear() {
        jdbcTemplate.update("DELETE FROM transactions");
    }

    private void parseFilters(MapSqlParameterSource params, SqlBuilder builder, TransactionSearchInput searchInput) {

        if (searchInput.settled() == Boolean.TRUE) {
            builder.whereConditions.add("settlement_date IS NOT NULL ");
        } else if (searchInput.settled() == Boolean.FALSE) {
            builder.whereConditions.add("settlement_date IS NULL ");
        }

        if (searchInput.selected() == Boolean.TRUE) {
            builder.whereConditions.add("settlement_run_id IS NOT NULL ");
        } else if (searchInput.selected() == Boolean.FALSE) {
            builder.whereConditions.add("settlement_run_id IS NULL ");
        }

        List<UUID> parsedUUIDS = isNotBlank(searchInput.uuid()) ? Arrays.stream(searchInput.uuid().split(","))
            .map(UUID::fromString)
            .toList() : null;

        new SqlFilterBuilder(params, builder)
            .addUUIDListFilter(parsedUUIDS, "transaction_uuid", "transaction_uuids")
            .addListFilter(searchInput.currency(), "currency", "currencies")
            .addListFilter(searchInput.accountId(), "venue_account", "accounts")
            .addUUIDListFilter(toUUIDList(searchInput.portfolioId()), "portfolio_id", "portfolios")
            .addUUIDFilter(searchInput.orderId(), "order_id", "orderId")
            .addUUIDFilter(searchInput.parentOrderId(), "parent_order_id", "parentOrderId")
            .addUUIDFilter(searchInput.rootOrderId(), "root_order_id", "rootOrderId")
            .addUUIDFilter(searchInput.executionId(), "execution_id", "executionId")
            .addUUIDFilter(searchInput.venueExecutionId(), "venue_execution_id", "venueExecutionId")
            .addUUIDFilter(searchInput.rootExecutionId(), "root_execution_id", "rootExecutionId")
            .addDateRangeFilter(searchInput.from(), searchInput.to(), "updated_at");
    }

    private List<UUID> toUUIDList(List<String> uuids) {
        if (isEmpty(uuids)) {
            return Collections.emptyList();
        }
        return uuids.stream().map(UUID::fromString).toList();
    }

    private SqlBuilder unionSettledAndUnsettledTransactions(MapSqlParameterSource params, SqlBuilder builder, long settlementRunId) {
        String settledQuery = new SqlBuilder(TransactionRepository.BASE_SELECT)
            .where(builder.whereConditions)
            .where("settlement_run_id = :specificSettlementId")
            .build();
        params.addValue("specificSettlementId", settlementRunId);

        String unsettledQuery = new SqlBuilder(TransactionRepository.BASE_SELECT)
            .where(builder.whereConditions)
            .where("settlement_run_id IS NULL")
            .build();

        return new SqlBuilder(SqlBuilder.unionAll(settledQuery, unsettledQuery));
    }

    private void applyPagination(SqlBuilder builder, TransactionSearchInput searchInput, MapSqlParameterSource params) {
        if (StringUtils.isNotBlank(searchInput.after())) {
            long lastId = Long.parseLong(searchInput.after());
            params.addValue("afterCursor", lastId);
            String operator = searchInput.sortingOrder() == SortingOrder.ASC ? ">" : "<";
            builder.where("id " + operator + " :afterCursor");
        }

        if (searchInput.first() != null) {
            builder.limit(searchInput.first());
        }
    }

    private TransactionEntity map(ResultSet rs) throws SQLException {
        String transactionTypeStr = rs.getString("transaction_type");
        TransactionType transactionType = transactionTypeStr != null ?
            TransactionType.valueOf(transactionTypeStr) : null;

        Integer settlementRunId = rs.getObject("settlement_run_id", Integer.class);

        return new TransactionEntity(
            rs.getObject("id", Integer.class).longValue(),
            rs.getObject("order_id", UUID.class),
            transactionType,
            rs.getObject("portfolio_id", UUID.class),
            rs.getObject("transaction_uuid", UUID.class),
            rs.getTimestamp("created_at"),
            rs.getTimestamp("updated_at"),
            rs.getTimestamp("transaction_datetime"),
            rs.getObject("venue_execution_id", UUID.class),
            rs.getString("description"),
            rs.getBigDecimal("quantity"),
            rs.getBigDecimal("leaves_quantity"),
            rs.getBigDecimal("price"),
            rs.getString("currency"),
            rs.getObject("int_order_id", UUID.class),
            rs.getObject("ext_order_id", UUID.class),
            rs.getString("base_currency"),
            rs.getString("venue_account"),
            rs.getObject("counter_portfolio_id", UUID.class),
            rs.getObject("parent_order_id", UUID.class),
            rs.getObject("root_order_id", UUID.class),
            rs.getObject("root_execution_id", UUID.class),
            rs.getObject("execution_id", UUID.class),
            isNull(settlementRunId) ? null : settlementRunId.longValue(),
            rs.getString("client_settlement_run_id"),
            rs.getTimestamp("settlement_date")
        );
    }

    public Long countUnsettledTransactions(String venueAccountId) {
        MapSqlParameterSource params = new MapSqlParameterSource();
        String sql = "SELECT COUNT(*) FROM transactions WHERE venue_account = :venueAccount AND settlement_run_id IS NULL";
        params.addValue("venueAccount", venueAccountId);
        return namedJdbcTemplate.queryForObject(sql, params, Long.class);
    }

    public List<UUID> findTransactionsByRunId(Long runId) {
        String sql = "SELECT transaction_uuid FROM transactions WHERE settlement_run_id = ?";
        return jdbcTemplate.query(sql, ps -> ps.setLong(1, runId), (rs, rowNum) -> rs.getObject("transaction_uuid", UUID.class));
    }

    public long count() {
        String sql = "SELECT COUNT(*) FROM transactions";
        return jdbcTemplate.queryForObject(sql, Long.class);
    }

    public List<TransactionEntity> findTransactionsWithoutSequenceNumbers() {
        String sql = """
            SELECT t.id, t.order_id, t.transaction_type, t.portfolio_id, t.transaction_uuid,
                   t.created_at, t.updated_at, t.transaction_datetime, t.venue_execution_id,
                   t.description, t.quantity, t.leaves_quantity, t.price, t.currency,
                   t.int_order_id, t.ext_order_id, t.base_currency, t.venue_account,
                   t.parent_order_id, t.root_order_id, t.root_execution_id, t.execution_id,
                   t.settlement_run_id, t.settlement_date
            FROM transactions t
            LEFT JOIN booking_sequence_number bsn ON t.id = bsn.transaction_id
            WHERE bsn.transaction_id IS NULL
            """;

        return jdbcTemplate.query(sql, transactionRowMapper);
    }

    public List<TransactionEntity> findUnsettledClientSettlementRunIds() {
        String sql = """
            select * from transactions where client_settlement_run_id is not null and settlement_run_id is null
            """;
        return jdbcTemplate.query(sql, transactionRowMapper);
    }

    public String findFirstClientSettlementRunId(Long runId) {
        String sql = "SELECT client_settlement_run_id FROM transactions WHERE settlement_run_id = ? LIMIT 1";
        List<String> results = jdbcTemplate.query(
            sql,
            ps -> ps.setLong(1, runId),
            (rs, rowNum) -> rs.getString("client_settlement_run_id")
        );
        return results.isEmpty() ? null : results.get(0);
    }

    private class TransactionEntityRowMapper implements RowMapper<TransactionEntity> {
        @Override
        public TransactionEntity mapRow(ResultSet rs, int rowNum) throws SQLException {
            return map(rs);
        }
    }
}