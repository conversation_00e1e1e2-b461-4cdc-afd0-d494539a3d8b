package io.wyden.settlement.server.settlement.api;

import io.wyden.settlement.server.settlement.reconciliation.ReconciliationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/settlement/reconciliation")
public class ReconciliationController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReconciliationController.class);

    private final ReconciliationService reconciliationService;

    public ReconciliationController(ReconciliationService reconciliationService) {
        this.reconciliationService = reconciliationService;
    }

    @PostMapping
    public void runReconciliation() {
        LOGGER.info("Starting reconciliation procedure via REST API");
        reconciliationService.reconcile();
    }
}
