package io.wyden.settlement.server.settlement.transaction;

/**
 * Entity representing booking sequence numbers for settlement transactions.
 * Note: Booking service assigns sequence numbers to both transactions and reservations,
 * but settlement service only tracks sequence numbers for transactions it processes.
 */
public record BookingSequenceNumberEntity(
    Long id,
    Long sequenceNumber
) {
    public static Builder builder() {
        return new Builder();
    }

    public Builder toBuilder() {
        return new Builder()
            .id(this.id)
            .sequenceNumber(this.sequenceNumber);
    }

    public static class Builder {
        private Long id;
        private Long sequenceNumber;

        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        public Builder sequenceNumber(Long sequenceNumber) {
            this.sequenceNumber = sequenceNumber;
            return this;
        }

        public BookingSequenceNumberEntity build() {
            return new BookingSequenceNumberEntity(
                id, sequenceNumber
            );
        }
    }
}
