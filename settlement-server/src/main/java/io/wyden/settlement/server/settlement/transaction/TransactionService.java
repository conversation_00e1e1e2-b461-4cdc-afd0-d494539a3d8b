package io.wyden.settlement.server.settlement.transaction;

import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.rest.pagination.PaginationWrapper;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.settlement.SettlementOperation;
import io.wyden.published.settlement.SettlementStatus;
import io.wyden.settlement.client.transaction.SelectAllTransactionInput;
import io.wyden.settlement.client.transaction.SettlementStreetCashTrade;
import io.wyden.settlement.client.transaction.TransactionSearchInput;
import io.wyden.settlement.client.transaction.TransactionSelectResponse;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.sink.StateChangeRabbitEmitter;
import io.wyden.settlement.server.settlement.reconciliation.ReconciliationService;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.run.fsm.transitions.create.CreateSettlementRunService;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

import static com.hazelcast.sql.impl.expression.predicate.TernaryLogic.isNotNull;
import static java.util.Objects.isNull;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class TransactionService {

    private Logger LOGGER = LoggerFactory.getLogger(TransactionService.class);

    private final SettlementLegRepository legRepository;
    private final SettlementRunRepository runRepository;
    private final TransactionRepository transactionRepository;
    private final StreetCashTradeMapper streetCashTradeMapper;
    private final TransactionFeeRepository transactionFeeRepository;
    private final CreateSettlementRunService createSettlementRunService;
    private final BookingSequenceNumberRepository bookingSequenceNumberRepository;
    private final StateChangeRabbitEmitter stateChangeRabbitEmitter;

    public TransactionService(SettlementRunRepository runRepository, CreateSettlementRunService createSettlementRunService, TransactionRepository transactionRepository, TransactionFeeRepository transactionFeeRepository, StreetCashTradeMapper streetCashTradeMapper, SettlementLegRepository legRepository, BookingSequenceNumberRepository bookingSequenceNumberRepository, StateChangeRabbitEmitter stateChangeRabbitEmitter) {
        this.runRepository = runRepository;
        this.createSettlementRunService = createSettlementRunService;
        this.transactionRepository = transactionRepository;
        this.transactionFeeRepository = transactionFeeRepository;
        this.streetCashTradeMapper = streetCashTradeMapper;
        this.legRepository = legRepository;
        this.bookingSequenceNumberRepository = bookingSequenceNumberRepository;
        this.stateChangeRabbitEmitter = stateChangeRabbitEmitter;
    }

    public TransactionEntity processBookingCompleted(BookingCompleted bookingCompleted) {
        TransactionEntity transactionEntity = null;

        if (bookingSequenceNumberRepository.findBySequenceNumber(bookingCompleted.getSequenceNumber()).isPresent()) {
            LOGGER.info("BookingCompleted with sequence number: {} already processed, skipping", bookingCompleted.getSequenceNumber());
            return null;
        }

        if (List.of(OemsOrderStatus.STATUS_FILLED, OemsOrderStatus.STATUS_PARTIALLY_FILLED).contains(bookingCompleted.getOrderStatus())) {

            if (bookingCompleted.hasTransactionCreated() && (bookingCompleted.getTransactionCreated().hasStreetCashTrade() || bookingCompleted.getTransactionCreated().hasClientCashTrade())) {
                TransactionSnapshot transactionCreated = bookingCompleted.getTransactionCreated();

                final List<Fee> fees;
                if (transactionCreated.hasStreetCashTrade()) {
                    StreetCashTradeSnapshot streetCashTrade = transactionCreated.getStreetCashTrade();
                    transactionEntity = StreetCashTradeMapper.mapToTransactionEntity(streetCashTrade, null);
                    fees = streetCashTrade.getTransactionFeeList();
                } else {
                    ClientCashTradeSnapshot clientCashTrade = transactionCreated.getClientCashTrade();
                    transactionEntity = ClientCashTradeMapper.mapToTransactionEntity(clientCashTrade, null);
                    fees = clientCashTrade.getTransactionFeeList();
                }

                long transactionId = transactionRepository.save(transactionEntity);
                storeTransactionFee(transactionId, fees);

            } else {
                LOGGER.info("Skipping transaction consumption, no client/street cash trade found");
            }
        } else {
            LOGGER.info("Skipping TransactionEntity save for orderId: {}, it has status: {}", bookingCompleted.getOrderId(), bookingCompleted.getOrderStatus());
        }
        return transactionEntity;
    }

    public void selectTransactions(String runId, List<TransactionSelectResponse> changes) {

        if (isNull(runId)) {
            throw new IllegalArgumentException("Settlement run id must be provided");
        }

        long parsedRunId = Long.parseLong(runId);
        changes.forEach(change -> {
            if (!change.selected()) {
                transactionRepository.setSettlementRunAsNull(parsedRunId, UUID.fromString(change.transactionId()));
            } else {
                transactionRepository.setSettlementRunId(parsedRunId, UUID.fromString(change.transactionId()));
            }
        });

        legRepository.deleteSettlementLegsByRunId(parsedRunId);
        SettlementRunEntity runEntity = runRepository.findById(parsedRunId);
        createSettlementRunService.createSettlementLegs(runEntity, SettlementStatus.PENDING, parsedRunId);
        stateChangeRabbitEmitter.emitRunStateChange(runEntity.id(), SettlementOperation.RECALCULATED);
    }

    public long selectAllTransactions(SelectAllTransactionInput request) {
        SettlementRunEntity runEntity = runRepository.findById(Integer.parseInt(request.settlementRunId()));
        if (runEntity.status() != SettlementStatus.PENDING) {
            throw new IllegalArgumentException("Settlement run is not in PENDING status");
        }

        long runId = runEntity.id();

        // 1. Set/Unset settlement run id for desired transactions
        if (isNotNull(request.filters()) && request.filters().selected()) {
            transactionRepository.setSettlementIdForCriteria(toQuery(request), runId);
        } else {
            transactionRepository.unsetSettlementIdForCriteria(toQuery(request), runId);
        }

        // 2. Remove previously calculated settlement legs
        legRepository.deleteSettlementLegsByRunId(runId);
        // 3. Recalculate settlement legs
        createSettlementRunService.createSettlementLegs(runEntity, SettlementStatus.PENDING, runId);

        stateChangeRabbitEmitter.emitRunStateChange(runEntity.id(), SettlementOperation.RECALCULATED);
        return runId;
    }

    private TransactionSearchInput toQuery(SelectAllTransactionInput wrapper) {
        if (wrapper == null || wrapper.filters() == null) {
            return null;
        }
        SelectAllTransactionInput.SelectAllTransactionFiltersInput request = wrapper.filters();

        return TransactionSearchInput.builder()
            .currency(request.currency())
            .accountId(getVenueAccountIds(wrapper.settlementRunId(), request.accountId()))
            .portfolioId(request.portfolioId())
            .orderId(request.orderId())
            .parentOrderId(request.parentOrderId())
            .rootOrderId(request.rootOrderId())
            .underlyingExecutionId(request.underlyingExecutionId())
            .executionId(request.executionId())
            .venueExecutionId(request.venueExecutionId())
            .rootExecutionId(request.rootExecutionId())
            .settlementRunId(wrapper.settlementRunId())
            .from(request.from())
            .to(request.to())
            .build();
    }

    public PaginationModel.CursorConnection<SettlementStreetCashTrade> queryTransactions(TransactionSearchInput request) {

        List<String> venueAccountsIds = getVenueAccountIds(request.settlementRunId(), request.accountId());

        TransactionSearchInput parsedRequest = request.toBuilder().accountId(venueAccountsIds).build();
        List<SettlementStreetCashTrade> transactions = transactionRepository.searchTransactions(parsedRequest, true)
            .stream()
            .map(streetCashTradeMapper::mapToDto)
            .toList();
        int total = transactionRepository.countTransactions(parsedRequest);
        int remaining = transactionRepository.countRemaining(parsedRequest);

        return PaginationWrapper.wrapToModel(
            transactions,
            SettlementStreetCashTrade::getId,
            remaining,
            (long) total);
    }

    private List<String> getVenueAccountIds(String runId, List<String> requestVenueAccountId) {
        List<String> venueAccountsIds;
        if (isNotBlank(runId)) {
            SettlementRunEntity run = runRepository.findById(Long.parseLong(runId));
            List<String> runAccountIds = run.venueAccountIds();
            if (isEmpty(requestVenueAccountId)) {
                venueAccountsIds = runAccountIds;
            } else {
                venueAccountsIds = requestVenueAccountId;
            }
        } else {
            venueAccountsIds = requestVenueAccountId;
        }
        return venueAccountsIds;
    }

    private void storeTransactionFee(long transactionId, List<Fee> fees) {
        List<TransactionFeeEntity> transactionFees = FeeMapper.mapToTransactionFeesEntity(fees, transactionId);
        transactionFees.forEach(transactionFeeRepository::save);
    }

}