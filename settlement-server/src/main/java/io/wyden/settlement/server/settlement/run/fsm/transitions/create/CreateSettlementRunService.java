package io.wyden.settlement.server.settlement.run.fsm.transitions.create;

import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.settlement.SettlementOperation;
import io.wyden.published.settlement.SettlementStatus;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.settlement.client.run.leg.SettlementLegDirection;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.sink.StateChangeRabbitEmitter;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.run.fsm.state.RunState;
import io.wyden.settlement.server.settlement.run.fsm.state.RunStatePending;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegEntity;
import io.wyden.settlement.server.settlement.transaction.CurrencyAmount;
import io.wyden.settlement.server.settlement.transaction.TransactionFeeRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
public class CreateSettlementRunService {
    private Logger LOGGER = LoggerFactory.getLogger(CreateSettlementRunService.class);
    private final VenueAccountCacheFacade venueAccountCacheFacade;
    private final SettlementRunRepository runRepository;
    private final TransactionRepository transactionRepository;
    private final TransactionFeeRepository transactionFeeRepository;
    private final StateChangeRabbitEmitter stateChangeRabbitEmitter;

    public CreateSettlementRunService(VenueAccountCacheFacade venueAccountCacheFacade, SettlementRunRepository runRepository, TransactionRepository transactionRepository, TransactionFeeRepository transactionFeeRepository, StateChangeRabbitEmitter stateChangeRabbitEmitter) {
        this.venueAccountCacheFacade = venueAccountCacheFacade;
        this.runRepository = runRepository;
        this.transactionRepository = transactionRepository;
        this.transactionFeeRepository = transactionFeeRepository;
        this.stateChangeRabbitEmitter = stateChangeRabbitEmitter;
    }

    public RunState create(List<String> accountIds) {
        Date startAt = new java.sql.Date(System.currentTimeMillis());
        LOGGER.info("New settlement run requested for accounts: {} and startDate: {}", accountIds, startAt);
//      1. Store parent - SettlementRunEntity
        SettlementRunEntity runEntity = SettlementRunEntity.builder()
            .startAt(startAt)
            .venueAccountIds(accountIds)
            .status(SettlementStatus.PENDING)
            .createdAt(Timestamp.from(ZonedDateTime.now().toInstant()))
            .build();
        final long runId = runRepository.persist(runEntity);
//      2. Connect all processing transactions to the settlement run
        transactionRepository.setSettlementId(accountIds, runId);

//      2. For all accounts find the unsettled currency amounts and transactions
        SettlementRunEntity entity = createSettlementLegs(runEntity, SettlementStatus.PENDING, runId);
        stateChangeRabbitEmitter.emitRunStateChange(runId, SettlementOperation.CREATED);
        return new RunStatePending(entity);
    }

    public SettlementRunEntity createSettlementLegs(SettlementRunEntity runEntity, SettlementStatus status, long runId) {
        List<SettlementLegEntity> legs = getSettlementLegs(runId, status);
        SettlementRunEntity entity = runEntity.toBuilder()
            .id(runId)
            .legs(legs)
            .build();
        runRepository.persist(entity);
        LOGGER.info("New settlement run created, legs count: {}", legs.size());
        return runRepository.findById(runId);
    }

    private List<SettlementLegEntity> getSettlementLegs(long runId, SettlementStatus status) {
        List<SettlementLegEntity> legs = new ArrayList<>();

        List<CurrencyAmount> transactionCurrencyAmounts = transactionRepository.findCurrencyAmounts(runId);
        List<CurrencyAmount> feeCurrencyAmounts = transactionFeeRepository.findCurrencyAmount(runId);
        List<CurrencyAmount> currencyAmounts = calculateDifference(transactionCurrencyAmounts, feeCurrencyAmounts);

        if (!currencyAmounts.isEmpty()) {
            currencyAmounts.forEach(currencyAmount -> {

                String venueName = null;
                if (isNotBlank(currencyAmount.accountId())) {
                    Optional<VenueAccount> venueAccount = venueAccountCacheFacade.find(currencyAmount.accountId());
                    venueName = venueAccount.map(VenueAccount::getVenueName)
                        .orElse("Unknown Venue Account");
                    LOGGER.info("Found unsettled currency amount for account: {}, currency {}", currencyAmount.accountId(), currencyAmount.currency());
                }

                SettlementLegEntity legEntity = SettlementLegEntity.builder()
                    .direction(currencyAmount.amount().compareTo(BigDecimal.ZERO) >= 0 ? SettlementLegDirection.RECEIVE : SettlementLegDirection.SEND)
                    .quantity(currencyAmount.amount().abs())
                    .asset(currencyAmount.currency())
                    .auto(false) //TODO: auto not supported yet
                    .runId(runId)
                    .status(status)
                    .venue(venueName)
                    .venueAccountId(currencyAmount.accountId())
                    .build();
                legs.add(legEntity);
            });
        }
        return legs;
    }

    public List<CurrencyAmount> calculateDifference(List<CurrencyAmount> transactionAmountsPerAccount,
                                                    List<CurrencyAmount> feeAmountsPerAccount) {


        Map<String, List<CurrencyAmount>> transactionsByAccountId = mapNullAccountsToEmptyString(transactionAmountsPerAccount).stream().collect(Collectors.groupingBy(CurrencyAmount::accountId));
        Map<String, List<CurrencyAmount>> feesByAccountId = mapNullAccountsToEmptyString(feeAmountsPerAccount).stream().collect(Collectors.groupingBy(CurrencyAmount::accountId));

        List<CurrencyAmount> result = new ArrayList<>();

        for (String accountId : transactionsByAccountId.keySet()) {

            List<CurrencyAmount> transactionAmounts = transactionsByAccountId.get(accountId);
            List<CurrencyAmount> feeByAccount = feesByAccountId.getOrDefault(accountId, List.of());

            Map<String, BigDecimal> diffMap = Stream.concat(
                    transactionAmounts.stream().map(ca -> Map.entry(ca.currency(), ca.amount())),
                    feeByAccount.stream().map(ca -> Map.entry(ca.currency(), ca.amount().negate()))
                )
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    Map.Entry::getValue,
                    BigDecimal::add
                ));
            List<CurrencyAmount> combined = diffMap.entrySet().stream()
                .filter(e -> e.getValue().compareTo(BigDecimal.ZERO) != 0)
                .map(e -> new CurrencyAmount(accountId, e.getKey(), e.getValue()))
                .toList();
            result.addAll(combined);
        }

        return result;
    }

    private List<CurrencyAmount> mapNullAccountsToEmptyString(List<CurrencyAmount> amounts) {
        return amounts.stream()
            .map(amount -> new CurrencyAmount(
                Optional.ofNullable(amount.accountId()).orElse(""),
                amount.currency(),
                amount.amount()))
            .toList();

    }


}