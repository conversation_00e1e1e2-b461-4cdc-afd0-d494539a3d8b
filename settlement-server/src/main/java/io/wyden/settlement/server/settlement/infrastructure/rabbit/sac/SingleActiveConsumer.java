package io.wyden.settlement.server.settlement.infrastructure.rabbit.sac;

import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.settlement.SettlementRunScheduled;
import io.wyden.settlement.server.settlement.reconciliation.ReconciliationService;
import io.wyden.settlement.server.settlement.run.fsm.state.RunStateContext;
import io.wyden.settlement.server.settlement.settlementconfiguration.SettlementAccountConfigurationEntity;
import io.wyden.settlement.server.settlement.settlementconfiguration.SettlementConfigurationRepository;
import io.wyden.settlement.server.settlement.settlementconfiguration.schedule.ScheduleRepository;
import io.wyden.settlement.server.settlement.settlementconfiguration.schedule.SettlementRunScheduler;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;

import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.consumed;
import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
public class SingleActiveConsumer implements MessageConsumer<Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(SingleActiveConsumer.class);
    private final TransactionService transactionService;
    private final SettlementConfigurationRepository configurationRepository;
    private final ReconciliationService reconciliationService;
    private final ScheduleRepository scheduleRepository;
    private final SettlementRunScheduler settlementRunScheduler;
    private final RunStateContext runStateContext;

    public SingleActiveConsumer(TransactionService transactionService,
                                SettlementConfigurationRepository configurationRepository,
                                ReconciliationService reconciliationService,
                                ScheduleRepository scheduleRepository,
                                SettlementRunScheduler settlementRunScheduler,
                                RunStateContext runStateContext) {
        this.transactionService = transactionService;
        this.configurationRepository = configurationRepository;
        this.reconciliationService = reconciliationService;
        this.scheduleRepository = scheduleRepository;
        this.settlementRunScheduler = settlementRunScheduler;
        this.runStateContext = runStateContext;
    }

    @Override
    public ConsumptionResult consume(Message message, AMQP.BasicProperties basicProperties) {
        if (message instanceof BookingCompleted bookingCompleted) {
            return consumeBookingCompleted(bookingCompleted);
        } else if (message instanceof SettlementRunScheduled settlementRunScheduled) {
            return consumeSettlementRunScheduled(settlementRunScheduled);
        } else {
            LOGGER.warn("Unknown message type: {}", message.getClass().getSimpleName());
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    private ConsumptionResult consumeBookingCompleted(BookingCompleted bookingCompleted) {
        try {
            TransactionEntity transactionEntity = transactionService.processBookingCompleted(bookingCompleted);
            //create settlement with single transaction for client settlement run
            String clientSettlementRunId = transactionEntity.clientSettlementRunId();
            if (isNotBlank(clientSettlementRunId)) {
                reconciliationService.reconcileClientSettlement(clientSettlementRunId, List.of(transactionEntity));
            }
        } catch (DataIntegrityViolationException e) {
            LOGGER.error("Error while processing booking completed message, discarding...", e);
            return ConsumptionResult.failureNonRecoverable();
        }
        return consumed();
    }

    private ConsumptionResult consumeSettlementRunScheduled(SettlementRunScheduled runScheduled) {
        if (scheduleRepository.findById(runScheduled.getMessageId()).isEmpty()) {
            LOGGER.warn("Message id is not found for registered schedules {}. Message will be ignored and unregistered.", runScheduled.getMessageId());
            settlementRunScheduler.cancel(runScheduled.getMessageId());
        }

        List<SettlementAccountConfigurationEntity> configurations = configurationRepository.findByIdIn(new HashSet<>(runScheduled.getAccountIdsList()));
        if (configurations.isEmpty()) {
            LOGGER.warn("Any configuration cannot be found for accounts {}. Message ignored.", runScheduled.getAccountIdsList());
            return consumed();
        }

        List<SettlementAccountConfigurationEntity> notExcludedConfigurations = configurations.stream()
            .filter(this::currentDayIsNotExcludedInConfiguration)
            .toList();

        if (notExcludedConfigurations.isEmpty()) {
            LOGGER.info("Consumed message {} is ignored due to excluded days.", runScheduled);
            return consumed();
        }

        runStateContext.onCreate(notExcludedConfigurations.stream()
            .map(SettlementAccountConfigurationEntity::accountId)
            .toList());
        return consumed();
    }

    private boolean currentDayIsNotExcludedInConfiguration(SettlementAccountConfigurationEntity config) {
        if (isNull(config.configuration().daysExcluded())) {
            return true;
        }
        return config.configuration().daysExcluded().stream()
            .noneMatch(mmdd -> LocalDate.parse(LocalDate.now().getYear() + "-" + mmdd).equals(LocalDate.now()));
    }
}