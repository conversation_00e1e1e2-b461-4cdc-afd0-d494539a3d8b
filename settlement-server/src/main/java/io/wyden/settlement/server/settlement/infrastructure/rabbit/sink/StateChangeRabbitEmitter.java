package io.wyden.settlement.server.settlement.infrastructure.rabbit.sink;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.Metadata;
import io.wyden.published.settlement.LegStateChange;
import io.wyden.published.settlement.RunStateChange;
import io.wyden.published.settlement.SettlementOperation;
import io.wyden.published.settlement.SettlementStateChange;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class StateChangeRabbitEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(StateChangeRabbitEmitter.class);

    private final RabbitExchange<SettlementStateChange> stateChangeRabbitExchange;

    public StateChangeRabbitEmitter(RabbitExchange<SettlementStateChange> stateChangeRabbitExchange) {
        this.stateChangeRabbitExchange = stateChangeRabbitExchange;
    }

    public void emitRunStateChange(long runId, SettlementOperation operation, List<UUID> affectedTransactions, String clientSettlementRunId) {
        RunStateChange.Builder stateChangeBuilder = RunStateChange.newBuilder()
            .setRunId("" + runId)
            .addAllAffectedTransactions(affectedTransactions.stream().map(UUID::toString).toList())
            .setOperation(operation);

        if (isNotBlank(clientSettlementRunId)) {
            stateChangeBuilder.setAffectedClientSettlementIds(clientSettlementRunId);
        }

        SettlementStateChange message = SettlementStateChange.newBuilder()
            .setMetadata(basicMetadata())
            .setRunStateChange(stateChangeBuilder)
            .build();
        LOGGER.info("Emitting run state change {}", message);
        emit(message, operation);
    }

    public void emitRunStateChange(long runId, SettlementOperation operation) {
        SettlementStateChange message = SettlementStateChange.newBuilder()
            .setMetadata(basicMetadata())
            .setRunStateChange(RunStateChange.newBuilder()
                .setRunId("" + runId)
                .setOperation(operation)
                .build())
            .build();
        LOGGER.info("Emitting run state change {}", message);
        emit(message, operation);
    }

    public void emitLegStateChange(long legId, long runId, String venueAccountId, SettlementOperation operation) {
        SettlementStateChange message = SettlementStateChange.newBuilder()
            .setMetadata(basicMetadata())
            .setLegStateChange(LegStateChange.newBuilder()
                .setLegId(String.valueOf(legId))
                .setRunId(String.valueOf(runId))
                .setOperation(operation)
                .setAccount(venueAccountId)
                .build())
            .build();

        LOGGER.info("Emitting leg state change {}", message);
        stateChangeRabbitExchange.tryPublish(message, "");
    }

    private void emit(SettlementStateChange settlementStateChange, SettlementOperation settlementOperation) {
        Map<String, String> headers = Map.of(
            "settlementOperation", settlementOperation.name()
        );
        stateChangeRabbitExchange.publishWithHeaders(settlementStateChange, headers);
    }

    private static Metadata.@NotNull Builder basicMetadata() {
        return Metadata.newBuilder()
            .setResponseId(UUID.randomUUID().toString())
            .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()));
    }
}
