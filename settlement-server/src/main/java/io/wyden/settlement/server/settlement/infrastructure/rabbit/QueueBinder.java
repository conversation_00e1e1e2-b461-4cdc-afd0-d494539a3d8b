package io.wyden.settlement.server.settlement.infrastructure.rabbit;

import com.google.protobuf.Message;
import io.wyden.cloud.utils.spring.util.ExclusiveNameGenerator;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.queue.ExpiringRabbitQueueBuilder;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.settlement.SettlementRunScheduled;
import io.wyden.published.settlement.SettlementStateChange;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.sac.SACMessageParser;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.sac.SingleActiveConsumer;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.sink.StateChangeConsumer;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class QueueBinder {

    private static final Logger LOGGER = LoggerFactory.getLogger(QueueBinder.class);

    private final RabbitExchange<BookingCompleted> bookingCompletedExchange;
    private final RabbitExchange<SettlementRunScheduled> scheduledMessagesExchange;
    private final RabbitExchange<SettlementStateChange> stateChangeRabbitExchange;
    private final RabbitIntegrator rabbitIntegrator;

    private final String sacQueueName;
    private final String sinkQueueName;
    private final SingleActiveConsumer singleActiveConsumer;
    private final StateChangeConsumer stateChangeConsumer;
    private RabbitQueue<Message> sacQueue;
    private RabbitQueue<SettlementStateChange> sinkQueue;

    public QueueBinder(RabbitExchange<BookingCompleted> bookingCompletedExchange,
                       RabbitDestinations destinations,
                       RabbitExchange<SettlementStateChange> stateChangeRabbitExchange,
                       SingleActiveConsumer singleActiveConsumer,
                       StateChangeConsumer stateChangeConsumer,
                       RabbitIntegrator rabbitIntegrator,
                       @Value("${settlement.sac.queue}") String sacQueueName,
                       @Value("${settlement.sink.queue.pattern}") String sinkQueuePattern,
                       ExclusiveNameGenerator exclusiveNameGenerator) {
        this.bookingCompletedExchange = bookingCompletedExchange;
        this.scheduledMessagesExchange = destinations.getSettlementRunScheduledExchange();
        this.stateChangeRabbitExchange = stateChangeRabbitExchange;
        this.rabbitIntegrator = rabbitIntegrator;
        this.sacQueueName = sacQueueName;
        this.sinkQueueName = exclusiveNameGenerator.getQueueName(sinkQueuePattern);
        this.singleActiveConsumer = singleActiveConsumer;
        this.stateChangeConsumer = stateChangeConsumer;
    }

    @PostConstruct
    public void initQueues() {
        LOGGER.info("Declaring queue {} for Messages", sacQueueName);
        this.sacQueue = new RabbitQueueBuilder<>(rabbitIntegrator)
            .setQueueName(this.sacQueueName)
            .setSingleActiveConsumer(true)
            .declare();

        sacQueue.attachConsumer(SACMessageParser.parser(), singleActiveConsumer);
        bindQueue(sacQueue, scheduledMessagesExchange, Map.of());
        bindQueue(sacQueue, bookingCompletedExchange, Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), BookingCompleted.class.getSimpleName(),
            OemsHeader.SOURCE.getHeaderName(), "booking-snapshotter"
        ));

        LOGGER.info("Declaring queue {} for Messages", sinkQueueName);
        this.sinkQueue = new ExpiringRabbitQueueBuilder<SettlementStateChange>(rabbitIntegrator)
            .setQueueName(this.sinkQueueName)
            .declare();
        sinkQueue.attachConsumer(SettlementStateChange.parser(), stateChangeConsumer);
        bindQueue(sinkQueue, stateChangeRabbitExchange, Map.of());
    }

    @PreDestroy
    public void cleanup() {
        LOGGER.info("Stopping RabbitMQ consumers");
        if (sacQueue != null) {
            rabbitIntegrator.tryDeleteQueue(sacQueueName);
        }
        if (sinkQueue != null) {
            rabbitIntegrator.tryDeleteQueue(sinkQueueName);
        }
    }

    private <T1 extends Message,T2 extends Message> void bindQueue(RabbitQueue<T1> queue, RabbitExchange<T2> exchange, Map<String, Object> headers) {
        LOGGER.info("Binding exchange {} and queue {} with headers {}", exchange.getName(), queue.getName(), headers);
        queue.bindWithHeaders(exchange, MatchingCondition.ALL, headers);
    }

}
