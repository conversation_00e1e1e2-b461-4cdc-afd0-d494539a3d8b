package io.wyden.settlement.server.settlement.run.leg;

import io.wyden.published.settlement.SettlementStatus;
import io.wyden.settlement.client.run.leg.SettlementLegDirection;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;

@Repository
@Component
public class SettlementLegRepository {
    private final JdbcTemplate jdbcTemplate;

    public SettlementLegRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public SettlementLegEntity findById(long legId) {
        String sql = "SELECT * FROM settlement_leg WHERE id = ?";
        return jdbcTemplate.queryForObject(sql, new SettlementLegRowMapper(), legId);
    }

    public List<SettlementLegEntity> findLegsBySettlementRunId(long settlementRunId) {
        String sql = "SELECT * FROM settlement_leg WHERE settlement_run_id = ?";
        return jdbcTemplate.query(sql, ps -> ps.setLong(1, settlementRunId), new SettlementLegRowMapper());
    }

    public SettlementStatus findStatusByLegId(long legId) {
        String sql = "SELECT status FROM settlement_leg WHERE id = ?";
        return jdbcTemplate.query(
            sql,
            ps -> ps.setLong(1, legId),
            rs -> {
                if (rs.next()) {
                    return SettlementStatus.valueOf(rs.getString("status"));
                } else {
                    throw new SettlementLegNotFoundException(legId);
                }
            }
        );
    }

    public SettlementLegEntity persist(SettlementLegEntity leg) {
        if (leg.id() != null) {
            // UPDATE existing record
            String sql = "UPDATE settlement_leg SET settlement_run_id = ?, account_id = ?, venue = ?, " +
                         "settled_at = ?, status = ?, direction = ?, asset = ?, quantity = ?, automatic = ? " +
                         "WHERE id = ?";

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql);
                ps.setLong(1, leg.runId());
                ps.setString(2, leg.venueAccountId());
                ps.setString(3, leg.venue());
                ps.setTimestamp(4, leg.settledAt());
                ps.setString(5, leg.status().name());
                ps.setString(6, leg.direction().name());
                ps.setString(7, leg.asset());
                ps.setBigDecimal(8, leg.quantity());
                ps.setBoolean(9, leg.auto());
                ps.setLong(10, leg.id());
                return ps;
            });

            return leg;
        } else {
            // INSERT new record
            String sql = "INSERT INTO settlement_leg (settlement_run_id, account_id, venue, settled_at, status, " +
                         "direction, asset, quantity, automatic) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, new String[]{"id"});
                ps.setLong(1, leg.runId());
                ps.setString(2, leg.venueAccountId());
                ps.setString(3, leg.venue());
                ps.setTimestamp(4, leg.settledAt());
                ps.setString(5, leg.status().name());
                ps.setString(6, leg.direction().name());
                ps.setString(7, leg.asset());
                ps.setBigDecimal(8, leg.quantity());
                ps.setBoolean(9, leg.auto());
                return ps;
            }, keyHolder);

            Long generatedId = keyHolder.getKey().longValue();

            return leg.toBuilder()
                .id(generatedId)
                .build();
        }
    }

    public int setCompletedStatus(long legId) {
        String sql = "UPDATE settlement_leg SET settled_at = ? , status = 'COMPLETED' WHERE id = ? ";
        return jdbcTemplate.update(sql, ps -> {
            ps.setTimestamp(1, Timestamp.from(Instant.now()));
            ps.setLong(2, legId);
        });
    }

    public int setInProgressStatus(long legId) {
        String sql = "UPDATE settlement_leg SET status = 'IN_PROGRESS' WHERE id = ? ";
        return jdbcTemplate.update(sql, ps -> {
            ps.setLong(1, legId);
        });
    }

    public void clear() {
        jdbcTemplate.update("delete from settlement_leg");
    }

    public void cancelAllByLegId(long legId) {
        String sql = "UPDATE settlement_leg set status = 'CANCELED' WHERE id = ?";
        jdbcTemplate.update(sql, ps -> ps.setLong(1, legId));
    }

    public void deleteSettlementLegsByRunId(long runId) {
        String sql = "DELETE FROM settlement_leg WHERE settlement_run_id = ?";
        jdbcTemplate.update(sql, ps -> ps.setLong(1, runId));
    }

    private static class SettlementLegRowMapper implements RowMapper<SettlementLegEntity> {
        @Override
        public SettlementLegEntity mapRow(ResultSet rs, int rowNum) throws SQLException {
            return new SettlementLegEntity(
                rs.getLong("id"),
                rs.getLong("settlement_run_id"),
                rs.getString("account_id"),
                rs.getString("venue"),
                rs.getTimestamp("settled_at"),
                SettlementLegDirection.valueOf(rs.getString("direction")),
                rs.getString("asset"),
                rs.getBigDecimal("quantity"),
                SettlementStatus.valueOf(rs.getString("status")),
                rs.getBoolean("automatic"),
                rs.getString("transfer_id")
            );
        }
    }
}
