package io.wyden.settlement.server.settlement.transaction;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

/**
 * Repository for managing booking sequence numbers for settlement transactions.
 * Note: Booking service assigns sequence numbers to both transactions and reservations,
 * but settlement service only tracks sequence numbers for transactions it processes.
 */
@Repository
public class BookingSequenceNumberRepository {

    private final JdbcTemplate jdbcTemplate;

    public BookingSequenceNumberRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public long save(BookingSequenceNumberEntity entity) {
        String sql = """
            INSERT INTO booking_sequence_number (
                sequence_number
            ) VALUES (?)
            """;

        KeyHolder keyHolder = new GeneratedKeyHolder();

        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, new String[]{"id"});
            ps.setLong(1, entity.sequenceNumber());
            return ps;
        }, keyHolder);

        return keyHolder.getKey().longValue();
    }

    public Optional<BookingSequenceNumberEntity> findBySequenceNumber(Long seqNumber) {
        String sql = """
            SELECT id, sequence_number
            FROM booking_sequence_number
            WHERE sequence_number = ?
            """;

        List<BookingSequenceNumberEntity> results = jdbcTemplate.query(sql, new BookingSequenceNumberRowMapper(), seqNumber);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    public Long findMaxSequenceNumber() {
        String sql = "SELECT MAX(sequence_number) FROM booking_sequence_number";
        Long result = jdbcTemplate.queryForObject(sql, Long.class);
        return Optional.ofNullable(result).orElse(0L);
    }

    public void saveAll(List<BookingSequenceNumberEntity> entities) {
        if (entities.isEmpty()) {
            return;
        }

        String sql = """
            INSERT INTO booking_sequence_number (sequence_number) VALUES (?)
            """;

        jdbcTemplate.batchUpdate(sql, entities, entities.size(), (ps, entity) -> {
            ps.setLong(1, entity.sequenceNumber());
        });
    }

    public void clear() {
        String sql = "DELETE FROM booking_sequence_number";
        jdbcTemplate.update(sql);
    }

    private static class BookingSequenceNumberRowMapper implements RowMapper<BookingSequenceNumberEntity> {
        @Override
        public BookingSequenceNumberEntity mapRow(ResultSet rs, int rowNum) throws SQLException {
            return BookingSequenceNumberEntity.builder()
                .id(rs.getLong("id"))
                .sequenceNumber(rs.getLong("sequence_number"))
                .build();
        }
    }
}
