package io.wyden.settlement.server.settlement.reconciliation;

import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.settlement.SettlementOperation;
import io.wyden.published.settlement.SettlementStatus;
import io.wyden.settlement.server.settlement.booking.BookingSnapshotterHttpClient;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.sink.StateChangeRabbitEmitter;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.run.fsm.transitions.create.CreateSettlementRunService;
import io.wyden.settlement.server.settlement.transaction.BookingSequenceNumberEntity;
import io.wyden.settlement.server.settlement.transaction.BookingSequenceNumberRepository;
import io.wyden.settlement.server.settlement.transaction.ClientCashTradeMapper;
import io.wyden.settlement.server.settlement.transaction.FeeMapper;
import io.wyden.settlement.server.settlement.transaction.StreetCashTradeMapper;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionFeeEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionFeeRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.sql.Date;
import java.sql.Timestamp;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.hazelcast.sql.impl.expression.predicate.TernaryLogic.isNotNull;

@Service
//@ConditionalOnProperty(name = "reconciliation.enabled", havingValue = "true", matchIfMissing = false)
public class ReconciliationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReconciliationService.class);

    private final TransactionRepository transactionRepository;
    private final StateChangeRabbitEmitter stateChangeRabbitEmitter;
    private final CreateSettlementRunService createSettlementRunService;
    private final BookingSequenceNumberRepository bookingSequenceNumberRepository;
    private final BookingSnapshotterHttpClient bookingSnapshotterHttpClient;
    private final TransactionService transactionService;
    private final TransactionFeeRepository transactionFeeRepository;
    private final SettlementRunRepository runRepository;
    private final PlatformTransactionManager transactionManager;

    private long lastProcessedSeqNum;

    public ReconciliationService(TransactionRepository transactionRepository,
                                 StateChangeRabbitEmitter stateChangeRabbitEmitter,
                                 CreateSettlementRunService createSettlementRunService,
                                 BookingSequenceNumberRepository bookingSequenceNumberRepository,
                                 BookingSnapshotterHttpClient bookingSnapshotterHttpClient,
                                 TransactionService transactionService,
                                 TransactionFeeRepository transactionFeeRepository,
                                 SettlementRunRepository runRepository,
                                 PlatformTransactionManager transactionManager) {
        this.transactionRepository = transactionRepository;
        this.stateChangeRabbitEmitter = stateChangeRabbitEmitter;
        this.createSettlementRunService = createSettlementRunService;
        this.bookingSequenceNumberRepository = bookingSequenceNumberRepository;
        this.bookingSnapshotterHttpClient = bookingSnapshotterHttpClient;
        this.transactionService = transactionService;
        this.transactionFeeRepository = transactionFeeRepository;

        this.lastProcessedSeqNum = bookingSequenceNumberRepository.findMaxSequenceNumber();
        this.runRepository = runRepository;
        this.transactionManager = transactionManager;
    }

//    @PostConstruct
    public void reconcile() {
        LOGGER.info("Starting transactions reconciliation procedure");
        long reconciledTransactionsCount = reconcileTransactions();
        LOGGER.info("Transaction - reconciliation completed, reconciled: {} transactions", reconciledTransactionsCount);

        LOGGER.info("Starting settlement reconciliation procedure");
        AtomicInteger reconciledSettlementsCount = reconcileClientSettlements();
        LOGGER.info("Settlement reconciliation completed successfully, reconciled {} events", reconciledSettlementsCount);
    }

    private long reconcileTransactions() {
        long reconciliationCounter = 0;
        final int BATCH_SIZE = 100;
        final int SLEEP_MS = 50;

        try {
            boolean hasMoreEvents = true;

            while (hasMoreEvents) {
                CursorConnection result = bookingSnapshotterHttpClient.fetchBookingCompletedEvents(lastProcessedSeqNum, BATCH_SIZE);

                // Zbierz WSZYSTKIE eventy (transakcje + rezerwacje) do przetworzenia
                List<BookingCompleted> allEvents = new ArrayList<>();
                result.getEdgesList().forEach(edge -> {
                    BookingCompleted bookingCompletedEvent = edge.getNode().getBookingCompleted();
                    allEvents.add(bookingCompletedEvent);
                    lastProcessedSeqNum = bookingCompletedEvent.getSequenceNumber();
                });

                // Przetwórz wszystkie eventy w jednej transakcji
                if (!allEvents.isEmpty()) {
                    reconcileTransactionsBatch(allEvents);
                }

                hasMoreEvents = result.getPageInfo().getHasNextPage();
                reconciliationCounter += allEvents.size();

                LOGGER.info("Processed batch of {} events, total: {}", allEvents.size(), reconciliationCounter);

                // Opóźnienie między batchami aby zmniejszyć obciążenie CPU
                if (hasMoreEvents) {
                    try {
                        Thread.sleep(SLEEP_MS);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Reconciliation interrupted", e);
                    }
                }
            }

            LOGGER.info("Startup recovery completed successfully, processed {} events", reconciliationCounter);
            return reconciliationCounter;
        } catch (Exception e) {
            LOGGER.error("Startup recovery failed, processed {} events", reconciliationCounter, e);
            throw new RuntimeException("Failed to recover missing events during startup", e);
        }
    }

    private void reconcileTransactionsBatch(List<BookingCompleted> events) {
        TransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);

        try {
            List<BookingSequenceNumberEntity> seqNumbersToSave = new ArrayList<>();
            List<TransactionWithFees> transactionsWithFeesToSave = new ArrayList<>();

            for (BookingCompleted bookingCompletedEvent : events) {
                // Przetwórz transakcje tylko jeśli event ma transakcję
                if (bookingCompletedEvent.hasTransactionCreated()) {
                    UUID transactionUUID = extractTransactionUUID(bookingCompletedEvent);

                    if (isNotNull(transactionUUID)) {
                        Optional<TransactionEntity> entity = transactionRepository.findByUUID(transactionUUID);
                        if (entity.isEmpty()) {
                            LOGGER.debug("Missing transaction: {} adding transaction table row", transactionUUID);
                            // Zbierz transakcje z fees do batch save
                            TransactionWithFees newTransactionWithFees = createTransactionFromEvent(bookingCompletedEvent);
                            if (newTransactionWithFees != null) {
                                transactionsWithFeesToSave.add(newTransactionWithFees);
                            }
                        }
                    }
                }

                // ZAWSZE zapisz sequence number (dla transakcji i rezerwacji)
                seqNumbersToSave.add(BookingSequenceNumberEntity.builder()
                    .sequenceNumber(bookingCompletedEvent.getSequenceNumber())
                    .build());
            }

            // Prawdziwy batch save transactions i fees!
            if (!transactionsWithFeesToSave.isEmpty()) {
                // Batch insert transakcji z RETURNING ids
                List<TransactionEntity> transactions = transactionsWithFeesToSave.stream()
                    .map(TransactionWithFees::transaction)
                    .toList();
                List<Long> transactionIds = transactionRepository.saveAllAndReturnIds(transactions);

                // Batch insert wszystkich fees
                List<TransactionFeeEntity> allFeeEntities = new ArrayList<>();
                for (int i = 0; i < transactionsWithFeesToSave.size(); i++) {
                    TransactionWithFees transactionWithFees = transactionsWithFeesToSave.get(i);
                    Long transactionId = transactionIds.get(i);

                    if (!transactionWithFees.fees().isEmpty()) {
                        List<TransactionFeeEntity> feeEntities = FeeMapper.mapToTransactionFeesEntity(transactionWithFees.fees(), transactionId);
                        allFeeEntities.addAll(feeEntities);
                    }
                }

                // Jeden batch insert dla wszystkich fees
                if (!allFeeEntities.isEmpty()) {
                    transactionFeeRepository.saveAll(allFeeEntities);
                }

                LOGGER.debug("Batch saved {} transactions with {} fees", transactionsWithFeesToSave.size(), allFeeEntities.size());
            }

            // Batch save sequence numbers
            if (!seqNumbersToSave.isEmpty()) {
                bookingSequenceNumberRepository.saveAll(seqNumbersToSave);
            }

            transactionManager.commit(status);
        } catch (Exception e) {
            LOGGER.error("Failed to process booking completed events batch, rollbacking transaction", e);
            transactionManager.rollback(status);
            throw e;
        }
    }

    private void storeBookingSeqNumber(BookingCompleted bookingCompletedEvent) {
        bookingSequenceNumberRepository.save(BookingSequenceNumberEntity.builder()
            .sequenceNumber(bookingCompletedEvent.getSequenceNumber())
            .build());
    }

    private AtomicInteger reconcileClientSettlements() {
        AtomicInteger reconciledCount = new AtomicInteger();
        List<TransactionEntity> transactions = transactionRepository.findUnsettledClientSettlementRunIds();
        transactions.stream()
            .collect(Collectors.groupingBy(TransactionEntity::clientSettlementRunId))
            .forEach((clientSettlementRunId, transactionList) -> {
                reconcileClientSettlement(clientSettlementRunId, transactionList);
                reconciledCount.getAndIncrement();
            });
        return reconciledCount;
    }

    private UUID extractTransactionUUID(BookingCompleted bookingCompletedEvent) {
        if (!bookingCompletedEvent.hasTransactionCreated()) {
            return null;
        }

        TransactionSnapshot transactionSnapshot = bookingCompletedEvent.getTransactionCreated();
        if (transactionSnapshot.hasStreetCashTrade()) {
            StreetCashTradeSnapshot streetCashTrade = transactionSnapshot.getStreetCashTrade();
            return UUID.fromString(streetCashTrade.getUuid());
        } else if (transactionSnapshot.hasClientCashTrade()) {
            ClientCashTradeSnapshot clientCashTrade = transactionSnapshot.getClientCashTrade();
            return UUID.fromString(clientCashTrade.getUuid());
        }
        return null;
    }

    private TransactionWithFees createTransactionFromEvent(BookingCompleted bookingCompletedEvent) {
        try {
            // Sprawdź czy już nie został przetworzony
            if (bookingSequenceNumberRepository.findBySequenceNumber(bookingCompletedEvent.getSequenceNumber()).isPresent()) {
                LOGGER.debug("BookingCompleted with sequence number: {} already processed, skipping transaction creation",
                    bookingCompletedEvent.getSequenceNumber());
                return null;
            }

            if (!List.of(OemsOrderStatus.STATUS_FILLED, OemsOrderStatus.STATUS_PARTIALLY_FILLED).contains(bookingCompletedEvent.getOrderStatus())) {
                LOGGER.debug("Skipping transaction creation for orderId: {}, status: {}",
                    bookingCompletedEvent.getOrderId(), bookingCompletedEvent.getOrderStatus());
                return null;
            }

            if (!bookingCompletedEvent.hasTransactionCreated()) {
                LOGGER.debug("No transaction created in booking completed event");
                return null;
            }

            TransactionSnapshot transactionCreated = bookingCompletedEvent.getTransactionCreated();
            TransactionEntity transactionEntity;
            List<Fee> fees;

            if (transactionCreated.hasStreetCashTrade()) {
                StreetCashTradeSnapshot streetCashTrade = transactionCreated.getStreetCashTrade();
                transactionEntity = StreetCashTradeMapper.mapToTransactionEntity(streetCashTrade, null);
                fees = streetCashTrade.getTransactionFeeList();
            } else if (transactionCreated.hasClientCashTrade()) {
                ClientCashTradeSnapshot clientCashTrade = transactionCreated.getClientCashTrade();
                transactionEntity = ClientCashTradeMapper.mapToTransactionEntity(clientCashTrade, null);
                fees = clientCashTrade.getTransactionFeeList();
            } else {
                LOGGER.debug("No client/street cash trade found in transaction");
                return null;
            }

            return new TransactionWithFees(transactionEntity, fees);
        } catch (Exception e) {
            LOGGER.error("Failed to create transaction from booking completed event: {}", bookingCompletedEvent, e);
            return null;
        }
    }

    private record TransactionWithFees(TransactionEntity transaction, List<Fee> fees) {}

    public void reconcileClientSettlement(String clientSettlementRunId, List<TransactionEntity> transactionList) {
        List<String> venueAccounts = transactionList.stream()
            .map(TransactionEntity::venueAccount)
            .filter(Objects::nonNull)
            .toList();
        Date startAt = new Date(System.currentTimeMillis());

        SettlementRunEntity runEntity = SettlementRunEntity.builder()
            .startAt(startAt)
            .venueAccountIds(venueAccounts)
            .status(SettlementStatus.COMPLETED)
            .createdAt(Timestamp.from(ZonedDateTime.now().toInstant()))
            .build();
        long runId = runRepository.persist(runEntity);

        List<UUID> transactionUUIDs = transactionList.stream()
            .map(TransactionEntity::transactionUuid).toList();
        transactionRepository.markClientTransactionWithSettlementRunId(runId, transactionUUIDs);

        SettlementRunEntity newSettlement = createSettlementRunService.createSettlementLegs(runEntity, SettlementStatus.COMPLETED, runId);
        LOGGER.info("Recreated new settlement run based on client settlement run id: {}, created legs count: {}", clientSettlementRunId, newSettlement.legs().size());
        stateChangeRabbitEmitter.emitRunStateChange(runId, SettlementOperation.MOVED_TO_COMPLETED, transactionUUIDs, clientSettlementRunId);
    }
}
