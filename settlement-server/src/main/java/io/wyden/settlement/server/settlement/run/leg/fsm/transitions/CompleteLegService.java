package io.wyden.settlement.server.settlement.run.leg.fsm.transitions;

import io.wyden.published.settlement.SettlementOperation;
import io.wyden.published.settlement.SettlementStatus;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.sink.StateChangeRabbitEmitter;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.run.StatusUtils;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegEntity;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegNotFoundException;
import io.wyden.settlement.server.settlement.run.leg.fsm.state.LegState;
import io.wyden.settlement.server.settlement.run.leg.fsm.state.LegStateCompleted;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.ZonedDateTime;
import java.util.List;

@Component
public class CompleteLegService {

    private final Logger LOGGER = LoggerFactory.getLogger(CompleteLegService.class);

    private final SettlementRunRepository runRepository;
    private final TransactionRepository transactionRepository;
    private final StateChangeRabbitEmitter stateChangeRabbitEmitter;

    public CompleteLegService(StateChangeRabbitEmitter stateChangeRabbitEmitter, SettlementRunRepository runRepository, TransactionRepository transactionRepository) {
        this.stateChangeRabbitEmitter = stateChangeRabbitEmitter;
        this.runRepository = runRepository;
        this.transactionRepository = transactionRepository;
    }

    public LegState complete(SettlementRunEntity run, Long legId) {
        SettlementLegEntity updatedLeg = run.legs().stream()
            .filter(leg -> legId.equals(leg.id()))
            .map(leg -> leg.toBuilder()
                .status(SettlementStatus.COMPLETED)
                .settledAt(Timestamp.from(ZonedDateTime.now().toInstant()))
                .build())
            .findFirst().orElseThrow(() -> new SettlementLegNotFoundException(legId));
        List<SettlementLegEntity> updatedLegs = run.legs().stream()
            .map(leg -> leg.id() != null && leg.id().equals(updatedLeg.id()) ? updatedLeg : leg)
            .toList();

        boolean runCompleted = updatedLegs.stream()
            .allMatch(leg -> StatusUtils.isFinal(leg.status()));

        SettlementRunEntity updatedRun;
        if (runCompleted) {
            // set a settlement date for all transactions
            transactionRepository.setSettlementDate(run.id());
            updatedRun = run.toBuilder()
                .legs(updatedLegs)
                .status(SettlementStatus.COMPLETED)
                .build();
            runRepository.persist(updatedRun);
            stateChangeRabbitEmitter.emitRunStateChange(run.id(), SettlementOperation.MOVED_TO_COMPLETED,
                transactionRepository.findTransactionsByRunId(run.id()),
                transactionRepository.findFirstClientSettlementRunId(run.id()));
            LOGGER.info("Settlement Run completed :{}", updatedRun.id());
        } else {
            updatedRun = run.toBuilder()
                .legs(updatedLegs)
                .build();
            stateChangeRabbitEmitter.emitLegStateChange(
                updatedLeg.id(),
                run.id(),
                updatedLeg.venueAccountId(),
                SettlementOperation.MOVED_TO_COMPLETED);
            runRepository.persist(updatedRun);
        }

        LOGGER.info("Leg {} completed, switched state to completed", legId);
        return new LegStateCompleted(updatedRun, updatedLeg.id());
    }


}
