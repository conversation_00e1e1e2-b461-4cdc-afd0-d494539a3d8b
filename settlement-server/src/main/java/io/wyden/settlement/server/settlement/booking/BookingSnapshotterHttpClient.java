package io.wyden.settlement.server.settlement.booking;

import io.wyden.published.common.CursorConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

@Service
public class BookingSnapshotterHttpClient {

    private final Logger LOGGER = LoggerFactory.getLogger(BookingSnapshotterHttpClient.class);
    private final RestTemplate restTemplate;
    private final String snapshotterBaseUrl;

    public BookingSnapshotterHttpClient(RestTemplate restTemplate,
                                        @Value("${booking.snapshotter.host}") String snapshotterBaseUrl) {
        this.restTemplate = restTemplate;
        this.snapshotterBaseUrl = snapshotterBaseUrl;
    }

    public CursorConnection fetchBookingCompletedEvents(Long sequenceNumber, int first) {

        LOGGER.info("Fetching booking completed event from booking snapshotter, after: {}, first: {}", sequenceNumber, first);

        URI uri = UriComponentsBuilder.fromHttpUrl(snapshotterBaseUrl)
            .path("/booking-completed/search")
            .queryParam("after", sequenceNumber)
            .queryParam("first", first)
            .build()
            .toUri();

        return restTemplate.getForObject(uri, CursorConnection.class);
    }
}
