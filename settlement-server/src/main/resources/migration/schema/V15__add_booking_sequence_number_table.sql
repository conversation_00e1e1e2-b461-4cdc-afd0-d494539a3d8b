-- Create table for storing booking sequence numbers for settlement transactions
-- Note: Sequence numbers in booking service cover both transactions and reservations,
-- but in settlement service we only track sequence numbers for transactions we process
CREATE TABLE booking_sequence_number
(
    id              BIGSERIAL PRIMARY KEY,
    sequence_number BIGINT NOT NULL

);

-- Create index for efficient sequence number lookups
CREATE INDEX idx_booking_sequence_number_sequence_number
    ON booking_sequence_number (sequence_number);

