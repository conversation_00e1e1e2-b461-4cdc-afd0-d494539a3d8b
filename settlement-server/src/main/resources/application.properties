spring.application.name = settlement
server.port = 8067
spring.main.web-application-type=reactive

schema.graphql.version=1.1.14
settlements.system.currency=USD

booking.snapshotter.host=http://localhost:8043
rabbitmq.username = settlement-server
rabbitmq.password = password
rabbitmq.virtualHost = /
rabbitmq.host = localhost
# default RabbitMQ port for non-TLS connections: 5672, default port for TLS connections: 5671
rabbitmq.port = 5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls =
settlement.sac.queue=settlement-queue.ALL
settlement.sink.queue.pattern=settlement-queue.sink.%s

spring.graphql.websocket.path=/graphql/ws

spring.graphql.schema.printer.enabled=true
spring.graphql.graphiql.enabled=true
spring.graphql.graphiql.path=/graphiql
spring.graphql.schema.locations=classpath:graphql/${schema.graphql.version}
spring.graphql.schema.file-extensions=.graphql

# comma-separated list of hz member hosts
hz.addressList = localhost
hz.outboundPortDefinition =
db.engine=psql

spring.datasource.hikari.maximum-pool-size=5
spring.datasource.url=*******************************************
spring.datasource.username=settlement
spring.datasource.password=password
spring.jpa.generate-ddl=true


spring.flyway.url=${spring.datasource.url}
spring.flyway.user=${spring.datasource.username}
spring.flyway.password=${spring.datasource.password}
spring.flyway.enabled=true
spring.flyway.locations=classpath:migration/schema,classpath:migration/data
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
#logging.level.org.springframework.web=DEBUG
#logging.level.org.springframework.web.reactive=DEBUG
