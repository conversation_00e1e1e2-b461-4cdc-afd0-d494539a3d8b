package io.wyden.settlement.server.settlement.run.cancel;

import io.wyden.published.booking.TransactionType;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.sink.StateChangeRabbitEmitter;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.run.SettlementRunSinkWrapper;
import io.wyden.settlement.server.settlement.run.fsm.state.RunStateContext;
import io.wyden.settlement.server.settlement.run.fsm.transitions.cancel.CancelSettlementRunException;
import io.wyden.settlement.server.settlement.run.fsm.transitions.cancel.CancelSettlementRunService;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegEntity;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegRepository;
import io.wyden.settlement.server.settlement.run.leg.fsm.state.LegStateContext;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static io.wyden.published.settlement.SettlementStatus.CANCELED;
import static io.wyden.published.settlement.SettlementStatus.IN_PROGRESS;
import static io.wyden.published.settlement.SettlementStatus.PENDING;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CancelSettlementRunTest {

    @Mock
    private TransactionRepository transactionRepository;

    @Mock
    private SettlementRunRepository runRepository;

    @Mock
    private SettlementLegRepository legRepository;

    @Mock
    private VenueAccountCacheFacade venueAccountCacheFacade;

    @Mock
    private StateChangeRabbitEmitter emitter;
    @InjectMocks
    private LegStateContext legStateContext;
    private RunStateContext runStateContext;

    @BeforeEach
    void init() {
        runStateContext = new RunStateContext(new CancelSettlementRunService(runRepository, transactionRepository, emitter), null, null, runRepository);
    }

    @Test
    void requestRunCancel_whenAllLegsArePending_shouldCancel() {
        // Given
        long runId = 1;
        SettlementLegEntity leg = SettlementLegEntity.builder().id(1L).runId(1L).status(PENDING).build();
        SettlementRunEntity run = SettlementRunEntity.builder().id(1L).status(PENDING).legs(List.of(leg)).build();
        when(runRepository.findById(runId)).thenReturn(run);

        // When
        runStateContext.onCancel(runId);

        // Then
        ArgumentCaptor<SettlementRunEntity> captor = ArgumentCaptor.forClass(SettlementRunEntity.class);
        verify(transactionRepository).setSettlementRunAsNull(runId);
        verify(runRepository).persist(captor.capture());

        // And
        assertThat(captor.getValue().legs()).allMatch(l -> l.status() == CANCELED);
        assertThat(captor.getValue().status()).isEqualTo(CANCELED);
    }

    @Test
    void requestRunCancel_whenThereIsOneLegInNotPending_shouldThrow() {
        // Given
        long runId = 1;
        SettlementRunEntity run = SettlementRunEntity.builder()
            .id(runId)
            .legs(List.of(SettlementLegEntity.builder().id(1L).status(IN_PROGRESS).build()))
            .status(PENDING).build();
        when(runRepository.findById(runId)).thenReturn(run);

        // When
        assertThatThrownBy(() -> runStateContext.onCancel(runId))
            .isInstanceOf(CancelSettlementRunException.class);

        // Then
        verify(transactionRepository, times(0)).setSettlementRunAsNull(runId);
        verify(legRepository, times(0)).cancelAllByLegId(anyLong());
    }

    @Test
    void requestRunCancel_whenThereIsOneTransactionIsSettled_shouldThrow() {
        // Given
        long runId = 1;
        TransactionEntity t1 = emptyTransactionWithSettledDate(Timestamp.from(Instant.now()));
        TransactionEntity t2 = emptyTransactionWithSettledDate(null);
        SettlementRunEntity run = SettlementRunEntity.builder().status(PENDING).build();
        when(runRepository.findById(runId)).thenReturn(run);
        when(transactionRepository.searchTransactions(any(), anyBoolean())).thenReturn(List.of(t1, t2));

        // When
        assertThatThrownBy(() -> runStateContext.onCancel(runId))
            .isInstanceOf(CancelSettlementRunException.class);

        // Then
        verify(transactionRepository, times(0)).setSettlementRunAsNull(runId);
        verify(legRepository, times(0)).cancelAllByLegId(anyLong());
    }

    private static @NotNull TransactionEntity emptyTransactionWithSettledDate(Timestamp timestamp) {
        return new TransactionEntity(
            null,
            UUID.randomUUID(),
            TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE,
            UUID.randomUUID(),
            UUID.randomUUID(),
            Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()),
            Timestamp.from(Instant.now()),
            UUID.randomUUID(),
            "Test Insert",
            BigDecimal.valueOf(50),
            BigDecimal.valueOf(10),
            BigDecimal.valueOf(500),
            "EUR",
            UUID.randomUUID(),
            UUID.randomUUID(),
            "BTC",
            "VenueABC",
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            1L,
            "1",
            timestamp
        );
    }
}