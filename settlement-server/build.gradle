plugins {
    id 'java'
    id 'jaco<PERSON>'
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.protobuf
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
    buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
}

version = "1.4.0$buildVersion"

protobuf {
    protoc {
        // The artifact spec for the Protobuf Compiler
        artifact = dependencyCatalog.protobuf.protoc.get()
    }
}

repositories {
    mavenLocal()
    maven {
        name 'nexus-snapshots'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    def nexusNpm = ivy {
        //	example full url 'https://repo.wyden.io/nexus/repository/npm-snapshots/@algotrader/schema-graphql/-/schema-graphql-1.0.0.tgz'
        url 'https://repo.wyden.io/nexus/repository/npm-snapshots/'
        patternLayout {
            artifact '/[organization]/[module]/-/[artifact]-[revision].[ext]'
        }
        // This is required in Gradle 6.0+ as metadata file (ivy.xml) is mandatory.
        // https://docs.gradle.org/6.2/userguide/declaring_repositories.html#sec:supported_metadata_sources
        metadataSources { artifact() }
        credentials {
            username repository_username
            password repository_password
        }
    }
    exclusiveContent {
        forRepositories(nexusNpm)
        filter { includeGroup("@algotrader") }
    }
    mavenCentral()
    maven { url 'https://repo.spring.io/milestone' }
}

dependencyManagement {
    resolutionStrategy {
        cacheChangingModulesFor 0, 'seconds'
    }
}

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

def props = new Properties()
file('src/main/resources/application.properties').withInputStream { props.load(it) }
def schemaGraphqlVersion = props.getProperty('schema.graphql.version')

dependencies {
    compileOnly "@algotrader:schema-graphql:${schemaGraphqlVersion}@tgz"

    implementation project(':settlement-client')

    implementation dependencyCatalog.access.gateway.client
    implementation dependencyCatalog.access.gateway.domain

    implementation dependencyCatalog.spring.boot.starter.oauth2.resource.server
    implementation dependencyCatalog.spring.boot.starter.webflux

    implementation dependencyCatalog.reference.data.client
    implementation dependencyCatalog.rate.service.client
    implementation dependencyCatalog.access.gateway.client
    implementation dependencyCatalog.access.gateway.domain
    implementation dependencyCatalog.reactor.core.micrometer

    implementation dependencyCatalog.spring.boot.starter.graphql
    implementation dependencyCatalog.spring.boot.starter.data.jpa
    implementation dependencyCatalog.flyway.core
    implementation dependencyCatalog.postgresql
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.cloud.utils.rabbitmq
    implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
    implementation dependencyCatalog.cloud.utils.rest
    implementation dependencyCatalog.cloud.utils.tools
    implementation dependencyCatalog.cloud.utils.spring

    implementation dependencyCatalog.spring.boot.starter.actuator
//    implementation dependencyCatalog.spring.boot.starter.web

    implementation dependencyCatalog.protobuf.java
    implementation dependencyCatalog.hazelcast

    testImplementation dependencyCatalog.junit.jupiter.api
    testImplementation dependencyCatalog.assertj.core
    testImplementation dependencyCatalog.spring.boot.starter.test
    testRuntimeOnly dependencyCatalog.junit.jupiter.engine
}

task addSchemaToResources(type: Copy) {
    def tarPath = project.configurations.compileClasspath.find { it.name.startsWith("schema-graphql") }
    println tarPath
    def outputDir = "${sourceSets.main.resources.srcDirs[0]}/graphql/${schemaGraphqlVersion}"
    println outputDir
    def outputDirAsFile = new File("${outputDir}")

    doFirst {
        if (outputDirAsFile.exists()) {
            println "Removing old .graphqls files:  ${outputDir}"

            outputDirAsFile.eachFile { file ->
                if (file.isDirectory()) {
                    // If it's a directory, delete it recursively
                    file.deleteDir()
                } else if (file.name.endsWith('.graphqls')) {
                    // If it's a file with .graphqls extension, delete it
                    println "Deleting file: ${file}"
                    file.delete()
                }
            }
        }
    }

    //extract schemas from artefact
    def tarFile = file(tarPath)
    into "${outputDir}"
    //remove package directory
    eachFile { file ->
        if (file.path.startsWith("package/")) {
            file.path = file.path.replaceFirst("^package/", "")
        }
    }
    include '**/*.graphql'
    from tarTree(tarFile)
    include '**/*.graphql'
}

processResources {
    dependsOn addSchemaToResources
}

sonarqube {
    properties {
        property "sonar.projectKey", "settlement-server"
        property "sonar.projectName", "Settlement Server"
    }
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {

            dependencies {
                implementation project()
                implementation project(':settlement-client')
                // TODO SPL move cucumber to dependency-catalog once versions are settled
                implementation 'io.cucumber:cucumber-java:7.21.1'
                implementation 'io.cucumber:cucumber-junit:7.21.1'
                implementation 'io.cucumber:cucumber-spring:7.21.1'
                implementation 'io.cucumber:cucumber-junit-platform-engine:7.21.1'
                implementation 'org.junit.platform:junit-platform-suite:1.10.0'

                // add dependency to test to use the same factory methods
                implementation sourceSets.test.output
                implementation dependencyCatalog.postgresql
                implementation dependencyCatalog.reactor.test
                implementation dependencyCatalog.spring.graphql.test
                implementation dependencyCatalog.reference.data.client
                implementation dependencyCatalog.reference.data.domain
                implementation dependencyCatalog.cloud.utils.rabbitmq
                implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
                implementation dependencyCatalog.cloud.utils.hazelcast
                implementation dependencyCatalog.hamcrest.library

                implementation dependencyCatalog.access.gateway.client
                implementation dependencyCatalog.access.gateway.domain
                implementation dependencyCatalog.rate.service.client
                implementation dependencyCatalog.published.language.oems

                implementation dependencyCatalog.spring.security.test
                implementation dependencyCatalog.spring.boot.starter.oauth2.resource.server

                implementation dependencyCatalog.spring.boot.starter.graphql
                implementation dependencyCatalog.spring.boot.starter.webflux
                implementation dependencyCatalog.spring.boot.starter.data.jpa
                implementation dependencyCatalog.published.language.oems
                implementation dependencyCatalog.cloud.utils.rabbitmq
                implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
                implementation dependencyCatalog.cloud.utils.telemetry
                implementation dependencyCatalog.cloud.utils.test
                implementation dependencyCatalog.cloud.utils.rest
                implementation dependencyCatalog.spring.boot.starter.test
                implementation dependencyCatalog.spring.boot.starter.web
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.testcontainers.rabbitmq
                implementation dependencyCatalog.hazelcast
                implementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }
                implementation dependencyCatalog.testcontainers.postgresql
            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${version}"
        )
    }
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9086"]
    environment([
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev"
    ])
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}


test {
    finalizedBy jacocoTestReport
}

jacocoTestReport {
    reports {
        xml.required = true
        csv.required = true
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

tasks.withType(Test) {
    testLogging {
        info {
            events TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED
        }
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_OUT,
                    TestLogEvent.STANDARD_ERROR
            exceptionFormat TestExceptionFormat.FULL
            showExceptions true
            showCauses true
            showStackTraces true
            showStandardStreams true
        }

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}
