package io.wyden.rate.client;

import io.wyden.cloud.utils.test.TracingMock;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.rate.Rate;
import io.wyden.published.rate.RateKey;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Map;

public class MockRatesCacheFacade extends RatesCacheFacade {

    public static final String RATE_TIMESTAMP = DateUtils.toIsoUtcTime(ZonedDateTime.of(2024, 1, 1, 1, 1, 1, 1, ZoneId.of("CET")));
    static final String BTC_TIMESTAMP = DateUtils.toIsoUtcTime(ZonedDateTime.of(2024, 1, 25, 1, 1, 1, 1, ZoneId.of("CET")));
    static final String TRY_TIMESTAMP = DateUtils.toIsoUtcTime(ZonedDateTime.of(2024, 1, 23, 1, 1, 1, 1, ZoneId.of("CET")));
    private static final Map<RateKey, Rate> RATE_MAP = Map.of(
        buildRateKey("LTC", "BNB"),
        buildRate("LTC", "BNB", "0.02"),
        buildRateKey("BTC", "USD"),
        buildRate("BTC", "USD", "90000", BTC_TIMESTAMP),
        buildRateKey("TRY", "USD"),
        buildRate("TRY", "USD", "0.0027", TRY_TIMESTAMP)
    );

    public MockRatesCacheFacade() {
        super(RATE_MAP, TracingMock.createMock());
    }

    private static Rate buildRate(String baseCurrency, String quoteCurrency, String value, String timestamp) {
        return Rate.newBuilder()
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .setValue(value)
            .setTimestamp(timestamp)
            .build();
    }

    private static Rate buildRate(String baseCurrency, String quoteCurrency, String value) {
        return Rate.newBuilder()
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .setValue(value)
            .setTimestamp(RATE_TIMESTAMP)
            .build();
    }

    private static RateKey buildRateKey(String baseCurrency, String quoteCurrency) {
        return RateKey.newBuilder()
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .build();
    }

}
