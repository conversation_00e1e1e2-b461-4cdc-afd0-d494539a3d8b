package io.wyden.brokerconfig.interfaces.rest;

import io.wyden.brokerconfig.application.PortfolioLookupService;
import io.wyden.brokerconfig.application.PortfolioStorageService;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.brokerdesk.HedgingConfig;
import io.wyden.published.brokerdesk.InstrumentConfig;
import io.wyden.published.brokerdesk.PortfolioConfig;
import io.wyden.published.brokerdesk.PortfolioConfigFlatList;
import io.wyden.published.brokerdesk.PortfolioConfigSnapshot;
import io.wyden.published.brokerdesk.PricingConfig;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
public class PortfolioConfigurationController {

    private static final Logger LOGGER = getLogger(PortfolioConfigurationController.class);

    private final PortfolioLookupService portfolioLookupService;
    private final PortfolioStorageService portfolioStorageService;

    public PortfolioConfigurationController(PortfolioLookupService portfolioLookupService,
                                            PortfolioStorageService portfolioStorageService) {
        this.portfolioLookupService = portfolioLookupService;
        this.portfolioStorageService = portfolioStorageService;
    }

    @GetMapping("/portfolio-configurations")
    public PortfolioConfigFlatList getPortfolioConfigList() {
        LOGGER.info("Getting portfolio configuration list");
        return portfolioLookupService.findAll();
    }

    @GetMapping("/portfolio-configurations/{portfolioId}")
    public PortfolioConfigSnapshot getPortfolioConfig(@PathVariable("portfolioId") String portfolioId) {
        LOGGER.info("Getting portfolio configuration snapshot for: {}", portfolioId);
        return portfolioLookupService.getPortfolioSnapshot(portfolioId)
            .orElse(null);
    }

    @PostMapping("/portfolio-configurations/{portfolioId}")
    public String savePortfolioConfig(@PathVariable String portfolioId, @RequestBody PortfolioConfig portfolio) {
        LOGGER.info("Saving new portfolio configuration: \n{}", portfolio);
        portfolioStorageService.save(portfolioId, portfolio);
        return portfolioId;
    }

    @PostMapping("/portfolio-configurations/{portfolioId}/execution")
    public String savePortfolioExecutionConfig(@PathVariable String portfolioId, @RequestBody ExecutionConfig executionConfig) {
        LOGGER.info("Saving new execution configuration: \n{}", executionConfig);
        return portfolioStorageService.save(portfolioId, executionConfig);
    }

    @PostMapping("/portfolio-configurations/{portfolioId}/pricing")
    public String savePortfolioPricingConfig(@PathVariable String portfolioId, @RequestBody PricingConfig pricingConfig) {
        LOGGER.info("Saving new pricing configuration: \n{}", pricingConfig);
        return portfolioStorageService.save(portfolioId, pricingConfig);
    }

    @PostMapping("/portfolio-configurations/{portfolioId}/hedging")
    public String savePortfolioHedgingConfig(@PathVariable String portfolioId, @RequestBody HedgingConfig hedgingConfig) {
        LOGGER.info("Saving new hedging configuration: \n{}", hedgingConfig);
        return portfolioStorageService.save(portfolioId, hedgingConfig);
    }

    @PostMapping("/portfolio-configurations/{portfolioId}/instrument")
    public String savePortfolioInstrumentConfig(@PathVariable String portfolioId, @RequestBody InstrumentConfig instrumentConfig) {
        LOGGER.info("Saving new portfolio instrument configuration: \n{}", instrumentConfig);
        return portfolioStorageService.save(portfolioId, instrumentConfig);
    }

    @DeleteMapping("/portfolio-configurations/{portfolioId}")
    public void deletePortfolioConfig(@PathVariable String portfolioId) {
        LOGGER.info("Removing portfolio config: {}", portfolioId);
        portfolioStorageService.delete(portfolioId);
    }
}
