package io.wyden.brokerconfig.domain;

import java.math.BigDecimal;

/**
 * An Execution Configuration is a set of properties that define how a client-side order will be executed.
 *
 * @param tradingMode          The trading mode: Agency or Principal
 * @param counterPortfolio     The portfolio against which the client-side orders will be booked.
 * @param fixedFee             A fixed amount charged per executed order.
 * @param percentageFee        A percentage charged on the amount of an executed order.
 * @param minFee               The minimum amount that will be charged if the percentage fee falls below this value.
 * @param feeCurrency          The currency of the fees. All fees will be charged in the same currency.
 * @param agencyTradingAccount (agency only) The Account for the street-side order in agency mode.
 * @param chargeExchangeFee    (agency only) If “Yes” the fee charged by the venue will be charged as-is to the client in the client-side order in the currency charged by the venue.
 * @param discloseTradingVenue (agency only) If “Yes” the trading venue will be visible in the client-side order.
 */
public record ExecutionConfiguration(
        String name,
        TradingMode tradingMode,
        String counterPortfolio,
        BigDecimal fixedFee,
        BigDecimal percentageFee,
        BigDecimal minFee,
        String feeCurrency,
        String agencyTradingAccount,
        Boolean chargeExchangeFee,
        Boolean discloseTradingVenue) implements ConfigurationItem<ExecutionConfiguration> {
}
