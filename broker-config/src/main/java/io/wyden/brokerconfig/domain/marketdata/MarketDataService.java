package io.wyden.brokerconfig.domain.marketdata;

import io.wyden.brokerconfig.domain.ConfigurationResolver;
import io.wyden.brokerconfig.domain.ConfigurationValidator;
import io.wyden.brokerconfig.domain.MissingConfigException;
import io.wyden.brokerconfig.interfaces.rabbit.EventLogEmitter;
import io.wyden.brokerconfig.interfaces.rabbit.MarketDataRequestEmitter;
import io.wyden.published.brokerdesk.PricingConfig;
import io.wyden.published.marketdata.MarketDataRequest;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.VenueType;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import jakarta.annotation.Nullable;
import org.springframework.stereotype.Component;

import static io.wyden.brokerconfig.domain.ConfigurationValidator.softValidate;
import static io.wyden.brokerconfig.interfaces.rabbit.ProtoUtil.toHumanReadableString;
import static org.apache.commons.lang3.Validate.notBlank;

@Component
public class MarketDataService {

    private final ConfigurationResolver configurationResolver;
    private final InstrumentsCacheFacade instrumentsCacheFacade;
    private final MarketDataRequestEmitter marketDataRequestEmitter;
    private final EventLogEmitter eventLogEmitter;

    public MarketDataService(ConfigurationResolver configurationResolver,
                             InstrumentsCacheFacade instrumentsCacheFacade,
                             MarketDataRequestEmitter marketDataRequestEmitter,
                             EventLogEmitter eventLogEmitter) {
        this.configurationResolver = configurationResolver;
        this.instrumentsCacheFacade = instrumentsCacheFacade;
        this.marketDataRequestEmitter = marketDataRequestEmitter;
        this.eventLogEmitter = eventLogEmitter;
    }

    public void onRequest(MarketDataRequest marketDataRequest) {
        io.wyden.published.referencedata.Instrument instrument = instrumentsCacheFacade.find(marketDataRequest.getInstrumentKey().getInstrumentId())
            .orElseThrow(() -> new MissingConfigException("Cannot find instrument for request: %s".formatted(marketDataRequest)));

        PricingConfig pricingConfig = null;

        // Pricing config is mandatory for Client side
        // Pricing config is not supported for Street side
        if (isClientSide(instrument)) {
            pricingConfig = getPricingConfig(marketDataRequest, instrument);
            ConfigurationValidator.ValidationResult pricingValidation = softValidate(pricingConfig);
            if (pricingValidation.preventsMarketData()) {
                eventLogEmitter.emitMarketDataRequestRejected(marketDataRequest,
                    "Cannot serve MarketDataRequest due to pricing configuration errors. Request: %s".formatted(toHumanReadableString(marketDataRequest)));
                return;
            }
        } else {
            PricingConfig
                .newBuilder().setPricingEnabled(false);
        }

        MarketDataRequest enrichedRequest = enrich(marketDataRequest, pricingConfig);
        marketDataRequestEmitter.emit(enrichedRequest);
    }

    private boolean isClientSide(Instrument instrument) {
        return instrument.getBaseInstrument().getVenueType() == VenueType.CLIENT;
    }

    @Nullable
    private PricingConfig getPricingConfig(MarketDataRequest marketDataRequest, io.wyden.published.referencedata.Instrument instrument) {
        String portfolioId = marketDataRequest.getPortfolioId();
        String instrumentId = marketDataRequest.getInstrumentKey().getInstrumentId();
        String baseCurrency = notBlank(instrument.getForexSpotProperties().getBaseCurrency());
        return configurationResolver.resolveForMarketData(portfolioId, instrumentId, baseCurrency, instrument.getBaseInstrument().getQuoteCurrency());
    }

    private MarketDataRequest enrich(MarketDataRequest marketDataRequest, @Nullable PricingConfig pricingConfig) {
        MarketDataRequest.Builder prototype = marketDataRequest.toBuilder();

        if (pricingConfig != null) {
            prototype.setPricingConfig(pricingConfig);
        }

        return prototype.build();
    }

}
