package io.wyden.brokerconfig.domain.resolver;

import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.function.BinaryOperator;

import static org.slf4j.LoggerFactory.getLogger;

public class GenericConfigResolver<CONFIG, BUILDER extends Message.Builder, MESSAGE extends Message> implements ConfigResolver<BUILDER, MESSAGE> {

    private static final Logger LOGGER = getLogger(GenericConfigResolver.class);

    private final String propertyName;
    private final BinaryOperator<CONFIG> configReducer;

    public GenericConfigResolver(String propertyName, BinaryOperator<CONFIG> configReducer) {
        this.propertyName = propertyName;
        this.configReducer = configReducer;
    }

    @Override
    public void resolve(BUILDER target, MESSAGE... configs) {
        Arrays.stream(configs)
            .map(config -> callGetter(config, propertyName))
            .reduce(configReducer)
            .ifPresent(value -> callSetter(target, propertyName, value));
    }

    private CONFIG callGetter(MESSAGE target, String propertyName) {
        try {
            Descriptors.FieldDescriptor fieldDescriptor = target.getDescriptorForType().findFieldByName(propertyName);
            CONFIG field = (CONFIG) target.getField(fieldDescriptor);
            LOGGER.trace("Resolved {} to {}", propertyName, field);
            return field;
        } catch (Exception e) {
            LOGGER.error("Failed to get value {} from message: \n{}", propertyName, target);
            return null;
        }
    }

    private void callSetter(BUILDER target, String propertyName, CONFIG value) {
        try {
            Descriptors.FieldDescriptor fieldDescriptor = target.getDescriptorForType().findFieldByName(propertyName);
            target.setField(fieldDescriptor, value);
        } catch (Exception e) {
            LOGGER.error("Failed to set value {}={} in message: \n{}", propertyName, value, target);
        }
    }
}
