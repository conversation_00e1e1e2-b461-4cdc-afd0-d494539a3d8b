# ---------------------------------------------------------------------------------------------------------------------
# TERRAGRUNT CONFIGURATION
# Terragrunt is a thin wrapper for Terraform that provides extra tools for working with multiple Terraform modules,
# remote state, and locking: https://github.com/gruntwork-io/terragrunt
# ---------------------------------------------------------------------------------------------------------------------

locals {
  merged = merge(
    yamldecode(file(find_in_parent_folders("subscription.yaml"))),
    yamldecode(file(find_in_parent_folders("region.yaml"))),
    yamldecode(file(find_in_parent_folders("env.yaml")))
  )

  location          = local.merged.location
  environment       = local.merged.environment
  subscription_id   = local.merged.subscription_id

  resource_group = "Developer_VMs"
  # To upgrade to v4 https://github.com/Azure/terraform-azurerm-aks/issues/597
  azurerm = "3.116.0"
  terraform-azurerm-network = "5.3.0"
  terraform-azurerm-aks = "9.1.0"
  terraform-storage-account = "1.7.0"
  terraform-azurerm-role-assignment = "1.2.0"
}

# Generate Azure providers
generate "versions" {
  path      = "versions_override.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
    terraform {
      required_providers {
        azurerm = {
          source = "hashicorp/azurerm"
          version = "${local.azurerm}"
        }
      }
    }

    provider "azurerm" {
        features {}
        subscription_id = "${local.subscription_id}"
    }
EOF
}

remote_state {
    backend = "azurerm"
    config = {
      subscription_id = "${local.subscription_id}"
      key = "${path_relative_to_include()}/terraform.tfstate"
      resource_group_name = "${local.resource_group}"
      storage_account_name = "wydenmvp"
      container_name = "environment-states"
    }
    generate = {
      path      = "backend.tf"
      if_exists = "overwrite_terragrunt"
    }
}

# ---------------------------------------------------------------------------------------------------------------------
# GLOBAL PARAMETERS
# These variables apply to all configurations in this subfolder. These are automatically merged into the child
# `terragrunt.hcl` config via the include block.
# ---------------------------------------------------------------------------------------------------------------------

# Configure root level variables that all resources can inherit. This is especially helpful with multi-account configs
# where terraform_remote_state data sources are placed directly into the modules.
# inputs = merge(
#   local.subscription_vars.locals,
#   local.region_vars.locals,
#   local.environment_vars.locals
# )