terraform {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-kms.git?ref=${include.root.locals.terraform-aws-kms}"
}

## Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

inputs = {
  aliases = ["rds/${include.root.locals.merged.default_tags.client}-${include.root.locals.merged.default_tags.environment}"]
  description                        = "Using for encrypt RDS"
  deletion_window_in_days            = 30
  enable_key_rotation                = true
  multi_region                       = true
  bypass_policy_lockout_safety_check = false
  customer_master_key_spec           = "SYMMETRIC_DEFAULT"
  is_enabled                         = true
  key_usage                          = "ENCRYPT_DECRYPT"
  policy                             = <<EOF
{
    "Version": "2012-10-17",
    "Id": "key-default-1",
    "Statement": [
        {
            "Sid": "Enable IAM User Permissions",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::${include.root.locals.account_id}:root"
            },
            "Action": "kms:*",
            "Resource": "*"
        }
    ]
}
EOF
  rotation_period_in_days            = 365

  tags = merge(include.root.locals.default_tags)
}
