terraform {
  source = "git::https://github.com/drata/terraform-aws-drata-autopilot-role.git?ref=${include.root.locals.terraform-aws-drata-autopilot-role}"
}

## Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

inputs = {
  role_sts_externalid = "4f17c4e1-be4a-4935-ada2-fa1aa7fea78e"
  tags = merge(include.root.locals.default_tags, { software = "drata" })
}
