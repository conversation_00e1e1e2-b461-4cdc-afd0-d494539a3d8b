apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: sopra-sandbox
spec:
  template:
    metadata:
      labels:
        environment: sandbox
        client: sopra
    spec:
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: sopra-sandbox
      taints:
        - key: sandbox
          value: "true"
          effect: NoSchedule
        - key: client
          value: "sopra"
          effect: NoSchedule
      requirements:
        - key: "topology.kubernetes.io/zone"
          operator: In
          values: [${availability_zones}]
        - key: "karpenter.sh/capacity-type"
          operator: In
          values: ["spot", "on-demand"]
        - key: "kubernetes.io/arch"
          operator: In
          values: ["arm64", "amd64"]
        - key: karpenter.k8s.aws/instance-category
          operator: In
          values: ["t"]
      kubelet:
        systemReserved:
          cpu: 100m
          memory: 100Mi
          ephemeral-storage: 1Gi
  limits:
    cpu: 30
    memory: 100Gi
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 30m
    budgets:
    # On Weekdays during business hours, don't do any deprovisioning.
    - schedule: "0 8 * * mon-fri"
      duration: 12h
      nodes: "0"
