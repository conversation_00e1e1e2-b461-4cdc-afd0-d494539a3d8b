terraform {
  source = "${get_parent_terragrunt_dir()}//modules/gitlab-runner"
}

dependency "eks" {
  config_path = "${get_terragrunt_dir()}/../../eks"
}

dependency "iam_s3_cache_read_write" {
  config_path = "${get_parent_terragrunt_dir("root")}/${include.root.locals.environment}/${include.root.locals.aws_region}/iam/policy/S3Bucket/runner-cache-develop/rw"
}

dependency "iam_s3_backup_read_write" {
  config_path = "${get_parent_terragrunt_dir("root")}/${include.root.locals.environment}/${include.root.locals.aws_region}/iam/policy/S3Bucket/wyden-backup-develop/rw"
}

dependency "iam_gitlab_runner" {
  config_path = "${get_parent_terragrunt_dir("root")}/${include.root.locals.environment}/${include.root.locals.aws_region}/iam/policy/gitlab-runner"
  mock_outputs = {
    arn = "arn:aws:iam::123456789012:policy/mock"
  }
}

# Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

inputs = {
  cluster_name          = dependency.eks.outputs.eks.cluster_name
  runner_name           = "gitlab-runner-kubernetes-dind"
  values_path           = "${get_terragrunt_dir()}/files/values.yaml"
  secrets               = yamldecode(file("${get_terragrunt_dir()}/files/secrets.yaml"))
  gitlab_runner_version = "0.72.0"
  gitlab_url            = "https://gitlab.wyden.io"
  role_policy_arns      = {
    cache = "${dependency.iam_s3_cache_read_write.outputs.arn}",
    backup = "${dependency.iam_s3_backup_read_write.outputs.arn}",
    gitlab = "${dependency.iam_gitlab_runner.outputs.arn}"
  }
  tags          = merge(include.root.locals.default_tags)
}
