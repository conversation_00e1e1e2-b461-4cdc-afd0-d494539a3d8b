dependency "eks" {
  config_path = "${get_parent_terragrunt_dir()}/${include.root.locals.environment}/${include.root.locals.aws_region}/eks-cluster/eks"
}

terraform {
  source = "${get_parent_terragrunt_dir()}//modules/aws-lb-controller"
}

# Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

inputs = {
  cluster_name  = dependency.eks.outputs.eks.cluster_name
  chart_version = "1.11.0"
  crd_version   = "0.0.189"
  tags          = merge(include.root.locals.default_tags)
}
