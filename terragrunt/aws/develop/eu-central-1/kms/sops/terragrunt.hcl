locals {
  description = "Using for encrypt Helm secrets"
}

terraform {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-kms.git?ref=${include.root.locals.terraform-aws-kms}"
}

dependency "gitlab-runner" {
  config_path = "${get_parent_terragrunt_dir("root")}/${include.root.locals.environment}/${include.root.locals.aws_region}/eks-cluster/addons/gitlab-runner"
  mock_outputs = {
    gitlab_runner_role_arn = "arn:aws:iam::123456789012:role/somerole"
  }
}



# Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

inputs = {
  aliases                             = ["sops/${include.root.locals.merged.default_tags.client}-${include.root.locals.merged.default_tags.environment}"]
  description                        = local.description
  deletion_window_in_days            = 30
  enable_key_rotation                = true
  multi_region                       = true
  bypass_policy_lockout_safety_check = null
  customer_master_key_spec           = "SYMMETRIC_DEFAULT"
  is_enabled                         = true
  key_usage                          = "ENCRYPT_DECRYPT"
  rotation_period_in_days            = 365
  policy                             = <<POLICYEOF
{
    "Version": "2012-10-17",
    "Id": "key-default-1",
    "Statement": [
        {
            "Sid": "Enable IAM User Permissions",
            "Effect": "Allow",
            "Principal": {
                "AWS": [
                    "arn:aws:iam::${include.root.locals.account_id}:root",
                    "arn:aws:iam::${include.root.locals.account_id}:role/OrganizationAccountAccessRole"
                ]
            },
            "Action": "kms:*",
            "Resource": "*"
        },
        {
            "Sid": "Allow use of the key",
            "Effect": "Allow",
            "Principal": {
                "AWS": [
                    "${dependency.gitlab-runner.outputs.gitlab_runner_role_arn}"
                ]
            },
            "Action": [
                "kms:Encrypt",
                "kms:Decrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
                "kms:DescribeKey"
            ],
            "Resource": "*"
        },
        {
            "Sid": "Allow attachment of persistent resources",
            "Effect": "Allow",
            "Principal": {
                "AWS": [
                    "${dependency.gitlab-runner.outputs.gitlab_runner_role_arn}"
                ]
            },
            "Action": [
                "kms:CreateGrant",
                "kms:ListGrants",
                "kms:RevokeGrant"
            ],
            "Resource": "*",
            "Condition": {
                "Bool": {
                    "kms:GrantIsForAWSResource": "true"
                }
            }
        }
    ]
}
POLICYEOF
  tags                               = merge(include.root.locals.default_tags)
}
