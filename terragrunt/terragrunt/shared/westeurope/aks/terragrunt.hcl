locals {
  location = include.root.locals.location
}

# dependency "oidc-azure" {
#   config_path = "../oidc-azure"
#   mock_outputs = {
#     managed_identity_name = "test"
#   }
# }

# dependencies {
#   paths = ["../oidc-azure"]
# }

terraform {
  source = "tfr:///Azure/aks/azurerm//?version=${include.root.locals.terraform-azurerm-aks}"
}

include "root" {
  path = find_in_parent_folders()
  expose = true
}

inputs = {
  prefix                               = "algotrader-poc-dns"
  resource_group_name                  = "${include.root.locals.resource_group}"
  location = local.location
  sku_tier = "Standard"
  kubernetes_version = include.root.locals.merged.aks.version
  cluster_name                         = "algotrader-poc"
  log_analytics_workspace_enabled      = false
  net_profile_pod_cidr              = "**********/16"
  private_cluster_enabled           = false
  rbac_aad_managed                  = true
  role_based_access_control_enabled = true
  api_server_authorized_ip_ranges = ["0.0.0.0/0"]
  rbac_aad_admin_group_object_ids = ["42064b83-3ea5-4d4f-9344-8df6c364c9fc", "80e0f3c5-e232-43ed-b653-7a40be181bb0"]

  # Specifies the name of the temporary node pool used to cycle the default node pool for VM resizing.
  temporary_name_for_rotation = "primarytemp"
  agents_pool_name = "primary"
  agents_size = "Standard_D2pls_v5"
  agents_min_count = 3
  agents_max_count = 5
  agents_pool_drain_timeout_in_minutes = 30

  auto_scaler_profile_enabled = true
  auto_scaler_profile_max_node_provisioning_time = "5m"
  auto_scaler_profile_max_unready_nodes = "0"
  auto_scaler_profile_skip_nodes_with_local_storage = false
  auto_scaler_profile_skip_nodes_with_system_pods = false

  # agents_pool_max_surge = "10%"
  os_disk_size_gb = 128
  enable_auto_scaling = true
  oidc_issuer_enabled = true
  # Doen't work because requires Helm installation
  # https://artifacthub.io/packages/helm/azure-workload-identity/workload-identity-webhook
  workload_identity_enabled = false

  tags = {
    Environment = "${include.root.locals.merged.environment}"
  }
  agents_tags = {
    Environment = "${include.root.locals.merged.environment}"
  }
}