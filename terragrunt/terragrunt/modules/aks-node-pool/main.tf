data "azurerm_kubernetes_cluster" "this" {
  name                = var.kubernetes_cluster_name
  resource_group_name = var.resource_group
}

resource "azurerm_public_ip_prefix" "user" {
  for_each = { for idx, pool in var.node_pools : idx => pool if try(pool.enable_node_public_ip, false) }

  name                = each.key
  resource_group_name = var.resource_group
  location            = var.location
  prefix_length       = try(each.value.prefix_length, 29)
}

resource "azurerm_kubernetes_cluster_node_pool" "user" {
  lifecycle {
    ignore_changes = [
      node_count
    ]
  }
  custom_ca_trust_enabled  = try(each.value.custom_ca_trust_enabled, false)
  fips_enabled             = try(each.value.fips_enabled, false)
  for_each                 = try(var.node_pools, {})
  name                     = each.key
  mode                     = try(each.value.mode, "User")
  kubernetes_cluster_id    = data.azurerm_kubernetes_cluster.this.id
  orchestrator_version     = data.azurerm_kubernetes_cluster.this.kubernetes_version
  vm_size                  = try(each.value.vm_size, null)
  os_disk_size_gb          = try(each.value.os_disk_size_gb, null)
  os_disk_type             = try(each.value.os_disk_type, null)
  node_count               = try(each.value.node_count, 1)
  min_count                = try(each.value.min_count, null)
  max_count                = try(each.value.max_count, null)
  priority                 = try(each.value.priority, null)
  eviction_policy          = try(each.value.eviction_policy, null)
  vnet_subnet_id           = try(var.vnet_subnet_id, null)
  zones                    = try(each.value.availability_zones, null)
  enable_auto_scaling      = try(each.value.enable_auto_scaling, false)
  enable_node_public_ip    = try(each.value.enable_node_public_ip, false)
  node_public_ip_prefix_id = try(each.value.enable_node_public_ip, false) ? azurerm_public_ip_prefix.user[each.key].id : null
  node_labels              = try(each.value.node_labels, null)
  node_taints              = try(each.value.node_taints, null)
  enable_host_encryption   = try(each.value.enable_host_encryption, false)
  max_pods                 = try(each.value.max_pods, 100)

  dynamic "upgrade_settings" {
    for_each = try(each.value.upgrade_settings, {})
    content {
      max_surge = upgrade_settings.value
    }
  }

  tags = try(merge(var.tags, each.value.agents_tags), var.tags)
}
