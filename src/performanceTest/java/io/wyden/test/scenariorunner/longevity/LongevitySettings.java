package io.wyden.test.scenariorunner.longevity;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Properties;

public record LongevitySettings(Duration clobQuotingTestDuration, int clobQuotingSubscriptionCount) {

    private static final Logger LOGGER = LoggerFactory.getLogger(LongevitySettings.class);

    private static final String CLOB_QUOTING_TEST_DURATION = "clob.quoting.test.duration";
    private static final String CLOB_QUOTING_SUBSCRIPTION_COUNT = "clob.quoting.subscription.count";

    public static LongevitySettings fromProperties(Properties properties) {
        LongevitySettings settings = new LongevitySettings(
            Duration.parse(properties.getProperty(CLOB_QUOTING_TEST_DURATION)),
            Integer.parseInt(properties.getProperty(CLOB_QUOTING_SUBSCRIPTION_COUNT))
        );
        LOGGER.info("Longevity settings: " + settings);
        return settings;
    }
}
