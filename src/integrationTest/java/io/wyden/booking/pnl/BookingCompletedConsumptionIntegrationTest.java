package io.wyden.booking.pnl;

import com.hazelcast.map.IMap;
import io.wyden.booking.pnl.actor.InboundMessageProducer;
import io.wyden.booking.pnl.domain.model.ledgerentry.LedgerEntry;
import io.wyden.booking.pnl.domain.service.PnlService;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.LedgerEntrySnapshot;
import io.wyden.published.booking.LedgerEntryType;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

public class BookingCompletedConsumptionIntegrationTest extends TestContainersIntegrationBase {

    private InboundMessageProducer fakeInboundMessageProducer;

    @Autowired
    private IMap<String, Portfolio> portfoliosMap;

    @Autowired
    private IMap<String, VenueAccount> venueAccountMap;

    @SpyBean
    protected PnlService pnlService;

    @BeforeEach
    void setUp() throws Exception {
        fakeInboundMessageProducer = new InboundMessageProducer(rabbitIntegrator);

        // Clear tables before each test
        DB.clearTable("ledger_entry");
        DB.clearTable("generic_reference");
        DB.clearTable("instrument");

        // Set up portfolio and venue account in Hazelcast
        Portfolio portfolio = Portfolio.newBuilder()
            .setId("p1")
            .setName("Test Portfolio")
            .setPortfolioCurrency("BTC")
            .build();

        portfoliosMap.put("p1", portfolio);

        VenueAccount venueAccount = VenueAccount.newBuilder()
            .setId("acc1")
            .setVenueAccountName("Test Account")
            .build();

        venueAccountMap.put("acc1", venueAccount);
    }

    @Test
    void shouldConsumeBookingCompleted() throws Exception {
        // when
        fakeInboundMessageProducer.produceBookingCompleted(BookingCompleted.newBuilder()
            .setSequenceNumber(1L)
            .addLedgerEntrySnapshot(LedgerEntrySnapshot.newBuilder()
                .setAccount("acc1")
                .setQuantity("1")
                .setPrice("95000")
                .setSymbol("BTCUSD@FOREX@Binance")
                .setType(LedgerEntryType.ASSET_TRADE_BUY)
                .setTransactionId("tx1")
                .setReservationRef("r1")
                .setCurrency("BTC")
                .build())
            .addLedgerEntrySnapshot(LedgerEntrySnapshot.newBuilder()
                .setPortfolio("p1")
                .setQuantity("1")
                .setPrice("95000")
                .setSymbol("BTCUSD@FOREX@Binance")
                .setType(LedgerEntryType.CASH_TRADE_CREDIT)
                .setTransactionId("tx1")
                .setReservationRef("r1")
                .setCurrency("BTC")
                .build())
            .build());

        // then
        ArgumentCaptor<List<LedgerEntry>> captor = ArgumentCaptor.forClass(List.class);
        verify(pnlService, timeout(1000)).consume(captor.capture(), anyLong());
        assertThat(captor.getValue()).hasSize(2);
        // assertThat(captor.getAllValues()).containsExactlyInAnyOrder(
        //     new LedgerEntry("acc1", null, "BTCUSD@FOREX@Binance", new BigDecimal("1"), new BigDecimal("95000")),
        //     new LedgerEntry(null, "p1", "BTCUSD@FOREX@Binance", new BigDecimal("1"), new BigDecimal("95000"))
        // );


        DB.logTableContent("instrument");
        DB.logTableContent("generic_reference");
        DB.logTableContent("ledger_entry");
    }
}
