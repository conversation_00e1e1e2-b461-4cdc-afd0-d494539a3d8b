package io.wyden.booking.reporting.integration;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.permissions.AccessGatewayMockClient;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntryRepository;
import io.wyden.booking.reporting.integration.producer.BookingCompletedMessageProducer;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.LedgerEntrySearch;
import io.wyden.published.booking.LedgerEntrySnapshot;
import io.wyden.published.booking.LedgerEntryType;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.Metadata;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.time.Duration;
import java.util.List;
import java.util.Set;

import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class LedgerEntryIntegrationTest extends SearchIntegrationTest {

    // TODO-MD Test scenarios:

    /*
    - when BookingPnL event is emitted to booking-engine.PNL_COMPLETED and contains LedgerEntries
    - then LedgerEntries can be queried in /ledger-entries/search
     */

    private static final Logger LOGGER = getLogger(LedgerEntryIntegrationTest.class);

    BookingCompletedMessageProducer bookingCompletedMessageProducer;

    @Autowired
    private AccessGatewayMockClient accessGatewayMockClient;

    @Autowired
    LedgerEntryRepository ledgerEntryRepository;

    @Autowired
    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        bookingCompletedMessageProducer = new BookingCompletedMessageProducer(rabbitIntegrator);
    }


    @Test
    void shouldPersistLedgerEntries() throws Exception {
        emitAndWait(1);

        List<LedgerEntry> ledgerEntries = ledgerEntryRepository.findAll();

        DB.logTableContent("ledger_entry");

        LOGGER.info("Ledger entries found ({}): {}", ledgerEntries.size(), ledgerEntries);

        assertThat(ledgerEntries).isNotEmpty();
    }

    @Test
    void shouldSearchLedgerEntries() throws Exception {
        // setup permissions
        Set<PermissionDto> permissions = Set.of(
            new PermissionDto("portfolio", "read", "client-a"),
            new PermissionDto("venue.account", "read", "bitmex-testnet1")
        );
        accessGatewayMockClient.grantPermissions(permissions);

        List<LedgerEntrySnapshot> ledgerEntries = emitAndWait(2);

        LedgerEntrySearch searchRequest = LedgerEntrySearch.newBuilder()
            .setOrderId("reservationRef-2")
            .setFirst(10)
            .build();

        // Send search request and get response
        MvcResult result = mockMvc.perform(post("/ledger-entries/search?version=proto")
                .contentType("application/x-protobuf")
                .content(searchRequest.toByteArray()))
            .andExpect(status().isOk())
            .andDo(print())
            .andReturn();

        // Parse the binary response
        byte[] content = result.getResponse().getContentAsByteArray();
        CursorConnection connection = CursorConnection.parseFrom(content);

        LOGGER.info("Search response: \n{}", connection);

        // Verify the connection has edges
        assertThat(connection.getEdgesList()).hasSameSizeAs(ledgerEntries);

        verifyLedgerEntriesMatch(connection, ledgerEntries);
    }

    private List<LedgerEntrySnapshot> emitAndWait(long sequenceNumber) {
        List<LedgerEntrySnapshot> ledgerEntries = createLedgerEntries(sequenceNumber);

        ledgerEntries.forEach(ledgerEntry -> {
            if (isNotBlank(ledgerEntry.getPortfolio())) {
                Portfolio.Builder portfolio = createPortfolio(ledgerEntry.getPortfolio());
                save(portfolio);
            }

            if (isNotBlank(ledgerEntry.getAccount())) {
                VenueAccount.Builder account = createAccount(ledgerEntry.getAccount());
                save(account);
            }
         });

        BookingCompleted bookingCompleted = BookingCompleted.newBuilder()
            .setSequenceNumber(sequenceNumber)
            .addAllLedgerEntryCreated(ledgerEntries)
            .build();

        bookingCompletedMessageProducer.emit(bookingCompleted);

        await("Waiting for ledgerEntries to be persisted")
            .atMost(Duration.ofSeconds(10))
            .pollInterval(Duration.ofSeconds(1))
            .until(() -> ledgerEntryRepository.findBySequenceNumber(sequenceNumber).size() == ledgerEntries.size());

        return ledgerEntries;
    }

    private List<LedgerEntrySnapshot> createLedgerEntries(long sequenceNumber) {
        Metadata metadata = Metadata.newBuilder()
            .setCreatedAt("2021-01-01T00:00:00Z")
            .setUpdatedAt("2021-01-01T01:12:13Z")
            .build();

        LedgerEntrySnapshot basePortfolioLedgerEntry = LedgerEntrySnapshot.newBuilder()
            .setMetadata(metadata)
            .setType(LedgerEntryType.CASH_TRADE_CREDIT)
            .setQuantity("1")
            .setPrice("60000")
            .setSymbol("BTC")
            .setPortfolio("client-a")
            .setSequenceNumber(sequenceNumber)
            .setTransactionId("transactionId")
            .setReservationRef("reservationRef-" + sequenceNumber)
            .setSettled(false)
            .setBalanceBefore("0")
            .setBalanceAfter("1")
            .addFees(Fee.newBuilder()
                .setFeeType(FeeType.TRANSACTION_FEE)
                .setCurrency("USD")
                .setAmount("10")
                .build())
            .build();

        LedgerEntrySnapshot baseAccountLedgerEntry = LedgerEntrySnapshot.newBuilder()
            .setMetadata(metadata)
            .setType(LedgerEntryType.CASH_TRADE_CREDIT)
            .setQuantity("1")
            .setPrice("60000")
            .setSymbol("BTC")
            .setAccount("bitmex-testnet1")
            .setSequenceNumber(sequenceNumber)
            .setTransactionId("transactionId")
            .setReservationRef("reservationRef-" + sequenceNumber)
            .setSettled(false)
            .setBalanceBefore("0")
            .setBalanceAfter("1")
            .addFees(Fee.newBuilder()
                .setFeeType(FeeType.TRANSACTION_FEE)
                .setCurrency("USD")
                .setAmount("10")
                .build())
            .build();

        LedgerEntrySnapshot quotePortfolioLedgerEntry = LedgerEntrySnapshot.newBuilder()
            .setMetadata(metadata)
            .setType(LedgerEntryType.CASH_TRADE_CREDIT)
            .setQuantity("-60000")
            .setPrice("1")
            .setSymbol("USD")
            .setPortfolio("client-a")
            .setSequenceNumber(sequenceNumber)
            .setTransactionId("transactionId")
            .setReservationRef("reservationRef-" + sequenceNumber)
            .setSettled(false)
            .setBalanceBefore("100000")
            .setBalanceAfter("40000")
            .build();

        LedgerEntrySnapshot quoteAccountLedgerEntry = LedgerEntrySnapshot.newBuilder()
            .setMetadata(metadata)
            .setType(LedgerEntryType.CASH_TRADE_CREDIT)
            .setQuantity("-60000")
            .setPrice("1")
            .setSymbol("USD")
            .setAccount("bitmex-testnet1")
            .setSequenceNumber(sequenceNumber)
            .setTransactionId("transactionId")
            .setReservationRef("reservationRef-" + sequenceNumber)
            .setSettled(false)
            .setBalanceBefore("100000")
            .setBalanceAfter("40000")
            .build();

        return List.of(
            basePortfolioLedgerEntry,
            quotePortfolioLedgerEntry,
            baseAccountLedgerEntry,
            quoteAccountLedgerEntry
        );
    }

    private void verifyLedgerEntriesMatch(CursorConnection connection, List<LedgerEntrySnapshot> expectedLedgerEntries) {
        // Extract LedgerEntrySnapshot objects from the connection
        List<LedgerEntrySnapshot> actualLedgerEntries = connection.getEdgesList().stream()
            .map(edge -> edge.getNode().getLedgerEntry())
            .toList();

        LOGGER.info("Expected ledger entries count: {}", expectedLedgerEntries.size());
        LOGGER.info("Actual ledger entries count: {}", actualLedgerEntries.size());

        // Verify the counts match
        assertThat(actualLedgerEntries).hasSameSizeAs(expectedLedgerEntries);

        // Sort both lists by a consistent field for comparison (e.g., symbol + portfolio/account)
        List<LedgerEntrySnapshot> sortedExpected = expectedLedgerEntries.stream()
            .sorted(this::compareEntries)
            .toList();

        List<LedgerEntrySnapshot> sortedActual = actualLedgerEntries.stream()
            .sorted(this::compareEntries)
            .toList();

        // Verify each ledger entry matches
        for (int i = 0; i < sortedExpected.size(); i++) {
            LedgerEntrySnapshot expected = sortedExpected.get(i);
            LedgerEntrySnapshot actual = sortedActual.get(i);

            LOGGER.debug("Comparing ledger entry {}: expected={}, actual={}", i, expected, actual);

            assertThat(actual)
                .as("Ledger entry at index %d should match", i)
                .satisfies(entry -> {
                    assertThat(entry.getType()).isEqualTo(expected.getType());
                    assertThat(entry.getQuantity()).isEqualTo(expected.getQuantity());
                    assertThat(entry.getPrice()).isEqualTo(expected.getPrice());
                    assertThat(entry.getSymbol()).isEqualTo(expected.getSymbol());
                    assertThat(entry.getSequenceNumber()).isEqualTo(expected.getSequenceNumber());
                    assertThat(entry.getTransactionId()).isEqualTo(expected.getTransactionId());
                    assertThat(entry.getReservationRef()).isEqualTo(expected.getReservationRef());
                    assertThat(entry.getSettled()).isEqualTo(expected.getSettled());
                    assertThat(entry.getBalanceBefore()).isEqualTo(expected.getBalanceBefore());
                    assertThat(entry.getBalanceAfter()).isEqualTo(expected.getBalanceAfter());

                    // Compare portfolio/account (only one should be set)
                    if (isNotBlank(expected.getPortfolio())) {
                        assertThat(entry.getPortfolio()).isEqualTo(expected.getPortfolio());
                    } else if (isNotBlank(expected.getAccount())) {
                        assertThat(entry.getAccount()).isEqualTo(expected.getAccount());
                    }

                    // Compare fees
                    assertThat(entry.getFeesList()).hasSameSizeAs(expected.getFeesList());
                    if (!entry.getFeesList().isEmpty()) {
                        for (int j = 0; j < entry.getFeesList().size(); j++) {
                            assertThat(entry.getFees(j).getAmount()).isEqualTo(expected.getFees(j).getAmount());
                            assertThat(entry.getFees(j).getCurrency()).isEqualTo(expected.getFees(j).getCurrency());
                            assertThat(entry.getFees(j).getFeeType()).isEqualTo(expected.getFees(j).getFeeType());
                        }
                    }
                });
        }
    }

    private int compareEntries(LedgerEntrySnapshot a, LedgerEntrySnapshot b) {
        // Create a consistent sorting key: symbol + portfolio + account
        String keyA = "%s_%s_%s".formatted(a.getSymbol(), a.getPortfolio(), a.getAccount());
        String keyB = "%s_%s_%s".formatted(b.getSymbol(), b.getPortfolio(), b.getAccount());
        return keyA.compareTo(keyB);
    }
}
