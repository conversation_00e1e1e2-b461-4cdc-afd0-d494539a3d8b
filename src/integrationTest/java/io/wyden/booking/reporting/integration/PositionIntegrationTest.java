package io.wyden.booking.reporting.integration;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.permissions.AccessGatewayMockClient;
import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.booking.reporting.domain.position.PositionRepository;
import io.wyden.booking.reporting.integration.producer.PositionSnapshotMessageProducer;
import io.wyden.published.booking.PositionSearch;
import io.wyden.published.booking.PositionSnapshot;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorNode;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.time.Duration;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class PositionIntegrationTest extends SearchIntegrationTest {

    @Autowired
    protected AccessGatewayMockClient accessGatewayMockClient;

    @Autowired
    private PositionRepository positionRepository;

    @Autowired
    private MockMvc mockMvc;

    PositionSnapshotMessageProducer positionSnapshotMessageProducer;

    @BeforeEach
    void setUp() {
        positionSnapshotMessageProducer = new PositionSnapshotMessageProducer(rabbitIntegrator, positionRepository);
    }

    @AfterEach
    void tearDown() throws Exception {
        DB.logTableContent("position");
    }

    @Test
    void testPositionSnapshotConsumption() {
        // given
        String symbol = UUID.randomUUID().toString();
        PositionSnapshot positionSnapshot = createPositionSnapshot(symbol);

        // when
        Position position = emitAndWait(positionSnapshot);

        // then
        assertThat(position.getSymbol()).isEqualTo(symbol);
        assertThat(position.getQuantity()).isEqualByComparingTo("50.75");
        assertThat(position.getAccountId()).isEqualTo(positionSnapshot.getAccount());
    }

    @Test
    void testPositionSnapshotUpdate() {
        String symbol = UUID.randomUUID().toString();

        // First insert
        PositionSnapshot positionSnapshot1 = createPositionSnapshot(symbol);
        Position position1 = emitAndWait(positionSnapshot1);

        String accountId = positionSnapshot1.getAccount();

        // Verify initial position is saved
        assertThat(position1.getQuantity()).isEqualByComparingTo("50.75");

        // Update with different quantity
        PositionSnapshot positionSnapshot2 = positionSnapshot1
            .toBuilder()
            .setSequenceNumber(++sequenceCounter)
            .setQuantity("100.25") // updated quantity
            .build();

        positionSnapshotMessageProducer.emit(positionSnapshot2);

        // then - verify position is updated
        await().atMost(Duration.ofSeconds(5)).untilAsserted(() -> {
            Optional<Position> positionOpt = positionRepository.findBySymbolAndAccountIdOrPortfolioId(symbol, accountId, "");
            assertThat(positionOpt).isPresent();
            assertThat(positionOpt.get().getQuantity()).isEqualByComparingTo("100.25");
        });
    }

    @Test
    void shouldReturnPositionSnapshotsMatchingSearchCriteria() {
        // Given
        String symbol = UUID.randomUUID().toString();
        String portfolioId = "portfolio-abc";
        String currency = "USD";

        // setup portfolio
        Portfolio.Builder portfolio = createPortfolio(portfolioId)
            .setPortfolioCurrency("EUR");

        save(portfolio);

        // setup permissions
        Set<PermissionDto> permissions = Set.of(
            new PermissionDto("portfolio", "read", portfolioId)
        );
        accessGatewayMockClient.grantPermissions(permissions);

        PositionSnapshot snapshot = PositionSnapshot.newBuilder()
            .setSequenceNumber(++sequenceCounter)
            .setSymbol(symbol)
            .setPortfolio(portfolioId)
            .setQuantity("100.50")
            .setCurrency(currency)
            .build();

        positionSnapshotMessageProducer.emit(snapshot);

        // Wait for processing
        await()
            .atMost(Duration.ofSeconds(5))
            .pollInterval(Duration.ofSeconds(1))
            .untilAsserted(() -> {
                // Build position search request
                PositionSearch positionSearch = PositionSearch.newBuilder()
                    .addSymbol(symbol)
                    .addPortfolioId(portfolioId)
                    .setFirst(10)
                    .build();

                // when - search for positions via REST API
                MvcResult result = mockMvc.perform(post("/positions/search?version=proto")
                        .contentType(MediaType.APPLICATION_PROTOBUF)
                        .content(positionSearch.toByteArray()))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andReturn();

                CursorConnection response = CursorConnection.parseFrom(result.getResponse().getContentAsByteArray());
                assertThat(response.getEdgesList()).hasSize(1);
                CursorNode node = response.getEdges(0).getNode();
                assertThat(node.hasPosition()).isTrue();
                assertThat(node.getPosition().getSymbol()).isEqualTo(symbol);
                assertThat(node.getPosition().getPortfolio()).isEqualTo(portfolioId);
            });
    }

    @Test
    void shouldReturnPositionSnapshotsViaInternalEndpoint() throws Exception {
        // Given
        String symbol = UUID.randomUUID().toString();
        String accountId = "account-def";

        // setup permissions
        accessGatewayMockClient.revokePermissions();

        PositionSnapshot snapshot = PositionSnapshot.newBuilder()
            .setSequenceNumber(++sequenceCounter)
            .setSymbol(symbol)
            .setAccount(accountId)
            .setPortfolio("")
            .setQuantity("250.75")
            .setCurrency("EUR")
            .build();

        emitAndWait(snapshot);

        // Build position search request
        PositionSearch positionSearch = PositionSearch.newBuilder()
            .addSymbol(symbol)
            .addAccountId(accountId)
            .setFirst(10)
            .build();

        // when - search for positions via internal REST API
        MvcResult result = mockMvc.perform(post("/internal/positions/search?version=proto")
                .contentType(MediaType.APPLICATION_PROTOBUF)
                .content(positionSearch.toByteArray()))
            .andDo(print())
            .andExpect(status().isOk())
            .andReturn();

        CursorConnection response = CursorConnection.parseFrom(result.getResponse().getContentAsByteArray());
        assertThat(response.getEdgesList()).hasSize(1);
        CursorNode node = response.getEdges(0).getNode();
        assertThat(node.hasPosition()).isTrue();
        assertThat(node.getPosition().getSymbol()).isEqualTo(symbol);
        assertThat(node.getPosition().getAccount()).isEqualTo(accountId);
    }

    private Position emitAndWait(PositionSnapshot positionSnapshot) {
        if (isNotBlank(positionSnapshot.getPortfolio())) {
            Portfolio.Builder portfolio = createPortfolio(positionSnapshot.getPortfolio());
            save(portfolio);
        }

        if (isNotBlank(positionSnapshot.getAccount())) {
            VenueAccount.Builder account = createAccount(positionSnapshot.getAccount());
            save(account);
        }

        return positionSnapshotMessageProducer.emitAndWait(positionSnapshot);
    }

    private static long sequenceCounter = 1000;
    
    private static PositionSnapshot createPositionSnapshot(String symbol) {
        return createPositionSnapshot(symbol, ++sequenceCounter);
    }

    private static PositionSnapshot createPositionSnapshot(String symbol, long sequenceNumber) {
        return PositionSnapshot.newBuilder()
            .setSequenceNumber(sequenceNumber)
            .setAccount("account1")
            .setCurrency("USD")
            .setSymbol(symbol)
            .setQuantity("50.75")
            .setBookingCurrency("USD")
            .setNetRealizedPnl("125.50")
            .setGrossRealizedPnl("130.00")
            .setNetCost("1000.00")
            .setGrossCost("1005.00")
            .setNotionalQuantity("50.75")
            .setMarketValue("1150.25")
            .setMarketValueSc("1150.25")
            .setNetRealizedPnlSc("125.50")
            .setGrossRealizedPnlSc("130.00")
            .setNetCostSc("1000.00")
            .setGrossCostSc("1005.00")
            .setNetAveragePrice("19.70")
            .setGrossAveragePrice("19.80")
            .setNetUnrealizedPnl("25.75")
            .setGrossUnrealizedPnl("30.25")
            .setNetUnrealizedPnlSc("25.75")
            .setGrossUnrealizedPnlSc("30.25")
            .setPendingQuantity("2.50")
            .setAvailableForTradingQuantity("48.25")
            .setAvailableForWithdrawalQuantity("45.00")
            .setSettledQuantity("48.00")
            .setUnsettledQuantity("2.75")
            .setLastAppliedLedgerEntryId("12345")
            .build();
    }
}