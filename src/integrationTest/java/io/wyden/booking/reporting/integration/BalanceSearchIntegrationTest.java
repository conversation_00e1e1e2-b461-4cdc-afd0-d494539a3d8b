package io.wyden.booking.reporting.integration;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.balance.BalanceQueryService;
import io.wyden.booking.reporting.domain.balance.Balance;
import io.wyden.booking.reporting.domain.balance.BalanceRepository;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static io.wyden.booking.reporting.interfaces.rest.RequestModel.PositionSearch.builder;
import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.slf4j.LoggerFactory.getLogger;

class BalanceSearchIntegrationTest extends SearchIntegrationTest {

    private static final Logger LOGGER = getLogger(BalanceSearchIntegrationTest.class);

    @Autowired
    private BalanceQueryService balanceQueryService;

    @Autowired
    private BalanceRepository balanceRepository;

    private static Balance b1;
    private static Balance b2;
    private static Balance b3;
    private static Balance b4;
    private static Balance b5;
    private static Balance b6;

    @BeforeEach
    void setUp() {
        setupBalances();
        setupPermissions();

        // setupBalances has to be static, because it could be used in @ParametrizedTest
        // saving in repository has to happen in non-static block
        balanceRepository.save(b1);
        balanceRepository.save(b2);
        balanceRepository.save(b3);
        balanceRepository.save(b4);
        balanceRepository.save(b5);
        balanceRepository.save(b6);
    }

    @AfterEach
    void tearDown() throws Exception {
        DB.clearTable("balance");
    }

    @Test
    void shouldReturnNothing() throws Exception {
        DB.clearTable("balance");

        RequestModel.PositionSearch search = RequestModel.PositionSearch.builder()
                .build();

        searchAndVerifyEmptyResults(search);
    }

    @Test
    void shouldReturnAll() {
        RequestModel.PositionSearch search = RequestModel.PositionSearch.builder()
            .build();

        searchAndVerifyResults(search, b1, b2, b3, b4, b5, b6);
    }

    @Test
    void shouldFindBySymbol() {
        RequestModel.PositionSearch search = builder()
            .symbol(b4.getSymbol(), b6.getSymbol())
            .build();

        searchAndVerifyResults(search, b4, b6);
    }

    @Test
    void shouldFindByCurrency() {
        RequestModel.PositionSearch search = builder()
            .currency(b5.getCurrency())
            .build();

        searchAndVerifyResults(search, b5);
    }

    @Test
    void shouldFindByMultipleFilters() {
        RequestModel.PositionSearch search = builder()
            .portfolio(b2.getPortfolioId())
            .currency(b2.getCurrency())
            .build();

        searchAndVerifyResults(search, b2);
    }

    @Test
    void shouldFindByNarrowingFilters() {
        RequestModel.PositionSearch search1 = builder()
            .currency("EUR")
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search1, b2, b4, b6);

        RequestModel.PositionSearch search2 = builder()
            .currency("EUR")
            .accountId(b6.getAccountId())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search2, b6);
    }

    @Test
    void shouldSortResults() {
        RequestModel.PositionSearch descSearch = builder()
            .sortingOrder(RequestModel.SortingOrder.DESC)
            .build();

        searchAndVerifyResults(descSearch, b6, b5, b4, b3, b2, b1);

        RequestModel.PositionSearch ascSearch = builder()
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(ascSearch, b1, b2, b3, b4, b5, b6);
    }

    @Test
    void shouldFindByAccountId() {
        RequestModel.PositionSearch search = builder()
            .accountId(b3.getAccountId())
            .build();

        searchAndVerifyResults(search, b3);
    }

    @Test
    void shouldFindByPortfolioId() {
        RequestModel.PositionSearch search = builder()
            .portfolio(b1.getPortfolioId())
            .build();

        searchAndVerifyResults(search, b1);
    }

    @Test
    void shouldFindByPortfolioAndAccountId() {
        RequestModel.PositionSearch search = builder()
            .portfolio(b2.getPortfolioId())
            .accountId(b3.getAccountId())
            .build();

        searchAndVerifyResults(search, b2, b3);
    }

    @Test
    void shouldPaginateResults() {
        RequestModel.PositionSearch search = builder()
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .first(3)
            .build();

        PaginationModel.CursorConnection<Balance> connection = lookup(search);

        assertThat(connection.pageInfo().hasNextPage()).isTrue();
        assertThat(connection.pageInfo().pageSize()).isEqualTo(3);
        String endCursor = connection.pageInfo().endCursor();
        assertThat(endCursor).isEqualTo(Long.toString(b3.getId()));
        verifyBalances(connection, b1, b2, b3);

        RequestModel.PositionSearch afterSearch = builder()
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .first(20)
            .after(endCursor)
            .build();

        PaginationModel.CursorConnection<Balance> afterConnection = lookup(afterSearch);

        assertThat(afterConnection.pageInfo().hasNextPage()).isFalse();
        assertThat(afterConnection.pageInfo().pageSize()).isEqualTo(3);
        assertThat(afterConnection.pageInfo().endCursor()).isEqualTo(Long.toString(b6.getId()));
        verifyBalances(afterConnection, b4, b5, b6);
    }

    @Test
    void shouldFindBySymbolAndPortfolio() {
        RequestModel.PositionSearch search = builder()
            .symbol(b1.getSymbol())
            .portfolio(b1.getPortfolioId())
            .build();

        searchAndVerifyResults(search, b1);
    }

    @Test
    void shouldFindBySymbolAndAccount() {
        RequestModel.PositionSearch search = builder()
            .symbol(b3.getSymbol())
            .accountId(b3.getAccountId())
            .build();

        searchAndVerifyResults(search, b3);
    }

    @Test
    void shouldFindByMultipleSymbols() {
        RequestModel.PositionSearch search = builder()
            .symbol(b1.getSymbol(), b2.getSymbol())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search, b1, b2);
    }

    @Test
    void shouldFindByMultipleCurrencies() {
        RequestModel.PositionSearch search = builder()
            .currency(b1.getCurrency(), b5.getCurrency())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search, b1, b5);
    }

    @ParameterizedTest
    @MethodSource("searchAllScenarios")
    void shouldReturnOnlyAuthorizedBalancesWithRequestAll(List<String> portfolioPermissions, List<String> accountPermissions, List<Balance> expectedBalances) {
        setupPermissions(portfolioPermissions, accountPermissions);

        RequestModel.PositionSearch search = RequestModel.PositionSearch.builder()
            .build();

        searchAndVerifyResults(search, expectedBalances.toArray(Balance[]::new));
    }

    private static Stream<Arguments> searchAllScenarios() {
        // set up is not called in a static context
        setupBalances();

        return Stream.of(
            arguments(List.of(b1.getPortfolioId()), NO_ACCESS, List.of(b1)),
            arguments(NO_ACCESS, List.of(b3.getAccountId()), List.of(b3)),
            arguments(List.of(b1.getPortfolioId(), b2.getPortfolioId()), List.of(b3.getAccountId()), List.of(b1, b2, b3)),
            arguments(List.of(b2.getPortfolioId(), b1.getPortfolioId()), NO_ACCESS, List.of(b1, b2)),
            arguments(NO_ACCESS, List.of(b3.getAccountId(), b4.getAccountId()), List.of(b3, b4)),

            // no permissions at all
            arguments(NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE)
        );
    }

    @ParameterizedTest
    @MethodSource("searchSpecificScenarios")
    void shouldReturnOnlyAuthorizedBalancesWithSpecificRequest(String searchPortfolio, String searchAccount, List<String> portfolioPermissions, List<String> accountPermissions, List<Balance> expectedBalances) {
        setupPermissions(portfolioPermissions, accountPermissions);

        RequestModel.PositionSearch search = RequestModel.PositionSearch.builder()
            .build();

        if (isNotBlank(searchPortfolio)) {
            search = search.toBuilder()
                .portfolio(searchPortfolio)
                .build();
        }

        if (isNotBlank(searchAccount)) {
            search = search.toBuilder()
                .accountId(searchAccount)
                .build();
        }

        searchAndVerifyResults(search, expectedBalances.toArray(Balance[]::new));
    }

    private static Stream<Arguments> searchSpecificScenarios() {
        // set up is not called in a static context
        setupBalances();

        return Stream.of(
            arguments(b1.getPortfolioId(), "", List.of(b1.getPortfolioId()), NO_ACCESS, List.of(b1)),
            arguments(b1.getPortfolioId(), "", NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            arguments("", b3.getAccountId(), NO_ACCESS, List.of(b3.getAccountId()), List.of(b3)),
            arguments("", b3.getAccountId(), NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            arguments(b1.getPortfolioId(), b3.getAccountId(), List.of(b1.getPortfolioId()), List.of(b3.getAccountId()), List.of(b1, b3)),
            arguments(b4.getPortfolioId(), b3.getAccountId(), NO_ACCESS, List.of(b3.getAccountId()), List.of(b3)),

            // search with non-existent ID
            arguments("other-id", "", NO_ACCESS, OTHER_ACCESS, EMPTY_RESPONSE)
        );
    }

    private PaginationModel.CursorConnection<Balance> lookup(RequestModel.PositionSearch search) {
        RequestModel.PositionSearch clientRequest = search.withClientId("client-id");

        PaginationModel.CursorConnection<Balance> connection = balanceQueryService.search(clientRequest);

        LOGGER.info("Connection response: {}", connection);

        if (connection.edges().isEmpty()) {
            LOGGER.info("No balances found");
        } else {
            List<String> symbols = connection.getAllNodes().stream()
                .map(Balance::getSymbol)
                .toList();

            LOGGER.info("Balances in response: {}", symbols);
        }

        return connection;
    }

    private void searchAndVerifyResults(RequestModel.PositionSearch search, Balance... expectedBalances) {
        PaginationModel.CursorConnection<Balance> connection = lookup(search);

        verifyBalances(connection, expectedBalances);
        verifyPageInfo(connection, expectedBalances.length);
    }

    private void searchAndVerifyEmptyResults(RequestModel.PositionSearch search) {
        PaginationModel.CursorConnection<Balance> connection = lookup(search);

        verifyBalances(connection);
        verifyPageInfo(connection, 0);
    }

    private static void verifyBalances(PaginationModel.CursorConnection<Balance> connection, Balance... expectedBalances) {
        String[] expectedSymbols = Arrays.stream(expectedBalances)
            .map(Balance::getSymbol)
            .toArray(String[]::new);

        assertThat(connection.getAllNodes())
            .extracting("symbol")
            .containsExactly(expectedSymbols);
    }

    private static void setupBalances() {
        b1 = createBalance(1)
            .portfolioId("portfolio-id-1")
            .symbol("b1")
            .currency("BTC")
            .quantity(BigDecimal.ONE)
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();

        b2 = createBalance(2)
            .portfolioId("portfolio-id-2")
            .symbol("b2")
            .currency("EUR")
            .quantity(bd(100_000))
            .portfolioType(RequestModel.PortfolioType.NOSTRO)
            .build();

        b3 = createBalance(3)
            .accountId("account-id-3")
            .symbol("b3")
            .currency("USD")
            .quantity(BigDecimal.ONE)
            .accountType(RequestModel.AccountType.WALLET)
            .accountWalletType(RequestModel.WalletType.VOSTRO)
            .build();

        b4 = createBalance(4)
            .accountId("account-id-4")
            .symbol("b4")
            .currency("EUR")
            .quantity(bd(10.5))
            .accountType(RequestModel.AccountType.WALLET)
            .accountWalletType(RequestModel.WalletType.NOSTRO)
            .build();

        b5 = createBalance(5)
            .accountId("account-id-5")
            .symbol("b5")
            .currency("USDT")
            .quantity(bd(50_000))
            .accountType(RequestModel.AccountType.EXCHANGE)
            .build();

        b6 = createBalance(6)
            .accountId("account-id-6")
            .symbol("b6")
            .currency("EUR")
            .quantity(bd(25_000))
            .accountType(RequestModel.AccountType.CLOB)
            .build();
    }

    private static Balance.Builder createBalance(long sequenceNumber) {
        return Balance.builder()
            .sequenceNumber(sequenceNumber)
            .lastAppliedLedgerEntryId(String.valueOf(1000 + sequenceNumber));
    }

    public Set<PermissionDto> setupPermissions() {
        return setupPermissions(b1, b2, b3, b4, b5, b6);
    }

    public Set<PermissionDto> setupPermissions(Balance... balances) {
        List<String> portfolioIds = Stream.of(balances)
            .map(Balance::getPortfolioId)
            .filter(StringUtils::isNotBlank)
            .toList();

        List<String> accountIds = Stream.of(balances)
            .map(Balance::getAccountId)
            .filter(StringUtils::isNotBlank)
            .toList();

        return setupPermissions(portfolioIds, accountIds);
    }
}