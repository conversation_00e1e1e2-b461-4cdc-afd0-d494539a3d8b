package io.wyden.booking.reporting.integration.producer;

import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.booking.reporting.domain.position.PositionRepository;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.published.booking.PositionSnapshot;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;

import static org.awaitility.Awaitility.await;

@Component
public class PositionSnapshotMessageProducer {

    private final RabbitExchange<PositionSnapshot> positionSnapshotRabbitExchange;
    private final PositionRepository positionRepository;

    public PositionSnapshotMessageProducer(RabbitIntegrator rabbitIntegrator, PositionRepository positionRepository) {
        this.positionSnapshotRabbitExchange = OemsExchange.Booking.declarePositionPnlCalculatedExchange(rabbitIntegrator);
        this.positionRepository = positionRepository;
    }

    public void emit(PositionSnapshot positionSnapshot) {
        positionSnapshotRabbitExchange.publishWithHeaders(positionSnapshot, Map.of());
    }

    public Position emitAndWait(PositionSnapshot positionSnapshot) {
        emit(positionSnapshot);

        return await()
            .atMost(Duration.ofSeconds(10))
            .pollInterval(Duration.ofSeconds(1))
            .until(() -> positionRepository.findBySymbolAndAccountIdOrPortfolioId(positionSnapshot.getSymbol(), positionSnapshot.getAccount(), positionSnapshot.getPortfolio())
                    .orElse(null),
                Objects::nonNull);
    }
}