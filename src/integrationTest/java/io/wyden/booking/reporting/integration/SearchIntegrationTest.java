package io.wyden.booking.reporting.integration;

import com.hazelcast.map.IMap;
import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.permissions.AccessGatewayMockClient;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.test.RandomStringUtils;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.WalletType;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.slf4j.LoggerFactory.getLogger;

public abstract class SearchIntegrationTest extends TestContainersIntegrationBase {

    private static final Logger LOGGER = getLogger(SearchIntegrationTest.class);

    public static final Collection<String> EMPTY_RESPONSE = List.of();
    public static final Collection<String> NO_ACCESS = List.of();
    public static final Collection<String> OTHER_ACCESS = List.of("other-id");

    @Autowired
    protected IMap<String, Portfolio> portfoliosMap;

    @Autowired
    protected IMap<String, VenueAccount> venueAccountMap;

    @Autowired
    protected AccessGatewayMockClient accessGatewayMockClient;

    protected <T extends Serializable> void verifyPageInfo(PaginationModel.CursorConnection<T> connection, int expectedCount) {
        assertThat(connection.pageInfo().pageSize()).isEqualTo(expectedCount);
        assertThat(connection.pageInfo().totalSize()).isEqualTo(expectedCount);
        assertThat(connection.pageInfo().hasNextPage()).isFalse();

        if (expectedCount > 0) {
            assertThat(connection.pageInfo().endCursor()).isNotBlank();
            assertThat(connection.edges()).hasSize(expectedCount);
        } else {
            assertThat(connection.pageInfo().endCursor()).isBlank();
            assertThat(connection.edges()).isEmpty();
        }
    }

    protected Set<PermissionDto> setupPermissions(Collection<String> portfolioIds, Collection<String> accountIds) {
        Set<PermissionDto> permissions = new HashSet<>();

        portfolioIds
            .forEach(portfolioId -> {
                save(createPortfolio(portfolioId));
                permissions.add(new PermissionDto("portfolio", "read", portfolioId));
            });

        accountIds
            .forEach(accountId -> {
                save(createAccount(accountId));
                permissions.add(new PermissionDto("venue.account", "read", accountId));
            });

        LOGGER.info("Granting permissions: {}", permissions);
        accessGatewayMockClient.grantPermissions(permissions);

        return permissions;
    }

    protected Portfolio.Builder createPortfolio(String portfolioId) {
        return Portfolio.newBuilder()
            .setPortfolioType(io.wyden.published.referencedata.PortfolioType.VOSTRO)
            .setPortfolioCurrency("USD")
            .setName(portfolioId)
            .setId(portfolioId);
    }

    protected void save(Portfolio.Builder portfolio) {
        portfoliosMap.put(portfolio.getId(), portfolio.build());
    }

    protected VenueAccount.Builder createAccount(String accountId) {
        return VenueAccount.newBuilder()
            .setId(accountId)
            .setAccountType(AccountType.WALLET)
            .setWalletType(WalletType.WALLET_VOSTRO)
            .setVenueName(RandomStringUtils.randomStringWithPrefix("venue"))
            .setVenueAccountName(RandomStringUtils.randomStringWithPrefix("account-"))
            .setId(accountId);
    }

    protected void save(VenueAccount.Builder account) {
        venueAccountMap.put(account.getId(), account.build());
    }
}
