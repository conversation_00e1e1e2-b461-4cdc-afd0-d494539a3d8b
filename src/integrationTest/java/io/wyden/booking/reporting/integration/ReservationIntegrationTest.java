package io.wyden.booking.reporting.integration;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.permissions.AccessGatewayMockClient;
import io.wyden.booking.reporting.domain.reservation.Reservation;
import io.wyden.booking.reporting.domain.reservation.ReservationRepository;
import io.wyden.booking.reporting.integration.producer.BookingCompletedMessageProducer;
import io.wyden.booking.reporting.interfaces.rabbitmq.BookingCompletedSequencedEventConsumer;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.ClientCashTradeReservationSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.ReservationSearch;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Set;

import static io.wyden.cloud.utils.test.RandomEnumUtils.randomEnum;
import static io.wyden.cloud.utils.test.RandomNumberUtils.randomProtoNumber;
import static io.wyden.cloud.utils.test.RandomStringUtils.randomUuid;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class ReservationIntegrationTest extends TestContainersIntegrationBase {

    private static final Logger LOGGER = getLogger(ReservationIntegrationTest.class);

    @Autowired
    private AccessGatewayMockClient accessGatewayMockClient;

    @Autowired
    private BookingCompletedMessageProducer bookingCompletedMessageProducer;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ReservationRepository reservationRepository;

    @Autowired
    private BookingCompletedSequencedEventConsumer bookingCompletedEventConsumer;

    private static long sequenceCounter = 0;

    @BeforeEach
    void setUp() {
        // Reset sequence number so that test messages are processed correctly
        bookingCompletedEventConsumer.resetSequenceNumber(sequenceCounter);
    }

    @Test
    void shouldPersistReservation() throws Exception {
        long sequenceNumber = ++sequenceCounter;

        emitAndWait(sequenceNumber);

        Collection<Reservation> reservations = reservationRepository.findAll();

        DB.logTableContent("reservation");

        LOGGER.info("Reservations found ({}): {}", reservations.size(), reservations);

        assertThat(reservations).isNotEmpty();
    }

    @Test
    void shouldQueryReservation() throws Exception {
        ReservationSnapshot reservation = emitAndWait(2);

        // Use MockMvc to test the REST endpoint
        MvcResult result = mockMvc.perform(get("/reservations/{reservationRef}", reservation.getClientCashTradeReservation().getReservationRef()))
            .andExpect(status().isOk())
            .andDo(print())
            .andReturn();

        // Parse the binary response
        byte[] content = result.getResponse().getContentAsByteArray();
        ReservationSnapshot retrievedReservation = ReservationSnapshot.parseFrom(content);

        LOGGER.info("Retrieved reservation: \n{}", retrievedReservation);

        // Verify the reservation
        ClientCashTradeReservationSnapshot actual = retrievedReservation.getClientCashTradeReservation();
        ClientCashTradeReservationSnapshot expected = reservation.getClientCashTradeReservation();

        validateReservation(actual, expected);
    }

    @Test
    void shouldReturn404WhenReservationIsNotFound() throws Exception {
        String nonExistentReservationRef = "non-existent-ref";

        mockMvc.perform(get("/reservations/{reservationRef}", nonExistentReservationRef))
            .andExpect(status().isNotFound())
            .andDo(print());
    }

    @Test
    void shouldSearchTransaction() throws Exception {
        // setup permissions
        Set<PermissionDto> permissions = Set.of(
            new PermissionDto("portfolio", "read", "client-a"),
            new PermissionDto("venue.account", "read", "bitmex-testnet1")
        );
        accessGatewayMockClient.grantPermissions(permissions);

        emitAndWait(3);
        emitAndWait(4);
        emitAndWait(5);
        ReservationSnapshot reservation = emitAndWait(6);

        String reservationRef = reservation.getClientCashTradeReservation().getReservationRef();

        // Create search request
        ReservationSearch searchRequest = ReservationSearch.newBuilder()
            .setReservationRef(reservationRef)
            .setClientId("client-a")
            .setFirst(10)
            .build();

        // Send search request and get response
        MvcResult result = mockMvc.perform(post("/reservations/search?version=proto")
                .contentType("application/x-protobuf")
                .content(searchRequest.toByteArray()))
            .andExpect(status().isOk())
            .andDo(print())
            .andReturn();

        // Parse the binary response
        byte[] content = result.getResponse().getContentAsByteArray();
        CursorConnection connection = CursorConnection.parseFrom(content);

        LOGGER.info("Search response: \n{}", connection);

        // Verify the connection has edges
        assertThat(connection.getEdgesCount()).isPositive();

        // Find our reservation in the results
        boolean foundReservation = false;
        for (CursorEdge edge : connection.getEdgesList()) {
            CursorNode node = edge.getNode();
            if (node.hasReservation()) {
                ReservationSnapshot foundTx = node.getReservation();
                if (foundTx.hasClientCashTradeReservation()) {

                    foundReservation = true;

                    ClientCashTradeReservationSnapshot actual = foundTx.getClientCashTradeReservation();
                    ClientCashTradeReservationSnapshot expected = reservation.getClientCashTradeReservation();

                    validateReservation(actual, expected);

                    break;
                }
            }
        }

        assertThat(foundReservation).isTrue();

        // Test searching by portfolio
        String portfolio = reservation.getClientCashTradeReservation().getPortfolioId();
        ReservationSearch portfolioSearch = ReservationSearch.newBuilder()
            .addPortfolioId(portfolio)
            .setFirst(2)
            .build();

        result = mockMvc.perform(post("/reservations/search?version=proto")
                .contentType("application/x-protobuf")
                .content(portfolioSearch.toByteArray()))
            .andExpect(status().isOk())
            .andReturn();

        // Parse and verify portfolio search results
        content = result.getResponse().getContentAsByteArray();
        connection = CursorConnection.parseFrom(content);

        assertThat(connection.getEdgesCount()).isPositive();
        assertThat(connection.getPageInfo().getTotalSize()).isPositive();
    }

    private static ReservationSnapshot createReservation(long sequenceNumber) {
        return ReservationSnapshot.newBuilder()
            .setUuid(randomUuid())
            .setClientCashTradeReservation(ClientCashTradeReservationSnapshot.newBuilder()
                .setReservationRef("reservation-ref-" + sequenceNumber)
                .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setQuantity(randomProtoNumber())
                .setPrice(randomProtoNumber(60_000, 2))
                .setStopPrice(randomProtoNumber(59_000, 2))
                .setBaseCurrency("BTC")
                .setCurrency("EUR")
                .setPortfolioId("client-a")
                .setCounterPortfolioId("nostro-a")
                .addReservationFee(Fee.newBuilder()
                    .setAmount(randomProtoNumber(10, 2))
                    .setCurrency("EUR")
                    .setFeeType(randomEnum(FeeType.class, FeeType.FEE_TYPE_UNSPECIFIED, FeeType.UNRECOGNIZED))
                    .build())
                .addReservationFee(Fee.newBuilder()
                    .setAmount(randomProtoNumber(100, 4))
                    .setCurrency("DOGE")
                    .setFeeType(FeeType.FIXED_FEE)
                    .build())
                .build())
            .build();
    }

    private ReservationSnapshot emitAndWait(long sequenceNumber) {
        ReservationSnapshot reservation = createReservation(sequenceNumber);

        BookingCompleted bookingCompleted = BookingCompleted.newBuilder()
            .setSequenceNumber(sequenceNumber)
            .setReservationSnapshot(reservation)
            .build();

        bookingCompletedMessageProducer.emit(bookingCompleted);

        await("Waiting for reservation to be persisted")
            .until(() -> !reservationRepository.findBySequenceNumber(sequenceNumber).isEmpty());

        return reservation;
    }

    private static void validateReservation(ClientCashTradeReservationSnapshot actual, ClientCashTradeReservationSnapshot expected) {
        assertThat(actual.getReservationRef()).isEqualTo(expected.getReservationRef());
        assertThat(actual.getDateTime()).isEqualTo(expected.getDateTime());
        assertThat(actual.getQuantity()).isEqualTo(expected.getQuantity());
        assertThat(actual.getPrice()).isEqualTo(expected.getPrice());
        assertThat(actual.getStopPrice()).isEqualTo(expected.getStopPrice());
        assertThat(actual.getCurrency()).isEqualTo(expected.getCurrency());
        assertThat(actual.getBaseCurrency()).isEqualTo(expected.getBaseCurrency());
        assertThat(actual.getPortfolioId()).isEqualTo(expected.getPortfolioId());
        assertThat(actual.getCounterPortfolioId()).isEqualTo(expected.getCounterPortfolioId());

        // Verify reservation fees
        assertThat(actual.getReservationFeeList()).hasSameSizeAs(expected.getReservationFeeList());

        for (int i = 0; i < expected.getReservationFeeList().size(); i++) {
            Fee actualFee = actual.getReservationFee(i);
            Fee expectedFee = expected.getReservationFee(i);

            assertThat(actualFee.getAmount()).isEqualTo(expectedFee.getAmount());
            assertThat(actualFee.getCurrency()).isEqualTo(expectedFee.getCurrency());
            assertThat(actualFee.getFeeType()).isEqualTo(expectedFee.getFeeType());
        }
    }
}