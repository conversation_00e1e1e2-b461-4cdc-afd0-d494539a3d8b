package io.wyden.booking.reporting.integration;

import io.wyden.booking.reporting.application.position.PositionCommandService;
import io.wyden.booking.reporting.application.recovery.PositionSnapshotGapRecoveryService;
import io.wyden.booking.reporting.application.recovery.PositionSnapshotRecoveryClient;
import io.wyden.booking.reporting.application.recovery.PositionSnapshotRecoveryResult;
import io.wyden.booking.reporting.domain.position.PositionRepository;
import io.wyden.booking.reporting.domain.tracking.EventTracking;
import io.wyden.booking.reporting.domain.tracking.EventTrackingRepository;
import io.wyden.booking.reporting.integration.producer.PositionSnapshotMessageProducer;
import io.wyden.booking.reporting.interfaces.rabbitmq.PositionSnapshotSequencedEventConsumer;
import io.wyden.published.booking.PositionSnapshot;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.annotation.DirtiesContext;

import java.time.Duration;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.slf4j.LoggerFactory.getLogger;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class PositionSnapshotGapRecoveryIntegrationTest extends SearchIntegrationTest {

    private static final Logger LOGGER = getLogger(PositionSnapshotGapRecoveryIntegrationTest.class);

    @Autowired
    private PositionRepository positionRepository;

    @Autowired
    private EventTrackingRepository trackingRepository;
    
    private static final String EVENT_TYPE = "position_snapshot";

    @Autowired
    private PositionSnapshotSequencedEventConsumer positionSnapshotEventConsumer;

    @Autowired
    private PositionSnapshotGapRecoveryService gapRecoveryService;

    @SpyBean
    private PositionCommandService positionCommandService;

    @MockBean
    private PositionSnapshotRecoveryClient recoveryClient;

    private PositionSnapshotMessageProducer positionSnapshotMessageProducer;
    private long sequenceCounter;

    @BeforeEach
    void setUp() {
        // Use unique sequence counter for each test to avoid interference
        sequenceCounter = System.currentTimeMillis() % 100000; // Use timestamp to ensure uniqueness
        
        positionSnapshotMessageProducer = new PositionSnapshotMessageProducer(rabbitIntegrator, positionRepository);
        
        // Clean up and reset tracking table for this test
        trackingRepository.deleteAll();
        EventTracking tracking = new EventTracking(EVENT_TYPE, sequenceCounter);
        trackingRepository.save(tracking);
        
        // Reset sequence number to current counter so next test gets sequenceCounter + 1
        positionSnapshotEventConsumer.resetSequenceNumber(sequenceCounter);
    }

    @Test
    void shouldDetectGapAndRecoverMissingEvents() {
        // Given: Mock the recovery client to return gap events
        long expectedSequence = sequenceCounter + 1;
        long gapStartSequence = expectedSequence;
        long gapEndSequence = expectedSequence + 2; // Gap of 3 events: expectedSequence, expectedSequence+1, expectedSequence+2
        long receivedSequence = expectedSequence + 4; // Jump to +4, creating a gap
        
        PositionSnapshot gapEvent1 = createPositionSnapshot("AAPL", gapStartSequence);
        PositionSnapshot gapEvent2 = createPositionSnapshot("AAPL", gapStartSequence + 1);
        PositionSnapshot gapEvent3 = createPositionSnapshot("AAPL", gapEndSequence);
        
        // Mock recovery client to return the gap events
        when(recoveryClient.fetchPositionSnapshotEventsAfter(sequenceCounter))
            .thenReturn(new PositionSnapshotRecoveryResult(
                List.of(gapEvent1, gapEvent2, gapEvent3), false));

        // When: Send a message with a gap (jumping sequence numbers)
        PositionSnapshot realtimeEvent = createPositionSnapshot("AAPL", receivedSequence);
        positionSnapshotMessageProducer.emit(realtimeEvent);

        // Then: Verify that gap recovery was triggered and sequence tracking was updated
        await().atMost(Duration.ofSeconds(5))
            .untilAsserted(() -> {
                // Verify the tracking was updated to the latest sequence (even if events failed processing)
                Long lastProcessedSequence = trackingRepository.findLastProcessedSequence(EVENT_TYPE).orElse(0L);
                assertThat(lastProcessedSequence).isEqualTo(receivedSequence);
            });

        LOGGER.info("Gap recovery integration test completed successfully");
    }

    @Test
    void shouldProcessEventsWithoutGapWhenSequencesAreConsecutive() {
        // Given: No gap - consecutive sequences
        long nextSequence = sequenceCounter + 1;
        
        PositionSnapshot event1 = createPositionSnapshot("MSFT", nextSequence);
        PositionSnapshot event2 = createPositionSnapshot("MSFT", nextSequence + 1);
        
        // When: Send consecutive events
        positionSnapshotMessageProducer.emit(event1);
        positionSnapshotMessageProducer.emit(event2);

        // Then: Both events should be processed without gap recovery, sequence tracking updated
        await().atMost(Duration.ofSeconds(5))
            .untilAsserted(() -> {
                Long lastProcessedSequence = trackingRepository.findLastProcessedSequence(EVENT_TYPE).orElse(0L);
                assertThat(lastProcessedSequence).isEqualTo(nextSequence + 1);
            });

        LOGGER.info("Consecutive sequence processing test completed successfully");
    }

    @Test
    void shouldSkipAlreadyProcessedEvents() {
        // Given: Process an event first
        long currentSequence = sequenceCounter + 1;
        PositionSnapshot initialEvent = createPositionSnapshot("GOOGL", currentSequence);
        positionSnapshotMessageProducer.emit(initialEvent);

        // Wait for initial event to be processed
        await().atMost(Duration.ofSeconds(5))
            .untilAsserted(() -> {
                Long lastProcessedSequence = trackingRepository.findLastProcessedSequence(EVENT_TYPE).orElse(0L);
                assertThat(lastProcessedSequence).isEqualTo(currentSequence);
            });

        // When: Send an older event (lower sequence number)
        PositionSnapshot olderEvent = createPositionSnapshot("GOOGL", currentSequence - 1);
        positionSnapshotMessageProducer.emit(olderEvent);

        // Then: Sequence should remain the same (older event skipped)
        await().atMost(Duration.ofSeconds(2))
            .untilAsserted(() -> {
                Long lastProcessedSequence = trackingRepository.findLastProcessedSequence(EVENT_TYPE).orElse(0L);
                assertThat(lastProcessedSequence).isEqualTo(currentSequence);
            });

        LOGGER.info("Older event skipping test completed successfully");
    }

    @Test
    void shouldAllowSameSequenceNumberEvents() {
        reset(positionCommandService);

        // Given: Process an event first
        long currentSequence = sequenceCounter + 1;
        PositionSnapshot initialEvent = createPositionSnapshot("TSLA", currentSequence);
        positionSnapshotMessageProducer.emit(initialEvent);

        // Wait for initial event to be processed
        await().atMost(Duration.ofSeconds(5))
            .untilAsserted(() -> {
                Long lastProcessedSequence = trackingRepository.findLastProcessedSequence(EVENT_TYPE).orElse(0L);
                assertThat(lastProcessedSequence).isEqualTo(currentSequence);
            });

        // When: Send the same sequence number again (duplicate sequence number)
        PositionSnapshot duplicateSequenceEvent = createPositionSnapshot("TSLA", currentSequence);
        positionSnapshotMessageProducer.emit(duplicateSequenceEvent);

        // Then: Sequence should remain the same but event is processed (not skipped)
        await().atMost(Duration.ofSeconds(2))
            .untilAsserted(() -> {
                Long lastProcessedSequence = trackingRepository.findLastProcessedSequence(EVENT_TYPE).orElse(0L);
                assertThat(lastProcessedSequence).isEqualTo(currentSequence);

                verify(positionCommandService, times(2)).handlePositionSnapshot(any());
            });

        LOGGER.info("Same sequence number handling test completed successfully");
    }

    @Test
    void shouldHandleStartupRecoveryScenario() {
        // Given: Mock startup recovery to return some events
        PositionSnapshot startupEvent1 = createPositionSnapshot("TESLA", sequenceCounter + 1);
        PositionSnapshot startupEvent2 = createPositionSnapshot("TESLA", sequenceCounter + 2);
        
        when(recoveryClient.fetchPositionSnapshotEventsAfter(anyLong()))
            .thenReturn(new PositionSnapshotRecoveryResult(
                List.of(startupEvent1, startupEvent2), false));

        // When: Trigger startup recovery directly
        List<PositionSnapshot> recoveredEvents = gapRecoveryService.recoverAllMissingEvents(sequenceCounter);

        // Then: Should return the mocked events
        assertThat(recoveredEvents).hasSize(2);
        assertThat(recoveredEvents.get(0).getSequenceNumber()).isEqualTo(sequenceCounter + 1);
        assertThat(recoveredEvents.get(1).getSequenceNumber()).isEqualTo(sequenceCounter + 2);

        LOGGER.info("Startup recovery test completed successfully");
    }

    private static PositionSnapshot createPositionSnapshot(String symbol, long sequenceNumber) {
        return PositionSnapshot.newBuilder()
            .setSequenceNumber(sequenceNumber)
            .setAccount("account1")
            .setCurrency("USD")
            .setSymbol(symbol)
            .setQuantity("50.75")
            .setBookingCurrency("USD")
            .setNetRealizedPnl("125.50")
            .setGrossRealizedPnl("130.00")
            .setNetCost("1000.00")
            .setGrossCost("1005.00")
            .setNotionalQuantity("50.75")
            .setMarketValue("1150.25")
            .setMarketValueSc("1150.25")
            .setNetRealizedPnlSc("125.50")
            .setGrossRealizedPnlSc("130.00")
            .setNetCostSc("1000.00")
            .setGrossCostSc("1005.00")
            .build();
    }
}