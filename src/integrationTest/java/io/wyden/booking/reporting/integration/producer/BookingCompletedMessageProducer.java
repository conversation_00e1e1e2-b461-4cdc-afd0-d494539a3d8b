package io.wyden.booking.reporting.integration.producer;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.published.booking.BookingCompleted;

import java.util.Map;

public class BookingCompletedMessageProducer {

    private final RabbitExchange<BookingCompleted> bookingCompletedRabbitExchange;

    public BookingCompletedMessageProducer(RabbitIntegrator rabbitIntegrator) {
        this.bookingCompletedRabbitExchange = OemsExchange.Booking.declareBookingCompletedExchange(rabbitIntegrator);
    }

    public void emit(BookingCompleted bookingCompleted) {
        bookingCompletedRabbitExchange.publishWithHeaders(bookingCompleted, Map.of());
    }
}
