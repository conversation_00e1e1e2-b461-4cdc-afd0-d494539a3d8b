package io.wyden.booking.reporting.integration;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.ReservationService;
import io.wyden.booking.reporting.domain.reservation.Reservation;
import io.wyden.booking.reporting.domain.reservation.ReservationRepository;
import io.wyden.booking.reporting.domain.transaction.TransactionFee;
import io.wyden.booking.reporting.domain.transaction.TransactionFeeType;
import io.wyden.booking.reporting.domain.transaction.TransactionType;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.RequestModel.AccountType;
import io.wyden.booking.reporting.interfaces.rest.RequestModel.PortfolioType;
import io.wyden.booking.reporting.interfaces.rest.RequestModel.WalletType;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.test.RandomEnumUtils;
import io.wyden.cloud.utils.test.RandomNumberUtils;
import io.wyden.cloud.utils.test.RandomStringUtils;
import io.wyden.cloudutils.tools.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static io.wyden.booking.reporting.interfaces.rest.RequestModel.ReservationSearch.builder;
import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.slf4j.LoggerFactory.getLogger;

public class ReservationSearchIntegrationTest extends SearchIntegrationTest {

    private static final Logger LOGGER = getLogger(ReservationSearchIntegrationTest.class);

    @Autowired
    private ReservationRepository reservationRepository;

    @Autowired
    private ReservationService reservationService;

    private static Reservation r1;
    private static Reservation r2;
    private static Reservation r3;
    private static Reservation r4;
    private static Reservation r5;
    private static Reservation r6;
    private static Reservation r7;
    private static Reservation r8;
    private static Reservation r9;
    private static Reservation r10;

    @BeforeEach
    void setUp() {
        setupReservations();
        setupPermissions();

        // setupTransactions has to be static, because it is used in @ParametrizedTest
        // saving in repository has to happen in non-static block
        reservationRepository.save(r1);
        reservationRepository.save(r2);
        reservationRepository.save(r3);
        reservationRepository.save(r4);
        reservationRepository.save(r5);
        reservationRepository.save(r6);
        reservationRepository.save(r7);
        reservationRepository.save(r8);
        reservationRepository.save(r9);
        reservationRepository.save(r10);
    }

    @AfterEach
    void tearDown() throws Exception {
        DB.clearTable("reservation");
    }

    @Test
    void shouldReturnNothing() throws Exception {
        DB.clearTable("reservation");

        RequestModel.ReservationSearch search = builder().build();

        searchAndVerifyEmptyResults(search);
    }

    @Test
    void shouldReturnAll() {
        RequestModel.ReservationSearch search = builder().build();

        searchAndVerifyResults(search, r10, r9, r8, r7, r6, r5, r4, r3, r2, r1);
    }

    @Test
    void shouldFindBySymbol() {
        RequestModel.ReservationSearch search = builder()
            .symbol("DOGE", "BTC/USD")
            .build();

        searchAndVerifyResults(search, r7, r4, r3);
    }

    @Test
    void shouldFindByCurrency() {
        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .currency("DOGE", "BTC")
            .build();

        searchAndVerifyResults(search, r7, r2, r1);
    }

    @Test
    void shouldFindByTransactionType() {
        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .transactionType(r10.getTransactionType())
            .build();

        searchAndVerifyResults(search, r10);
    }

    @Test
    void shouldFindByReservationRef() {
        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .reservationRef(r6.getReservationRef())
            .build();

        searchAndVerifyResults(search, r6);
    }

    @Test
    void shouldFindByDateSingleTransaction() {
        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .dateRange(r1.getDateTime(), r2.getDateTime())
            .build();

        searchAndVerifyResults(search, r1);
    }

    @Test
    void shouldFindByDateMultipleTransactions() {
        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .dateRange(r1.getDateTime(), r10.getDateTime())
            .build();

        searchAndVerifyResults(search, r9, r8, r7, r6, r5, r4, r3, r2, r1);
    }

    @Test
    void shouldFindByDateNoTransactions() {
        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .dateRange(r1.getDateTime().plusSeconds(1), r2.getDateTime())
            .build();

        searchAndVerifyEmptyResults(search);
    }

    @Test
    void shouldFindByMultipleFilters() {
        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .portfolio(r2.getPortfolioId())
            .accountId(r2.getAccountId())
            .currency(r2.getBaseCurrency())
            .symbol(r2.getCurrency())
            .build();

        searchAndVerifyResults(search, r2);
    }

    @Test
    void shouldFindByNarrowingFilters() {
        RequestModel.ReservationSearch search1 = RequestModel.ReservationSearch.builder()
            .currency("USD")
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search1, r1, r2, r3, r4, r5, r10);

        RequestModel.ReservationSearch search2 = RequestModel.ReservationSearch.builder()
            .currency("USD")
            .dateRange(r1.getDateTime(), r4.getDateTime())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search2, r1, r2, r3);

        RequestModel.ReservationSearch search3 = RequestModel.ReservationSearch.builder()
            .currency("USD")
            .dateRange(r1.getDateTime(), r4.getDateTime())
            .portfolio(r3.getPortfolioId())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search3, r3);
    }

    @Test
    void shouldSortResults() {
        RequestModel.ReservationSearch descSearch = RequestModel.ReservationSearch.builder()
            .sortingOrder(RequestModel.SortingOrder.DESC)
            .build();

        searchAndVerifyResults(descSearch, r10, r9, r8, r7, r6, r5, r4, r3, r2, r1);

        RequestModel.ReservationSearch ascSearch = RequestModel.ReservationSearch.builder()
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(ascSearch, r1, r2, r3, r4, r5, r6, r7, r8, r9, r10);
    }

    @Test
    void shouldFindByAccountId() {
        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .accountId(r2.getAccountId())
            .build();

        searchAndVerifyResults(search, r2);
    }

    @Test
    void shouldFindByPortfolioId() {
        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .portfolio(r1.getPortfolioId())
            .build();

        searchAndVerifyResults(search, r1);
    }

    @Test
    void shouldFindByPortfolioAndAccountId() {
        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .portfolio(r2.getPortfolioId())
            .accountId(r2.getAccountId())
            .build();

        searchAndVerifyResults(search, r2);
    }

    @Test
    void shouldPaginateResults() {
        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .currency("USD")
            .sortingOrder(RequestModel.SortingOrder.DESC)
            .first(3)
            .build();

        PaginationModel.CursorConnection<Reservation> connection = lookup(search);

        assertThat(connection.pageInfo().hasNextPage()).isTrue();
        assertThat(connection.pageInfo().pageSize()).isEqualTo(3);
        String endCursor = connection.pageInfo().endCursor();
        assertThat(endCursor).isEqualTo(Long.toString(r4.getId()));
        verifyReservations(connection, r10, r5, r4);

        RequestModel.ReservationSearch afterSearch = RequestModel.ReservationSearch.builder()
            .currency("USD")
            .sortingOrder(RequestModel.SortingOrder.DESC)
            .first(3)
            .after(endCursor)
            .build();

        PaginationModel.CursorConnection<Reservation> afterConnection = lookup(afterSearch);

        assertThat(afterConnection.pageInfo().hasNextPage()).isFalse();
        assertThat(afterConnection.pageInfo().pageSize()).isEqualTo(3);
        assertThat(afterConnection.pageInfo().endCursor()).isEqualTo(Long.toString(r1.getId()));
        verifyReservations(afterConnection, r3, r2, r1);
    }

    @Test
    void shouldReturnNothingWhenNoAccess() {
        accessGatewayMockClient.revokePermissions();

        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .build();

        searchAndVerifyEmptyResults(search);
    }

    @Test
    void shouldReturnAuthorizedTransactionsWithStaticPortfolioAccess() {
        accessGatewayMockClient.revokePermissions();
        accessGatewayMockClient.grantStaticPortfolioPermission("read");

        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .build();

        // transactions between portfolios (client trades, portfolio transfers)
        searchAndVerifyResults(search, r8, r7, r3, r1);
    }

    @Test
    void shouldReturnAuthorizedTransactionsWithStaticAccountAccess() {
        accessGatewayMockClient.revokePermissions();
        accessGatewayMockClient.grantStaticVenueAccountPermission("read");

        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .build();

        // transactions between accounts (account transfers)
        searchAndVerifyResults(search, r9);
    }

    @Test
    void shouldReturnAuthorizedTransactionsWithStaticPortfolioAndAccountAccess() {
        accessGatewayMockClient.revokePermissions();
        accessGatewayMockClient.grantStaticPortfolioPermission("read");
        accessGatewayMockClient.grantStaticVenueAccountPermission("read");

        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .build();

        // all transactions
        searchAndVerifyResults(search, r10, r9, r8, r7, r6, r5, r4, r3, r2, r1);
    }

    @Test
    void shouldReturnSingleTransactionWithStaticPortfolioAccess() {
        accessGatewayMockClient.revokePermissions();
        accessGatewayMockClient.grantStaticPortfolioPermission("read");

        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .portfolio(r1.getPortfolioId())
            .build();

        searchAndVerifyResults(search, r1);
    }

    @Test
    void shouldReturnSingleTransactionWithStaticAccountAccess() {
        // grant access to single portoflio
        setupPermissions(List.of(r2.getPortfolioId()), List.of());

        // grant access to all accounts
        accessGatewayMockClient.grantStaticVenueAccountPermission("read");

        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .accountId(r2.getAccountId())
            .build();

        searchAndVerifyResults(search, r2);
    }

    @ParameterizedTest
    @MethodSource("searchAllScenarios")
    void shouldReturnOnlyAuthorizedReservationsWithRequestAll(List<String> portfolioPermissions, List<String> accountPermissions, List<Reservation> expectedReservations) {
        setupPermissions(portfolioPermissions, accountPermissions);

        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .build();

        searchAndVerifyResults(search, expectedReservations.toArray(Reservation[]::new));
    }

    private static Stream<Arguments> searchAllScenarios() {
        // set up is not called in a static context
        setupReservations();

        return Stream.of(
            // client trade
            arguments(List.of(r1.getPortfolioId()), NO_ACCESS, List.of(r1)),

            // client trade (missing portfolio or counter portfolio)
            arguments(NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            // street trade
            arguments(List.of(r2.getPortfolioId()), List.of(r2.getAccountId()), List.of(r2)),

            // street trade (missing portfolio)
            arguments(NO_ACCESS, List.of(r2.getAccountId()), EMPTY_RESPONSE),

            // payment (deposit / withdrawal)
            arguments(List.of(r5.getPortfolioId()), List.of(r5.getAccountId()), List.of(r5)),

            // payment (missing account)
            arguments(List.of(r5.getPortfolioId()), NO_ACCESS, EMPTY_RESPONSE),

            // portfolio transfer
            arguments(List.of(r7.getTargetPortfolioId()), NO_ACCESS, List.of(r7)),

            // portfolio transfer (missing source or target portfolio)
            arguments(NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            // account transfer
            arguments(List.of(r9.getFeePortfolioId()), List.of(r9.getSourceAccountId(), r9.getTargetAccountId()), List.of(r9)),

            // account transfer (missing source account)
            arguments(List.of(r9.getFeePortfolioId()), List.of(r9.getTargetAccountId()), EMPTY_RESPONSE),

            // fee
            arguments(List.of(r10.getPortfolioId()), List.of(r10.getAccountId()), List.of(r10)),

            // fee (missing account)
            arguments(List.of(r10.getPortfolioId()), NO_ACCESS, EMPTY_RESPONSE),

            // settlement (has no reference to portfolio nor account, so it is always accessible as long as you have any permissions)
            arguments(NO_ACCESS, List.of("other-account"), EMPTY_RESPONSE)
        );
    }

    @ParameterizedTest
    @MethodSource("searchSpecificScenarios")
    void shouldReturnOnlyAuthorizedReservationsWithSpecificRequest(String searchPortfolio, String searchAccount, List<String> portfolioPermissions, List<String> accountPermissions, List<Reservation> expectedReservations) {
        setupPermissions(portfolioPermissions, accountPermissions);

        RequestModel.ReservationSearch search = RequestModel.ReservationSearch.builder()
            .build();

        if (isNotBlank(searchPortfolio)) {
            search = search.toBuilder()
                .portfolio(searchPortfolio)
                .build();
        }

        if (isNotBlank(searchAccount)) {
            search = search.toBuilder()
                .accountId(searchAccount)
                .build();
        }

        searchAndVerifyResults(search, expectedReservations.toArray(Reservation[]::new));
    }

    private static Stream<Arguments> searchSpecificScenarios() {
        // set up is not called in a static context
        setupReservations();

        return Stream.of(
            // client trade by portfolioId
            arguments(r1.getCounterPortfolioId(), "", List.of(r1.getCounterPortfolioId()), NO_ACCESS, List.of(r1)),

            // client trade (missing portfolio or counter-portfolio)
            arguments(r1.getPortfolioId(), "", NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            // street trade by accountId
            arguments("", r2.getAccountId(), List.of(r2.getPortfolioId()), List.of(r2.getAccountId()), List.of(r2)),

            // street trade (missing portfolio)
            arguments("", r2.getAccountId(), NO_ACCESS, List.of(r2.getAccountId()), EMPTY_RESPONSE),

            // payment (deposit / withdrawal) by portfolioId
            arguments(r5.getPortfolioId(), "", List.of(r5.getPortfolioId()), List.of(r5.getAccountId()), List.of(r5)),

            // payment (missing account)
            arguments(r5.getPortfolioId(), "", List.of(r5.getPortfolioId()), NO_ACCESS, EMPTY_RESPONSE),

            // portfolio transfer by sourcePortfolioId
            arguments(r7.getSourcePortfolioId(), "", List.of(r7.getSourcePortfolioId(), r7.getTargetPortfolioId()), NO_ACCESS, List.of(r7)),

            // portfolio transfer (missing source or target portfolio)
            arguments(r7.getSourcePortfolioId(), "", NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            // account transfer by targetAccountId
            arguments("", r9.getTargetAccountId(), NO_ACCESS, List.of(r9.getSourceAccountId(), r9.getTargetAccountId()), List.of(r9)),

            // account transfer (missing source account)
            arguments("", r9.getTargetAccountId(), NO_ACCESS, List.of(r9.getTargetAccountId()), EMPTY_RESPONSE),

            // fee by portfolioId
            arguments(r10.getPortfolioId(), "", List.of(r10.getPortfolioId()), List.of(r10.getAccountId()), List.of(r10)),

            // fee (missing account)
            arguments(r10.getPortfolioId(), "", List.of(r10.getPortfolioId()), NO_ACCESS, EMPTY_RESPONSE),

            // settlement (has no reference to portfolio nor account, so it is always accessible as long as you have any permissions)
            arguments("other-id", "", NO_ACCESS, OTHER_ACCESS, EMPTY_RESPONSE)
        );
    }

    private PaginationModel.CursorConnection<Reservation> lookup(RequestModel.ReservationSearch search) {
        RequestModel.ReservationSearch clientRequest = search.toBuilder()
            .clientId("client-id")
            .build();

        PaginationModel.CursorConnection<Reservation> connection = reservationService.search(clientRequest);

        LOGGER.info("Connection response: {}", connection);

        if (connection.edges().isEmpty()) {
            LOGGER.info("No reservations found");
        } else {
            List<String> reservationRefs = connection.getAllNodes().stream()
                .map(Reservation::getReservationRef)
                .toList();

            LOGGER.info("Reservations in response: {}", reservationRefs);
        }

        return connection;
    }

    private void searchAndVerifyResults(RequestModel.ReservationSearch search, Reservation... expectedReservations) {
        PaginationModel.CursorConnection<Reservation> connection = lookup(search);

        verifyReservations(connection, expectedReservations);
        verifyPageInfo(connection, expectedReservations.length);
    }

    private void searchAndVerifyEmptyResults(RequestModel.ReservationSearch search) {
        PaginationModel.CursorConnection<Reservation> connection = lookup(search);

        verifyReservations(connection);
        verifyPageInfo(connection, 0);
    }

    private static void verifyReservations(PaginationModel.CursorConnection<Reservation> connection, Reservation... expectedReservations) {
        String[] expectedRefs = Arrays.stream(expectedReservations)
            .map(Reservation::getReservationRef)
            .toArray(String[]::new);

        assertThat(connection.getAllNodes())
            .extracting("reservationRef")
            .containsExactly(expectedRefs);
    }

    private static void setupReservations() {
        r1 = createReservation(1)
            .reservationRef("r1")
            .portfolioId("portfolio-1")
            .counterPortfolioId("counter-portfolio-1")
            .baseCurrency("BTC")
            .currency("USD")
            .quantity(bd(0.0001))
            .price(bd(60_000))
            .stopPrice(bd(61_000))
            .transactionType(TransactionType.CLIENT_CASH_TRADE)
            .portfolioType(PortfolioType.VOSTRO)
            .counterPortfolioType(PortfolioType.NOSTRO)
            .build();

        r2 = createReservation(2)
            .reservationRef("r2")
            .portfolioId("portfolio-2")
            .accountId("account-2")
            .baseCurrency("BTC")
            .currency("USD")
            .quantity(bd(0.0001))
            .price(bd(60_000))
            .stopPrice(bd(62_000))
            .transactionType(TransactionType.STREET_CASH_TRADE)
            .portfolioType(PortfolioType.VOSTRO)
            .accountType(AccountType.EXCHANGE)
            .build();

        r3 = createReservation(3)
            .reservationRef("r3")
            .portfolioId("portfolio-3")
            .counterPortfolioId("counter-portfolio-3")
            .asset("BTC/USD")
            .currency("USD")
            .quantity(bd(0.0001))
            .price(bd(60_000))
            .stopPrice(bd(63_000))
            .transactionType(TransactionType.CLIENT_ASSET_TRADE)
            .portfolioType(PortfolioType.VOSTRO)
            .counterPortfolioType(PortfolioType.NOSTRO)
            .build();

        r4 = createReservation(4)
            .reservationRef("r4")
            .portfolioId("portfolio-4")
            .accountId("account-4")
            .asset("BTC/USD")
            .currency("USD")
            .quantity(bd(0.0001))
            .price(bd(60_000))
            .stopPrice(bd(64_000))
            .transactionType(TransactionType.STREET_ASSET_TRADE)
            .portfolioType(PortfolioType.VOSTRO)
            .accountType(AccountType.EXCHANGE)
            .build();

        r5 = createReservation(5)
            .reservationRef("r5")
            .portfolioId("portfolio-5")
            .accountId("account-5")
            .feePortfolioId("fee-portfolio-5")
            .feeAccountId("fee-account-5")
            .currency("USD")
            .quantity(bd(1_000))
            .transactionType(TransactionType.DEPOSIT)
            .portfolioType(PortfolioType.VOSTRO)
            .accountType(AccountType.WALLET)
            .accountWalletType(WalletType.VOSTRO)
            .feePortfolioType(PortfolioType.VOSTRO)
            .feeAccountType(AccountType.WALLET)
            .feeAccountWalletType(WalletType.VOSTRO)
            .build();

        r6 = createReservation(6)
            .reservationRef("r6")
            .portfolioId("portfolio-6")
            .accountId("account-6")
            .feePortfolioId("fee-portfolio-6")
            .feeAccountId("fee-account-6")
            .currency("EUR")
            .quantity(bd(500))
            .transactionType(TransactionType.WITHDRAWAL)
            .portfolioType(PortfolioType.VOSTRO)
            .accountType(AccountType.WALLET)
            .accountWalletType(WalletType.VOSTRO)
            .feePortfolioType(PortfolioType.VOSTRO)
            .feeAccountType(AccountType.WALLET)
            .feeAccountWalletType(WalletType.VOSTRO)
            .build();

        r7 = createReservation(7)
            .reservationRef("r7")
            .sourcePortfolioId("source-portfolio-7")
            .targetPortfolioId("target-portfolio-7")
            .feePortfolioId("fee-portfolio-7")
            .currency("DOGE")
            .quantity(bd(1_000))
            .transactionType(TransactionType.PORTFOLIO_CASH_TRANSFER)
            .sourcePortfolioType(PortfolioType.VOSTRO)
            .targetPortfolioType(PortfolioType.VOSTRO)
            .feePortfolioType(PortfolioType.VOSTRO)
            .build();

        r8 = createReservation(8)
            .reservationRef("r8")
            .sourcePortfolioId("source-portfolio-8")
            .targetPortfolioId("target-portfolio-8")
            .feePortfolioId("fee-portfolio-8")
            .asset("BTC/EUR")
            .quantity(bd(0.8))
            .transactionType(TransactionType.PORTFOLIO_ASSET_TRANSFER)
            .sourcePortfolioType(PortfolioType.VOSTRO)
            .targetPortfolioType(PortfolioType.VOSTRO)
            .feePortfolioType(PortfolioType.VOSTRO)
            .build();

        r9 = createReservation(9)
            .reservationRef("r9")
            .sourceAccountId("source-account-9")
            .targetAccountId("target-account-9")
            .feePortfolioId("fee-portfolio-9")
            .feeAccountId("fee-account-9")
            .currency("EUR")
            .quantity(bd(500))
            .transactionType(TransactionType.ACCOUNT_CASH_TRANSFER)
            .sourceAccountType(AccountType.WALLET)
            .sourceAccountWalletType(WalletType.VOSTRO)
            .targetAccountType(AccountType.CLOB)
            .targetAccountWalletType(WalletType.NOSTRO)
            .feeAccountType(AccountType.WALLET)
            .feeAccountWalletType(WalletType.VOSTRO)
            .feePortfolioType(PortfolioType.VOSTRO)
            .build();

        r10 = createReservation(10)
            .reservationRef("r10")
            .portfolioId("portfolio-10")
            .accountId("account-10")
            .currency("USD")
            .quantity(bd(10))
            .transactionType(TransactionType.FEE)
            .portfolioType(PortfolioType.VOSTRO)
            .accountType(AccountType.EXCHANGE)
            .build();
    }

    public Set<PermissionDto> setupPermissions() {
        return setupPermissions(r1, r2, r3, r4, r5, r6, r7, r8, r9, r10);
    }

    public Set<PermissionDto> setupPermissions(Reservation... reservations) {
        List<String> portfolioIds = Stream.of(reservations)
            .flatMap(transaction -> Stream.of(
                transaction.getPortfolioId(),
                transaction.getCounterPortfolioId(),
                transaction.getFeePortfolioId(),
                transaction.getSourcePortfolioId(),
                transaction.getTargetPortfolioId()))
            .filter(StringUtils::isNotBlank)
            .toList();

        List<String> accountIds = Stream.of(reservations)
            .flatMap(transaction -> Stream.of(
                transaction.getAccountId(),
                transaction.getFeeAccountId(),
                transaction.getSourceAccountId(),
                transaction.getTargetAccountId()))
            .filter(StringUtils::isNotBlank)
            .toList();

        return setupPermissions(portfolioIds, accountIds);
    }

    public static Reservation.Builder createReservation(long sequenceNumber) {
        TransactionFeeType feeType = RandomEnumUtils.randomEnum(TransactionFeeType.class, TransactionFeeType.FEE_TYPE_UNSPECIFIED);

        TransactionFee fee = new TransactionFee(
            RandomNumberUtils.randomNumber(100, 2),
            "DOGE",
            "test-fee-" + sequenceNumber,
            feeType);

        ZonedDateTime base = LocalDate.parse("2025-01-01").atStartOfDay(DateUtils.UTC);
        ZonedDateTime dateTime = base.plusDays(sequenceNumber);

        return Reservation.builder()
            .uuid(RandomStringUtils.randomUuid())
            .reservationRef("reservation-ref-" + sequenceNumber)
            .sequenceNumber(sequenceNumber)
            .dateTime(dateTime)
            .addFee(fee);
    }
}

