package io.wyden.booking.reporting.integration.infrastructure;

import org.junit.jupiter.api.extension.AfterAllCallback;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.Container;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.shaded.org.awaitility.Awaitility;
import org.testcontainers.utility.DockerImageName;

import java.io.IOException;
import java.time.Duration;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static io.wyden.booking.reporting.integration.TestContainersIntegrationBase.SHARED_NETWORK;

public class PostgreSQLSetupExtension extends DatabaseSetupExtension implements BeforeAllCallback, AfterAllCallback, BeforeEachCallback {

    private static final Logger LOGGER = LoggerFactory.getLogger(PostgreSQLSetupExtension.class);

    protected static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>(DockerImageName.parse("docker.wyden.io/mirror/postgres:14.9")
        .asCompatibleSubstituteFor("postgres"))
        .withExposedPorts(5432)
        .withNetwork(SHARED_NETWORK);

    @Override
    public void beforeAll(ExtensionContext context) throws Exception {
        injectStaticSelf(context);
        postgres.start();

        String urlSuffix = "%s:%d/%s".formatted(
            postgres.getHost(),
            postgres.getMappedPort(5432),
            postgres.getDatabaseName());

        System.setProperty("spring.datasource.url", "jdbc:postgresql://" + urlSuffix);
        System.setProperty("spring.datasource.username", postgres.getUsername());
        System.setProperty("spring.datasource.password", postgres.getPassword());

        System.setProperty("spring.flyway.url", "jdbc:postgresql://" + urlSuffix);
        System.setProperty("spring.flyway.user", postgres.getUsername());
        System.setProperty("spring.flyway.password", postgres.getPassword());
        System.setProperty("spring.flyway.locations", "classpath:psql/migration");
    }

    @Override
    public void afterAll(ExtensionContext context) throws Exception {
        logTableContent("position");
        logTableContent("balance");
        logTableContent("transaction");
        logTableContent("reservation");
        logTableContent("ledger_entry");
        postgres.stop();
    }

    @Override
    public void beforeEach(ExtensionContext context) throws Exception {
        injectSelf(context);
    }

    public String queryDb(String query) throws IOException, InterruptedException {
        String command = "psql -U %s -d %s -c '%s'".formatted(postgres.getUsername(), postgres.getDatabaseName(), query);
        Container.ExecResult result = postgres.execInContainer("sh", "-c", command);
        return result.getStdout();
    }

    @Override
    public void logTableContent(String tableName) throws IOException, InterruptedException {
        String query = "SELECT * from %s".formatted(tableName);
        LOGGER.info("Content of table name: {}\n{}", tableName, queryDb(query));
    }

    @Override
    public void clearTable(String tableName) throws IOException, InterruptedException {
        String query = "DELETE from %s".formatted(tableName);
        LOGGER.debug(queryDb(query));
    }

    @Override
    public int count(String tableName) throws IOException, InterruptedException {
        String query = "SELECT COUNT(*) FROM %s".formatted(tableName);
        String result = queryDb(query);
        return extractCount(result);
    }

    private static int extractCount(String psqlOutput) {
        Pattern pattern = Pattern.compile("\\s*(\\d+)\\s*"); // Matches a number surrounded by spaces
        Matcher matcher = pattern.matcher(psqlOutput);

        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1)); // Extract and parse the number
        } else {
            throw new IllegalArgumentException("Could not extract count from psql output");
        }
    }

    @Override
    public void awaitExpectedRecordsInTable(String tableName, int i) {
        String query = "SELECT count(*) FROM %s".formatted(tableName);
        Awaitility.await()
            .atMost(Duration.ofSeconds(60))
            .until(() -> {
                String expected = i + "\n(1 row)";
                String stdout = queryDb(query);
                return stdout.contains(expected);
            });
    }
}
