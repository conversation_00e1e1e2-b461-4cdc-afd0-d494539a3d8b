package io.wyden.booking.reporting.integration;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.permissions.AccessGatewayMockClient;
import io.wyden.booking.reporting.domain.transaction.Transaction;
import io.wyden.booking.reporting.domain.transaction.TransactionRepository;
import io.wyden.booking.reporting.domain.transaction.TransactionType;
import io.wyden.booking.reporting.integration.producer.BookingCompletedMessageProducer;
import io.wyden.booking.reporting.interfaces.rabbitmq.BookingCompletedSequencedEventConsumer;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSearch;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.oems.OemsExecType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import static io.wyden.cloud.utils.test.RandomEnumUtils.randomEnum;
import static io.wyden.cloud.utils.test.RandomNumberUtils.randomProtoNumber;
import static io.wyden.cloud.utils.test.RandomStringUtils.randomUuid;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class TransactionIntegrationTest extends TestContainersIntegrationBase {

    private static final Logger LOGGER = getLogger(TransactionIntegrationTest.class);

    @Autowired
    private AccessGatewayMockClient accessGatewayMockClient;

    @Autowired
    private BookingCompletedMessageProducer bookingCompletedMessageProducer;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private TransactionRepository transactionRepository;



    @Autowired
    private BookingCompletedSequencedEventConsumer bookingCompletedEventConsumer;

    private static long sequenceCounter = 0;

    @BeforeEach
    void setUp() {
        // Reset sequence number so that test messages are processed correctly
        bookingCompletedEventConsumer.resetSequenceNumber(sequenceCounter);
    }

    @Test
    void shouldPersistTransaction() throws Exception {
        long sequenceNumber = ++sequenceCounter;

        emitAndWait(sequenceNumber);

        Collection<Transaction> transactions = transactionRepository.findBySequenceNumber(sequenceNumber);

        DB.logTableContent("transaction");

        LOGGER.info("Transactions found ({}): {}", transactions.size(), transactions);

        assertThat(transactions).isNotEmpty();
    }

    @Test
    void shouldQueryTransaction() throws Exception {
        TransactionSnapshot transaction = emitAndWait(2);

        // Use MockMvc to test the REST endpoint
        MvcResult result = mockMvc.perform(get("/transactions/{transactionId}", transaction.getStreetCashTrade().getUuid()))
            .andExpect(status().isOk())
            .andDo(print())
            .andReturn();

        // Parse the binary response
        byte[] content = result.getResponse().getContentAsByteArray();
        TransactionSnapshot retrievedTransaction = TransactionSnapshot.parseFrom(content);

        LOGGER.info("Retrieved transaction: \n{}", retrievedTransaction);

        // Verify the transaction
        StreetCashTradeSnapshot actual = retrievedTransaction.getStreetCashTrade();
        StreetCashTradeSnapshot expected = transaction.getStreetCashTrade();

        validateTransaction(actual, expected);
    }

    @Test
    void shouldSearchTransaction() throws Exception {
        // setup permissions
        Set<PermissionDto> permissions = Set.of(
            new PermissionDto("portfolio", "read", "client-a"),
            new PermissionDto("venue.account", "read", "bitmex-testnet1")
        );
        accessGatewayMockClient.grantPermissions(permissions);

        emitAndWait(3);
        emitAndWait(4);
        emitAndWait(5);
        TransactionSnapshot transaction = emitAndWait(6);

        String executionId = transaction.getStreetCashTrade().getExecutionId();

        // Create search request
        TransactionSearch searchRequest = TransactionSearch.newBuilder()
            .setExecutionId(executionId)
            .setFirst(10)
            .build();

        // Send search request and get response
        MvcResult result = mockMvc.perform(post("/transactions/search?version=proto")
                .contentType("application/x-protobuf")
                .content(searchRequest.toByteArray()))
            .andExpect(status().isOk())
            .andDo(print())
            .andReturn();

        // Parse the binary response
        byte[] content = result.getResponse().getContentAsByteArray();
        CursorConnection connection = CursorConnection.parseFrom(content);

        LOGGER.info("Search response: \n{}", connection);

        // Verify the connection has edges
        assertThat(connection.getEdgesCount()).isPositive();

        // Find our transaction in the results
        boolean foundTransaction = false;
        for (CursorEdge edge : connection.getEdgesList()) {
            CursorNode node = edge.getNode();
            if (node.hasTransaction()) {
                TransactionSnapshot foundTx = node.getTransaction();
                if (foundTx.hasStreetCashTrade()) {

                    foundTransaction = true;

                    StreetCashTradeSnapshot actual = foundTx.getStreetCashTrade();
                    StreetCashTradeSnapshot expected = transaction.getStreetCashTrade();

                    validateTransaction(actual, expected);

                    break;
                }
            }
        }

        assertThat(foundTransaction).isTrue();

        // Test searching by portfolio
        String portfolio = transaction.getStreetCashTrade().getPortfolio();
        TransactionSearch portfolioSearch = TransactionSearch.newBuilder()
            .addPortfolioId(portfolio)
            .setFirst(2)
            .build();

        result = mockMvc.perform(post("/transactions/search?version=proto")
                .contentType("application/x-protobuf")
                .content(portfolioSearch.toByteArray()))
            .andExpect(status().isOk())
            .andReturn();

        // Parse and verify portfolio search results
        content = result.getResponse().getContentAsByteArray();
        connection = CursorConnection.parseFrom(content);

        assertThat(connection.getEdgesCount()).isPositive();
        assertThat(connection.getPageInfo().getTotalSize()).isPositive();
    }

    @Test
    void shouldReturn404WhenTransactionIsNotFound() throws Exception {
        String nonExistentId = "non-existent-uuid";

        mockMvc.perform(get("/transactions/{transactionId}", nonExistentId))
            .andExpect(status().isNotFound())
            .andDo(print());
    }

    @Test
    void shouldReturnTransactionTypes() throws Exception {
        List<String> expectedTypes = TransactionType.stringValues();

        // Verify they're not empty (sanity check)
        assertThat(expectedTypes).isNotEmpty();

        mockMvc.perform(get("/transaction-types")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$").isNotEmpty())
            .andExpect(jsonPath("$.length()").value(expectedTypes.size()))
            // Verify each expected type exists in the response
            .andExpect(result -> {
                String content = result.getResponse().getContentAsString();
                for (String type : expectedTypes) {
                    assertThat(content).contains("\"" + type + "\"");
                }
            })
            .andDo(print());
    }


    private static TransactionSnapshot createTransaction(long sequenceNumber) {
        return TransactionSnapshot.newBuilder()
            .setStreetCashTrade(StreetCashTradeSnapshot.newBuilder()
                .setUuid(randomUuid())
                .setReservationRef("reservation-ref-" + sequenceNumber)
                .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setExecutionId("execution-id-" + sequenceNumber)
                .setVenueExecutionId("venue-execution-id-" + sequenceNumber)
                .setDescription("description-" + sequenceNumber)
                .setQuantity(randomProtoNumber())
                .setLeavesQuantity(randomProtoNumber())
                .setPrice(randomProtoNumber(60_000, 2))
                .setBaseCurrency("BTC")
                .setCurrency("EUR")
                .setIntOrderId("int-order-id-" + sequenceNumber)
                .setExtOrderId("ext-order-id-" + sequenceNumber)
                .setOrderId("order-id-" + sequenceNumber)
                .setPortfolio("client-a")
                .setVenueAccount("bitmex-testnet1")
                .addTransactionFee(Fee.newBuilder()
                    .setAmount(randomProtoNumber(10, 2))
                    .setCurrency("EUR")
                    .setFeeType(randomEnum(FeeType.class, FeeType.FEE_TYPE_UNSPECIFIED, FeeType.UNRECOGNIZED))
                    .build())
                .addTransactionFee(Fee.newBuilder()
                    .setAmount(randomProtoNumber(100, 4))
                    .setCurrency("DOGE")
                    .setFeeType(FeeType.FIXED_FEE)
                    .build())
                .setParentOrderId("parent-order-id-" + sequenceNumber)
                .setRootOrderId("root-order-id-" + sequenceNumber)
                .setUnderlyingExecutionId("underlying-execution-id-" + sequenceNumber)
                .setRootExecutionId("root-execution-id-" + sequenceNumber)
                .setSettled(true)
                .setIsLive(true)
                .setClientRootOrderId("client-root-order-id-" + sequenceNumber)
                .setSettlementId("settlement-id-" + sequenceNumber)
                .setSettledDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setClientSettlementId("client-settlement-id-" + sequenceNumber)
                .setExecType(OemsExecType.PARTIAL_FILL)
                .build())
            .build();
    }

    private TransactionSnapshot emitAndWait(long sequenceNumber) {
        TransactionSnapshot transaction = createTransaction(sequenceNumber);

        BookingCompleted bookingCompleted = BookingCompleted.newBuilder()
            .setSequenceNumber(sequenceNumber)
            .setTransactionCreated(transaction)
            .build();

        bookingCompletedMessageProducer.emit(bookingCompleted);

        await("Waiting for transaction to be persisted")
            .until(() -> !transactionRepository.findBySequenceNumber(sequenceNumber).isEmpty());

        return transaction;
    }

    @SuppressWarnings({"java:S125", "java:S1135"})
    private static void validateTransaction(StreetCashTradeSnapshot actual, StreetCashTradeSnapshot expected) {
        assertThat(actual.getUuid()).isEqualTo(expected.getUuid());
        assertThat(actual.getReservationRef()).isEqualTo(expected.getReservationRef());
        assertThat(actual.getReservationRef()).isEqualTo(expected.getReservationRef());
        assertThat(actual.getDateTime()).isEqualTo(expected.getDateTime());
        assertThat(actual.getQuantity()).isEqualTo(expected.getQuantity());
        assertThat(actual.getLeavesQuantity()).isEqualTo(expected.getLeavesQuantity());
        assertThat(actual.getPrice()).isEqualTo(expected.getPrice());
        assertThat(actual.getCurrency()).isEqualTo(expected.getCurrency());
        assertThat(actual.getBaseCurrency()).isEqualTo(expected.getBaseCurrency());
        assertThat(actual.getPortfolio()).isEqualTo(expected.getPortfolio());
        assertThat(actual.getVenueAccount()).isEqualTo(expected.getVenueAccount());
        assertThat(actual.getExecutionId()).isEqualTo(expected.getExecutionId());
        assertThat(actual.getVenueExecutionId()).isEqualTo(expected.getVenueExecutionId());
        assertThat(actual.getIntOrderId()).isEqualTo(expected.getIntOrderId());
        assertThat(actual.getExtOrderId()).isEqualTo(expected.getExtOrderId());
        assertThat(actual.getOrderId()).isEqualTo(expected.getOrderId());
        assertThat(actual.getParentOrderId()).isEqualTo(expected.getParentOrderId());
        assertThat(actual.getRootOrderId()).isEqualTo(expected.getRootOrderId());
        assertThat(actual.getUnderlyingExecutionId()).isEqualTo(expected.getUnderlyingExecutionId());
        assertThat(actual.getRootExecutionId()).isEqualTo(expected.getRootExecutionId());
        assertThat(actual.getClientRootOrderId()).isEqualTo(expected.getClientRootOrderId());
        assertThat(actual.getDescription()).isEqualTo(expected.getDescription());
        assertThat(actual.getIsLive()).isEqualTo(expected.getIsLive());
        assertThat(actual.getSettled()).isEqualTo(expected.getSettled());
        assertThat(actual.getSettlementId()).isEqualTo(expected.getSettlementId());
        assertThat(actual.getSettledDateTime()).isEqualTo(expected.getSettledDateTime());
        // TODO SPL verify if clientSettlementId is required in Transaction model
        //        assertThat(actual.getClientSettlementId()).isEqualTo(expected.getClientSettlementId());
        assertThat(actual.getExecType()).isEqualTo(expected.getExecType());

        // Verify transaction fees
        assertThat(actual.getTransactionFeeList()).hasSameSizeAs(expected.getTransactionFeeList());

        for (int i = 0; i < expected.getTransactionFeeList().size(); i++) {
            Fee actualFee = actual.getTransactionFee(i);
            Fee expectedFee = expected.getTransactionFee(i);

            assertThat(actualFee.getAmount()).isEqualTo(expectedFee.getAmount());
            assertThat(actualFee.getCurrency()).isEqualTo(expectedFee.getCurrency());
            assertThat(actualFee.getFeeType()).isEqualTo(expectedFee.getFeeType());
        }
    }
}
