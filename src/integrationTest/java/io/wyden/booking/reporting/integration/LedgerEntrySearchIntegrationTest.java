package io.wyden.booking.reporting.integration;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.LedgerEntryService;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntryRepository;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntryType;
import io.wyden.booking.reporting.domain.transaction.TransactionFee;
import io.wyden.booking.reporting.domain.transaction.TransactionFeeType;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.test.RandomEnumUtils;
import io.wyden.cloud.utils.test.RandomNumberUtils;
import io.wyden.cloudutils.tools.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static io.wyden.booking.reporting.interfaces.rest.RequestModel.LedgerEntrySearch.builder;
import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.slf4j.LoggerFactory.getLogger;

public class LedgerEntrySearchIntegrationTest extends SearchIntegrationTest {

    private static final Logger LOGGER = getLogger(LedgerEntrySearchIntegrationTest.class);

    @Autowired
    private LedgerEntryService ledgerEntryService;

    @Autowired
    private LedgerEntryRepository ledgerEntryRepository;

    private static LedgerEntry le1;
    private static LedgerEntry le2;
    private static LedgerEntry le3;
    private static LedgerEntry le4;
    private static LedgerEntry le5;
    private static LedgerEntry le6;
    private static LedgerEntry le7;
    private static LedgerEntry le8;
    private static LedgerEntry le9;
    private static LedgerEntry le10;
    private static LedgerEntry le11;
    private static LedgerEntry le12;
    private static LedgerEntry le13;
    private static LedgerEntry le14;
    private static LedgerEntry le15;
    private static LedgerEntry le16;
    private static LedgerEntry le17;

    @BeforeEach
    void setUp() {
        setupLedgerEntries();
        setupPermissions();

        // setupLedgerEntries has to be static, because it is used in @ParametrizedTest
        // saving in repository has to happen in non-static block
        ledgerEntryRepository.save(le1);
        ledgerEntryRepository.save(le2);
        ledgerEntryRepository.save(le3);
        ledgerEntryRepository.save(le4);
        ledgerEntryRepository.save(le5);
        ledgerEntryRepository.save(le6);
        ledgerEntryRepository.save(le7);
        ledgerEntryRepository.save(le8);
        ledgerEntryRepository.save(le9);
        ledgerEntryRepository.save(le10);
        ledgerEntryRepository.save(le11);
        ledgerEntryRepository.save(le12);
        ledgerEntryRepository.save(le13);
        ledgerEntryRepository.save(le14);
        ledgerEntryRepository.save(le15);
        ledgerEntryRepository.save(le16);
        ledgerEntryRepository.save(le17);
    }

    @AfterEach
    void tearDown() throws Exception {
        DB.clearTable("ledger_entry");
    }

    @Test
    void shouldReturnNothing() throws Exception {
        DB.clearTable("ledger_entry");

        RequestModel.LedgerEntrySearch search = RequestModel.LedgerEntrySearch.builder()
            .build();

        searchAndVerifyEmptyResults(search);
    }

    @Test
    void shouldReturnAll() {
        RequestModel.LedgerEntrySearch search = RequestModel.LedgerEntrySearch.builder()
            .build();

        searchAndVerifyResults(search, le17, le16, le15, le14, le13, le12, le11, le10, le9, le8, le7, le6, le5, le4, le3, le2, le1);
    }

    @Test
    void shouldFindBySymbol() {
        RequestModel.LedgerEntrySearch search = builder()
            .symbol(le4.getSymbol(), le10.getSymbol())
            .build();

        searchAndVerifyResults(search, le10, le4);
    }

    @Test
    void shouldFindByCurrency() {
        RequestModel.LedgerEntrySearch search = builder()
            .currency(le4.getSymbol(), le10.getSymbol())
            .build();

        searchAndVerifyResults(search, le10, le4);
    }

    @Test
    void shouldFindByLedgerEntryType() {
        RequestModel.LedgerEntrySearch search = RequestModel.LedgerEntrySearch.builder()
            .ledgerEntryType(le10.getLedgerEntryType())
            .build();

        searchAndVerifyResults(search, le10);
    }

    @Test
    void shouldFindByOrderId() {
        RequestModel.LedgerEntrySearch search = builder()
            .orderId(le2.getReservationRef())
            .build();

        searchAndVerifyResults(search, le2);
    }

    @Test
    void shouldFindByTransactionId() {
        RequestModel.LedgerEntrySearch search = builder()
            .transactionId(le2.getTransactionId())
            .build();

        searchAndVerifyResults(search, le2);
    }

    @Test
    void shouldFindByDateSingleTransaction() {
        RequestModel.LedgerEntrySearch search = builder()
            .dateRange(le1.getDateTime(), le2.getDateTime())
            .build();

        searchAndVerifyResults(search, le1);
    }

    @Test
    void shouldFindByDateMultipleTransactions() {
        RequestModel.LedgerEntrySearch search = builder()
            .dateRange(le1.getDateTime(), le11.getDateTime())
            .build();

        searchAndVerifyResults(search, le10, le9, le8, le7, le6, le5, le4, le3, le2, le1);
    }

    @Test
    void shouldFindByDateNoTransactions() {
        RequestModel.LedgerEntrySearch search = builder()
            .dateRange(le1.getDateTime().plusSeconds(1), le2.getDateTime())
            .build();

        searchAndVerifyEmptyResults(search);
    }

    @Test
    void shouldFindByMultipleFilters() {
        RequestModel.LedgerEntrySearch search = builder()
            .portfolio(le2.getPortfolioId())
            .currency(le2.getSymbol())
            .orderId(le2.getReservationRef())
            .build();

        searchAndVerifyResults(search, le2);
    }

    @Test
    void shouldFindByNarrowingFilters() {
        RequestModel.LedgerEntrySearch search1 = builder()
            .currency("USD")
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search1, le6, le8, le17);

        RequestModel.LedgerEntrySearch search2 = builder()
            .currency("USD")
            .dateRange(le6.getDateTime(), le17.getDateTime())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search2, le6, le8);

        RequestModel.LedgerEntrySearch search3 = builder()
            .currency("USD")
            .dateRange(le6.getDateTime(), le17.getDateTime())
            .portfolio(le6.getPortfolioId())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search3, le6);
    }

    @Test
    void shouldSortResults() {
        RequestModel.LedgerEntrySearch descSearch = builder()
            .sortingOrder(RequestModel.SortingOrder.DESC)
            .build();

        searchAndVerifyResults(descSearch, le17, le16, le15, le14, le13, le12, le11, le10, le9, le8, le7, le6, le5, le4, le3, le2, le1);

        RequestModel.LedgerEntrySearch ascSearch = builder()
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(ascSearch, le1, le2, le3, le4, le5, le6, le7, le8, le9, le10, le11, le12, le13, le14, le15, le16, le17);
    }

    @Test
    void shouldFindByAccountId() {
        RequestModel.LedgerEntrySearch search = builder()
            .accountId(le3.getAccountId())
            .build();

        searchAndVerifyResults(search, le3);
    }

    @Test
    void shouldFindByPortfolioId() {
        RequestModel.LedgerEntrySearch search = builder()
            .portfolio(le1.getPortfolioId())
            .build();

        searchAndVerifyResults(search, le1);
    }

    @Test
    void shouldFindByPortfolioAndAccountId() {
        RequestModel.LedgerEntrySearch search = builder()
            .portfolio(le2.getPortfolioId())
            .accountId(le3.getAccountId())
            .build();

        searchAndVerifyResults(search, le3, le2);
    }

    @Test
    void shouldPaginateResults() {
        RequestModel.LedgerEntrySearch search = builder()
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .first(3)
            .build();

        PaginationModel.CursorConnection<LedgerEntry> connection = lookup(search);

        assertThat(connection.pageInfo().hasNextPage()).isTrue();
        assertThat(connection.pageInfo().pageSize()).isEqualTo(3);
        String endCursor = connection.pageInfo().endCursor();
        assertThat(endCursor).isEqualTo(Long.toString(le3.getId()));
        verifyLedgerEntries(connection, le1, le2, le3);

        RequestModel.LedgerEntrySearch afterSearch = builder()
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .first(20)
            .after(endCursor)
            .build();

        PaginationModel.CursorConnection<LedgerEntry> afterConnection = lookup(afterSearch);

        assertThat(afterConnection.pageInfo().hasNextPage()).isFalse();
        assertThat(afterConnection.pageInfo().pageSize()).isEqualTo(14);
        assertThat(afterConnection.pageInfo().endCursor()).isEqualTo(Long.toString(le17.getId()));
        verifyLedgerEntries(afterConnection, le4, le5, le6, le7, le8, le9, le10, le11, le12, le13, le14, le15, le16, le17);
    }


    @ParameterizedTest
    @MethodSource("searchAllScenarios")
    void shouldReturnOnlyAuthorizedLedgerEntriesWithRequestAll(List<String> portfolioPermissions, List<String> accountPermissions, List<LedgerEntry> expectedLedgerEntries) {
        setupPermissions(portfolioPermissions, accountPermissions);

        RequestModel.LedgerEntrySearch search = RequestModel.LedgerEntrySearch.builder()
            .build();

        searchAndVerifyResults(search, expectedLedgerEntries.toArray(LedgerEntry[]::new));
    }

    private static Stream<Arguments> searchAllScenarios() {
        // set up is not called in a static context
        setupLedgerEntries();

        return Stream.of(
            // vostro portfolio reservation (BTC)
            arguments(List.of(le1.getPortfolioId()), NO_ACCESS, List.of(le1)),

            // wallet account asset trade buy (BTC/USD)
            arguments(NO_ACCESS, List.of(le3.getAccountId()), List.of(le3)),

            // multiple permissions - portfolio and account
            arguments(List.of(le1.getPortfolioId(), le4.getPortfolioId()), List.of(le3.getAccountId()), List.of(le4, le3, le1)),

            // partial permissions - only portfolio access
            arguments(List.of(le2.getPortfolioId(), le5.getPortfolioId()), NO_ACCESS, List.of(le5, le2)),

            // no permissions at all
            arguments(NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE)
        );
    }

    @ParameterizedTest
    @MethodSource("searchSpecificScenarios")
    void shouldReturnOnlyAuthorizedLedgerEntriesWithSpecificRequest(String searchPortfolio, String searchAccount, List<String> portfolioPermissions, List<String> accountPermissions, List<LedgerEntry> expectedLedgerEntries) {
        setupPermissions(portfolioPermissions, accountPermissions);

        RequestModel.LedgerEntrySearch search = RequestModel.LedgerEntrySearch.builder()
            .build();

        if (isNotBlank(searchPortfolio)) {
            search = search.toBuilder()
                .portfolio(searchPortfolio)
                .build();
        }

        if (isNotBlank(searchAccount)) {
            search = search.toBuilder()
                .accountId(searchAccount)
                .build();
        }

        searchAndVerifyResults(search, expectedLedgerEntries.toArray(LedgerEntry[]::new));
    }

    private static Stream<Arguments> searchSpecificScenarios() {
        // set up is not called in a static context
        setupLedgerEntries();

        return Stream.of(
            // vostro portfolio reservation by portfolioId
            arguments(le1.getPortfolioId(), "", List.of(le1.getPortfolioId()), NO_ACCESS, List.of(le1)),

            // vostro portfolio reservation (missing portfolio)
            arguments(le1.getPortfolioId(), "", NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            // wallet account asset trade by accountId
            arguments("", le3.getAccountId(), NO_ACCESS, List.of(le3.getAccountId()), List.of(le3)),

            // wallet account asset trade (missing account)
            arguments("", le3.getAccountId(), NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            // combined portfolio and account search with permissions
            arguments(le1.getPortfolioId(), le3.getAccountId(), List.of(le1.getPortfolioId()), List.of(le3.getAccountId()), List.of(le3, le1)),

            // combined search (missing portfolio permission)
            arguments(le4.getPortfolioId(), le3.getAccountId(), NO_ACCESS, List.of(le3.getAccountId()), List.of(le3)),

            // search with non-existent ID
            arguments("other-id", "", NO_ACCESS, OTHER_ACCESS, EMPTY_RESPONSE)
        );
    }

    private PaginationModel.CursorConnection<LedgerEntry> lookup(RequestModel.LedgerEntrySearch search) {
        RequestModel.LedgerEntrySearch clientRequest = search.withClientId("client-id");

        PaginationModel.CursorConnection<LedgerEntry> connection = ledgerEntryService.search(clientRequest);

        LOGGER.info("Connection response: {}", connection);

        if (connection.edges().isEmpty()) {
            LOGGER.info("No ledger entries found");
        } else {
            List<String> ledgerEntryRefs = connection.getAllNodes().stream()
                .map(LedgerEntry::getReservationRef)
                .toList();

            LOGGER.info("Ledger entries in response: {}", ledgerEntryRefs);
        }

        return connection;
    }

    private void searchAndVerifyResults(RequestModel.LedgerEntrySearch search, LedgerEntry... expectedLedgerEntries) {
        PaginationModel.CursorConnection<LedgerEntry> connection = lookup(search);

        verifyLedgerEntries(connection, expectedLedgerEntries);
        verifyPageInfo(connection, expectedLedgerEntries.length);
    }

    private void searchAndVerifyEmptyResults(RequestModel.LedgerEntrySearch search) {
        PaginationModel.CursorConnection<LedgerEntry> connection = lookup(search);

        verifyLedgerEntries(connection);
        verifyPageInfo(connection, 0);
    }

    private static void verifyLedgerEntries(PaginationModel.CursorConnection<LedgerEntry> connection, LedgerEntry... expectedLedgerEntries) {
        String[] expectedRefs = Arrays.stream(expectedLedgerEntries)
            .map(LedgerEntry::getReservationRef)
            .toArray(String[]::new);

        assertThat(connection.getAllNodes())
            .extracting("reservationRef")
            .containsExactly(expectedRefs);
    }

    private static void setupLedgerEntries() {
        le1 = createLedgerEntry(1)
            .ledgerEntryType(LedgerEntryType.RESERVATION)
            .portfolioId("portfolio-id-1")
            .symbol("BTC")
            .quantity(BigDecimal.ONE)
            .price(bd(60_000))
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();

        le2 = createLedgerEntry(2)
            .ledgerEntryType(LedgerEntryType.WITHDRAWAL_RESERVATION)
            .portfolioId("portfolio-id-2")
            .symbol("EUR")
            .quantity(bd(-100_000))
            .price(BigDecimal.ONE)
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();

        le3 = createLedgerEntry(3)
            .ledgerEntryType(LedgerEntryType.ASSET_TRADE_BUY)
            .accountId("account-id-3")
            .symbol("BTC/USD")
            .quantity(BigDecimal.ONE)
            .price(bd(60_000))
            .accountType(RequestModel.AccountType.WALLET)
            .accountWalletType(RequestModel.WalletType.NOSTRO)
            .build();

        le4 = createLedgerEntry(4)
            .ledgerEntryType(LedgerEntryType.ASSET_TRADE_SELL)
            .portfolioId("portfolio-id-4")
            .symbol("ETH/EUR")
            .quantity(bd(-2.5))
            .price(bd(12_500.99))
            .portfolioType(RequestModel.PortfolioType.NOSTRO)
            .build();

        le5 = createLedgerEntry(5)
            .ledgerEntryType(LedgerEntryType.CASH_TRADE_CREDIT)
            .portfolioId("portfolio-id-5")
            .symbol("BTC")
            .quantity(BigDecimal.ONE)
            .price(bd(60_000))
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();

        le6 = createLedgerEntry(6)
            .ledgerEntryType(LedgerEntryType.CASH_TRADE_DEBIT)
            .portfolioId("portfolio-id-6")
            .symbol("USD")
            .quantity(bd(-69_000))
            .price(BigDecimal.ONE)
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();

        le7 = createLedgerEntry(7)
            .ledgerEntryType(LedgerEntryType.ASSET_TRADE_PROCEEDS)
            .accountId("account-id-7")
            .symbol("EUR")
            .quantity(bd(10.5))
            .price(bd(1.12))
            .accountType(RequestModel.AccountType.EXCHANGE)
            .build();

        le8 = createLedgerEntry(8)
            .ledgerEntryType(LedgerEntryType.DEPOSIT)
            .portfolioId("portfolio-id-8")
            .symbol("USD")
            .quantity(bd(100_000))
            .price(BigDecimal.ONE)
            .portfolioType(RequestModel.PortfolioType.NOSTRO)
            .build();

        le9 = createLedgerEntry(9)
            .ledgerEntryType(LedgerEntryType.WITHDRAWAL)
            .accountId("account-id-9")
            .symbol("USDT")
            .quantity(bd(-50_000))
            .price(BigDecimal.ONE)
            .accountType(RequestModel.AccountType.CUSTODY)
            .build();

        le10 = createLedgerEntry(10)
            .ledgerEntryType(LedgerEntryType.TRANSFER)
            .portfolioId("portfolio-id-10")
            .symbol("DOGE")
            .quantity(bd(125))
            .price(bd(0.000125))
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();

        le11 = createLedgerEntry(11)
            .ledgerEntryType(LedgerEntryType.FEE)
            .accountId("account-id-11")
            .symbol("EUR")
            .quantity(bd(-50_000))
            .price(bd(1.12))
            .accountType(RequestModel.AccountType.CLOB)
            .build();

        le12 = createLedgerEntry(12)
            .ledgerEntryType(LedgerEntryType.TRADING_FEE)
            .accountId("account-id-12")
            .symbol("EUR")
            .quantity(bd(-50_000))
            .price(bd(1.12))
            .accountType(RequestModel.AccountType.EXCHANGE)
            .build();

        le13 = createLedgerEntry(13)
            .ledgerEntryType(LedgerEntryType.DEPOSIT_FEE)
            .portfolioId("portfolio-id-13")
            .symbol("EUR")
            .quantity(bd(-50_000))
            .price(bd(1.12))
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();

        le14 = createLedgerEntry(14)
            .ledgerEntryType(LedgerEntryType.WITHDRAWAL_FEE)
            .portfolioId("portfolio-id-14")
            .symbol("CHF")
            .quantity(bd(-50_000))
            .price(bd(1.4))
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();

        le15 = createLedgerEntry(15)
            .ledgerEntryType(LedgerEntryType.TRANSFER_FEE)
            .portfolioId("portfolio-id-15")
            .symbol("EUR")
            .quantity(bd(-50_000))
            .price(bd(1.12))
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();

        le16 = createLedgerEntry(16)
            .ledgerEntryType(LedgerEntryType.RESERVATION_RELEASE)
            .portfolioId("portfolio-id-16")
            .symbol("BTC")
            .quantity(bd(-1))
            .price(bd(60_000))
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();

        le17 = createLedgerEntry(17)
            .ledgerEntryType(LedgerEntryType.RESERVATION_RELEASE_REMAINING)
            .portfolioId("portfolio-id-17")
            .symbol("USD")
            .quantity(bd(9.999))
            .price(bd(60_000))
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();
    }

    private static LedgerEntry.Builder createLedgerEntry(long sequenceNumber) {
        TransactionFeeType feeType = RandomEnumUtils.randomEnum(TransactionFeeType.class, TransactionFeeType.FEE_TYPE_UNSPECIFIED);

        TransactionFee fee = new TransactionFee(
            RandomNumberUtils.randomNumber(100, 2),
            "DOGE",
            "test-fee-" + sequenceNumber,
            feeType);

        ZonedDateTime base = LocalDate.parse("2025-01-01").atStartOfDay(DateUtils.UTC);
        ZonedDateTime dateTime = base.plusDays(sequenceNumber);

        return LedgerEntry.builder()
            .dateTime(dateTime)
            .sequenceNumber(sequenceNumber)
            .reservationRef("le" + sequenceNumber)
            .transactionId("transaction-id-" + sequenceNumber)
            .settled(false)
            .addFee(fee);
    }

    public Set<PermissionDto> setupPermissions() {
        return setupPermissions(le1, le2, le3, le4, le5, le6, le7, le8, le9, le10, le11, le12, le13, le14, le15, le16, le17);
    }

    public Set<PermissionDto> setupPermissions(LedgerEntry... ledgerEntries) {
        List<String> portfolioIds = Stream.of(ledgerEntries)
            .map(LedgerEntry::getPortfolioId)
            .filter(StringUtils::isNotBlank)
            .toList();

        List<String> accountIds = Stream.of(ledgerEntries)
            .map(LedgerEntry::getAccountId)
            .filter(StringUtils::isNotBlank)
            .toList();

        return setupPermissions(portfolioIds, accountIds);
    }
}
