package io.wyden.booking.reporting.integration;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.booking.reporting.application.permissions.AccessGatewayMockClient;
import io.wyden.booking.reporting.integration.producer.BookingCompletedMessageProducer;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.published.rate.Rate;
import io.wyden.published.rate.RateKey;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.rate.domain.map.RateMapConfig;
import io.wyden.referencedata.domain.PortfolioMapConfig;
import io.wyden.referencedata.domain.VenueAccountMapConfig;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

import java.util.HashSet;

@TestConfiguration
public class TestContainersConfiguration {

    @Bean
    public BookingCompletedMessageProducer bookingCompletedMessageProducer(RabbitIntegrator rabbitIntegrator) {
        return new BookingCompletedMessageProducer(rabbitIntegrator);
    }

    @Bean
    public AccessGatewayMockClient accessGatewayFacade() {
        return new AccessGatewayMockClient(new HashSet<>());
    }

    @Bean
    public IMap<String, Portfolio> portfoliosMap(HazelcastInstance hazelcast) {
        return PortfolioMapConfig.getMap(hazelcast);
    }

    @Bean
    public IMap<String, VenueAccount> venueAccountMap(HazelcastInstance hazelcast) {
        return VenueAccountMapConfig.getMap(hazelcast);
    }

    @Bean
    public IMap<RateKey, Rate> ratesMap(HazelcastInstance hazelcast) {
        return RateMapConfig.getMap(hazelcast);
    }
}
