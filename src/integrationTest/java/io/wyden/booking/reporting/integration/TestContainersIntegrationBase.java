package io.wyden.booking.reporting.integration;

import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import io.wyden.booking.reporting.integration.infrastructure.DatabaseSetupExtension;
import io.wyden.booking.reporting.integration.infrastructure.PostgreSQLSetupExtension;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static org.slf4j.LoggerFactory.getLogger;

@Testcontainers
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
                properties = {
                    "spring.main.allow-bean-definition-overriding=true",
                    "logging.level.io.wyden.booking=debug"
                },
                classes = TestContainersConfiguration.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@ExtendWith(PostgreSQLSetupExtension.class)
@AutoConfigureMockMvc
public class TestContainersIntegrationBase {

    private static final Logger LOGGER = getLogger(TestContainersIntegrationBase.class);

    public static final Network SHARED_NETWORK = Network.newNetwork();

    protected static DatabaseSetupExtension DB;

    @Container
    protected static RabbitMQContainer rabbitmq = new RabbitMQContainer(DockerImageName.parse("docker.wyden.io/mirror/rabbitmq:3.12-management")
        .asCompatibleSubstituteFor("rabbitmq:management"))
        .withExposedPorts(5672)
        .withNetwork(SHARED_NETWORK);

    @Container
    protected static GenericContainer<?> hazelcast = new GenericContainer<>("docker.wyden.io/mirror/hazelcast/hazelcast:5.2.0")
        .withExposedPorts(5701)
        .withNetwork(SHARED_NETWORK);

    @LocalServerPort
    protected int port;

    @Autowired
    protected RabbitIntegrator rabbitIntegrator;

    @DynamicPropertySource
    static void registerDynamicProperties(DynamicPropertyRegistry registry) {
        registry.add("rabbitmq.host", rabbitmq::getHost);
        registry.add("rabbitmq.port", () -> rabbitmq.getMappedPort(5672));
        registry.add("rabbitmq.username", rabbitmq::getAdminUsername);
        registry.add("rabbitmq.password", rabbitmq::getAdminPassword);

        registry.add("hz.addressList", () -> hazelcast.getHost() + ":" + hazelcast.getFirstMappedPort());
    }

    protected <T extends Message> Collection<T> bindToTestQueue(RabbitExchange<T> rabbitExchange, Parser<T> parser) {
        RabbitQueue<T> queue = new RabbitQueueBuilder<T>(rabbitIntegrator)
            .declare();

        queue.bindWithHeaders(rabbitExchange, MatchingCondition.ALL, Map.of());

        List<T> producedMessages = new ArrayList<>();
        queue.attachConsumer(parser, (message, basicProperties) -> {
            LOGGER.debug("Consumed message from test queue connected to: {} \n{}", rabbitExchange.getName(), message);
            producedMessages.add(message);
            return ConsumptionResult.consumed();
        });

        return producedMessages;
    }
}
