package io.wyden.booking.reporting.integration;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.TransactionService;
import io.wyden.booking.reporting.domain.transaction.ExecType;
import io.wyden.booking.reporting.domain.transaction.SettlementType;
import io.wyden.booking.reporting.domain.transaction.Transaction;
import io.wyden.booking.reporting.domain.transaction.TransactionFee;
import io.wyden.booking.reporting.domain.transaction.TransactionFeeType;
import io.wyden.booking.reporting.domain.transaction.TransactionRepository;
import io.wyden.booking.reporting.domain.transaction.TransactionType;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.RequestModel.AccountType;
import io.wyden.booking.reporting.interfaces.rest.RequestModel.PortfolioType;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.test.RandomEnumUtils;
import io.wyden.cloud.utils.test.RandomNumberUtils;
import io.wyden.cloud.utils.test.RandomStringUtils;
import io.wyden.cloudutils.tools.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static io.wyden.booking.reporting.interfaces.rest.RequestModel.TransactionSearch.builder;
import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.slf4j.LoggerFactory.getLogger;

public class TransactionSearchIntegrationTest extends SearchIntegrationTest {

    private static final Logger LOGGER = getLogger(TransactionSearchIntegrationTest.class);

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private TransactionRepository transactionRepository;

    private static Transaction t1;
    private static Transaction t2;
    private static Transaction t3;
    private static Transaction t4;
    private static Transaction t5;
    private static Transaction t6;
    private static Transaction t7;
    private static Transaction t8;
    private static Transaction t9;
    private static Transaction t10;
    private static Transaction t11;

    @BeforeEach
    void setUp() {
        setupTransactions();
        setupPermissions();

        // setupTransactions has to be static, because it is used in @ParametrizedTest
        // saving in repository has to happen in non-static block
        transactionRepository.save(t1);
        transactionRepository.save(t2);
        transactionRepository.save(t3);
        transactionRepository.save(t4);
        transactionRepository.save(t5);
        transactionRepository.save(t6);
        transactionRepository.save(t7);
        transactionRepository.save(t8);
        transactionRepository.save(t9);
        transactionRepository.save(t10);
        transactionRepository.save(t11);
    }

    @AfterEach
    void tearDown() throws Exception {
        DB.clearTable("transaction");
    }

    @Test
    void shouldReturnNothing() throws Exception {
        DB.clearTable("transaction");

        RequestModel.TransactionSearch search = builder()
            .build();

        searchAndVerifyEmptyResults(search);
    }

    @Test
    void shouldReturnAll() {
        RequestModel.TransactionSearch search = builder()
            .build();

        searchAndVerifyResults(search, t10, t9, t8, t7, t6, t5, t4, t3, t2, t1);
    }

    @Test
    void shouldFindBySymbol() {
        RequestModel.TransactionSearch search = builder()
            .symbol("DOGE", "BTC/USD")
            .build();

        searchAndVerifyResults(search, t7, t4, t3);
    }

    @Test
    void shouldFindByCurrency() {
        RequestModel.TransactionSearch search = builder()
            .currency("DOGE", "BTC")
            .build();

        searchAndVerifyResults(search, t7, t2, t1);
    }

    @Test
    void shouldFindByTransactionType() {
        RequestModel.TransactionSearch search = RequestModel.TransactionSearch.builder()
            .transactionType(t10.getTransactionType())
            .build();

        searchAndVerifyResults(search, t10);
    }

    @Test
    void shouldFindByOrderId() {
        RequestModel.TransactionSearch search = builder()
            .orderId(t2.getOrderId())
            .build();

        searchAndVerifyResults(search, t2);
    }

    @Test
    void shouldFindByExecutionId() {
        RequestModel.TransactionSearch search = builder()
            .executionId(t3.getExecutionId())
            .build();

        searchAndVerifyResults(search, t3);
    }

    @Test
    void shouldFindByVenueExecutionId() {
        RequestModel.TransactionSearch search = builder()
            .venueExecutionId(t4.getVenueExecutionId())
            .build();

        searchAndVerifyResults(search, t4);
    }

    @Test
    void shouldFindByTransactionUuid() {
        RequestModel.TransactionSearch search = builder()
            .uuids(t2.getUuid(), t3.getUuid())
            .build();

        searchAndVerifyResults(search, t3, t2);
    }

    @Test
    void shouldFindByReservationRef() {
        RequestModel.TransactionSearch search = builder()
            .reservationRef(t6.getReservationRef())
            .build();

        searchAndVerifyResults(search, t6);
    }

    @Test
    void shouldFindByDateSingleTransaction() {
        RequestModel.TransactionSearch search = builder()
            .dateRange(t1.getDateTime(), t2.getDateTime())
            .build();

        searchAndVerifyResults(search, t1);
    }

    @Test
    void shouldFindByDateMultipleTransactions() {
        RequestModel.TransactionSearch search = builder()
            .dateRange(t1.getDateTime(), t11.getDateTime())
            .build();

        searchAndVerifyResults(search, t10, t9, t8, t7, t6, t5, t4, t3, t2, t1);
    }

    @Test
    void shouldFindByDateNoTransactions() {
        RequestModel.TransactionSearch search = builder()
            .dateRange(t1.getDateTime().plusSeconds(1), t2.getDateTime())
            .build();

        searchAndVerifyEmptyResults(search);
    }

    @Test
    void shouldFindBySettlementDateSingleTransaction() {
        RequestModel.TransactionSearch search = builder()
            .settlementDateRange(t1.getSettlementDateTime(), t2.getSettlementDateTime())
            .build();

        searchAndVerifyResults(search, t1);
    }

    @Test
    void shouldFindBySettlementDateMultipleTransactions() {
        RequestModel.TransactionSearch search = builder()
            .settlementDateRange(t1.getSettlementDateTime(), t11.getSettlementDateTime())
            .build();

        searchAndVerifyResults(search, t10, t9, t8, t7, t6, t5, t4, t3, t2, t1);
    }

    @Test
    void shouldFindBySettlementDateNoTransactions() {
        RequestModel.TransactionSearch search = builder()
            .settlementDateRange(t1.getSettlementDateTime().plusSeconds(1), t2.getSettlementDateTime())
            .build();

        searchAndVerifyEmptyResults(search);
    }

    @Test
    void shouldFindSettledTransactionsOnly() {
        RequestModel.TransactionSearch search = builder()
            .settled(true)
            .build();

        searchAndVerifyResults(search, t10, t9, t8, t7, t6, t5, t4, t3, t2, t1);
    }

    @Test
    void shouldFindUnsettledTransactionsOnly() {
        Transaction unsettled = t5.toBuilder()
            .isSettled(false)
            .build();

        transactionRepository.save(unsettled);

        RequestModel.TransactionSearch search = builder()
            .settled(false)
            .build();

        searchAndVerifyResults(search, unsettled);
    }

    @Test
    void shouldFindByMultipleFilters() {
        RequestModel.TransactionSearch search = builder()
            .portfolio(t2.getPortfolioId())
            .accountId(t2.getAccountId())
            .currency(t2.getBaseCurrency())
            .executionId(t2.getExecutionId())
            .venueExecutionId(t2.getVenueExecutionId())
            .orderId(t2.getOrderId())
            .parentOrderId(t2.getParentOrderId())
            .rootOrderId(t2.getRootOrderId())
            .build();

        searchAndVerifyResults(search, t2);
    }

    @Test
    void shouldFindByNarrowingFilters() {
        RequestModel.TransactionSearch search1 = builder()
            .currency("USD")
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search1, t1, t2, t3, t4, t5, t10);

        RequestModel.TransactionSearch search2 = builder()
            .currency("USD")
            .dateRange(t1.getDateTime(), t4.getDateTime())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search2, t1, t2, t3);

        RequestModel.TransactionSearch search3 = builder()
            .currency("USD")
            .dateRange(t1.getDateTime(), t4.getDateTime())
            .portfolio(t3.getPortfolioId())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(search3, t3);
    }

    @Test
    void shouldSortResults() {
        RequestModel.TransactionSearch descSearch = builder()
            .sortingOrder(RequestModel.SortingOrder.DESC)
            .build();

        searchAndVerifyResults(descSearch, t10, t9, t8, t7, t6, t5, t4, t3, t2, t1);

        RequestModel.TransactionSearch ascSearch = builder()
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(ascSearch, t1, t2, t3, t4, t5, t6, t7, t8, t9, t10);
    }

    @Test
    void shouldFindByAccountId() {
        RequestModel.TransactionSearch search = builder()
            .accountId(t2.getAccountId())
            .build();

        searchAndVerifyResults(search, t2);
    }

    @Test
    void shouldFindByPortfolioId() {
        RequestModel.TransactionSearch search = builder()
            .portfolio(t1.getPortfolioId())
            .build();

        searchAndVerifyResults(search, t1);
    }

    @Test
    void shouldFindByPortfolioAndAccountId() {
        RequestModel.TransactionSearch search = builder()
            .portfolio(t2.getPortfolioId())
            .accountId(t2.getAccountId())
            .build();

        searchAndVerifyResults(search, t2);
    }

    @Test
    void shouldExcludeTransactionForCancelledOrders() {
        Transaction cancelled = t1.toBuilder()
            .execType(ExecType.CANCELED)
            .build();

        Transaction expired = t2.toBuilder()
            .execType(ExecType.EXPIRED)
            .build();

        Transaction rejected = t3.toBuilder()
            .execType(ExecType.REJECTED)
            .build();

        transactionRepository.save(cancelled);
        transactionRepository.save(expired);
        transactionRepository.save(rejected);

        RequestModel.TransactionSearch search = builder()
            .build();

        // no cancelled, no expired, no rejected
        searchAndVerifyResults(search, t10, t9, t8, t7, t6, t5, t4, t3, t2, t1);
    }

    @Test
    void shouldPaginateResults() {
        RequestModel.TransactionSearch search = builder()
            .currency("USD")
            .sortingOrder(RequestModel.SortingOrder.DESC)
            .first(3)
            .build();

        PaginationModel.CursorConnection<Transaction> connection = lookup(search);

        assertThat(connection.pageInfo().hasNextPage()).isTrue();
        assertThat(connection.pageInfo().pageSize()).isEqualTo(3);
        String endCursor = connection.pageInfo().endCursor();
        assertThat(endCursor).isEqualTo(Long.toString(t4.getId()));
        verifyTransactions(connection, t10, t5, t4);

        RequestModel.TransactionSearch afterSearch = builder()
            .currency("USD")
            .sortingOrder(RequestModel.SortingOrder.DESC)
            .first(3)
            .after(endCursor)
            .build();

        PaginationModel.CursorConnection<Transaction> afterConnection = lookup(afterSearch);

        assertThat(afterConnection.pageInfo().hasNextPage()).isFalse();
        assertThat(afterConnection.pageInfo().pageSize()).isEqualTo(3);
        assertThat(afterConnection.pageInfo().endCursor()).isEqualTo(Long.toString(t1.getId()));
        verifyTransactions(afterConnection, t3, t2, t1);
    }

    @Test
    void shouldReturnNothingWhenNoAccess() {
        accessGatewayMockClient.revokePermissions();

        RequestModel.TransactionSearch search = builder()
            .build();

        searchAndVerifyEmptyResults(search);
    }

    @Test
    void shouldReturnAuthorizedTransactionsWithStaticPortfolioAccess() {
        accessGatewayMockClient.revokePermissions();
        accessGatewayMockClient.grantStaticPortfolioPermission("read");

        RequestModel.TransactionSearch search = builder()
            .build();

        // transactions between portfolios (client trades, portfolio transfers)
        searchAndVerifyResults(search, t8, t7, t3, t1);
    }

    @Test
    void shouldReturnAuthorizedTransactionsWithStaticAccountAccess() {
        accessGatewayMockClient.revokePermissions();
        accessGatewayMockClient.grantStaticVenueAccountPermission("read");

        RequestModel.TransactionSearch search = builder()
            .build();

        // transactions between accounts (account transfers)
        searchAndVerifyResults(search, t9);
    }

    @Test
    void shouldReturnAuthorizedTransactionsWithStaticPortfolioAndAccountAccess() {
        accessGatewayMockClient.revokePermissions();
        accessGatewayMockClient.grantStaticPortfolioPermission("read");
        accessGatewayMockClient.grantStaticVenueAccountPermission("read");

        RequestModel.TransactionSearch search = builder()
            .build();

        // all transactions
        searchAndVerifyResults(search, t10, t9, t8, t7, t6, t5, t4, t3, t2, t1);
    }

    @Test
    void shouldReturnSingleTransactionWithStaticPortfolioAccess() {
        accessGatewayMockClient.revokePermissions();
        accessGatewayMockClient.grantStaticPortfolioPermission("read");

        RequestModel.TransactionSearch search = builder()
            .portfolio(t1.getPortfolioId())
            .build();

        searchAndVerifyResults(search, t1);
    }

    @Test
    void shouldReturnSingleTransactionWithStaticAccountAccess() {
        // grant access to single portoflio
        setupPermissions(List.of(t2.getPortfolioId()), NO_ACCESS);

        // grant access to all accounts
        accessGatewayMockClient.grantStaticVenueAccountPermission("read");

        RequestModel.TransactionSearch search = builder()
            .accountId(t2.getAccountId())
            .build();

        searchAndVerifyResults(search, t2);
    }

    // TEST WALLETS static permissions
    // TEST wallet vostro / nostro single permissions

    @ParameterizedTest
    @MethodSource("searchAllScenarios")
    void shouldReturnOnlyAuthorizedTransactionsWithRequestAll(List<String> portfolioPermissions, List<String> accountPermissions, List<Transaction> expectedTransactions) {
        setupPermissions(portfolioPermissions, accountPermissions);

        RequestModel.TransactionSearch search = builder()
            .build();

        searchAndVerifyResults(search, expectedTransactions.toArray(Transaction[]::new));
    }

    private static Stream<Arguments> searchAllScenarios() {
        // set up is not called in a static context
        setupTransactions();

        return Stream.of(
            // client trade
            arguments(List.of(t1.getPortfolioId()), NO_ACCESS, List.of(t1)),

            // client trade (missing portfolio or counter portfolio)
            arguments(NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            // street trade
            arguments(List.of(t2.getPortfolioId()), List.of(t2.getAccountId()), List.of(t2)),

            // street trade (missing portfolio)
            arguments(NO_ACCESS, List.of(t2.getAccountId()), EMPTY_RESPONSE),

            // payment (deposit / withdrawal)
            arguments(List.of(t5.getPortfolioId()), List.of(t5.getAccountId()), List.of(t5)),

            // payment (missing account)
            arguments(List.of(t5.getPortfolioId()), NO_ACCESS, EMPTY_RESPONSE),

            // portfolio transfer
            arguments(List.of(t7.getTargetPortfolioId()), NO_ACCESS, List.of(t7)),

            // portfolio transfer (missing source or target portfolio)
            arguments(NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            // account transfer
            arguments(List.of(t9.getFeePortfolioId()), List.of(t9.getSourceAccountId(), t9.getTargetAccountId()), List.of(t9)),

            // account transfer (missing source account)
            arguments(List.of(t9.getFeePortfolioId()), List.of(t9.getTargetAccountId()), EMPTY_RESPONSE),

            // fee
            arguments(List.of(t10.getPortfolioId()), List.of(t10.getAccountId()), List.of(t10)),

            // fee (missing account)
            arguments(List.of(t10.getPortfolioId()), NO_ACCESS, EMPTY_RESPONSE),

            // settlement (has no reference to portfolio nor account, so it is always accessible as long as you have any permissions)
            arguments(NO_ACCESS, List.of("other-account"), EMPTY_RESPONSE)
        );
    }

    @ParameterizedTest
    @MethodSource("searchSpecificScenarios")
    void shouldReturnOnlyAuthorizedTransactionsWithSpecificRequest(String searchPortfolio, String searchAccount, List<String> portfolioPermissions, List<String> accountPermissions, List<Transaction> expectedTransactions) {
        setupPermissions(portfolioPermissions, accountPermissions);

        RequestModel.TransactionSearch search = builder()
            .build();

        if (isNotBlank(searchPortfolio)) {
            search = search.withPortfolio(searchPortfolio);
        }

        if (isNotBlank(searchAccount)) {
            search = search.withAccount(searchAccount);
        }

        searchAndVerifyResults(search, expectedTransactions.toArray(Transaction[]::new));
    }

    private static Stream<Arguments> searchSpecificScenarios() {
        // set up is not called in a static context
        setupTransactions();

        return Stream.of(
            // client trade by portfolioId
            arguments(t1.getCounterPortfolioId(), "", List.of(t1.getCounterPortfolioId()), NO_ACCESS, List.of(t1)),

            // client trade (missing portfolio or counter-portfolio)
            arguments(t1.getPortfolioId(), "", NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            // street trade by accountId
            arguments("", t2.getAccountId(), List.of(t2.getPortfolioId()), List.of(t2.getAccountId()), List.of(t2)),

            // street trade (missing portfolio)
            arguments("", t2.getAccountId(), NO_ACCESS, List.of(t2.getAccountId()), EMPTY_RESPONSE),

            // payment (deposit / withdrawal) by portfolioId
            arguments(t5.getPortfolioId(), "", List.of(t5.getPortfolioId()), List.of(t5.getAccountId()), List.of(t5)),

            // payment (missing account)
            arguments(t5.getPortfolioId(), "", List.of(t5.getPortfolioId()), NO_ACCESS, EMPTY_RESPONSE),

            // portfolio transfer by sourcePortfolioId
            arguments(t7.getSourcePortfolioId(), "", List.of(t7.getSourcePortfolioId(), t7.getTargetPortfolioId()), NO_ACCESS, List.of(t7)),

            // portfolio transfer (missing source or target portfolio)
            arguments(t7.getSourcePortfolioId(), "", NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            // account transfer by targetAccountId
            arguments("", t9.getTargetAccountId(), NO_ACCESS, List.of(t9.getSourceAccountId(), t9.getTargetAccountId()), List.of(t9)),

            // account transfer (missing source account)
            arguments("", t9.getTargetAccountId(), NO_ACCESS, List.of(t9.getTargetAccountId()), EMPTY_RESPONSE),

            // fee by portfolioId
            arguments(t10.getPortfolioId(), "", List.of(t10.getPortfolioId()), List.of(t10.getAccountId()), List.of(t10)),

            // fee (missing account)
            arguments(t10.getPortfolioId(), "", List.of(t10.getPortfolioId()), NO_ACCESS, EMPTY_RESPONSE),

            // settlement (has no reference to portfolio nor account, so it is always accessible as long as you have any permissions)
            arguments("other-id", "", NO_ACCESS, OTHER_ACCESS, EMPTY_RESPONSE)
        );
    }

    public static Transaction.TransactionBuilder createTransaction(long sequenceNumber) {
        TransactionFeeType feeType = RandomEnumUtils.randomEnum(TransactionFeeType.class, TransactionFeeType.FEE_TYPE_UNSPECIFIED);

        TransactionFee fee = new TransactionFee(
            RandomNumberUtils.randomNumber(100, 2),
            "DOGE",
            "test-fee-" + sequenceNumber,
            feeType);

        ZonedDateTime base = LocalDate.parse("2025-01-01").atStartOfDay(DateUtils.UTC);
        ZonedDateTime dateTime = base.plusDays(sequenceNumber);
        ZonedDateTime settlementDateTime = dateTime.plusHours(8);

        return Transaction.builder()
            .uuid(RandomStringUtils.randomUuid())
            .reservationRef("reservation-ref-" + sequenceNumber)
            .sequenceNumber(sequenceNumber)
            .executionId("execution-id-" + sequenceNumber)
            .venueExecutionId("venue-execution-id-" + sequenceNumber)
            .rootExecutionId("root-execution-id-" + sequenceNumber)
            .underlyingExecutionId("underlying-execution-id-" + sequenceNumber)
            .orderId("order-id-" + sequenceNumber)
            .parentOrderId("parent-order-id-" + sequenceNumber)
            .rootOrderId("root-order-id-" + sequenceNumber)
            .dateTime(dateTime)
            .isSettled(true)
            .settlementType(SettlementType.DEFERRED_SETTLEMENT)
            .settlementDateTime(settlementDateTime)
            .settlementId("settlement-id-" + sequenceNumber)
            .execType(ExecType.FILL)
            .description(RandomStringUtils.randomStringWithPrefix("description"))
            .addFee(fee);
    }

    private PaginationModel.CursorConnection<Transaction> lookup(RequestModel.TransactionSearch search) {
        RequestModel.TransactionSearch clientRequest = search.withClientId("client-id");

        PaginationModel.CursorConnection<Transaction> connection = transactionService.search(clientRequest);

        LOGGER.info("Connection response: {}", connection);

        if (connection.edges().isEmpty()) {
            LOGGER.info("No transactions found");
        } else {
            List<String> transactionRefs = connection.getAllNodes().stream()
                .map(Transaction::getReservationRef)
                .toList();

            LOGGER.info("Transactions in response: {}", transactionRefs);
        }

        return connection;
    }

    private void searchAndVerifyResults(RequestModel.TransactionSearch search, Transaction... expectedTransactions) {
        PaginationModel.CursorConnection<Transaction> connection = lookup(search);

        verifyTransactions(connection, expectedTransactions);
        verifyPageInfo(connection, expectedTransactions.length);
    }

    private void searchAndVerifyEmptyResults(RequestModel.TransactionSearch search) {
        PaginationModel.CursorConnection<Transaction> connection = lookup(search);

        verifyTransactions(connection);
        verifyPageInfo(connection, 0);
    }

    private static void verifyTransactions(PaginationModel.CursorConnection<Transaction> connection, Transaction... expectedTransactions) {
        String[] expectedRefs = Arrays.stream(expectedTransactions)
            .map(Transaction::getReservationRef)
            .toArray(String[]::new);

        assertThat(connection.getAllNodes())
            .extracting("reservationRef")
            .containsExactly(expectedRefs);
    }

    private static void setupTransactions() {
        t1 = createTransaction(1)
            .reservationRef("t1")
            .portfolioId("portfolio-1")
            .counterPortfolioId("counter-portfolio-1")
            .baseCurrency("BTC")
            .currency("USD")
            .quantity(bd(0.0001))
            .price(bd(60_000))
            .transactionType(TransactionType.CLIENT_CASH_TRADE)
            .portfolioType(PortfolioType.VOSTRO)
            .counterPortfolioType(PortfolioType.NOSTRO)
            .build();

        t2 = createTransaction(2)
            .reservationRef("t2")
            .portfolioId("portfolio-2")
            .accountId("account-2")
            .baseCurrency("BTC")
            .currency("USD")
            .quantity(bd(0.0001))
            .price(bd(60_000))
            .transactionType(TransactionType.STREET_CASH_TRADE)
            .portfolioType(PortfolioType.VOSTRO)
            .accountType(AccountType.EXCHANGE)
            .build();

        t3 = createTransaction(3)
            .reservationRef("t3")
            .portfolioId("portfolio-3")
            .counterPortfolioId("counter-portfolio-3")
            .asset("BTC/USD")
            .currency("USD")
            .quantity(bd(0.0001))
            .price(bd(60_000))
            .transactionType(TransactionType.CLIENT_ASSET_TRADE)
            .portfolioType(PortfolioType.VOSTRO)
            .counterPortfolioType(PortfolioType.NOSTRO)
            .build();

        t4 = createTransaction(4)
            .reservationRef("t4")
            .portfolioId("portfolio-4")
            .accountId("account-4")
            .asset("BTC/USD")
            .currency("USD")
            .quantity(bd(0.0001))
            .price(bd(60_000))
            .transactionType(TransactionType.STREET_ASSET_TRADE)
            .portfolioType(PortfolioType.VOSTRO)
            .accountType(AccountType.EXCHANGE)
            .build();

        t5 = createTransaction(5)
            .reservationRef("t5")
            .portfolioId("portfolio-5")
            .accountId("account-5")
            .feePortfolioId("fee-portfolio-5")
            .feeAccountId("fee-account-5")
            .currency("USD")
            .quantity(bd(1_000))
            .transactionType(TransactionType.DEPOSIT)
            .portfolioType(PortfolioType.NOSTRO)
            .accountType(AccountType.WALLET)
            .accountWalletType(RequestModel.WalletType.NOSTRO)
            .feePortfolioType(PortfolioType.NOSTRO)
            .feeAccountType(AccountType.WALLET)
            .feeAccountWalletType(RequestModel.WalletType.NOSTRO)
            .build();

        t6 = createTransaction(6)
            .reservationRef("t6")
            .portfolioId("portfolio-6")
            .accountId("account-6")
            .feePortfolioId("fee-portfolio-6")
            .feeAccountId("fee-account-6")
            .currency("EUR")
            .quantity(bd(500))
            .transactionType(TransactionType.WITHDRAWAL)
            .portfolioType(PortfolioType.VOSTRO)
            .accountType(AccountType.WALLET)
            .accountWalletType(RequestModel.WalletType.VOSTRO)
            .feePortfolioType(PortfolioType.VOSTRO)
            .feeAccountType(AccountType.WALLET)
            .feeAccountWalletType(RequestModel.WalletType.VOSTRO)
            .build();

        t7 = createTransaction(7)
            .reservationRef("t7")
            .sourcePortfolioId("source-portfolio-7")
            .targetPortfolioId("target-portfolio-7")
            .feePortfolioId("fee-portfolio-7")
            .currency("DOGE")
            .quantity(bd(1_000))
            .transactionType(TransactionType.PORTFOLIO_CASH_TRANSFER)
            .sourcePortfolioType(PortfolioType.VOSTRO)
            .targetPortfolioType(PortfolioType.VOSTRO)
            .feePortfolioType(PortfolioType.VOSTRO)
            .build();

        t8 = createTransaction(8)
            .reservationRef("t8")
            .sourcePortfolioId("source-portfolio-8")
            .targetPortfolioId("target-portfolio-8")
            .feePortfolioId("fee-portfolio-8")
            .asset("BTC/EUR")
            .quantity(bd(0.8))
            .transactionType(TransactionType.PORTFOLIO_ASSET_TRANSFER)
            .sourcePortfolioType(PortfolioType.VOSTRO)
            .targetPortfolioType(PortfolioType.VOSTRO)
            .feePortfolioType(PortfolioType.VOSTRO)
            .build();

        t9 = createTransaction(9)
            .reservationRef("t9")
            .sourceAccountId("source-account-9")
            .targetAccountId("target-account-9")
            .feePortfolioId("fee-portfolio-9")
            .feeAccountId("fee-account-9")
            .currency("EUR")
            .quantity(bd(500))
            .transactionType(TransactionType.ACCOUNT_CASH_TRANSFER)
            .sourceAccountType(AccountType.WALLET)
            .sourceAccountWalletType(RequestModel.WalletType.VOSTRO)
            .targetAccountType(AccountType.CLOB)
            .targetAccountWalletType(RequestModel.WalletType.NOSTRO)
            .feeAccountType(AccountType.WALLET)
            .feeAccountWalletType(RequestModel.WalletType.VOSTRO)
            .feePortfolioType(PortfolioType.VOSTRO)
            .build();

        t10 = createTransaction(10)
            .reservationRef("t10")
            .portfolioId("portfolio-10")
            .accountId("account-10")
            .currency("USD")
            .quantity(bd(10))
            .transactionType(TransactionType.FEE)
            .portfolioType(PortfolioType.VOSTRO)
            .accountType(AccountType.EXCHANGE)
            .build();

        t11 = createTransaction(11)
            .reservationRef("t11")
            .transactionType(TransactionType.SETTLEMENT)
            .build();
    }

    public Set<PermissionDto> setupPermissions() {
        return setupPermissions(t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11);
    }

    public Set<PermissionDto> setupPermissions(Transaction... transactions) {
        List<String> portfolioIds = Stream.of(transactions)
            .flatMap(transaction -> Stream.of(
                transaction.getPortfolioId(),
                transaction.getCounterPortfolioId(),
                transaction.getFeePortfolioId(),
                transaction.getSourcePortfolioId(),
                transaction.getTargetPortfolioId()))
            .filter(StringUtils::isNotBlank)
            .toList();

        List<String> accountIds = Stream.of(transactions)
            .flatMap(transaction -> Stream.of(
                transaction.getAccountId(),
                transaction.getFeeAccountId(),
                transaction.getSourceAccountId(),
                transaction.getTargetAccountId()))
            .filter(StringUtils::isNotBlank)
            .toList();

        return setupPermissions(portfolioIds, accountIds);
    }
}
