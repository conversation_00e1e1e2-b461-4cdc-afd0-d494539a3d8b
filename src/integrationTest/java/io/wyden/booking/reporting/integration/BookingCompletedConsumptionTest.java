package io.wyden.booking.reporting.integration;

import io.wyden.booking.reporting.application.LedgerEntryService;
import io.wyden.booking.reporting.application.ReservationService;
import io.wyden.booking.reporting.application.TransactionService;
import io.wyden.booking.reporting.application.balance.BalanceCommandService;
import io.wyden.booking.reporting.integration.producer.BookingCompletedMessageProducer;
import io.wyden.published.booking.BalanceSnapshot;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.DepositReservationSnapshot;
import io.wyden.published.booking.LedgerEntrySnapshot;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.oems.OemsExecType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.List;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

public class BookingCompletedConsumptionTest extends TestContainersIntegrationBase {

    BookingCompletedMessageProducer bookingCompletedMessageProducer;

    @SpyBean
    BalanceCommandService balanceCommandService;

    @SpyBean
    LedgerEntryService ledgerEntryService;

    @SpyBean
    ReservationService reservationService;

    @SpyBean
    TransactionService transactionService;

    @BeforeEach
    void setUp() {
        bookingCompletedMessageProducer = new BookingCompletedMessageProducer(rabbitIntegrator);
    }

    @Test
    void bookingCompletedWithReservationIsConsumed() {
        // given
        ReservationSnapshot reservationSnapshot = ReservationSnapshot.newBuilder()
            .setDepositReservation(DepositReservationSnapshot.newBuilder()
                .setAccountId("accountId")
                .setPortfolioId("portfolioId")
                .setCurrency("ETH")
                .setQuantity("2")
                .build())
            .build();

        BookingCompleted bookingCompleted = BookingCompleted.newBuilder()
            .setSequenceNumber(1)
            .setReservationSnapshot(reservationSnapshot)
            .build();

        // when
        bookingCompletedMessageProducer.emit(bookingCompleted);

        // then
        verify(reservationService, timeout(500)).accept(eq(bookingCompleted), eq(reservationSnapshot));
    }

    @Test
    void bookingCompletedWithTransactionIsConsumed() {
        // given
        TransactionSnapshot transactionSnapshot = TransactionSnapshot.newBuilder()
            .setStreetCashTrade(StreetCashTradeSnapshot.newBuilder()
                .setBaseCurrency("BTC")
                .setQuantity("1")
                .setOrderId("order-a")
                .setExecType(OemsExecType.FILL)
                .setPortfolio("portfolio1")
                .setVenueAccount("account2")
                .build())
            .build();

        BookingCompleted bookingCompleted = BookingCompleted.newBuilder()
            .setSequenceNumber(1)
            .setTransactionCreated(transactionSnapshot)
            .build();

        // when
        bookingCompletedMessageProducer.emit(bookingCompleted);

        // then
        verify(transactionService, timeout(500)).accept(eq(bookingCompleted), eq(transactionSnapshot));
    }

    @Test
    void bookingCompletedWithLEdgerEntriesIsConsumed() {
        // given
        LedgerEntrySnapshot ledgerEntrySnapshot = LedgerEntrySnapshot.newBuilder()
            .setAccount("account1")
            .setQuantity("2")
            .setSettled(true)
            .build();

        BookingCompleted bookingCompleted = BookingCompleted.newBuilder()
            .setSequenceNumber(1)
            .addLedgerEntryCreated(ledgerEntrySnapshot)
            .build();

        // when
        bookingCompletedMessageProducer.emit(bookingCompleted);

        // then
        verify(ledgerEntryService, timeout(500)).accept(eq(bookingCompleted), eq(List.of(ledgerEntrySnapshot)));
    }

    @Test
    void bookingCompletedWithBalanceSnapshotIsConsumed() {
        // given
        BalanceSnapshot balanceSnapshot = BalanceSnapshot.newBuilder()
            .setAccountId("account1")
            .setCurrency("DOGE")
            .setQuantity("4")
            .setPendingQuantity("54")
            .setAvailableForTradingQuantity("1")
            .setAvailableForWithdrawalQuantity("0")
            .build();
        BookingCompleted bookingCompleted = BookingCompleted.newBuilder()
            .setSequenceNumber(1)
            .addBalanceSnapshot(balanceSnapshot)
            .build();

        // when
        bookingCompletedMessageProducer.emit(bookingCompleted);

        // then
        verify(balanceCommandService, timeout(500)).accept(eq(bookingCompleted), eq(List.of(balanceSnapshot)));
    }
}
