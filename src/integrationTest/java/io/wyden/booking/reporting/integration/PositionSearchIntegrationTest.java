package io.wyden.booking.reporting.integration;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.position.PositionQueryService;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntryRepository;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntryType;
import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.booking.reporting.domain.position.PositionRepository;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloudutils.tools.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static io.wyden.booking.reporting.interfaces.rest.RequestModel.PositionSearch.builder;
import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.slf4j.LoggerFactory.getLogger;

public class PositionSearchIntegrationTest extends SearchIntegrationTest {

    private static final Logger LOGGER = getLogger(PositionSearchIntegrationTest.class);

    @Autowired
    private PositionQueryService positionQueryService;

    @Autowired
    private PositionRepository positionRepository;

    @Autowired
    private LedgerEntryRepository ledgerEntryRepository;

    private static Position p1;
    private static Position p2;
    private static Position p3;
    private static Position p4;
    private static Position p5;
    private static Position p6;

    private static LedgerEntry le1;
    private static LedgerEntry le2;
    private static LedgerEntry le3;

    @BeforeEach
    void setUp() throws Exception {
        setupPositions();
        setupPermissions();

        // setupPositions has to be static, because it could be used in @ParametrizedTest
        // saving in repository has to happen in non-static block
        positionRepository.save(p1);
        positionRepository.save(p2);
        positionRepository.save(p3);
        positionRepository.save(p4);
        positionRepository.save(p5);
        positionRepository.save(p6);

        ledgerEntryRepository.save(le1);
        ledgerEntryRepository.save(le2);
        ledgerEntryRepository.save(le3);

        DB.logTableContent("position");
        DB.logTableContent("ledger_entry");
    }

    @AfterEach
    void tearDown() throws Exception {
        DB.clearTable("position");
        DB.clearTable("ledger_entry");
    }

    @Test
    void shouldReturnNothing() throws Exception {
        DB.clearTable("position");

        RequestModel.PositionSearch searchRequest = RequestModel.PositionSearch.builder()
            .build();

        searchAndVerifyEmptyResults(searchRequest);
    }

    @Test
    void shouldReturnAll() {
        RequestModel.PositionSearch searchRequest = RequestModel.PositionSearch.builder()
            .build();

        searchAndVerifyResults(searchRequest, p1, p2, p3, p4, p5, p6);
    }

    @Test
    void shouldFindBySymbol() {
        RequestModel.PositionSearch searchRequest = builder()
            .symbol(p4.getSymbol(), p6.getSymbol())
            .build();

        searchAndVerifyResults(searchRequest, p4, p6);
    }

    @Test
    void shouldFindByCurrency() {
        RequestModel.PositionSearch searchRequest = builder()
            .currency(p5.getCurrency())
            .build();

        searchAndVerifyResults(searchRequest, p5);
    }

    @Test
    void shouldFindByMultipleFilters() {
        RequestModel.PositionSearch searchRequest = builder()
            .portfolio(p2.getPortfolioId())
            .currency(p2.getCurrency())
            .build();

        searchAndVerifyResults(searchRequest, p2);
    }

    @Test
    void shouldFindByNarrowingFilters() {
        RequestModel.PositionSearch firstSearchRequest = builder()
            .currency("EUR")
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(firstSearchRequest, p2, p4, p6);

        RequestModel.PositionSearch secondSearchRequest = builder()
            .currency("EUR")
            .accountId(p6.getAccountId())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(secondSearchRequest, p6);
    }

    @Test
    void shouldSortResults() {
        RequestModel.PositionSearch descendingSearchRequest = builder()
            .sortingOrder(RequestModel.SortingOrder.DESC)
            .build();

        searchAndVerifyResults(descendingSearchRequest, p6, p5, p4, p3, p2, p1);

        RequestModel.PositionSearch ascendingSearchRequest = builder()
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(ascendingSearchRequest, p1, p2, p3, p4, p5, p6);
    }

    @Test
    void shouldFindByAccountId() {
        RequestModel.PositionSearch searchRequest = builder()
            .accountId(p3.getAccountId())
            .build();

        searchAndVerifyResults(searchRequest, p3);
    }

    @Test
    void shouldFindByPortfolioId() {
        RequestModel.PositionSearch searchRequest = builder()
            .portfolio(p1.getPortfolioId())
            .build();

        searchAndVerifyResults(searchRequest, p1);
    }

    @Test
    void shouldFindByPortfolioAndAccountId() {
        RequestModel.PositionSearch searchRequest = builder()
            .portfolio(p2.getPortfolioId())
            .accountId(p3.getAccountId())
            .build();

        searchAndVerifyResults(searchRequest, p2, p3);
    }

    @Test
    void shouldPaginateResults() {
        RequestModel.PositionSearch searchRequest = builder()
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .first(3)
            .build();

        PaginationModel.CursorConnection<Position> connection = lookup(searchRequest);

        assertThat(connection.pageInfo().hasNextPage()).isTrue();
        assertThat(connection.pageInfo().pageSize()).isEqualTo(3);
        String endCursor = connection.pageInfo().endCursor();
        assertThat(endCursor).isEqualTo(Long.toString(p3.getId()));
        verifyPositions(connection, p1, p2, p3);

        RequestModel.PositionSearch afterSearchRequest = builder()
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .first(20)
            .after(endCursor)
            .build();

        PaginationModel.CursorConnection<Position> afterConnection = lookup(afterSearchRequest);

        assertThat(afterConnection.pageInfo().hasNextPage()).isFalse();
        assertThat(afterConnection.pageInfo().pageSize()).isEqualTo(3);
        assertThat(afterConnection.pageInfo().endCursor()).isEqualTo(Long.toString(p6.getId()));
        verifyPositions(afterConnection, p4, p5, p6);
    }

    @Test
    void shouldFindBySymbolAndPortfolio() {
        RequestModel.PositionSearch searchRequest = builder()
            .symbol(p1.getSymbol())
            .portfolio(p1.getPortfolioId())
            .build();

        searchAndVerifyResults(searchRequest, p1);
    }

    @Test
    void shouldFindBySymbolAndAccount() {
        RequestModel.PositionSearch searchRequest = builder()
            .symbol(p3.getSymbol())
            .accountId(p3.getAccountId())
            .build();

        searchAndVerifyResults(searchRequest, p3);
    }

    @Test
    void shouldFindByMultipleSymbols() {
        RequestModel.PositionSearch searchRequest = builder()
            .symbol(p1.getSymbol(), p2.getSymbol())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(searchRequest, p1, p2);
    }

    @Test
    void shouldFindByMultipleCurrencies() {
        RequestModel.PositionSearch searchRequest = builder()
            .currency(p1.getCurrency(), p5.getCurrency())
            .sortingOrder(RequestModel.SortingOrder.ASC)
            .build();

        searchAndVerifyResults(searchRequest, p1, p5);
    }

    @Test
    void shouldFindByOrderId() {
        RequestModel.PositionSearch searchRequest = builder()
            .orderId(le1.getReservationRef())
            .build();

        searchAndVerifyResults(searchRequest, p1);
    }

    @Test
    void shouldFindByOrderIdAndSymbol() {
        RequestModel.PositionSearch searchRequest = builder()
            .orderId(le1.getReservationRef())
            .symbol(p1.getSymbol())
            .build();

        searchAndVerifyResults(searchRequest, p1);
    }

    @Test
    void shouldNotFindByOrderIdWithWrongSymbol() {
        RequestModel.PositionSearch searchRequest = builder()
            .orderId(le1.getReservationRef())
            .symbol(p2.getSymbol()) // Wrong symbol for this orderId
            .build();

        searchAndVerifyEmptyResults(searchRequest);
    }

    @Test
    void shouldNotFindByNonExistentOrderId() {
        RequestModel.PositionSearch searchRequest = builder()
            .orderId("non-existent-order")
            .build();

        searchAndVerifyEmptyResults(searchRequest);
    }

    @Test
    void shouldFindByOrderIdAndPortfolioType() {
        RequestModel.PositionSearch searchRequest = builder()
            .orderId(le2.getReservationRef())
            .portfolioType(p2.getPortfolioType())
            .build();

        searchAndVerifyResults(searchRequest, p2);
    }

    @ParameterizedTest
    @MethodSource("searchAllScenarios")
    void shouldReturnOnlyAuthorizedPositionsWithRequestAll(List<String> portfolioPermissions, List<String> accountPermissions, List<Position> expectedPositions) {
        setupPermissions(portfolioPermissions, accountPermissions);

        RequestModel.PositionSearch searchRequest = RequestModel.PositionSearch.builder()
            .build();

        searchAndVerifyResults(searchRequest, expectedPositions.toArray(Position[]::new));
    }

    private static Stream<Arguments> searchAllScenarios() {
        // set up is not called in a static context
        setupPositions();

        return Stream.of(
            arguments(List.of(p1.getPortfolioId()), NO_ACCESS, List.of(p1)),
            arguments(NO_ACCESS, List.of(p3.getAccountId()), List.of(p3)),
            arguments(List.of(p1.getPortfolioId(), p2.getPortfolioId()), List.of(p3.getAccountId()), List.of(p1, p2, p3)),
            arguments(List.of(p2.getPortfolioId(), p1.getPortfolioId()), NO_ACCESS, List.of(p1, p2)),
            arguments(NO_ACCESS, List.of(p3.getAccountId(), p4.getAccountId()), List.of(p3, p4)),

            // no permissions at all
            arguments(NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE)
        );
    }

    @ParameterizedTest
    @MethodSource("searchSpecificScenarios")
    void shouldReturnOnlyAuthorizedPositionsWithSpecificRequest(String searchPortfolio, String searchAccount, List<String> portfolioPermissions, List<String> accountPermissions, List<Position> expectedPositions) {
        setupPermissions(portfolioPermissions, accountPermissions);

        RequestModel.PositionSearch searchRequest = RequestModel.PositionSearch.builder()
            .build();

        if (isNotBlank(searchPortfolio)) {
            searchRequest = searchRequest.toBuilder()
                .portfolio(searchPortfolio)
                .build();
        }

        if (isNotBlank(searchAccount)) {
            searchRequest = searchRequest.toBuilder()
                .accountId(searchAccount)
                .build();
        }

        searchAndVerifyResults(searchRequest, expectedPositions.toArray(Position[]::new));
    }

    private static Stream<Arguments> searchSpecificScenarios() {
        // set up is not called in a static context
        setupPositions();

        return Stream.of(
            arguments(p1.getPortfolioId(), "", List.of(p1.getPortfolioId()), NO_ACCESS, List.of(p1)),
            arguments(p1.getPortfolioId(), "", NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            arguments("", p3.getAccountId(), NO_ACCESS, List.of(p3.getAccountId()), List.of(p3)),
            arguments("", p3.getAccountId(), NO_ACCESS, NO_ACCESS, EMPTY_RESPONSE),

            arguments(p1.getPortfolioId(), p3.getAccountId(), List.of(p1.getPortfolioId()), List.of(p3.getAccountId()), List.of(p1, p3)),
            arguments(p4.getPortfolioId(), p3.getAccountId(), NO_ACCESS, List.of(p3.getAccountId()), List.of(p3)),

            // search with non-existent ID
            arguments("other-id", "", NO_ACCESS, OTHER_ACCESS, EMPTY_RESPONSE)
        );
    }

    private PaginationModel.CursorConnection<Position> lookup(RequestModel.PositionSearch searchRequest) {
        RequestModel.PositionSearch clientRequest = searchRequest.withClientId("client-id");

        PaginationModel.CursorConnection<Position> connection = positionQueryService.search(clientRequest);

        LOGGER.info("Connection response: {}", connection);

        if (connection.edges().isEmpty()) {
            LOGGER.info("No positions found");
        } else {
            List<String> symbols = connection.getAllNodes().stream()
                .map(Position::getSymbol)
                .toList();

            LOGGER.info("Positions in response: {}", symbols);
        }

        return connection;
    }

    private void searchAndVerifyResults(RequestModel.PositionSearch searchRequest, Position... expectedPositions) {
        PaginationModel.CursorConnection<Position> connection = lookup(searchRequest);

        verifyPositions(connection, expectedPositions);
        verifyPageInfo(connection, expectedPositions.length);
    }

    private void searchAndVerifyEmptyResults(RequestModel.PositionSearch searchRequest) {
        PaginationModel.CursorConnection<Position> connection = lookup(searchRequest);

        verifyPositions(connection);
        verifyPageInfo(connection, 0);
    }

    private static void verifyPositions(PaginationModel.CursorConnection<Position> connection, Position... expectedPositions) {
        String[] expectedSymbols = Arrays.stream(expectedPositions)
            .map(Position::getSymbol)
            .toArray(String[]::new);

        assertThat(connection.getAllNodes())
            .extracting("symbol")
            .containsExactly(expectedSymbols);
    }

    private static void setupPositions() {
        p1 = createPosition(1)
            .portfolioId("portfolio-id-1")
            .symbol("p1")
            .currency("BTC")
            .quantity(BigDecimal.ONE)
            .portfolioCurrency("USD")
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .build();

        p2 = createPosition(2)
            .portfolioId("portfolio-id-2")
            .symbol("p2")
            .currency("EUR")
            .quantity(bd(100_000))
            .portfolioCurrency("EUR")
            .portfolioType(RequestModel.PortfolioType.NOSTRO)
            .build();

        p3 = createPosition(3)
            .accountId("account-id-3")
            .symbol("p3")
            .currency("USD")
            .quantity(BigDecimal.ONE)
            .accountCurrency("USD")
            .accountType(RequestModel.AccountType.WALLET)
            .accountWalletType(RequestModel.WalletType.VOSTRO)
            .build();

        p4 = createPosition(4)
            .accountId("account-id-4")
            .symbol("p4")
            .currency("EUR")
            .quantity(bd(10.5))
            .accountCurrency("USD")
            .accountType(RequestModel.AccountType.WALLET)
            .accountWalletType(RequestModel.WalletType.NOSTRO)
            .build();

        p5 = createPosition(5)
            .accountId("account-id-5")
            .symbol("p5")
            .currency("USDT")
            .quantity(bd(50_000))
            .accountCurrency("USD")
            .accountType(RequestModel.AccountType.EXCHANGE)
            .build();

        p6 = createPosition(6)
            .accountId("account-id-6")
            .symbol("p6")
            .currency("EUR")
            .quantity(bd(25_000))
            .accountCurrency("USD")
            .accountType(RequestModel.AccountType.CLOB)
            .build();

        le1 = createLedgerEntry(p1)
            .reservationRef("order-id-1")
            .build();

        le2 = createLedgerEntry(p2)
            .reservationRef("order-id-2")
            .build();

        le3 = createLedgerEntry(p3)
            .reservationRef("order-id-3")
            .build();
    }

    private static Position.Builder createPosition(long sequenceNumber) {
        return Position.builder()
            .sequenceNumber(sequenceNumber)
            .lastAppliedLedgerEntryId(1000 + sequenceNumber);
    }

    private static LedgerEntry.Builder createLedgerEntry(Position position) {
        long sequenceNumber = position.getSequenceNumber();
        ZonedDateTime base = LocalDate.parse("2025-01-01").atStartOfDay(DateUtils.UTC);
        ZonedDateTime dateTime = base.plusDays(sequenceNumber);

        return LedgerEntry.builder()
            .sequenceNumber(sequenceNumber)
            .reservationRef("order-id-" + sequenceNumber)
            .transactionId("transaction-id-" + sequenceNumber)
            .symbol(position.getSymbol())
            .portfolioId(position.getPortfolioId())
            .accountId(position.getAccountId())
            .portfolioType(position.getPortfolioType())
            .accountType(position.getAccountType())
            .accountWalletType(position.getAccountWalletType())
            .ledgerEntryType(LedgerEntryType.CASH_TRADE_CREDIT)
            .quantity(BigDecimal.ONE)
            .dateTime(dateTime)
            .price(bd(50_000))
            .settled(false);
    }

    public Set<PermissionDto> setupPermissions() {
        return setupPermissions(p1, p2, p3, p4, p5, p6);
    }

    public Set<PermissionDto> setupPermissions(Position... positions) {
        List<String> portfolioIds = Stream.of(positions)
            .map(Position::getPortfolioId)
            .filter(StringUtils::isNotBlank)
            .toList();

        List<String> accountIds = Stream.of(positions)
            .map(Position::getAccountId)
            .filter(StringUtils::isNotBlank)
            .toList();

        return setupPermissions(portfolioIds, accountIds);
    }
}