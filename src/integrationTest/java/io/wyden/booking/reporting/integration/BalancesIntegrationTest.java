package io.wyden.booking.reporting.integration;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.permissions.AccessGatewayMockClient;
import io.wyden.booking.reporting.application.permissions.SearchRequestAuthorizer;
import io.wyden.booking.reporting.domain.balance.Balance;
import io.wyden.booking.reporting.domain.balance.BalanceRepository;
import io.wyden.booking.reporting.integration.producer.BookingCompletedMessageProducer;
import io.wyden.booking.reporting.interfaces.rabbitmq.BookingCompletedSequencedEventConsumer;
import io.wyden.published.booking.BalanceSnapshot;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.PositionSearch;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorNode;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class BalancesIntegrationTest extends SearchIntegrationTest {

    @Autowired
    protected AccessGatewayMockClient accessGatewayMockClient;

    @Autowired
    private BalanceRepository balanceRepository;

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SearchRequestAuthorizer searchRequestAuthorizer;

    @Autowired
    private BookingCompletedSequencedEventConsumer bookingCompletedEventConsumer;

    BookingCompletedMessageProducer bookingCompletedMessageProducer;

    @BeforeEach
    void setUp() {
        bookingCompletedMessageProducer = new BookingCompletedMessageProducer(rabbitIntegrator);
        Mockito.doAnswer(invocation -> invocation.getArgument(0)).when(searchRequestAuthorizer).authorize(any());
        
        // Reset sequence number to 0 so that messages with sequence 1, 2, etc. are accepted
        bookingCompletedEventConsumer.resetSequenceNumber(0);
    }

    @Test
    void testBalanceSnapshotSaveAndUpdate() {
        // given
        String symbol = UUID.randomUUID().toString();
        BalanceSnapshot balanceSnapshot = getBalanceSnapshot(symbol);
        BookingCompleted bookingCompleted = BookingCompleted.newBuilder()
            .setSequenceNumber(1)
            .addBalanceSnapshot(balanceSnapshot)
            .build();

        VenueAccount.Builder account = createAccount(balanceSnapshot.getAccountId());
        save(account);

        // when
        bookingCompletedMessageProducer.emit(bookingCompleted);

        // then should save
        Awaitility.await()
            .atMost(Duration.ofSeconds(5))
            .untilAsserted(() -> {
                Optional<Balance> entityOpt = balanceRepository.findBySymbolAndAccountIdAndPortfolioId(symbol, "account1", "");
                assertThat(entityOpt).isPresent();

                Balance entity = entityOpt.get();

                assertThat(entity.getQuantity()).isEqualByComparingTo("4");
                assertThat(entity.getPendingQuantity()).isEqualByComparingTo("54");
                assertThat(entity.getAvailableForTradingQuantity()).isEqualByComparingTo("1");
                assertThat(entity.getAvailableForWithdrawalQuantity()).isEqualByComparingTo("0");
                assertThat(entity.getCurrency()).isEqualTo(symbol);
                assertThat(entity.getSequenceNumber()).isEqualTo(1L);
            });

        // given
        BalanceSnapshot balanceSnapshot2 = getBalanceSnapshot(symbol)
            .toBuilder()
            .setQuantity("11") // qty change should be reflected
            .build();

        BookingCompleted bookingCompleted2 = BookingCompleted.newBuilder()
            .setSequenceNumber(2)
            .addBalanceSnapshot(balanceSnapshot2)
            .build();

        // when
        bookingCompletedMessageProducer.emit(bookingCompleted2);

        // then should update
        Awaitility.await().atMost(Duration.ofSeconds(5)).untilAsserted(() -> {
            Optional<Balance> entityOpt = balanceRepository.findBySymbolAndAccountIdAndPortfolioId(symbol, "account1", "");
            assertThat(entityOpt).isPresent();
            Balance entity = entityOpt.get();
            assertThat(entity.getQuantity()).isEqualByComparingTo("11");
            assertThat(entity.getSequenceNumber()).isEqualTo(2L);
        });
    }

    @Test
    void testBalanceSnapshotDoesNotUpdateIfSequenceNumberLower() {
        String symbol = UUID.randomUUID().toString();
        // First insert with seq=2
        BalanceSnapshot balanceSnapshot1 = getBalanceSnapshot(symbol);
        BookingCompleted bookingCompleted1 = BookingCompleted.newBuilder()
            .setSequenceNumber(2)
            .addBalanceSnapshot(balanceSnapshot1)
            .build();

        bookingCompletedMessageProducer.emit(bookingCompleted1);

        // Try to update with lower seq=1
        BalanceSnapshot balanceSnapshot2 = getBalanceSnapshot(symbol)
            .toBuilder()
            .setQuantity("100") // different quantity, should be ignored
            .build();

        BookingCompleted bookingCompleted2 = BookingCompleted.newBuilder()
            .setSequenceNumber(1)
            .addBalanceSnapshot(balanceSnapshot2)
            .build();

        bookingCompletedMessageProducer.emit(bookingCompleted2);

        // Wait a moment for processing, then verify no update occurred
        Awaitility.await()
            .atMost(Duration.ofSeconds(2))
            .untilAsserted(() -> {
                Optional<Balance> entityOpt = balanceRepository.findBySymbolAndAccountIdAndPortfolioId(symbol, "account1", "");
                
                assertThat(entityOpt).isPresent();
                Balance entity = entityOpt.get();
                
                // Should still hold original values with seq=2, no update
                assertThat(entity.getQuantity()).isEqualByComparingTo("4");
                assertThat(entity.getSequenceNumber()).isEqualTo(2L);
            });
    }

    private static BalanceSnapshot getBalanceSnapshot(String symbol) {
        return BalanceSnapshot.newBuilder()
            .setAccountId("account1")
            .setCurrency(symbol)
            .setQuantity("4")
            .setPendingQuantity("54")
            .setAvailableForTradingQuantity("1")
            .setAvailableForWithdrawalQuantity("0")
            .setSettledQuantity("0")
            .setUnsettledQuantity("4")
            .setSymbol(symbol)
            .build();
    }

    @Test
    void shouldReturnBalanceSnapshotsMatchingSearchCriteria() throws Exception {
        // Given
        String symbol = UUID.randomUUID().toString();
        String portfolioId = "portfolio-abc";
        String currency = "USD";

        Portfolio.Builder portfolio = createPortfolio(portfolioId);
        save(portfolio);

        // setup permissions
        Set<PermissionDto> permissions = Set.of(
            new PermissionDto("portfolio", "read", portfolioId)
        );
        accessGatewayMockClient.grantPermissions(permissions);

        BalanceSnapshot snapshot = BalanceSnapshot.newBuilder()
            .setSymbol(symbol)
            .setPortfolioId(portfolioId)
            .setQuantity("100")
            .setPendingQuantity("10")
            .setAvailableForTradingQuantity("90")
            .setAvailableForWithdrawalQuantity("85")
            .setSettledQuantity("0")
            .setUnsettledQuantity("100")
            .setCurrency(currency)
            .build();

        BookingCompleted bookingCompleted = BookingCompleted.newBuilder()
            .setSequenceNumber(100)
            .addBalanceSnapshot(snapshot)
            .build();

        bookingCompletedMessageProducer.emit(bookingCompleted);

        // Then
        Awaitility.await().atMost(Duration.ofSeconds(5)).untilAsserted(() -> {
            List<Balance> all = balanceRepository.findAll();
            assertThat(all).anySatisfy(entity -> {
                assertThat(entity.getSymbol()).isEqualTo(symbol);
                assertThat(entity.getCurrency()).isEqualTo(currency);
                assertThat(entity.getPortfolioId()).isEqualTo(portfolioId);
            });
        });

        // When
        PositionSearch request = PositionSearch.newBuilder()
            .addCurrency(currency)
            .setClientId("client-a")
            .addPortfolioId(portfolioId)
            .setFirst(10)
            .build();

        MvcResult result = mockMvc.perform(post("/balances/search?version=proto")
                .contentType(MediaType.APPLICATION_PROTOBUF)
                .content(request.toByteArray()))
            .andDo(print())
            .andExpect(status().isOk())
            .andReturn();

        CursorConnection response = CursorConnection.parseFrom(result.getResponse().getContentAsByteArray());
        assertThat(response.getEdgesList()).hasSize(1);
        CursorNode node = response.getEdges(0).getNode();
        assertThat(node.hasBalance()).isTrue();
        assertThat(node.getBalance().getSymbol()).isEqualTo(symbol);
        assertThat(node.getBalance().getPortfolioId()).isEqualTo(portfolioId);
    }
}
