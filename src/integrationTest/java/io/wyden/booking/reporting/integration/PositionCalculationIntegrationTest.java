package io.wyden.booking.reporting.integration;

import com.hazelcast.map.IMap;
import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.application.permissions.AccessGatewayMockClient;
import io.wyden.booking.reporting.application.position.PositionQueryService;
import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.booking.reporting.domain.position.PositionRepository;
import io.wyden.booking.reporting.integration.producer.PositionSnapshotMessageProducer;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.PositionSnapshot;
import io.wyden.published.rate.Rate;
import io.wyden.published.rate.RateKey;
import io.wyden.published.referencedata.VenueAccount;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Set;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.assertj.core.api.Assertions.assertThat;

class PositionCalculationIntegrationTest extends SearchIntegrationTest {

    @Autowired
    private AccessGatewayMockClient accessGatewayMockClient;

    @Autowired
    private PositionQueryService positionQueryService;

    @Autowired
    private PositionRepository positionRepository;

    @Autowired
    private IMap<RateKey, Rate> ratesMap;

    private PositionSnapshotMessageProducer positionSnapshotMessageProducer;

    @BeforeEach
    void setUp() {
        positionSnapshotMessageProducer = new PositionSnapshotMessageProducer(rabbitIntegrator, positionRepository);
    }

    @Test
    void shouldRecalculatePositionMetricsWhenExchangeRatesChange() {
        PositionSnapshot position = PositionSnapshot.newBuilder()
            .setSequenceNumber(3000)
            .setSymbol("BTC")
            .setCurrency("BTC")
            .setAccount("bitmex-testnet1")
            .setQuantity("2.5")
            .setNotionalQuantity("2.5")
            .setGrossCost("125000") // 2.5 * 50'000
            .setNetCost("125100") // slightly higher gross cost due to fees
            .setGrossRealizedPnl("500")
            .setNetRealizedPnl("490")
            .build();

        String symbol = position.getSymbol();
        String accountId = position.getAccount();
        BigDecimal positionQuantity = bd(position.getQuantity());
        BigDecimal netCostBasis = bd(position.getNetCost());

        VenueAccount.Builder account = createAccount(accountId);
        save(account);

        // setup permissions
        Set<PermissionDto> permissions = Set.of(
            new PermissionDto("venue.account", "read", accountId)
        );
        accessGatewayMockClient.grantPermissions(permissions);

        // BTC price increased to 60'000
        BigDecimal initialPrice = bd(60_000);
        updateRates("BTC", "USD", initialPrice);

        positionSnapshotMessageProducer.emitAndWait(position);

        // then - perform first search with initial rates
        RequestModel.PositionSearch search = RequestModel.PositionSearch.builder()
            .symbol(symbol)
            .accountId(accountId)
            .build();

        PaginationModel.CursorConnection<Position> initialSearchResults = performPositionSearch(search);

        assertThat(initialSearchResults.getAllNodes()).hasSize(1);
        Position positionAfterInitialSearch = initialSearchResults.getAllNodes().iterator().next();

        // Verify initial calculated metrics (2.5 BTC * $60,000 = $150,000 market value)
        BigDecimal expectedInitialMarketValue = positionQuantity.multiply(initialPrice);
        BigDecimal expectedInitialUnrealizedPnl = expectedInitialMarketValue.subtract(netCostBasis); // $150,000 - $125,100 = $24,900 profit

        assertThat(positionAfterInitialSearch.getMarketValue())
            .as("Initial market value should be calculated correctly")
            .isEqualByComparingTo(expectedInitialMarketValue);

        assertThat(positionAfterInitialSearch.getNetUnrealizedPnl())
            .as("Initial unrealized P&L should be calculated correctly")
            .isEqualByComparingTo(expectedInitialUnrealizedPnl);

        // when - BTC price dropped to $40,000 per BTC
        BigDecimal updatedPrice = bd(40_000);
        updateRates("BTC", "USD", updatedPrice);

        // then - perform second search with updated rates
        PaginationModel.CursorConnection<Position> updatedSearchResults = performPositionSearch(search);

        assertThat(updatedSearchResults.getAllNodes()).hasSize(1);
        Position positionAfterRateUpdate = updatedSearchResults.getAllNodes().iterator().next();

        // Verify recalculated metrics (2.5 BTC * $40,000 = $90,000 market value)
        BigDecimal expectedUpdatedMarketValue = positionQuantity.multiply(updatedPrice);
        BigDecimal expectedUpdatedUnrealizedPnl = expectedUpdatedMarketValue.subtract(netCostBasis); // $90,000 - $125,100 = -$35,100 loss

        assertThat(positionAfterRateUpdate.getMarketValue())
            .as("Updated market value should reflect new exchange rate")
            .isEqualByComparingTo(expectedUpdatedMarketValue);

        assertThat(positionAfterRateUpdate.getNetUnrealizedPnl())
            .as("Updated unrealized P&L should reflect new exchange rate")
            .isEqualByComparingTo(expectedUpdatedUnrealizedPnl);

        // Verify the position was actually recalculated (not just fetched from cache)
        assertThat(positionAfterRateUpdate.getMarketValue())
            .as("Market value should have changed due to rate update")
            .isNotEqualByComparingTo(positionAfterInitialSearch.getMarketValue());

        assertThat(positionAfterRateUpdate.getNetUnrealizedPnl())
            .as("Unrealized P&L should have changed due to rate update")
            .isNotEqualByComparingTo(positionAfterInitialSearch.getNetUnrealizedPnl());

        assertThat(positionAfterRateUpdate.getUpdatedAt())
            .as("Position should have been updated after rate update, not saved as new one")
            .isAfter(positionAfterRateUpdate.getCreatedAt());
    }

    private void updateRates(String baseCurrency, String quoteCurrency, BigDecimal exchangeRate) {
        RateKey rateKey = RateKey.newBuilder()
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .build();

        Rate rate = Rate.newBuilder()
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .setValue(exchangeRate.toString())
            .setTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .build();

        ratesMap.put(rateKey, rate);
    }

    private PaginationModel.CursorConnection<Position> performPositionSearch(RequestModel.PositionSearch searchRequest) {
        RequestModel.PositionSearch clientRequest = searchRequest.withClientId("test-client-id");
        return positionQueryService.search(clientRequest);
    }
}