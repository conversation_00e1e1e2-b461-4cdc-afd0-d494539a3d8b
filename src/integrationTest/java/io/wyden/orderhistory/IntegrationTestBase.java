package io.wyden.orderhistory;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.cloud.utils.test.TracingMock;
import io.wyden.cloudutils.telemetry.Telemetry;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.ContextConfiguration;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.junit.jupiter.Testcontainers;

import static org.mockito.Mockito.mock;

@Testcontainers
@ContextConfiguration(initializers = IntegrationTestBase.Initializer.class)
public abstract class IntegrationTestBase {

    static final RabbitMQContainer RABBIT_MQ = new RabbitMQContainer("rabbitmq:3.12-management")
        .withReuse(true);

    static {
        RABBIT_MQ.start();
    }

    public static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Bean
        Telemetry telemetry() {
            return new Telemetry(TracingMock.createMock(), mock(MeterRegistry.class));
        }

        @Override
        public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {

            var values = TestPropertyValues.of(
                "rabbitmq.host=" + RABBIT_MQ.getHost(),
                "rabbitmq.port=" + RABBIT_MQ.getMappedPort(5672),
                "rabbitmq.username=" + RABBIT_MQ.getAdminUsername(),
                "rabbitmq.password=" + RABBIT_MQ.getAdminPassword()
            );
            values.applyTo(applicationContext);
        }
    }
}
