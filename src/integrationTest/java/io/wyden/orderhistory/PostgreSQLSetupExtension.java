package io.wyden.orderhistory;

import org.junit.jupiter.api.extension.AfterAllCallback;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.Container;
import org.testcontainers.containers.PostgreSQLContainer;

import java.io.IOException;

public class PostgreSQLSetupExtension extends DatabaseSetupExtension implements BeforeAllCallback, BeforeEachCallback, AfterAllCallback {

    private static final Logger LOGGER = LoggerFactory.getLogger(PostgreSQLSetupExtension.class);

    protected static PostgreSQLContainer<?> postgreSQLContainer = new PostgreSQLContainer<>("postgres")
        .withReuse(true)
        .withDatabaseName("order_history")
        .withUsername("order_history")
        .withPassword("password");

    @Override
    public void afterAll(ExtensionContext context) {
        postgreSQLContainer.stop();
    }

    @Override
    public void beforeAll(ExtensionContext context) {
        injectStaticSelf(context);
        postgreSQLContainer.start();

        String urlSuffix = postgreSQLContainer.getHost() + ":" + postgreSQLContainer.getFirstMappedPort()
            + "/" + postgreSQLContainer.getDatabaseName();

        System.setProperty("spring.datasource.url", "jdbc:postgresql://" + urlSuffix);
        System.setProperty("spring.datasource.username", postgreSQLContainer.getUsername());
        System.setProperty("spring.datasource.password", postgreSQLContainer.getPassword());

        System.setProperty("spring.flyway.url", "jdbc:postgresql://" + urlSuffix);
        System.setProperty("spring.flyway.user", postgreSQLContainer.getUsername());
        System.setProperty("spring.flyway.password", postgreSQLContainer.getPassword());
        System.setProperty("spring.flyway.locations", "classpath:psql/migration/schema");

        System.setProperty("database.type", "PostgreSql");
    }

    @Override
    public void beforeEach(ExtensionContext context) {
        injectSelf(context);
    }

    public String queryDb(String query) throws IOException, InterruptedException {
        String command = "psql -U order_history -d order_history -c '%s'".formatted(query);
        Container.ExecResult result = postgreSQLContainer.execInContainer("sh", "-c", command);
        return result.getStdout();
    }

    @Override
    public void logTableContent(String tableName) throws IOException, InterruptedException {
        String query = "SELECT * from %s".formatted(tableName);
        LOGGER.info(queryDb(query));
    }
}
