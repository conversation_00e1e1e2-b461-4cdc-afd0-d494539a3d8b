package io.wyden.orderhistory.service;

import com.google.protobuf.Message;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.orderhistory.IntegrationTestBase;
import io.wyden.orderhistory.PostgreSQLSetupExtension;
import io.wyden.orderhistory.infrastructure.rabbit.RabbitDestinations;
import io.wyden.orderhistory.infrastructure.rabbit.TradingResponseMessageConsumer;
import io.wyden.orderhistory.repository.OrderEventRepository;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientResponseType;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Map;
import java.util.UUID;

@ExtendWith(PostgreSQLSetupExtension.class)
@ExtendWith(SpringExtension.class)
@MockBean(classes = { HazelcastInstance.class })
@SpringBootTest
public abstract class TradingMessageConsumerIntegrationTestBase extends IntegrationTestBase {

    static final Map<String, String> TRADING_HEADERS = Map.of(OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName());

    @Autowired
    protected RabbitIntegrator rabbitIntegrator;

    @Autowired
    protected OrderEventRepository orderEventRepository;

    @Autowired
    protected RabbitDestinations rabbitDestinations;

    @Autowired
    protected RabbitExchange<Message> tradingIngressExchange;

    @Autowired
    protected Telemetry telemetry;

    @MockBean
    protected AsyncOrderStateProcessor mockAsyncOrderStateProcessor;

    protected TradingResponseMessageConsumer consumer;
    protected String queueName = "test-trading-queue";

    @BeforeEach
    void setUp() {
        consumer = new TradingResponseMessageConsumer(
            rabbitIntegrator,
            rabbitDestinations.tradingIngressExchange(rabbitIntegrator),
            orderEventRepository,
            mockAsyncOrderStateProcessor,
            telemetry.getTracing(),
            queueName,
            "trading-message-consumer-test-consumer");
    }

    protected OemsResponse createTestOemsResponse(String orderId) {
        return OemsResponse.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                .build())
            .setOrderId(orderId)
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setRequest(OemsRequest.newBuilder()
                .setMetadata(Metadata.newBuilder()
                    .setRequestId(UUID.randomUUID().toString())
                    .build())
                .setOrderId(orderId)
                .build())
            .build();
    }

    protected ClientResponse createTestClientResponse(String orderId) {
        return ClientResponse.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                .build())
            .setOrderId(orderId)
            .setResponseType(ClientResponseType.EXECUTION_REPORT)
            .setRequest(ClientRequest.newBuilder()
                .setMetadata(Metadata.newBuilder()
                    .setRequestId(UUID.randomUUID().toString())
                    .build())
                .setOrderId(orderId)
                .build())
            .build();
    }

    protected OemsResponse createNonProcessableOemsResponse() {
        return OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.RESPONSE_TYPE_UNSPECIFIED)
            .build();
    }
}
