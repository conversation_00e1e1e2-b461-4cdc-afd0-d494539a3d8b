package io.wyden.orderhistory.service;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.orderhistory.model.MessageType;
import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.orderhistory.model.OrderHistorySearchIndex;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientResponseType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.reporting.OrderState;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.annotation.DirtiesContext;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
public class AsyncOrderStateProcessorIntegrationTest extends AsyncOrderStateProcessorIntegrationTestBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncOrderStateProcessorIntegrationTest.class);

    @Test
    void shouldProcessValidOemsResponseAndSaveEvent() {
        // Given
        String orderId = UUID.randomUUID().toString();
        LOGGER.info("Starting test with orderId: {}", orderId);

        OemsResponse response = createTestOemsResponse(orderId);
        LOGGER.info("Created test OemsResponse: {}", response.getOrderId());

        // When
        LOGGER.info("Publishing message to trading ingress exchange");
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);

        // Then
        LOGGER.info("Waiting for OrderEventEntity to be saved");
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId);
            LOGGER.info("Found {} events in database", events.size());
            assertThat(events).isNotEmpty();
            assertThat(events).anyMatch(event -> {
                LOGGER.info("Event orderId: {}, expected: {}", event.getOrderId(), response.getOrderId());
                return event.getOrderId().equalsIgnoreCase(response.getOrderId());
            });
        });

        LOGGER.info("Waiting for OrderState to be emitted");
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            OrderState orderState = awaitOrderState();
            LOGGER.info("Received OrderState: {}", orderState != null ? orderState.getOrderId() : "null");
            assertThat(orderState).isNotNull();
            assertThat(orderState.getOrderId()).isEqualTo(response.getOrderId());
        });
    }

    @Test
    void shouldProcessValidClientResponseAndSaveEvent() {
        // Given
        ClientResponse response = createTestClientResponse();
        LOGGER.info("Created test ClientResponse: {}", response.getOrderId());

        // When
        LOGGER.info("Publishing ClientResponse message to trading ingress exchange");
        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(response, clientHeaders);

        // Then
        LOGGER.info("Waiting for OrderEventEntity to be saved");
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(response.getOrderId());
            LOGGER.info("Found {} events in database", events.size());
            assertThat(events).isNotEmpty();
            assertThat(events).anyMatch(event -> {
                LOGGER.info("Event orderId: {}, messageType: {}", event.getOrderId(), event.getMessageType());
                return event.getOrderId().equalsIgnoreCase(response.getOrderId()) && event.getMessageType().equals(MessageType.CLIENT_RESPONSE);
            });
        });

        LOGGER.info("Waiting for OrderState to be emitted");
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            OrderState orderState = awaitOrderState();
            LOGGER.info("Received OrderState: {}", orderState != null ? orderState.getOrderId() : "null");
            assertThat(orderState).isNotNull();
            assertThat(orderState.getOrderId()).isEqualTo(response.getOrderId());
        });
    }

    @Test
    void shouldUpdateSearchIndexCorrectly() {
        // Given
        String orderId = UUID.randomUUID().toString();
        LOGGER.info("Starting search index test with orderId: {}", orderId);

        OemsResponse response = createTestOemsResponse(orderId);

        // When
        LOGGER.info("Publishing message to trading ingress exchange");
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);

        // Then
        LOGGER.info("Waiting for OrderHistorySearchIndex to be updated");
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            Optional<OrderHistorySearchIndex> searchIndex = searchIndexRepository.findByOrderId(orderId);
            LOGGER.info("Search index found: {}", searchIndex.isPresent());
            assertThat(searchIndex).isPresent();
            
            OrderHistorySearchIndex index = searchIndex.get();
            assertThat(index.getOrderId()).isEqualTo(orderId);
            assertThat(index.getLatestMessageTimestamp()).isNotNull();
            assertThat(index.getLatestEventId()).isNotNull();
            LOGGER.info("Search index verified: orderId={}, timestamp={}", 
                       index.getOrderId(), index.getLatestMessageTimestamp());
        });
    }

    @Test
    void shouldSkipOlderEventsBasedOnTimestamp() {
        // Given
        String orderId = UUID.randomUUID().toString();
        ZonedDateTime newerTimestamp = ZonedDateTime.now();
        OemsResponse oemsResponse1 = createTestOemsResponse(orderId, newerTimestamp, OemsOrderStatus.STATUS_PARTIALLY_FILLED, 10);

        LOGGER.info("Publishing response with newer timestamp");
        tradingIngressExchange.publishWithHeaders(oemsResponse1, TRADING_HEADERS);

        //// Wait for processing to complete
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            Optional<OrderHistorySearchIndex> searchIndex = searchIndexRepository.findByOrderId(orderId);
            assertThat(searchIndex).isPresent();
            assertThat(searchIndex.get().getLatestMessageTimestamp().toString()).isEqualTo(DateUtils.toIsoUtcTime(newerTimestamp));
        });

        //// Consume the first OrderState
        awaitOrderState(Duration.ofSeconds(3));

        LOGGER.info("Creating second event with older timestamp");
        ZonedDateTime olderTimestamp = newerTimestamp.minusMinutes(5);
        OemsResponse oemsResponse2 = createTestOemsResponse(orderId, olderTimestamp, OemsOrderStatus.STATUS_PARTIALLY_FILLED, 5);

        // When
        LOGGER.info("Processing older event with timestamp: {}", olderTimestamp);
        tradingIngressExchange.publishWithHeaders(oemsResponse2, TRADING_HEADERS);

        // Then
        //// Verify the older event was skipped (search index timestamp should remain unchanged)
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            Optional<OrderHistorySearchIndex> finalSearchIndex = searchIndexRepository.findByOrderId(orderId);
            assertThat(finalSearchIndex).isPresent();
            assertThat(finalSearchIndex.get().getLatestMessageTimestamp().toString()).isEqualTo(DateUtils.toIsoUtcTime(newerTimestamp));
        });
        LOGGER.info("Verified older event was skipped - timestamp remained: {}", newerTimestamp);

        //// Verify no new OrderState was emitted for the older event
        ensureNoMoreMessages(Duration.ofSeconds(5));
    }

    @Test
    void shouldProcessMultipleEventsForSameOrder() {
        // Given
        String orderId = UUID.randomUUID().toString();
        LOGGER.info("Starting multiple events test with orderId: {}", orderId);
        OemsResponse firstResponse = createTestOemsResponse(orderId);
        OemsResponse secondResponse = createTestOemsResponse(orderId);
        OemsResponse thirdResponse = createTestOemsResponse(orderId);

        // When
        tradingIngressExchange.publishWithHeaders(firstResponse, TRADING_HEADERS);
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId);
            assertThat(events).hasSize(1);
        });

        tradingIngressExchange.publishWithHeaders(secondResponse, TRADING_HEADERS);
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId);
            assertThat(events).hasSize(2);
        });

        tradingIngressExchange.publishWithHeaders(thirdResponse, TRADING_HEADERS);
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId);
            assertThat(events).hasSize(3);
        });

        // Then
        //// Verify search index was updated with latest event
        Optional<OrderHistorySearchIndex> searchIndex = searchIndexRepository.findByOrderId(orderId);
        assertThat(searchIndex).isPresent();
        assertThat(searchIndex.get().getOrderId()).isEqualTo(orderId);

        //// Verify OrderStates were emitted for both events
        OrderState firstOrderState = awaitOrderState();
        OrderState secondOrderState = awaitOrderState();
        OrderState thirdOrderState = awaitOrderState();
        assertThat(firstOrderState).isNotNull();
        assertThat(secondOrderState).isNotNull();
        assertThat(thirdOrderState).isNotNull();
        assertThat(firstOrderState.getOrderId()).isEqualTo(orderId);
        assertThat(secondOrderState.getOrderId()).isEqualTo(orderId);
        assertThat(thirdOrderState.getOrderId()).isEqualTo(orderId);

        LOGGER.info("Successfully processed all events for orderId: {}", orderId);
    }

    @Test
    void shouldHandleInvalidProtoGracefully() {
        // Given
        String orderId = UUID.randomUUID().toString();
        OrderEventEntity invalidEvent = OrderEventEntity.builder()
            .orderId(orderId)
            .messageType(MessageType.OEMS_RESPONSE)
            .messageTimestamp(OffsetDateTime.now())
            .protoBlob("invalid-proto-data".getBytes())
            .build();
        
        OrderEventEntity savedEvent = orderEventRepository.save(invalidEvent);
        
        // When
        asyncOrderStateProcessor.processEvent(savedEvent);
        
        // Then
        // Verify no OrderState was emitted due to proto parsing error
        ensureNoMoreMessages(Duration.ofSeconds(3));
        
        // Verify no search index was created
        Optional<OrderHistorySearchIndex> searchIndex = searchIndexRepository.findByOrderId(orderId);
        assertThat(searchIndex).isEmpty();
    }

    @Test
    void shouldHandleUnsupportedMessageType() {
        // Given
        String orderId = UUID.randomUUID().toString();
        
        // Create a valid OrderEventEntity but with an invalid proto blob that can't be deserialized
        // This tests the AsyncOrderStateProcessor's handling of deserialization errors
        OrderEventEntity unsupportedEvent = OrderEventEntity.builder()
            .orderId(orderId)
            .messageType(MessageType.OEMS_RESPONSE) // Valid message type but invalid proto blob
            .messageTimestamp(OffsetDateTime.now())
            .protoBlob(new byte[]{1, 2, 3}) // Invalid proto data
            .build();
        
        OrderEventEntity savedEvent = orderEventRepository.save(unsupportedEvent);
        
        // When
        asyncOrderStateProcessor.processEvent(savedEvent);
        
        // Then
        // Verify no OrderState was emitted due to deserialization error
        ensureNoMoreMessages(Duration.ofSeconds(3));
        
        // Verify no search index was created
        Optional<OrderHistorySearchIndex> searchIndex = searchIndexRepository.findByOrderId(orderId);
        assertThat(searchIndex).isEmpty();
    }

    @Test
    void shouldHandleDifferentOrderStatuses() {
        // Given
        String orderId = UUID.randomUUID().toString();
        
        // Test different OemsOrderStatus values
        OemsOrderStatus[] statusesToTest = {
            OemsOrderStatus.STATUS_NEW,
            OemsOrderStatus.STATUS_PARTIALLY_FILLED,
            OemsOrderStatus.STATUS_FILLED,
            OemsOrderStatus.STATUS_CANCELED,
            OemsOrderStatus.STATUS_REJECTED
        };
        
        for (int i = 0; i < statusesToTest.length; i++) {
            String testOrderId = orderId + "-" + i;
            OemsResponse response = createTestOemsResponse(testOrderId, ZonedDateTime.now().plusSeconds(i), statusesToTest[i], 10);
            
            // When
            tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);
            
            // Then
            await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
                OrderState orderState = awaitOrderState();
                assertThat(orderState).isNotNull();
                assertThat(orderState.getOrderId()).isEqualTo(testOrderId);
            });
        }
    }


    @Test
    void shouldProcessEventsInCorrectOrderWhenMultipleEventsArrive() {
        // Given
        String orderId = UUID.randomUUID().toString();
        ZonedDateTime baseTime = ZonedDateTime.now();
        
        OemsResponse firstEvent = createTestOemsResponse(orderId, baseTime, OemsOrderStatus.STATUS_NEW, 0);
        OemsResponse secondEvent = createTestOemsResponse(orderId, baseTime.plusSeconds(1), OemsOrderStatus.STATUS_PARTIALLY_FILLED, 5);
        OemsResponse thirdEvent = createTestOemsResponse(orderId, baseTime.plusSeconds(2), OemsOrderStatus.STATUS_FILLED, 10);
        
        // When - publish events in rapid succession
        tradingIngressExchange.publishWithHeaders(firstEvent, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(secondEvent, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(thirdEvent, TRADING_HEADERS);
        
        // Then - verify all events were processed and final state is correct
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId);
            assertThat(events).hasSize(3);
        });
        
        // Verify search index reflects the latest event
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            Optional<OrderHistorySearchIndex> searchIndex = searchIndexRepository.findByOrderIdOrderByLatestMessageTimestampDesc(orderId);
            assertThat(searchIndex).isPresent();
            assertThat(searchIndex.get().getLatestMessageTimestamp().toString())
                .isEqualTo(DateUtils.toIsoUtcTime(baseTime.plusSeconds(2)));
        });
        
        // Verify all OrderStates were emitted
        OrderState state1 = awaitOrderState();
        OrderState state2 = awaitOrderState();
        OrderState state3 = awaitOrderState();
        assertThat(state1.getOrderId()).isEqualTo(orderId);
        assertThat(state2.getOrderId()).isEqualTo(orderId);
        assertThat(state3.getOrderId()).isEqualTo(orderId);
    }

    @Test
    void shouldEmitCorrectOrderStateForClientResponse() {
        // Given
        ClientResponse response = createTestClientResponse();

        // When
        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(response, clientHeaders);

        // Then
        //// Wait for OrderState to be emitted and verify its content
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            OrderState orderState = awaitOrderState();
            assertThat(orderState).isNotNull();
            assertThat(orderState.getOrderId()).isEqualTo(response.getOrderId());
            
            // Verify OrderState was built from ClientResponse correctly
            // The exact mapping depends on OrderStateMapperExtensions implementation
            LOGGER.info("Verified OrderState content for ClientResponse: orderId={}", orderState.getOrderId());
        });

        //// Verify search index was also updated correctly
        String orderId = response.getOrderId();
        Optional<OrderHistorySearchIndex> searchIndex = searchIndexRepository.findByOrderId(orderId);
        assertThat(searchIndex).isPresent();
        assertThat(searchIndex.get().getOrderId()).isEqualTo(orderId);
    }

    @Test
    void shouldHandleClientResponseWithoutRequest() {
        // Given
        String orderId = UUID.randomUUID().toString();
        ClientResponse responseWithoutRequest = ClientResponse.newBuilder()
            .setOrderId(orderId)
            .setResponseType(ClientResponseType.EXECUTION_REPORT)
            // No request set
            .build();
        
        // When
        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(responseWithoutRequest, clientHeaders);
        
        // Then
        // Verify no event was saved (SimplifiedTradingMessageConsumer should skip it)
        await().atMost(Duration.ofSeconds(3)).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId);
            assertThat(events).isEmpty();
        });
        
        // Verify no OrderState was emitted
        ensureNoMoreMessages(Duration.ofSeconds(3));
    }

    @Test
    void shouldVerifySearchIndexFieldsAreCorrectlyPopulated() {
        // Given
        String orderId = UUID.randomUUID().toString();
        OemsResponse response = createTestOemsResponse(orderId);
        
        // When
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);
        
        // Then
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            Optional<OrderHistorySearchIndex> searchIndex = searchIndexRepository.findByOrderId(orderId);
            assertThat(searchIndex).isPresent();
            
            OrderHistorySearchIndex index = searchIndex.get();
            assertThat(index.getOrderId()).isEqualTo(orderId);
            assertThat(index.getLatestMessageTimestamp()).isNotNull();
            assertThat(index.getLatestEventId()).isNotNull();
            assertThat(index.getPortfolioId()).isNotNull();
            assertThat(index.getInstrumentId()).isNotNull();
            assertThat(index.getOrderStatus()).isNotNull();
            assertThat(index.getOrderCategory()).isNotNull();
            assertThat(index.getTif()).isNotNull();
        });
    }
}
