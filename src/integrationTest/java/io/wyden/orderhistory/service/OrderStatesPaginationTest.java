package io.wyden.orderhistory.service;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.SortingOrder;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.reporting.OrderState;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.DirtiesContext;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nonnull;

import static org.assertj.core.api.Assertions.assertThat;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
public class OrderStatesPaginationTest extends AsyncOrderStateProcessorIntegrationTestBase {

    @Autowired
    OrderStateQueryService orderStateQueryService;

    @AfterEach
    void cleanup() {
        searchIndexRepository.deleteAll();
        orderEventRepository.deleteAll();
    }

    @Test
    void shouldPaginateResultsFromNewestToOldestByDefault() {
        // given
        Map<Integer, ClientResponse> responses = createTestClientResponses();
        responses.values().forEach(response -> {
            try {
                tradingIngressExchange.publishWithHeaders(response, Map.of("messageType", ClientResponse.class.getSimpleName()));
                Thread.sleep(100); // Allow processing time
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
        });

        // Wait for all events to be processed
        await().atMost(Duration.ofSeconds(5)).until(() -> searchIndexRepository.count() == responses.size());

        // when
        OrderHistorySearchInput searchInput1 = new OrderHistorySearchInput(List.of(), List.of(), List.of(), 3, null, SortingOrder.DESC, null);
        CursorConnection orderHistoryPaged1 = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput1);

        // then
        numberOfEdges(orderHistoryPaged1, 3);
        orderIds(orderHistoryPaged1, "8", "7", "6");
        hasNextPage(orderHistoryPaged1, true);
        String endCursor = endCursorPointsTo(orderHistoryPaged1, responses.get(6));

        // when
        OrderHistorySearchInput searchInput2 = new OrderHistorySearchInput(List.of(), List.of(), List.of(), 3, endCursor, SortingOrder.DESC, null);
        CursorConnection orderHistoryPaged2 = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput2);

        // then
        numberOfEdges(orderHistoryPaged2, 3);
        orderIds(orderHistoryPaged2, "5", "4", "3");
        hasNextPage(orderHistoryPaged2, true);
        endCursor = endCursorPointsTo(orderHistoryPaged2, responses.get(3));

        // when
        OrderHistorySearchInput searchInput3 = new OrderHistorySearchInput(List.of(), List.of(), List.of(), 3, endCursor, SortingOrder.DESC, null);
        CursorConnection orderHistoryPaged3 = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput3);

        // then
        numberOfEdges(orderHistoryPaged3, 2);
        orderIds(orderHistoryPaged3, "2", "1");
        hasNextPage(orderHistoryPaged3, false);
        endCursorPointsTo(orderHistoryPaged3, responses.get(1));
    }

    @Test
    void shouldPaginateResultsFromOldestToLatest() {
        // given
        Map<Integer, ClientResponse> responses = createTestClientResponses();
        responses.values().forEach(response -> {
            try {
                tradingIngressExchange.publishWithHeaders(response, Map.of("messageType", ClientResponse.class.getSimpleName()));
                Thread.sleep(100); // Allow processing time
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
        });

        // Wait for all events to be processed
        await().atMost(Duration.ofSeconds(5)).until(() -> searchIndexRepository.count() == responses.size());

        // when
        OrderHistorySearchInput searchInput1 = new OrderHistorySearchInput(List.of(), List.of(), List.of(), 3, null, SortingOrder.ASC, null);
        CursorConnection orderHistoryPaged1 = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput1);

        // then
        numberOfEdges(orderHistoryPaged1, 3);
        orderIds(orderHistoryPaged1, "1", "2", "3");
        hasNextPage(orderHistoryPaged1, true);
        String endCursor = endCursorPointsTo(orderHistoryPaged1, responses.get(3));

        // when
        OrderHistorySearchInput searchInput2 = new OrderHistorySearchInput(List.of(), List.of(), List.of(), 3, endCursor, SortingOrder.ASC, null);
        CursorConnection orderHistoryPaged2 = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput2);

        // then
        numberOfEdges(orderHistoryPaged2, 3);
        orderIds(orderHistoryPaged2, "4", "5", "6");
        hasNextPage(orderHistoryPaged2, true);
        endCursor = endCursorPointsTo(orderHistoryPaged2, responses.get(6));

        // when
        OrderHistorySearchInput searchInput3 = new OrderHistorySearchInput(List.of(), List.of(), List.of(), 3, endCursor, SortingOrder.ASC, null);
        CursorConnection orderHistoryPaged3 = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput3);

        // then
        numberOfEdges(orderHistoryPaged3, 2);
        orderIds(orderHistoryPaged3, "7", "8");
        hasNextPage(orderHistoryPaged3, false);
        endCursorPointsTo(orderHistoryPaged3, responses.get(8));
    }

    private static String endCursorPointsTo(CursorConnection cursorConnection, ClientResponse clientResponse) {
        String endCursor = cursorConnection.getPageInfo().getEndCursor();
        assertThat(endCursor).isEqualTo(isoUtcTimeToEpochMillis(clientResponse.getCreatedAt()));
        return endCursor;
    }

    private static void hasNextPage(CursorConnection cursorConnection, boolean expected) {
        assertThat(cursorConnection.getPageInfo().getHasNextPage()).isEqualTo(expected);
    }

    private static void numberOfEdges(CursorConnection cursorConnection, int expected) {
        assertThat(cursorConnection.getEdgesList())
            .hasSize(expected);
    }

    private static void orderIds(CursorConnection cursorConnection, String... expected) {
        assertThat(cursorConnection.getEdgesList())
            .extracting(CursorEdge::getNode)
            .extracting(CursorNode::getOrderState)
            .extracting(OrderState::getOrderId)
            .containsExactly(expected);
    }

    private Map<Integer, ClientResponse> createTestClientResponses() {
        ZonedDateTime baseTime = ZonedDateTime.now();
        return Map.of(
            1, createTestClientResponse("1", baseTime.plusMinutes(1)),
            2, createTestClientResponse("2", baseTime.plusMinutes(2)),
            3, createTestClientResponse("3", baseTime.plusMinutes(3)),
            4, createTestClientResponse("4", baseTime.plusMinutes(4)),
            5, createTestClientResponse("5", baseTime.plusMinutes(5)),
            6, createTestClientResponse("6", baseTime.plusMinutes(6)),
            7, createTestClientResponse("7", baseTime.plusMinutes(7)),
            8, createTestClientResponse("8", baseTime.plusMinutes(8))
        );
    }

    public static String isoUtcTimeToEpochMillis(@Nonnull String isoString) {
        return Optional.of(isoString).map(DateUtils::isoUtcTimeToInstant).map(DateUtils::instantToEpochMicros).orElse(null);
    }
}
