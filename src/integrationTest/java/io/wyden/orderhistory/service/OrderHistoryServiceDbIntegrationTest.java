package io.wyden.orderhistory.service;

import com.hazelcast.core.HazelcastInstance;
import io.wyden.orderhistory.DatabaseSetupExtension;
import io.wyden.orderhistory.PostgreSQLSetupExtension;
import io.wyden.orderhistory.model.CollectionPredicateInput;
import io.wyden.orderhistory.model.DatePredicateInput;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.orderhistory.model.SimplePredicateInput;
import io.wyden.orderhistory.model.SortingOrder;
import io.wyden.orderhistory.utils.TestingData;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.reporting.OrderCategory;
import io.wyden.published.reporting.OrderState;
import io.wyden.published.reporting.OrderStatus;
import io.wyden.published.reporting.TIF;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.DirtiesContext;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Stream;

import static io.wyden.orderhistory.model.CollectionPredicateInput.PredicateType.IN;
import static io.wyden.orderhistory.model.DatePredicateInput.Field.CREATED_AT;
import static io.wyden.orderhistory.model.DatePredicateInput.Field.UPDATED_AT;
import static io.wyden.orderhistory.model.DatePredicateInput.PredicateType.FROM;
import static io.wyden.orderhistory.model.DatePredicateInput.PredicateType.TO;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.EXT_ORDER_ID;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.PORTFOLIO_TYPE;
import static io.wyden.orderhistory.model.SimplePredicateInput.PredicateType.CONTAINS;
import static io.wyden.orderhistory.model.SimplePredicateInput.PredicateType.EQUAL;
import static io.wyden.orderhistory.model.SimplePredicateInput.PredicateType.NOT_EQUAL;
import static io.wyden.orderhistory.utils.TestingData.CLIENT_ROOT_ORDER_ID;
import static io.wyden.orderhistory.utils.TestingData.VENUE_ACCOUNT;
import static io.wyden.orderhistory.utils.TestingData.clientNewOrderRequest;
import static io.wyden.orderhistory.utils.TestingData.expectedExecutionReportNew;
import static io.wyden.orderhistory.utils.TestingData.oemsRequest;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.of;
import static org.mockito.Mockito.when;

public abstract class OrderHistoryServiceDbIntegrationTest extends OrderHistoryServiceDbIntegrationTestBase {

    DatabaseSetupExtension DB;

    static Stream<Arguments> predicate_verificationMethod() {
        return Stream.of(
            of(List.of(new SimplePredicateInput(CONTAINS, SimplePredicateInput.Field.PORTFOLIO_ID, "Portfolio-1")), List.of(),
                (Function<OrderState, Boolean>) state -> state.getPortfolioId().contains("Portfolio-1")),
            of(List.of(), List.of(new CollectionPredicateInput(IN, CollectionPredicateInput.Field.VENUE_ACCOUNT_ID, List.of(VENUE_ACCOUNT))),
                (Function<OrderState, Boolean>) state -> state.getVenueAccountsList().contains(VENUE_ACCOUNT)),
            of(List.of(new SimplePredicateInput(CONTAINS, SimplePredicateInput.Field.INSTRUMENT_ID, "BTCUSD")), List.of(),
                (Function<OrderState, Boolean>) state -> state.getInstrumentId().contains("BTCUSD@Wyden")),
            of(List.of(new SimplePredicateInput(CONTAINS, SimplePredicateInput.Field.ORDER_ID, "1")), List.of(),
                (Function<OrderState, Boolean>) state -> state.getOrderId().contains("1")),
            of(List.of(new SimplePredicateInput(CONTAINS, SimplePredicateInput.Field.CL_ORDER_ID, "1")), List.of(),
                (Function<OrderState, Boolean>) state -> state.getClOrderId().contains("1")),
            of(List.of(new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.TIF, "GTC")), List.of(),
                (Function<OrderState, Boolean>) state -> state.getTif().equals(TIF.GTC)),
            of(List.of(new SimplePredicateInput(NOT_EQUAL, SimplePredicateInput.Field.RESULT, "SUCCESS")), List.of(),
                (Function<OrderState, Boolean>) state -> !state.getLastRequestResult().equals("SUCCESS")),
            of(List.of(new SimplePredicateInput(NOT_EQUAL, SimplePredicateInput.Field.REASON, "")), List.of(),
                (Function<OrderState, Boolean>) state -> state.getReason().isEmpty()),
            of(List.of(new SimplePredicateInput(CONTAINS, SimplePredicateInput.Field.ORDER_STATUS, "fIlLeD")), List.of(),
                (Function<OrderState, Boolean>) state -> state.getOrderStatus().equals(OrderStatus.FILLED)),
            of(List.of(new SimplePredicateInput(CONTAINS, SimplePredicateInput.Field.ORDER_STATUS, "fIlLeD")), List.of(),
                (Function<OrderState, Boolean>) state -> state.getOrderStatus().equals(OrderStatus.FILLED))
        );
    }

    @Test
    void shouldStoreOrderState() {
        // given
        ClientRequest newOrderRequest = clientNewOrderRequest(ClientOrderType.LIMIT, null);

        // when
        orderHistoryService.processClientNewOrderRequest(newOrderRequest);

        // then
        Optional<OrderStateEntity> orderState = orderHistoryService.getOrderStateSnapshotByOrderId(newOrderRequest.getOrderId());
        assertThat(orderState).isNotEmpty();
    }

    @Test
    void shouldUpdateOrderState() {
        // given
        ClientRequest newOrderRequest = clientNewOrderRequest(ClientOrderType.LIMIT, null);
        ClientResponse clientResponse = expectedExecutionReportNew(1, newOrderRequest.getOrderId(), newOrderRequest.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest.getVenueAccounts(0));

        // when
        orderHistoryService.processClientNewOrderRequest(newOrderRequest);

        // then
        Optional<OrderStateEntity> orderState1 = orderHistoryService.getOrderStateSnapshotByOrderId(newOrderRequest.getOrderId());
        assertThat(orderState1).isNotEmpty();

        // when
        orderHistoryService.processClientExecutionReport(clientResponse);

        // then
        Optional<OrderStateEntity> orderState2 = orderHistoryService.getOrderStateSnapshotByOrderId(clientResponse.getOrderId());
        assertThat(orderState2).isNotEmpty();
    }

    @Test
    void shouldGetAllOrderStates() {
        // given
        ClientRequest newOrderRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now().minusMinutes(10));
        ClientResponse clientResponse1 = expectedExecutionReportNew(1, newOrderRequest1.getOrderId(), newOrderRequest1.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest1.getVenueAccounts(0));

        ClientRequest newOrderRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now().minusMinutes(5));
        ClientResponse clientResponse2 = expectedExecutionReportNew(1, newOrderRequest2.getOrderId(), newOrderRequest2.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest2.getVenueAccounts(0));

        ClientRequest newOrderRequest3 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now());
        ClientResponse clientResponse3 = expectedExecutionReportNew(1, newOrderRequest3.getOrderId(), newOrderRequest3.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest3.getVenueAccounts(0));

        orderHistoryService.processClientNewOrderRequest(newOrderRequest1);
        orderHistoryService.processClientExecutionReport(clientResponse1);

        orderHistoryService.processClientNewOrderRequest(newOrderRequest2);
        orderHistoryService.processClientExecutionReport(clientResponse2);

        orderHistoryService.processClientNewOrderRequest(newOrderRequest3);
        orderHistoryService.processClientExecutionReport(clientResponse3);

        // when
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).isNotEmpty();
        assertThat(orderHistory.size()).isEqualTo(3);
    }

    @Test
    void shouldGetAllOrderStatesPaged() {
        // given
        ClientRequest newOrderRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now().minusMinutes(10));
        ClientResponse clientResponse1 = expectedExecutionReportNew(1, newOrderRequest1.getOrderId(), newOrderRequest1.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest1.getVenueAccounts(0));

        ClientRequest newOrderRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now().minusMinutes(5));
        ClientResponse clientResponse2 = expectedExecutionReportNew(1, newOrderRequest2.getOrderId(), newOrderRequest2.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest2.getVenueAccounts(0));

        ClientRequest newOrderRequest3 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now());
        ClientResponse clientResponse3 = expectedExecutionReportNew(1, newOrderRequest3.getOrderId(), newOrderRequest3.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest3.getVenueAccounts(0));

        orderHistoryService.processClientNewOrderRequest(newOrderRequest1);
        orderHistoryService.processClientExecutionReport(clientResponse1);

        orderHistoryService.processClientNewOrderRequest(newOrderRequest2);
        orderHistoryService.processClientExecutionReport(clientResponse2);

        orderHistoryService.processClientNewOrderRequest(newOrderRequest3);
        orderHistoryService.processClientExecutionReport(clientResponse3);

        // when
        String from = ZonedDateTime.now().minusMinutes(7).toOffsetDateTime().format(DateTimeFormatter.ISO_DATE_TIME);
        CursorConnection orderHistory = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(),
            List.of(new DatePredicateInput(FROM, DatePredicateInput.Field.CREATED_AT, from)), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory.getEdgesList()).isNotEmpty();
        assertThat(orderHistory.getEdgesList().size()).isEqualTo(2);
    }

    @Test
    void twoPortfolioIdsPassed_shouldReturnBoth() {
        // given
        ClientRequest newOrderSingleRequest1 = clientNewOrderRequest(TestingData.PORTFOLIO_A, ClientOrderType.LIMIT, ZonedDateTime.now());
        ClientRequest newOrderSingleRequest2 = clientNewOrderRequest(TestingData.PORTFOLIO_B, ClientOrderType.LIMIT, ZonedDateTime.now());
        orderHistoryService.processClientNewOrderRequest(newOrderSingleRequest1);
        orderHistoryService.processClientNewOrderRequest(newOrderSingleRequest2);
        
        // when
        List<SimplePredicateInput> simplePredicateInputs = List.of(
            new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, TestingData.PORTFOLIO_A),
            new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, TestingData.PORTFOLIO_B)
        );
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(simplePredicateInputs, List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(2);
    }

    @ParameterizedTest(name = "predicateFilteringTest {0}")
    @MethodSource("predicate_verificationMethod")
    void predicateFilteringTest(List<SimplePredicateInput> simplePredicateInputs, List<CollectionPredicateInput> collectionPredicateInput, Function<OrderState, Boolean> verificationFunction) {
        // given
        ClientRequest clientRequest = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now());
        ClientResponse clientResponse1 = expectedExecutionReportNew(1, clientRequest.getOrderId(), clientRequest.getClOrderId(), ClientOrderStatus.NEW, clientRequest.getVenueAccounts(0));
        ClientResponse clientResponse2 = expectedExecutionReportNew(1, clientRequest.getOrderId(), clientRequest.getClOrderId(), ClientOrderStatus.FILLED, clientRequest.getVenueAccounts(0));
        orderHistoryService.processClientNewOrderRequest(clientRequest);
        orderHistoryService.processClientExecutionReport(clientResponse1);
        orderHistoryService.processClientExecutionReport(clientResponse2);

        // when
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(simplePredicateInputs, collectionPredicateInput, List.of(), null, null, SortingOrder.DESC, null));

        // then
        orderHistory.forEach(orderState -> Assertions.assertTrue(verificationFunction.apply(orderState)));
    }

    @Test
    void datePredicate_shouldReturnValidEntry() {
        // given
        ZonedDateTime now = ZonedDateTime.of(2024, 1, 1, 12, 0, 0, 0, ZoneId.of("UTC"));
        ZonedDateTime oneHourBefore = now.minusHours(1);

        ClientRequest clientRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, now);
        ClientRequest clientRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, oneHourBefore);

        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);

        // when
        DatePredicateInput datePredicateInput1 = new DatePredicateInput(FROM, UPDATED_AT, now.toOffsetDateTime().format(DateTimeFormatter.ISO_DATE_TIME));
        DatePredicateInput datePredicateInput2 = new DatePredicateInput(TO, CREATED_AT, now.minusMinutes(15).toOffsetDateTime().format(DateTimeFormatter.ISO_DATE_TIME));

        List<OrderState> orderHistory1 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(datePredicateInput1), null, null, SortingOrder.DESC, null));
        List<OrderState> orderHistory2 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(datePredicateInput2), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory1)
            .hasSize(1)
            .first()
            .extracting(OrderState::getOrderId)
            .isEqualTo(clientRequest1.getOrderId());
        assertThat(orderHistory2)
            .hasSize(1)
            .first()
            .extracting(OrderState::getOrderId)
            .isEqualTo(clientRequest2.getOrderId());
    }

    @Test
    void collectionPredicate_shouldReturnAllData() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest clientRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, now);
        ClientRequest clientRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, now);
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);

        // when
        CollectionPredicateInput collectionPredicateInput1 = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.ORDER_ID, Set.of(clientRequest1.getOrderId(), clientRequest2.getOrderId()));
        List<OrderState> orderHistory1 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput1), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged1 = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput1), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory1).hasSize(2);
        assertThat(orderHistoryPaged1.getEdgesList()).hasSize(2);

        // when
        CollectionPredicateInput collectionPredicateInput2 = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.ORDER_ID, Set.of(clientRequest1.getOrderId()));
        List<OrderState> orderHistory2 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput2), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged2 = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput2), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory2).hasSize(1).first().extracting(OrderState::getOrderId).isEqualTo(clientRequest1.getOrderId());
        assertThat(orderHistoryPaged2.getEdgesList()).hasSize(1).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderId).isEqualTo(clientRequest1.getOrderId());
    }

    @Test
    void singlePredicate_shouldReturnAllDataByVenueAccountId() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest clientRequest1 = withRandomVenueAccount(clientNewOrderRequest(ClientOrderType.LIMIT, now));
        ClientRequest clientRequest2 = withRandomVenueAccount(clientNewOrderRequest(ClientOrderType.LIMIT, now));
        ClientRequest clientRequest3 = withRandomVenueAccount(clientNewOrderRequest(ClientOrderType.LIMIT, now));
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);
        orderHistoryService.processClientNewOrderRequest(clientRequest3);

        // when
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, clientRequest1.getVenueAccounts(0));
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).hasSize(1).first().extracting(OrderState::getVenueAccountId).isEqualTo(clientRequest1.getVenueAccounts(0));
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getVenueAccountId).isEqualTo(clientRequest1.getVenueAccounts(0));
    }

    private ClientRequest withRandomVenueAccount(ClientRequest clientRequest) {
        return clientRequest.toBuilder()
            .clearVenueAccounts()
            .addVenueAccounts(UUID.randomUUID().toString())
            .build();
    }

    @Test
    void singlePredicate_shouldReturnAllDataByVenueAccountIdTwoInputs() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest clientRequest1 = withRandomVenueAccount(clientNewOrderRequest(ClientOrderType.LIMIT, now));
        ClientRequest clientRequest2 = withRandomVenueAccount(clientNewOrderRequest(ClientOrderType.LIMIT, now));
        ClientRequest clientRequest3 = withRandomVenueAccount(clientNewOrderRequest(ClientOrderType.LIMIT, now));
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);
        orderHistoryService.processClientNewOrderRequest(clientRequest3);
        List<String> venueAccountIdList = List.of(clientRequest1.getVenueAccounts(0), clientRequest2.getVenueAccounts(0));

        // when
        SimplePredicateInput simplePredicateInput1 = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, clientRequest1.getVenueAccounts(0));
        SimplePredicateInput simplePredicateInput2 = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, clientRequest2.getVenueAccounts(0));
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput1, simplePredicateInput2), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput1, simplePredicateInput2), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(2);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(2);
        assertThat(orderHistory).extracting(OrderState::getVenueAccountId).containsAnyElementsOf(venueAccountIdList);
        assertThat(orderHistoryPaged.getEdgesList()).extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getVenueAccountId).containsAnyElementsOf(venueAccountIdList);
    }

    @Test
    void collectionPredicate_shouldReturnAllDataByVenueAccountId() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest clientRequest1 = withRandomVenueAccount(clientNewOrderRequest(ClientOrderType.LIMIT, now));
        ClientRequest clientRequest2 = withRandomVenueAccount(clientNewOrderRequest(ClientOrderType.LIMIT, now));
        ClientRequest clientRequest3 = withRandomVenueAccount(clientNewOrderRequest(ClientOrderType.LIMIT, now));
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);
        orderHistoryService.processClientNewOrderRequest(clientRequest3);
        List<String> venueAccountIdList = List.of(clientRequest1.getVenueAccounts(0), clientRequest2.getVenueAccounts(0));

        // when
        CollectionPredicateInput collectionPredicateInput = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.VENUE_ACCOUNT_ID, venueAccountIdList);
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(2);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(2);
        assertThat(orderHistory).extracting(OrderState::getVenueAccountId).containsAnyElementsOf(venueAccountIdList);
        assertThat(orderHistoryPaged.getEdgesList()).extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getVenueAccountId).containsAnyElementsOf(venueAccountIdList);
    }

    @Test
    void orderHistory_shouldReturnEmptyList() {
        // when
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).isEmpty();
        assertThat(orderHistoryPaged.getEdgesList()).isEmpty();
    }

    @Test
    void orderHistory_orderUpdatedWithReportHasUpdatedState() {
        // given
        ClientRequest newOrderRequest = clientNewOrderRequest(ClientOrderType.LIMIT, null);
        ClientResponse clientResponse = expectedExecutionReportNew(1, newOrderRequest.getOrderId(), newOrderRequest.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest.getVenueAccounts(0));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest);
        orderHistoryService.processClientExecutionReport(clientResponse);

        // when
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.NEW);
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.NEW);
        assertThat(orderHistory).last().extracting(OrderState::getSequenceNumber).isEqualTo(2);
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getSequenceNumber).isEqualTo(2);
    }

    @Test
    void orderHistory_orderUpdatedWithMultipleReportsHasStateFromLastUpdate() {
        // given
        ClientRequest newOrderRequest = clientNewOrderRequest(ClientOrderType.LIMIT, null);
        ClientResponse clientResponse1 = expectedExecutionReportNew(1, newOrderRequest.getOrderId(), newOrderRequest.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse2 = expectedExecutionReportNew(2, newOrderRequest.getOrderId(), newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse3 = expectedExecutionReportNew(3, newOrderRequest.getOrderId(), newOrderRequest.getClOrderId(), ClientOrderStatus.FILLED, newOrderRequest.getVenueAccounts(0));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest);
        orderHistoryService.processClientExecutionReport(clientResponse1);
        orderHistoryService.processClientExecutionReport(clientResponse2);
        orderHistoryService.processClientExecutionReport(clientResponse3);

        // when
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.FILLED);
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.FILLED);
        assertThat(orderHistory).last().extracting(OrderState::getSequenceNumber).isEqualTo(4);
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getSequenceNumber).isEqualTo(4);
    }

    @Test
    void orderHistory_unknownReportIsIgnored() {
        // given
        ClientResponse clientResponse = expectedExecutionReportNew(1, UUID.randomUUID().toString(), UUID.randomUUID().toString(), ClientOrderStatus.NEW, "venueAccount1");
        orderHistoryService.processClientExecutionReport(clientResponse);

        // when
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).isEmpty();
        assertThat(orderHistoryPaged.getEdgesList()).isEmpty();
    }

    @Test
    void testLimit() {
        // given
        ClientRequest clientRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now().minusMinutes(1));
        ClientRequest clientRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now());
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);

        // when
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(), 1, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(), List.of(), 1, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getOrderId).isEqualTo(clientRequest2.getOrderId());
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderId).isEqualTo(clientRequest2.getOrderId());
    }

    @Test
    void testQueryBySymbol() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest clientRequest1 = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID, ClientOrderType.LIMIT, now);
        ClientRequest clientRequest2 = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID_2, ClientOrderType.LIMIT, now);
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);

        // when
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.INSTRUMENT_ID, TestingData.INTEGRATION_TEST_INSTRUMENT_ID);
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getInstrumentId).isEqualTo(TestingData.INTEGRATION_TEST_INSTRUMENT_ID);
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getInstrumentId).isEqualTo(TestingData.INTEGRATION_TEST_INSTRUMENT_ID);
    }

    @Test
    void testQueryByOrderId() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest clientRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, now);
        ClientRequest clientRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, now);
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);

        // when
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, clientRequest2.getOrderId());
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getOrderId).isEqualTo(clientRequest2.getOrderId());
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderId).isEqualTo(clientRequest2.getOrderId());
    }

    @Test
    void testQueryByClOrderId() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest clientRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, now);
        ClientRequest clientRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, now);
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);

        // when
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.CL_ORDER_ID, clientRequest2.getClOrderId());
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getClOrderId).isEqualTo(clientRequest2.getClOrderId());
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getClOrderId).isEqualTo(clientRequest2.getClOrderId());
    }

    @Test
    void testQueryByPortfolio() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest clientRequest1 = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.PORTFOLIO_A, ClientOrderType.LIMIT, now);
        ClientRequest clientRequest2 = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.PORTFOLIO_B, ClientOrderType.LIMIT, now);
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);

        // when
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, TestingData.PORTFOLIO_B);
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getPortfolioId).isEqualTo(TestingData.PORTFOLIO_B);
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getPortfolioId).isEqualTo(TestingData.PORTFOLIO_B);
    }

    @Test
    void testTrackAndReturnOemsOrders() {
        // given
        OemsRequest order1 = oemsRequest("o1", "p1", TestingData.ROOT_A);
        orderHistoryService.processOemsRequest(order1);

        // when
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, "o1");
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getOrderId).isEqualTo("o1");
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderId).isEqualTo("o1");
    }

    @Test
    void whenProcessOemsRequest_clOrderIdIsSavedInOrderState() {
        // given
        OemsRequest order1 = oemsRequest("o1", "p1", TestingData.ROOT_A);
        orderHistoryService.processOemsRequest(order1);

        // when
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, "o1");
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStatesPaged(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getOrderId).isEqualTo("o1");
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState)
            .extracting(OrderState::getOrderId, OrderState::getClOrderId).containsExactly("o1", CLIENT_ROOT_ORDER_ID);
    }

    @Test
    void sameOrderWithClientRequestAndOemsRequestIsNotDuplicated() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest clientRequest1 = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.PORTFOLIO_A, ClientOrderType.LIMIT, now);
        orderHistoryService.processClientNewOrderRequest(clientRequest1);

        OemsRequest order1 = oemsRequest(clientRequest1.getOrderId(), "p1", TestingData.ROOT_A);
        orderHistoryService.processOemsRequest(order1);

        // when
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, clientRequest1.getOrderId());
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getOrderId).isEqualTo(clientRequest1.getOrderId());
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderId).isEqualTo(clientRequest1.getOrderId());
    }

    @Test
    void testQueryByRootOrderId() {
        // given
        OemsRequest order1 = oemsRequest("o1", "p1", TestingData.ROOT_A);
        OemsRequest order2 = oemsRequest("o2", "p2", TestingData.ROOT_A);
        OemsRequest order3 = oemsRequest("o3", "p3", TestingData.ROOT_B); // should not return
        orderHistoryService.processOemsRequest(order1);
        orderHistoryService.processOemsRequest(order2);
        orderHistoryService.processOemsRequest(order3);

        // when
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ROOT_ORDER_ID, TestingData.ROOT_A);
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(2);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(2);
        assertThat(orderHistory).last().extracting(OrderState::getRootOrderId).isEqualTo(TestingData.ROOT_A);
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getRootOrderId).isEqualTo(TestingData.ROOT_A);
    }

    @Test
    void testQueryByIsClosed() {
        // given
        ZonedDateTime now = ZonedDateTime.now();

        ClientRequest clientRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, now.minusSeconds(10));
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        ClientResponse clientResponseRequest1 = expectedExecutionReportNew(1, clientRequest1.getOrderId(), clientRequest1.getClOrderId(), ClientOrderStatus.NEW, clientRequest1.getVenueAccounts(0));
        orderHistoryService.processClientExecutionReport(clientResponseRequest1);

        ClientRequest clientRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, now.minusSeconds(9));
        orderHistoryService.processClientNewOrderRequest(clientRequest2);
        ClientResponse clientResponse1Request2 = expectedExecutionReportNew(1, clientRequest2.getOrderId(), clientRequest2.getClOrderId(), ClientOrderStatus.NEW, clientRequest1.getVenueAccounts(0));
        orderHistoryService.processClientExecutionReport(clientResponse1Request2);
        ClientResponse clientResponse2Request2 = expectedExecutionReportNew(2, clientRequest2.getOrderId(), clientRequest2.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, clientRequest2.getVenueAccounts(0));
        orderHistoryService.processClientExecutionReport(clientResponse2Request2);
        ClientResponse clientResponse3Request2 = expectedExecutionReportNew(3, clientRequest2.getOrderId(), clientRequest2.getClOrderId(), ClientOrderStatus.FILLED, clientRequest2.getVenueAccounts(0));
        orderHistoryService.processClientExecutionReport(clientResponse3Request2);

        // when
        CollectionPredicateInput collectionPredicateInput = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.ORDER_STATUS, TestingData.CLOSED_ORDER_STATUSES.stream().map(Enum::name).toList());
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).first().extracting(OrderState::getOrderStatus).isIn(TestingData.CLOSED_ORDER_STATUSES);
        assertThat(orderHistory).first().extracting(OrderState::getOrderStatus).isNotIn(TestingData.OPEN_ORDER_STATUSES);
        assertThat(orderHistory).first().extracting(OrderState::getOrderId).isEqualTo(clientRequest2.getOrderId());
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).isIn(TestingData.CLOSED_ORDER_STATUSES);
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).isNotIn(TestingData.OPEN_ORDER_STATUSES);
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderId).isEqualTo(clientRequest2.getOrderId());
    }

    @Test
    void testQueryByIsOpen() {
        // given
        ZonedDateTime now = ZonedDateTime.now();

        ClientRequest clientRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, now.minusSeconds(10));
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        ClientResponse clientResponseRequest1 = expectedExecutionReportNew(1, clientRequest1.getOrderId(), clientRequest1.getClOrderId(), ClientOrderStatus.NEW, clientRequest1.getVenueAccounts(0));
        orderHistoryService.processClientExecutionReport(clientResponseRequest1);

        ClientRequest clientRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, now.minusSeconds(9));
        orderHistoryService.processClientNewOrderRequest(clientRequest2);
        ClientResponse clientResponse1Request2 = expectedExecutionReportNew(1, clientRequest2.getOrderId(), clientRequest2.getClOrderId(), ClientOrderStatus.NEW, clientRequest1.getVenueAccounts(0));
        orderHistoryService.processClientExecutionReport(clientResponse1Request2);
        ClientResponse clientResponse2Request2 = expectedExecutionReportNew(2, clientRequest2.getOrderId(), clientRequest2.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, clientRequest2.getVenueAccounts(0));
        orderHistoryService.processClientExecutionReport(clientResponse2Request2);
        ClientResponse clientResponse3Request2 = expectedExecutionReportNew(3, clientRequest2.getOrderId(), clientRequest2.getClOrderId(), ClientOrderStatus.FILLED, clientRequest2.getVenueAccounts(0));
        orderHistoryService.processClientExecutionReport(clientResponse3Request2);

        // when
        CollectionPredicateInput collectionPredicateInput = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.ORDER_STATUS, TestingData.OPEN_ORDER_STATUSES.stream().map(Enum::name).toList());
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).first().extracting(OrderState::getOrderStatus).isIn(TestingData.OPEN_ORDER_STATUSES);
        assertThat(orderHistory).first().extracting(OrderState::getOrderStatus).isNotIn(TestingData.CLOSED_ORDER_STATUSES);
        assertThat(orderHistory).first().extracting(OrderState::getOrderId).isEqualTo(clientRequest1.getOrderId());
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).isIn(TestingData.OPEN_ORDER_STATUSES);
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).isNotIn(TestingData.CLOSED_ORDER_STATUSES);
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderId).isEqualTo(clientRequest1.getOrderId());
    }

    @Test
    void orderHistory_shouldFilterByOrderStatus() {
        // given
        ZonedDateTime now = ZonedDateTime.now();

        ClientRequest clientRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, now.minusSeconds(10));
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        ClientResponse clientResponseRequest1 = expectedExecutionReportNew(1, clientRequest1.getOrderId(), clientRequest1.getClOrderId(), ClientOrderStatus.PENDING_NEW, clientRequest1.getVenueAccounts(0));
        orderHistoryService.processClientExecutionReport(clientResponseRequest1);

        ClientRequest clientRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, now.minusSeconds(9));
        orderHistoryService.processClientNewOrderRequest(clientRequest2);
        ClientResponse clientResponseRequest2 = expectedExecutionReportNew(1, clientRequest2.getOrderId(), clientRequest2.getClOrderId(), ClientOrderStatus.NEW, clientRequest2.getVenueAccounts(0));
        orderHistoryService.processClientExecutionReport(clientResponseRequest2);

        ClientRequest clientRequest3 = clientNewOrderRequest(ClientOrderType.LIMIT, now.minusSeconds(8));
        orderHistoryService.processClientNewOrderRequest(clientRequest3);
        ClientResponse clientResponseRequest3 = expectedExecutionReportNew(2, clientRequest3.getOrderId(), clientRequest3.getClOrderId(), ClientOrderStatus.FILLED, clientRequest3.getVenueAccounts(0));
        orderHistoryService.processClientExecutionReport(clientResponseRequest3);

        // when
        SimplePredicateInput simplePredicateInput1 = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_STATUS, TestingData.PENDING_NEW);
        List<OrderState> orderHistory1 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput1), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged1 = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput1), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory1).hasSize(1);
        assertThat(orderHistoryPaged1.getEdgesList()).hasSize(1);
        assertThat(orderHistory1).first().extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.PENDING_NEW);
        assertThat(orderHistoryPaged1.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.PENDING_NEW);

        // when
        SimplePredicateInput simplePredicateInput2 = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_STATUS, TestingData.NEW);
        List<OrderState> orderHistory2 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput2), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged2 = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput2), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory2).hasSize(1);
        assertThat(orderHistoryPaged2.getEdgesList()).hasSize(1);
        assertThat(orderHistory2).first().extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.NEW);
        assertThat(orderHistoryPaged2.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.NEW);

        // when
        SimplePredicateInput simplePredicateInput3 = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_STATUS, TestingData.FILLED);
        List<OrderState> orderHistory3 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput3), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged3 = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput3), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory3).hasSize(1);
        assertThat(orderHistoryPaged3.getEdgesList()).hasSize(1);
        assertThat(orderHistory3).first().extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.FILLED);
        assertThat(orderHistoryPaged3.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.FILLED);
    }

    @Test
    void testQueryByMinDate() {
        // given
        ClientRequest newOrderRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, OffsetDateTime.now().minusMinutes(10).toZonedDateTime());
        ClientResponse clientResponse1 = expectedExecutionReportNew(1, newOrderRequest1.getOrderId(), newOrderRequest1.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest1.getVenueAccounts(0));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest1);
        orderHistoryService.processClientExecutionReport(clientResponse1);

        ClientRequest newOrderRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, OffsetDateTime.now().minusMinutes(5).toZonedDateTime());
        ClientResponse clientResponse2 = expectedExecutionReportNew(1, newOrderRequest2.getOrderId(), newOrderRequest2.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest2.getVenueAccounts(0));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest2);
        orderHistoryService.processClientExecutionReport(clientResponse2);

        ClientRequest newOrderRequest3 = clientNewOrderRequest(ClientOrderType.LIMIT, OffsetDateTime.now().toZonedDateTime());
        ClientResponse clientResponse3 = expectedExecutionReportNew(1, newOrderRequest3.getOrderId(), newOrderRequest3.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest3.getVenueAccounts(0));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest3);
        orderHistoryService.processClientExecutionReport(clientResponse3);

        // when
        ZonedDateTime expectedDateTime = ZonedDateTime.now().minusMinutes(7);
        String expectedDateTimeOffset = expectedDateTime.toOffsetDateTime().format(DateTimeFormatter.ISO_DATE_TIME);

        DatePredicateInput datePredicateInput = new DatePredicateInput(FROM, DatePredicateInput.Field.CREATED_AT, expectedDateTimeOffset);
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(datePredicateInput), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(), List.of(datePredicateInput), null, null, SortingOrder.DESC, null));

        // then
        ZoneId zoneId = ZoneId.of("Europe/Paris");

        assertThat(orderHistory)
            .isNotEmpty()
            .hasSize(2)
            .allMatch(orderState -> OffsetDateTime.parse(orderState.getCreatedAt()).atZoneSameInstant(zoneId).isAfter(expectedDateTime))
            .allMatch(orderState -> List.of(newOrderRequest2.getOrderId(), newOrderRequest3.getOrderId()).contains(orderState.getOrderId()));

        assertThat(orderHistoryPaged.getEdgesList())
            .isNotEmpty()
            .hasSize(2)
            .extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState)
            .allMatch(orderState -> OffsetDateTime.parse(orderState.getCreatedAt()).atZoneSameInstant(zoneId).isAfter(expectedDateTime))
            .allMatch(orderState -> List.of(newOrderRequest2.getOrderId(), newOrderRequest3.getOrderId()).contains(orderState.getOrderId()));
    }

    @Test
    void testQueryByMaxDate() {
        // given
        ClientRequest newOrderRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now().minusMinutes(10));
        ClientResponse clientResponse1 = expectedExecutionReportNew(1, newOrderRequest1.getOrderId(), newOrderRequest1.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest1.getVenueAccounts(0), ZonedDateTime.now().minusMinutes(10));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest1);
        orderHistoryService.processClientExecutionReport(clientResponse1);

        ClientRequest newOrderRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now().minusMinutes(5));
        ClientResponse clientResponse2 = expectedExecutionReportNew(1, newOrderRequest2.getOrderId(), newOrderRequest2.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest2.getVenueAccounts(0), ZonedDateTime.now().minusMinutes(5));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest2);
        orderHistoryService.processClientExecutionReport(clientResponse2);

        ClientRequest newOrderRequest3 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now());
        ClientResponse clientResponse3 = expectedExecutionReportNew(1, newOrderRequest3.getOrderId(), newOrderRequest3.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest3.getVenueAccounts(0));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest3);
        orderHistoryService.processClientExecutionReport(clientResponse3);

        // when
        ZonedDateTime expectedDateTime = ZonedDateTime.now().minusMinutes(7);
        String expectedDateTimeOffset = expectedDateTime.toOffsetDateTime().format(DateTimeFormatter.ISO_DATE_TIME);

        DatePredicateInput datePredicateInput = new DatePredicateInput(TO, UPDATED_AT, expectedDateTimeOffset);
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(datePredicateInput), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(), List.of(datePredicateInput), null, null, SortingOrder.DESC, null));

        // then
        ZoneId zoneId = ZoneId.of("Europe/Paris");

        assertThat(orderHistory)
            .isNotEmpty()
            .hasSize(1)
            .last()
            .matches(orderState -> OffsetDateTime.parse(orderState.getCreatedAt()).atZoneSameInstant(zoneId).isBefore(expectedDateTime))
            .matches(orderState -> orderState.getOrderId().equals(newOrderRequest1.getOrderId()));

        assertThat(orderHistoryPaged.getEdgesList())
            .isNotEmpty()
            .hasSize(1)
            .last()
            .extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState)
            .matches(orderState -> OffsetDateTime.parse(orderState.getCreatedAt()).atZoneSameInstant(zoneId).isBefore(expectedDateTime))
            .matches(orderState -> orderState.getOrderId().equals(newOrderRequest1.getOrderId()));
    }

    @Test
    void collectionPredicate_shouldReturnByOrderCategory() {
        // given
        ClientRequest clientRequest1 = TestingData.clientNewSorOrderRequest(ClientOrderType.MARKET);
        ClientRequest clientRequest2 = clientNewOrderRequest(ClientOrderType.MARKET, ZonedDateTime.now());
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);
        orderHistoryService.processOemsRequest(oemsRequest(clientRequest1));
        orderHistoryService.processOemsRequest(oemsRequest(clientRequest2));
        orderHistoryService.processOemsRequest(oemsRequest(UUID.randomUUID().toString(), clientRequest1.getOrderId()));
        orderHistoryService.processOemsRequest(oemsRequest(UUID.randomUUID().toString(), clientRequest1.getOrderId()));

        // when
        List<OrderCategory> ordersCategory1 = List.of(OrderCategory.SOR_CHILD_ORDER, OrderCategory.DIRECT_MARKET_ACCESS_ORDER);
        CollectionPredicateInput collectionPredicateInput1 = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.ORDER_CATEGORY, ordersCategory1.stream().map(Enum::name).toList());
        List<OrderState> orderHistory1 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput1), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged1 = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput1), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory1)
            .hasSize(3)
            .allMatch(orderState -> ordersCategory1.contains(orderState.getOrderCategory()));

        assertThat(orderHistoryPaged1.getEdgesList())
            .hasSize(3)
            .extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState)
            .allMatch(orderState -> ordersCategory1.contains(orderState.getOrderCategory()));

        // when
        List<OrderCategory> ordersCategory2 = List.of(OrderCategory.SOR_CHILD_ORDER);
        CollectionPredicateInput collectionPredicateInput2 = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.ORDER_CATEGORY, ordersCategory2.stream().map(Enum::name).toList());
        List<OrderState> orderHistory2 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput2), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged2 = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput2), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory2)
            .hasSize(2)
            .allMatch(orderState -> ordersCategory2.contains(orderState.getOrderCategory()));

        assertThat(orderHistoryPaged2.getEdgesList())
            .hasSize(2)
            .extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState)
            .allMatch(orderState -> ordersCategory2.contains(orderState.getOrderCategory()));

        // when
        List<OrderCategory> ordersCategory3 = List.of(OrderCategory.SOR_ORDER);
        CollectionPredicateInput collectionPredicateInput3 = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.ORDER_CATEGORY, ordersCategory3.stream().map(Enum::name).toList());
        List<OrderState> orderHistory3 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput3), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged3 = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput3), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory3)
            .hasSize(1)
            .allMatch(orderState -> ordersCategory3.contains(orderState.getOrderCategory()));

        assertThat(orderHistoryPaged3.getEdgesList())
            .hasSize(1)
            .extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState)
            .allMatch(orderState -> ordersCategory3.contains(orderState.getOrderCategory()));
    }

    @Test
    void collectionPredicate_shouldReturnByParentOrderId() {
        // given
        ClientRequest clientRequest1 = clientNewOrderRequest(ClientOrderType.MARKET, ZonedDateTime.now());
        ClientRequest clientRequest2 = clientNewOrderRequest(ClientOrderType.MARKET, ZonedDateTime.now());
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);

        orderHistoryService.processOemsRequest(oemsRequest(UUID.randomUUID().toString(), clientRequest1.getOrderId()));
        orderHistoryService.processOemsRequest(oemsRequest(UUID.randomUUID().toString(), clientRequest1.getOrderId()));
        orderHistoryService.processOemsRequest(oemsRequest(UUID.randomUUID().toString(), clientRequest2.getOrderId()));

        // when
        SimplePredicateInput simplePredicateInput1 = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.PARENT_ORDER_ID, clientRequest1.getOrderId());
        List<OrderState> orderHistory1 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput1), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged1 = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput1), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory1)
            .hasSize(2)
            .allMatch(orderState -> orderState.getParentOrderId().equals(clientRequest1.getOrderId()));

        assertThat(orderHistoryPaged1.getEdgesList())
            .hasSize(2)
            .extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState)
            .allMatch(orderState -> orderState.getParentOrderId().equals(clientRequest1.getOrderId()));

        // when
        SimplePredicateInput simplePredicateInput2 = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.PARENT_ORDER_ID, clientRequest2.getOrderId());
        List<OrderState> orderHistory2 = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput2), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged2 = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput2), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory2)
            .hasSize(1)
            .allMatch(orderState -> orderState.getParentOrderId().equals(clientRequest2.getOrderId()));

        assertThat(orderHistoryPaged2.getEdgesList())
            .hasSize(1)
            .extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState)
            .allMatch(orderState -> orderState.getParentOrderId().equals(clientRequest2.getOrderId()));
    }

    @Test
    void collectionPredicate_shouldHandleProblematicPortfolioNamesWhenGettingOrderHistory() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        final String problematicPortfolioId1 = "\\";
        final String problematicPortfolioId2 = "[;;'.";
        final String problematicPortfolioId3 = "' OR '1";
        ClientRequest clientRequest1 = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.PORTFOLIO_A, ClientOrderType.LIMIT, now);
        ClientRequest clientRequest2 = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.PORTFOLIO_B, ClientOrderType.LIMIT, now);
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);

        // when
        CollectionPredicateInput collectionPredicateInput = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.PORTFOLIO_ID, Set.of(TestingData.PORTFOLIO_B, problematicPortfolioId1, problematicPortfolioId2, problematicPortfolioId3));
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getPortfolioId).isEqualTo(TestingData.PORTFOLIO_B);
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getPortfolioId).isEqualTo(TestingData.PORTFOLIO_B);
    }

    @Test
    void collectionPredicate_shouldHandleProblematicVenueAccountIdWhenGettingOrderHistory() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        final String problematicVenueAccount1 = "\\";
        final String problematicVenueAccount2 = "[;;'.";
        final String problematicVenueAccount3 = "' OR '1";
        ClientRequest clientRequest1 = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.PORTFOLIO_A, ClientOrderType.LIMIT, now).toBuilder()
            .clearVenueAccounts()
            .addVenueAccounts("test-1").build();
        ClientRequest clientRequest2 = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.PORTFOLIO_B, ClientOrderType.LIMIT, now).toBuilder()
            .clearVenueAccounts()
            .addVenueAccounts("test-2").build();
        String venueAccount = "test-1";
        orderHistoryService.processClientNewOrderRequest(clientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientRequest2);

        // when
        CollectionPredicateInput collectionPredicateInput = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.VENUE_ACCOUNT_ID, Set.of(venueAccount, problematicVenueAccount1, problematicVenueAccount2, problematicVenueAccount3));
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).last().extracting(OrderState::getVenueAccountsList).isEqualTo(clientRequest1.getVenueAccountsList());
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getVenueAccountsList).isEqualTo(clientRequest1.getVenueAccountsList());
    }


    @Test
    void simplePredicate_shouldReturnOrderStatesByVenueAccountId() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest streetClientRequest1 = clientNewOrderRequest("streetOrder1", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_1, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now.minusMinutes(20));
        ClientRequest streetClientRequest2 = clientNewOrderRequest("streetOrder2", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_2, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now.minusMinutes(15));
        ClientRequest streetClientRequest3 = clientNewOrderRequest("streetOrder3", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_1, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(10));
        ClientRequest streetClientRequest4 = clientNewOrderRequest("streetOrder4", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_2, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(5));
        ClientRequest clientClientRequest1 = clientNewOrderRequest("clientOrder1", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, null, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(3));
        ClientRequest clientClientRequest2 = clientNewOrderRequest("clientOrder2", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, null, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest1);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest2);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest3);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest4);
        orderHistoryService.processClientNewOrderRequest(clientClientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientClientRequest2);

        // when
        SimplePredicateInput simplePredicateInput1 = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, TestingData.VENUE_1);
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput1), List.of(), List.of(), null, null, SortingOrder.ASC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput1), List.of(), List.of(), null, null, SortingOrder.ASC, null));

        // then
        assertThat(orderHistory).hasSize(2);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(2);
        assertThat(orderHistory).first().extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_1));
        assertThat(orderHistory).last().extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_1));
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_1));
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_1));
    }

    @Test
    void simplePredicate_shouldReturnOrderStatesByVenueAccountIdAndPortfolioId() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest streetClientRequest1 = clientNewOrderRequest("streetOrder1", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_1, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now.minusMinutes(20));
        ClientRequest streetClientRequest2 = clientNewOrderRequest("streetOrder2", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_2, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now.minusMinutes(15));
        ClientRequest streetClientRequest3 = clientNewOrderRequest("streetOrder3", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_1, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(10));
        ClientRequest streetClientRequest4 = clientNewOrderRequest("streetOrder4", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_2, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(5));
        ClientRequest clientClientRequest1 = clientNewOrderRequest("clientOrder1", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, null, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(3));
        ClientRequest clientClientRequest2 = clientNewOrderRequest("clientOrder2", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, null, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest1);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest2);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest3);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest4);
        orderHistoryService.processClientNewOrderRequest(clientClientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientClientRequest2);

        // when
        SimplePredicateInput simplePredicateInput1 = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, TestingData.VENUE_1);
        SimplePredicateInput simplePredicateInput2 = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, TestingData.PORTFOLIO_A);
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput1, simplePredicateInput2), List.of(), List.of(), null, null, SortingOrder.ASC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput1, simplePredicateInput2), List.of(), List.of(), null, null, SortingOrder.ASC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).first().extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_1));
        assertThat(orderHistory).first().extracting(OrderState::getPortfolioId).isEqualTo(TestingData.PORTFOLIO_A);
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_1));
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getPortfolioId).isEqualTo(TestingData.PORTFOLIO_A);
    }

    @Test
    void collectionPredicate_shouldReturnAllOrderStatesByVenueAccountId() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest streetClientRequest1 = clientNewOrderRequest("streetOrder1", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_1, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now.minusMinutes(20));
        ClientRequest streetClientRequest2 = clientNewOrderRequest("streetOrder2", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_2, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now.minusMinutes(15));
        ClientRequest streetClientRequest3 = clientNewOrderRequest("streetOrder3", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_1, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(10));
        ClientRequest streetClientRequest4 = clientNewOrderRequest("streetOrder4", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_2, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(5));
        ClientRequest clientClientRequest1 = clientNewOrderRequest("clientOrder1", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, null, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(3));
        ClientRequest clientClientRequest2 = clientNewOrderRequest("clientOrder2", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, null, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest1);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest2);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest3);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest4);
        orderHistoryService.processClientNewOrderRequest(clientClientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientClientRequest2);

        // when
        CollectionPredicateInput collectionPredicateInput1 = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.VENUE_ACCOUNT_ID, List.of(TestingData.VENUE_2));
//        CollectionPredicateInput collectionPredicateInput1 = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.VENUE_ACCOUNT_ID, List.of(TestingData.VENUE_2, TestingData.VENUE_1));

        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput1), List.of(), null, null, SortingOrder.ASC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput1), List.of(), null, null, SortingOrder.ASC, null));

        // then
        assertThat(orderHistory).hasSize(2);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(2);
        assertThat(orderHistory).first().extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_2));
        assertThat(orderHistory).last().extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_2));
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_2));
        assertThat(orderHistoryPaged.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_2));
    }

    @Test
    void collectionPredicate_shouldReturnOrderStateByVenueAccountIdAndPortfolioId() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest streetClientRequest1 = clientNewOrderRequest("streetOrder1", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_1, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now.minusMinutes(20));
        ClientRequest streetClientRequest2 = clientNewOrderRequest("streetOrder2", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_2, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now.minusMinutes(15));
        ClientRequest streetClientRequest3 = clientNewOrderRequest("streetOrder3", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_1, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(10));
        ClientRequest streetClientRequest4 = clientNewOrderRequest("streetOrder4", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_2, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(5));
        ClientRequest clientClientRequest1 = clientNewOrderRequest("clientOrder1", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, null, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(3));
        ClientRequest clientClientRequest2 = clientNewOrderRequest("clientOrder2", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, null, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest1);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest2);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest3);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest4);
        orderHistoryService.processClientNewOrderRequest(clientClientRequest1);
        orderHistoryService.processClientNewOrderRequest(clientClientRequest2);

        // when
        CollectionPredicateInput collectionPredicateInput1 = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.VENUE_ACCOUNT_ID, List.of(TestingData.VENUE_2));
        CollectionPredicateInput collectionPredicateInput2 = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.PORTFOLIO_ID, List.of(TestingData.PORTFOLIO_B));
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput1, collectionPredicateInput2), List.of(), null, null, SortingOrder.ASC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput1, collectionPredicateInput2), List.of(), null, null, SortingOrder.ASC, null));

        // then
        assertThat(orderHistory).hasSize(1);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(1);
        assertThat(orderHistory).first().extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_2));
        assertThat(orderHistory).first().extracting(OrderState::getPortfolioId).isEqualTo(TestingData.PORTFOLIO_B);
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getVenueAccountsList).isEqualTo(List.of(TestingData.VENUE_2));
        assertThat(orderHistoryPaged.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getPortfolioId).isEqualTo(TestingData.PORTFOLIO_B);
    }

    @Test
    void collectionPredicate_shouldNotReturnOrderStatesByWrongVenueAccountId() {
        // given
        ZonedDateTime now = ZonedDateTime.now();
        ClientRequest streetClientRequest1 = clientNewOrderRequest("streetOrder1", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_1, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now.minusMinutes(20));
        ClientRequest streetClientRequest2 = clientNewOrderRequest("streetOrder2", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_2, TestingData.PORTFOLIO_A, ClientOrderType.MARKET, now.minusMinutes(15));
        ClientRequest streetClientRequest3 = clientNewOrderRequest("streetOrder3", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_1, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(10));
        ClientRequest streetClientRequest4 = clientNewOrderRequest("streetOrder4", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.VENUE_2, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(5));
        ClientRequest streetClientRequest5 = clientNewOrderRequest("streetOrder5", TestingData.INTEGRATION_TEST_INSTRUMENT_ID, null, TestingData.PORTFOLIO_B, ClientOrderType.MARKET, now.minusMinutes(0));
        orderHistoryService.processClientNewOrderRequest(streetClientRequest1);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest2);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest3);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest4);
        orderHistoryService.processClientNewOrderRequest(streetClientRequest5);

        // when
        SimplePredicateInput simplePredicateInput1 = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, TestingData.ZERO_STRING);
        CollectionPredicateInput collectionPredicateInput1 = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.PORTFOLIO_ID, List.of(TestingData.PORTFOLIO_A, TestingData.PORTFOLIO_B));
        List<OrderState> orderHistory = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(simplePredicateInput1), List.of(collectionPredicateInput1), List.of(), 50, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(simplePredicateInput1), List.of(collectionPredicateInput1), List.of(), 50, null, SortingOrder.DESC, null));

        // then
        assertThat(orderHistory).hasSize(0);
        assertThat(orderHistoryPaged.getEdgesList()).hasSize(0);
    }

    @Test
    void shouldStoreOrderStateWithCounterPortfolioId() {
        // given
        ClientRequest newOrderRequest = clientNewOrderRequest(ClientOrderType.LIMIT, null);
        ClientResponse clientResponse = expectedExecutionReportNew(1, newOrderRequest.getOrderId(), newOrderRequest.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse2 = expectedExecutionReportNew(1, newOrderRequest.getOrderId(), newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0), TestingData.PORTFOLIO_B);

        // when
        orderHistoryService.processClientNewOrderRequest(newOrderRequest);
        orderHistoryService.processClientExecutionReport(clientResponse);
        orderHistoryService.processClientExecutionReport(clientResponse2);

        // then
        Optional<OrderStateEntity> orderState = orderHistoryService.getOrderStateSnapshotByOrderId(clientResponse.getOrderId());
        assertThat(orderState).isNotEmpty();
        assertThat(orderState).hasValueSatisfying(orderStateEntity -> assertThat(orderStateEntity.counterPortfolioId()).isEqualTo(TestingData.PORTFOLIO_B));
    }

    @Test
    void orderHistory_orderUpdatedWithMultipleReportsHasStateFromLastUpdateAndAllStatesInDB() {
        // given
        ClientRequest newOrderRequest = clientNewOrderRequest(ClientOrderType.LIMIT, null);
        String orderId = newOrderRequest.getOrderId();
        ClientResponse clientResponse1 = expectedExecutionReportNew(1, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse2 = expectedExecutionReportNew(2, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse3 = expectedExecutionReportNew(3, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.FILLED, newOrderRequest.getVenueAccounts(0));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest);
        orderHistoryService.processClientExecutionReport(clientResponse1);
        orderHistoryService.processClientExecutionReport(clientResponse2);
        orderHistoryService.processClientExecutionReport(clientResponse3);

        // when
        List<OrderState> orderStateSnapshots = orderHistoryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection pagedOrderStateSnapshots = orderHistoryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderStateSnapshots).hasSize(1);
        assertThat(pagedOrderStateSnapshots.getEdgesList()).hasSize(1);
        assertThat(orderStateSnapshots).last().extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.FILLED);
        assertThat(pagedOrderStateSnapshots.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.FILLED);
        assertThat(orderStateSnapshots).last().extracting(OrderState::getSequenceNumber).isEqualTo(4);
        assertThat(pagedOrderStateSnapshots.getEdgesList()).last().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getSequenceNumber).isEqualTo(4);

        // when
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        List<OrderState> orderStates = orderHistoryService.getOrderStates(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection pagedOrderStates = orderHistoryService.getOrderStatesPaged(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        OrderStatus[] orderStatuses = {OrderStatus.PENDING_NEW, OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED, OrderStatus.FILLED};
        assertThat(orderStates).hasSize(4);
        assertThat(pagedOrderStates.getEdgesList()).hasSize(4);
        assertThat(orderStates).extracting(OrderState::getOrderStatus).containsOnlyOnce(orderStatuses);
        assertThat(pagedOrderStates.getEdgesList()).extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).containsOnlyOnce(orderStatuses);

        assertThat(orderStates).extracting(OrderState::getOrderId).contains(orderId);
        assertThat(pagedOrderStates.getEdgesList()).extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderId).containsOnly(orderId);
    }

    @Test
    void orderStates_ShouldReturnAllStates() {
        // given
        ClientRequest newOrderRequest = clientNewOrderRequest(ClientOrderType.LIMIT, null);
        String orderId = newOrderRequest.getOrderId();
        ClientResponse clientResponse1 = expectedExecutionReportNew(1, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse2 = expectedExecutionReportNew(2, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse3 = expectedExecutionReportNew(3, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse4 = expectedExecutionReportNew(4, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse5 = expectedExecutionReportNew(5, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.FILLED, newOrderRequest.getVenueAccounts(0));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest);
        orderHistoryService.processClientExecutionReport(clientResponse1);
        orderHistoryService.processClientExecutionReport(clientResponse2);
        orderHistoryService.processClientExecutionReport(clientResponse3);
        orderHistoryService.processClientExecutionReport(clientResponse4);
        orderHistoryService.processClientExecutionReport(clientResponse5);

        // when
        SimplePredicateInput simpleInputOrder = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        List<OrderState> orderStates = orderHistoryService.getOrderStates(new OrderHistorySearchInput(List.of(simpleInputOrder), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection pagedOrderStates = orderHistoryService.getOrderStatesPaged(new OrderHistorySearchInput(List.of(simpleInputOrder), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        List<OrderStatus> orderStatuses = Arrays.asList(OrderStatus.PENDING_NEW, OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED, OrderStatus.PARTIALLY_FILLED, OrderStatus.PARTIALLY_FILLED, OrderStatus.FILLED);
        Collections.reverse(orderStatuses);
        assertThat(orderStates).hasSize(6);
        assertThat(pagedOrderStates.getEdgesList()).hasSize(6);
        assertThat(orderStates).extracting(OrderState::getOrderId).contains(orderId);
        assertThat(pagedOrderStates.getEdgesList()).extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderId).containsOnly(orderId);
        assertThat(orderStates).first().extracting(OrderState::getSequenceNumber).isEqualTo(6);
        assertThat(pagedOrderStates.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getSequenceNumber).isEqualTo(6);
        assertThat(orderStates).first().extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.FILLED);
        assertThat(pagedOrderStates.getEdgesList()).first().extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).isEqualTo(OrderStatus.FILLED);
        assertThat(orderStates).extracting(OrderState::getOrderStatus).containsExactlyElementsOf(orderStatuses);
        assertThat(pagedOrderStates.getEdgesList()).extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState).extracting(OrderState::getOrderStatus).containsExactlyElementsOf(orderStatuses);
    }

    @Test
    void orderStatesWithNostroPortfolio_ShouldReturnAllStatesWithNostroPortfolio() {
        // given
        ClientRequest newOrderRequest = clientNewOrderRequest(ClientOrderType.LIMIT, null);
        String orderId = newOrderRequest.getOrderId();
        when(portfoliosCacheFacade.find(newOrderRequest.getPortfolioId())).thenReturn(
            Optional.of(Portfolio.newBuilder()
                .setPortfolioType(PortfolioType.NOSTRO)
                .setId(newOrderRequest.getPortfolioId())
                .build()));

        ClientResponse clientResponse1 = expectedExecutionReportNew(1, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse2 = expectedExecutionReportNew(2, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse3 = expectedExecutionReportNew(3, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse4 = expectedExecutionReportNew(4, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse5 = expectedExecutionReportNew(5, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.FILLED, newOrderRequest.getVenueAccounts(0));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest);
        orderHistoryService.processClientExecutionReport(clientResponse1);
        orderHistoryService.processClientExecutionReport(clientResponse2);
        orderHistoryService.processClientExecutionReport(clientResponse3);
        orderHistoryService.processClientExecutionReport(clientResponse4);
        orderHistoryService.processClientExecutionReport(clientResponse5);

        // when
        SimplePredicateInput simpleInputOrder = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        SimplePredicateInput simplePortfolioType = new SimplePredicateInput(EQUAL, PORTFOLIO_TYPE, "NOSTRO");
        List<OrderState> orderStates = orderHistoryService.getOrderStates(new OrderHistorySearchInput(List.of(simpleInputOrder, simplePortfolioType), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection pagedOrderStates = orderHistoryService.getOrderStatesPaged(new OrderHistorySearchInput(List.of(simpleInputOrder, simplePortfolioType), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        List<OrderStatus> orderStatuses = Arrays.asList(OrderStatus.PENDING_NEW, OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED, OrderStatus.PARTIALLY_FILLED, OrderStatus.PARTIALLY_FILLED, OrderStatus.FILLED);
        Collections.reverse(orderStatuses);
        assertThat(orderStates).hasSize(6);
        assertThat(pagedOrderStates.getEdgesList()).hasSize(6);
        assertThat(orderStates).extracting(OrderState::getOrderId).contains(orderId);
    }

    @Test
    void orderStatesWithNostroPortfolio_butRequestForVostro_ShouldReturnEmptyResult() {
        // given
        ClientRequest newOrderRequest = clientNewOrderRequest(ClientOrderType.LIMIT, null);
        String orderId = newOrderRequest.getOrderId();
        when(portfoliosCacheFacade.find(newOrderRequest.getPortfolioId())).thenReturn(
            Optional.of(Portfolio.newBuilder()
                .setPortfolioType(PortfolioType.NOSTRO)
                .setId(newOrderRequest.getPortfolioId())
                .build()));

        ClientResponse clientResponse1 = expectedExecutionReportNew(1, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse2 = expectedExecutionReportNew(2, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse3 = expectedExecutionReportNew(3, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse4 = expectedExecutionReportNew(4, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse5 = expectedExecutionReportNew(5, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.FILLED, newOrderRequest.getVenueAccounts(0));
        orderHistoryService.processClientNewOrderRequest(newOrderRequest);
        orderHistoryService.processClientExecutionReport(clientResponse1);
        orderHistoryService.processClientExecutionReport(clientResponse2);
        orderHistoryService.processClientExecutionReport(clientResponse3);
        orderHistoryService.processClientExecutionReport(clientResponse4);
        orderHistoryService.processClientExecutionReport(clientResponse5);

        // when
        SimplePredicateInput simpleInputOrder = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        SimplePredicateInput simplePortfolioType = new SimplePredicateInput(EQUAL, PORTFOLIO_TYPE, "VOSTRO");
        List<OrderState> orderStates = orderHistoryService.getOrderStates(new OrderHistorySearchInput(List.of(simpleInputOrder, simplePortfolioType),
            List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderStates).isEmpty();
    }

    @Test
    void orderStatesWithNostroAndVostroPortfolio_butRequestedForVostro_returnsOnlyVostroAndWithoutType() {
        // given
        ClientRequest newOrderRequestN = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.PORTFOLIO_A, ClientOrderType.LIMIT, ZonedDateTime.now());
        String orderIdNostro = newOrderRequestN.getOrderId();
        String portfolioNostroId = newOrderRequestN.getPortfolioId();
        when(portfoliosCacheFacade.find(portfolioNostroId)).thenReturn(
            Optional.of(Portfolio.newBuilder()
                .setPortfolioType(PortfolioType.NOSTRO)
                .setId(portfolioNostroId)
                .build()));

        ClientRequest newOrderRequestV = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.PORTFOLIO_B, ClientOrderType.LIMIT, ZonedDateTime.now());
        String orderIdVostro = newOrderRequestV.getOrderId();
        String portfolioVostroId = newOrderRequestV.getPortfolioId();
        when(portfoliosCacheFacade.find(portfolioVostroId)).thenReturn(
            Optional.of(Portfolio.newBuilder()
                .setPortfolioType(PortfolioType.VOSTRO)
                .setId(portfolioVostroId)
                .build()));

        ClientRequest newOrderRequest = clientNewOrderRequest(TestingData.INTEGRATION_TEST_INSTRUMENT_ID, TestingData.PORTFOLIO_C, ClientOrderType.LIMIT, ZonedDateTime.now());
        String orderId = newOrderRequest.getOrderId();
        String portfolioId = newOrderRequest.getPortfolioId();
        when(portfoliosCacheFacade.find(portfolioId)).thenReturn(
            Optional.of(Portfolio.newBuilder()
                .setId(portfolioId)
                .build()));


        ClientResponse clientResponseN1 = expectedExecutionReportNew(1, orderIdNostro, newOrderRequestN.getClOrderId(), ClientOrderStatus.NEW, newOrderRequestN.getVenueAccounts(0));
        ClientResponse clientResponseN2 = expectedExecutionReportNew(2, orderIdNostro, newOrderRequestN.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequestN.getVenueAccounts(0));
        ClientResponse clientResponseN3 = expectedExecutionReportNew(3, orderIdNostro, newOrderRequestN.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequestN.getVenueAccounts(0));
        ClientResponse clientResponseN4 = expectedExecutionReportNew(4, orderIdNostro, newOrderRequestN.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequestN.getVenueAccounts(0));
        ClientResponse clientResponseN5 = expectedExecutionReportNew(5, orderIdNostro, newOrderRequestN.getClOrderId(), ClientOrderStatus.FILLED, newOrderRequestN.getVenueAccounts(0));

        ClientResponse clientResponseV1 = expectedExecutionReportNew(1, orderIdVostro, newOrderRequestV.getClOrderId(), ClientOrderStatus.NEW, newOrderRequestV.getVenueAccounts(0));
        ClientResponse clientResponseV2 = expectedExecutionReportNew(2, orderIdVostro, newOrderRequestV.getClOrderId(), ClientOrderStatus.PARTIALLY_FILLED, newOrderRequestV.getVenueAccounts(0));
        ClientResponse clientResponseV3 = expectedExecutionReportNew(3, orderIdVostro, newOrderRequestV.getClOrderId(), ClientOrderStatus.FILLED, newOrderRequestV.getVenueAccounts(0));

        ClientResponse clientResponse1 = expectedExecutionReportNew(1, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.NEW, newOrderRequest.getVenueAccounts(0));
        ClientResponse clientResponse2 = expectedExecutionReportNew(2, orderId, newOrderRequest.getClOrderId(), ClientOrderStatus.FILLED, newOrderRequest.getVenueAccounts(0));


        orderHistoryService.processClientNewOrderRequest(newOrderRequestN);
        orderHistoryService.processClientExecutionReport(clientResponseN1);
        orderHistoryService.processClientExecutionReport(clientResponseN2);
        orderHistoryService.processClientExecutionReport(clientResponseN3);
        orderHistoryService.processClientExecutionReport(clientResponseN4);
        orderHistoryService.processClientExecutionReport(clientResponseN5);

        orderHistoryService.processClientNewOrderRequest(newOrderRequestV);
        orderHistoryService.processClientExecutionReport(clientResponseV1);
        orderHistoryService.processClientExecutionReport(clientResponseV2);
        orderHistoryService.processClientExecutionReport(clientResponseV3);

        orderHistoryService.processClientNewOrderRequest(newOrderRequest);
        orderHistoryService.processClientExecutionReport(clientResponse1);
        orderHistoryService.processClientExecutionReport(clientResponse2);

        // when
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, PORTFOLIO_TYPE, "VOSTRO");
        List<OrderState> orderStates = orderHistoryService.getOrderStates(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        assertThat(orderStates).hasSize(7);
        assertThat(orderStates).extracting(OrderState::getOrderId).contains(orderIdVostro, orderId);
    }

    @Test
    void shouldReturnOrderStateByExtOrderId() throws Exception {
        // given
        String extOrderId1 = "extOrderId1";
        OemsRequest oemsRequest1 = oemsRequest(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        OemsResponse oemsResponse1 = OemsResponse.newBuilder().setOrderId(oemsRequest1.getOrderId()).setExtId(extOrderId1).setOrderQty(oemsRequest1.getQuantity()).setLeavesQty(oemsRequest1.getQuantity()).build();
        OemsRequest oemsRequest2 = oemsRequest(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        OemsResponse oemsResponse2 = OemsResponse.newBuilder().setOrderId(oemsRequest2.getOrderId()).setExtId("extOrderId2").setOrderQty(oemsRequest2.getQuantity()).setLeavesQty(oemsRequest2.getQuantity()).build();
        orderHistoryService.processOemsRequest(oemsRequest1);
        orderHistoryService.processOemsRequest(oemsRequest2);

        // when
        orderHistoryService.processOemsResponse(oemsResponse1);
        orderHistoryService.processOemsResponse(oemsResponse2);

        // then
        DB.logTableContent("order_state");
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, EXT_ORDER_ID, extOrderId1);
        List<OrderState> orderState = orderHistoryService.getOrderStates(new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        assertThat(orderState).isNotEmpty();
        assertThat(orderState).allSatisfy(state -> assertThat(state.getExtOrderId()).isEqualTo(extOrderId1));
    }
}

@ExtendWith(PostgreSQLSetupExtension.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@MockBean(classes = { HazelcastInstance.class })
class PostgreSqlOrderHistoryServiceDbIntegrationTest extends OrderHistoryServiceDbIntegrationTest {
}