package io.wyden.orderhistory.service;

import io.wyden.orderhistory.model.MessageType;
import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientResponseType;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.junit.jupiter.api.Test;
import org.springframework.test.annotation.DirtiesContext;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class TradingMessageConsumerIntegrationTest extends TradingMessageConsumerIntegrationTestBase {

    @Test
    void shouldProcessValidOemsResponseAndSaveEvent() throws Exception {
        // Given
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            latch.countDown();
            return true;
        }).when(mockAsyncOrderStateProcessor).scheduleProcessing(any());

        // Create a test OemsResponse
        OemsResponse response = createTestOemsResponse(UUID.randomUUID().toString());

        // When
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);

        // Then
        boolean processed = latch.await(5, TimeUnit.SECONDS);
        assertThat(processed).isTrue();

        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).isNotEmpty();
            assertThat(events).anyMatch(event -> event.getOrderId().equalsIgnoreCase(response.getOrderId()));
        });

        verify(mockAsyncOrderStateProcessor, times(1)).scheduleProcessing(any());
    }

    @Test
    void shouldProcessValidClientResponseAndSaveEvent() throws Exception {
        // Given
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            latch.countDown();
            return true;
        }).when(mockAsyncOrderStateProcessor).scheduleProcessing(any());

        // Create a test ClientResponse
        ClientResponse response = createTestClientResponse(UUID.randomUUID().toString());

        // When
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);

        // Then
        boolean processed = latch.await(5, TimeUnit.SECONDS);
        assertThat(processed).isTrue();

        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).isNotEmpty();
            assertThat(events).anyMatch(event -> event.getOrderId().equalsIgnoreCase(response.getOrderId()));
        });

        verify(mockAsyncOrderStateProcessor, times(1)).scheduleProcessing(any());
    }

    @Test
    void shouldHandleInvalidMessageGracefully() {
        // When
        OemsResponse responseWithoutRequest = createTestOemsResponse(UUID.randomUUID().toString()).toBuilder().clearRequest().build();
        tradingIngressExchange.publishWithHeaders(responseWithoutRequest, TRADING_HEADERS);

        // Then
        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            // Verify no events were saved
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).noneMatch(event -> event.getOrderId().equalsIgnoreCase(responseWithoutRequest.getOrderId()));
        });

        // No processing should be scheduled
        verify(mockAsyncOrderStateProcessor, times(0)).scheduleProcessing(any());
    }

    @Test
    void shouldNotProcessMessagesNotRequiringProcessing() {
        // Given
        OemsResponse response = createNonProcessableOemsResponse();

        // When
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);

        // Then
        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            // Verify event were not saved
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).noneMatch(event -> event.getOrderId().equalsIgnoreCase(response.getOrderId()));
        });

        // No processing should be scheduled
        verify(mockAsyncOrderStateProcessor, times(0)).scheduleProcessing(any());
    }

    @Test
    void shouldHandleCorruptedMessageGracefully() {
        // Given - Consumer should handle gracefully without crashing
        // (This test verifies the null check in consumeInner method)
        
        // When - This would normally be done by RabbitMQ infrastructure
        // but we're testing the consumer's handling of null/invalid messages
        
        // Then - No events should be saved and no processing scheduled
        verify(mockAsyncOrderStateProcessor, times(0)).scheduleProcessing(any());
    }

    @Test
    void shouldHandleClientResponseWithDifferentHeaders() throws Exception {
        // Given
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            latch.countDown();
            return true;
        }).when(mockAsyncOrderStateProcessor).scheduleProcessing(any());
        
        ClientResponse response = createTestClientResponse(UUID.randomUUID().toString());
        
        // When - Use different header format
        Map<String, String> differentHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(response, differentHeaders);
        
        // Then
        boolean processed = latch.await(5, TimeUnit.SECONDS);
        assertThat(processed).isTrue();
        
        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).isNotEmpty();
            assertThat(events).anyMatch(event -> 
                event.getOrderId().equalsIgnoreCase(response.getOrderId()) && event.getMessageType().equals(MessageType.CLIENT_RESPONSE)
            );
        });
        
        verify(mockAsyncOrderStateProcessor, times(1)).scheduleProcessing(any());
    }

    @Test
    void shouldHandleOemsResponseWithoutTimestamp() throws Exception {
        // Given
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            latch.countDown();
            return true;
        }).when(mockAsyncOrderStateProcessor).scheduleProcessing(any());
        
        // Create OemsResponse without metadata timestamp
        String orderId = UUID.randomUUID().toString();
        OemsRequest request = createTestOemsRequest(orderId);
        OemsResponse responseWithoutTimestamp = OemsResponse.newBuilder()
            .setOrderId(orderId)
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setRequest(request)
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                // No createdAt timestamp
                .build())
            .build();
        
        // When
        tradingIngressExchange.publishWithHeaders(responseWithoutTimestamp, TRADING_HEADERS);
        
        // Then
        boolean processed = latch.await(5, TimeUnit.SECONDS);
        assertThat(processed).isTrue();
        
        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).isNotEmpty();
            assertThat(events).anyMatch(event -> {
                return event.getOrderId().equalsIgnoreCase(orderId) &&
                       event.getMessageTimestamp() != null; // Should use current time as fallback
            });
        });
        
        verify(mockAsyncOrderStateProcessor, times(1)).scheduleProcessing(any());
    }

    @Test
    void shouldHandleClientResponseWithoutTimestamp() throws Exception {
        // Given
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            latch.countDown();
            return true;
        }).when(mockAsyncOrderStateProcessor).scheduleProcessing(any());
        
        // Create ClientResponse without metadata timestamp
        String orderId = UUID.randomUUID().toString();
        ClientRequest request = createTestClientRequest(orderId);
        ClientResponse responseWithoutTimestamp = ClientResponse.newBuilder()
            .setOrderId(orderId)
            .setResponseType(ClientResponseType.EXECUTION_REPORT)
            .setRequest(request)
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                // No createdAt timestamp
                .build())
            .build();
        
        // When
        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(responseWithoutTimestamp, clientHeaders);
        
        // Then
        boolean processed = latch.await(5, TimeUnit.SECONDS);
        assertThat(processed).isTrue();
        
        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).isNotEmpty();
            assertThat(events).anyMatch(event -> {
                return event.getOrderId().equalsIgnoreCase(orderId) &&
                       event.getMessageTimestamp() != null; // Should use current time as fallback
            });
        });
        
        verify(mockAsyncOrderStateProcessor, times(1)).scheduleProcessing(any());
    }

    @Test
    void shouldHandleMultipleMessageTypesSequentially() throws Exception {
        // Given
        CountDownLatch latch = new CountDownLatch(2);
        doAnswer(invocation -> {
            latch.countDown();
            return true;
        }).when(mockAsyncOrderStateProcessor).scheduleProcessing(any());
        
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        
        OemsResponse oemsResponse = createTestOemsResponse(orderId1);
        ClientResponse clientResponse = createTestClientResponse(orderId2);
        
        // When
        tradingIngressExchange.publishWithHeaders(oemsResponse, TRADING_HEADERS);
        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(clientResponse, clientHeaders);
        
        // Then
        boolean processed = latch.await(10, TimeUnit.SECONDS);
        assertThat(processed).isTrue();
        
        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            List<OrderEventEntity> events1 = orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1);
            List<OrderEventEntity> events2 = orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2);
            assertThat(events1).hasSize(1);
            assertThat(events2).hasSize(1);
            assertThat(events1.getFirst().getMessageType()).isEqualTo(MessageType.OEMS_RESPONSE);
            assertThat(events2.getFirst().getMessageType()).isEqualTo(MessageType.CLIENT_RESPONSE);
        });
        
        verify(mockAsyncOrderStateProcessor, times(2)).scheduleProcessing(any());
    }

    private OemsRequest createTestOemsRequest(String orderId) {
        return OemsRequest.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                .build())
            .setOrderId(orderId)
            .build();
    }

    private ClientRequest createTestClientRequest(String orderId) {
        return ClientRequest.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                .build())
            .setOrderId(orderId)
            .build();
    }
}
