package io.wyden.orderhistory.service;

import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.reporting.OrderState;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.springframework.test.annotation.DirtiesContext;

import java.time.ZonedDateTime;
import java.util.Map;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class OrderHistoryServiceEmitterIntegrationTest extends AsyncOrderStateProcessorIntegrationTestBase {

    @Test
    void shouldStoreOrderState() {
        // given
        String orderId = UUID.randomUUID().toString();
        ClientResponse clientResponse = createTestClientResponse(orderId);

        // when
        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(clientResponse, clientHeaders);

        // then
        OrderState orderState = awaitOrderState();
        assertThat(orderState).isNotNull();
        assertThat(orderState.getOrderId()).isEqualTo(orderId);
    }

    @Test
    void shouldProcessClientResponseForNewOrder() {
        // given
        String orderId = UUID.randomUUID().toString();
        ClientResponse clientResponse = createTestClientResponse(orderId);

        // when
        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(clientResponse, clientHeaders);

        // then
        OrderState orderState = awaitOrderState();
        assertThat(orderState).isNotNull();
        assertThat(orderState.getOrderId()).isEqualTo(orderId);
    }

    @Test
    void shouldProcessOemsResponseForRejectedHedgingOrder() {
        // given
        OemsResponse executionReport = getRejectedOemsResponse();

        // when
        tradingIngressExchange.publishWithHeaders(executionReport, TRADING_HEADERS);

        // then
        OrderState orderState = awaitOrderState();
        assertThat(orderState).isNotNull();
        assertThat(orderState.getOrderId()).isEqualTo(executionReport.getOrderId());
    }

    @Test
    void shouldProcessMultipleOemsResponses() {
        // given
        String orderId = UUID.randomUUID().toString();
        OemsResponse firstResponse = createTestOemsResponse(orderId, ZonedDateTime.now());
        OemsResponse secondResponse = createTestOemsResponse(orderId, ZonedDateTime.now().plusSeconds(1));

        // when
        tradingIngressExchange.publishWithHeaders(firstResponse, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(secondResponse, TRADING_HEADERS);

        // then
        OrderState firstOrderState = awaitOrderState();
        OrderState secondOrderState = awaitOrderState();
        
        assertThat(firstOrderState).isNotNull();
        assertThat(secondOrderState).isNotNull();
        assertThat(firstOrderState.getOrderId()).isEqualTo(orderId);
        assertThat(secondOrderState.getOrderId()).isEqualTo(orderId);
    }

    private @NotNull OemsResponse getRejectedOemsResponse() {
        String orderId = UUID.randomUUID().toString();
        return createTestOemsResponse(orderId, ZonedDateTime.now(), OemsOrderStatus.STATUS_REJECTED, 0);
    }
}
