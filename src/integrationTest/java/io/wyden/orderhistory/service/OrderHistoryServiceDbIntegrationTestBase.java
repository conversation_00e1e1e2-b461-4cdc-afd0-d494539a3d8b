package io.wyden.orderhistory.service;

import io.wyden.orderhistory.IntegrationTestBase;
import io.wyden.orderhistory.infrastructure.rabbit.TradingMessageConsumer;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import org.junit.jupiter.api.AfterEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

@SpringBootTest(properties = {"message.consumer.use.v1 = true", "message.consumer.use.v2 = false"})
@MockBean(classes = { TradingMessageConsumer.class })
public abstract class OrderHistoryServiceDbIntegrationTestBase extends IntegrationTestBase {

    @Autowired
    OrderHistoryService orderHistoryService;

    @MockBean
    PortfoliosCacheFacade portfoliosCacheFacade;

    @AfterEach
    void cleanup() {
        orderHistoryService.deleteAll();
    }
}
