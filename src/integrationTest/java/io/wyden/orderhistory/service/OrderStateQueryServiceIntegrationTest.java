package io.wyden.orderhistory.service;

import io.wyden.orderhistory.model.CollectionPredicateInput;
import io.wyden.orderhistory.model.DatePredicateInput;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.SimplePredicateInput;
import io.wyden.orderhistory.model.SortingOrder;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.oems.OemsOrderCategory;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.reporting.OrderState;
import io.wyden.published.reporting.OrderStatus;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.DirtiesContext;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static io.wyden.orderhistory.model.CollectionPredicateInput.Field.ORDER_CATEGORY;
import static io.wyden.orderhistory.model.CollectionPredicateInput.Field.ORDER_ID;
import static io.wyden.orderhistory.model.CollectionPredicateInput.Field.ORDER_STATUS;
import static io.wyden.orderhistory.model.CollectionPredicateInput.Field.PORTFOLIO_ID;
import static io.wyden.orderhistory.model.CollectionPredicateInput.PredicateType.IN;
import static io.wyden.orderhistory.model.DatePredicateInput.Field.UPDATED_AT;
import static io.wyden.orderhistory.model.DatePredicateInput.PredicateType.FROM;
import static io.wyden.orderhistory.model.DatePredicateInput.PredicateType.TO;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.CL_ORDER_ID;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.EXT_ORDER_ID;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.INSTRUMENT_ID;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.PARENT_ORDER_ID;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.PORTFOLIO_TYPE;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.ROOT_ORDER_ID;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.VENUE_ACCOUNT_ID;
import static io.wyden.orderhistory.model.SimplePredicateInput.PredicateType.EQUAL;
import static io.wyden.orderhistory.utils.TestingData.INTEGRATION_TEST_INSTRUMENT_ID;
import static io.wyden.orderhistory.utils.TestingData.INTEGRATION_TEST_INSTRUMENT_ID_2;
import static io.wyden.orderhistory.utils.TestingData.PORTFOLIO_A;
import static io.wyden.orderhistory.utils.TestingData.PORTFOLIO_B;
import static io.wyden.orderhistory.utils.TestingData.PORTFOLIO_C;
import static io.wyden.orderhistory.utils.TestingData.ROOT_A;
import static io.wyden.orderhistory.utils.TestingData.ROOT_B;
import static io.wyden.orderhistory.utils.TestingData.clientNewOrderRequest;
import static io.wyden.orderhistory.utils.TestingData.expectedExecutionReportWithTimestamp;
import static io.wyden.orderhistory.utils.TestingData.expectedOemsExecutionReport;
import static io.wyden.orderhistory.utils.TestingData.oemsRequest;
import static org.assertj.core.api.Assertions.assertThat;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
public class OrderStateQueryServiceIntegrationTest extends AsyncOrderStateProcessorIntegrationTestBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStateQueryServiceIntegrationTest.class);

    @Autowired
    private OrderStateQueryService orderStateQueryService;

    @Test
    void shouldQueryOrderStatesByOrderId() {
        // Given - Process an OemsResponse
        String orderId = UUID.randomUUID().toString();
        LOGGER.info("Testing order state query with orderId: {}", orderId);

        OemsResponse response = createTestOemsResponse(orderId);
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);

        // Wait for processing to complete
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).isNotEmpty());

        // Consume the OrderState from the queue to clear it
        awaitOrderState();

        // When - Query by order ID
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then
        assertThat(orderStates).hasSize(1);
        assertThat(orderStates.get(0).getOrderId()).isEqualTo(orderId);
    }

    @Test
    void shouldQueryOrderStatesByPortfolioId() {
        // Given - Process multiple OemsResponses with different portfolio IDs
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String orderId3 = UUID.randomUUID().toString();

        OemsResponse response1 = createTestOemsResponse(orderId1);
        OemsResponse response2 = createTestOemsResponse(orderId2);
        OemsResponse response3 = createTestOemsResponse(orderId3);

        tradingIngressExchange.publishWithHeaders(response1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response2, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response3, TRADING_HEADERS);

        // Wait for all processing to complete
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId3)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query by portfolio ID (using PORTFOLIO_A from test data)
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, PORTFOLIO_A);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return orders matching the portfolio ID
        assertThat(orderStates).isNotEmpty();
        orderStates.forEach(orderState -> assertThat(orderState.getPortfolioId()).isEqualTo(PORTFOLIO_A));
    }

    @Test
    void shouldQueryOrderStatesByOrderStatus() {
        // Given - Process orders with different statuses
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String orderId3 = UUID.randomUUID().toString();

        ZonedDateTime now = ZonedDateTime.now();
        OemsResponse filledOrder = createTestOemsResponse(orderId1, now, OemsOrderStatus.STATUS_FILLED, 10);
        OemsResponse newOrder = createTestOemsResponse(orderId2, now.plusSeconds(1), OemsOrderStatus.STATUS_NEW, 0);
        OemsResponse canceledOrder = createTestOemsResponse(orderId3, now.plusSeconds(2), OemsOrderStatus.STATUS_CANCELED, 5);

        tradingIngressExchange.publishWithHeaders(filledOrder, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(newOrder, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(canceledOrder, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId3)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // Wait for search index to be updated
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(searchIndexRepository.findByOrderId(orderId1)).isPresent();
            assertThat(searchIndexRepository.findByOrderId(orderId2)).isPresent();
            assertThat(searchIndexRepository.findByOrderId(orderId3)).isPresent();
        });

        // When - Query by order status (FILLED)
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_STATUS, "FILLED");
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return only filled orders
        assertThat(orderStates).isNotEmpty();
        orderStates.forEach(orderState -> assertThat(orderState.getOrderStatus()).isEqualTo(OrderStatus.FILLED));
    }

    @Test
    void shouldQueryOrderStatesWithPagination() {
        // Given - Process multiple orders
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String orderId3 = UUID.randomUUID().toString();
        String orderId4 = UUID.randomUUID().toString();

        ZonedDateTime baseTime = ZonedDateTime.now();
        OemsResponse response1 = createTestOemsResponse(orderId1, baseTime, OemsOrderStatus.STATUS_FILLED, 10);
        OemsResponse response2 = createTestOemsResponse(orderId2, baseTime.plusSeconds(1), OemsOrderStatus.STATUS_FILLED, 10);
        OemsResponse response3 = createTestOemsResponse(orderId3, baseTime.plusSeconds(2), OemsOrderStatus.STATUS_FILLED, 10);
        OemsResponse response4 = createTestOemsResponse(orderId4, baseTime.plusSeconds(3), OemsOrderStatus.STATUS_FILLED, 10);

        tradingIngressExchange.publishWithHeaders(response1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response2, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response3, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response4, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId3)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId4)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query with pagination (first 2 results)
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_STATUS, "FILLED");
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 2, null, SortingOrder.DESC, null);

        CursorConnection result = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput);

        // Then
        assertThat(result.getEdgesList()).hasSize(2);
        assertThat(result.getPageInfo().getHasNextPage()).isTrue();
    }

    @Test
    void shouldQueryAllOrderStatesIncludingHistory() {
        // Given - Process multiple events for the same order
        String orderId = UUID.randomUUID().toString();
        ZonedDateTime baseTime = ZonedDateTime.now();

        OemsResponse newOrder = createTestOemsResponse(orderId, baseTime, OemsOrderStatus.STATUS_NEW, 0);
        OemsResponse partialFill = createTestOemsResponse(orderId, baseTime.plusSeconds(1), OemsOrderStatus.STATUS_PARTIALLY_FILLED, 5);
        OemsResponse fullFill = createTestOemsResponse(orderId, baseTime.plusSeconds(2), OemsOrderStatus.STATUS_FILLED, 10);

        tradingIngressExchange.publishWithHeaders(newOrder, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(partialFill, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(fullFill, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(3));

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query all order states (history)
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 20, null, SortingOrder.ASC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStates(searchInput);

        // Then - Should return all 3 states in chronological order
        assertThat(orderStates).hasSize(3);
        assertThat(orderStates.get(0).getOrderStatus()).isEqualTo(OrderStatus.NEW);
        assertThat(orderStates.get(1).getOrderStatus()).isEqualTo(OrderStatus.PARTIALLY_FILLED);
        assertThat(orderStates.get(2).getOrderStatus()).isEqualTo(OrderStatus.FILLED);
    }

    @Test
    void shouldQueryLatestOrderState() {
        // Given - Process multiple events for the same order
        String orderId = UUID.randomUUID().toString();
        ZonedDateTime baseTime = ZonedDateTime.now();

        OemsResponse newOrder = createTestOemsResponse(orderId, baseTime, OemsOrderStatus.STATUS_NEW, 0);
        OemsResponse partialFill = createTestOemsResponse(orderId, baseTime.plusSeconds(1), OemsOrderStatus.STATUS_PARTIALLY_FILLED, 5);
        OemsResponse fullFill = createTestOemsResponse(orderId, baseTime.plusSeconds(2), OemsOrderStatus.STATUS_FILLED, 10);

        tradingIngressExchange.publishWithHeaders(newOrder, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(partialFill, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(fullFill, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(3));

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query all order states (history)
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 20, null, SortingOrder.ASC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return latest state only
        assertThat(orderStates).hasSize(1);
        assertThat(orderStates.getFirst().getOrderStatus()).isEqualTo(OrderStatus.FILLED);
    }

    @Test
    void shouldQueryClientResponseOrderStates() {
        // Given - Process a ClientResponse
        String orderId = UUID.randomUUID().toString();
        ClientResponse clientResponse = createTestClientResponse(orderId);

        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(clientResponse, clientHeaders);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();

        // When - Query by order ID
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStates(searchInput);

        // Then
        assertThat(orderStates).hasSize(1);
        assertThat(orderStates.getFirst().getOrderId()).isEqualTo(orderId);
        LOGGER.info("Successfully queried ClientResponse-based OrderState: {}", orderStates.getFirst().getOrderId());
    }

    @Test
    void shouldStoreOrderState() {
        // Given
        String orderId = UUID.randomUUID().toString();
        OemsResponse oemsResponse = createTestOemsResponse(orderId);

        tradingIngressExchange.publishWithHeaders(oemsResponse, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).isNotEmpty());

        // Clear the queue
        awaitOrderState();

        // When - Query by order ID
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStates(searchInput);

        // Then
        assertThat(orderStates).hasSize(1);
        assertThat(orderStates.getFirst().getOrderId()).isEqualTo(orderId);
        LOGGER.info("Successfully stored and queried OrderState: {}", orderStates.getFirst().getOrderId());
    }

    @Test
    void shouldUpdateOrderState() {
        // Given
        String orderId = UUID.randomUUID().toString();
        ZonedDateTime baseTime = ZonedDateTime.now();

        OemsResponse newOrder = createTestOemsResponse(orderId, baseTime, OemsOrderStatus.STATUS_NEW, 0);
        OemsResponse partialFill = createTestOemsResponse(orderId, baseTime.plusSeconds(1), OemsOrderStatus.STATUS_PARTIALLY_FILLED, 5);

        tradingIngressExchange.publishWithHeaders(newOrder, TRADING_HEADERS);

        // Wait for first processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(1));

        // Clear the queue
        awaitOrderState();

        // When - Update the order
        tradingIngressExchange.publishWithHeaders(partialFill, TRADING_HEADERS);

        // Wait for second processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(2));

        // Clear the queue
        awaitOrderState();

        // Then - Query and verify updated state
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then
        assertThat(orderStates).hasSize(1);
        assertThat(orderStates.getFirst().getOrderId()).isEqualTo(orderId);
        assertThat(orderStates.getFirst().getOrderStatus()).isEqualTo(OrderStatus.PARTIALLY_FILLED);
        LOGGER.info("Successfully updated and queried OrderState: {}", orderStates.getFirst().getOrderId());
    }

    @Test
    void testQueryBySymbol() {
        // Given
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        OemsResponse oemsResponse1 = createTestOemsResponse(orderId1, INTEGRATION_TEST_INSTRUMENT_ID);
        OemsResponse oemsResponse2 = createTestOemsResponse(orderId2, INTEGRATION_TEST_INSTRUMENT_ID_2);

        tradingIngressExchange.publishWithHeaders(oemsResponse1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(oemsResponse2, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();

        // When - Query by specific instrument ID
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, INSTRUMENT_ID, INTEGRATION_TEST_INSTRUMENT_ID);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then
        assertThat(orderStates).hasSize(1);
        assertThat(orderStates.getFirst().getInstrumentId()).isEqualTo(INTEGRATION_TEST_INSTRUMENT_ID);
        assertThat(orderStates.getFirst().getOrderId()).isEqualTo(orderId1);
    }

    @Test
    void testLimit() {
        // Given - Create multiple orders
        ZonedDateTime baseTime = ZonedDateTime.now();
        OemsResponse response1 = createTestOemsResponse(UUID.randomUUID().toString(), baseTime);
        OemsResponse response2 = createTestOemsResponse(UUID.randomUUID().toString(), baseTime.plusSeconds(1));
        OemsResponse response3 = createTestOemsResponse(UUID.randomUUID().toString(), baseTime.plusSeconds(2), OemsOrderStatus.STATUS_NEW, 0);
        OemsResponse response4 = createTestOemsResponse(response3.getOrderId(), baseTime.plusSeconds(3), OemsOrderStatus.STATUS_FILLED, 10);

        tradingIngressExchange.publishWithHeaders(response1, TRADING_HEADERS);
        awaitOrderState();

        tradingIngressExchange.publishWithHeaders(response2, TRADING_HEADERS);
        awaitOrderState();

        tradingIngressExchange.publishWithHeaders(response3, TRADING_HEADERS);
        awaitOrderState();

        tradingIngressExchange.publishWithHeaders(response4, TRADING_HEADERS);
        awaitOrderState();

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(response1.getOrderId())).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(response2.getOrderId())).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(response3.getOrderId())).isNotEmpty();
        });

        // When - Query with limit
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(), List.of(), 2, null, SortingOrder.DESC, null);
        OrderHistorySearchInput searchInput2 = new OrderHistorySearchInput(List.of(), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> snapshots = orderStateQueryService.getOrderStateSnapshots(searchInput);
        CursorConnection snapshotsPaged = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput2);

        // Then
        assertThat(snapshots).hasSize(2);
        assertThat(snapshotsPaged.getEdgesList()).hasSize(3);
        // Most recent order should be first (DESC order)
        assertThat(snapshots.getFirst().getOrderId()).isEqualTo(response3.getOrderId());
    }

    @Test
    void testQueryByClOrderId() {
        // Given
        ClientResponse clientResponse1 = createTestClientResponse();
        ClientResponse clientResponse2 = createTestClientResponse();

        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(clientResponse1, clientHeaders);
        tradingIngressExchange.publishWithHeaders(clientResponse2, clientHeaders);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse1.getOrderId())).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse2.getOrderId())).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();

        // When - Query by specific client order ID
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, CL_ORDER_ID, clientResponse2.getClOrderId());
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);
        CursorConnection orderStatesPaged = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput);

        // Then
        assertThat(orderStates).hasSize(1);
        assertThat(orderStatesPaged.getEdgesList()).hasSize(1);
        assertThat(orderStates.getFirst().getClOrderId()).isEqualTo(clientResponse2.getClOrderId());
        assertThat(orderStates.getFirst().getOrderId()).isEqualTo(clientResponse2.getOrderId());
    }

    @Test
    void testQueryByRootOrderId() {
        // Given - Create orders with different root order IDs
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String orderId3 = UUID.randomUUID().toString();

        ZonedDateTime now = ZonedDateTime.now();
        OemsRequest oemsRequest1 = oemsRequest(orderId1, null, ROOT_A);
        OemsRequest oemsRequest2 = oemsRequest(orderId2, null, ROOT_A);
        OemsRequest oemsRequest3 = oemsRequest(orderId3, null, ROOT_B); // Different root - should not return

        OemsResponse response1 = expectedOemsExecutionReport(oemsRequest1, OemsOrderStatus.STATUS_FILLED, 10, now);
        OemsResponse response2 = expectedOemsExecutionReport(oemsRequest2, OemsOrderStatus.STATUS_FILLED, 10, now.plusSeconds(1));
        OemsResponse response3 = expectedOemsExecutionReport(oemsRequest3, OemsOrderStatus.STATUS_FILLED, 10, now.plusSeconds(2));

        tradingIngressExchange.publishWithHeaders(response1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response2, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response3, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId3)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query by root order ID
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, ROOT_ORDER_ID, ROOT_A);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);
        CursorConnection orderStatesPaged = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput);

        // Then - Should return only orders with ROOT_A
        assertThat(orderStates).hasSize(2);
        assertThat(orderStatesPaged.getEdgesList()).hasSize(2);
        orderStates.forEach(orderState -> assertThat(orderState.getRootOrderId()).isEqualTo(ROOT_A));
    }

    @Test
    void testQueryByMinDate() {
        // given
        ClientRequest newOrderRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now().minusMinutes(10));
        ClientResponse clientResponse1 = expectedExecutionReportWithTimestamp(1, newOrderRequest1, ClientOrderStatus.NEW);
        tradingIngressExchange.publishWithHeaders(clientResponse1, TRADING_HEADERS);

        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse1.getOrderId())).isNotEmpty());
        OrderState orderState1 = awaitOrderState();

        ClientRequest newOrderRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now().minusMinutes(5));
        ClientResponse clientResponse2 = expectedExecutionReportWithTimestamp(1, newOrderRequest2, ClientOrderStatus.NEW);
        tradingIngressExchange.publishWithHeaders(clientResponse2, TRADING_HEADERS);

        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse2.getOrderId())).isNotEmpty());
        awaitOrderState();

        ClientRequest newOrderRequest3 = clientNewOrderRequest(ClientOrderType.LIMIT, ZonedDateTime.now());
        ClientResponse clientResponse3 = expectedExecutionReportWithTimestamp(1, newOrderRequest3, ClientOrderStatus.NEW);
        tradingIngressExchange.publishWithHeaders(clientResponse3, TRADING_HEADERS);

        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse3.getOrderId())).isNotEmpty());
        awaitOrderState();

        // when
        ZonedDateTime expectedDateTime = ZonedDateTime.now().minusMinutes(7);
        String expectedDateTimeOffset = expectedDateTime.toOffsetDateTime().format(DateTimeFormatter.ISO_DATE_TIME);

        DatePredicateInput datePredicateInput = new DatePredicateInput(FROM, DatePredicateInput.Field.CREATED_AT, expectedDateTimeOffset);
        List<OrderState> orderHistory = orderStateQueryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(datePredicateInput), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderStateQueryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(), List.of(datePredicateInput), null, null, SortingOrder.DESC, null));

        // then
        ZoneId zoneId = ZoneId.of("Europe/Paris");

        assertThat(orderHistory)
            .isNotEmpty()
            .hasSize(2)
            .allMatch(orderState -> OffsetDateTime.parse(orderState.getCreatedAt()).atZoneSameInstant(zoneId).isAfter(expectedDateTime))
            .allMatch(orderState -> List.of(newOrderRequest2.getOrderId(), newOrderRequest3.getOrderId()).contains(orderState.getOrderId()));

        assertThat(orderHistoryPaged.getEdgesList())
            .isNotEmpty()
            .hasSize(2)
            .extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState)
            .allMatch(orderState -> OffsetDateTime.parse(orderState.getCreatedAt()).atZoneSameInstant(zoneId).isAfter(expectedDateTime))
            .allMatch(orderState -> List.of(newOrderRequest2.getOrderId(), newOrderRequest3.getOrderId()).contains(orderState.getOrderId()));
    }

    @Test
    void testQueryByMaxDate() {
        // given
        ClientRequest newOrderRequest1 = clientNewOrderRequest(ClientOrderType.LIMIT, OffsetDateTime.now().minusMinutes(10).toZonedDateTime());
        ClientResponse clientResponse1 = expectedExecutionReportWithTimestamp(1, newOrderRequest1, ClientOrderStatus.NEW);
        tradingIngressExchange.publishWithHeaders(clientResponse1, TRADING_HEADERS);

        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse1.getOrderId())).isNotEmpty());
        awaitOrderState();

        ClientRequest newOrderRequest2 = clientNewOrderRequest(ClientOrderType.LIMIT, OffsetDateTime.now().minusMinutes(5).toZonedDateTime());
        ClientResponse clientResponse2 = expectedExecutionReportWithTimestamp(1, newOrderRequest2, ClientOrderStatus.NEW);
        tradingIngressExchange.publishWithHeaders(clientResponse2, TRADING_HEADERS);

        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse2.getOrderId())).isNotEmpty());
        awaitOrderState();

        ClientRequest newOrderRequest3 = clientNewOrderRequest(ClientOrderType.LIMIT, OffsetDateTime.now().toZonedDateTime());
        ClientResponse clientResponse3 = expectedExecutionReportWithTimestamp(1, newOrderRequest3, ClientOrderStatus.NEW);
        tradingIngressExchange.publishWithHeaders(clientResponse3, TRADING_HEADERS);

        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse3.getOrderId())).isNotEmpty());
        awaitOrderState();

        // when
        ZonedDateTime expectedDateTime = ZonedDateTime.now().minusMinutes(7);
        String expectedDateTimeOffset = expectedDateTime.toOffsetDateTime().format(DateTimeFormatter.ISO_DATE_TIME);

        DatePredicateInput datePredicateInput = new DatePredicateInput(TO, UPDATED_AT, expectedDateTimeOffset);
        List<OrderState> orderHistory = orderStateQueryService.getOrderStateSnapshots(new OrderHistorySearchInput(List.of(), List.of(), List.of(datePredicateInput), null, null, SortingOrder.DESC, null));
        CursorConnection orderHistoryPaged = orderStateQueryService.getOrderStateSnapshotsPaged(new OrderHistorySearchInput(List.of(), List.of(), List.of(datePredicateInput), null, null, SortingOrder.DESC, null));

        // then
        ZoneId zoneId = ZoneId.of("Europe/Paris");

        assertThat(orderHistory)
            .isNotEmpty()
            .hasSize(1)
            .last()
            .matches(orderState -> OffsetDateTime.parse(orderState.getCreatedAt()).atZoneSameInstant(zoneId).isBefore(expectedDateTime))
            .matches(orderState -> orderState.getOrderId().equals(newOrderRequest1.getOrderId()));

        assertThat(orderHistoryPaged.getEdgesList())
            .isNotEmpty()
            .hasSize(1)
            .last()
            .extracting(CursorEdge::getNode).extracting(CursorNode::getOrderState)
            .matches(orderState -> OffsetDateTime.parse(orderState.getCreatedAt()).atZoneSameInstant(zoneId).isBefore(expectedDateTime))
            .matches(orderState -> orderState.getOrderId().equals(newOrderRequest1.getOrderId()));
    }

    @Test
    void collectionPredicate_shouldReturnByOrderCategory() {
        // Given - Create orders with different categories
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String orderId3 = UUID.randomUUID().toString();

        // Create OemsRequests with different categories
        ZonedDateTime now = ZonedDateTime.now();
        OemsRequest request1 = oemsRequest(orderId1, null).toBuilder().setOrderCategory(OemsOrderCategory.SOR_ORDER).build();
        OemsRequest request2 = oemsRequest(orderId2, null).toBuilder().setOrderCategory(OemsOrderCategory.DIRECT_MARKET_ACCESS_ORDER).build();
        OemsRequest request3 = oemsRequest(orderId3, null).toBuilder().setOrderCategory(OemsOrderCategory.SOR_ORDER).build();

        OemsResponse response1 = expectedOemsExecutionReport(request1, OemsOrderStatus.STATUS_FILLED, 10, now);
        OemsResponse response2 = expectedOemsExecutionReport(request2, OemsOrderStatus.STATUS_FILLED, 10, now.plusSeconds(1));
        OemsResponse response3 = expectedOemsExecutionReport(request3, OemsOrderStatus.STATUS_FILLED, 10, now.plusSeconds(2));

        tradingIngressExchange.publishWithHeaders(response1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response2, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response3, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId3)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query by order category and specific order IDs using collection predicate
        CollectionPredicateInput input1 = new CollectionPredicateInput(IN, ORDER_CATEGORY, List.of("SOR_ORDER"));
        CollectionPredicateInput input2 = new CollectionPredicateInput(IN, ORDER_ID, List.of(orderId1, orderId3));
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(input1, input2), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return only SOR_ORDER orders from this test
        assertThat(orderStates).hasSize(2);
        orderStates.forEach(orderState -> assertThat(orderState.getOrderCategory().name()).isEqualTo("SOR_ORDER"));
    }

    @Test
    void collectionPredicate_shouldReturnByPortfolioId() {
        // Given - Create orders with different portfolio IDs
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String orderId3 = UUID.randomUUID().toString();

        ZonedDateTime now = ZonedDateTime.now();
        
        // Create OemsRequests with different portfolio IDs
        OemsRequest request1 = oemsRequest(orderId1, null).toBuilder().setPortfolioId(PORTFOLIO_A).build();
        OemsRequest request2 = oemsRequest(orderId2, null).toBuilder().setPortfolioId(PORTFOLIO_B).build();
        OemsRequest request3 = oemsRequest(orderId3, null).toBuilder().setPortfolioId(PORTFOLIO_C).build();

        // Create OemsResponses with matching portfolio IDs
        OemsResponse response1 = expectedOemsExecutionReport(request1, OemsOrderStatus.STATUS_FILLED, 10, now)
            .toBuilder().setPortfolioId(PORTFOLIO_A).build();
        OemsResponse response2 = expectedOemsExecutionReport(request2, OemsOrderStatus.STATUS_FILLED, 10, now.plusSeconds(1))
            .toBuilder().setPortfolioId(PORTFOLIO_B).build();
        OemsResponse response3 = expectedOemsExecutionReport(request3, OemsOrderStatus.STATUS_FILLED, 10, now.plusSeconds(2))
            .toBuilder().setPortfolioId(PORTFOLIO_C).build();

        tradingIngressExchange.publishWithHeaders(response1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response2, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response3, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId3)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query by multiple portfolio IDs using collection predicate
        CollectionPredicateInput collectionPredicateInput = new CollectionPredicateInput(IN, PORTFOLIO_ID, List.of(PORTFOLIO_A, PORTFOLIO_B));
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return orders for Portfolio A and B, but not C
        assertThat(orderStates).hasSize(2);
        orderStates.forEach(orderState -> assertThat(List.of(PORTFOLIO_A, PORTFOLIO_B)).contains(orderState.getPortfolioId()));
    }

    @Test
    void testQueryByIsClosed() {
        // Given - Create orders with different statuses
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String orderId3 = UUID.randomUUID().toString();

        ZonedDateTime now = ZonedDateTime.now();
        OemsResponse filledOrder = createTestOemsResponse(orderId1, now, OemsOrderStatus.STATUS_FILLED, 10);
        OemsResponse newOrder = createTestOemsResponse(orderId2, now.plusSeconds(1), OemsOrderStatus.STATUS_NEW, 0);
        OemsResponse canceledOrder = createTestOemsResponse(orderId3, now.plusSeconds(2), OemsOrderStatus.STATUS_CANCELED, 5);

        tradingIngressExchange.publishWithHeaders(filledOrder, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(newOrder, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(canceledOrder, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId3)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query for closed orders using collection predicate
        CollectionPredicateInput collectionPredicateInput = new CollectionPredicateInput(IN, ORDER_STATUS, List.of("FILLED", "CANCELED"));
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return only closed orders (FILLED and CANCELED)
        assertThat(orderStates).hasSize(2);
        orderStates.forEach(orderState -> assertThat(List.of(OrderStatus.FILLED, OrderStatus.CANCELED)).contains(orderState.getOrderStatus()));
    }

    @Test
    void testQueryByIsOpen() {
        // Given - Create orders with different statuses
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String orderId3 = UUID.randomUUID().toString();

        ZonedDateTime now = ZonedDateTime.now();
        OemsResponse filledOrder = createTestOemsResponse(orderId1, now, OemsOrderStatus.STATUS_FILLED, 10);
        OemsResponse newOrder = createTestOemsResponse(orderId2, now.plusSeconds(1), OemsOrderStatus.STATUS_NEW, 0);
        OemsResponse partialOrder = createTestOemsResponse(orderId3, now.plusSeconds(2), OemsOrderStatus.STATUS_PARTIALLY_FILLED, 5);

        tradingIngressExchange.publishWithHeaders(filledOrder, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(newOrder, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(partialOrder, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId3)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query for open orders using collection predicate
        CollectionPredicateInput collectionPredicateInput = new CollectionPredicateInput(IN, ORDER_STATUS, List.of("NEW", "PARTIALLY_FILLED"));
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return only open orders (NEW and PARTIALLY_FILLED)
        assertThat(orderStates).hasSize(2);
        orderStates.forEach(orderState -> assertThat(List.of(OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED)).contains(orderState.getOrderStatus()));
    }

    @Test
    void orderStatesWithNostroPortfolio_ShouldReturnAllStatesWithNostroPortfolio() {
        // given
        ClientRequest newOrderRequest = clientNewOrderRequest(ClientOrderType.LIMIT, null);
        String orderId = newOrderRequest.getOrderId();
        ZonedDateTime baseTime = ZonedDateTime.now();

        // Setup portfolio with NOSTRO type in Hazelcast cache
        Portfolio nostroPortfolio = Portfolio.newBuilder()
            .setId(newOrderRequest.getPortfolioId())
            .setPortfolioType(PortfolioType.NOSTRO)
            .setName("Test NOSTRO Portfolio")
            .build();
        portfoliosMap.put(newOrderRequest.getPortfolioId(), nostroPortfolio);

        ClientResponse clientResponse1 = createTestClientResponse(orderId, baseTime, ClientOrderStatus.NEW);
        ClientResponse clientResponse2 = createTestClientResponse(orderId, baseTime.plusSeconds(1), ClientOrderStatus.PARTIALLY_FILLED);
        ClientResponse clientResponse3 = createTestClientResponse(orderId, baseTime.plusSeconds(2), ClientOrderStatus.PARTIALLY_FILLED);
        ClientResponse clientResponse4 = createTestClientResponse(orderId, baseTime.plusSeconds(3), ClientOrderStatus.PARTIALLY_FILLED);
        ClientResponse clientResponse5 = createTestClientResponse(orderId, baseTime.plusSeconds(4), ClientOrderStatus.FILLED);

        tradingIngressExchange.publishWithHeaders(clientResponse1, TRADING_HEADERS);
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() ->
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(1)
        );
        awaitOrderState();

        tradingIngressExchange.publishWithHeaders(clientResponse2, TRADING_HEADERS);
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() ->
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(2)
        );
        awaitOrderState();

        tradingIngressExchange.publishWithHeaders(clientResponse3, TRADING_HEADERS);
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() ->
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(3)
        );
        awaitOrderState();

        tradingIngressExchange.publishWithHeaders(clientResponse4, TRADING_HEADERS);
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() ->
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(4)
        );
        awaitOrderState();

        tradingIngressExchange.publishWithHeaders(clientResponse5, TRADING_HEADERS);
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() ->
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(5)
        );
        awaitOrderState();

        // when
        SimplePredicateInput simpleInputOrder = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        SimplePredicateInput simplePortfolioType = new SimplePredicateInput(EQUAL, PORTFOLIO_TYPE, "NOSTRO");
        List<OrderState> orderStates = orderStateQueryService.getOrderStates(new OrderHistorySearchInput(List.of(simpleInputOrder, simplePortfolioType), List.of(), List.of(), null, null, SortingOrder.DESC, null));
        CursorConnection pagedOrderStates = orderStateQueryService.getOrderStatesPaged(new OrderHistorySearchInput(List.of(simpleInputOrder, simplePortfolioType), List.of(), List.of(), null, null, SortingOrder.DESC, null));

        // then
        List<OrderStatus> orderStatuses = Arrays.asList(OrderStatus.PENDING_NEW, OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED, OrderStatus.PARTIALLY_FILLED, OrderStatus.PARTIALLY_FILLED, OrderStatus.FILLED);
        Collections.reverse(orderStatuses);
        assertThat(orderStates).hasSize(5);
        assertThat(pagedOrderStates.getEdgesList()).hasSize(5);
        assertThat(orderStates).extracting(OrderState::getOrderId).contains(orderId);
    }

    @Test
    void shouldReturnEmptyList() {
        // Given - No orders in the system (clean state)
        // When - Query for any orders
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return empty list
        assertThat(orderStates).isEmpty();
    }

    @Test
    void testQueryByExtOrderId() {
        // Given - Create orders with different external order IDs
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String extOrderId1 = "EXT-" + UUID.randomUUID();
        String extOrderId2 = "EXT-" + UUID.randomUUID();

        ZonedDateTime now = ZonedDateTime.now();
        OemsRequest request1 = oemsRequest(orderId1, null);
        OemsRequest request2 = oemsRequest(orderId2, null);

        OemsResponse response1 = expectedOemsExecutionReport(request1, OemsOrderStatus.STATUS_FILLED, 10, now)
            .toBuilder().setExtId(extOrderId1).build();
        OemsResponse response2 = expectedOemsExecutionReport(request2, OemsOrderStatus.STATUS_FILLED, 10, now.plusSeconds(1))
            .toBuilder().setExtId(extOrderId2).build();

        tradingIngressExchange.publishWithHeaders(response1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response2, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();

        // When - Query by external order ID
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, EXT_ORDER_ID, extOrderId1);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return only orders with the specified external order ID
        assertThat(orderStates).hasSize(1);
        assertThat(orderStates.getFirst().getExtOrderId()).isEqualTo(extOrderId1);
        assertThat(orderStates.getFirst().getOrderId()).isEqualTo(orderId1);
    }

    @Test
    void testQueryByParentOrderId() {
        // Given - Create parent and child orders
        String parentOrderId = UUID.randomUUID().toString();
        String childOrderId1 = UUID.randomUUID().toString();
        String childOrderId2 = UUID.randomUUID().toString();
        String orphanOrderId = UUID.randomUUID().toString();

        ZonedDateTime now = ZonedDateTime.now();
        
        // Create parent order
        OemsRequest parentRequest = oemsRequest(parentOrderId, null);
        OemsResponse parentResponse = expectedOemsExecutionReport(parentRequest, OemsOrderStatus.STATUS_NEW, 0, now);

        // Create child orders with parent order ID
        OemsRequest childRequest1 = oemsRequest(childOrderId1, parentOrderId);
        OemsRequest childRequest2 = oemsRequest(childOrderId2, parentOrderId);
        OemsRequest orphanRequest = oemsRequest(orphanOrderId, null); // No parent

        OemsResponse childResponse1 = expectedOemsExecutionReport(childRequest1, OemsOrderStatus.STATUS_FILLED, 5, now.plusSeconds(1));
        OemsResponse childResponse2 = expectedOemsExecutionReport(childRequest2, OemsOrderStatus.STATUS_FILLED, 5, now.plusSeconds(2));
        OemsResponse orphanResponse = expectedOemsExecutionReport(orphanRequest, OemsOrderStatus.STATUS_FILLED, 10, now.plusSeconds(3));

        tradingIngressExchange.publishWithHeaders(parentResponse, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(childResponse1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(childResponse2, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(orphanResponse, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(parentOrderId)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(childOrderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(childOrderId2)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orphanOrderId)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query by parent order ID
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, PARENT_ORDER_ID, parentOrderId);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return only child orders
        assertThat(orderStates).hasSize(2);
        orderStates.forEach(orderState -> assertThat(orderState.getParentOrderId()).isEqualTo(parentOrderId));
        assertThat(orderStates).extracting(OrderState::getOrderId).containsExactlyInAnyOrder(childOrderId1, childOrderId2);
    }

    @Test
    void testQueryByVenueAccountId() {
        // Given - Create orders using standard ClientResponses that already have venue accounts
        // The test helpers already create orders with venue accounts from TestingData.VENUE_ACCOUNT
        ClientResponse clientResponse1 = createTestClientResponse();
        ClientResponse clientResponse2 = createTestClientResponse();
        ClientResponse clientResponse3 = createTestClientResponse();

        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(clientResponse1, clientHeaders);
        tradingIngressExchange.publishWithHeaders(clientResponse2, clientHeaders);
        tradingIngressExchange.publishWithHeaders(clientResponse3, clientHeaders);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse1.getOrderId())).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse2.getOrderId())).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse3.getOrderId())).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query by venue account ID using the default test venue account
        String defaultVenueAccount = clientResponse1.getVenueAccount(); // Get the venue account from first response
        SimplePredicateInput simplePredicateInput = new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, defaultVenueAccount);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simplePredicateInput), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return all orders with the default venue account
        assertThat(orderStates).hasSize(3);
        orderStates.forEach(orderState -> assertThat(orderState.getVenueAccountsList()).contains(defaultVenueAccount));
        assertThat(orderStates).extracting(OrderState::getOrderId)
            .containsExactlyInAnyOrder(clientResponse1.getOrderId(), clientResponse2.getOrderId(), clientResponse3.getOrderId());
    }

    @Test
    void collectionPredicate_shouldHandleProblematicInputs() {
        // Given - Create normal orders
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();

        OemsResponse response1 = createTestOemsResponse(orderId1);
        OemsResponse response2 = createTestOemsResponse(orderId2);

        tradingIngressExchange.publishWithHeaders(response1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response2, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();

        // When - Query with problematic input values (potential SQL injection attempts)
        List<String> problematicInputs = List.of("\\", "[;;'.", "' OR '1'='1", "valid-portfolio-id");
        CollectionPredicateInput collectionPredicateInput = new CollectionPredicateInput(IN, PORTFOLIO_ID, problematicInputs);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(collectionPredicateInput), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return only legitimate results (no SQL injection should occur)
        // Only orders with PORTFOLIO_A (from test data) should be returned since it's the only valid portfolio ID in our test data
        orderStates.forEach(orderState -> assertThat(orderState.getPortfolioId()).isEqualTo(PORTFOLIO_A));
    }

    @Test
    void testMultiplePortfolioIdPredicates() {
        // Given - Create orders with different portfolio IDs
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String orderId3 = UUID.randomUUID().toString();

        ZonedDateTime now = ZonedDateTime.now();
        OemsRequest request1 = oemsRequest(orderId1, null).toBuilder().setPortfolioId(PORTFOLIO_A).build();
        OemsRequest request2 = oemsRequest(orderId2, null).toBuilder().setPortfolioId(PORTFOLIO_B).build();
        OemsRequest request3 = oemsRequest(orderId3, null).toBuilder().setPortfolioId(PORTFOLIO_C).build();

        OemsResponse response1 = expectedOemsExecutionReport(request1, OemsOrderStatus.STATUS_FILLED, 10, now)
            .toBuilder().setPortfolioId(PORTFOLIO_A).build();
        OemsResponse response2 = expectedOemsExecutionReport(request2, OemsOrderStatus.STATUS_FILLED, 10, now.plusSeconds(1))
            .toBuilder().setPortfolioId(PORTFOLIO_B).build();
        OemsResponse response3 = expectedOemsExecutionReport(request3, OemsOrderStatus.STATUS_FILLED, 10, now.plusSeconds(2))
            .toBuilder().setPortfolioId(PORTFOLIO_C).build();

        tradingIngressExchange.publishWithHeaders(response1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response2, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response3, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId3)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query with multiple portfolio ID predicates using collection predicate (OR logic)
        CollectionPredicateInput collectionPredicate = new CollectionPredicateInput(IN, PORTFOLIO_ID, List.of(PORTFOLIO_A, PORTFOLIO_B));
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(collectionPredicate), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return orders from both PORTFOLIO_A and PORTFOLIO_B
        assertThat(orderStates).hasSize(2);
        orderStates.forEach(orderState -> assertThat(List.of(PORTFOLIO_A, PORTFOLIO_B)).contains(orderState.getPortfolioId()));
    }

    @Test
    void orderStatesWithVostroPortfolio_butRequestForNostro_ShouldReturnEmptyResult() {
        // Given - Create order with VOSTRO portfolio type
        ClientRequest newOrderRequest = clientNewOrderRequest(ClientOrderType.LIMIT, null);
        String orderId = newOrderRequest.getOrderId();

        // Setup portfolio with VOSTRO type in Hazelcast cache
        Portfolio vostroPortfolio = Portfolio.newBuilder()
            .setId(newOrderRequest.getPortfolioId())
            .setPortfolioType(PortfolioType.VOSTRO)
            .setName("Test VOSTRO Portfolio")
            .build();
        portfoliosMap.put(newOrderRequest.getPortfolioId(), vostroPortfolio);

        ClientResponse clientResponse = createTestClientResponse(orderId, ZonedDateTime.now(), ClientOrderStatus.NEW);
        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(clientResponse, clientHeaders);

        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() ->
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(1)
        );
        awaitOrderState();

        // When - Query for NOSTRO portfolio type (should not find VOSTRO orders)
        SimplePredicateInput simpleInputOrder = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        SimplePredicateInput simplePortfolioType = new SimplePredicateInput(EQUAL, PORTFOLIO_TYPE, "NOSTRO");
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(simpleInputOrder, simplePortfolioType), List.of(), List.of(), null, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return empty result
        assertThat(orderStates).isEmpty();
    }

    @Test
    void singlePredicate_shouldReturnNoDataByVenueAccountIdTwoInputs() {
        // Given - Create orders with different venue accounts
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String orderId3 = UUID.randomUUID().toString();
        String venueAccount1 = "VENUE_A";
        String venueAccount2 = "VENUE_B";
        String venueAccount3 = "VENUE_C";

        // Create ClientRequests with different venue accounts
        ClientRequest request1 = clientNewOrderRequest(orderId1, INTEGRATION_TEST_INSTRUMENT_ID, venueAccount1, PORTFOLIO_A, ClientOrderType.LIMIT, ZonedDateTime.now());
        ClientRequest request2 = clientNewOrderRequest(orderId2, INTEGRATION_TEST_INSTRUMENT_ID, venueAccount2, PORTFOLIO_A, ClientOrderType.LIMIT, ZonedDateTime.now());
        ClientRequest request3 = clientNewOrderRequest(orderId3, INTEGRATION_TEST_INSTRUMENT_ID, venueAccount3, PORTFOLIO_A, ClientOrderType.LIMIT, ZonedDateTime.now());

        // Create ClientResponses based on the requests
        ClientResponse clientResponse1 = expectedExecutionReportWithTimestamp(1, request1, ClientOrderStatus.NEW);
        ClientResponse clientResponse2 = expectedExecutionReportWithTimestamp(1, request2, ClientOrderStatus.NEW);
        ClientResponse clientResponse3 = expectedExecutionReportWithTimestamp(1, request3, ClientOrderStatus.NEW);

        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(clientResponse1, clientHeaders);
        tradingIngressExchange.publishWithHeaders(clientResponse2, clientHeaders);
        tradingIngressExchange.publishWithHeaders(clientResponse3, clientHeaders);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse1.getOrderId())).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse2.getOrderId())).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse3.getOrderId())).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query with two SimplePredicateInput for venue accounts (AND logic - both conditions must match, but venue account is array field so this should find orders that have either venue)
        SimplePredicateInput predicate1 = new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, venueAccount1);
        SimplePredicateInput predicate2 = new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, venueAccount2);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(predicate1, predicate2), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);
        CursorConnection orderStatesPaged = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput);

        // Then - Should return orders with either of the specified venue accounts
        assertThat(orderStates).isEmpty();
        assertThat(orderStatesPaged.getEdgesList()).isEmpty();
    }

    @Test
    void collectionPredicate_shouldReturnAllDataByVenueAccountId() {
        // Given - Create orders with different venue accounts
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();
        String orderId3 = UUID.randomUUID().toString();
        String venueAccount1 = "VENUE_A";
        String venueAccount2 = "VENUE_B";
        String venueAccount3 = "VENUE_C";

        // Create ClientRequests with different venue accounts
        ClientRequest request1 = clientNewOrderRequest(orderId1, INTEGRATION_TEST_INSTRUMENT_ID, venueAccount1, PORTFOLIO_A, ClientOrderType.LIMIT, ZonedDateTime.now());
        ClientRequest request2 = clientNewOrderRequest(orderId2, INTEGRATION_TEST_INSTRUMENT_ID, venueAccount2, PORTFOLIO_A, ClientOrderType.LIMIT, ZonedDateTime.now());
        ClientRequest request3 = clientNewOrderRequest(orderId3, INTEGRATION_TEST_INSTRUMENT_ID, venueAccount3, PORTFOLIO_A, ClientOrderType.LIMIT, ZonedDateTime.now());

        // Create ClientResponses based on the requests
        ClientResponse clientResponse1 = expectedExecutionReportWithTimestamp(1, request1, ClientOrderStatus.NEW);
        ClientResponse clientResponse2 = expectedExecutionReportWithTimestamp(1, request2, ClientOrderStatus.NEW);
        ClientResponse clientResponse3 = expectedExecutionReportWithTimestamp(1, request3, ClientOrderStatus.NEW);

        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(clientResponse1, clientHeaders);
        tradingIngressExchange.publishWithHeaders(clientResponse2, clientHeaders);
        tradingIngressExchange.publishWithHeaders(clientResponse3, clientHeaders);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse1.getOrderId())).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse2.getOrderId())).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse3.getOrderId())).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query using CollectionPredicateInput for venue accounts
        List<String> venueAccounts = List.of(venueAccount1, venueAccount2);
        CollectionPredicateInput collectionPredicate = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.VENUE_ACCOUNT_ID, venueAccounts);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(collectionPredicate), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);
        CursorConnection orderStatesPaged = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput);

        // Then - Should return orders with the specified venue accounts
        assertThat(orderStates).hasSize(2);
        assertThat(orderStatesPaged.getEdgesList()).hasSize(2);
        orderStates.forEach(orderState -> 
            assertThat(orderState.getVenueAccountsList()).anyMatch(venueAccounts::contains));
    }

    @Test
    void orderHistory_orderUpdatedWithReportHasUpdatedState() {
        // Given - Process an order and then update it
        String orderId = UUID.randomUUID().toString();
        ZonedDateTime baseTime = ZonedDateTime.now();

        OemsResponse newOrder = createTestOemsResponse(orderId, baseTime, OemsOrderStatus.STATUS_NEW, 0);
        OemsResponse updatedOrder = createTestOemsResponse(orderId, baseTime.plusSeconds(1), OemsOrderStatus.STATUS_PARTIALLY_FILLED, 5);

        tradingIngressExchange.publishWithHeaders(newOrder, TRADING_HEADERS);

        // Wait for first processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> 
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(1));
        awaitOrderState();

        // When - Update the order
        tradingIngressExchange.publishWithHeaders(updatedOrder, TRADING_HEADERS);

        // Wait for second processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> 
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(2));
        awaitOrderState();

        // Then - Query and verify the latest state
        SimplePredicateInput predicate = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(predicate), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Should return the latest state
        assertThat(orderStates).hasSize(1);
        assertThat(orderStates.getFirst().getOrderStatus()).isEqualTo(OrderStatus.PARTIALLY_FILLED);
        assertThat(orderStates.getFirst().getFilledQty()).isEqualTo("5");
    }

    @Test
    void orderHistory_orderUpdatedWithMultipleReportsHasStateFromLastUpdate() {
        // Given - Process an order with multiple updates
        String orderId = UUID.randomUUID().toString();
        ZonedDateTime baseTime = ZonedDateTime.now();

        OemsResponse newOrder = createTestOemsResponse(orderId, baseTime, OemsOrderStatus.STATUS_NEW, 0);
        OemsResponse partialFill1 = createTestOemsResponse(orderId, baseTime.plusSeconds(1), OemsOrderStatus.STATUS_PARTIALLY_FILLED, 3);
        OemsResponse partialFill2 = createTestOemsResponse(orderId, baseTime.plusSeconds(2), OemsOrderStatus.STATUS_PARTIALLY_FILLED, 7);
        OemsResponse finalFill = createTestOemsResponse(orderId, baseTime.plusSeconds(3), OemsOrderStatus.STATUS_FILLED, 10);

        tradingIngressExchange.publishWithHeaders(newOrder, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(partialFill1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(partialFill2, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(finalFill, TRADING_HEADERS);

        // Wait for all processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> 
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId)).hasSize(4));
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query for the latest state
        SimplePredicateInput predicate = new SimplePredicateInput(EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(predicate), List.of(), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return the final state
        assertThat(orderStates).hasSize(1);
        assertThat(orderStates.getFirst().getOrderStatus()).isEqualTo(OrderStatus.FILLED);
        assertThat(orderStates.getFirst().getFilledQty()).isEqualTo("10");
    }

    @Test
    void collectionPredicate_shouldReturnByParentOrderId() {
        // Given - Create parent and child orders
        String parentOrderId = UUID.randomUUID().toString();
        String childOrderId1 = UUID.randomUUID().toString();
        String childOrderId2 = UUID.randomUUID().toString();
        String orphanOrderId = UUID.randomUUID().toString();

        ZonedDateTime now = ZonedDateTime.now();
        
        // Create parent and child orders
        OemsRequest parentRequest = oemsRequest(parentOrderId, null);
        OemsRequest childRequest1 = oemsRequest(childOrderId1, parentOrderId);
        OemsRequest childRequest2 = oemsRequest(childOrderId2, parentOrderId);
        OemsRequest orphanRequest = oemsRequest(orphanOrderId, null);

        OemsResponse parentResponse = expectedOemsExecutionReport(parentRequest, OemsOrderStatus.STATUS_NEW, 0, now);
        OemsResponse childResponse1 = expectedOemsExecutionReport(childRequest1, OemsOrderStatus.STATUS_FILLED, 5, now.plusSeconds(1));
        OemsResponse childResponse2 = expectedOemsExecutionReport(childRequest2, OemsOrderStatus.STATUS_FILLED, 5, now.plusSeconds(2));
        OemsResponse orphanResponse = expectedOemsExecutionReport(orphanRequest, OemsOrderStatus.STATUS_FILLED, 10, now.plusSeconds(3));

        tradingIngressExchange.publishWithHeaders(parentResponse, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(childResponse1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(childResponse2, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(orphanResponse, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(parentOrderId)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(childOrderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(childOrderId2)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orphanOrderId)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();
        awaitOrderState();

        // When - Query by parent order ID using collection predicate
        CollectionPredicateInput collectionPredicate = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.PARENT_ORDER_ID, List.of(parentOrderId));
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(collectionPredicate), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return only child orders
        assertThat(orderStates).hasSize(2);
        orderStates.forEach(orderState -> assertThat(orderState.getParentOrderId()).isEqualTo(parentOrderId));
        assertThat(orderStates).extracting(OrderState::getOrderId).containsExactlyInAnyOrder(childOrderId1, childOrderId2);
    }

    @Test
    void collectionPredicate_shouldHandleProblematicPortfolioNamesWhenGettingOrderHistory() {
        // Given - Create orders with normal portfolio
        String orderId1 = UUID.randomUUID().toString();
        String orderId2 = UUID.randomUUID().toString();

        OemsResponse response1 = createTestOemsResponse(orderId1);
        OemsResponse response2 = createTestOemsResponse(orderId2);

        tradingIngressExchange.publishWithHeaders(response1, TRADING_HEADERS);
        tradingIngressExchange.publishWithHeaders(response2, TRADING_HEADERS);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> {
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId1)).isNotEmpty();
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId2)).isNotEmpty();
        });

        // Clear the queue
        awaitOrderState();
        awaitOrderState();

        // When - Query with problematic portfolio names (potential SQL injection)
        List<String> problematicPortfolios = List.of(
            "'; DROP TABLE orders; --",
            "' OR '1'='1",
            "\\'; SELECT * FROM users; --",
            PORTFOLIO_A // Valid portfolio ID
        );
        CollectionPredicateInput collectionPredicate = new CollectionPredicateInput(IN, PORTFOLIO_ID, problematicPortfolios);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(collectionPredicate), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return only legitimate results (no SQL injection should occur)
        orderStates.forEach(orderState -> assertThat(orderState.getPortfolioId()).isEqualTo(PORTFOLIO_A));
    }

    @Test
    void collectionPredicate_shouldHandleProblematicVenueAccountIdWhenGettingOrderHistory() {
        // Given - Create order with normal venue account
        ClientResponse clientResponse1 = createTestClientResponse();

        Map<String, String> clientHeaders = Map.of("messageType", ClientResponse.class.getSimpleName());
        tradingIngressExchange.publishWithHeaders(clientResponse1, clientHeaders);

        // Wait for processing
        await().atMost(DEFAULT_TIMEOUT).untilAsserted(() -> 
            assertThat(orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(clientResponse1.getOrderId())).isNotEmpty());

        awaitOrderState();

        // When - Query with problematic venue account IDs (potential SQL injection)
        String validVenueAccount = clientResponse1.getVenueAccount();
        List<String> problematicVenueAccounts = List.of(
            "'; DROP TABLE venues; --",
            "' OR '1'='1",
            "\\\\'; SELECT * FROM accounts; --",
            validVenueAccount // Valid venue account
        );
        CollectionPredicateInput collectionPredicate = new CollectionPredicateInput(IN, CollectionPredicateInput.Field.VENUE_ACCOUNT_ID, problematicVenueAccounts);
        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(List.of(), List.of(collectionPredicate), List.of(), 10, null, SortingOrder.DESC, null);

        List<OrderState> orderStates = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then - Should return only legitimate results
        assertThat(orderStates).hasSize(1);
        assertThat(orderStates.get(0).getVenueAccountsList()).contains(validVenueAccount);
    }
}