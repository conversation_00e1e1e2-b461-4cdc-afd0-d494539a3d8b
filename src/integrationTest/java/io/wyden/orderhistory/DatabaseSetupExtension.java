package io.wyden.orderhistory;

import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.platform.commons.support.ModifierSupport;
import org.junit.platform.commons.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.function.Predicate;

import static org.junit.platform.commons.util.ReflectionUtils.HierarchyTraversalMode.TOP_DOWN;

public abstract class DatabaseSetupExtension {

    public abstract void logTableContent(String tableName) throws Exception;

    protected void injectStaticSelf(ExtensionContext context) {
        Class<?> testClass = context.getRequiredTestClass();
        injectSelf(testClass, null, ModifierSupport::isStatic);
    }

    protected void injectSelf(ExtensionContext context) {
        Class<?> testClass = context.getRequiredTestClass();
        Object testInstance = context.getRequiredTestInstance();
        injectSelf(testClass, testInstance, ModifierSupport::isNotStatic);
    }

    private void injectSelf(Class<?> testClass, Object testInstance, Predicate<Field> predicate) {
        predicate = predicate.and(field -> isSelf(field.getType()));
        ReflectionUtils.findFields(testClass, predicate, TOP_DOWN).forEach(field -> {
            try {
                field.setAccessible(true);
                field.set(testInstance, this);
            } catch (Exception ex) {
                throw new RuntimeException(ex);
            }
        });
    }

    private boolean isSelf(Class<?> type) {
        return type == DatabaseSetupExtension.class;
    }
}
