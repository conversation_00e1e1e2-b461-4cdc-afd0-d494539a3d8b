package io.wyden.smartrecommendationengine.service.client.inbound;

import io.wyden.cloud.utils.test.TracingMock;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.smartrecommendationengine.ExecutionRecommendations;
import io.wyden.smartrecommendationengine.domain.RecommendationSubscriptionCache;
import io.wyden.smartrecommendationengine.infra.SimpleIntegrationTestBase;
import io.wyden.smartrecommendationengine.service.audit.AuditService;
import io.wyden.smartrecommendationengine.service.connectorstate.ConnectorStateService;
import io.wyden.smartrecommendationengine.service.marketdata.MarketDataCache;
import io.wyden.smartrecommendationengine.service.marketdata.MarketDataSubscriber;
import io.wyden.smartrecommendationengine.service.marketdata.model.MdRequest;
import io.wyden.smartrecommendationengine.utils.HostIdUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.consumed;
import static io.wyden.published.smartrecommendationengine.ResponseStatus.DONE;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

@ContextConfiguration(initializers = SimpleIntegrationTestBase.Initializer.class)
@SpringBootTest(properties = {"spring.main.allow-bean-definition-overriding=true", "logging.level.root=debug"})
@ExtendWith(SpringExtension.class)
public abstract class IntegrationTestBase extends SimpleIntegrationTestBase{

    public static final Logger LOGGER = LoggerFactory.getLogger(IntegrationTestBase.class);
    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(4);
    private static final Duration ONE_SECOND = Duration.ofSeconds(1);

    protected static final String HOST_ID = "1";
    protected static final String SUBSCRIPTION_ID = "1";
    protected static final String UNKNOWN_SUBSCRIPTION_ID = "2";
    protected static final String LIMIT = "200";
    protected static final String HALF_LIMIT = "100";
    protected static final String HALF_STOP = "100";
    protected static final String TRIPLE_STOP = "600";
    protected static final String DOUBLE_LIMIT = "400";
    protected static final String STOP = "200";
    protected static final String QUARTER_LIMIT = "50";
    protected static final String QUANTITY = "10";

    @MockBean
    protected BestExecutionRequestHandler bestExecutionRequestHandler;
    @MockBean
    protected MarketDataCache marketDataCache;
    @MockBean
    protected ConnectorStateService connectorStateService;
    @MockBean
    protected AuditService auditService;

    protected RecommendationSubscriptionCache recommendationSubscriptionCache;
    protected RabbitExchange<ExecutionRecommendations> executionRecommendationsExchange;
    protected RabbitQueue<ExecutionRecommendations> executionRecommendationsQueue;
    protected List<ExecutionRecommendations> executionRecommendations = new ArrayList<>();
    protected AtomicReference<ExecutionRecommendations> executionRecommendationsDone = new AtomicReference<>();
    protected AtomicReference<ExecutionRecommendations> executionRecommendationsHeartbeat = new AtomicReference<>();
    protected InstrumentKey instrumentKey1;
    protected InstrumentKey instrumentKey2;
    private String executionRecommendationsTag;

    @BeforeEach
    void setUpBase() {
        executionRecommendationsQueue = new RabbitQueueBuilder<ExecutionRecommendations>(rabbitIntegrator).setQueueName("temporary-execution-recommendations-queue").declare();
        executionRecommendationsExchange = OemsExchange.SmartRecommendationEngine.bestExecutionRecommendationsExchange(rabbitIntegrator);
        recommendationSubscriptionCache = new RecommendationSubscriptionCache();
        HostIdUtils hostIdUtils = mock(HostIdUtils.class);
        when(hostIdUtils.getHostId()).thenReturn(HOST_ID);
        ExecutionRecommendationsEmitter executionRecommendationsEmitter = new ExecutionRecommendationsEmitter(hostIdUtils, executionRecommendationsExchange, TracingMock.createMock());
        marketDataCache = spy(MarketDataCache.class);
        MarketDataSubscriber marketDataSubscriber = mock(MarketDataSubscriber.class);
        when(marketDataSubscriber.subscribe(anyList(), any(MdRequest.class))).thenReturn(CompletableFuture.completedFuture(null));
        Tracing tracing = mock(Tracing.class);
        bestExecutionRequestHandler = new BestExecutionRequestHandler(recommendationSubscriptionCache, executionRecommendationsEmitter, marketDataCache, connectorStateService, marketDataSubscriber, auditService, tracing, 100, 6000);
        instrumentKey1 = InstrumentKey.newBuilder()
            .setInstrumentId("BTC/USD@BNC")
            .build();
        instrumentKey2 = InstrumentKey.newBuilder()
            .setInstrumentId("BTC/USD@BMX")
            .build();

        ReflectionTestUtils.setField(bestExecutionRequestHandler, "recommendationInterval", 10);
        ReflectionTestUtils.setField(bestExecutionRequestHandler, "recommendationSubscriptionRefreshTimeout", 30000);

        observeExecutionRecommendationsExchange();
    }

    @AfterEach
    void tearDownBase() throws IOException {
        // clean up scheduled tasks
        recommendationSubscriptionCache.removeSubscriptionForId(SUBSCRIPTION_ID);
        recommendationSubscriptionCache.removeSubscriptionForId(UNKNOWN_SUBSCRIPTION_ID);
        detachExecutionRecommendationsExchange();
    }

    public void detachExecutionRecommendationsExchange() throws IOException {
        rabbitIntegrator.getConsumptionChannel().basicCancel(executionRecommendationsTag);
    }

    public void observeExecutionRecommendationsExchange() {

        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), ExecutionRecommendations.class.getSimpleName()
        );

        executionRecommendationsQueue.bindWithHeaders(executionRecommendationsExchange, MatchingCondition.ALL, headers);

        executionRecommendationsTag = executionRecommendationsQueue.attachConsumer(ExecutionRecommendations.parser(), (recommendations, params) -> {
            LOGGER.warn("Received Execution Recommendations: {}", recommendations);
            executionRecommendations.add(recommendations);
            if (recommendations.getStatus().equals(DONE)) {
                executionRecommendationsDone.set(recommendations);
            } else {
                executionRecommendationsHeartbeat.set(recommendations);
            }
            return consumed();
        });
    }

    public ExecutionRecommendations awaitExecutionRecommendationsDone() {
        return await().atMost(DEFAULT_TIMEOUT).until(() -> executionRecommendationsDone.getAndSet(null), Objects::nonNull);
    }

    public ExecutionRecommendations awaitExecutionRecommendationsHeartbeat() {
        return await().atMost(DEFAULT_TIMEOUT).until(() -> executionRecommendationsHeartbeat.getAndSet(null), Objects::nonNull);
    }

    public void ensureNoMoreExecutionRecommendations() {
        await().during(DEFAULT_TIMEOUT).atMost(DEFAULT_TIMEOUT.plus(ONE_SECOND)).until(() -> executionRecommendationsDone.get() == null && executionRecommendationsHeartbeat.get() == null);
    }
}
