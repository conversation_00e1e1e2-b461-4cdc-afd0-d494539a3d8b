Feature: Cancelled Orders should release reserved values

Scenario: cancelled reservations are released fully

When Client_12 reserves TRADE-A for a trade of 0.001 BTC/USD at price 90000.0 on Bitfinex_12 account with fixed fee 5.0 USD

Then position state change to:
| portfolioId | instrument | qty | pendingQty | availableForTradingQty | availableForWithdrawalQty |
| Client_12   | USD        | -   | -95        | -95                    | -95                       |
| Client_12   | BTC        | -   | 0.001      | -                      | -                         |

When TRADE-A reservation of BTC/USD on Bitfinex_11 account and Client_12 portfolio is canceled by the user

Then position state change to:
| portfolioId | instrument | qty | pendingQty | availableForTradingQty | availableForWithdrawalQty |
| Client_12   | USD        | -   | -          | -                      | -                         |
| Client_12   | BTC        | -   | -          | -                      | -                         |
