Feature: Portfolio Asset Transfer metrics calculation
  As a booking system
  I want to apply ledger entries to positions
  So that I calculate P&L for trades

  Background:
    Given last processed sequence number is 9

  Scenario: Client transfers 2 contracts of BTC/USD to another portfolio with 10 USD fee booked on Bank portfolio
    Given portfolio Client_10 exists and is configured with USD currency
    Given portfolio Target_10 exists and is configured with USD currency
    And portfolio Bank_10 exists and is configured with USD currency

    Given current market price for BTC/USD is 11000 USD

    Given a BookingCompleted event for test-transfer-10 with sequence number 10
    And the event contains a list of ledger entries with following details:
      | portfolioId | ledgerEntryType | instrument | qty | price  | fee |
      | Client_10   | TRANSFER        | BTC/USD    | -2  | 11_000 | -   |
      | Client_10   | TRANSFER_FEE    | USD        | -10 | -      | -   |
      | Target_10   | TRANSFER        | BTC/USD    | 2   | 11_000 | -   |
      | Bank_10     | TRANSFER_FEE    | USD        | 10  | -      | -   |

    When event with sequence number 10 is processed

    Then the ledger entries for test-transfer-10 should be stored in the database
    And positions should be updated with following details:
      | referenceId               | Client_10 | Client_10 | Target_10 | Bank_10 |
      | instrument                | BTC/USD   | USD       | BTC/USD   | USD     |
      | qty                       | -2        | -10       | 2         | 10      |
      | notionalQty               | -2        | -10       | 2         | 10      |
      | netAveragePrice           | 11_000    | 1         | 11_000    | 1       |
      | grossAveragePrice         | 11_000    | 1         | 11_000    | 1       |
      | marketValue               | -22_000   | -10       | 22_000    | 10      |
      | marketValueSc             | -22_000   | -10       | 22_000    | 10      |
      | netCost                   | -22_000   | -10       | 22_000    | 10      |
      | netCostSc                 | -22_000   | -10       | 22_000    | 10      |
      | grossCost                 | -22_000   | -10       | 22_000    | 10      |
      | grossCostSc               | -22_000   | -10       | 22_000    | 10      |
      | netRealizedPnl            | -         | -         | -         | -       |
      | netRealizedPnlSc          | -         | -         | -         | -       |
      | grossRealizedPnl          | -         | -         | -         | -       |
      | grossRealizedPnlSc        | -         | -         | -         | -       |
      | netUnrealizedPnl          | -         | -         | -         | -       |
      | netUnrealizedPnlSc        | -         | -         | -         | -       |
      | grossUnrealizedPnl        | -         | -         | -         | -       |
      | grossUnrealizedPnlSc      | -         | -         | -         | -       |

# TODO SPL double check is transfers are settled immediately
