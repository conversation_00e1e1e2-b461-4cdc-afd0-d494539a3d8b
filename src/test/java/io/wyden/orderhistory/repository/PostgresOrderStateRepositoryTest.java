package io.wyden.orderhistory.repository;

import io.wyden.orderhistory.model.CollectionPredicateInput;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.orderhistory.model.SimplePredicateInput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;

import java.util.List;
import java.util.Set;

import static io.wyden.orderhistory.model.SimplePredicateInput.Field.PORTFOLIO_ID;
import static io.wyden.orderhistory.model.SimplePredicateInput.PredicateType.EQUAL;
import static io.wyden.orderhistory.model.SortingOrder.DESC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PostgresOrderStateRepositoryTest {

    private static String VENUE_ACCOUNT_1 = "e2e_WydenMock_Account_1";
    private static String VENUE_ACCOUNT_2 = "e2e_WydenMock_Account_2";
    private static String VENUE_ACCOUNT_3 = "e2e_WydenMock_Account_3";
    private static String PORTFOLIO_ID_1 = "e2e_portfolio_1";
    private static String PORTFOLIO_ID_2 = "e2e_portfolio_2";
    private static String PORTFOLIO_ID_3 = "e2e_portfolio_3";
    private static String INSTRUMENT_ID_1 = "BTCUSD@FOREX@Test1";
    private static String INSTRUMENT_ID_2 = "BTCUSD@FOREX@Test2";
    private static String INSTRUMENT_ID_3 = "BTCUSD@FOREX@Test3";

    @Mock
    JdbcTemplate jdbcTemplate;

    OrderStateRepository orderStateRepository;

    @BeforeEach
    void setup() {
        orderStateRepository = new PostgresOrderStateRepository(jdbcTemplate);
    }

    @Test
    void shouldCreateProperQueryForMultipleVenueAccountsAndSinglePortfolio() {
        // given
        when(jdbcTemplate.query(anyString(), any(RowMapper.class), any(Object[].class))).thenReturn(List.of());

        OrderHistorySearchInput orderHistorySearch = new OrderHistorySearchInput(
            Set.of(new SimplePredicateInput(EQUAL, PORTFOLIO_ID, PORTFOLIO_ID_1)),
            Set.of(new CollectionPredicateInput(CollectionPredicateInput.PredicateType.IN, CollectionPredicateInput.Field.INSTRUMENT_ID, List.of(INSTRUMENT_ID_1, INSTRUMENT_ID_2, INSTRUMENT_ID_3)),
                new CollectionPredicateInput(CollectionPredicateInput.PredicateType.IN, CollectionPredicateInput.Field.VENUE_ACCOUNT_ID, List.of(VENUE_ACCOUNT_1, VENUE_ACCOUNT_2, VENUE_ACCOUNT_3))),
            Set.of(),
            null,
            null,
            DESC,
            null
        );

        // when
        List<OrderStateEntity> orderStateList = orderStateRepository.findOrderStateSnapshotsBySearchInput(orderHistorySearch);

        // then
        assertThat(orderStateList).isNotNull();
    }
}
