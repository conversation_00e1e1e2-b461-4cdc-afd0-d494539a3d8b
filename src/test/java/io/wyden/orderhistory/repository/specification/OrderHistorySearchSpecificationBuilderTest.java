package io.wyden.orderhistory.repository.specification;

import io.wyden.orderhistory.model.CollectionPredicateInput;
import io.wyden.orderhistory.model.DatePredicateInput;
import io.wyden.orderhistory.model.OrderHistorySearchIndex;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.SimplePredicateInput;
import io.wyden.orderhistory.model.SortingField;
import io.wyden.orderhistory.model.SortingOrder;
import org.junit.jupiter.api.Test;
import org.springframework.data.jpa.domain.Specification;

import java.time.OffsetDateTime;
import java.util.List;

import static io.wyden.orderhistory.repository.specification.OrderHistorySearchSpecificationBuilder.buildSpecification;
import static org.assertj.core.api.Assertions.assertThat;

class OrderHistorySearchSpecificationBuilderTest {

    @Test
    void buildSpecification_withSimplePredicates_createsCorrectSpecification() {
        // Given
        SimplePredicateInput portfolioIdPredicate = new SimplePredicateInput(
            SimplePredicateInput.PredicateType.EQUAL,
            SimplePredicateInput.Field.PORTFOLIO_ID,
            "test-portfolio"
        );

        SimplePredicateInput orderStatusPredicate = new SimplePredicateInput(
            SimplePredicateInput.PredicateType.EQUAL,
            SimplePredicateInput.Field.ORDER_STATUS,
            "FILLED"
        );

        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(
            List.of(portfolioIdPredicate, orderStatusPredicate),
            List.of(),
            List.of(),
            10,
            null,
            SortingOrder.DESC,
            SortingField.LATEST_MESSAGE_TIMESTAMP
        );

        // When
        Specification<OrderHistorySearchIndex> specification = buildSpecification(searchInput);

        // Then
        assertThat(specification).isNotNull();
        // Note: In a real test, you would test the specification against a test database
        // or use a more sophisticated mocking approach to verify the generated criteria
    }

    @Test
    void buildSpecification_withCollectionPredicates_createsCorrectSpecification() {
        // Given
        CollectionPredicateInput portfolioIdsPredicate = new CollectionPredicateInput(
            CollectionPredicateInput.PredicateType.IN,
            CollectionPredicateInput.Field.PORTFOLIO_ID,
            List.of("portfolio1", "portfolio2", "portfolio3")
        );

        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(
            List.of(),
            List.of(portfolioIdsPredicate),
            List.of(),
            10,
            null,
            SortingOrder.ASC,
            SortingField.LATEST_MESSAGE_TIMESTAMP
        );

        // When
        Specification<OrderHistorySearchIndex> specification = buildSpecification(searchInput);

        // Then
        assertThat(specification).isNotNull();
    }

    @Test
    void buildSpecification_withDatePredicates_createsCorrectSpecification() {
        // Given
        OffsetDateTime startTime = OffsetDateTime.now().minusDays(7);
        OffsetDateTime endTime = OffsetDateTime.now();

        DatePredicateInput startDatePredicate = new DatePredicateInput(
            DatePredicateInput.PredicateType.FROM,
            DatePredicateInput.Field.CREATED_AT,
            startTime.toString()
        );

        DatePredicateInput endDatePredicate = new DatePredicateInput(
            DatePredicateInput.PredicateType.TO,
            DatePredicateInput.Field.CREATED_AT,
            endTime.toString()
        );

        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(
            List.of(),
            List.of(),
            List.of(startDatePredicate, endDatePredicate),
            10,
            null,
            SortingOrder.DESC,
            SortingField.LATEST_MESSAGE_TIMESTAMP
        );

        // When
        Specification<OrderHistorySearchIndex> specification = buildSpecification(searchInput);

        // Then
        assertThat(specification).isNotNull();
    }

    @Test
    void buildSpecification_withMultipleSimplePredicatesForSameField_optimizesToCollectionPredicate() {
        // Given - Multiple EQUAL predicates for the same field should be optimized to IN predicate
        SimplePredicateInput portfolioId1 = new SimplePredicateInput(
            SimplePredicateInput.PredicateType.EQUAL,
            SimplePredicateInput.Field.PORTFOLIO_ID,
            "portfolio1"
        );

        SimplePredicateInput portfolioId2 = new SimplePredicateInput(
            SimplePredicateInput.PredicateType.EQUAL,
            SimplePredicateInput.Field.PORTFOLIO_ID,
            "portfolio2"
        );

        SimplePredicateInput portfolioId3 = new SimplePredicateInput(
            SimplePredicateInput.PredicateType.EQUAL,
            SimplePredicateInput.Field.PORTFOLIO_ID,
            "portfolio3"
        );

        OrderHistorySearchInput searchInput = new OrderHistorySearchInput(
            List.of(portfolioId1, portfolioId2, portfolioId3),
            List.of(),
            List.of(),
            10,
            null,
            SortingOrder.DESC,
            SortingField.LATEST_MESSAGE_TIMESTAMP
        );

        // When
        Specification<OrderHistorySearchIndex> specification = buildSpecification(searchInput);

        // Then
        assertThat(specification).isNotNull();
        // The builder should have optimized the 3 simple predicates into 1 collection predicate
    }
}
