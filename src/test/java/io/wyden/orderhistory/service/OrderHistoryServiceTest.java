package io.wyden.orderhistory.service;

import io.wyden.orderhistory.model.CollectionPredicateInput;
import io.wyden.orderhistory.model.DatePredicateInput;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.orderhistory.model.SimplePredicateInput;
import io.wyden.orderhistory.model.SortingField;
import io.wyden.orderhistory.repository.OrderStateRepository;
import io.wyden.orderhistory.service.utils.DbUtils;
import io.wyden.published.reporting.OrderCategory;
import io.wyden.published.reporting.OrderState;
import io.wyden.published.reporting.OrderType;
import io.wyden.published.reporting.TIF;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.Collection;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.UUID;

import static io.wyden.cloudutils.tools.DateUtils.isoUtcTimeToZonedDateTime;
import static io.wyden.cloudutils.tools.DateUtils.toIsoUtcTime;
import static io.wyden.orderhistory.model.DatePredicateInput.Field.CREATED_AT;
import static io.wyden.orderhistory.model.DatePredicateInput.PredicateType.FROM;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.INSTRUMENT_ID;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.PORTFOLIO_ID;
import static io.wyden.orderhistory.model.SimplePredicateInput.Field.VENUE_ACCOUNT_ID;
import static io.wyden.orderhistory.model.SimplePredicateInput.PredicateType.EQUAL;
import static io.wyden.orderhistory.model.SortingOrder.DESC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderHistoryServiceTest {

    private static final String VENUE_ACCOUNT_1 = "e2e_WydenMock_Account_1";
    private static final String VENUE_ACCOUNT_2 = "e2e_WydenMock_Account_2";
    private static final String VENUE_ACCOUNT_3 = "e2e_WydenMock_Account_3";
    private static final String PORTFOLIO_ID_1 = "e2e_portfolio_1";
    private static final String PORTFOLIO_ID_2 = "e2e_portfolio_2";
    private static final String PORTFOLIO_ID_3 = "e2e_portfolio_3";
    private static final String INSTRUMENT_ID_1 = "BTCUSD@FOREX@Test1";
    private static final String INSTRUMENT_ID_2 = "BTCUSD@FOREX@Test2";
    private static final String INSTRUMENT_ID_3 = "BTCUSD@FOREX@Test3";

    @Mock
    OrderStateRepository orderStateRepository;
    @Mock
    OrderStateProcessor orderStateProcessor;

    OrderHistoryService orderHistoryService;

    @BeforeEach
    void setup() {
        orderHistoryService = new OrderHistoryService(orderStateRepository, orderStateProcessor);
    }

    @Test
    void shouldCreateProperSearchInputsForSimpleInputsOnly() {
        // given
        when(orderStateRepository.findOrderStateSnapshotsBySearchInput(any(OrderHistorySearchInput.class))).thenAnswer(invocationOnMock -> {
            OrderHistorySearchInput input = invocationOnMock.getArgument(0, OrderHistorySearchInput.class);
            SimplePredicateInput simplePredicateInput = input.simplePredicates().stream().findAny().orElseThrow();
            return input.collectionPredicates().stream()
                .map(CollectionPredicateInput::value)
                .flatMap(Collection::stream)
                .map(venueAccountId -> OrderStateEntity.OrderStateEntityBuilder.builder()
                    .orderId(UUID.randomUUID().toString())
                    .clientId(UUID.randomUUID().toString())
                    .orderStatus("NEW")
                    .orderQty("1")
                    .tif(TIF.GTD.name())
                    .remainingQty("1")
                    .side("BUY")
                    .venueAccounts(List.of(venueAccountId))
                    .createdAt(OffsetDateTime.now())
                    .orderCategory(OrderCategory.SIMPLE.name())
                    .orderType(OrderType.MARKET.name())
                    .portfolioId(simplePredicateInput.value())
                    .build())
                .toList();
        });

        OrderHistorySearchInput orderHistorySearch = new OrderHistorySearchInput(
            Set.of(new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_1), new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_2),
                new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_3), new SimplePredicateInput(EQUAL, PORTFOLIO_ID, PORTFOLIO_ID_1)),
            Set.of(),
            Set.of(),
            null,
            null,
            DESC,
            SortingField.CREATED_AT);

        // when
        List<OrderState> orderHistoryTest = orderHistoryService.getOrderStateSnapshots(orderHistorySearch);

        // then
        List<String> expectedAccounts = List.of(VENUE_ACCOUNT_1, VENUE_ACCOUNT_2, VENUE_ACCOUNT_3);

        assertThat(orderHistoryTest).isNotEmpty();
        assertThat(orderHistoryTest.size()).isEqualTo(3);
        assertThat(orderHistoryTest.get(0).getVenueAccountId()).isIn(expectedAccounts);
        assertThat(orderHistoryTest.get(0).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(1).getVenueAccountId()).isIn(expectedAccounts);
        assertThat(orderHistoryTest.get(1).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(2).getVenueAccountId()).isIn(expectedAccounts);
        assertThat(orderHistoryTest.get(2).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
    }

    @Test
    void shouldCreateProperSearchInputsForSimpleAndCollectionInputs() {
        // given
        when(orderStateRepository.findOrderStateSnapshotsBySearchInput(any(OrderHistorySearchInput.class))).thenAnswer(invocationOnMock -> {
            OrderHistorySearchInput input = invocationOnMock.getArgument(0, OrderHistorySearchInput.class);
            SimplePredicateInput simplePredicateInput = input.simplePredicates().stream().findAny().orElseThrow();

            List<String> instrumentIds = Lists.newArrayList(input.collectionPredicates().stream().filter(predicateInput -> predicateInput.fieldName().equals(INSTRUMENT_ID.name())).findFirst().orElseThrow().value());
            List<String> venueAccounts = Lists.newArrayList(input.collectionPredicates().stream().filter(predicateInput -> predicateInput.fieldName().equals(VENUE_ACCOUNT_ID.name())).findFirst().orElseThrow().value());
            Random random = new Random();

            return input.collectionPredicates().stream()
                .map(CollectionPredicateInput::value)
                .flatMap(Collection::stream)
                .map(s -> OrderStateEntity.OrderStateEntityBuilder.builder()
                    .orderId(UUID.randomUUID().toString())
                    .clientId(UUID.randomUUID().toString())
                    .orderStatus("NEW")
                    .orderQty("1")
                    .tif(TIF.GTD.name())
                    .remainingQty("1")
                    .side("BUY")
                    .venueAccounts(List.of(venueAccounts.get(random.nextInt(venueAccounts.size()))))
                    .createdAt(OffsetDateTime.now())
                    .orderCategory(OrderCategory.SIMPLE.name())
                    .orderType(OrderType.MARKET.name())
                    .portfolioId(simplePredicateInput.value())
                    .instrumentId(instrumentIds.get(random.nextInt(instrumentIds.size())))
                    .build())
                .toList();
        });

        OrderHistorySearchInput orderHistorySearch = new OrderHistorySearchInput(
            Set.of(new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_1), new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_2),
                new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_3), new SimplePredicateInput(EQUAL, PORTFOLIO_ID, PORTFOLIO_ID_1)),
            Set.of(new CollectionPredicateInput(CollectionPredicateInput.PredicateType.IN, CollectionPredicateInput.Field.INSTRUMENT_ID, List.of(INSTRUMENT_ID_1, INSTRUMENT_ID_2, INSTRUMENT_ID_3))),
            Set.of(),
            null,
            null,
            DESC,
            SortingField.CREATED_AT);

        // when
        List<OrderState> orderHistoryTest = orderHistoryService.getOrderStateSnapshots(orderHistorySearch);

        // then
        List<String> expectedAccounts = List.of(VENUE_ACCOUNT_1, VENUE_ACCOUNT_2, VENUE_ACCOUNT_3);
        List<String> expectedInstrumentIds = List.of(INSTRUMENT_ID_1, INSTRUMENT_ID_2, INSTRUMENT_ID_3);

        assertThat(orderHistoryTest).isNotEmpty();
        assertThat(orderHistoryTest.size()).isEqualTo(6);
        assertThat(orderHistoryTest.get(0).getVenueAccountId()).isIn(expectedAccounts);
        assertThat(orderHistoryTest.get(0).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(0).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(1).getVenueAccountId()).isIn(expectedAccounts);
        assertThat(orderHistoryTest.get(1).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(1).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(2).getVenueAccountId()).isIn(expectedAccounts);
        assertThat(orderHistoryTest.get(2).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(2).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(3).getVenueAccountId()).isIn(expectedAccounts);
        assertThat(orderHistoryTest.get(3).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(3).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(4).getVenueAccountId()).isIn(expectedAccounts);
        assertThat(orderHistoryTest.get(4).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(4).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(5).getVenueAccountId()).isIn(expectedAccounts);
        assertThat(orderHistoryTest.get(5).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(5).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
    }

    @Test
    void shouldCreateProperSearchInputsForAllInputs() {
        // given
        when(orderStateRepository.findOrderStateSnapshotsBySearchInput(any(OrderHistorySearchInput.class))).thenAnswer(invocationOnMock -> {
            OrderHistorySearchInput input = invocationOnMock.getArgument(0, OrderHistorySearchInput.class);
            SimplePredicateInput simplePredicateInput = input.simplePredicates().stream().findAny().orElseThrow();
            DatePredicateInput datePredicateInput = input.datePredicateInputs().stream().findAny().orElseThrow();

            List<String> instrumentIds = Lists.newArrayList(input.collectionPredicates().stream().filter(predicateInput -> predicateInput.fieldName().equals(INSTRUMENT_ID.name())).findFirst().orElseThrow().value());
            List<String> venueAccountIds = Lists.newArrayList(input.collectionPredicates().stream().filter(predicateInput -> predicateInput.fieldName().equals(VENUE_ACCOUNT_ID.name())).findFirst().orElseThrow().value());
            Random random = new Random();

            return input.collectionPredicates().stream()
                .map(CollectionPredicateInput::value)
                .flatMap(Collection::stream)
                .map(s -> OrderStateEntity.OrderStateEntityBuilder.builder()
                    .orderId(UUID.randomUUID().toString())
                    .clientId(UUID.randomUUID().toString())
                    .orderStatus("NEW")
                    .orderQty("1")
                    .tif(TIF.GTD.name())
                    .remainingQty("1")
                    .side("BUY")
                    .venueAccounts(List.of(venueAccountIds.get(random.nextInt(venueAccountIds.size()))))
                    .createdAt(OffsetDateTime.parse(datePredicateInput.value()))
                    .orderCategory(OrderCategory.SIMPLE.name())
                    .orderType(OrderType.MARKET.name())
                    .portfolioId(simplePredicateInput.value())
                    .instrumentId(instrumentIds.get(random.nextInt(instrumentIds.size())))
                    .build())
                .toList();
        });

        OrderHistorySearchInput orderHistorySearch = new OrderHistorySearchInput(
            Set.of(new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_1), new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_2),
                new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_3), new SimplePredicateInput(EQUAL, PORTFOLIO_ID, PORTFOLIO_ID_1)),
            Set.of(new CollectionPredicateInput(CollectionPredicateInput.PredicateType.IN, CollectionPredicateInput.Field.INSTRUMENT_ID, List.of(INSTRUMENT_ID_1, INSTRUMENT_ID_2, INSTRUMENT_ID_3))),
            Set.of(new DatePredicateInput(FROM, CREATED_AT, toIsoUtcTime(ZonedDateTime.now().minusHours(2)))),
            null,
            null,
            DESC,
            SortingField.CREATED_AT);

        // when
        List<OrderState> orderHistoryTest = orderHistoryService.getOrderStateSnapshots(orderHistorySearch);

        // then
        List<String> expectedVenueAccountIds = List.of(VENUE_ACCOUNT_1, VENUE_ACCOUNT_2, VENUE_ACCOUNT_3);
        List<String> expectedInstrumentIds = List.of(INSTRUMENT_ID_1, INSTRUMENT_ID_2, INSTRUMENT_ID_3);

        assertThat(orderHistoryTest).isNotEmpty();
        assertThat(orderHistoryTest.size()).isEqualTo(6);
        assertThat(orderHistoryTest.get(0).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(0).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(0).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(isoUtcTimeToZonedDateTime(orderHistoryTest.get(0).getCreatedAt())).isBefore(ZonedDateTime.now().minusHours(1));
        assertThat(orderHistoryTest.get(1).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(1).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(1).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(isoUtcTimeToZonedDateTime(orderHistoryTest.get(1).getCreatedAt())).isBefore(ZonedDateTime.now().minusHours(1));
        assertThat(orderHistoryTest.get(2).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(2).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(2).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(isoUtcTimeToZonedDateTime(orderHistoryTest.get(2).getCreatedAt())).isBefore(ZonedDateTime.now().minusHours(1));
        assertThat(orderHistoryTest.get(3).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(3).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(3).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(isoUtcTimeToZonedDateTime(orderHistoryTest.get(3).getCreatedAt())).isBefore(ZonedDateTime.now().minusHours(1));
        assertThat(orderHistoryTest.get(4).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(4).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(4).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(isoUtcTimeToZonedDateTime(orderHistoryTest.get(4).getCreatedAt())).isBefore(ZonedDateTime.now().minusHours(1));
        assertThat(orderHistoryTest.get(5).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(5).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(5).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(isoUtcTimeToZonedDateTime(orderHistoryTest.get(5).getCreatedAt())).isBefore(ZonedDateTime.now().minusHours(1));
    }

    @Test
    void shouldCreateProperSearchInputsForSimpleAndCollectionInputsWithDuplicatedPortfolioId() {
        // given
        when(orderStateRepository.findOrderStateSnapshotsBySearchInput(any(OrderHistorySearchInput.class))).thenAnswer(invocationOnMock -> {
            OrderHistorySearchInput input = invocationOnMock.getArgument(0, OrderHistorySearchInput.class);

            List<String> portfolios = Lists.newArrayList(input.collectionPredicates().stream().filter(predicateInput -> predicateInput.fieldName().equals(PORTFOLIO_ID.name())).findFirst().orElseThrow().value());
            List<String> venueAccounts = Lists.newArrayList(input.collectionPredicates().stream().filter(predicateInput -> predicateInput.fieldName().equals(VENUE_ACCOUNT_ID.name())).findFirst().orElseThrow().value());

            Random random = new Random();

            return input.collectionPredicates().stream()
                .map(CollectionPredicateInput::value)
                .flatMap(Collection::stream)
                .map(s -> OrderStateEntity.OrderStateEntityBuilder.builder()
                    .orderId(UUID.randomUUID().toString())
                    .clientId(UUID.randomUUID().toString())
                    .orderStatus("NEW")
                    .orderQty("1")
                    .tif(TIF.GTD.name())
                    .remainingQty("1")
                    .side("BUY")
                    .venueAccounts(List.of(venueAccounts.get(random.nextInt(venueAccounts.size()))))
                    .createdAt(OffsetDateTime.now())
                    .orderCategory(OrderCategory.SIMPLE.name())
                    .orderType(OrderType.MARKET.name())
                    .portfolioId(portfolios.get(random.nextInt(portfolios.size())))
                    .build())
                .toList();
        });

        OrderHistorySearchInput orderHistorySearch = new OrderHistorySearchInput(
            Set.of(new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_1), new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_2),
                new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_3), new SimplePredicateInput(EQUAL, PORTFOLIO_ID, PORTFOLIO_ID_1)),
            Set.of(new CollectionPredicateInput(CollectionPredicateInput.PredicateType.IN, CollectionPredicateInput.Field.PORTFOLIO_ID, List.of(PORTFOLIO_ID_1, PORTFOLIO_ID_2))),
            Set.of(),
            null,
            null,
            DESC,
            SortingField.CREATED_AT);

        // when
        List<OrderState> orderHistoryTest = orderHistoryService.getOrderStateSnapshots(orderHistorySearch);

        // then
        List<String> expectedVenueAccounts = List.of(VENUE_ACCOUNT_1, VENUE_ACCOUNT_2, VENUE_ACCOUNT_3);
        List<String> expectedPortfolioIds = List.of(PORTFOLIO_ID_1, PORTFOLIO_ID_2);

        assertThat(orderHistoryTest).isNotEmpty();
        assertThat(orderHistoryTest.size()).isEqualTo(5);
        assertThat(orderHistoryTest.get(0).getVenueAccountId()).isIn(expectedVenueAccounts);
        assertThat(orderHistoryTest.get(0).getPortfolioId()).isIn(expectedPortfolioIds);
        assertThat(orderHistoryTest.get(1).getVenueAccountId()).isIn(expectedVenueAccounts);
        assertThat(orderHistoryTest.get(1).getPortfolioId()).isIn(expectedPortfolioIds);
        assertThat(orderHistoryTest.get(2).getVenueAccountId()).isIn(expectedVenueAccounts);
        assertThat(orderHistoryTest.get(2).getPortfolioId()).isIn(expectedPortfolioIds);
        assertThat(orderHistoryTest.get(3).getVenueAccountId()).isIn(expectedVenueAccounts);
        assertThat(orderHistoryTest.get(3).getPortfolioId()).isIn(expectedPortfolioIds);
        assertThat(orderHistoryTest.get(4).getVenueAccountId()).isIn(expectedVenueAccounts);
        assertThat(orderHistoryTest.get(4).getPortfolioId()).isIn(expectedPortfolioIds);
    }

    @Test
    void shouldCreateProperSearchInputsForSimpleAndCollectionInputsWithDuplicatedTarget() {
        // given
        when(orderStateRepository.findOrderStateSnapshotsBySearchInput(any(OrderHistorySearchInput.class))).thenAnswer(invocationOnMock -> {
            OrderHistorySearchInput input = invocationOnMock.getArgument(0, OrderHistorySearchInput.class);

            List<String> venueAccountIds = Lists.newArrayList(input.collectionPredicates().stream().filter(predicateInput -> predicateInput.fieldName().equals(VENUE_ACCOUNT_ID.name())).findFirst().orElseThrow().value());
            List<String> instrumentsId = Lists.newArrayList(input.collectionPredicates().stream().filter(predicateInput -> predicateInput.fieldName().equals(INSTRUMENT_ID.name())).findFirst().orElseThrow().value());

            Random random = new Random();

            return input.collectionPredicates().stream()
                .map(CollectionPredicateInput::value)
                .flatMap(Collection::stream)
                .map(s -> OrderStateEntity.OrderStateEntityBuilder.builder()
                    .orderId(UUID.randomUUID().toString())
                    .clientId(UUID.randomUUID().toString())
                    .orderStatus("NEW")
                    .orderQty("1")
                    .tif(TIF.GTD.name())
                    .remainingQty("1")
                    .side("BUY")
                    .venueAccounts(List.of(venueAccountIds.get(random.nextInt(venueAccountIds.size()))))
                    .createdAt(OffsetDateTime.now())
                    .orderCategory(OrderCategory.SIMPLE.name())
                    .orderType(OrderType.MARKET.name())
                    .portfolioId(PORTFOLIO_ID_1)
                    .instrumentId(instrumentsId.get(random.nextInt(instrumentsId.size())))
                    .build())
                .toList();
        });

        OrderHistorySearchInput orderHistorySearch = new OrderHistorySearchInput(
            Set.of(new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_1),
                new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_2),
                new SimplePredicateInput(EQUAL, VENUE_ACCOUNT_ID, VENUE_ACCOUNT_3),
                new SimplePredicateInput(EQUAL, INSTRUMENT_ID, INSTRUMENT_ID_1),
                new SimplePredicateInput(EQUAL, INSTRUMENT_ID, INSTRUMENT_ID_2),
                new SimplePredicateInput(EQUAL, PORTFOLIO_ID, PORTFOLIO_ID_1)),
            Set.of(new CollectionPredicateInput(CollectionPredicateInput.PredicateType.IN, CollectionPredicateInput.Field.VENUE_ACCOUNT_ID, List.of(VENUE_ACCOUNT_1, VENUE_ACCOUNT_2)),
                new CollectionPredicateInput(CollectionPredicateInput.PredicateType.IN, CollectionPredicateInput.Field.INSTRUMENT_ID, List.of(INSTRUMENT_ID_1, INSTRUMENT_ID_3))),
            Set.of(),
            null,
            null,
            DESC,
            SortingField.CREATED_AT);

        // when
        List<OrderState> orderHistoryTest = orderHistoryService.getOrderStateSnapshots(orderHistorySearch);

        // then
        List<String> expectedVenueAccountIds = List.of(VENUE_ACCOUNT_1, VENUE_ACCOUNT_2, VENUE_ACCOUNT_3);
        List<String> expectedInstrumentIds = List.of(INSTRUMENT_ID_1, INSTRUMENT_ID_2, INSTRUMENT_ID_3);

        assertThat(orderHistoryTest).isNotEmpty();
        assertThat(orderHistoryTest.size()).isEqualTo(6);
        assertThat(orderHistoryTest.get(0).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(0).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(0).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(1).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(1).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(1).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(2).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(2).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(2).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(3).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(3).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(3).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(4).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(4).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(4).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
        assertThat(orderHistoryTest.get(5).getVenueAccountId()).isIn(expectedVenueAccountIds);
        assertThat(orderHistoryTest.get(5).getInstrumentId()).isIn(expectedInstrumentIds);
        assertThat(orderHistoryTest.get(5).getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
    }

    @Test
    void shouldUnifyDatesProperly() {
        // given
        final DateTimeFormatter ISO_MILLI_FORMATTER = new DateTimeFormatterBuilder().parseCaseInsensitive().appendInstant(3).toFormatter();
        final DateTimeFormatter ISO_MICRO_FORMATTER = new DateTimeFormatterBuilder().parseCaseInsensitive().appendInstant(6).toFormatter();

        ZonedDateTime testDateTime = ZonedDateTime.now().minusHours(2);
        DatePredicateInput datePredicateInput1 = new DatePredicateInput(FROM, CREATED_AT, toIsoUtcTime(testDateTime));
        DatePredicateInput datePredicateInput2 = new DatePredicateInput(FROM, CREATED_AT, String.valueOf(testDateTime.toInstant().toEpochMilli()));
        DatePredicateInput datePredicateInput3 = new DatePredicateInput(FROM, CREATED_AT, "TEST");

        // when
        DatePredicateInput unifiedDatePredicate1 = DbUtils.unifyDatesInDatePredicateInput(datePredicateInput1);
        DatePredicateInput unifiedDatePredicate2 = DbUtils.unifyDatesInDatePredicateInput(datePredicateInput2);
        DatePredicateInput unifiedDatePredicate3 = DbUtils.unifyDatesInDatePredicateInput(datePredicateInput3);

        // then
        assertThat(unifiedDatePredicate1.value()).isEqualTo(testDateTime.format(ISO_MICRO_FORMATTER));
        assertThat(unifiedDatePredicate2.value()).isEqualTo(testDateTime.format(ISO_MILLI_FORMATTER));
        assertThat(unifiedDatePredicate3.value()).isEqualTo(ZonedDateTime.of(1970, 1, 1, 0, 0, 0, 0, ZoneId.of("Z")).format(DateTimeFormatter.ISO_INSTANT));
    }
}