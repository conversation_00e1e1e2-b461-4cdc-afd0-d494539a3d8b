package io.wyden.orderhistory.service;

import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.SimplePredicateInput;
import io.wyden.orderhistory.model.SortingField;
import io.wyden.orderhistory.repository.OrderEventRepository;
import io.wyden.orderhistory.repository.OrderHistorySearchIndexRepository;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.reporting.OrderState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderStateQueryServiceTest {

    @Mock
    private OrderEventRepository orderEventRepository;

    @Mock
    private OrderHistorySearchIndexRepository searchIndexRepository;

    private OrderStateQueryService orderStateQueryService;

    @BeforeEach
    void setUp() {
        orderStateQueryService = new OrderStateQueryService(orderEventRepository, searchIndexRepository);
    }

    @Test
    void getOrderStateSnapshots_withEmptyResult_returnsEmptyList() {
        // Given
        OrderHistorySearchInput searchInput = createTestSearchInput();
        when(searchIndexRepository.findAll(any(Specification.class))).thenReturn(List.of());
        Slice<OrderEventEntity> emptyPage = new SliceImpl<>(List.of());
        when(orderEventRepository.findLatestEventsByOrderIds(anyList(), any(Pageable.class))).thenReturn(emptyPage);

        // When
        List<OrderState> result = orderStateQueryService.getOrderStateSnapshots(searchInput);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void getOrderStateSnapshotsPaged_withEmptyResult_returnsEmptyConnection() {
        // Given
        OrderHistorySearchInput searchInput = createTestSearchInput();

        when(searchIndexRepository.findAll(any(Specification.class))).thenReturn(List.of());

        // When
        CursorConnection result = orderStateQueryService.getOrderStateSnapshotsPaged(searchInput);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getEdgesList()).isEmpty();
        assertThat(result.getPageInfo().getHasNextPage()).isFalse();
    }

    @Test
    void getOrderStates_withEmptyResult_returnsEmptyList() {
        // Given
        OrderHistorySearchInput searchInput = createTestSearchInput();

        when(searchIndexRepository.findAll(any(Specification.class))).thenReturn(List.of());
        Page<OrderEventEntity> emptyPage = new PageImpl<>(List.of());
        when(orderEventRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(emptyPage);

        // When
        List<OrderState> result = orderStateQueryService.getOrderStates(searchInput);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void getOrderStatesPaged_withEmptyResult_returnsEmptyConnection() {
        // Given
        OrderHistorySearchInput searchInput = createTestSearchInput();

        when(searchIndexRepository.findAll(any(Specification.class))).thenReturn(List.of());

        // When
        CursorConnection result = orderStateQueryService.getOrderStatesPaged(searchInput);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getEdgesList()).isEmpty();
        assertThat(result.getPageInfo().getHasNextPage()).isFalse();
    }

    private OrderHistorySearchInput createTestSearchInput() {
        SimplePredicateInput portfolioIdPredicate = new SimplePredicateInput(
            SimplePredicateInput.PredicateType.EQUAL,
            SimplePredicateInput.Field.PORTFOLIO_ID,
            "test-portfolio-id"
        );

        return new OrderHistorySearchInput(
            List.of(portfolioIdPredicate),
            List.of(),
            List.of(),
            10,
            null,
            null,
            SortingField.CREATED_AT
        );
    }
}
