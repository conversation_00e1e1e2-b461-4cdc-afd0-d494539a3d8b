package io.wyden.smartrecommendationengine.exchangeotcmix;

import io.wyden.published.smartrecommendationengine.BestExecutionRequest;
import io.wyden.published.smartrecommendationengine.Strategy;
import io.wyden.smartrecommendationengine.assertions.RankedCandidatesAssertions;
import io.wyden.smartrecommendationengine.domain.RecommendationSubscription;
import io.wyden.smartrecommendationengine.service.SmartRecommendationEngine;
import io.wyden.smartrecommendationengine.service.audit.AuditService;
import io.wyden.smartrecommendationengine.service.connectorstate.ConnectorStateService;
import io.wyden.smartrecommendationengine.service.marketdata.MarketDataCache;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.util.List;

import static io.wyden.published.oems.OemsSide.BUY;
import static io.wyden.published.oems.OemsSide.SELL;
import static io.wyden.smartrecommendationengine.assertions.RankedCandidatesAssertions.assertThat;
import static java.util.Optional.empty;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class BestExecutionRequestWithStopMixTest extends BestExecutionRequestMixTestBase {
    @Mock
    private MarketDataCache marketDataCache;

    @Mock
    private ConnectorStateService connectorStateService;

    @Mock
    private SmartRecommendationEngine smartRecommendationEngine;

    @Mock
    private AuditService auditService;

    private RecommendationSubscription recommendationSubscription;

    private int stopOrderTriggerRequiredVenuesPercentage = 100;
    private int stopOrderTriggerRequiredVenuesHalfPercentage = 50;
    private final int maxMarketDataAge = 6000;

    public BestExecutionRequestWithStopMixTest() {
        marketDataCache = mock(MarketDataCache.class);
        connectorStateService = mock(ConnectorStateService.class);
        recommendationSubscription = new RecommendationSubscription("1", null, null, List.of(B2C2, BINANCE));
        auditService = mock(AuditService.class);
        doNothing().when(auditService).sendAuditEventWithOrderBooks(anyList(), anyString(), anyString());

        when(connectorStateService.isTradingAlive(anyString())).thenReturn(true);
    }

    @Test
    public void testBuyOrderBetterExchange() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("50")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5"), // 1 * 100 + 2 * 200 + 300 = 1100
            new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5") // 5 * 300 = 1500
        );
    }

    @Test
    public void testBuyOrderBetterOtc() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockWorstOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("50")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5"), // 5 * 300 = 1500
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5") // 1 * 400 + 2 * 500 + 2 * 600 = 2600
        );
    }

    @Test
    public void testSellOrderBetterExchange() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("1000")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5"), // 3 * 600 + 2 * 400 = 1600
            new RankedCandidatesAssertions.RankingResult(B2C2, "2000", "5") // 5 * 400 = 2000
        );
    }

    @Test
    public void testSellOrderBetterOtc() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockWorstOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("1000")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(B2C2, "2000", "5"), // 5 * 400 = 2000
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1300", "5") // 3 * 300 + 2 * 200 = 1300
        );
    }

    @Test
    public void testOBNotDeepEnoughForExchangeCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("20")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("1000")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance has volume of 6 - we take as much as we can
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(B2C2, "8000", "20"), // 20 * 400 = 8000
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2800", "6") // 3 + 600 + 2 * 400 + 200 = 2800
        );
    }

    @Test
    public void testOBNotDeepEnoughForOtcCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockNotDeepEnoughOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("1000")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // B2C2 has max volume of 2 - candidate excluded
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5") // 3 + 600 + 2 * 400 = 2600
        );
    }

    @Test
    public void testOBNotDeepEnoughForExchangeCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("20")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("50")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance has volume of 6 - we take as much as we can
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1400", "6"), // 1 * 100 + 2 * 200 + 3 * 300 = 1400
            new RankedCandidatesAssertions.RankingResult(B2C2, "6000", "20") // 20 * 300 = 6000
        );
    }

    @Test
    public void testOBNotDeepEnoughForOtcCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockNotDeepEnoughOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("50")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // B2C2 has max volume of 2 - candidate excluded
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5") // 1 * 100 + 2 * 200 + 2 * 300 = 1100
        );
    }

    @Test
    public void testOBNotDeepEnoughForBothCandidatesSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("40")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("1000")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance has volume of 6 - we take as much as we can
        // B2C2 has max volume of 2 - candidate excluded
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2800", "6") // 3 + 600 + 2 * 400 + 200 = 2800
        );
    }

    @Test
    public void testOBNotDeepEnoughForBothCandidatesBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("40")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("50")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance has volume of 6 - we take as much as we can
        // B2C2 has max volume of 2 - candidate excluded
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1400", "6") // 1 * 100 + 2 * 200 + 3 * 300 = 1400
        );
    }

    @Test
    public void testOBNotPresentForExchangeCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("50")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // B2C2 topAsk = 100 > stop
        // but Binance OB empty
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOBNotPresentForOtcCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("50")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance topAsk = 100 > stop
        // B2C2 OB empty
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOBNotPresentForExchangeCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("1000")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // B2C2 topBid = 800 < stop
        // but Binance OB empty
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOBNotPresentForOtcCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("1500")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance topBid = 600 < stop
        // but B2C2 OB empty
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOBNotPresentForBothCandidatesBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .setStop("1300")
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // both OBs empty
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOBNotPresentForBothCandidatesSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .setStop("1300")
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // both OBs empty
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testExchangeCandidateHittingStopSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockAboveStopOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("500")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // B2C2 topBid = 500 < stop
        // Binance topBid = 600 > stop
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOtcCandidateHittingStopSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("700")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance topBid = 600 < stop
        // B2C2 topBid = 800 > stop
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOtcCandidateHittingStopBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockWorstOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("150")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance topAsk = 200 > stop
        // B2C2 topAsk = 100 < stop
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testExchangeCandidateHittingStopBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockAboveStopOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("300")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance topAsk = 100 < stop
        // B2C2 topAsk = 400 > stop
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testBothCandidatesNotHittingStopSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("300")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // B2C2 topBid = 800 > stop
        // Binance topBid = 600 > stop
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testBothCandidatesNotHittingStopBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("1400")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // B2C2 topAsk = 100 < stop
        // Binance topAsk = 100 < stop
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOBTooOldForExchangeCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));
    
        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("50")
            .build();
    
        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // B2C2 topAsk = 100 > stop
        // but Binance too old OB
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOBTooOldForOtcCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("50")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance topAsk = 100 > stop
        // but B2C2 too old OB
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOBTooOldForExchangeCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("1000")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // B2C2 topBid = 800 < stop
        // but Binance too old OB
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOBTooOldForOtcCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .setStop("1000")
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance topBid = 600 < stop
        // but B2C2 too old OB
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOBTooOldForTwoCandidatesSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .setStop("1000")
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // both OBs too old
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Test
    public void testOBTooOldForTwoCandidatesBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .setStop("50")
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // both OBs too old
        // stopOrderTriggerRequiredVenuesPercentage = 100%
        assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
    }

    @Nested
    class HalfTriggerPercentageTests {
        @Test
        public void testBuyOrderBetterExchange() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("50")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5"), // 1 * 100 + 2 * 200 + 300 = 1100
                new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5") // 5 * 300 = 1500
            );
        }

        @Test
        public void testBuyOrderBetterOtc() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockWorstOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("50")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5"), // 5 * 300 = 1500
                new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5") // 1 * 400 + 2 * 500 + 2 * 600 = 2600
            );
        }

        @Test
        public void testSellOrderBetterExchange() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("1000")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5"), // 3 * 600 + 2 * 400 = 1600
                new RankedCandidatesAssertions.RankingResult(B2C2, "2000", "5") // 5 * 400 = 2000
            );
        }

        @Test
        public void testSellOrderBetterOtc() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockWorstOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("1000")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(B2C2, "2000", "5"), // 5 * 400 = 2000
                new RankedCandidatesAssertions.RankingResult(BINANCE, "1300", "5") // 3 * 300 + 2 * 200 = 1300
            );
        }

        @Test
        public void testOBNotDeepEnoughForExchangeCandidateSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("20")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("1000")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // Binance has volume of 6 - we take as much as we can
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(B2C2, "8000", "20"), // 20 * 400 = 8000
                new RankedCandidatesAssertions.RankingResult(BINANCE, "2800", "6") // 3 + 600 + 2 * 400 + 200 = 2800
            );
        }

        @Test
        public void testOBNotDeepEnoughForOtcCandidateSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockNotDeepEnoughOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("1000")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // B2C2 has max volume of 2 - candidate excluded
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5") // 3 + 600 + 2 * 400 = 2600
            );
        }

        @Test
        public void testOBNotDeepEnoughForExchangeCandidateBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("20")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("50")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // Binance has volume of 6 - we take as much as we can
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "1400", "6"), // 1 * 100 + 2 * 200 + 3 * 300 = 1400
                new RankedCandidatesAssertions.RankingResult(B2C2, "6000", "20") // 20 * 300 = 6000
            );
        }

        @Test
        public void testOBNotDeepEnoughForOtcCandidateBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockNotDeepEnoughOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("50")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // B2C2 has max volume of 2 - candidate excluded
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5") // 1 * 100 + 2 * 200 + 2 * 300 = 1100
            );
        }

        @Test
        public void testOBNotDeepEnoughForBothCandidatesSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("40")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("1000")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // Binance has volume of 6 - we take as much as we can
            // B2C2 has max volume of 2 - candidate excluded
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "2800", "6") // 3 + 600 + 2 * 400 + 200 = 2800
            );
        }

        @Test
        public void testOBNotDeepEnoughForBothCandidatesBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("40")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("50")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // Binance has volume of 6 - we take as much as we can
            // B2C2 has max volume of 2 - candidate excluded
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "1400", "6") // 1 * 100 + 2 * 200 + 3 * 300 = 1400
            );
        }

        @Test
        public void testOBNotPresentForExchangeCandidateBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("50")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // B2C2 topAsk = 100 > stop
            // Binance OB empty
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5") // 5 * 300 = 1500
            );
        }

        @Test
        public void testOBNotPresentForOtcCandidateBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("50")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // Binance topAsk = 100 > stop
            // B2C2 OB empty
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5") // 1 * 100 + 2 * 200 + 2 * 300 = 1100
            );
        }

        @Test
        public void testOBNotPresentForExchangeCandidateSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("1000")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // B2C2 topBid = 800 < stop
            // Binance OB empty
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(B2C2, "2000", "5") // 5 * 400 = 2000
            );

        }

        @Test
        public void testOBNotPresentForOtcCandidateSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("1500")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // Binance topBid = 600 < stop
            // but B2C2 OB empty
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5") // 3 * 600 + 2 * 400 = 12600
            );
        }

        @Test
        public void testOBNotPresentForBothCandidatesBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .setStop("1300")
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // both OBs empty
            assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
        }

        @Test
        public void testOBNotPresentForBothCandidatesSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("10")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .setStop("1300")
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // both OBs empty
            assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
        }

        @Test
        public void testExchangeCandidateHittingStopSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockAboveStopOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("500")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // B2C2 topBid = 400 < stop
            // Binance topBid = 600 > stop
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5"), // 3 * 600 + 2 * 400 = 2600
                new RankedCandidatesAssertions.RankingResult(B2C2, "1000", "5") // 5 * 200 = 1000
            );
        }

        @Test
        public void testOtcCandidateHittingStopSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("700")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // Binance topBid = 600 < stop
            // B2C2 topBid = 800 > stop
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5"), // 3 * 600 + 2 * 400 = 12600
                new RankedCandidatesAssertions.RankingResult(B2C2, "2000", "5") // 5 * 400 = 2000
            );
        }

        @Test
        public void testOtcCandidateHittingStopBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockWorstOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("150")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // Binance topAsk = 200 > stop
            // B2C2 topAsk = 100 < stop
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5"), // 5 * 300 = 1500
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5") // 1 * 400 + 2 * 500 + 2 * 600 = 2600
            );
        }

        @Test
        public void testExchangeCandidateHittingStopBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockAboveStopOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("300")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // Binance topAsk = 100 < stop
            // B2C2 topAsk = 400 > stop
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5"), // 1 * 100 + 2 * 200 + 2 * 300 = 1100
                new RankedCandidatesAssertions.RankingResult(B2C2, "2500", "5") // 5 * 500 = 2500
            );
        }

        @Test
        public void testBothCandidatesNotHittingStopSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("300")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // B2C2 topBid = 800 > stop
            // Binance topBid = 600 > stop
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
        }

        @Test
        public void testBothCandidatesNotHittingStopBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("1400")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // B2C2 topAsk = 100 < stop
            // Binance topAsk = 100 < stop
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
        }

        @Test
        public void testOBTooOldForExchangeCandidateBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("50")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // B2C2 topAsk = 100 > stop
            // Binance too old OB
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5") // 5 * 300 = 1500
            );
        }

        @Test
        public void testOBTooOldForOtcCandidateBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("50")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // Binance topAsk = 100 > stop
            // B2C2 too old OB
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5") // 1 * 100 + 2 * 200 + 2 * 300 = 1100
            );
        }

        @Test
        public void testOBTooOldForExchangeCandidateSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("1000")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // B2C2 topBid = 800 < stop
            // Binance too old OB
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(B2C2, "2000", "5") // 5 * 400 = 2000
            );
        }

        @Test
        public void testOBTooOldForOtcCandidateSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOtcOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("1000")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // Binance topBid = 600 < stop
            // B2C2 too old OB
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
                new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5") // 3 * 600 + 2 * 400 = 12600
            );
        }

        @Test
        public void testOBTooOldForTwoCandidatesSellOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("10")
                .setSide(SELL)
                .setStrategy(Strategy.SIMPLE)
                .setStop("1000")
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // both OBs too old
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
        }

        @Test
        public void testOBTooOldForTwoCandidatesBuyOrder() {
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOrderBook(B2C2));
            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("10")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .setStop("50")
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesHalfPercentage, maxMarketDataAge, auditService);

            // both OBs too old
            // stopOrderTriggerRequiredVenuesPercentage = 50%
            assertThat(smartRecommendationEngine.getRankedCandidates()).isNotDone();
        }
    }

    @Nested
    class ConnectorStateTests {
        @Test
        public void testExchangeAliveOtcNotAlive() {
            when(connectorStateService.isTradingAlive(BINANCE.getVenueAccount())).thenReturn(true);
            when(connectorStateService.isTradingAlive(B2C2.getVenueAccount())).thenReturn(false);

            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(BINANCE, B2C2))
                .setStop("100")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, 
                marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

            RankedCandidatesAssertions.assertThat(smartRecommendationEngine.getRankedCandidates())
                .isCompletedWithResults(
                    new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5")
                );
        }

        @Test
        public void testExchangeNotAliveOtcAlive() {
            when(connectorStateService.isTradingAlive(BINANCE.getVenueAccount())).thenReturn(false);
            when(connectorStateService.isTradingAlive(B2C2.getVenueAccount())).thenReturn(true);

            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(BINANCE, B2C2))
                .setStop("100")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, 
                marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

            RankedCandidatesAssertions.assertThat(smartRecommendationEngine.getRankedCandidates())
                .isCompletedWithResults(
                    new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5")
                );
        }

        @Test
        public void testAllVenuesNotTradingAlive() {
            when(connectorStateService.isTradingAlive(BINANCE.getVenueAccount())).thenReturn(false);
            when(connectorStateService.isTradingAlive(B2C2.getVenueAccount())).thenReturn(false);

            when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));
            when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));

            BestExecutionRequest request = BestExecutionRequest.newBuilder()
                .setQuantity("5")
                .setSide(BUY)
                .setStrategy(Strategy.SIMPLE)
                .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
                .setStop("100")
                .build();

            smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, 
                marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

            RankedCandidatesAssertions.assertThat(smartRecommendationEngine.getRankedCandidates())
                .isNotDone();
        }
    }
}
