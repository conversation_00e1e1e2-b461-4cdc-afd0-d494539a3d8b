package io.wyden.test.scenariorunner.trading;

import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientRequest;
import io.wyden.test.scenariorunner.data.trading.SimpleOrderFactory;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.extension.annotation.TestSetup;
import io.wyden.test.scenariorunner.extension.eachtest.ClientActorConnectorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClientActorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClientSessionExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ConnectorMockSessionExtension;
import io.wyden.test.scenariorunner.extension.eachtest.PortfolioExtension;
import io.wyden.test.scenariorunner.extension.eachtest.VenueAccountExtension;
import io.wyden.test.scenariorunner.integration.service.Service;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.extension.RegisterExtension;

import java.util.Map;

import static io.wyden.published.client.ClientExecType.CLIENT_EXEC_TYPE_NEW;

//TODO future mature trading on different predefined venue accounts e.g. Bitmex, Binance, etc.
@TestSetup
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.ORDER_COLLIDER,
    Service.ORDER_GATEWAY,
    Service.REFERENCE_DATA,
    Service.CONNECTOR_WRAPPER_MOCK,
    Service.RISK_ENGINE,
    Service.STORAGE
})
public abstract class TradingTestBase {

    protected static final double REQUESTED_QTY = 10000.0;

    @RegisterExtension
    @Order(0)
    protected ClientActorExtension clientActorExtension = new ClientActorExtension();
    @RegisterExtension
    @Order(1)
    protected ConnectorMockSessionExtension connectorSessionExtension = new ConnectorMockSessionExtension();
    @RegisterExtension
    @Order(2)
    VenueAccountExtension venueAccountExtension = new VenueAccountExtension(
        clientActorExtension.clientId(),
        Map.of(VenueAccountConstants.MOCK_VENUE, connectorSessionExtension.venueAccounts())
    );
    @RegisterExtension
    @Order(3)
    protected PortfolioExtension portfolioExtension = portfolioExtension();
    @RegisterExtension
    @Order(4)
    ClientActorConnectorExtension clientActorConnectorExtension = new ClientActorConnectorExtension(clientActorExtension.clientId(), true);
    @RegisterExtension
    @Order(5)
    protected ClientSessionExtension clientSessionExtension = new ClientSessionExtension(
        clientActorExtension.clientId(),
        connectorSessionExtension.venueAccountName(),
        portfolioExtension.portfolioId()
    );

    protected final SimpleOrderFactory orderFactory = new SimpleOrderFactory(
        clientActorExtension.clientId(),
        connectorSessionExtension.venueAccountName(),
        portfolioExtension.portfolioId()
    );
    protected String portfolio;

    @BeforeEach
    void setupPortfolio() {
        this.portfolio = portfolioExtension.portfolioId();
    }

    protected static ClientRequest createAcceptedOrderWithGivenOrderLimit(ClientSession client, ConnectorMockSession conn, double requestedQty) {
        client.sendDefaultStreetLimitOrder(requestedQty);
        conn.acceptNewOrder();
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);
        client.assertExecutionReportQuantities(requestedQty, 0.0, requestedQty, 0.0);
        return client.getOrder();
    }

    protected PortfolioExtension portfolioExtension() {
        return PortfolioExtension.ofClientId(clientActorExtension.clientId());
    }

}
