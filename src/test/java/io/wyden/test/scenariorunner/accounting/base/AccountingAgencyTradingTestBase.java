package io.wyden.test.scenariorunner.accounting.base;

import io.qameta.allure.Epic;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel.CurrencyType;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel.ExecutionMode;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel.TradingMode;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioTypeDto;
import io.wyden.apiserver.rest.security.accessgateway.AddOrRemoveUserPermissionsRequestDto;
import io.wyden.apiserver.rest.security.model.AuthorityDto;
import io.wyden.apiserver.rest.security.model.Resource;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.test.scenariorunner.data.refdata.PortfolioFactory;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants;
import io.wyden.test.scenariorunner.data.trading.SimpleOrderFactory;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.extension.annotation.TestSetup;
import io.wyden.test.scenariorunner.extension.eachtest.BrokerDeskInstrumentCreationExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClientActorConnectorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClientActorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClientSessionExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ConnectorMockSessionExtension;
import io.wyden.test.scenariorunner.extension.eachtest.PortfolioExtension;
import io.wyden.test.scenariorunner.extension.eachtest.VenueAccountExtension;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.integration.service.Service;
import io.wyden.test.scenariorunner.model.brokerdesk.ExecutionConfigurationInputBuilder;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.extension.RegisterExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static io.wyden.test.scenariorunner.data.refdata.VenueAccountFactory.randomVenueAccountName;
import static io.wyden.test.scenariorunner.data.infra.Epics.ACCOUNTING;
import static io.wyden.test.scenariorunner.tool.TestGarbageRemover.e2ePrefix;

@Epic(ACCOUNTING)
@TestSetup
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.ORDER_COLLIDER,
    Service.ORDER_GATEWAY,
    Service.REFERENCE_DATA,
    Service.BOOKING_ENGINE,
    Service.RATE_SERVICE,
    Service.CONNECTOR_WRAPPER_MOCK,
    Service.RISK_ENGINE,
    Service.STORAGE,
    Service.BROKER_CONFIG_SERVICE,
    Service.AGENCY_TRADING_SERVICE,
    Service.PRICING_SERVICE
})
public abstract class AccountingAgencyTradingTestBase {

    protected static final String CLIENT_VENUE = e2ePrefix("client_venue");
    protected static final String STREET_VENUE = VenueAccountConstants.MOCK_VENUE;

    protected final String CLIENT_VENUE_ACCOUNT = randomVenueAccountName("bank-account");
    protected final String STREET_VENUE_ACCOUNT = randomVenueAccountName("pric-src-acc");

    protected static final BigDecimal INITIAL_MARKET_PRICE = BigDecimal.valueOf(0.05);
    protected static final BigDecimal AGENCY_FIXED_FEE = BigDecimal.valueOf(3.5);

    // actors
    @RegisterExtension
    @Order(0)
    protected ClientActorExtension clientActorExtension = new ClientActorExtension();
    @RegisterExtension
    @Order(1)
    protected ClientActorExtension bankActorExtension = new ClientActorExtension();

    // retail client config
    @RegisterExtension
    @Order(2)
    protected PortfolioExtension clientPortfolioExtension = PortfolioExtension.ofClientIdAndPortfolioType(clientActorExtension.clientId(), PortfolioTypeDto.VOSTRO);
    @RegisterExtension
    @Order(3)
    VenueAccountExtension clientAccountExtension = new VenueAccountExtension(
        clientActorExtension.clientId(),
        Map.of(CLIENT_VENUE, List.of(CLIENT_VENUE_ACCOUNT))
    );
    @RegisterExtension
    @Order(4)
    ClientActorConnectorExtension clientActorConnectorExtension = new ClientActorConnectorExtension(clientActorExtension.clientId(), true);

    // bank config
    @RegisterExtension
    @Order(5)
    protected PortfolioExtension bankPortfolioExtension = PortfolioExtension.ofClientIdAndPortfolioType(bankActorExtension.clientId(), PortfolioTypeDto.NOSTRO);
    @RegisterExtension
    @Order(6)
    VenueAccountExtension streetAccountExtension = new VenueAccountExtension(
        bankActorExtension.clientId(),
        Map.of(STREET_VENUE, List.of(STREET_VENUE_ACCOUNT))
    );
    @RegisterExtension
    @Order(7)
    protected ConnectorMockSessionExtension streetConnectorSessionExtension = new ConnectorMockSessionExtension(STREET_VENUE_ACCOUNT);

    @RegisterExtension
    @Order(8)
    ClientSessionExtension clientSessionExtension = new ClientSessionExtension(
        clientActorExtension.clientId(),
        CLIENT_VENUE_ACCOUNT,
        clientPortfolioExtension.portfolioId()
    );

    @RegisterExtension
    @Order(9)
    BrokerDeskInstrumentCreationExtension instrumentCreationExtension = new BrokerDeskInstrumentCreationExtension(
        clientActorExtension.clientId(), bankActorExtension.clientId(), true, CLIENT_VENUE, STREET_VENUE
    );

    protected final SimpleOrderFactory clientOrderFactory = new SimpleOrderFactory(clientActorExtension.clientId(), CLIENT_VENUE_ACCOUNT, clientPortfolioExtension.portfolioId());

    protected GraphQLActor clientGqlActor;
    protected GraphQLActor bankGqlActor;

    protected ClientActor clientActor;
    protected ClientActor bankActor;

    protected String clientPortfolio;
    protected String bankPortfolio;

    @BeforeEach
    void setup(ConnectorMockSession streetConn, InstrumentResponseDto streetInstrument) {
        this.clientGqlActor = clientActorExtension.configGqlActor();
        this.bankGqlActor = bankActorExtension.configGqlActor();

        this.clientActor = clientActorExtension.actor();
        this.bankActor = bankActorExtension.actor();

        this.clientPortfolio = clientPortfolioExtension.portfolioId();
        this.bankPortfolio = bankPortfolioExtension.portfolioId();
        clientGqlActor.permission().addUserPermissions(new AddOrRemoveUserPermissionsRequestDto(
            bankActor.getClientId(), Set.of(new AuthorityDto(Resource.PORTFOLIO, clientPortfolio, Scope.MANAGE))));

        bankGqlActor.brokerDesk().updatePortfolioExecutionConfiguration(clientPortfolio, new ExecutionConfigurationInputBuilder()
            .setFixedFee(AGENCY_FIXED_FEE)
            .setFixedFeeCurrency(PortfolioFactory.DEFAULT_BOOKING_CURRENCY)
            .setTradingMode(TradingMode.AGENCY)
            .setCounterPortfolioId(bankPortfolio)
            .setPercentageFee(null)
            .setPercentageFeeCurrency(null)
            .setPercentageFeeCurrencyType(CurrencyType.BASE_CURRENCY)
            .setMinFee(null)
            .setMinFeeCurrency(null)
            .setAgencyTradingAccount(STREET_VENUE_ACCOUNT)
            .setAgencyTargetInstrumentId(streetInstrument.instrumentIdentifiers().instrumentId())
            .setChargeExchangeFee(true)
            .setDiscloseTradingVenue(false)
            .setExecutionMode(ExecutionMode.SIMPLE)
            .build());
        bankGqlActor.brokerDesk()
            .updatePortfolioPricingConfiguration(clientPortfolio, new BrokerDeskConfigModel.PricingConfigurationInput(List.of(STREET_VENUE_ACCOUNT), BigDecimal.ZERO));

        streetConn.sendBidAskQuote(streetInstrument.instrumentIdentifiers().adapterTicker(), INITIAL_MARKET_PRICE);
    }

}
