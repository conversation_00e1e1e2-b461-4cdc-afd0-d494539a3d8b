package io.wyden.test.scenariorunner.accounting;

import io.qameta.allure.Step;
import io.wyden.apiserver.rest.booking.BookingModel.PositionResponse;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.cloudutils.tools.MathUtils;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientSide;
import io.wyden.test.scenariorunner.accounting.base.AccountingAgencyTradingTestBase;
import io.wyden.test.scenariorunner.assertion.accounting.PositionSoftAssert;
import io.wyden.test.scenariorunner.config.Timeouts;
import io.wyden.test.scenariorunner.data.trading.ClientResponseFeeExtractor;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.integration.gqlclient.searchinput.PositionSearchInputs;
import io.wyden.test.scenariorunner.integration.service.RateServiceClient;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Collection;

import static io.wyden.published.client.ClientSide.BUY;
import static io.wyden.published.client.ClientSide.SELL;
import static io.wyden.test.scenariorunner.config.Timeouts.WAIT_FOR_CONDITION_S;
import static io.wyden.test.scenariorunner.util.WaitUtils.justWait;

@Fix
public class PositionClientCashTradeTest extends AccountingAgencyTradingTestBase {

    private static final int POSITIONS_PER_PORTFOLIO = 2;

    private String baseCurrency;
    private String quoteCurrency;
    private final RateServiceClient rateServiceClient = new RateServiceClient();

    @BeforeEach
    void setupCurrencies(InstrumentResponseDto clientInstrument) {
        baseCurrency = clientInstrument.forexSpotProperties().baseCurrency();
        quoteCurrency = clientInstrument.baseInstrument().quoteCurrency();
    }

    @Test
    void whenClientTraderBuyAndSellInstrumentQuotedInCashCurrency_thenCorrespondingPositionsAreCreated(ClientSession clientSession, ConnectorMockSession streetConn, InstrumentResponseDto clientInstrument) {

        // TODO we need to be able to configure price here with connector wrapper mock
        // so that prices will be same for ExecutionEngine and BookingEngine
        rateServiceClient.updatePrice(baseCurrency, quoteCurrency, INITIAL_MARKET_PRICE);

        BigDecimal quantity = BigDecimal.valueOf(10);
        ClientResponse buyFilled = tradeInstrument(BUY, quantity, INITIAL_MARKET_PRICE, clientInstrument.instrumentIdentifiers().instrumentId(), clientPortfolio, clientSession, streetConn);

        waitUntilPortfolioPositionsAreReturned(clientGqlActor, clientPortfolio);

        Collection<PositionResponse> clientPositionsPage = getClientPositionsPage(clientGqlActor);

        PositionResponse clientBaseCurrencyPosition = PositionStreetCashTradeTest.getPosition(clientPositionsPage, baseCurrency);
        verifyClientBaseCurrencyPosition(buyFilled, clientBaseCurrencyPosition);

        PositionResponse clientQuoteCurrencyPosition = PositionStreetCashTradeTest.getPosition(clientPositionsPage, quoteCurrency);
        verifyClientQuoteCurrencyPosition(buyFilled, clientQuoteCurrencyPosition);

        waitUntilPortfolioPositionsAreReturned(bankGqlActor, bankPortfolio);

        Collection<PositionResponse> bankPositionsPage = getBankPositionsPage(bankGqlActor);

        PositionResponse bankBaseCurrencyPosition = PositionStreetCashTradeTest.getPosition(bankPositionsPage, baseCurrency);
        verifyBankBaseCurrencyPosition(buyFilled, bankBaseCurrencyPosition);

        PositionResponse bankQuoteCurrencyPosition = PositionStreetCashTradeTest.getPosition(bankPositionsPage, quoteCurrency);
        verifyBankQuoteCurrencyPosition(buyFilled, bankQuoteCurrencyPosition);

        BigDecimal updatedPrice = BigDecimal.valueOf(0.08);
        streetConn.sendBidAskQuote(ClientSession.HEDGING_TICKER, updatedPrice);
        rateServiceClient.updatePrice(baseCurrency, quoteCurrency, updatedPrice);

        justWait(WAIT_FOR_CONDITION_S);

        // sell instrument fully
        ClientResponse sellFilled = tradeInstrument(SELL, quantity, updatedPrice, clientInstrument.instrumentIdentifiers().instrumentId(), clientPortfolio, clientSession, streetConn);

        waitUntilClientBasePositionUpdated(clientGqlActor, baseCurrency, BigDecimal.ZERO);

        Collection<PositionResponse> updatedClientPositionsPage = getClientPositionsPage(clientGqlActor);

        PositionResponse updatedClientBaseCurrencyPosition = PositionStreetCashTradeTest.getPosition(updatedClientPositionsPage, baseCurrency);
        verifyUpdatedBaseCurrencyPosition(sellFilled, updatedClientBaseCurrencyPosition);

        PositionResponse updatedClientQuoteCurrencyPosition = PositionStreetCashTradeTest.getPosition(updatedClientPositionsPage, quoteCurrency);
        verifyUpdatedQuoteCurrencyPosition(sellFilled, updatedClientQuoteCurrencyPosition);

        Collection<PositionResponse> updatedBankPositionsPage = getBankPositionsPage(bankGqlActor);

        PositionResponse updatedBankBaseCurrencyPosition = PositionStreetCashTradeTest.getPosition(updatedBankPositionsPage, baseCurrency);
        verifyUpdatedBankBaseCurrencyPosition(sellFilled, updatedBankBaseCurrencyPosition);

        PositionResponse updatedBankQuoteCurrencyPosition = PositionStreetCashTradeTest.getPosition(updatedBankPositionsPage, quoteCurrency);
        verifyUpdatedBankQuoteCurrencyPosition(sellFilled, updatedBankQuoteCurrencyPosition);
    }

    private void verifyClientBaseCurrencyPosition(ClientResponse buyFill, PositionResponse baseCurrencyPosition) {

        BigDecimal buyFee = ClientResponseFeeExtractor.getFeeAmountSum(buyFill); // 3.5
        BigDecimal quantity = new BigDecimal(buyFill.getLastQty()); // 10

        BigDecimal grossCost = quantity.multiply(INITIAL_MARKET_PRICE); // 10 * 0.05 = 0.5
        BigDecimal netCost = grossCost.add(buyFee);  // 0.5 + 3.5 = 4
        BigDecimal netAveragePrice = MathUtils.divide(netCost, quantity); // 4 / 10 = 0.4

        PositionSoftAssert.assertThat(baseCurrencyPosition)
            .symbolIs(baseCurrency)
            .bookingCurrencyIs(quoteCurrency)
            .quantityIs(quantity)
            .netCostIs(netCost)
            .grossCostIs(grossCost)
            .marketValueIs(grossCost)
            .netUnrealizedPnlIs(buyFee.negate()) // no paper profit yet, price should change
            .netAveragePriceIs(netAveragePrice)
            .grossAveragePriceIs(INITIAL_MARKET_PRICE) // 0.05
            .assertAll();
    }

    private void verifyClientQuoteCurrencyPosition(ClientResponse buyFill, PositionResponse quoteCurrencyPosition) {

        BigDecimal buyFee = ClientResponseFeeExtractor.getFeeAmountSum(buyFill);
        BigDecimal baseQuantity = new BigDecimal(buyFill.getLastQty());

        BigDecimal quoteQuantity = baseQuantity.multiply(INITIAL_MARKET_PRICE).add(buyFee).negate(); // -((10 * 0.05) + 3.5) = -4

        PositionSoftAssert.assertThat(quoteCurrencyPosition)
            .symbolIs(quoteCurrency)
            .bookingCurrencyIs(quoteCurrency)
            .quantityIs(quoteQuantity)
            .netCostIs(quoteQuantity)
            .marketValueIs(quoteQuantity)
            .netUnrealizedPnlIs(BigDecimal.ZERO) // no paper profit on quote currency
            .netRealizedPnlIs(BigDecimal.ZERO) // no realized profit
            .netAveragePriceIs(BigDecimal.ONE) // USD to USD 1 to 1
            .grossAveragePriceIs(BigDecimal.ONE)
            .assertAll();
    }

    private void verifyBankBaseCurrencyPosition(ClientResponse buyFill, PositionResponse bankBaseCurrencyPosition) {

        BigDecimal expectedQuantity = BigDecimal.ZERO; // in agency mode instrument is traded vs street so bank receive only fees

        PositionSoftAssert.assertThat(bankBaseCurrencyPosition)
            .symbolIs(baseCurrency)
            .bookingCurrencyIs(quoteCurrency)
            .quantityIs(expectedQuantity)
            .netCostIs(expectedQuantity)
            .grossCostIs(expectedQuantity)
            .marketValueIs(expectedQuantity)
            .netUnrealizedPnlIs(BigDecimal.ZERO) // no paper profit, because no price change
            .netAveragePriceIs(expectedQuantity)
            .grossAveragePriceIs(expectedQuantity)
            .assertAll();
    }

    private void verifyBankQuoteCurrencyPosition(ClientResponse buyFill, PositionResponse bankQuoteCurrencyPosition) {

        BigDecimal buyFee = ClientResponseFeeExtractor.getFeeAmountSum(buyFill);

        PositionSoftAssert.assertThat(bankQuoteCurrencyPosition)
            .symbolIs(quoteCurrency)
            .bookingCurrencyIs(quoteCurrency)
            .quantityIs(buyFee) // only fees
            .netCostIs(buyFee)
            .marketValueIs(buyFee)
            .netUnrealizedPnlIs(BigDecimal.ZERO) // no unrealized profit, because no price change
            .netRealizedPnlIs(BigDecimal.ZERO) // no realized profit yet
            .netAveragePriceIs(BigDecimal.ONE) // USD to USD 1 to 1
            .grossAveragePriceIs(BigDecimal.ONE)
            .assertAll();
    }

    private void verifyUpdatedBaseCurrencyPosition(ClientResponse sellFill, PositionResponse secondBaseCurrencyPosition) {

        BigDecimal sellFee = ClientResponseFeeExtractor.getFeeAmountSum(sellFill);
        BigDecimal allFees = sellFee.multiply(BigDecimal.valueOf(2)); // fixed fee buy + fixed fee sell

        BigDecimal quantity = new BigDecimal(sellFill.getLastQty());
        BigDecimal price = new BigDecimal(sellFill.getLastPrice());

        BigDecimal netRealizedPnlWithoutFees = quantity.multiply(price.subtract(INITIAL_MARKET_PRICE)); // 10 * (0.08-0.05) = 0.3

        BigDecimal netRealizedPnlWithFees = netRealizedPnlWithoutFees.subtract(allFees); // realized profit is 0.3 - 7 = -6.7

        PositionSoftAssert.assertThat(secondBaseCurrencyPosition)
            .symbolIs(baseCurrency)
            .bookingCurrencyIs(quoteCurrency)
            .quantityIs(BigDecimal.ZERO) // instrument was sold fully
            .netCostIs(BigDecimal.ZERO)
            .grossCostIs(BigDecimal.ZERO)
            .marketValueIs(BigDecimal.ZERO)
            .netRealizedPnlIs(netRealizedPnlWithFees)
            .netUnrealizedPnlIs(BigDecimal.ZERO)
            .netAveragePriceIs(BigDecimal.ZERO)
            .grossAveragePriceIs(BigDecimal.ZERO)
            .assertAll();
    }

    private void waitUntilClientBasePositionUpdated(GraphQLActor actor, String baseCurrency, BigDecimal baseQuantity) {
        WaitUtils.waitUntil("positions are updated",
            () -> getClientPositionsPage(actor)
                .stream()
                .filter(position -> position.symbol().equals(baseCurrency))
                .anyMatch(position -> position.quantity().compareTo(baseQuantity) == 0),
            Timeouts.WAIT_FOR_CONDITION_M);
    }

    private void verifyUpdatedQuoteCurrencyPosition(ClientResponse sellFill, PositionResponse updatedQuoteCurrencyPosition) {

        BigDecimal quantity = new BigDecimal(sellFill.getLastQty());
        BigDecimal updatedPrice = new BigDecimal(sellFill.getLastPrice());

        BigDecimal sellFee = ClientResponseFeeExtractor.getFeeAmountSum(sellFill);
        BigDecimal allFees = sellFee.multiply(BigDecimal.valueOf(2));

        BigDecimal spentQuote = quantity.multiply(INITIAL_MARKET_PRICE).negate();
        BigDecimal gainedQuote = quantity.multiply(updatedPrice);
        BigDecimal netCost = spentQuote.add(gainedQuote).subtract(allFees);

        PositionSoftAssert.assertThat(updatedQuoteCurrencyPosition)
            .symbolIs(quoteCurrency)
            .bookingCurrencyIs(quoteCurrency)
            .quantityIs(netCost)
            .netCostIs(netCost)
            .marketValueIs(netCost)
            .netUnrealizedPnlIs(BigDecimal.ZERO)
            .netRealizedPnlIs(BigDecimal.ZERO)
            .netAveragePriceIs(BigDecimal.ONE)
            .grossAveragePriceIs(BigDecimal.ONE)
            .assertAll();
    }

    private void verifyUpdatedBankBaseCurrencyPosition(ClientResponse sellFill, PositionResponse updatedBankBaseCurrencyPosition) {

        BigDecimal sellFee = ClientResponseFeeExtractor.getFeeAmountSum(sellFill);
        BigDecimal allFees = sellFee.multiply(BigDecimal.valueOf(2));

        PositionSoftAssert.assertThat(updatedBankBaseCurrencyPosition)
            .symbolIs(baseCurrency)
            .bookingCurrencyIs(quoteCurrency)
            .quantityIs(BigDecimal.ZERO)
            .netCostIs(BigDecimal.ZERO)
            .marketValueIs(BigDecimal.ZERO)
            .netUnrealizedPnlIs(BigDecimal.ZERO)
            .netRealizedPnlIs(allFees)
            .netAveragePriceIs(BigDecimal.ZERO)
            .grossAveragePriceIs(BigDecimal.ZERO)
            .assertAll();
    }

    private void verifyUpdatedBankQuoteCurrencyPosition(ClientResponse sellFill, PositionResponse updatedBankQuoteCurrencyPosition) {

        BigDecimal sellFee = ClientResponseFeeExtractor.getFeeAmountSum(sellFill);
        BigDecimal allFees = sellFee.multiply(BigDecimal.valueOf(2));

        PositionSoftAssert.assertThat(updatedBankQuoteCurrencyPosition)
            .symbolIs(quoteCurrency)
            .bookingCurrencyIs(quoteCurrency)
            .quantityIs(allFees)
            .netCostIs(allFees)
            .marketValueIs(allFees)
            .netUnrealizedPnlIs(BigDecimal.ZERO)
            .netRealizedPnlIs(BigDecimal.ZERO)
            .netAveragePriceIs(BigDecimal.ONE)
            .grossAveragePriceIs(BigDecimal.ONE)
            .assertAll();
    }

    private static Collection<PositionResponse> getPositionsPage(GraphQLActor actor, String portfolio) {
        return actor.booking().positionPage(PositionSearchInputs.byPortfolio(portfolio)).getAllNodes();
    }

    private Collection<PositionResponse> getClientPositionsPage(GraphQLActor actor) {
        return getPositionsPage(actor, clientPortfolio);
    }

    private Collection<PositionResponse> getBankPositionsPage(GraphQLActor actor) {
        return getPositionsPage(actor, bankPortfolio);
    }

    private void waitUntilPortfolioPositionsAreReturned(GraphQLActor clientActor, String portfolio) {
        WaitUtils.waitUntil("%s positions for portfolio [%s] are returned".formatted(POSITIONS_PER_PORTFOLIO, portfolio),
            () -> {
                Collection<PositionResponse> positions = getPositionsPage(clientActor, portfolio);
                boolean containsPositionForEachPortfolio = positions.size() >= POSITIONS_PER_PORTFOLIO;
                boolean positionsAreUpdated = positions.stream().anyMatch(position -> position.quantity().compareTo(BigDecimal.ZERO) != 0);
                return containsPositionForEachPortfolio && positionsAreUpdated;
            },
            Timeouts.WAIT_FOR_CONDITION_M);
    }

    @Step
    private static ClientResponse tradeInstrument(ClientSide side, BigDecimal quantity, BigDecimal price, String instrumentId, String portfolioId, ClientSession client, ConnectorMockSession streetConn) {
        double quantityDouble = quantity.doubleValue();
        double priceDouble = price.doubleValue();
        client.sendMarketOrder(side, quantityDouble, instrumentId, portfolioId);
        streetConn.acceptNewOrder();
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);
        streetConn.fillFull(quantityDouble, priceDouble);
        ClientResponse fill = client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_FILL, ClientOrderStatus.FILLED);
        return fill;
    }

}
