package io.wyden.test.scenariorunner.connectors.bitmex;

import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.session.ClientSession;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

abstract class BitmexConnectorTest extends BitmexTradingTest {

    private static final String INSTRUMENT_ID = "APEUSDT@FOREX@BitMEX";

    @Test
    void whenCancelRequestIsIssuedToAZeroFilledOrderItIsCancelled(ClientSession client) {
        // given - order is placed on BitMEX
        double quantity = 1;
        client.sendOrder(orderFactory.defaultLimitOrder(INSTRUMENT_ID, ClientSide.BUY, quantity, "0.01", ClientTIF.GTC));
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);
        assertThat(client.getExecutionReport().getClOrderId()).isEqualTo(client.getClientOrderId());

        // when cancel is sent
        client.sendCancel();

        // then PENDING_CANCEL is emitted
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_CANCEL, ClientOrderStatus.PENDING_CANCEL);
        client.assertExecutionReportQuantities(quantity, 0.0, quantity, 0.0);
        assertThat(client.getExecutionReport().getClOrderId()).isEqualTo(client.getCancelClientOrderId());

        // then CANCELLED is emitted
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_CANCELED, ClientOrderStatus.CANCELED);
        assertThat(client.getExecutionReport().getClOrderId()).isEqualTo(client.getCancelClientOrderId());
    }

}

@Fix
@Disabled("Bitmex is not used by any clients right now and testnet itself is unstable")
class FixProtocolBitmexConnectorTest extends BitmexConnectorTest {

}
