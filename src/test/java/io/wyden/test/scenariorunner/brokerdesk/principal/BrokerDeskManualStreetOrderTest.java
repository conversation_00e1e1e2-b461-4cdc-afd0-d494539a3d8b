package io.wyden.test.scenariorunner.brokerdesk.principal;

import io.qameta.allure.Epic;
import io.wyden.published.client.ClientSide;
import io.wyden.test.scenariorunner.accounting.base.StreetAccountingBase;
import io.wyden.test.scenariorunner.config.Timeouts;
import io.wyden.test.scenariorunner.data.refdata.InstrumentId;
import io.wyden.test.scenariorunner.data.source.BuySellSource;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.annotation.Rest;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.params.ParameterizedTest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.concurrent.Callable;

import static io.wyden.cloud.utils.test.TestUtils.bd;
import static io.wyden.published.client.ClientSide.SELL;
import static io.wyden.test.scenariorunner.data.infra.Epics.BROKER_DESK_PRINCIPAL;
import static org.awaitility.Awaitility.await;

@Epic(BROKER_DESK_PRINCIPAL)
public abstract class BrokerDeskManualStreetOrderTest extends StreetAccountingBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(BrokerDeskManualStreetOrderTest.class);

    private static final String INSTRUMENT = InstrumentId.DOGE_USD_FOREX_WYDENMOCK.getName();

//    private final ExecutionEngineClient executionEngineClient = new ExecutionEngineClient();
//
//    @RegisterExtension
//    @Order(1)
//    BrokerDeskConfigExtension brokerDeskConfigExtension = new BrokerDeskConfigExtension(
//        InstrumentId.DOGE_USD_FOREX_BANK, InstrumentId.DOGE_USD_FOREX_WYDENMOCK
//    );
//    @RegisterExtension
//    @Order(2)
//    ExecutionEngineResetExtension executionEngineResetExtension = new ExecutionEngineResetExtension(
//        InstrumentId.DOGE_USD_FOREX_BANK
//    );

    @ParameterizedTest(name = "engineIsAwareOfExposureTriggeredByManualStreetSideOrder {0}")
    @BuySellSource
    void engineIsAwareOfExposureTriggeredByManualStreetSideOrder(ClientSide side, ClientSession bank, ConnectorMockSession bankConn) throws IOException {
        // Place manual street order
        bank.sendMarketOrder(side, 10_000, INSTRUMENT, portfolio);
        // Receive order on connector
        bankConn.acceptNewOrder();
        // Execute order partially
        bankConn.fillPart(5_000, 0.05);
        // Wait for balance update
        await()
            .pollInterval(Timeouts.POLL_INTERVAL)
            .atMost(Timeouts.WAIT_FOR_CONDITION)
            .until(isExpectedBaseCurrencyExposure(5_000, side));
        // Execute order
        bankConn.fillFull(10_000, 5_000, 0.05, 0.05);
        // Wait for balance update
        await()
            .pollInterval(Timeouts.POLL_INTERVAL)
            .atMost(Timeouts.WAIT_FOR_CONDITION)
            .until(isExpectedBaseCurrencyExposure(10_000, side));
    }

    private Callable<Boolean> isExpectedBaseCurrencyExposure(double exposure, ClientSide side) {
        BigDecimal expectedExposure = SELL == side ? bd(String.valueOf(exposure)).negate() : bd(String.valueOf(exposure));
        return () -> {
            BigDecimal actualExposure = BigDecimal.ZERO;
//                executionEngineClient.getCurrentBaseCurrencyExposure(ClientSession.CLIENT_INSTRUMENT_ID);
            LOGGER.info("Expected base currency exposure: {}", exposure);
            LOGGER.info("Actual base currency exposure: {}", actualExposure);
            return actualExposure.compareTo(expectedExposure) == 0;
        };
    }
}

@Disabled("Execution-engine is decommissioned")
@Fix
class FixBrokerDeskManualStreetOrderTest extends BrokerDeskManualStreetOrderTest {

}

@Disabled("Execution-engine is decommissioned")
@Rest
class RestBrokerDeskManualStreetOrderTest extends BrokerDeskManualStreetOrderTest {

}

@Disabled("Execution-engine is decommissioned")
@GraphQL
class GraphQLBrokerDeskManualStreetOrderTest extends BrokerDeskManualStreetOrderTest {

}
