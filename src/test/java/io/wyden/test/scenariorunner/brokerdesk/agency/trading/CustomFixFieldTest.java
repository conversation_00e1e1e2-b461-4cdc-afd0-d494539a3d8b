package io.wyden.test.scenariorunner.brokerdesk.agency.trading;

import io.qameta.allure.Step;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.test.scenariorunner.assertion.trading.ClientResponseSoftAssert;
import io.wyden.test.scenariorunner.brokerdesk.agency.AgencyTradingTestBase;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.integration.gqlclient.searchinput.TransactionSearchInputs;
import io.wyden.test.scenariorunner.model.booking.trade.StreetCashTrade;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.NoSuchElementException;

import static io.wyden.published.client.ClientExecType.CLIENT_EXEC_TYPE_FILL;
import static io.wyden.published.client.ClientExecType.CLIENT_EXEC_TYPE_NEW;
import static io.wyden.published.client.ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL;
import static io.wyden.rest.management.domain.TransactionModel.TransactionType.STREET_CASH_TRADE;
import static io.wyden.test.scenariorunner.data.infra.TestTags.BPCE;

public class CustomFixFieldTest {

    @Nested
    @Fix
    @Tag(BPCE)
    class RootExecutionIdTest extends AgencyTradingTestBase {

        @Test
        void rootExecutionId_whenOrderFilledPartiallyAndThenFully(ConnectorMockSession conn, InstrumentResponseDto clientInstrument, ClientSession client) {
            // when
            double orderQuantity = 1.0;
            client.sendOrder(clientOrderFactory.defaultOrder(ClientOrderType.LIMIT, clientInstrument.instrumentIdentifiers().instrumentId(), orderQuantity));
            conn.acceptNewOrder();

            // then
            client.receiveExecutionReport();
            client.assertExecutionReportStatus(CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

            // when
            conn.fillPart("execution-1", 0.5, 1000);

            // then
            client.receiveExecutionReport();
            client.assertExecutionReportStatus(CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);

            String partFillRootExecutionId = getRootExecutionIdFromLatestStreetTransaction();
            ClientResponseSoftAssert.assertThat(client.getExecutionReport())
                .rootExecutionIdIs(partFillRootExecutionId)
                .assertAll();

            // when
            conn.fillFull("execution-2", 1, 1000);

            // then
            client.receiveExecutionReport();
            client.assertExecutionReportStatus(CLIENT_EXEC_TYPE_FILL, ClientOrderStatus.FILLED);

            String fullFillRootExecutionId = getRootExecutionIdFromLatestStreetTransaction();
            ClientResponseSoftAssert.assertThat(client.getExecutionReport())
                .rootExecutionIdIs(fullFillRootExecutionId)
                .assertAll();
        }

        @Step
        private String getRootExecutionIdFromLatestStreetTransaction() {
            WaitUtils.waitUntil("transaction of portfolio=%s returned".formatted(bankPortfolio),
                () -> !bankActorExtension.configGqlActor().booking()
                    .transactionPage(TransactionSearchInputs.byPortfoliosAndTransactionTypes(List.of(bankPortfolio), List.of(STREET_CASH_TRADE.name())))
                    .getAllNodes()
                    .isEmpty());
            StreetCashTrade streetCashTrade = (StreetCashTrade) bankActorExtension.configGqlActor().booking()
                .transactionPage(TransactionSearchInputs.byPortfoliosAndTransactionTypes(List.of(bankPortfolio), List.of(STREET_CASH_TRADE.name())))
                .getAllNodes()
                .stream()
                .findFirst()
                .orElseThrow(() -> new NoSuchElementException("Transaction not found by bankPortfolio=%s and type=%s".formatted(bankPortfolio, STREET_CASH_TRADE.name())));
            return streetCashTrade.getRootExecution().executionId();
        }
    }

}
