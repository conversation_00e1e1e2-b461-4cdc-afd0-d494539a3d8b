package io.wyden.booking.pnl.acceptance;

import com.hazelcast.map.IMap;
import io.cucumber.datatable.DataTable;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.wyden.booking.pnl.TestUtils;
import io.wyden.booking.pnl.acceptance.datatable.LedgerEntryMapping;
import io.wyden.booking.pnl.acceptance.datatable.PositionMapping;
import io.wyden.booking.pnl.application.BookingCompletedMessageConsumer;
import io.wyden.booking.pnl.application.PositionService;
import io.wyden.booking.pnl.application.RateService;
import io.wyden.booking.pnl.domain.model.ledgerentry.LedgerEntry;
import io.wyden.booking.pnl.domain.model.ledgerentry.LedgerEntryRepository;
import io.wyden.booking.pnl.domain.model.position.Position;
import io.wyden.booking.pnl.domain.model.position.PositionRepository;
import io.wyden.booking.pnl.domain.model.position.Track;
import io.wyden.booking.pnl.domain.model.rate.RateValidator;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.LedgerEntrySnapshot;
import io.wyden.published.booking.LedgerEntryType;
import io.wyden.published.common.Metadata;
import io.wyden.published.rate.Rate;
import io.wyden.published.rate.RateKey;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.referencedata.VenueAccount;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static io.wyden.cloudutils.tools.ProtobufUtils.toProtoString;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.slf4j.LoggerFactory.getLogger;

public class Steps {

    private static final Logger LOGGER = getLogger(Steps.class);

    private final BookingCompletedMessageConsumer bookingCompletedMessageConsumer;
    private final LedgerEntryRepository ledgerEntryRepository;
    private final PositionRepository positionRepository;
    private final PositionService positionService;
    private final RateService rateService;
    private final RateValidator rateValidator;

    private final IMap<String, Portfolio> portfoliosMap;
    private final IMap<RateKey, Rate> ratesMap;
    private final IMap<String, VenueAccount> venueAccountMap;

    private BookingCompleted event;
    private String orderId;

    public Steps(BookingCompletedMessageConsumer bookingCompletedMessageConsumer,
                 LedgerEntryRepository ledgerEntryRepository,
                 PositionRepository positionRepository,
                 PositionService positionService,
                 RateService rateService,
                 RateValidator rateValidator,
                 IMap<String, Portfolio> portfoliosMap,
                 IMap<RateKey, Rate> ratesMap,
                 IMap<String, VenueAccount> venueAccountMap) {
        this.bookingCompletedMessageConsumer = bookingCompletedMessageConsumer;
        this.ledgerEntryRepository = ledgerEntryRepository;
        this.positionRepository = positionRepository;
        this.positionService = positionService;
        this.rateService = rateService;
        this.rateValidator = rateValidator;
        this.portfoliosMap = portfoliosMap;
        this.ratesMap = ratesMap;
        this.venueAccountMap = venueAccountMap;
    }

    @Given("last processed sequence number is {long}")
    public void lastProcessedSequenceNumberIs(Long num) {
        bookingCompletedMessageConsumer.resetSequenceNumber(num);
    }

    @Given("portfolio {word} exists and is configured with {word} currency")
    public void portfolioExistsAndIsConfiguredWithCurrency(String portfolioId, String currency) {
        Portfolio portfolio = Portfolio.newBuilder()
            .setId(portfolioId)
            .setName(portfolioId + " Test Portfolio")
            .setPortfolioCurrency(currency)
            .setPortfolioType(PortfolioType.VOSTRO)
            .build();

        portfoliosMap.put(portfolioId, portfolio);
    }

    @Given("account {word} exists and is configured with {word} currency")
    public void accountExistsAndIsConfiguredWithCurrency(String accountId, String currency) {
        VenueAccount account = VenueAccount.newBuilder()
            .setId(accountId)
            .setVenueAccountName(accountId + " Test Account")
            .setAccountType(AccountType.WALLET)
            .build();

        venueAccountMap.put(accountId, account);
    }

    @Given("current market price for {word} is {bigdecimal} {word}")
    public void currentMarketPriceForIs(String instrument, BigDecimal price, String currency) {
        Rate rate = Rate.newBuilder()
            .setBaseCurrency(instrument)
            .setQuoteCurrency(currency)
            .setValue(toProtoString(price))
            .build();

        RateKey rateKey = RateKey.newBuilder()
            .setBaseCurrency(instrument)
            .setQuoteCurrency(currency)
            .build();

        ratesMap.put(rateKey, rate);
    }

    @Given("a BookingCompleted event for {word} with sequence number {long}")
    public void aWalEventWithSequenceNumber(String orderId, long sequenceNumber) {
        this.orderId = orderId;
        this.event = BookingCompleted.newBuilder()
            .setSequenceNumber(sequenceNumber)
            .setMetadata(Metadata.newBuilder()
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .build())
            .build();
    }

    @Given("the event contains a ledger entry with following details:")
    public void theEventContainsALedgerEntry(DataTable dataTable) {
        LedgerEntryMapping.LedgerEntryTableType entry = dataTable.convert(LedgerEntryMapping.LedgerEntryTableType.class, true);

        LedgerEntrySnapshot.Builder builder = toLedgerEntry(entry);

        event = event.toBuilder()
            .addLedgerEntrySnapshot(builder)
            .build();
    }

    @Given("the event contains a list of ledger entries with following details:")
    public void theEventContainsAListOfLedgerEntries(DataTable dataTable) {
        List<LedgerEntryMapping.LedgerEntryTableType> entries = dataTable.asList(LedgerEntryMapping.LedgerEntryTableType.class);

        for (LedgerEntryMapping.LedgerEntryTableType entry : entries) {
            LedgerEntrySnapshot.Builder ledgerEntry = toLedgerEntry(entry);
            LOGGER.debug("Parsed ledger entry: \n{}", ledgerEntry);

            event = event.toBuilder()
                .addLedgerEntrySnapshot(ledgerEntry)
                .build();
        }
    }

    @When("event with sequence number {long} is processed")
    public void eventWithSequenceNumberIsProcessed(long sequenceNumber) {
        ConsumptionResult consumptionResult = bookingCompletedMessageConsumer.consume(event, null);

        assertThat(consumptionResult)
            .as("failed to process the event with sequence number: %s".formatted(sequenceNumber))
            .isEqualTo(ConsumptionResult.consumed());

        await("wait for processing to be completed")
            .atMost(5, TimeUnit.SECONDS)
            .pollDelay(100, TimeUnit.MILLISECONDS)
            .until(() -> ledgerEntryRepository.count() > 0);
    }

    @Then("the ledger entries for {word} should be stored in the database")
    public void theLedgerEntryShouldBeStoredInTheDatabase(String orderId) {
        Collection<LedgerEntry> savedLedgerEntries = ledgerEntryRepository.findByReservationRef(orderId);
        assertThat(savedLedgerEntries).hasSameSizeAs(event.getLedgerEntrySnapshotList());
    }

    @Then("positions should be updated with following details:")
    public void thePositionShouldBeUpdatedWithFollowingDetails(DataTable dataTable) {
        Collection<Track> positions = positionRepository.findAll().stream()
            .map(positionService::getPositionMetrics)
            .sorted(usingTestComparator().thenComparing(track -> track.instrument().getSymbol()))
            .toList();

        LOGGER.info("Total {} positions: \n{}", positions.size(), TestUtils.toGherkinTable(positions));

        List<PositionMapping.PositionTableType> positionRows = dataTable
            .transpose()
            .asList(PositionMapping.PositionTableType.class);

        for (PositionMapping.PositionTableType positionTable : positionRows) {
            String symbol = positionTable.instrument();
            String referenceId = positionTable.referenceId();

            Position position = positionRepository.findByReferenceIdAndSymbol(referenceId, symbol)
                .orElseThrow(() -> new IllegalArgumentException("Cannot find position for referenceId: " + referenceId + " and symbol: " + symbol));

            Track track = positionService.getPositionMetrics(position);

            assertThat(position).as("Failed for: %s".formatted(positionTable.identifier()))
                .satisfies(p -> {
                    assertThat(p.getInstrumentCurrency()).as("currency").isEqualTo(symbol);
                    assertThat(p.getQuantity()).as("qty").isEqualByComparingTo(positionTable.qty());

                    if (positionTable.notionalQty() != null) {
                        assertThat(track.notionalQty()).as("notionalQty").isEqualByComparingTo(positionTable.notionalQty());
                    }

                    if (positionTable.netAveragePrice() != null) {
                        assertThat(track.netAveragePrice()).as("netAveragePrice")
                            // used to avoid scaling issues, 0.9990009 (expected) vs  0.9990009990009990009990009990009990 (actual)
                            // TODO SPL should it be made generic for all comparisons?
                            .usingComparator(Comparator.comparing(a -> a.setScale(6, RoundingMode.HALF_UP)))
                            .isEqualTo(positionTable.netAveragePrice());
                    }

                    if (positionTable.grossAveragePrice() != null) {
                        assertThat(track.grossAveragePrice()).as("grossAveragePrice").isEqualByComparingTo(positionTable.grossAveragePrice());
                    }

                    if (positionTable.marketValue() != null) {
                        assertThat(track.marketValue()).as("marketValue").isEqualByComparingTo(positionTable.marketValue());
                    }

                    if (positionTable.marketValueSc() != null) {
                        assertThat(track.marketValueSc()).as("marketValueSc").isEqualByComparingTo(positionTable.marketValueSc());
                    }

                    if (positionTable.netCost() != null) {
                        assertThat(track.netCost()).as("netCost").isEqualByComparingTo(positionTable.netCost());
                    }

                    if (positionTable.netCostSc() != null) {
                        assertThat(track.netCostSc()).as("netCostSc").isEqualByComparingTo(positionTable.netCostSc());
                    }

                    if (positionTable.grossCost() != null) {
                        assertThat(track.grossCost()).as("grossCost").isEqualByComparingTo(positionTable.grossCost());
                    }

                    if (positionTable.grossCostSc() != null) {
                        assertThat(track.grossCostSc()).as("grossCostSc").isEqualByComparingTo(positionTable.grossCostSc());
                    }

                    if (positionTable.netRealizedPnl() != null) {
                        assertThat(track.netRealizedPnl()).as("netRealizedPnl").isEqualByComparingTo(positionTable.netRealizedPnl());
                    }

                    if (positionTable.netRealizedPnlSc() != null) {
                        assertThat(track.netRealizedPnlSc()).as("netRealizedPnlSc").isEqualByComparingTo(positionTable.netRealizedPnlSc());
                    }

                    if (positionTable.grossRealizedPnl() != null) {
                        assertThat(track.grossRealizedPnl()).as("grossRealizedPnl").isEqualByComparingTo(positionTable.grossRealizedPnl());
                    }

                    if (positionTable.grossRealizedPnlSc() != null) {
                        assertThat(track.grossRealizedPnlSc()).as("grossRealizedPnlSc").isEqualByComparingTo(positionTable.grossRealizedPnlSc());
                    }

                    if (positionTable.netUnrealizedPnl() != null) {
                        assertThat(track.netUnrealizedPnl()).as("netUnrealizedPnl").isEqualByComparingTo(positionTable.netUnrealizedPnl());
                    }

                    if (positionTable.netUnrealizedPnlSc() != null) {
                        assertThat(track.netUnrealizedPnlSc()).as("netUnrealizedPnlSc").isEqualByComparingTo(positionTable.netUnrealizedPnlSc());
                    }

                    if (positionTable.grossUnrealizedPnl() != null) {
                        assertThat(track.grossUnrealizedPnl()).as("grossUnrealizedPnl").isEqualByComparingTo(positionTable.grossUnrealizedPnl());
                    }

                    if (positionTable.grossUnrealizedPnlSc() != null) {
                        assertThat(track.grossUnrealizedPnlSc()).as("grossUnrealizedPnlSc").isEqualByComparingTo(positionTable.grossUnrealizedPnlSc());
                    }
                });
        }
    }

    private LedgerEntrySnapshot.Builder toLedgerEntry(LedgerEntryMapping.LedgerEntryTableType entry) {
        LedgerEntrySnapshot.Builder builder = LedgerEntrySnapshot.newBuilder();

        if (isNotBlank(entry.ledgerEntryType())) {
            LedgerEntryType ledgerEntryType = toLedgerEntryType(entry.ledgerEntryType());
            builder.setType(ledgerEntryType)
                .setSettled(toSettled(ledgerEntryType));
        }

        if (isNotBlank(entry.transactionId())) {
            builder.setTransactionId(entry.transactionId());
        }

        String reservationRef = StringUtils.firstNonBlank(entry.reservationRef(), orderId);
        if (isNotBlank(reservationRef)) {
            builder.setReservationRef(reservationRef);
        }

        if (isNotBlank(entry.portfolioId())) {
            builder.setPortfolio(entry.portfolioId());
        }

        if (isNotBlank(entry.accountId())) {
            builder.setAccount(entry.accountId());
        }

        if (isNotBlank(entry.instrument())) {
            builder.setSymbol(entry.instrument())
                .setCurrency(entry.instrument());
        }

        if (entry.qty() != null) {
            builder.setQuantity(toProtoString(entry.qty()));
        }

        if (entry.price() != null) {
            builder.setPrice(toProtoString(entry.price()));
        }

        entry.getFees()
            .forEach(fee -> builder.addFees(Fee.newBuilder()
                .setAmount(toProtoString(fee.quantity()))
                .setCurrency(fee.currency())
                .setFeeType(FeeType.TRANSACTION_FEE)
                .build()));
        return builder;
    }

    private static LedgerEntryType toLedgerEntryType(String ledgerEntryType) {
        if (isBlank(ledgerEntryType)) {
            return LedgerEntryType.UNSPECIFIED;
        }

        return switch (ledgerEntryType) {
            case "ASSET_TRADE_BUY" -> LedgerEntryType.ASSET_TRADE_BUY;
            case "ASSET_TRADE_SELL" -> LedgerEntryType.ASSET_TRADE_SELL;
            case "CASH_TRADE_CREDIT" -> LedgerEntryType.CASH_TRADE_CREDIT;
            case "CASH_TRADE_DEBIT" -> LedgerEntryType.CASH_TRADE_DEBIT;
            case "ASSET_TRADE_PROCEEDS" -> LedgerEntryType.ASSET_TRADE_PROCEEDS;
            case "DEPOSIT" -> LedgerEntryType.DEPOSIT;
            case "WITHDRAWAL" -> LedgerEntryType.WITHDRAWAL;
            case "TRANSFER" -> LedgerEntryType.TRANSFER;
            case "FEE" -> LedgerEntryType.FEE;
            case "TRADING_FEE" -> LedgerEntryType.TRADING_FEE;
            case "DEPOSIT_FEE" -> LedgerEntryType.DEPOSIT_FEE;
            case "WITHDRAWAL_FEE" -> LedgerEntryType.WITHDRAWAL_FEE;
            case "TRANSFER_FEE" -> LedgerEntryType.TRANSFER_FEE;
            case "RESERVATION" -> LedgerEntryType.RESERVATION;
            case "RESERVATION_RELEASE" -> LedgerEntryType.RESERVATION_RELEASE;
            case "RESERVATION_RELEASE_REMAINING" -> LedgerEntryType.RESERVATION_RELEASE_REMAINING;
            case "WITHDRAWAL_RESERVATION" -> LedgerEntryType.WITHDRAWAL_RESERVATION;
            default -> LedgerEntryType.UNSPECIFIED;
        };
    }

    private boolean toSettled(LedgerEntryType ledgerEntryType) {
        return switch (ledgerEntryType) {
            case CASH_TRADE_CREDIT, CASH_TRADE_DEBIT, ASSET_TRADE_BUY, ASSET_TRADE_SELL, ASSET_TRADE_PROCEEDS -> false;
            case DEPOSIT, WITHDRAWAL, TRANSFER, RESERVATION, RESERVATION_RELEASE, RESERVATION_RELEASE_REMAINING, WITHDRAWAL_RESERVATION -> true;
            default -> false;
        };
    }

    /**
     * Provides a comparator for comparing two Track objects based on the priority of their reference IDs.
     * The priority is determined as follows:
     * 1. References starting with "Client" have the highest priority.
     * 2. Among references that do not start with "Client", those starting with "Bank" have higher priority.
     * 3. If neither condition applies or if the priorities are equal, references are compared alphabetically.
     *
     * @return a Comparator for comparing Track objects based on the defined priority rules.
     */
    private static @NotNull Comparator<Track> usingTestComparator() {
        return (track1, track2) -> {
            String ref1 = track1.reference().getReferenceId();
            String ref2 = track2.reference().getReferenceId();

            // Check if referenceId starts with "Client"
            boolean isClient1 = ref1.startsWith("Client");
            boolean isClient2 = ref2.startsWith("Client");

            if (isClient1 && !isClient2) return -1;
            if (!isClient1 && isClient2) return 1;

            // If both are Client or both are not Client, check for "Bank"
            if (!isClient1 && !isClient2) {
                boolean isBank1 = ref1.startsWith("Bank");
                boolean isBank2 = ref2.startsWith("Bank");

                if (isBank1 && !isBank2) return -1;
                if (!isBank1 && isBank2) return 1;
            }

            // If both have same priority, sort alphabetically
            return ref1.compareTo(ref2);
        };
    }
}
