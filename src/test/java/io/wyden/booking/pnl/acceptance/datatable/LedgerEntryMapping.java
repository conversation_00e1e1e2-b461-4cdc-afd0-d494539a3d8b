package io.wyden.booking.pnl.acceptance.datatable;

import io.cucumber.java.DataTableType;
import io.wyden.booking.pnl.Amount;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;
import javax.annotation.Nullable;

import static io.wyden.booking.pnl.TestUtils.parseAmount;
import static io.wyden.booking.pnl.TestUtils.parseBigDecimal;
import static io.wyden.booking.pnl.TestUtils.parseText;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isNonZero;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isNullOrZero;

public class LedgerEntryMapping {

    public record LedgerEntryTableType(
        String portfolioId,
        String accountId,
        String ledgerEntryType,
        String instrument,
        String transactionId,
        String reservationRef,
        BigDecimal qty,
        BigDecimal price,
        Amount fee,
        Amount fee1,
        Amount fee2,
        Amount fee3) {

        public Collection<Amount> getFees() {
            return Stream.of(fee, fee1, fee2, fee3)
                .filter(Objects::nonNull)
                .filter(amount -> isNonZero(amount.quantity()))
                .toList();
        }
    }

    @DataTableType
    public LedgerEntryTableType convertLedgerEntry(Map<String, String> entry) {
        return new LedgerEntryTableType(
            parseText(entry.get("portfolioId")),
            parseText(entry.get("accountId")),
            parseText(entry.get("ledgerEntryType")),
            parseText(entry.get("instrument")),
            parseText(entry.get("transactionId")),
            parseText(entry.get("reservationRef")),
            parseBigDecimal(entry.get("qty")),
            parseBigDecimal(entry.get("price")),
            parseAmount(entry.get("fee")), // fee without number is for backward compatibility to convert old tests from booking-engine and not miss anything
            parseAmount(entry.get("fee1")),
            parseAmount(entry.get("fee2")),
            parseAmount(entry.get("fee3"))
        );
    }
}
