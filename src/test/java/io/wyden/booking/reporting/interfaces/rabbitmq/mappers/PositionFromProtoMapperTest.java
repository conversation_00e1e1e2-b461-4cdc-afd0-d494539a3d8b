package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.application.referencedata.AccountSnapshot;
import io.wyden.booking.reporting.application.referencedata.PortfolioSnapshot;
import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.published.booking.PositionSearch;
import io.wyden.published.booking.PositionSnapshot;
import io.wyden.published.common.Metadata;
import io.wyden.published.common.SortingOrder;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class PositionFromProtoMapperTest {

    @Test
    void shouldMapAllPositionSnapshotFieldsToPosition() {
        // Given
        PositionSnapshot positionSnapshot = PositionSnapshot.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setRequesterId("test-requester")
                .setResponseId("test-response-id")
                .setCreatedAt("2023-12-15T14:30:25.123456Z")
                .setUpdatedAt("2023-12-15T14:30:25.123456Z")
                .build())
            .setSymbol("BTCUSD")
            .setCurrency("BTC")
            .setPortfolio("test-portfolio")
            .setAccount("test-account")
            .setQuantity("100.50")
            .setNotionalQuantity("50000.25")
            .setNetCost("49500.00")
            .setNetCostSc("49500.00")
            .setGrossCost("49510.00")
            .setGrossCostSc("49510.00")
            .setNetRealizedPnl("500.00")
            .setNetRealizedPnlSc("500.00")
            .setGrossRealizedPnl("490.00")
            .setGrossRealizedPnlSc("490.00")
            .setNetAveragePrice("492.54")
            .setGrossAveragePrice("492.64")
            .setMarketValue("50250.00")
            .setMarketValueSc("50250.00")
            .setNetUnrealizedPnl("750.00")
            .setNetUnrealizedPnlSc("750.00")
            .setGrossUnrealizedPnl("740.00")
            .setGrossUnrealizedPnlSc("740.00")
            .setLastAppliedLedgerEntryId("12345")
            // Proto-only fields (not mapped to Position)
            .setPendingQuantity("10.0")
            .setAvailableForTradingQuantity("90.0")
            .setAvailableForWithdrawalQuantity("85.0")
            .setSettledQuantity("95.0")
            .setUnsettledQuantity("5.0")
            .build();

        PortfolioSnapshot portfolioSnapshot = new PortfolioSnapshot(positionSnapshot.getPortfolio(), "EUR", RequestModel.PortfolioType.VOSTRO);

        // When
        Position position = PositionFromProtoMapper.map(positionSnapshot, portfolioSnapshot, null);

        // Then
        assertThat(position).isNotNull();

        // Basic identification fields
        assertThat(position.getSymbol()).isEqualTo("BTCUSD");
        assertThat(position.getCurrency()).isEqualTo("BTC");
        assertThat(position.getPortfolioId()).isEqualTo("test-portfolio");
        assertThat(position.getAccountId()).isNull(); // Not set when portfolio is present
        assertThat(position.getPortfolioCurrency()).isEqualTo("EUR"); // From parameter
        assertThat(position.getAccountCurrency()).isNull(); // Not set when portfolio is present

        // Quantity fields
        assertThat(position.getQuantity()).isEqualTo(bd("100.50"));
        assertThat(position.getNotionalQuantity()).isEqualTo(bd("50000.25"));

        // Cost fields
        assertThat(position.getNetCost()).isEqualTo(bd("49500.00"));
        assertThat(position.getNetCostSc()).isEqualTo(bd("49500.00"));
        assertThat(position.getGrossCost()).isEqualTo(bd("49510.00"));
        assertThat(position.getGrossCostSc()).isEqualTo(bd("49510.00"));

        // Realized PnL fields
        assertThat(position.getNetRealizedPnl()).isEqualTo(bd("500.00"));
        assertThat(position.getNetRealizedPnlSc()).isEqualTo(bd("500.00"));
        assertThat(position.getGrossRealizedPnl()).isEqualTo(bd("490.00"));
        assertThat(position.getGrossRealizedPnlSc()).isEqualTo(bd("490.00"));

        // Average price fields
        assertThat(position.getNetAveragePrice()).isEqualTo(bd("492.54"));
        assertThat(position.getGrossAveragePrice()).isEqualTo(bd("492.64"));

        // Market value fields
        assertThat(position.getMarketValue()).isEqualTo(bd("50250.00"));
        assertThat(position.getMarketValueSc()).isEqualTo(bd("50250.00"));

        // Unrealized PnL fields
        assertThat(position.getNetUnrealizedPnl()).isEqualTo(bd("750.00"));
        assertThat(position.getNetUnrealizedPnlSc()).isEqualTo(bd("750.00"));
        assertThat(position.getGrossUnrealizedPnl()).isEqualTo(bd("740.00"));
        assertThat(position.getGrossUnrealizedPnlSc()).isEqualTo(bd("740.00"));

        // Tracking fields
        assertThat(position.getLastAppliedLedgerEntryId()).isEqualTo(12345L);
        assertThat(position.getSequenceNumber()).isEqualTo(0L); // Hard-coded in mapper

        // Position-only fields (not in proto) should have defaults
        assertThat(position.getContractSize()).isEqualTo(BigDecimal.ONE); // Default value
        assertThat(position.isInverseContract()).isFalse(); // Default value
        assertThat(position.getCreatedBy()).isNull(); // Not mapped
        assertThat(position.getUpdatedBy()).isNull(); // Not mapped
    }

    @Test
    void shouldThrowExceptionWhenBothPortfolioAndAccountAreEmpty() {
        // Given
        PositionSnapshot positionSnapshot = PositionSnapshot.newBuilder()
            .setMetadata(Metadata.newBuilder().setRequesterId("test").build())
            .setSymbol("BTCUSD")
            .setPortfolio("") // Empty
            .setAccount("") // Empty
            .setQuantity("100.0")
            .build();

        PortfolioSnapshot portfolioSnapshot = new PortfolioSnapshot(positionSnapshot.getPortfolio(), "EUR", RequestModel.PortfolioType.VOSTRO);
        AccountSnapshot accountSnapshot = new AccountSnapshot(positionSnapshot.getAccount(), "USD", RequestModel.AccountType.WALLET, RequestModel.WalletType.VOSTRO);

        // When & Then
        assertThatThrownBy(() -> PositionFromProtoMapper.map(positionSnapshot, portfolioSnapshot, accountSnapshot))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("Portfolio id and account id cannot both be empty");
    }

    @Test
    void shouldMapPositionSearchToRequestModel() {
        // Given
        PositionSearch positionSearch = PositionSearch.newBuilder()
            .addSymbol("BTCUSD")
            .addSymbol("ETHUSD")
            .addCurrency("BTC")
            .addCurrency("ETH")
            .addAccountId("account1")
            .addAccountId("account2")
            .addPortfolioId("portfolio1")
            .addPortfolioId("portfolio2")
            .setClientId("test-client")
            .setFirst(50)
            .setAfter("cursor123")
            .setSortingOrder(SortingOrder.SORTING_ORDER_ASC)
            .build();

        // When
        RequestModel.PositionSearch result = PositionFromProtoMapper.map(positionSearch);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.symbol()).containsExactly("BTCUSD", "ETHUSD");
        assertThat(result.currency()).containsExactly("BTC", "ETH");
        assertThat(result.accountId()).containsExactly("account1", "account2");
        assertThat(result.portfolio()).containsExactly("portfolio1", "portfolio2");
        assertThat(result.clientId()).isEqualTo("test-client");
        assertThat(result.first()).isEqualTo(50);
        assertThat(result.after()).isEqualTo("cursor123");
        assertThat(result.sortingOrder()).isEqualTo(RequestModel.SortingOrder.ASC);

        // Fields not mapped from proto should have default values
        assertThat(result.portfolioType()).isNull();
        assertThat(result.accountType()).isNull();
        assertThat(result.walletType()).isNull();
    }
}