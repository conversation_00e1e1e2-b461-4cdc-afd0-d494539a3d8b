package io.wyden.booking.reporting.interfaces.rabbitmq;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test class demonstrating the gap detection logic for PositionSnapshot events.
 * This shows how the system handles multiple events with the same sequence number.
 */
class PositionSnapshotGapDetectionTest {

    @Test
    void shouldAllowMultipleEventsWithSameSequenceNumber() {
        // Given - we've processed up to sequence 99, so 100 is expected next
        long lastProcessedSequence = 99L;
        
        // When - multiple events with sequence 100 arrive
        boolean shouldProcess1 = shouldProcessEvent(100L, lastProcessedSequence); // Expected next sequence
        boolean shouldProcess2 = shouldProcessEvent(100L, lastProcessedSequence); // Same sequence again
        boolean shouldProcess3 = shouldProcessEvent(101L, lastProcessedSequence); // Following sequence
        
        // Then
        assertThat(shouldProcess1).isTrue(); // Should process expected sequence (100 == 100)
        assertThat(shouldProcess2).isTrue(); // Should process same sequence again (100 == 99, allowed)
        assertThat(shouldProcess3).isTrue(); // Should process next sequence (101 > 100, gap recovery would trigger)
    }

    @Test
    void shouldDetectGapWhenSequenceNumberJumps() {
        // Given
        long lastProcessedSequence = 100L;
        
        // When - sequence number jumps (gap detected)
        boolean shouldProcess = shouldProcessEvent(103L, lastProcessedSequence);
        boolean isGapDetected = isGapDetected(103L, lastProcessedSequence);
        
        // Then
        assertThat(shouldProcess).isTrue(); // Should still process after recovery
        assertThat(isGapDetected).isTrue(); // Gap should be detected
    }

    @Test
    void shouldSkipOldEvents() {
        // Given
        long lastProcessedSequence = 100L;
        
        // When - old event arrives
        boolean shouldProcess = shouldProcessEvent(99L, lastProcessedSequence);
        
        // Then
        assertThat(shouldProcess).isFalse(); // Should skip old events
    }

    /**
     * Simulates the gap detection logic from PositionSnapshotEventConsumer
     */
    private boolean shouldProcessEvent(long inboundSequenceNumber, long lastProcessedSequence) {
        long expectedSequence = lastProcessedSequence + 1;
        
        if (inboundSequenceNumber < expectedSequence) {
            return false; // Skip already processed
        }
        
        if (inboundSequenceNumber == expectedSequence || inboundSequenceNumber == lastProcessedSequence) {
            return true; // Same sequence number or expected next sequence - continue processing
        }
        
        if (inboundSequenceNumber > expectedSequence) {
            // Gap detected - would trigger recovery, then process
            return true;
        }
        
        return true;
    }

    private boolean isGapDetected(long inboundSequenceNumber, long lastProcessedSequence) {
        long expectedSequence = lastProcessedSequence + 1;
        return inboundSequenceNumber > expectedSequence;
    }
}