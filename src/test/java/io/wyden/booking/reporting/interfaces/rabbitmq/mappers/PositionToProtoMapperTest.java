package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.published.booking.PositionSnapshot;
import org.junit.jupiter.api.Test;

import java.sql.Timestamp;
import java.time.Instant;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.assertj.core.api.Assertions.assertThat;

class PositionToProtoMapperTest {

    @Test
    void shouldMapAllPositionFieldsToPositionSnapshot() {
        // Given
        Timestamp now = Timestamp.from(Instant.now());
        Position position = Position.builder()
            // Inherited from PositionIdentifiers
            .id(1L)
            .createdAt(now)
            .updatedAt(now)
            .version(2L)
            .portfolioId("test-portfolio")
            .accountId("test-account")
            .portfolioType(RequestModel.PortfolioType.VOSTRO)
            .accountType(RequestModel.AccountType.WALLET)
            .accountWalletType(RequestModel.WalletType.NOSTRO)
            
            // Position-specific fields
            .symbol("BTCUSD")
            .currency("BTC")
            .portfolioCurrency("EUR")
            .accountCurrency("USD")
            .contractSize(bd("0.001"))
            .inverseContract(true)
            .quantity(bd("100.50"))
            .notionalQuantity(bd("50000.25"))
            .netCost(bd("49500.00"))
            .netCostSc(bd("49500.00"))
            .grossCost(bd("49510.00"))
            .grossCostSc(bd("49510.00"))
            .netRealizedPnl(bd("500.00"))
            .netRealizedPnlSc(bd("500.00"))
            .grossRealizedPnl(bd("490.00"))
            .grossRealizedPnlSc(bd("490.00"))
            .netAveragePrice(bd("492.54"))
            .grossAveragePrice(bd("492.64"))
            .marketValue(bd("50250.00"))
            .marketValueSc(bd("50250.00"))
            .netUnrealizedPnl(bd("750.00"))
            .netUnrealizedPnlSc(bd("750.00"))
            .grossUnrealizedPnl(bd("740.00"))
            .grossUnrealizedPnlSc(bd("740.00"))
            .lastAppliedLedgerEntryId(12345L)
            .sequenceNumber(9876L)
            .createdBy("test-host-1")
            .updatedBy("test-host-2")
            .build();

        // When
        PositionSnapshot result = PositionToProtoMapper.map(position);

        // Then
        assertThat(result).isNotNull();
        
        // Basic identification fields
        assertThat(result.getSymbol()).isEqualTo("BTCUSD");
        assertThat(result.getCurrency()).isEqualTo("BTC");
        assertThat(result.getBookingCurrency()).isEqualTo("EUR"); // Should use portfolioCurrency since portfolioId is set
        assertThat(result.getPortfolio()).isEqualTo("test-portfolio");
        assertThat(result.getAccount()).isEqualTo("test-account");
        
        // Quantity fields
        assertThat(result.getQuantity()).isEqualTo("100.5");
        assertThat(result.getNotionalQuantity()).isEqualTo("50000.25");
        
        // Cost fields
        assertThat(result.getNetCost()).isEqualTo("49500");
        assertThat(result.getNetCostSc()).isEqualTo("49500");
        assertThat(result.getGrossCost()).isEqualTo("49510");
        assertThat(result.getGrossCostSc()).isEqualTo("49510");
        
        // Realized PnL fields
        assertThat(result.getNetRealizedPnl()).isEqualTo("500");
        assertThat(result.getNetRealizedPnlSc()).isEqualTo("500");
        assertThat(result.getGrossRealizedPnl()).isEqualTo("490");
        assertThat(result.getGrossRealizedPnlSc()).isEqualTo("490");
        
        // Average price fields
        assertThat(result.getNetAveragePrice()).isEqualTo("492.54");
        assertThat(result.getGrossAveragePrice()).isEqualTo("492.64");
        
        // Market value fields
        assertThat(result.getMarketValue()).isEqualTo("50250");
        assertThat(result.getMarketValueSc()).isEqualTo("50250");
        
        // Unrealized PnL fields
        assertThat(result.getNetUnrealizedPnl()).isEqualTo("750");
        assertThat(result.getNetUnrealizedPnlSc()).isEqualTo("750");
        assertThat(result.getGrossUnrealizedPnl()).isEqualTo("740");
        assertThat(result.getGrossUnrealizedPnlSc()).isEqualTo("740");
        
        // Tracking fields
        assertThat(result.getLastAppliedLedgerEntryId()).isEqualTo("12345");

        // Check metadata
        assertThat(result.getMetadata()).isNotNull();
        assertThat(result.getMetadata().getRequesterId()).isEqualTo("booking-reporting");
        assertThat(result.getMetadata().getResponseId()).isNotNull();
        assertThat(result.getMetadata().getCreatedAt()).isNotNull();
        assertThat(result.getMetadata().getUpdatedAt()).isNotNull();

        // Check that proto-only fields remain as default values (empty strings)
        assertThat(result.getPendingQuantity()).isEqualTo("");
        assertThat(result.getAvailableForTradingQuantity()).isEqualTo("");
        assertThat(result.getAvailableForWithdrawalQuantity()).isEqualTo("");
        assertThat(result.getSettledQuantity()).isEqualTo("");
        assertThat(result.getUnsettledQuantity()).isEqualTo("");
    }

    @Test
    void shouldHandleNullStringFields() {
        // Given
        Position position = Position.builder()
            .symbol(null) // Null symbol
            .currency(null) // Null currency
            .portfolioId("test-portfolio")
            .portfolioCurrency("USD")
            .quantity(bd("10.0"))
            .build();

        // When
        PositionSnapshot result = PositionToProtoMapper.map(position);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getSymbol()).isEqualTo(""); // Not set because symbol is null
        assertThat(result.getCurrency()).isEqualTo(""); // Not set because currency is null
        assertThat(result.getBookingCurrency()).isEqualTo("USD");
        assertThat(result.getQuantity()).isEqualTo("10");
    }
}
