package io.wyden.booking.reporting.application.recovery;

import io.wyden.published.booking.BookingCompleted;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BookingCompletedGapRecoveryServiceTest {

    @Mock
    private BookingCompletedRecoveryClient recoveryClient;

    private BookingCompletedGapRecoveryService gapRecoveryService;

    @BeforeEach
    void setUp() {
        gapRecoveryService = new BookingCompletedGapRecoveryService(recoveryClient);
    }

    @Test
    void shouldRecoverGapBetweenSequences() {
        // Given
        long expectedSequence = 5L;
        long receivedSequence = 8L;
        
        BookingCompleted event5 = createBookingCompleted(5L);
        BookingCompleted event6 = createBookingCompleted(6L);
        BookingCompleted event7 = createBookingCompleted(7L);
        
        when(recoveryClient.fetchBookingCompletedEventsAfter(4L))
            .thenReturn(new BookingCompletedRecoveryResult(List.of(event5, event6, event7), false));
        
        // When
        List<BookingCompleted> result = gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
        
        // Then
        assertThat(result).hasSize(3);
        assertThat(result.get(0).getSequenceNumber()).isEqualTo(5L);
        assertThat(result.get(1).getSequenceNumber()).isEqualTo(6L);  
        assertThat(result.get(2).getSequenceNumber()).isEqualTo(7L);
    }

    @Test
    void shouldRecoverGapInMultipleBatches() {
        // Given
        long expectedSequence = 5L;
        long receivedSequence = 10L;
        
        BookingCompleted event5 = createBookingCompleted(5L);
        BookingCompleted event6 = createBookingCompleted(6L);
        BookingCompleted event7 = createBookingCompleted(7L);
        BookingCompleted event8 = createBookingCompleted(8L);
        BookingCompleted event9 = createBookingCompleted(9L);
        
        // First batch
        when(recoveryClient.fetchBookingCompletedEventsAfter(4L))
            .thenReturn(new BookingCompletedRecoveryResult(List.of(event5, event6), true));
        
        // Second batch
        when(recoveryClient.fetchBookingCompletedEventsAfter(6L))
            .thenReturn(new BookingCompletedRecoveryResult(List.of(event7, event8, event9), false));
        
        // When
        List<BookingCompleted> result = gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
        
        // Then
        assertThat(result).hasSize(5);
        assertThat(result.get(0).getSequenceNumber()).isEqualTo(5L);
        assertThat(result.get(1).getSequenceNumber()).isEqualTo(6L);
        assertThat(result.get(2).getSequenceNumber()).isEqualTo(7L);
        assertThat(result.get(3).getSequenceNumber()).isEqualTo(8L);
        assertThat(result.get(4).getSequenceNumber()).isEqualTo(9L);
    }

    @Test
    void shouldReturnEmptyListWhenNoEventsToRecover() {
        // Given
        long expectedSequence = 5L;
        long receivedSequence = 6L;
        
        when(recoveryClient.fetchBookingCompletedEventsAfter(4L))
            .thenReturn(new BookingCompletedRecoveryResult(List.of(), false));
        
        // When
        List<BookingCompleted> result = gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
        
        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void shouldStopRecoveryAtGapEnd() {
        // Given
        long expectedSequence = 5L;
        long receivedSequence = 7L;
        
        BookingCompleted event5 = createBookingCompleted(5L);
        BookingCompleted event6 = createBookingCompleted(6L);
        BookingCompleted event7 = createBookingCompleted(7L); // Should not be included
        BookingCompleted event8 = createBookingCompleted(8L); // Should not be included
        
        when(recoveryClient.fetchBookingCompletedEventsAfter(4L))
            .thenReturn(new BookingCompletedRecoveryResult(List.of(event5, event6, event7, event8), false));
        
        // When
        List<BookingCompleted> result = gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
        
        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getSequenceNumber()).isEqualTo(5L);
        assertThat(result.get(1).getSequenceNumber()).isEqualTo(6L);
    }

    @Test
    void shouldRecoverAllMissingEventsFromStartup() {
        // Given
        long lastProcessedSequence = 10L;
        
        BookingCompleted event11 = createBookingCompleted(11L);
        BookingCompleted event12 = createBookingCompleted(12L);
        BookingCompleted event13 = createBookingCompleted(13L);
        
        when(recoveryClient.fetchBookingCompletedEventsAfter(10L))
            .thenReturn(new BookingCompletedRecoveryResult(List.of(event11, event12, event13), false));
        
        // When
        List<BookingCompleted> result = gapRecoveryService.recoverAllMissingEvents(lastProcessedSequence);
        
        // Then
        assertThat(result).hasSize(3);
        assertThat(result.get(0).getSequenceNumber()).isEqualTo(11L);
        assertThat(result.get(1).getSequenceNumber()).isEqualTo(12L);
        assertThat(result.get(2).getSequenceNumber()).isEqualTo(13L);
    }

    @Test
    void shouldHandleRecoveryException() {
        // Given
        long expectedSequence = 5L;
        long receivedSequence = 8L;
        
        when(recoveryClient.fetchBookingCompletedEventsAfter(4L))
            .thenThrow(new RuntimeException("Recovery failed"));
        
        // When
        List<BookingCompleted> result = gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
        
        // Then
        assertThat(result).isEmpty();
    }

    private BookingCompleted createBookingCompleted(long sequenceNumber) {
        return BookingCompleted.newBuilder()
            .setSequenceNumber(sequenceNumber)
            .build();
    }
}