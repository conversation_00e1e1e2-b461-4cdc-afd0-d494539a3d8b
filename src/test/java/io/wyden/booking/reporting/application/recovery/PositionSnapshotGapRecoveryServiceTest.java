package io.wyden.booking.reporting.application.recovery;

import io.wyden.published.booking.PositionSnapshot;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PositionSnapshotGapRecoveryServiceTest {

    @Mock
    private PositionSnapshotRecoveryClient recoveryClient;

    private PositionSnapshotGapRecoveryService gapRecoveryService;

    @BeforeEach
    void setUp() {
        gapRecoveryService = new PositionSnapshotGapRecoveryService(recoveryClient);
    }

    @Test
    void shouldRecoverGapBetweenSequences() {
        // Given
        long expectedSequence = 5L;
        long receivedSequence = 8L;
        
        PositionSnapshot event5 = createPositionSnapshot(5L);
        PositionSnapshot event6 = createPositionSnapshot(6L);
        PositionSnapshot event7 = createPositionSnapshot(7L);
        
        when(recoveryClient.fetchPositionSnapshotEventsAfter(4L))
            .thenReturn(new PositionSnapshotRecoveryResult(List.of(event5, event6, event7), false));
        
        // When
        List<PositionSnapshot> result = gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
        
        // Then
        assertThat(result).hasSize(3);
        assertThat(result.get(0).getSequenceNumber()).isEqualTo(5L);
        assertThat(result.get(1).getSequenceNumber()).isEqualTo(6L);  
        assertThat(result.get(2).getSequenceNumber()).isEqualTo(7L);
    }

    @Test
    void shouldRecoverGapInMultipleBatches() {
        // Given
        long expectedSequence = 5L;
        long receivedSequence = 10L;
        
        PositionSnapshot event5 = createPositionSnapshot(5L);
        PositionSnapshot event6 = createPositionSnapshot(6L);
        PositionSnapshot event7 = createPositionSnapshot(7L);
        PositionSnapshot event8 = createPositionSnapshot(8L);
        PositionSnapshot event9 = createPositionSnapshot(9L);
        
        // First batch
        when(recoveryClient.fetchPositionSnapshotEventsAfter(4L))
            .thenReturn(new PositionSnapshotRecoveryResult(List.of(event5, event6), true));
        
        // Second batch
        when(recoveryClient.fetchPositionSnapshotEventsAfter(6L))
            .thenReturn(new PositionSnapshotRecoveryResult(List.of(event7, event8, event9), false));
        
        // When
        List<PositionSnapshot> result = gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
        
        // Then
        assertThat(result).hasSize(5);
        assertThat(result.get(0).getSequenceNumber()).isEqualTo(5L);
        assertThat(result.get(1).getSequenceNumber()).isEqualTo(6L);
        assertThat(result.get(2).getSequenceNumber()).isEqualTo(7L);
        assertThat(result.get(3).getSequenceNumber()).isEqualTo(8L);
        assertThat(result.get(4).getSequenceNumber()).isEqualTo(9L);
    }

    @Test
    void shouldReturnEmptyListWhenNoEventsToRecover() {
        // Given
        long expectedSequence = 5L;
        long receivedSequence = 6L;
        
        when(recoveryClient.fetchPositionSnapshotEventsAfter(4L))
            .thenReturn(new PositionSnapshotRecoveryResult(List.of(), false));
        
        // When
        List<PositionSnapshot> result = gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
        
        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void shouldStopRecoveryAtGapEnd() {
        // Given
        long expectedSequence = 5L;
        long receivedSequence = 7L;
        
        PositionSnapshot event5 = createPositionSnapshot(5L);
        PositionSnapshot event6 = createPositionSnapshot(6L);
        PositionSnapshot event7 = createPositionSnapshot(7L); // Should not be included
        PositionSnapshot event8 = createPositionSnapshot(8L); // Should not be included
        
        when(recoveryClient.fetchPositionSnapshotEventsAfter(4L))
            .thenReturn(new PositionSnapshotRecoveryResult(List.of(event5, event6, event7, event8), false));
        
        // When
        List<PositionSnapshot> result = gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
        
        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getSequenceNumber()).isEqualTo(5L);
        assertThat(result.get(1).getSequenceNumber()).isEqualTo(6L);
    }

    @Test
    void shouldRecoverAllMissingEventsFromStartup() {
        // Given
        long lastProcessedSequence = 10L;
        
        PositionSnapshot event11 = createPositionSnapshot(11L);
        PositionSnapshot event12 = createPositionSnapshot(12L);
        PositionSnapshot event13 = createPositionSnapshot(13L);
        
        when(recoveryClient.fetchPositionSnapshotEventsAfter(10L))
            .thenReturn(new PositionSnapshotRecoveryResult(List.of(event11, event12, event13), false));
        
        // When
        List<PositionSnapshot> result = gapRecoveryService.recoverAllMissingEvents(lastProcessedSequence);
        
        // Then
        assertThat(result).hasSize(3);
        assertThat(result.get(0).getSequenceNumber()).isEqualTo(11L);
        assertThat(result.get(1).getSequenceNumber()).isEqualTo(12L);
        assertThat(result.get(2).getSequenceNumber()).isEqualTo(13L);
    }

    @Test
    void shouldHandleRecoveryException() {
        // Given
        long expectedSequence = 5L;
        long receivedSequence = 8L;
        
        when(recoveryClient.fetchPositionSnapshotEventsAfter(4L))
            .thenThrow(new RuntimeException("Recovery failed"));
        
        // When
        List<PositionSnapshot> result = gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
        
        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void shouldRecoverMultipleBatchesFromStartup() {
        // Given
        long lastProcessedSequence = 5L;
        
        PositionSnapshot event6 = createPositionSnapshot(6L);
        PositionSnapshot event7 = createPositionSnapshot(7L);
        PositionSnapshot event8 = createPositionSnapshot(8L);
        PositionSnapshot event9 = createPositionSnapshot(9L);
        
        // First batch
        when(recoveryClient.fetchPositionSnapshotEventsAfter(5L))
            .thenReturn(new PositionSnapshotRecoveryResult(List.of(event6, event7), true));
        
        // Second batch
        when(recoveryClient.fetchPositionSnapshotEventsAfter(7L))
            .thenReturn(new PositionSnapshotRecoveryResult(List.of(event8, event9), false));
        
        // When  
        List<PositionSnapshot> result = gapRecoveryService.recoverAllMissingEvents(lastProcessedSequence);
        
        // Then
        assertThat(result).hasSize(4);
        assertThat(result.get(0).getSequenceNumber()).isEqualTo(6L);
        assertThat(result.get(1).getSequenceNumber()).isEqualTo(7L);
        assertThat(result.get(2).getSequenceNumber()).isEqualTo(8L);
        assertThat(result.get(3).getSequenceNumber()).isEqualTo(9L);
    }

    @Test
    void shouldReturnEmptyListWhenStartupRecoveryFindsNoEvents() {
        // Given
        long lastProcessedSequence = 10L;
        
        when(recoveryClient.fetchPositionSnapshotEventsAfter(10L))
            .thenReturn(new PositionSnapshotRecoveryResult(List.of(), false));
        
        // When
        List<PositionSnapshot> result = gapRecoveryService.recoverAllMissingEvents(lastProcessedSequence);
        
        // Then
        assertThat(result).isEmpty();
    }

    private PositionSnapshot createPositionSnapshot(long sequenceNumber) {
        return PositionSnapshot.newBuilder()
            .setSequenceNumber(sequenceNumber)
            .build();
    }
}