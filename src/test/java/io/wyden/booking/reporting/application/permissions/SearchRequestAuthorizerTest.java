package io.wyden.booking.reporting.application.permissions;

import io.wyden.accessgateway.client.permission.PermissionLimitException;
import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.WalletType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class SearchRequestAuthorizerTest {

    Set<PermissionDto> permissions = Set.of(
        new PermissionDto("venue.account", "read", "simulator"),
        new PermissionDto("venue.account", "read", "bitmex-testnet1"),
        new PermissionDto("venue.account", "read", "simulator2"),
        new PermissionDto("venue.account", "read", "bitmex-testnet2"),
        new PermissionDto("venue.account", "read", "simulator3"),
        new PermissionDto("venue.account", "read", "bitmex-testnet3"),
        new PermissionDto("portfolio", "read", "Client 1"),
        new PermissionDto("portfolio", "read", "Client 2"),
        new PermissionDto("portfolio", "read", "Client 3"),
        new PermissionDto("portfolio", "read", "Client 4"),
        new PermissionDto("portfolio", "read", "Client 5"),
        new PermissionDto("portfolio", "read", "Bank"),
        new PermissionDto("portfolio", "read", "e2e_BANK_Portfolio"),
        new PermissionDto("portfolio", "read", "e2e_RETAIL_portfolio")
    );

    List<String> requestedAccounts = permissions.stream()
        .filter(p -> p.getResource().equals("venue.account"))
        .filter(p -> p.getScope().equals("read"))
        .map(PermissionDto::getResourceId)
        .limit(2)
        .toList();

    List<String> requestedPortfolios = permissions.stream()
        .filter(p -> p.getResource().equals("portfolio"))
        .filter(p -> p.getScope().equals("read"))
        .map(PermissionDto::getResourceId)
        .limit(2)
        .toList();

    Set<String> resourcesWithDynamicPermissions = permissions.stream()
        .filter(p -> p.getScope().equals("read"))
        .map(PermissionDto::getResourceId)
        .collect(Collectors.toSet());

    Set<String> accountsWithDynamicPermissions = permissions.stream()
        .filter(p -> p.getResource().equals("venue.account"))
        .filter(p -> p.getScope().equals("read"))
        .map(PermissionDto::getResourceId)
        .collect(Collectors.toSet());

    Set<String> portfoliosWithDynamicPermissions = permissions.stream()
        .filter(p -> p.getResource().equals("portfolio"))
        .filter(p -> p.getScope().equals("read"))
        .map(PermissionDto::getResourceId)
        .collect(Collectors.toSet());

    Map<String, Portfolio> portfolios = Map.of(
        "Client 1", Portfolio.newBuilder().setId("Client 1").setPortfolioType(PortfolioType.NOSTRO).build(),
        "Client 2", Portfolio.newBuilder().setId("Client 2").setPortfolioType(PortfolioType.VOSTRO).build(),
        "Client 3", Portfolio.newBuilder().setId("Client 3").setPortfolioType(PortfolioType.NOSTRO).build(),
        "Client 4", Portfolio.newBuilder().setId("Client 4").setPortfolioType(PortfolioType.VOSTRO).build(),
        "Client 5", Portfolio.newBuilder().setId("Client 5").setPortfolioType(PortfolioType.NOSTRO).build(),
        "Bank", Portfolio.newBuilder().setId("Bank").setPortfolioType(PortfolioType.NOSTRO).build(),
        "e2e_BANK_Portfolio", Portfolio.newBuilder().setId("e2e_BANK_Portfolio").setPortfolioType(PortfolioType.VOSTRO).build(),
        "e2e_RETAIL_portfolio", Portfolio.newBuilder().setId("e2e_RETAIL_portfolio").setPortfolioType(PortfolioType.NOSTRO).build()
    );

    Map<String, VenueAccount> accounts = Map.of(
        "simulator", VenueAccount.newBuilder().setId("simulator").setAccountType(AccountType.EXCHANGE).build(),
        "bitmex-testnet1", VenueAccount.newBuilder().setId("bitmex-testnet1").setAccountType(AccountType.EXCHANGE).build(),
        "simulator2", VenueAccount.newBuilder().setId("simulator2").setAccountType(AccountType.EXCHANGE).build(),
        "bitmex-testnet2", VenueAccount.newBuilder().setId("bitmex-testnet2").setAccountType(AccountType.EXCHANGE).build(),
        "simulator3", VenueAccount.newBuilder().setId("simulator3").setAccountType(AccountType.EXCHANGE).build(),
        "bitmex-testnet3", VenueAccount.newBuilder().setId("bitmex-testnet3").setAccountType(AccountType.EXCHANGE).build(),
        "wallet-id", VenueAccount.newBuilder().setId("wallet-id").setAccountType(AccountType.WALLET).build(),
        "wallet-nostro-id", VenueAccount.newBuilder().setId("wallet-nostro-id").setAccountType(AccountType.WALLET).setWalletType(WalletType.WALLET_NOSTRO).build(),
        "wallet-vostro-id", VenueAccount.newBuilder().setId("wallet-vostro-id").setAccountType(AccountType.WALLET).setWalletType(WalletType.WALLET_VOSTRO).build()
    );

    Set<String> requestedPortfoliosVostroIds = portfolios.values().stream()
        .filter(portfolio -> portfolio.getPortfolioType().equals(PortfolioType.VOSTRO))
        .map(Portfolio::getId)
        .limit(2)
        .collect(Collectors.toSet());

    Set<String> requestedPortfoliosNostroIds = portfolios.values().stream()
        .filter(portfolio -> portfolio.getPortfolioType().equals(PortfolioType.NOSTRO))
        .map(Portfolio::getId)
        .limit(2)
        .collect(Collectors.toSet());

    private final AccessGatewayMockClient accessGatewayClient = new AccessGatewayMockClient(new HashSet<>());
    private final AccessGatewayService accessGatewayService = new AccessGatewayService(accessGatewayClient);
    private final SearchRequestAuthorizer searchRequestAuthorizer = new SearchRequestAuthorizer(accessGatewayService, new MockPortfolioProvider(portfolios), new MockVenueAccountProvider(accounts));

    @BeforeEach
    void setup() {
        accessGatewayClient.grantPermissions(permissions);
    }

    @Test
    void emptyRequestWithoutPermissions_authorizedRequestShouldBeNull() {
        accessGatewayClient.revokePermissions();

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all().withClientId("tester"));

        assertThat(authorized).isNull();
    }

    @Test
    void emptyRequestWithOnlyDynamicPermissions_shouldContainsAllResourcesWithDynamicPermission() {
        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all().withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).containsAll(accountsWithDynamicPermissions);
        assertThat(authorized.portfolio()).containsAll(portfoliosWithDynamicPermissions);
        assertThat(authorized.portfolioType()).isNull();
    }

    @Test
    void emptyRequestWithStaticReadPermissions_shouldReturnEmptyRequest() {
        accessGatewayClient.grantStaticPortfolioPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all().withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.ALL);
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void emptyRequestWithStaticReadVostroNostroPermissions_shouldReturnEmptyRequest() {
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all().withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.ALL);
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void emptyRequestWithPortfolioStaticReadPermissions_shouldReturnRequestWithVenueIds() {
        accessGatewayClient.grantStaticPortfolioPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all().withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).containsAll(
            permissions.stream()
                .filter(p -> p.getResource().equals("venue.account"))
                .filter(p -> p.getScope().equals("read"))
                .map(PermissionDto::getResourceId)
                .collect(Collectors.toSet()));
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.ALL);
        assertThat(authorized.reference()).isEmpty();

    }

    @Test
    void emptyRequestWithVenueStaticReadPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all().withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.portfolio()).containsAll(
            permissions.stream()
                .filter(p -> p.getResource().equals("portfolio"))
                .filter(p -> p.getScope().equals("read"))
                .map(PermissionDto::getResourceId)
                .collect(Collectors.toSet()));
        assertThat(authorized.portfolioType()).isNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.reference()).isEmpty();

    }

    @Test
    void emptyRequestWithVenueStaticReadPermissionsAndPortfolioVostroPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all().withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.portfolio()).containsAll(
            permissions.stream()
                .filter(p -> p.getResource().equals("portfolio"))
                .filter(p -> p.getScope().equals("read"))
                .map(PermissionDto::getResourceId)
                .collect(Collectors.toSet()));
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.VOSTRO);
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void emptyRequestWithVenueStaticReadPermissionsAndPortfolioNostroPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all().withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.portfolio()).containsAll(
            permissions.stream()
                .filter(p -> p.getResource().equals("portfolio"))
                .filter(p -> p.getScope().equals("read"))
                .map(PermissionDto::getResourceId)
                .collect(Collectors.toSet()));
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.NOSTRO);
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.reference()).isEmpty();
    }


    // with requested IDs
    @Test
    void requestedAccountsWithOnlyDynamicPermissions_shouldContainsRequestedResourcesWithDynamicPermission() {


        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer
            .authorize(RequestModel.LedgerEntrySearch.all()
                .withAccount(requestedAccounts)
                .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).containsAll(requestedAccounts);
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.reference()).isEmpty();
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.NONE);

    }

    @Test
    void requestedPortfoliosWithOnlyDynamicPermissions_shouldContainsRequestedResourcesWithDynamicPermission() {


        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer
            .authorize(RequestModel.LedgerEntrySearch.all()
                .withPortfolio(requestedPortfolios)
                .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.portfolio()).containsAll(requestedPortfolios);
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.reference()).isEmpty();
        assertThat(authorized.portfolioType()).isNull();
    }

    @Test
    void requestedAccountRequestWithStaticReadPermissions_shouldReturnRequestedResources() {
        accessGatewayClient.grantStaticPortfolioPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withAccount(requestedAccounts)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).containsAll(requestedAccounts);
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.NONE);
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedPortfoliosRequestWithStaticReadPermissions_shouldReturnRequestedResources() {
        accessGatewayClient.grantStaticPortfolioPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withPortfolio(requestedPortfolios)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).containsAll(requestedPortfolios);
        assertThat(authorized.portfolioType()).isNull();
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedAccountsRequestWithStaticReadVostroNostroPermissions_shouldReturnEmptyRequest() {
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withAccount(requestedAccounts)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).containsAll(requestedAccounts);
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.NONE);
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedPortfoliosRequestWithStaticReadVostroNostroPermissions_shouldReturnEmptyRequest() {
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withPortfolio(requestedPortfolios)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).containsAll(requestedPortfolios);
        assertThat(authorized.portfolioType()).isNull();
        assertThat(authorized.reference()).isEmpty();
    }


    @Test
    void requestedAccountsRequestWithPortfolioStaticReadPermissions_shouldReturnRequestWithVenueIds() {
        accessGatewayClient.grantStaticPortfolioPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withAccount(requestedAccounts)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).containsAll(requestedAccounts);
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.NONE);
        assertThat(authorized.reference()).isEmpty();

    }

    @Test
    void requestedPortfoliosRequestWithPortfolioStaticReadPermissions_shouldReturnRequestWithVenueIds() {
        accessGatewayClient.grantStaticPortfolioPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withPortfolio(requestedPortfolios)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).containsAll(requestedPortfolios);
        assertThat(authorized.portfolioType()).isNull();
        assertThat(authorized.reference()).isEmpty();

    }


    @Test
    void requestedAccountsRequestWithVenueStaticReadPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withAccount(requestedAccounts)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.NONE);
        assertThat(authorized.accountId()).containsAll(requestedAccounts);
        assertThat(authorized.reference()).isEmpty();

    }

    @Test
    void requestedPortfoliosRequestWithVenueStaticReadPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withPortfolio(requestedPortfolios)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.portfolio()).containsAll(requestedPortfolios);
        assertThat(authorized.portfolioType()).isNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.reference()).isEmpty();

    }

    @Test
    void requestedAccountsRequestWithVenueStaticReadPermissionsAndPortfolioVostroPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withAccount(requestedAccounts)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).containsAll(requestedAccounts);
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.NONE);
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedPortfoliosRequestWithVenueStaticReadPermissionsAndPortfolioVostroPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withPortfolio(requestedPortfolios)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.portfolio()).containsAll(requestedPortfolios);
        assertThat(authorized.portfolioType()).isNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.reference()).isEmpty();
    }


    @Test
    void requestedAccountsRequestWithVenueStaticReadPermissionsAndPortfolioNostroPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withAccount(requestedAccounts)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).containsAll(requestedAccounts);
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.portfolioType()).isEqualTo(RequestModel.PortfolioType.NONE);
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedPortfoliosRequestWithVenueStaticReadPermissionsAndPortfolioNostroPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withPortfolio(requestedPortfolios)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).containsAll(requestedPortfolios);
        assertThat(authorized.portfolioType()).isNull();
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedOnlyPortfoliosRequestWithVenueStaticReadPermissionsAndPortfolioNostroPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withPortfolio(requestedPortfoliosNostroIds)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).containsAll(requestedPortfoliosNostroIds);
        assertThat(authorized.portfolioType()).isNull();
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedOnlyPortfoliosRequestWithVenueStaticReadPermissionsAndPortfolioVostroPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");

        RequestModel.LedgerEntrySearch authorized = (RequestModel.LedgerEntrySearch) searchRequestAuthorizer.authorize(RequestModel.LedgerEntrySearch.all()
            .withPortfolio(requestedPortfoliosVostroIds)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).containsAll(requestedPortfoliosVostroIds);
        assertThat(authorized.portfolioType()).isNull();
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void emptyRequestWithWalletStaticRead_shouldReturnAccountType() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantPermissions(List.of(
            new PermissionDto("wallet", "read")
        ));

        RequestModel.PositionSearch authorized = (RequestModel.PositionSearch) searchRequestAuthorizer.authorize(RequestModel.PositionSearch.all()
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.accountType()).isEqualTo(RequestModel.AccountType.WALLET);
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void emptyRequestWithWalletVostroStaticRead_shouldReturnWalletType() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantPermissions(List.of(
            new PermissionDto("wallet.vostro", "read")
        ));

        RequestModel.PositionSearch authorized = (RequestModel.PositionSearch) searchRequestAuthorizer.authorize(RequestModel.PositionSearch.all()
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.accountType()).isEqualTo(RequestModel.AccountType.WALLET);
        assertThat(authorized.walletType()).isEqualTo(RequestModel.WalletType.VOSTRO);
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void emptyRequestWithWalletNostroStaticRead_shouldReturnWalletType() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantPermissions(List.of(
            new PermissionDto("wallet.nostro", "read")
        ));

        RequestModel.PositionSearch authorized = (RequestModel.PositionSearch) searchRequestAuthorizer.authorize(RequestModel.PositionSearch.all()
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.accountType()).isEqualTo(RequestModel.AccountType.WALLET);
        assertThat(authorized.walletType()).isEqualTo(RequestModel.WalletType.NOSTRO);
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedWalletsWithWalletNostroStaticRead_shouldReturnRequestWithWalletAccounts() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantPermissions(List.of(
            new PermissionDto("wallet.nostro", "read")
        ));

        List<String> walletIds = accounts.values().stream()
            .filter(va -> va.getAccountType().equals(AccountType.WALLET))
            .map(VenueAccount::getId)
            .toList();

        RequestModel.PositionSearch authorized = (RequestModel.PositionSearch) searchRequestAuthorizer.authorize(RequestModel.PositionSearch.all()
            .withAccount(walletIds)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).containsOnly("wallet-id", "wallet-nostro-id");
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.accountType()).isNull();
        assertThat(authorized.walletType()).isEqualTo(RequestModel.WalletType.NOSTRO);
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedOnlyTypedWalletsWithAccountDynamic_shouldReturnRequestWithWalletAccounts() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantPermissions(List.of(
            new PermissionDto("venue.account", "read", "wallet-nostro-id")
        ));

        List<String> walletIds = accounts.values().stream()
            .filter(va -> !va.getWalletType().equals(WalletType.WALLET_TYPE_UNSPECIFIED))
            .map(VenueAccount::getId)
            .toList();

        RequestModel.PositionSearch authorized = (RequestModel.PositionSearch) searchRequestAuthorizer.authorize(RequestModel.PositionSearch.all()
            .withAccount(walletIds)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).containsOnly("wallet-nostro-id");
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.accountType()).isNull();
        assertThat(authorized.walletType()).isNull();
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedPortfolioIds_withStaticAccountAndWalletRead_shouldReturnRequestWithoutWalletType() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantPermissions(List.of(
            new PermissionDto("portfolio", "read"),
            new PermissionDto("venue.account", "read"),
            new PermissionDto("wallet", "read")
        ));

        List<String> portfolios = this.portfolios.values().stream()
            .map(Portfolio::getId)
            .toList();

        RequestModel.PositionSearch authorized = (RequestModel.PositionSearch) searchRequestAuthorizer.authorize(RequestModel.PositionSearch.all()
            .withPortfolio(portfolios)
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).containsAll(portfolios);
        assertThat(authorized.accountType()).isEqualTo(RequestModel.AccountType.NONE);
        assertThat(authorized.walletType()).isNull();
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedWalletIds_withWalletVostroRead_shouldReturnRequestWithWalletType() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantPermissions(List.of(
            new PermissionDto("wallet.vostro", "read")
        ));

        RequestModel.PositionSearch authorized = (RequestModel.PositionSearch) searchRequestAuthorizer.authorize(RequestModel.PositionSearch.all()
            .withAccount(Set.of("wallet-vostro-id"))
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).contains("wallet-vostro-id");
        assertThat(authorized.portfolio()).isEmpty();
        assertThat(authorized.accountType()).isNull();
        assertThat(authorized.walletType()).isEqualTo(RequestModel.WalletType.VOSTRO);
        assertThat(authorized.reference()).isEmpty();
    }


    @Test
    void requestedPortfolioWhenHasPortfolioStaticNostroAndVostroPermission_shouldReturnRequestWithPortfolioId() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantPermissions(List.of(
            new PermissionDto("portfolio.vostro", "read"),
            new PermissionDto("portfolio.nostro", "read")
        ));

        RequestModel.PositionSearch authorized = (RequestModel.PositionSearch) searchRequestAuthorizer.authorize(RequestModel.PositionSearch.all()
            .withPortfolio(Set.of("Client 1"))
            .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.accountId()).isEmpty();
        assertThat(authorized.portfolio()).contains("Client 1");
        assertThat(authorized.accountType()).isEqualTo(RequestModel.AccountType.NONE);
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedOnlyOneVostroWalletAccounts_shouldReturnRequestWithAccounts() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantPermissions(List.of(
            new PermissionDto("portfolio", "read", "Client 1"),
            new PermissionDto("portfolio", "read", "Client 2"),
            new PermissionDto("portfolio", "read", "Client 3"),
            new PermissionDto("portfolio", "read", "Client 4")));
        accessGatewayClient.grantStaticPortfolioPermission("read");

        RequestModel.PositionSearch authorized = (RequestModel.PositionSearch) searchRequestAuthorizer.authorize(
            RequestModel.PositionSearch.all().withPortfolio(List.of("Client 2"))
                .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.portfolio()).containsOnly("Client 2");
        assertThat(authorized.accountType()).isEqualTo(RequestModel.AccountType.NONE);
        assertThat(authorized.portfolioType()).isNull();
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedOnlyOneVostroWalletAccounts_whenStaticPermisssion_shouldReturnRequestWithAccounts() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantStaticPortfolioPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        RequestModel.PositionSearch authorized = (RequestModel.PositionSearch) searchRequestAuthorizer.authorize(
            RequestModel.PositionSearch.all().withPortfolio(List.of("Client 2"))
                .withClientId("tester"));

        assertThat(authorized).isNotNull();
        assertThat(authorized.portfolio()).containsOnly("Client 2");
        assertThat(authorized.accountType()).isEqualTo(RequestModel.AccountType.NONE);
        assertThat(authorized.portfolioType()).isNull();
        assertThat(authorized.reference()).isEmpty();
    }

    @Test
    void requestedPortfoliosVostroPlusNostroWithVostroPermission_shouldThrowPortfolioLimitException() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantPermissions(List.of(
            new PermissionDto("portfolio", "read", "Client 1"),
            new PermissionDto("portfolio", "read", "Client 2"),
            new PermissionDto("portfolio", "read", "Client 3"),
            new PermissionDto("portfolio", "read", "Client 4")));
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");
        accessGatewayClient.setDynamicPermissionLimit(1);

        RequestModel.PositionSearch search = RequestModel.PositionSearch.all().withPortfolio(List.of("Client 1", "Client 2"))
            .withClientId("tester");

        assertThatThrownBy(() -> searchRequestAuthorizer.authorize(search)).isInstanceOf(PermissionLimitException.class);
    }

    @Test
    void requestedPortfoliosVostroPlusNostroWithVostroPermission_shouldSkipLimitAndReturnRequest() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantPermissions(List.of(
            new PermissionDto("portfolio", "read", "Client 1"),
            new PermissionDto("portfolio", "read", "Client 2"),
            new PermissionDto("portfolio", "read", "Client 3"),
            new PermissionDto("portfolio", "read", "Client 4")));
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");
        accessGatewayClient.setDynamicPermissionLimit(1);

        RequestModel.PositionSearch authorized = (RequestModel.PositionSearch) searchRequestAuthorizer.authorize(
            RequestModel.PositionSearch.all().withPortfolio(List.of("Client 1", "Client 2"))
                .withClientId("tester"));

        assertThat(authorized).isNotNull();
    }


}
