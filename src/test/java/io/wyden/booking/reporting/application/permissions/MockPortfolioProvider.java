package io.wyden.booking.reporting.application.permissions;

import io.wyden.booking.reporting.application.referencedata.PortfolioProvider;
import io.wyden.booking.reporting.application.referencedata.PortfolioSnapshot;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.BalanceFromProtoMapper;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;

import java.util.Map;
import java.util.Optional;

import static org.apache.commons.lang3.StringUtils.isBlank;

public class MockPortfolioProvider implements PortfolioProvider {

    private final Map<String, Portfolio> portfolios;

    public MockPortfolioProvider(Map<String, Portfolio> portfolios) {
        this.portfolios = portfolios;
    }

    @Override
    public String getPortfolioCurrency(String portfolioId) {
        String currency = portfolios.get(portfolioId).getPortfolioCurrency();
        if (isBlank(currency)) {
            throw new IllegalArgumentException("Portfolio currency not found for: " + portfolioId);
        }

        return currency;
    }

    @Override
    public Portfolio getPortfolio(String portfolioId) {
        return portfolios.get(portfolioId);
    }

    public void setPortfolio(String portfolioId, String currency, PortfolioType portfolioType) {
        portfolios.put(portfolioId, Portfolio.newBuilder()
            .setId(portfolioId)
            .setPortfolioCurrency(currency)
            .setPortfolioType(portfolioType)
            .build());
    }

    @Override
    public Optional<PortfolioSnapshot> getPortfolioSnapshot(String portfolioId) {
        return Optional.ofNullable(portfolios.get(portfolioId))
            .map(portfolio -> new PortfolioSnapshot(
                portfolioId,
                portfolio.getPortfolioCurrency(),
                BalanceFromProtoMapper.map(portfolio.getPortfolioType())));
    }

    @Override
    public RequestModel.PortfolioType getPortfolioType(String portfolioId) {
        if (isBlank(portfolioId)) {
            return null;
        }

        return getPortfolioSnapshot(portfolioId)
            .map(PortfolioSnapshot::portfolioType)
            .orElse(null);
    }
}
