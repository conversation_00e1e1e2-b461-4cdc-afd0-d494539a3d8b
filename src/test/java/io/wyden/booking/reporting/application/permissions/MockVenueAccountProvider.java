package io.wyden.booking.reporting.application.permissions;

import io.wyden.booking.reporting.application.referencedata.AccountSnapshot;
import io.wyden.booking.reporting.application.referencedata.VenueAccountProvider;
import io.wyden.booking.reporting.domain.rate.SystemCurrencyProvider;
import io.wyden.published.referencedata.VenueAccount;

import java.util.Map;
import java.util.Optional;

import static io.wyden.booking.reporting.interfaces.rabbitmq.mappers.BalanceFromProtoMapper.map;
import static org.apache.commons.lang3.StringUtils.isBlank;

public class MockVenueAccountProvider implements VenueAccountProvider {
    private final Map<String, VenueAccount> venueAccountMap;

    public MockVenueAccountProvider(Map<String, VenueAccount> venueAccountMap) {
        this.venueAccountMap = venueAccountMap;
    }

    @Override
    public Optional<VenueAccount> getVenueAccount(String accountId) {
        return venueAccountMap.containsKey(accountId) ? Optional.of(venueAccountMap.get(accountId)) : Optional.empty();
    }

    @Override
    public Optional<AccountSnapshot> getAccountSnapshot(String accountId) {
        if (isBlank(accountId)) {
            return Optional.empty();
        }

        return getVenueAccount(accountId)
            .map(account -> new AccountSnapshot(
                account.getId(),
                SystemCurrencyProvider.getSystemCurrency(),
                map(account.getAccountType()),
                map(account.getWalletType())));
    }

    public void setVenueAccount(VenueAccount venueAccount) {
        this.venueAccountMap.put(venueAccount.getId(), venueAccount);
    }


}
