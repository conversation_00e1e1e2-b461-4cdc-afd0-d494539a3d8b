package io.wyden.booking.reporting.application.recovery;

import io.wyden.cloudutils.rabbitmq.InfrastructureException;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.PageInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BookingCompletedRecoveryClientTest {

    @Mock
    private RestTemplate restTemplate;

    private BookingCompletedRecoveryClient recoveryClient;

    @BeforeEach
    void setUp() {
        recoveryClient = new BookingCompletedRecoveryClient(
            restTemplate,
            "http://booking-snapshotter:8043",
            100
        );
    }

    @Test
    void shouldFetchBookingCompletedEventsSuccessfully() {
        // Given
        long afterSeqNumber = 5L;
        BookingCompleted event1 = createBookingCompleted(6L);
        BookingCompleted event2 = createBookingCompleted(7L);
        
        CursorConnection response = createCursorConnection(
            List.of(event1, event2),
            true
        );
        
        when(restTemplate.getForEntity(any(URI.class), eq(CursorConnection.class)))
            .thenReturn(ResponseEntity.ok(response));
        
        // When
        BookingCompletedRecoveryResult result = recoveryClient.fetchBookingCompletedEventsAfter(afterSeqNumber);
        
        // Then
        assertThat(result.getBookingCompletedEvents()).hasSize(2);
        assertThat(result.getBookingCompletedEvents().get(0).getSequenceNumber()).isEqualTo(6L);
        assertThat(result.getBookingCompletedEvents().get(1).getSequenceNumber()).isEqualTo(7L);
        assertThat(result.hasMoreEvents()).isTrue();
    }

    @Test
    void shouldReturnEmptyResultWhenNoEvents() {
        // Given
        long afterSeqNumber = 5L;
        CursorConnection response = createCursorConnection(List.of(), false);
        
        when(restTemplate.getForEntity(any(URI.class), eq(CursorConnection.class)))
            .thenReturn(ResponseEntity.ok(response));
        
        // When
        BookingCompletedRecoveryResult result = recoveryClient.fetchBookingCompletedEventsAfter(afterSeqNumber);
        
        // Then
        assertThat(result.getBookingCompletedEvents()).isEmpty();
        assertThat(result.hasMoreEvents()).isFalse();
    }

    @Test
    void shouldReturnEmptyResultWhenResponseIsNull() {
        // Given
        long afterSeqNumber = 5L;
        
        when(restTemplate.getForEntity(any(URI.class), eq(CursorConnection.class)))
            .thenReturn(ResponseEntity.ok(null));
        
        // When
        BookingCompletedRecoveryResult result = recoveryClient.fetchBookingCompletedEventsAfter(afterSeqNumber);
        
        // Then
        assertThat(result.getBookingCompletedEvents()).isEmpty();
        assertThat(result.hasMoreEvents()).isFalse();
    }

    @Test
    void shouldThrowExceptionWhenRestCallFails() {
        // Given
        long afterSeqNumber = 5L;
        
        when(restTemplate.getForEntity(any(URI.class), eq(CursorConnection.class)))
            .thenThrow(new InfrastructureException("REST call failed"));
        
        // When & Then
        assertThatThrownBy(() -> recoveryClient.fetchBookingCompletedEventsAfter(afterSeqNumber))
            .isInstanceOf(BookingCompletedRecoveryClient.BookingCompletedRecoveryException.class)
            .hasMessageContaining("Failed to fetch BookingCompleted events after 5");
    }

    @Test
    void shouldFilterOnlyBookingCompletedEvents() {
        // Given
        long afterSeqNumber = 5L;
        BookingCompleted bookingCompleted = createBookingCompleted(6L);
        
        // Create a mix of nodes - some with BookingCompleted, some without
        CursorNode nodeWithBookingCompleted = CursorNode.newBuilder()
            .setBookingCompleted(bookingCompleted)
            .build();
        
        CursorNode nodeWithoutBookingCompleted = CursorNode.newBuilder()
            .build();
        
        CursorEdge edge1 = CursorEdge.newBuilder()
            .setNode(nodeWithBookingCompleted)
            .build();
        
        CursorEdge edge2 = CursorEdge.newBuilder()
            .setNode(nodeWithoutBookingCompleted)
            .build();
        
        CursorConnection response = CursorConnection.newBuilder()
            .addAllEdges(List.of(edge1, edge2))
            .setPageInfo(PageInfo.newBuilder().setHasNextPage(false).build())
            .build();
        
        when(restTemplate.getForEntity(any(URI.class), eq(CursorConnection.class)))
            .thenReturn(ResponseEntity.ok(response));
        
        // When
        BookingCompletedRecoveryResult result = recoveryClient.fetchBookingCompletedEventsAfter(afterSeqNumber);
        
        // Then
        assertThat(result.getBookingCompletedEvents()).hasSize(1);
        assertThat(result.getBookingCompletedEvents().get(0).getSequenceNumber()).isEqualTo(6L);
    }

    private BookingCompleted createBookingCompleted(long sequenceNumber) {
        return BookingCompleted.newBuilder()
            .setSequenceNumber(sequenceNumber)
            .build();
    }

    private CursorConnection createCursorConnection(List<BookingCompleted> events, boolean hasNextPage) {
        CursorConnection.Builder builder = CursorConnection.newBuilder();
        
        for (BookingCompleted event : events) {
            CursorNode node = CursorNode.newBuilder()
                .setBookingCompleted(event)
                .build();
            
            CursorEdge edge = CursorEdge.newBuilder()
                .setNode(node)
                .build();
            
            builder.addEdges(edge);
        }
        
        PageInfo pageInfo = PageInfo.newBuilder()
            .setHasNextPage(hasNextPage)
            .build();
        
        return builder.setPageInfo(pageInfo).build();
    }
}