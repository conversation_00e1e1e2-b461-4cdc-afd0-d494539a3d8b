package io.wyden.booking.reporting.application.permissions;

import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.accessgateway.client.permission.PermissionLimitException;
import io.wyden.accessgateway.client.permission.WydenRole;
import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.EMPTY;

public class AccessGatewayMockClient implements AccessGatewayFacade {

    private final Set<PermissionDto> permissions;
    private int dynamicPermissionLimit = Integer.MAX_VALUE;

    public AccessGatewayMockClient(Set<PermissionDto> permissions) {
        this.permissions = permissions;
    }

    @Override
    public User getUserAttributes(String username) {
        return new User(username, Set.of(), Set.of());
    }

    @Override
    public boolean hasPermission(Set<WydenRole> roles, Set<String> groups, String username, Permission permission) {
        return permissions.contains(new PermissionDto(permission.getResource(), permission.getScope(), null));
    }

    @Override
    public Set<String> getPermittedResourceIds(Set<String> groups, String username, String resource, String scope) {
        Set<String> ids = permissions.stream()
            .filter(p -> p.getResource().equals(resource) && p.getScope().equals(scope))
            .map(PermissionDto::getResourceId)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());

        if (ids.size() > dynamicPermissionLimit) {
            throw new PermissionLimitException(String.format("User %s permitted %s:%s has exceeded limit of %d", username, resource, scope, dynamicPermissionLimit));
        } else {
            return ids;
        }
    }

    private io.wyden.published.security.Permission toPermission(PermissionDto dto) {
        return io.wyden.published.security.Permission.newBuilder()
            .setResource(dto.getResource())
            .setScope(dto.getScope())
            .setResourceId(ObjectUtils.firstNonNull(dto.getResourceId(), EMPTY))
            .build();
    }

    public Collection<PermissionDto> revokePermissions() {
        permissions.clear();
        dynamicPermissionLimit = Integer.MAX_VALUE;
        return permissions;
    }

    public Collection<PermissionDto> grantPermissions(Collection<PermissionDto> permissions) {
        revokePermissions();
        this.permissions.addAll(permissions);
        return this.permissions;
    }

    public Collection<PermissionDto> grantStaticPortfolioPermission(String scope) {
        this.permissions.add(new PermissionDto("portfolio", scope));
        return this.permissions;
    }

    public Collection<PermissionDto> grantStaticPortfolioVostroPermission(String scope) {
        this.permissions.add(new PermissionDto("portfolio.vostro", scope));
        return this.permissions;
    }

    public Collection<PermissionDto> grantStaticPortfolioNostroPermission(String scope) {
        this.permissions.add(new PermissionDto("portfolio.nostro", scope));
        return this.permissions;
    }

    public Collection<PermissionDto> grantStaticWalletPermission(String scope) {
        this.permissions.add(new PermissionDto("wallet", scope));
        return this.permissions;
    }

    public Collection<PermissionDto> grantStaticWalletVostroPermission(String scope) {
        this.permissions.add(new PermissionDto("wallet.vostro", scope));
        return this.permissions;
    }

    public Collection<PermissionDto> grantStaticWalletNostroPermission(String scope) {
        this.permissions.add(new PermissionDto("wallet.nostro", scope));
        return this.permissions;
    }

    public Collection<PermissionDto> grantStaticVenueAccountPermission(String scope) {
        this.permissions.add(new PermissionDto("venue.account", scope));
        return this.permissions;
    }

    public void setDynamicPermissionLimit(int limit) {
        this.dynamicPermissionLimit = limit;
    }
}
