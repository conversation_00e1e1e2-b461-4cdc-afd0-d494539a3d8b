package io.wyden.booking.reporting.application.recovery;

import io.wyden.published.booking.PositionSnapshot;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.PageInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PositionSnapshotRecoveryClientTest {

    @Mock
    private RestTemplate restTemplate;

    private PositionSnapshotRecoveryClient recoveryClient;

    private static final String BOOKING_PNL_URL = "http://booking-pnl:8080";
    private static final int BATCH_SIZE = 50;

    @BeforeEach
    void setUp() {
        recoveryClient = new PositionSnapshotRecoveryClient(restTemplate, BOOKING_PNL_URL, BATCH_SIZE);
    }

    @Test
    void shouldFetchPositionSnapshotEventsSuccessfully() {
        // Given
        long afterSeqNumber = 10L;
        
        PositionSnapshot position1 = createPositionSnapshot(11L);
        PositionSnapshot position2 = createPositionSnapshot(12L);
        
        CursorConnection response = CursorConnection.newBuilder()
            .addEdges(CursorEdge.newBuilder()
                .setNode(CursorNode.newBuilder().setPosition(position1).build())
                .build())
            .addEdges(CursorEdge.newBuilder()
                .setNode(CursorNode.newBuilder().setPosition(position2).build())
                .build())
            .setPageInfo(PageInfo.newBuilder().setHasNextPage(true).build())
            .build();

        URI expectedUri = URI.create("http://booking-pnl:8080/position-snapshot/search?first=50&after=10");
        when(restTemplate.getForEntity(expectedUri, CursorConnection.class))
            .thenReturn(ResponseEntity.ok(response));

        // When
        PositionSnapshotRecoveryResult result = recoveryClient.fetchPositionSnapshotEventsAfter(afterSeqNumber);

        // Then
        assertThat(result.getPositionSnapshotEvents()).hasSize(2);
        assertThat(result.getPositionSnapshotEvents().get(0).getSequenceNumber()).isEqualTo(11L);
        assertThat(result.getPositionSnapshotEvents().get(1).getSequenceNumber()).isEqualTo(12L);
        assertThat(result.hasMoreEvents()).isTrue();
    }

    @Test
    void shouldReturnEmptyListWhenNoEventsFound() {
        // Given
        long afterSeqNumber = 100L;
        
        CursorConnection response = CursorConnection.newBuilder()
            .setPageInfo(PageInfo.newBuilder().setHasNextPage(false).build())
            .build();

        URI expectedUri = URI.create("http://booking-pnl:8080/position-snapshot/search?first=50&after=100");
        when(restTemplate.getForEntity(expectedUri, CursorConnection.class))
            .thenReturn(ResponseEntity.ok(response));

        // When
        PositionSnapshotRecoveryResult result = recoveryClient.fetchPositionSnapshotEventsAfter(afterSeqNumber);

        // Then
        assertThat(result.getPositionSnapshotEvents()).isEmpty();
        assertThat(result.hasMoreEvents()).isFalse();
    }

    @Test
    void shouldHandleNullResponseFromService() {
        // Given
        long afterSeqNumber = 10L;
        
        URI expectedUri = URI.create("http://booking-pnl:8080/position-snapshot/search?first=50&after=10");
        when(restTemplate.getForEntity(expectedUri, CursorConnection.class))
            .thenReturn(ResponseEntity.ok(null));

        // When
        PositionSnapshotRecoveryResult result = recoveryClient.fetchPositionSnapshotEventsAfter(afterSeqNumber);

        // Then
        assertThat(result.getPositionSnapshotEvents()).isEmpty();
        assertThat(result.hasMoreEvents()).isFalse();
    }

    @Test
    void shouldFilterOnlyPositionSnapshotEvents() {
        // Given
        long afterSeqNumber = 10L;
        
        PositionSnapshot position = createPositionSnapshot(11L);
        
        CursorConnection response = CursorConnection.newBuilder()
            .addEdges(CursorEdge.newBuilder()
                .setNode(CursorNode.newBuilder().setPosition(position).build())
                .build())
            .addEdges(CursorEdge.newBuilder()
                .setNode(CursorNode.newBuilder().build()) // Node without position
                .build())
            .setPageInfo(PageInfo.newBuilder().setHasNextPage(false).build())
            .build();

        URI expectedUri = URI.create("http://booking-pnl:8080/position-snapshot/search?first=50&after=10");
        when(restTemplate.getForEntity(expectedUri, CursorConnection.class))
            .thenReturn(ResponseEntity.ok(response));

        // When
        PositionSnapshotRecoveryResult result = recoveryClient.fetchPositionSnapshotEventsAfter(afterSeqNumber);

        // Then
        assertThat(result.getPositionSnapshotEvents()).hasSize(1);
        assertThat(result.getPositionSnapshotEvents().get(0).getSequenceNumber()).isEqualTo(11L);
        assertThat(result.hasMoreEvents()).isFalse();
    }

    @Test
    void shouldThrowExceptionWhenRestCallFails() {
        // Given
        long afterSeqNumber = 10L;
        
        URI expectedUri = URI.create("http://booking-pnl:8080/position-snapshot/search?first=50&after=10");
        when(restTemplate.getForEntity(expectedUri, CursorConnection.class))
            .thenThrow(new RestClientException("Connection failed"));

        // When & Then
        assertThatThrownBy(() -> recoveryClient.fetchPositionSnapshotEventsAfter(afterSeqNumber))
            .isInstanceOf(PositionSnapshotRecoveryClient.PositionSnapshotRecoveryException.class)
            .hasMessageContaining("Failed to fetch PositionSnapshot events after 10")
            .hasCauseInstanceOf(RestClientException.class);
    }

    @Test
    void shouldUseDefaultBatchSizeWhenConfiguredBatchSizeIsNull() {
        // Given
        PositionSnapshotRecoveryClient clientWithNullBatchSize = 
            new PositionSnapshotRecoveryClient(restTemplate, BOOKING_PNL_URL, null);
        
        long afterSeqNumber = 10L;
        
        CursorConnection response = CursorConnection.newBuilder()
            .setPageInfo(PageInfo.newBuilder().setHasNextPage(false).build())
            .build();

        // The URI should use default batch size of 100
        URI expectedUri = URI.create("http://booking-pnl:8080/position-snapshot/search?first=100&after=10");
        when(restTemplate.getForEntity(expectedUri, CursorConnection.class))
            .thenReturn(ResponseEntity.ok(response));

        // When
        PositionSnapshotRecoveryResult result = clientWithNullBatchSize.fetchPositionSnapshotEventsAfter(afterSeqNumber);

        // Then
        assertThat(result.getPositionSnapshotEvents()).isEmpty();
        assertThat(result.hasMoreEvents()).isFalse();
    }

    @Test
    void shouldHandleMultiplePositionSnapshotsInResponse() {
        // Given
        long afterSeqNumber = 5L;
        
        PositionSnapshot position1 = createPositionSnapshot(6L);
        PositionSnapshot position2 = createPositionSnapshot(7L);
        PositionSnapshot position3 = createPositionSnapshot(8L);
        
        CursorConnection response = CursorConnection.newBuilder()
            .addEdges(CursorEdge.newBuilder()
                .setNode(CursorNode.newBuilder().setPosition(position1).build())
                .build())
            .addEdges(CursorEdge.newBuilder()
                .setNode(CursorNode.newBuilder().setPosition(position2).build())
                .build())
            .addEdges(CursorEdge.newBuilder()
                .setNode(CursorNode.newBuilder().setPosition(position3).build())
                .build())
            .setPageInfo(PageInfo.newBuilder().setHasNextPage(false).build())
            .build();

        URI expectedUri = URI.create("http://booking-pnl:8080/position-snapshot/search?first=50&after=5");
        when(restTemplate.getForEntity(expectedUri, CursorConnection.class))
            .thenReturn(ResponseEntity.ok(response));

        // When
        PositionSnapshotRecoveryResult result = recoveryClient.fetchPositionSnapshotEventsAfter(afterSeqNumber);

        // Then
        assertThat(result.getPositionSnapshotEvents()).hasSize(3);
        assertThat(result.getPositionSnapshotEvents().get(0).getSequenceNumber()).isEqualTo(6L);
        assertThat(result.getPositionSnapshotEvents().get(1).getSequenceNumber()).isEqualTo(7L);
        assertThat(result.getPositionSnapshotEvents().get(2).getSequenceNumber()).isEqualTo(8L);
        assertThat(result.hasMoreEvents()).isFalse();
    }

    private PositionSnapshot createPositionSnapshot(long sequenceNumber) {
        return PositionSnapshot.newBuilder()
            .setSequenceNumber(sequenceNumber)
            .build();
    }
}