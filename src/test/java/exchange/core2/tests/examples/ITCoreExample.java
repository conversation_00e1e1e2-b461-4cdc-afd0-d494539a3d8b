package exchange.core2.tests.examples;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import exchange.core2.collections.objpool.ObjectsPool;
import exchange.core2.core.IEventsHandler;
import exchange.core2.core.SimpleEventsProcessor;
import exchange.core2.core.common.CoreSymbolSpecification;
import exchange.core2.core.common.L2MarketData;
import exchange.core2.core.common.MatcherTradeEvent;
import exchange.core2.core.common.OrderAction;
import exchange.core2.core.common.OrderType;
import exchange.core2.core.common.SymbolType;
import exchange.core2.core.common.api.binary.BatchAddSymbolsCommand;
import exchange.core2.core.common.api.reports.SingleUserReportQuery;
import exchange.core2.core.common.api.reports.SingleUserReportResult;
import exchange.core2.core.common.cmd.CommandResultCode;
import exchange.core2.core.common.cmd.OrderCommand;
import exchange.core2.core.common.cmd.OrderCommandType;
import exchange.core2.core.common.config.ExchangeConfiguration;
import exchange.core2.core.orderbook.IOrderBook;
import exchange.core2.core.orderbook.OrderBookDirectImpl;
import exchange.core2.core.processors.MatchingEngineRouter;
import exchange.core2.core.processors.SharedPool;
import exchange.core2.core.processors.journaling.DummySerializationProcessor;
import exchange.core2.core.processors.journaling.ISerializationProcessor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ITCoreExample {

    private static final int XBT = 11;
    private static final int LTC = 15;
    private static final int XBT_LTC = 241;

    private SimpleEventsProcessor eventsProcessor;
    private ObjectsPool objectsPool;
    private MatchingEngineRouter router;

    private OrderCommand cmd = new OrderCommand();
    private long correlation = 0;
    private MatcherTradeEvent currentEvent;
    private MatcherTradeEvent nextEvent;

    @BeforeEach
    public void setup() {

        eventsProcessor = new SimpleEventsProcessor(new IEventsHandler() {

            @Override
            public void addSymbolSpecificationResult(long correlationId) {
                System.out.println(String.format("addSymbolSpecificationResult correlationId %d", correlationId));
            }

            @Override
            public void placeOrderResult(long price, long size, long acceptedAmount, long orderId, OrderAction action, OrderType orderType, long uid, int symbol, CommandResultCode resultCode, int serviceFlags, long orderUuidMostSigBits, long orderUuidLeastSigBits, long timestamp, long seq) {
                System.out.println(String.format("placeOrderResult price %d, size %d, acceptedAmount %d, orderId %d, action %s, orderType %s, uid %d, symbol %d, resultCode %s, serviceFlags %d, timestamp %d, seq %d", price, size, acceptedAmount, orderId, action, orderType, uid, symbol, resultCode, serviceFlags, timestamp, seq));
            }

            @Override
            public void moveOrderResult(long orderId, long newPrice, long uid, int symbol, CommandResultCode resultCode, int serviceFlags, long orderUuidMostSigBits, long orderUuidLeastSigBits, long timestamp, long seq) {
                System.out.println(String.format("moveOrderResult orderId %d, newPrice %d, uid %d, symbol %d, resultCode %s, serviceFlags %d, timestamp %d, seq %d", orderId, newPrice, uid, symbol, resultCode, serviceFlags, timestamp, seq));

            }

            @Override
            public void reduceOrderResult(long orderId, long uid, int symbol, long reduceSize, CommandResultCode resultCode, int serviceFlags, long orderUuidMostSigBits, long orderUuidLeastSigBits, long timestamp, long seq) {
                System.out.println(String.format("reduceOrderResult orderId %d, uid %d, symbol %d, reduceSize %d, resultCode %s, serviceFlags %d, timestamp %d, seq %d", orderId, uid, symbol, reduceSize, resultCode, serviceFlags, timestamp, seq));
            }

            @Override
            public void cancelOrderResult(long orderId, long uid, int symbol, CommandResultCode resultCode, int serviceFlags, long orderUuidMostSigBits, long orderUuidLeastSigBits, long timestamp, long seq) {
                System.out.println(String.format("cancelOrderResult orderId %d, uid %d, symbol %d, resultCode %s, serviceFlags %d, serviceFlags %d, timestamp %d, seq %d", orderId, uid, symbol, resultCode, serviceFlags, serviceFlags, timestamp, seq));
            }

            @Override
            public void orderBookResult(int symbol, int size, CommandResultCode resultCode, long timestamp, long seq) {
                System.out.println(String.format("orderBookResult symbol %d, size %d, resultCode %s, timestamp %d, seq %d", symbol, size, resultCode, timestamp, seq));
            }

            @Override
            public void tradeEvent(int symbol, long price, long totalVolume, long takerOrderId, long takerUid, long takerTradeId, long matchId, OrderAction takerAction, boolean takerOrderCompleted, long takerOrderUuidMostSigBits, long takerOrderUuidLeastSigBits, long timestamp, int tradesCount, List<MatcherTradeEvent> matcherTradeEvents) {
                System.out.println(String.format("tradeEvent symbol %d, price %d, totalVolume %d, takerOrderId %d, takerUid %d, takerTradeId %d, matchId %d, takerAction %s, takerOrderCompleted %s, timestamp %d, tradesCount%d, matcherTradeEvents %s", symbol, price, totalVolume, takerOrderId, takerUid, takerTradeId, matchId, takerAction, takerOrderCompleted, timestamp, tradesCount, matcherTradeEvents));
            }

            @Override
            public void reduceEvent(int symbol, long reducedVolume, boolean orderCompleted, long orderUuidMostSigBits, long orderUuidLeastSigBits, long price, long orderId, long uid, long timestamp) {
                System.out.println(String.format("reduceEvent symbol %d, reducedVolume %d, orderCompleted %s, price %d, orderId %d, uid %d", symbol, reducedVolume, orderCompleted, price, orderId, uid));
            }

            @Override
            public void rejectEvent(int symbol, long rejectedVolume, long orderUuidMostSigBits, long orderUuidLeastSigBits, long price, long orderId, long uid, long timestamp) {
                System.out.println(String.format("rejectEvent symbol %d, rejectedVolume %d, price %d, orderId %d, uid %d, timestamp %d", symbol, rejectedVolume, price, orderId, uid, timestamp));
            }

            @Override
            public void orderBook(int symbol, L2MarketData marketData, long timestamp, long seq) {
                System.out.println(String.format("orderBook symbol %d, marketData %s, timestamp %d, seq %d",  symbol, marketData, timestamp, seq));
            }
        }, 1000);

        final ExchangeConfiguration exchangeConf = ExchangeConfiguration.defaultBuilder().build();
        final SharedPool sharedPool = new SharedPool(32, 32, 1024);
        final ISerializationProcessor serializationProcessor = DummySerializationProcessor.INSTANCE;
        final IOrderBook.OrderBookFactory orderBookFactory = OrderBookDirectImpl::new;

        final HashMap<Integer, Integer> objectsPoolConfig = new HashMap<>();
        objectsPoolConfig.put(ObjectsPool.ORDER, 1024 * 1024);
        objectsPoolConfig.put(ObjectsPool.DIRECT_ORDER, 1024 * 1024);
        objectsPoolConfig.put(ObjectsPool.DIRECT_BUCKET, 1024 * 64);
        objectsPoolConfig.put(ObjectsPool.MATCHER_TRADE_EVENT, 1024);
        objectsPoolConfig.put(ObjectsPool.ART_NODE_4, 1024 * 32);
        objectsPoolConfig.put(ObjectsPool.ART_NODE_16, 1024 * 16);
        objectsPoolConfig.put(ObjectsPool.ART_NODE_48, 1024 * 8);
        objectsPoolConfig.put(ObjectsPool.ART_NODE_256, 1024 * 4);

        objectsPool = new ObjectsPool(objectsPoolConfig);
        router = new MatchingEngineRouter(serializationProcessor, orderBookFactory, sharedPool, exchangeConf, objectsPool);
    }

    @Test
    public void sampleTest() throws Exception {

        // create symbol specification and publish it
        final CoreSymbolSpecification symbolSpecXbtLtc = CoreSymbolSpecification.builder()
                .symbolId(XBT_LTC) // symbol id
                .type(SymbolType.CURRENCY_EXCHANGE_PAIR)
                .baseCurrency(XBT) // base = satoshi (1E-8)
                .quoteCurrency(LTC) // quote = litoshi (1E-8)
                .build();

        addSymbol(symbolSpecXbtLtc);

        // first user places Good-till-Cancel Bid order
        // he assumes BTCLTC exchange rate 154 LTC for 1 BTC
        // bid price for 1 lot (0.01BTC) is 1.54 LTC => 1_5400_0000 litoshi => 10K * 15_400 (in price steps)
        placeOrder(
                301L,
                5001L,
                15_400L,
                12L, // order size is 12 lots
                OrderAction.BID,
                OrderType.GTC, // Good-till-Cancel
                XBT_LTC);

        // second user places Immediate-or-Cancel Ask (Sell) order
        // he assumes wost rate to sell 152.5 LTC for 1 BTC
        placeOrder(
                302L,
                5002L,
                15_250L,
                10L, // order size is 10 lots
                OrderAction.ASK,
                OrderType.IOC, // Immediate-or-Cancel
                XBT_LTC);

        // request order book
        requestOrderBook(XBT_LTC, 10);

        // check open orders
        System.out.println("SingleUserReportQuery 1: " + singleUserReportsQuery(new SingleUserReportQuery(301)).get().getOrders());
        System.out.println("SingleUserReportQuery 2: " + singleUserReportsQuery(new SingleUserReportQuery(302)).get().getOrders());

        // first user moves remaining order to price 1.53 LTC
        moveOrder(
                301L,
                5001L,
                XBT_LTC,
                15_300L);


        // first user reduces order
        reduceOrder(
                301L,
                5001L,
                XBT_LTC,
                1);

        // first user cancel remaining order
        cancelOrder(
                301L,
                5001L,
                XBT_LTC);

        System.out.println("SingleUserReportQuery 1: " + singleUserReportsQuery(new SingleUserReportQuery(301)).get().getOrders());
        System.out.println("SingleUserReportQuery 2: " + singleUserReportsQuery(new SingleUserReportQuery(302)).get().getOrders());
    }

    private void addSymbol(final CoreSymbolSpecification symbolSpecXbtLtc) {
        router.handleBinaryMessage(new BatchAddSymbolsCommand(symbolSpecXbtLtc));
    }

    private void placeOrder(long uid, long orderId, long price, long size, OrderAction action, OrderType orderType, int symbol) {
        cmd.command = OrderCommandType.PLACE_ORDER;
        cmd.uid = uid;
        cmd.orderId = orderId;
        cmd.price = price;
        cmd.size = size;
        cmd.action = action;
        cmd.orderType = orderType;
        cmd.symbol = symbol;
        cmd.timestamp = System.currentTimeMillis();
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;

        processCommand();
    }

    private void moveOrder(long uid, long orderId, int symbol, long newPrice) {
        cmd.command = OrderCommandType.MOVE_ORDER;
        cmd.uid = uid;
        cmd.orderId = orderId;
        cmd.symbol = symbol;
        cmd.price = newPrice;
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;

        processCommand();
    }

    private void reduceOrder(long uid, long orderId, int symbol, long reduceSize) {
        cmd.command = OrderCommandType.REDUCE_ORDER;
        cmd.uid = uid;
        cmd.orderId = orderId;
        cmd.symbol = symbol;
        cmd.size = reduceSize;
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;

        processCommand();
    }

    private void cancelOrder(long uid, long orderId, int symbol) {
        cmd.command = OrderCommandType.CANCEL_ORDER;
        cmd.uid = uid;
        cmd.orderId = orderId;
        cmd.symbol = symbol;
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;

        processCommand();
    }

    private void requestOrderBook(int symbol, int depth) {

        cmd.command = OrderCommandType.ORDER_BOOK_REQUEST;
        cmd.symbol = symbol;
        cmd.size = depth;

        processCommand();
    }

    private Optional<SingleUserReportResult> singleUserReportsQuery(SingleUserReportQuery singleUserReportQuery) {
        return router.handleReportQuery(singleUserReportQuery);
    }

    private void processCommand() {

        router.processOrder(correlation, cmd);

        eventsProcessor.accept(cmd, correlation, false);

        currentEvent = cmd.matcherEvent;
        if (currentEvent != null) {
            do {
                nextEvent = currentEvent.nextEvent;
                currentEvent.eventType = null;
                currentEvent.nextEvent = null;
                objectsPool.put(ObjectsPool.MATCHER_TRADE_EVENT, currentEvent);
                currentEvent = nextEvent;
            } while (currentEvent != null);
        }

        cmd.userCookie = 0;
        cmd.timestamp = 0;
        cmd.resultCode = null;
        cmd.orderId = 0;
        cmd.symbol = 0;
        cmd.price = 0;
        cmd.size = 0;
        cmd.action = null;
        cmd.orderType = null;
        cmd.uid = 0;
        cmd.timestamp = 0;
        cmd.userCookie = 0;
        cmd.resultCode = null;
        cmd.matcherEvent = null;
        cmd.marketData = null;

        correlation += 1;
    }
}
