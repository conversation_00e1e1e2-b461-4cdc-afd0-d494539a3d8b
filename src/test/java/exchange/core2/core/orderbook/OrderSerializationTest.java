package exchange.core2.core.orderbook;

import net.openhft.chronicle.bytes.Bytes;
import net.openhft.chronicle.bytes.BytesIn;
import net.openhft.chronicle.bytes.BytesOut;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import exchange.core2.core.common.Order;
import exchange.core2.core.common.OrderAction;
import exchange.core2.core.orderbook.OrderBookDirectImpl.DirectOrder;

public class OrderSerializationTest {

    @Test
    public void testDirectOrderSerialization() {
        // Create a new format order
        DirectOrder order = DirectOrder.builder()
            .orderId(123L)
            .price(1000L)
            .size(500L)
            .maxAmount(1000L)
            .filled(100L)
            .action(OrderAction.ASK)
            .uid(456L)
            .timestamp(789L)
            .orderUuidMostSigBits(111L)
            .orderUuidLeastSigBits(222L)
            .build();

        // Serialize
        Bytes bytes = Bytes.allocateElasticOnHeap();
        order.writeMarshallable(bytes);

        // Deserialize
        bytes.readPosition(0);
        DirectOrder deserialized = new DirectOrder(bytes);

        // Verify all fields
        assertEquals(order.getOrderId(), deserialized.getOrderId());
        assertEquals(order.getPrice(), deserialized.getPrice());
        assertEquals(order.getSize(), deserialized.getSize());
        assertEquals(order.getMaxAmount(), deserialized.getMaxAmount());
        assertEquals(order.getFilled(), deserialized.getFilled());
        assertEquals(order.getAction(), deserialized.getAction());
        assertEquals(order.getUid(), deserialized.getUid());
        assertEquals(order.getTimestamp(), deserialized.getTimestamp());
        assertEquals(order.getOrderUuidMostSigBits(), deserialized.getOrderUuidMostSigBits());
        assertEquals(order.getOrderUuidLeastSigBits(), deserialized.getOrderUuidLeastSigBits());
    }

    @Test
    public void testDirectOrderOldFormatDeserialization() {
        // Create bytes in old format (without version marker and UUID fields)
        Bytes bytes = Bytes.allocateElasticOnHeap();
        bytes.writeLong(123L); // orderId
        bytes.writeLong(1000L); // price
        bytes.writeLong(500L); // size
        bytes.writeLong(1000L); // maxAmount
        bytes.writeLong(100L); // filled
        bytes.writeByte(OrderAction.ASK.getCode()); // action
        bytes.writeLong(456L); // uid
        bytes.writeLong(789L); // timestamp

        // Deserialize
        bytes.readPosition(0);
        DirectOrder deserialized = new DirectOrder(bytes);

        // Verify fields
        assertEquals(123L, deserialized.getOrderId());
        assertEquals(1000L, deserialized.getPrice());
        assertEquals(500L, deserialized.getSize());
        assertEquals(1000L, deserialized.getMaxAmount());
        assertEquals(100L, deserialized.getFilled());
        assertEquals(OrderAction.ASK, deserialized.getAction());
        assertEquals(456L, deserialized.getUid());
        assertEquals(789L, deserialized.getTimestamp());
        assertEquals(0L, deserialized.getOrderUuidMostSigBits()); // Default value for old format
        assertEquals(0L, deserialized.getOrderUuidLeastSigBits()); // Default value for old format
    }

    @Test
    public void testOrderSerialization() {
        // Create a new format order
        Order order = Order.builder()
            .orderId(123L)
            .price(1000L)
            .size(500L)
            .maxAmount(1000L)
            .filled(100L)
            .action(OrderAction.ASK)
            .uid(456L)
            .timestamp(789L)
            .orderUuidMostSigBits(111L)
            .orderUuidLeastSigBits(222L)
            .build();

        // Serialize
        Bytes bytes = Bytes.allocateElasticOnHeap();
        order.writeMarshallable(bytes);

        // Deserialize
        bytes.readPosition(0);
        Order deserialized = new Order(bytes);

        // Verify all fields
        assertEquals(order.getOrderId(), deserialized.getOrderId());
        assertEquals(order.getPrice(), deserialized.getPrice());
        assertEquals(order.getSize(), deserialized.getSize());
        assertEquals(order.getMaxAmount(), deserialized.getMaxAmount());
        assertEquals(order.getFilled(), deserialized.getFilled());
        assertEquals(order.getAction(), deserialized.getAction());
        assertEquals(order.getUid(), deserialized.getUid());
        assertEquals(order.getTimestamp(), deserialized.getTimestamp());
        assertEquals(order.getOrderUuidMostSigBits(), deserialized.getOrderUuidMostSigBits());
        assertEquals(order.getOrderUuidLeastSigBits(), deserialized.getOrderUuidLeastSigBits());
    }

    @Test
    public void testOrderOldFormatDeserialization() {
        // Create bytes in old format (without version marker and UUID fields)
        Bytes bytes = Bytes.allocateElasticOnHeap();
        bytes.writeLong(123L); // orderId
        bytes.writeLong(1000L); // price
        bytes.writeLong(500L); // size
        bytes.writeLong(1000L); // maxAmount
        bytes.writeLong(100L); // filled
        bytes.writeByte(OrderAction.ASK.getCode()); // action
        bytes.writeLong(456L); // uid
        bytes.writeLong(789L); // timestamp

        // Deserialize
        bytes.readPosition(0);
        Order deserialized = new Order(bytes);

        // Verify fields
        assertEquals(123L, deserialized.getOrderId());
        assertEquals(1000L, deserialized.getPrice());
        assertEquals(500L, deserialized.getSize());
        assertEquals(1000L, deserialized.getMaxAmount());
        assertEquals(100L, deserialized.getFilled());
        assertEquals(OrderAction.ASK, deserialized.getAction());
        assertEquals(456L, deserialized.getUid());
        assertEquals(789L, deserialized.getTimestamp());
        assertEquals(0L, deserialized.getOrderUuidMostSigBits()); // Default value for old format
        assertEquals(0L, deserialized.getOrderUuidLeastSigBits()); // Default value for old format
    }
} 