import { Autocomplete as MUIAutocomplete, AutocompleteProps } from '@mui/material';
import { ReactComponent as ArrowDropdownIcon } from '@wyden/assets/arrow-dropdown.svg';
import { styled } from './styled';

export function Autocomplete<
  T,
  Multiple extends boolean | undefined = undefined,
  DisableClearable extends boolean | undefined = undefined,
  FreeSolo extends boolean | undefined = undefined,
>(props: AutocompleteProps<T, Multiple, DisableClearable, FreeSolo>) {
  return (
    <MUIAutocomplete
      {...props}
      data-testid={`${props.id}-autocomplete`}
      popupIcon={<StyledArrowDropdownIcon />}
      ListboxProps={{ style: { maxHeight: '50vh' } }}
    />
  );
}

const StyledArrowDropdownIcon = styled(ArrowDropdownIcon)`
  margin-top: 2px;
`;
