import { styled } from '@ui/styled';
import React from 'react';

type Props = {
  variant?: 'big' | 'regular' | 'small' | 'tiny';
  children: React.ReactNode;
  className?: string;
};

export function Label({ variant = 'regular', children, className }: Props) {
  const ProperComponent = {
    big: styledBig,
    regular: styledRegular,
    small: styledSmall,
    tiny: styledTiny,
  }[variant];

  return <ProperComponent className={className}>{children}</ProperComponent>;
}

const styledBig = styled('span')`
  font-size: 1rem;
  line-height: 1.5rem;
  margin: 0;
  padding: 0;
  font-weight: 500;
`;

const styledRegular = styled('span')`
  font-size: 0.875rem;
  line-height: 1.375rem;
  margin: 0;
  padding: 0;
  font-weight: 500;
`;

const styledSmall = styled('span')`
  font-size: 0.75rem;
  line-height: 1.125rem;
  margin: 0;
  padding: 0;
  font-weight: 500;
`;

const styledTiny = styled('span')`
  font-size: 0.625rem;
  line-height: 1rem;
  margin: 0;
  padding: 0;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.8px;
`;
