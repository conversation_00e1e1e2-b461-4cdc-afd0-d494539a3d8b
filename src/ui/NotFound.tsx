import { useTranslation } from 'react-i18next';
import { styled } from '@ui/styled';
import { Label } from '@ui/Typography/Label';
import { color } from '@ui/theme/colors';
import { ReactComponent as WarningIcon } from '@wyden/assets/warning.svg';

export const NotFound = () => {
  const { t } = useTranslation();
  return (
    <NoResultsFoundContainer>
      <WarningIcon />
      <WarningLabel>{t('common.notFound')}</WarningLabel>
    </NoResultsFoundContainer>
  );
};

const NoResultsFoundContainer = styled('div')`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
`;

const WarningLabel = styled(Label)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
`;
