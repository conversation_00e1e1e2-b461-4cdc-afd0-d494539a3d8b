import { styled } from './styled';

type VerticalSpacerProps = {
  space?: number;
};

export function VerticalSpacer({ space = 1 }: VerticalSpacerProps) {
  return <StyledSpacer space={space} />;
}

const StyledSpacer = styled('div')<{ space: number }>`
  min-height: ${({ theme, space }) => theme.spacing(space)};
  min-height: ${({ theme, space }) => theme.spacing(space)};
  width: 100%;
  visibility: hidden;
  display: flex;
`;
