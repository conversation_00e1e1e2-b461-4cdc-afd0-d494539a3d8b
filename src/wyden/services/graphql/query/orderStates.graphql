query OrderStates(
  $portfolioId: String
  $venueAccount: String
  $instrumentId: String
  $orderId: String
  $clOrderId: String
  $from: String
  $to: String
) {
  orderStates(
    portfolioId: $portfolioId
    venueAccount: $venueAccount
    instrumentId: $instrumentId
    orderId: $orderId
    clOrderId: $clOrderId
    from: $from
    to: $to
  ) {
    orderId
    clientId
    clOrderId
    extOrderId
    expirationDateTime
    origClOrderId
    portfolioId
    portfolioName
    counterPortfolioId
    counterPortfolioName
    orderStatus
    orderQty
    limitPrice
    stopPrice
    tif
    filledQty
    remainingQty
    lastQty
    avgPrice
    lastPrice
    reason
    lastRequestResult
    side
    instrument {
      ...InstrumentContent
    }
    symbol
    assetClass
    venue
    venueTimestamp
    createdAt
    updatedAt
    orderCategory
    parentOrderId
    venueAccountDescs {
      id
      name
    }
  }
}
