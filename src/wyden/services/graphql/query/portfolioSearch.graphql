query PortfolioSearch($search: PortfolioSearchInput) {
  portfolioSearch(search: $search) {
    edges {
      node {
        id
        name
        createdAt
        scopes
        dynamicScopes
        portfolioCurrency
        portfolioType
        tags {
          value
          key
        }
        archivedAt
      }
      cursor
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }
}

fragment PortfolioContent on PortfolioResponse {
  id
  name
  createdAt
  portfolioType
  portfolioCurrency
  scopes
  dynamicScopes
  tags {
    key
    value
  }
  archivedAt
}
