query Transactions($search: TransactionSearchInput!) {
  transactions(search: $search) {
    edges {
      node {
        ... on ClientCashTrade {
          ...ClientCashTradeFragment
        }
        ... on StreetAssetTrade {
          ...StreetAssetTradeFragment
        }
        ... on ClientAssetTrade {
          ...ClientAssetTradeFragment
        }
        ... on StreetCashTrade {
          ...StreetCashTradeFragment
        }
        ... on Withdrawal {
          ...WithdrawalFragment
        }
        ... on Deposit {
          ...DepositFragment
        }
        ... on AccountCashTransfer {
          ...AccountCashTransferFragment
        }
        ... on PortfolioCashTransfer {
          ...PortfolioCashTransferFragment
        }
        ... on Settlement {
          ...SettlementFragment
        }
        ...on Fee {
          ...FeeFragment
        }
      }
      cursor
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
fragment ClientCashTradeFragment on ClientCashTrade {
  # common in each transaction
  dateTime
  uuid
  updatedAt
  description

  #common in trades
  quantity
  price
  currency
  fee
  feeCurrency
  intOrderId
  extOrderId
  settled
  settledDateTime
  orderId
  portfolioId
  portfolioName
  baseCurrency

  #common in some
  parentOrderId
  underlyingExecutionId
  rootOrderId
  rootExecution {
    orderId
    executionId
  }
  executionId
  venueExecutionId
  counterPortfolioId
  counterPortfolioName
}

fragment StreetCashTradeFragment on StreetCashTrade {
  # common in each transaction
  dateTime
  uuid
  updatedAt
  description

  #common in trades
  quantity
  price
  currency
  fee
  feeCurrency
  intOrderId
  extOrderId
  settled
  settledDateTime
  orderId
  portfolioId
  portfolioName
  baseCurrency

  #common in some
  parentOrderId
  underlyingExecutionId
  rootOrderId
  rootExecution {
    orderId
    executionId
  }
  executionId
  venueExecutionId

  # additional
  venueAccount
  venueAccountName
}

fragment ClientAssetTradeFragment on ClientAssetTrade {
  # common in each transaction
  dateTime
  uuid
  updatedAt
  description

  #common in trades
  quantity
  price
  currency
  fee
  feeCurrency
  intOrderId
  extOrderId
  settled
  settledDateTime
  orderId
  portfolioId
  portfolioName

  #common in some
  rootExecution {
    orderId
    executionId
  }
  executionId
  venueExecutionId
  counterPortfolioId
  counterPortfolioName
  # additional
  instrument {
    ...InstrumentContent
  }
}

fragment StreetAssetTradeFragment on StreetAssetTrade {
  # common in each transaction
  dateTime
  uuid
  updatedAt
  description

  #common in trades
  quantity
  price
  currency
  fee
  feeCurrency
  intOrderId
  extOrderId
  settled
  settledDateTime
  orderId
  portfolioId
  portfolioName

  #common in some
  rootOrderId
  executionId
  venueExecutionId
  # additional
  instrument {
    ...InstrumentContent
  }
  venueAccount
  venueAccountName
}

fragment WithdrawalFragment on Withdrawal {
  # common in each transaction
  dateTime
  uuid
  updatedAt
  description
  # common with some
  quantity
  currency
  portfolioId
  portfolioName
  settled
  settledDateTime
  venueExecutionId
  executionId

  # unique
  account
  accountName
  feeAccountId
  feeAccountName
  feePortfolioId
  feePortfolioName
}

fragment DepositFragment on Deposit {
  # common in each transaction
  dateTime
  uuid
  updatedAt
  description
  # common with some
  quantity
  currency
  portfolioId
  portfolioName
  settled
  settledDateTime
  venueExecutionId
  executionId

  # unique
  account
  accountName
  feeAccountId
  feeAccountName
  feePortfolioId
  feePortfolioName
}

fragment AccountCashTransferFragment on AccountCashTransfer {
  # common in each transaction
  dateTime
  uuid
  updatedAt
  description
  # common with some
  quantity
  currency
  settled
  settledDateTime
  venueExecutionId
  executionId

  # unique
  sourceAccountId
  sourceAccountName
  targetAccountId
  targetAccountName
  feeAccountId
  feeAccountName
  feePortfolioId
  feePortfolioName
}

fragment PortfolioCashTransferFragment on PortfolioCashTransfer {
  # common in each transaction
  dateTime
  uuid
  updatedAt
  description

  # common with some
  quantity
  currency
  settled
  settledDateTime
  venueExecutionId
  executionId

  # unique
  sourcePortfolioId
  sourcePortfolioName
  targetPortfolioId
  targetPortfolioName
  feePortfolioId
  feePortfolioName
}

fragment SettlementFragment on Settlement {
  # common in each transaction
  dateTime
  uuid
  updatedAt
  description

  # unique
  settledTransactionIds
}

fragment FeeFragment on Fee {
  dateTime
  uuid
  updatedAt
  executionId
  venueExecutionId
  description
  quantity
  currency
  portfolioId
  portfolioName
  account
  accountName
  settled
  settledDateTime
  feeOrderId: orderId
  parentOrderId
  underlyingExecutionId
  rootExecution {
    orderId
    executionId
  }
}
