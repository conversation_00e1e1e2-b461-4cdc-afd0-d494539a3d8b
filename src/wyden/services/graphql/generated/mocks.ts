import {
  AccountCapabilities,
  AccountCashTransfer,
  ActivateVenueAccountInput,
  AddOrRemoveGroupPermissionsInput,
  AddOrRemoveUserPermissionsInput,
  ApiKey,
  ApiKeyResponse,
  AuditLogEvent,
  BaseInstrumentResponse,
  CancelRejectResponse,
  CapabilityState,
  ClientAssetTrade,
  ClientCashTrade,
  CollectionPredicateInput,
  ConfigCurrencyInstrumentValidationError,
  ConfigCurrencyInstrumentValidationResult,
  ConfigCurrencyValidationResult,
  ConfigField,
  ConfigInstrumentValidationResult,
  ConfigValidationError,
  ConfigValidationResult,
  ConnectorConfigField,
  ConnectorDetailsInput,
  ConnectorDetailsResponse,
  ConnectorResponse,
  ConnectorStatesResponse,
  ConversionRate,
  ConversionSourceResponse,
  ConversionSourceVenueAccount,
  ConversionSourceVenueAccountResponse,
  Counterparty,
  CreateBaseInstrumentInput,
  CreateConversionSourceInput,
  CreateForexSpotPropertiesInput,
  CreateInstrumentIdentifiersInput,
  CreateInstrumentInput,
  CreateInstrumentResult,
  CreatePortfolioInput,
  CreateRateSubscriptionInput,
  CreateSettlementRunInput,
  CreateSettlementRunResponse,
  CreateTradingConstraintsInput,
  CreateVenueAccountInput,
  CreateWalletAccountInput,
  CurrencyInput,
  CurrencyResponse,
  CurrencySearchInput,
  DatePredicate,
  DeactivateVenueAccountInput,
  DefaultTif,
  DeleteRateSubscriptionInput,
  Deposit,
  DetailedCapabilities,
  EffectiveVenueAccountsResponse,
  EventLogResponse,
  ExecutionConfiguration,
  ExecutionConfigurationInput,
  ExecutionReportResponse,
  Fee,
  FlatPermission,
  ForexSpotPropertiesResponse,
  GroupPermission,
  GroupResponse,
  GroupsPermissionsForResourceResponse,
  HedgeResult,
  HedgingConfigValidationResult,
  HedgingConfiguration,
  HedgingConfigurationInput,
  HistoricalDataCapabilities,
  HistoryOrderStatusesInput,
  InstrumentConfiguration,
  InstrumentConfigurationInput,
  InstrumentEdge,
  InstrumentGroupConfiguration,
  InstrumentGroupConfigurationInput,
  InstrumentIdentifiersResponse,
  InstrumentIdsPredicateInput,
  InstrumentKey,
  InstrumentKeyInput,
  InstrumentPricingConfiguration,
  InstrumentPricingConfigurationInput,
  InstrumentQuotingConfiguration,
  InstrumentQuotingConfigurationInput,
  InstrumentResponse,
  InstrumentsConnection,
  InstrumentsRefreshRequestInput,
  InstrumentsSearchInput,
  InternalExchangeConfigurationInput,
  InternalExchangeConfigurationResponse,
  KeyValue,
  KeyValueInput,
  KeyValues,
  LedgerEntryConnection,
  LedgerEntryEdge,
  LedgerEntryResponse,
  LedgerEntrySearchInput,
  MarketDataCapabilities,
  MarketDataResponse,
  MarketQuote,
  MatchResponse,
  MatchesResult,
  MatchesSearchInput,
  Mutation,
  MutationReturnValue,
  MutationSubmittedResponse,
  NewOrderSingleRequestInput,
  OrderBook,
  OrderBookResponse,
  OrderBookSnapshot,
  OrderBookSnapshotSearchInput,
  OrderCancelReplaceRequestInput,
  OrderCancelRequestInput,
  OrderRequestsPerOrderType,
  OrderSnapshot,
  OrderStateConnection,
  OrderStateEdge,
  OrderStateResponse,
  OrderStateSearchInput,
  PageInfo,
  Permission,
  PermissionConnection,
  PermissionEdge,
  PermissionInput,
  PermissionSearchInput,
  PortfolioCashTransfer,
  PortfolioConfiguration,
  PortfolioConfigurationFlat,
  PortfolioConfigurationInput,
  PortfolioConnection,
  PortfolioEdge,
  PortfolioGroupConfiguration,
  PortfolioGroupConfigurationFlat,
  PortfolioGroupConfigurationInput,
  PortfolioPredicateInput,
  PortfolioResponse,
  PortfolioSearchInput,
  PortfolioTag,
  PositionConnection,
  PositionEdge,
  PositionResponse,
  PositionSearchInput,
  PreTradeCheck,
  PreTradeCheckAuditLog,
  PreTradeCheckAuditLogConnection,
  PreTradeCheckAuditLogEdge,
  PreTradeCheckAuditLogSearchInput,
  PreTradeCheckInput,
  PreTradeCheckProperty,
  PreTradeCheckPropertyInput,
  PreTradeCheckPropertySchema,
  PreTradeCheckResult,
  PreTradeCheckSchema,
  PricingConfiguration,
  PricingConfigurationInput,
  Query,
  QuotingCalendar,
  QuotingCalendarInput,
  QuotingConfigValidationResult,
  QuotingConfiguration,
  QuotingConfigurationInput,
  QuotingSourceAccountConfig,
  QuotingSourceAccountConfigInput,
  RateSubscriptionResponse,
  ReferenceDataCapabilities,
  ResetConfigurationInput,
  RootExecution,
  SelectAllTransactionsFiltersInput,
  SelectAllTransactionsInput,
  SelectTransactionsInput,
  Settlement,
  SettlementAccountConfiguration,
  SettlementAccountConfigurationInput,
  SettlementConfigurationInput,
  SettlementConfigurationResponse,
  SettlementLeg,
  SettlementRunResponse,
  SettlementSchedulePoint,
  SettlementSchedulePointInput,
  SettlementStreetCashTrade,
  SettlementTransaction,
  SettlementTransactionConnection,
  SettlementTransactionEdge,
  SettlementTransactionSearchInput,
  SimplePredicateInput,
  SorTarget,
  SorTargetInput,
  SourceConfiguration,
  SourceConfigurationInput,
  StreetAssetTrade,
  StreetCashTrade,
  Subscription,
  SymbolPredicateInput,
  SymbolSearchConnection,
  SymbolSearchEdge,
  SymbolSearchInput,
  SymbolSearchResponse,
  Tag,
  TagInput,
  TargetCapabilityStatus,
  TargetStatesResponse,
  ThresholdConfiguration,
  ThresholdConfigurationInput,
  TifsPerOrderType,
  TimelineConnection,
  TimelineEdge,
  TimelineResult,
  TimelineSearchInput,
  Trade,
  TradingCapabilities,
  TradingConstraintsResponse,
  TransactionConnection,
  TransactionEdge,
  TransactionInput,
  TransactionSearchInput,
  TransactionSelect,
  TransferInput,
  TransferStateConnection,
  TransferStateDateTimePredicateInput,
  TransferStateEdge,
  TransferStateNumberPredicateInput,
  TransferStatePredicateInput,
  TransferStateResponse,
  TransferStateSearchInput,
  TreasuryThreshold,
  TreasuryThresholdInput,
  UpdateApiKeyNameInput,
  UpdateBaseInstrumentInput,
  UpdateConversionSourceInput,
  UpdateInstrumentIdentifiersInput,
  UpdateInstrumentInput,
  UpdateInstrumentResult,
  UpdatePortfolioInput,
  UpdateTradingConstraintsInput,
  UpdateUserDataInput,
  UpdateVenueAccountInput,
  UserDataResponse,
  UserResponse,
  UsersPermissionsForResourceResponse,
  ValidationError,
  ValidationResponse,
  Venue,
  VenueAccount,
  VenueAccountConnection,
  VenueAccountDesc,
  VenueAccountDetailsInput,
  VenueAccountDetailsResponse,
  VenueAccountEdge,
  VenueAccountInput,
  VenueAccountNamesPerVenue,
  VenueAccountPosition,
  VenueAccountPositionInput,
  VenueAccountPositionsResponse,
  VenueAccountResponse,
  VenueAccountSearchInput,
  VenueAccountsPerVenue,
  VenueAccountsWithUnsettledCountResponse,
  VenueCapabilitiesResponse,
  VenueResponse,
  WalletAccountConnection,
  WalletAccountEdge,
  WalletAccountResponse,
  WalletAccountSearchInput,
  WalletByAsset,
  WalletByAssetInput,
  Withdrawal,
  ApiKeyStatus,
  AssetClass,
  CancelRejectResponseTo,
  Capability,
  CollectionPredicateType,
  ConfigurationLevel,
  ConfigurationType,
  ConnectorActionType,
  CurrencyCategory,
  CurrencyType,
  DatePredicateType,
  DayOfTheWeek,
  ErrorType,
  ExecType,
  ExecutionMode,
  HealthStatus,
  InstrumentIdsPredicateType,
  InstrumentSortBy,
  InstrumentType,
  LedgerEntryType,
  MarketDataType,
  MutationType,
  OrderBookLevel,
  OrderCategory,
  OrderRequest,
  OrderStateCollectionField,
  OrderStateDateField,
  OrderStateStringField,
  OrderStatus,
  OrderType,
  ParticipantReferenceType,
  PayloadType,
  PortfolioPredicateType,
  PortfolioSearchType,
  PortfolioSortBy,
  PortfolioType,
  PreTradeCheckChannels,
  PreTradeCheckLevel,
  PreTradeCheckPropertyFormat,
  PreTradeCheckPropertyType,
  PreTradeCheckStatus,
  Priority,
  QuoteTtlUnit,
  RateSubscriptionHealth,
  Resource,
  Scope,
  SettlementDirection,
  SettlementDirectionPriority,
  SettlementLegPriority,
  SettlementStatus,
  Side,
  SimplePredicateType,
  SortingOrder,
  Status,
  SymbolPredicateType,
  Tif,
  TimelineEventType,
  TradingMode,
  TransactionType,
  TransactionTypeInput,
  TransferStateCollectionPredicateField,
  TransferStateCollectionPredicateType,
  TransferStateDateTimePredicateField,
  TransferStateDateTimePredicateType,
  TransferStateNumberPredicateField,
  TransferStateNumberPredicateType,
  TransferStatus,
  VenueAccountActionType,
  VenueAccountType,
  VenueType,
  VostroNostro,
  WalletType,
} from '@wyden/services/graphql/generated/graphql';

export const anAccountCapabilities = (
  overrides?: Partial<AccountCapabilities>,
): { __typename: 'AccountCapabilities' } & AccountCapabilities => {
  return {
    __typename: 'AccountCapabilities',
    accountPositionsRetrievalSupported:
      overrides && overrides.hasOwnProperty('accountPositionsRetrievalSupported')
        ? overrides.accountPositionsRetrievalSupported!
        : true,
    allocationSupported:
      overrides && overrides.hasOwnProperty('allocationSupported')
        ? overrides.allocationSupported!
        : true,
    balanceRetrievalBySubAccount:
      overrides && overrides.hasOwnProperty('balanceRetrievalBySubAccount')
        ? overrides.balanceRetrievalBySubAccount!
        : false,
    balanceRetrievalSupported:
      overrides && overrides.hasOwnProperty('balanceRetrievalSupported')
        ? overrides.balanceRetrievalSupported!
        : false,
    balanceStreamingSupported:
      overrides && overrides.hasOwnProperty('balanceStreamingSupported')
        ? overrides.balanceStreamingSupported!
        : true,
    depositAddressRetrievalSupported:
      overrides && overrides.hasOwnProperty('depositAddressRetrievalSupported')
        ? overrides.depositAddressRetrievalSupported!
        : false,
    extAccountsRetrievalSupported:
      overrides && overrides.hasOwnProperty('extAccountsRetrievalSupported')
        ? overrides.extAccountsRetrievalSupported!
        : true,
    orderReconciliationSupported:
      overrides && overrides.hasOwnProperty('orderReconciliationSupported')
        ? overrides.orderReconciliationSupported!
        : false,
    subAccountRetrievalSupported:
      overrides && overrides.hasOwnProperty('subAccountRetrievalSupported')
        ? overrides.subAccountRetrievalSupported!
        : false,
    walletHistorySupported:
      overrides && overrides.hasOwnProperty('walletHistorySupported')
        ? overrides.walletHistorySupported!
        : false,
    withdrawalSupported:
      overrides && overrides.hasOwnProperty('withdrawalSupported')
        ? overrides.withdrawalSupported!
        : true,
  };
};

export const anAccountCashTransfer = (
  overrides?: Partial<AccountCashTransfer>,
): { __typename: 'AccountCashTransfer' } & AccountCashTransfer => {
  return {
    __typename: 'AccountCashTransfer',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'aliquam',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'voluptate',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'et',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'veritatis',
    feeAccountId:
      overrides && overrides.hasOwnProperty('feeAccountId') ? overrides.feeAccountId! : 'alias',
    feeAccountName:
      overrides && overrides.hasOwnProperty('feeAccountName')
        ? overrides.feeAccountName!
        : 'minima',
    feePortfolioId:
      overrides && overrides.hasOwnProperty('feePortfolioId')
        ? overrides.feePortfolioId!
        : 'mollitia',
    feePortfolioName:
      overrides && overrides.hasOwnProperty('feePortfolioName')
        ? overrides.feePortfolioName!
        : 'corporis',
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 7.35,
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : 'ea',
    settledDateTime:
      overrides && overrides.hasOwnProperty('settledDateTime') ? overrides.settledDateTime! : 6.99,
    sourceAccountId:
      overrides && overrides.hasOwnProperty('sourceAccountId') ? overrides.sourceAccountId! : 'est',
    sourceAccountName:
      overrides && overrides.hasOwnProperty('sourceAccountName')
        ? overrides.sourceAccountName!
        : 'dolorem',
    targetAccountId:
      overrides && overrides.hasOwnProperty('targetAccountId') ? overrides.targetAccountId! : 'eos',
    targetAccountName:
      overrides && overrides.hasOwnProperty('targetAccountName')
        ? overrides.targetAccountName!
        : 'qui',
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 4.54,
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'molestiae',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'sit',
  };
};

export const anActivateVenueAccountInput = (
  overrides?: Partial<ActivateVenueAccountInput>,
): ActivateVenueAccountInput => {
  return {
    correlationObject:
      overrides && overrides.hasOwnProperty('correlationObject')
        ? overrides.correlationObject!
        : 'ut',
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId')
        ? overrides.venueAccountId!
        : 'consequatur',
  };
};

export const anAddOrRemoveGroupPermissionsInput = (
  overrides?: Partial<AddOrRemoveGroupPermissionsInput>,
): AddOrRemoveGroupPermissionsInput => {
  return {
    groupName:
      overrides && overrides.hasOwnProperty('groupName') ? overrides.groupName! : 'voluptatem',
    permissions:
      overrides && overrides.hasOwnProperty('permissions')
        ? overrides.permissions!
        : [aPermissionInput()],
  };
};

export const anAddOrRemoveUserPermissionsInput = (
  overrides?: Partial<AddOrRemoveUserPermissionsInput>,
): AddOrRemoveUserPermissionsInput => {
  return {
    permissions:
      overrides && overrides.hasOwnProperty('permissions')
        ? overrides.permissions!
        : [aPermissionInput()],
    username: overrides && overrides.hasOwnProperty('username') ? overrides.username! : 'qui',
  };
};

export const anApiKey = (overrides?: Partial<ApiKey>): { __typename: 'ApiKey' } & ApiKey => {
  return {
    __typename: 'ApiKey',
    createdAt: overrides && overrides.hasOwnProperty('createdAt') ? overrides.createdAt! : 'aut',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'consequatur',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'consequatur',
    status:
      overrides && overrides.hasOwnProperty('status') ? overrides.status! : ApiKeyStatus.Active,
  };
};

export const anApiKeyResponse = (
  overrides?: Partial<ApiKeyResponse>,
): { __typename: 'ApiKeyResponse' } & ApiKeyResponse => {
  return {
    __typename: 'ApiKeyResponse',
    createdAt:
      overrides && overrides.hasOwnProperty('createdAt') ? overrides.createdAt! : 'eligendi',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'maxime',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'dignissimos',
    secret: overrides && overrides.hasOwnProperty('secret') ? overrides.secret! : 'assumenda',
    status:
      overrides && overrides.hasOwnProperty('status') ? overrides.status! : ApiKeyStatus.Active,
  };
};

export const anAuditLogEvent = (
  overrides?: Partial<AuditLogEvent>,
): { __typename: 'AuditLogEvent' } & AuditLogEvent => {
  return {
    __typename: 'AuditLogEvent',
    createdAt:
      overrides && overrides.hasOwnProperty('createdAt') ? overrides.createdAt! : 'voluptas',
    payload: overrides && overrides.hasOwnProperty('payload') ? overrides.payload! : 'nihil',
    payloadType:
      overrides && overrides.hasOwnProperty('payloadType')
        ? overrides.payloadType!
        : PayloadType.OrderBook,
    source: overrides && overrides.hasOwnProperty('source') ? overrides.source! : 'sit',
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'ut',
  };
};

export const aBaseInstrumentResponse = (
  overrides?: Partial<BaseInstrumentResponse>,
): { __typename: 'BaseInstrumentResponse' } & BaseInstrumentResponse => {
  return {
    __typename: 'BaseInstrumentResponse',
    assetClass:
      overrides && overrides.hasOwnProperty('assetClass')
        ? overrides.assetClass!
        : AssetClass.Forex,
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'et',
    inverseContract:
      overrides && overrides.hasOwnProperty('inverseContract') ? overrides.inverseContract! : true,
    quoteCurrency:
      overrides && overrides.hasOwnProperty('quoteCurrency') ? overrides.quoteCurrency! : 'non',
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'quia',
    venueName:
      overrides && overrides.hasOwnProperty('venueName') ? overrides.venueName! : 'mollitia',
    venueType:
      overrides && overrides.hasOwnProperty('venueType') ? overrides.venueType! : VenueType.Client,
  };
};

export const aCancelRejectResponse = (
  overrides?: Partial<CancelRejectResponse>,
): { __typename: 'CancelRejectResponse' } & CancelRejectResponse => {
  return {
    __typename: 'CancelRejectResponse',
    cancelRejectReason:
      overrides && overrides.hasOwnProperty('cancelRejectReason')
        ? overrides.cancelRejectReason!
        : 'alias',
    cancelRejectResponseTo:
      overrides && overrides.hasOwnProperty('cancelRejectResponseTo')
        ? overrides.cancelRejectResponseTo!
        : CancelRejectResponseTo.OrderCancelReplaceRequest,
    clOrderId: overrides && overrides.hasOwnProperty('clOrderId') ? overrides.clOrderId! : 'et',
    clientId: overrides && overrides.hasOwnProperty('clientId') ? overrides.clientId! : 'sequi',
    orderStatus:
      overrides && overrides.hasOwnProperty('orderStatus')
        ? overrides.orderStatus!
        : OrderStatus.Calculated,
    origClOrderId:
      overrides && overrides.hasOwnProperty('origClOrderId') ? overrides.origClOrderId! : 'quia',
    origOrderId:
      overrides && overrides.hasOwnProperty('origOrderId') ? overrides.origOrderId! : 'voluptatum',
    result: overrides && overrides.hasOwnProperty('result') ? overrides.result! : 'inventore',
  };
};

export const aCapabilityState = (
  overrides?: Partial<CapabilityState>,
): { __typename: 'CapabilityState' } & CapabilityState => {
  return {
    __typename: 'CapabilityState',
    capability:
      overrides && overrides.hasOwnProperty('capability')
        ? overrides.capability!
        : Capability.Account,
    healthStatus:
      overrides && overrides.hasOwnProperty('healthStatus')
        ? overrides.healthStatus!
        : HealthStatus.Alive,
    message: overrides && overrides.hasOwnProperty('message') ? overrides.message! : 'qui',
    timestamp:
      overrides && overrides.hasOwnProperty('timestamp') ? overrides.timestamp! : 'ratione',
  };
};

export const aClientAssetTrade = (
  overrides?: Partial<ClientAssetTrade>,
): { __typename: 'ClientAssetTrade' } & ClientAssetTrade => {
  return {
    __typename: 'ClientAssetTrade',
    counterPortfolioId:
      overrides && overrides.hasOwnProperty('counterPortfolioId')
        ? overrides.counterPortfolioId!
        : 'et',
    counterPortfolioName:
      overrides && overrides.hasOwnProperty('counterPortfolioName')
        ? overrides.counterPortfolioName!
        : 'saepe',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'harum',
    dateTime:
      overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'cupiditate',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'quae',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'veritatis',
    extOrderId:
      overrides && overrides.hasOwnProperty('extOrderId') ? overrides.extOrderId! : 'nobis',
    fee: overrides && overrides.hasOwnProperty('fee') ? overrides.fee! : 3.81,
    feeCurrency:
      overrides && overrides.hasOwnProperty('feeCurrency') ? overrides.feeCurrency! : 'ipsam',
    instrument:
      overrides && overrides.hasOwnProperty('instrument')
        ? overrides.instrument!
        : anInstrumentResponse(),
    intOrderId: overrides && overrides.hasOwnProperty('intOrderId') ? overrides.intOrderId! : 'ex',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'sit',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'nam',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName')
        ? overrides.portfolioName!
        : 'occaecati',
    price: overrides && overrides.hasOwnProperty('price') ? overrides.price! : 0.58,
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 9.78,
    rootExecution:
      overrides && overrides.hasOwnProperty('rootExecution')
        ? overrides.rootExecution!
        : aRootExecution(),
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : 'dolores',
    settledDateTime:
      overrides && overrides.hasOwnProperty('settledDateTime') ? overrides.settledDateTime! : 6.26,
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 2.25,
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'dicta',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'ipsam',
  };
};

export const aClientCashTrade = (
  overrides?: Partial<ClientCashTrade>,
): { __typename: 'ClientCashTrade' } & ClientCashTrade => {
  return {
    __typename: 'ClientCashTrade',
    baseCurrency:
      overrides && overrides.hasOwnProperty('baseCurrency') ? overrides.baseCurrency! : 'nesciunt',
    counterPortfolioId:
      overrides && overrides.hasOwnProperty('counterPortfolioId')
        ? overrides.counterPortfolioId!
        : 'eum',
    counterPortfolioName:
      overrides && overrides.hasOwnProperty('counterPortfolioName')
        ? overrides.counterPortfolioName!
        : 'illum',
    currency:
      overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'exercitationem',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'et',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'est',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'ab',
    extOrderId:
      overrides && overrides.hasOwnProperty('extOrderId') ? overrides.extOrderId! : 'nobis',
    fee: overrides && overrides.hasOwnProperty('fee') ? overrides.fee! : 4.41,
    feeCurrency:
      overrides && overrides.hasOwnProperty('feeCurrency') ? overrides.feeCurrency! : 'quod',
    intOrderId:
      overrides && overrides.hasOwnProperty('intOrderId') ? overrides.intOrderId! : 'voluptatibus',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'perferendis',
    parentOrderId:
      overrides && overrides.hasOwnProperty('parentOrderId') ? overrides.parentOrderId! : 'rem',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'qui',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName')
        ? overrides.portfolioName!
        : 'accusamus',
    price: overrides && overrides.hasOwnProperty('price') ? overrides.price! : 3.62,
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 3.1,
    rootExecution:
      overrides && overrides.hasOwnProperty('rootExecution')
        ? overrides.rootExecution!
        : aRootExecution(),
    rootOrderId:
      overrides && overrides.hasOwnProperty('rootOrderId') ? overrides.rootOrderId! : 'quia',
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : 'veritatis',
    settledDateTime:
      overrides && overrides.hasOwnProperty('settledDateTime') ? overrides.settledDateTime! : 5.27,
    underlyingExecutionId:
      overrides && overrides.hasOwnProperty('underlyingExecutionId')
        ? overrides.underlyingExecutionId!
        : 'officia',
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 0.19,
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'aspernatur',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'qui',
  };
};

export const aCollectionPredicateInput = (
  overrides?: Partial<CollectionPredicateInput>,
): CollectionPredicateInput => {
  return {
    field:
      overrides && overrides.hasOwnProperty('field')
        ? overrides.field!
        : OrderStateCollectionField.InstrumentId,
    method:
      overrides && overrides.hasOwnProperty('method')
        ? overrides.method!
        : CollectionPredicateType.In,
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : ['dolore'],
  };
};

export const aConfigCurrencyInstrumentValidationError = (
  overrides?: Partial<ConfigCurrencyInstrumentValidationError>,
): {
  __typename: 'ConfigCurrencyInstrumentValidationError';
} & ConfigCurrencyInstrumentValidationError => {
  return {
    __typename: 'ConfigCurrencyInstrumentValidationError',
    error: overrides && overrides.hasOwnProperty('error') ? overrides.error! : 'expedita',
    errorType:
      overrides && overrides.hasOwnProperty('errorType') ? overrides.errorType! : ErrorType.Invalid,
    fieldName: overrides && overrides.hasOwnProperty('fieldName') ? overrides.fieldName! : 'at',
  };
};

export const aConfigCurrencyInstrumentValidationResult = (
  overrides?: Partial<ConfigCurrencyInstrumentValidationResult>,
): {
  __typename: 'ConfigCurrencyInstrumentValidationResult';
} & ConfigCurrencyInstrumentValidationResult => {
  return {
    __typename: 'ConfigCurrencyInstrumentValidationResult',
    errors:
      overrides && overrides.hasOwnProperty('errors')
        ? overrides.errors!
        : [aConfigCurrencyInstrumentValidationError()],
    isValid: overrides && overrides.hasOwnProperty('isValid') ? overrides.isValid! : false,
  };
};

export const aConfigCurrencyValidationResult = (
  overrides?: Partial<ConfigCurrencyValidationResult>,
): { __typename: 'ConfigCurrencyValidationResult' } & ConfigCurrencyValidationResult => {
  return {
    __typename: 'ConfigCurrencyValidationResult',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'molestiae',
    error: overrides && overrides.hasOwnProperty('error') ? overrides.error! : 'sed',
    instrumentValidationResult:
      overrides && overrides.hasOwnProperty('instrumentValidationResult')
        ? overrides.instrumentValidationResult!
        : aConfigCurrencyInstrumentValidationResult(),
    isValid: overrides && overrides.hasOwnProperty('isValid') ? overrides.isValid! : false,
  };
};

export const aConfigField = (
  overrides?: Partial<ConfigField>,
): { __typename: 'ConfigField' } & ConfigField => {
  return {
    __typename: 'ConfigField',
    defaultValue:
      overrides && overrides.hasOwnProperty('defaultValue') ? overrides.defaultValue! : 'animi',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'ea',
    enumValues:
      overrides && overrides.hasOwnProperty('enumValues') ? overrides.enumValues! : ['id'],
    multiline: overrides && overrides.hasOwnProperty('multiline') ? overrides.multiline! : true,
    propertyKey:
      overrides && overrides.hasOwnProperty('propertyKey') ? overrides.propertyKey! : 'hic',
    required: overrides && overrides.hasOwnProperty('required') ? overrides.required! : false,
    secret: overrides && overrides.hasOwnProperty('secret') ? overrides.secret! : true,
    type: overrides && overrides.hasOwnProperty('type') ? overrides.type! : 'consectetur',
  };
};

export const aConfigInstrumentValidationResult = (
  overrides?: Partial<ConfigInstrumentValidationResult>,
): { __typename: 'ConfigInstrumentValidationResult' } & ConfigInstrumentValidationResult => {
  return {
    __typename: 'ConfigInstrumentValidationResult',
    errors:
      overrides && overrides.hasOwnProperty('errors')
        ? overrides.errors!
        : [aConfigValidationError()],
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId') ? overrides.instrumentId! : 'iusto',
    isValid: overrides && overrides.hasOwnProperty('isValid') ? overrides.isValid! : true,
    preventsMarketData:
      overrides && overrides.hasOwnProperty('preventsMarketData')
        ? overrides.preventsMarketData!
        : false,
    preventsTrading:
      overrides && overrides.hasOwnProperty('preventsTrading') ? overrides.preventsTrading! : false,
  };
};

export const aConfigValidationError = (
  overrides?: Partial<ConfigValidationError>,
): { __typename: 'ConfigValidationError' } & ConfigValidationError => {
  return {
    __typename: 'ConfigValidationError',
    errorMessage:
      overrides && overrides.hasOwnProperty('errorMessage') ? overrides.errorMessage! : 'nihil',
    errorType:
      overrides && overrides.hasOwnProperty('errorType') ? overrides.errorType! : ErrorType.Invalid,
    fieldName: overrides && overrides.hasOwnProperty('fieldName') ? overrides.fieldName! : 'non',
    preventsMarketData:
      overrides && overrides.hasOwnProperty('preventsMarketData')
        ? overrides.preventsMarketData!
        : true,
    preventsTrading:
      overrides && overrides.hasOwnProperty('preventsTrading') ? overrides.preventsTrading! : true,
  };
};

export const aConfigValidationResult = (
  overrides?: Partial<ConfigValidationResult>,
): { __typename: 'ConfigValidationResult' } & ConfigValidationResult => {
  return {
    __typename: 'ConfigValidationResult',
    resultsPerInstrument:
      overrides && overrides.hasOwnProperty('resultsPerInstrument')
        ? overrides.resultsPerInstrument!
        : [aConfigInstrumentValidationResult()],
  };
};

export const aConnectorConfigField = (
  overrides?: Partial<ConnectorConfigField>,
): { __typename: 'ConnectorConfigField' } & ConnectorConfigField => {
  return {
    __typename: 'ConnectorConfigField',
    defaultValue:
      overrides && overrides.hasOwnProperty('defaultValue') ? overrides.defaultValue! : 'dolor',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'praesentium',
    enumValues:
      overrides && overrides.hasOwnProperty('enumValues') ? overrides.enumValues! : ['veniam'],
    multiline: overrides && overrides.hasOwnProperty('multiline') ? overrides.multiline! : false,
    propertyKey:
      overrides && overrides.hasOwnProperty('propertyKey') ? overrides.propertyKey! : 'autem',
    required: overrides && overrides.hasOwnProperty('required') ? overrides.required! : true,
    secret: overrides && overrides.hasOwnProperty('secret') ? overrides.secret! : true,
    type: overrides && overrides.hasOwnProperty('type') ? overrides.type! : 'sint',
  };
};

export const aConnectorDetailsInput = (
  overrides?: Partial<ConnectorDetailsInput>,
): ConnectorDetailsInput => {
  return {
    connectorId:
      overrides && overrides.hasOwnProperty('connectorId') ? overrides.connectorId! : 'eveniet',
    connectorName:
      overrides && overrides.hasOwnProperty('connectorName') ? overrides.connectorName! : 'libero',
    connectorType:
      overrides && overrides.hasOwnProperty('connectorType')
        ? overrides.connectorType!
        : 'architecto',
    deactivatedAtDateTime:
      overrides && overrides.hasOwnProperty('deactivatedAtDateTime')
        ? overrides.deactivatedAtDateTime!
        : 'iste',
    keyValues:
      overrides && overrides.hasOwnProperty('keyValues')
        ? overrides.keyValues!
        : [aKeyValueInput()],
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'rerum',
  };
};

export const aConnectorDetailsResponse = (
  overrides?: Partial<ConnectorDetailsResponse>,
): { __typename: 'ConnectorDetailsResponse' } & ConnectorDetailsResponse => {
  return {
    __typename: 'ConnectorDetailsResponse',
    connectorId:
      overrides && overrides.hasOwnProperty('connectorId') ? overrides.connectorId! : 'similique',
    connectorName:
      overrides && overrides.hasOwnProperty('connectorName') ? overrides.connectorName! : 'tenetur',
    connectorType:
      overrides && overrides.hasOwnProperty('connectorType') ? overrides.connectorType! : 'beatae',
    deactivatedAtDateTime:
      overrides && overrides.hasOwnProperty('deactivatedAtDateTime')
        ? overrides.deactivatedAtDateTime!
        : 'occaecati',
    keyValues:
      overrides && overrides.hasOwnProperty('keyValues') ? overrides.keyValues! : [aKeyValue()],
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'ut',
  };
};

export const aConnectorResponse = (
  overrides?: Partial<ConnectorResponse>,
): { __typename: 'ConnectorResponse' } & ConnectorResponse => {
  return {
    __typename: 'ConnectorResponse',
    connectorId:
      overrides && overrides.hasOwnProperty('connectorId') ? overrides.connectorId! : 'est',
    connectorName:
      overrides && overrides.hasOwnProperty('connectorName') ? overrides.connectorName! : 'qui',
    connectorType:
      overrides && overrides.hasOwnProperty('connectorType') ? overrides.connectorType! : 'ut',
    deactivatedAtDateTime:
      overrides && overrides.hasOwnProperty('deactivatedAtDateTime')
        ? overrides.deactivatedAtDateTime!
        : 'ducimus',
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'porro',
  };
};

export const aConnectorStatesResponse = (
  overrides?: Partial<ConnectorStatesResponse>,
): { __typename: 'ConnectorStatesResponse' } & ConnectorStatesResponse => {
  return {
    __typename: 'ConnectorStatesResponse',
    capability:
      overrides && overrides.hasOwnProperty('capability')
        ? overrides.capability!
        : [aCapabilityState()],
    connectorId:
      overrides && overrides.hasOwnProperty('connectorId') ? overrides.connectorId! : 'expedita',
  };
};

export const aConversionRate = (overrides?: Partial<ConversionRate>): ConversionRate => {
  return {
    baseCurrency:
      overrides && overrides.hasOwnProperty('baseCurrency') ? overrides.baseCurrency! : 'non',
    quoteCurrency:
      overrides && overrides.hasOwnProperty('quoteCurrency') ? overrides.quoteCurrency! : 'harum',
    rate: overrides && overrides.hasOwnProperty('rate') ? overrides.rate! : 'similique',
  };
};

export const aConversionSourceResponse = (
  overrides?: Partial<ConversionSourceResponse>,
): { __typename: 'ConversionSourceResponse' } & ConversionSourceResponse => {
  return {
    __typename: 'ConversionSourceResponse',
    priority: overrides && overrides.hasOwnProperty('priority') ? overrides.priority! : 608,
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'voluptas',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'explicabo',
  };
};

export const aConversionSourceVenueAccount = (
  overrides?: Partial<ConversionSourceVenueAccount>,
): { __typename: 'ConversionSourceVenueAccount' } & ConversionSourceVenueAccount => {
  return {
    __typename: 'ConversionSourceVenueAccount',
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'unde',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'nostrum',
  };
};

export const aConversionSourceVenueAccountResponse = (
  overrides?: Partial<ConversionSourceVenueAccountResponse>,
): {
  __typename: 'ConversionSourceVenueAccountResponse';
} & ConversionSourceVenueAccountResponse => {
  return {
    __typename: 'ConversionSourceVenueAccountResponse',
    venueAccounts:
      overrides && overrides.hasOwnProperty('venueAccounts')
        ? overrides.venueAccounts!
        : [aConversionSourceVenueAccount()],
    venueName:
      overrides && overrides.hasOwnProperty('venueName') ? overrides.venueName! : 'officia',
  };
};

export const aCounterparty = (
  overrides?: Partial<Counterparty>,
): { __typename: 'Counterparty' } & Counterparty => {
  return {
    __typename: 'Counterparty',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'maiores',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'quas',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName') ? overrides.portfolioName! : 'tempore',
    price: overrides && overrides.hasOwnProperty('price') ? overrides.price! : 2.21,
    referenceType:
      overrides && overrides.hasOwnProperty('referenceType')
        ? overrides.referenceType!
        : ParticipantReferenceType.Portfolio,
    rootOrderId:
      overrides && overrides.hasOwnProperty('rootOrderId') ? overrides.rootOrderId! : 'sequi',
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'ipsum',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'qui',
    volume: overrides && overrides.hasOwnProperty('volume') ? overrides.volume! : 8.84,
  };
};

export const aCreateBaseInstrumentInput = (
  overrides?: Partial<CreateBaseInstrumentInput>,
): CreateBaseInstrumentInput => {
  return {
    assetClass:
      overrides && overrides.hasOwnProperty('assetClass')
        ? overrides.assetClass!
        : AssetClass.Forex,
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'accusantium',
    inverseContract:
      overrides && overrides.hasOwnProperty('inverseContract') ? overrides.inverseContract! : true,
    quoteCurrency:
      overrides && overrides.hasOwnProperty('quoteCurrency') ? overrides.quoteCurrency! : 'nam',
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'debitis',
    venueName: overrides && overrides.hasOwnProperty('venueName') ? overrides.venueName! : 'et',
    venueType:
      overrides && overrides.hasOwnProperty('venueType') ? overrides.venueType! : VenueType.Client,
  };
};

export const aCreateConversionSourceInput = (
  overrides?: Partial<CreateConversionSourceInput>,
): CreateConversionSourceInput => {
  return {
    priority: overrides && overrides.hasOwnProperty('priority') ? overrides.priority! : 2642,
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount')
        ? overrides.venueAccount!
        : 'accusantium',
  };
};

export const aCreateForexSpotPropertiesInput = (
  overrides?: Partial<CreateForexSpotPropertiesInput>,
): CreateForexSpotPropertiesInput => {
  return {
    baseCurrency:
      overrides && overrides.hasOwnProperty('baseCurrency') ? overrides.baseCurrency! : 'id',
  };
};

export const aCreateInstrumentIdentifiersInput = (
  overrides?: Partial<CreateInstrumentIdentifiersInput>,
): CreateInstrumentIdentifiersInput => {
  return {
    adapterTicker:
      overrides && overrides.hasOwnProperty('adapterTicker') ? overrides.adapterTicker! : 'nihil',
    tradingViewId:
      overrides && overrides.hasOwnProperty('tradingViewId')
        ? overrides.tradingViewId!
        : 'possimus',
    venueTradingViewId:
      overrides && overrides.hasOwnProperty('venueTradingViewId')
        ? overrides.venueTradingViewId!
        : 'id',
  };
};

export const aCreateInstrumentInput = (
  overrides?: Partial<CreateInstrumentInput>,
): CreateInstrumentInput => {
  return {
    baseInstrument:
      overrides && overrides.hasOwnProperty('baseInstrument')
        ? overrides.baseInstrument!
        : aCreateBaseInstrumentInput(),
    correlationObject:
      overrides && overrides.hasOwnProperty('correlationObject')
        ? overrides.correlationObject!
        : 'vel',
    forexSpotProperties:
      overrides && overrides.hasOwnProperty('forexSpotProperties')
        ? overrides.forexSpotProperties!
        : aCreateForexSpotPropertiesInput(),
    instrumentIdentifiers:
      overrides && overrides.hasOwnProperty('instrumentIdentifiers')
        ? overrides.instrumentIdentifiers!
        : aCreateInstrumentIdentifiersInput(),
    tradingConstraints:
      overrides && overrides.hasOwnProperty('tradingConstraints')
        ? overrides.tradingConstraints!
        : aCreateTradingConstraintsInput(),
  };
};

export const aCreateInstrumentResult = (
  overrides?: Partial<CreateInstrumentResult>,
): { __typename: 'CreateInstrumentResult' } & CreateInstrumentResult => {
  return {
    __typename: 'CreateInstrumentResult',
    status: overrides && overrides.hasOwnProperty('status') ? overrides.status! : 'harum',
  };
};

export const aCreatePortfolioInput = (
  overrides?: Partial<CreatePortfolioInput>,
): CreatePortfolioInput => {
  return {
    correlationObject:
      overrides && overrides.hasOwnProperty('correlationObject')
        ? overrides.correlationObject!
        : 'minus',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'quibusdam',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'dolores',
    portfolioCurrency:
      overrides && overrides.hasOwnProperty('portfolioCurrency')
        ? overrides.portfolioCurrency!
        : 'quo',
    portfolioType:
      overrides && overrides.hasOwnProperty('portfolioType')
        ? overrides.portfolioType!
        : PortfolioType.Nostro,
    tags: overrides && overrides.hasOwnProperty('tags') ? overrides.tags! : [aTagInput()],
  };
};

export const aCreateRateSubscriptionInput = (
  overrides?: Partial<CreateRateSubscriptionInput>,
): CreateRateSubscriptionInput => {
  return {
    baseCurrency:
      overrides && overrides.hasOwnProperty('baseCurrency') ? overrides.baseCurrency! : 'doloribus',
    quoteCurrency:
      overrides && overrides.hasOwnProperty('quoteCurrency') ? overrides.quoteCurrency! : 'sunt',
  };
};

export const aCreateSettlementRunInput = (
  overrides?: Partial<CreateSettlementRunInput>,
): CreateSettlementRunInput => {
  return {
    accountIds:
      overrides && overrides.hasOwnProperty('accountIds') ? overrides.accountIds! : ['excepturi'],
  };
};

export const aCreateSettlementRunResponse = (
  overrides?: Partial<CreateSettlementRunResponse>,
): { __typename: 'CreateSettlementRunResponse' } & CreateSettlementRunResponse => {
  return {
    __typename: 'CreateSettlementRunResponse',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'ab',
  };
};

export const aCreateTradingConstraintsInput = (
  overrides?: Partial<CreateTradingConstraintsInput>,
): CreateTradingConstraintsInput => {
  return {
    contractSize:
      overrides && overrides.hasOwnProperty('contractSize') ? overrides.contractSize! : 'nulla',
    maxPrice: overrides && overrides.hasOwnProperty('maxPrice') ? overrides.maxPrice! : 'aliquid',
    maxQty: overrides && overrides.hasOwnProperty('maxQty') ? overrides.maxQty! : 'fugiat',
    maxQuoteQty:
      overrides && overrides.hasOwnProperty('maxQuoteQty') ? overrides.maxQuoteQty! : 'repellendus',
    minNotional:
      overrides && overrides.hasOwnProperty('minNotional') ? overrides.minNotional! : 'tempora',
    minPrice: overrides && overrides.hasOwnProperty('minPrice') ? overrides.minPrice! : 'eum',
    minQty: overrides && overrides.hasOwnProperty('minQty') ? overrides.minQty! : 'beatae',
    minQuoteQty:
      overrides && overrides.hasOwnProperty('minQuoteQty') ? overrides.minQuoteQty! : 'ab',
    priceIncr: overrides && overrides.hasOwnProperty('priceIncr') ? overrides.priceIncr! : 'et',
    qtyIncr: overrides && overrides.hasOwnProperty('qtyIncr') ? overrides.qtyIncr! : 'veritatis',
    quoteQtyIncr:
      overrides && overrides.hasOwnProperty('quoteQtyIncr')
        ? overrides.quoteQtyIncr!
        : 'voluptates',
    tradeable: overrides && overrides.hasOwnProperty('tradeable') ? overrides.tradeable! : false,
  };
};

export const aCreateVenueAccountInput = (
  overrides?: Partial<CreateVenueAccountInput>,
): CreateVenueAccountInput => {
  return {
    apiKey: overrides && overrides.hasOwnProperty('apiKey') ? overrides.apiKey! : 'quasi',
    apiSecret: overrides && overrides.hasOwnProperty('apiSecret') ? overrides.apiSecret! : 'cum',
    correlationObject:
      overrides && overrides.hasOwnProperty('correlationObject')
        ? overrides.correlationObject!
        : 'praesentium',
    keyValues:
      overrides && overrides.hasOwnProperty('keyValues')
        ? overrides.keyValues!
        : [aKeyValueInput()],
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId')
        ? overrides.venueAccountId!
        : 'suscipit',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'harum',
    venueName: overrides && overrides.hasOwnProperty('venueName') ? overrides.venueName! : 'a',
  };
};

export const aCreateWalletAccountInput = (
  overrides?: Partial<CreateWalletAccountInput>,
): CreateWalletAccountInput => {
  return {
    correlationObject:
      overrides && overrides.hasOwnProperty('correlationObject')
        ? overrides.correlationObject!
        : 'occaecati',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'et',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'non',
    walletType:
      overrides && overrides.hasOwnProperty('walletType')
        ? overrides.walletType!
        : WalletType.Nostro,
  };
};

export const aCurrencyInput = (overrides?: Partial<CurrencyInput>): CurrencyInput => {
  return {
    displayPrecision:
      overrides && overrides.hasOwnProperty('displayPrecision')
        ? overrides.displayPrecision!
        : 9942,
    precision: overrides && overrides.hasOwnProperty('precision') ? overrides.precision! : 8496,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'ut',
    type: overrides && overrides.hasOwnProperty('type') ? overrides.type! : CurrencyCategory.Crypto,
  };
};

export const aCurrencyResponse = (
  overrides?: Partial<CurrencyResponse>,
): { __typename: 'CurrencyResponse' } & CurrencyResponse => {
  return {
    __typename: 'CurrencyResponse',
    displayPrecision:
      overrides && overrides.hasOwnProperty('displayPrecision')
        ? overrides.displayPrecision!
        : 9323,
    precision: overrides && overrides.hasOwnProperty('precision') ? overrides.precision! : 7139,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'cupiditate',
    type: overrides && overrides.hasOwnProperty('type') ? overrides.type! : CurrencyCategory.Crypto,
  };
};

export const aCurrencySearchInput = (
  overrides?: Partial<CurrencySearchInput>,
): CurrencySearchInput => {
  return {
    symbols: overrides && overrides.hasOwnProperty('symbols') ? overrides.symbols! : ['dolorem'],
    type: overrides && overrides.hasOwnProperty('type') ? overrides.type! : CurrencyCategory.Crypto,
  };
};

export const aDatePredicate = (overrides?: Partial<DatePredicate>): DatePredicate => {
  return {
    field:
      overrides && overrides.hasOwnProperty('field')
        ? overrides.field!
        : OrderStateDateField.CreatedAt,
    method:
      overrides && overrides.hasOwnProperty('method') ? overrides.method! : DatePredicateType.From,
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : 'aspernatur',
  };
};

export const aDeactivateVenueAccountInput = (
  overrides?: Partial<DeactivateVenueAccountInput>,
): DeactivateVenueAccountInput => {
  return {
    correlationObject:
      overrides && overrides.hasOwnProperty('correlationObject')
        ? overrides.correlationObject!
        : 'et',
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId') ? overrides.venueAccountId! : 'odio',
  };
};

export const aDefaultTif = (
  overrides?: Partial<DefaultTif>,
): { __typename: 'DefaultTif' } & DefaultTif => {
  return {
    __typename: 'DefaultTif',
    key: overrides && overrides.hasOwnProperty('key') ? overrides.key! : OrderType.Limit,
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : Tif.Day,
  };
};

export const aDeleteRateSubscriptionInput = (
  overrides?: Partial<DeleteRateSubscriptionInput>,
): DeleteRateSubscriptionInput => {
  return {
    baseCurrency:
      overrides && overrides.hasOwnProperty('baseCurrency') ? overrides.baseCurrency! : 'mollitia',
    quoteCurrency:
      overrides && overrides.hasOwnProperty('quoteCurrency')
        ? overrides.quoteCurrency!
        : 'cupiditate',
  };
};

export const aDeposit = (overrides?: Partial<Deposit>): { __typename: 'Deposit' } & Deposit => {
  return {
    __typename: 'Deposit',
    account: overrides && overrides.hasOwnProperty('account') ? overrides.account! : 'est',
    accountName:
      overrides && overrides.hasOwnProperty('accountName') ? overrides.accountName! : 'suscipit',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'magni',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'inventore',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'quam',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'provident',
    feeAccountId:
      overrides && overrides.hasOwnProperty('feeAccountId') ? overrides.feeAccountId! : 'qui',
    feeAccountName:
      overrides && overrides.hasOwnProperty('feeAccountName')
        ? overrides.feeAccountName!
        : 'nostrum',
    feePortfolioId:
      overrides && overrides.hasOwnProperty('feePortfolioId') ? overrides.feePortfolioId! : 'ipsam',
    feePortfolioName:
      overrides && overrides.hasOwnProperty('feePortfolioName')
        ? overrides.feePortfolioName!
        : 'aut',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'est',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName') ? overrides.portfolioName! : 'est',
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 0.18,
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : 'velit',
    settledDateTime:
      overrides && overrides.hasOwnProperty('settledDateTime') ? overrides.settledDateTime! : 9,
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 6.01,
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'autem',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'aliquid',
  };
};

export const aDetailedCapabilities = (
  overrides?: Partial<DetailedCapabilities>,
): { __typename: 'DetailedCapabilities' } & DetailedCapabilities => {
  return {
    __typename: 'DetailedCapabilities',
    accountCapabilities:
      overrides && overrides.hasOwnProperty('accountCapabilities')
        ? overrides.accountCapabilities!
        : anAccountCapabilities(),
    connectorCapabilities:
      overrides && overrides.hasOwnProperty('connectorCapabilities')
        ? overrides.connectorCapabilities!
        : [Capability.Account],
    dropCopyCapabilities:
      overrides && overrides.hasOwnProperty('dropCopyCapabilities')
        ? overrides.dropCopyCapabilities!
        : false,
    genericEventCapabilities:
      overrides && overrides.hasOwnProperty('genericEventCapabilities')
        ? overrides.genericEventCapabilities!
        : true,
    historicalDataCapabilities:
      overrides && overrides.hasOwnProperty('historicalDataCapabilities')
        ? overrides.historicalDataCapabilities!
        : aHistoricalDataCapabilities(),
    marketDataCapabilities:
      overrides && overrides.hasOwnProperty('marketDataCapabilities')
        ? overrides.marketDataCapabilities!
        : aMarketDataCapabilities(),
    rateLimited:
      overrides && overrides.hasOwnProperty('rateLimited') ? overrides.rateLimited! : false,
    referenceDataCapabilities:
      overrides && overrides.hasOwnProperty('referenceDataCapabilities')
        ? overrides.referenceDataCapabilities!
        : aReferenceDataCapabilities(),
    tradingCapabilities:
      overrides && overrides.hasOwnProperty('tradingCapabilities')
        ? overrides.tradingCapabilities!
        : aTradingCapabilities(),
    transferCapabilities:
      overrides && overrides.hasOwnProperty('transferCapabilities')
        ? overrides.transferCapabilities!
        : true,
  };
};

export const anEffectiveVenueAccountsResponse = (
  overrides?: Partial<EffectiveVenueAccountsResponse>,
): { __typename: 'EffectiveVenueAccountsResponse' } & EffectiveVenueAccountsResponse => {
  return {
    __typename: 'EffectiveVenueAccountsResponse',
    isSor: overrides && overrides.hasOwnProperty('isSor') ? overrides.isSor! : false,
    venueAccounts:
      overrides && overrides.hasOwnProperty('venueAccounts') ? overrides.venueAccounts! : ['in'],
  };
};

export const anEventLogResponse = (
  overrides?: Partial<EventLogResponse>,
): { __typename: 'EventLogResponse' } & EventLogResponse => {
  return {
    __typename: 'EventLogResponse',
    clientId: overrides && overrides.hasOwnProperty('clientId') ? overrides.clientId! : 'earum',
    correlationObject:
      overrides && overrides.hasOwnProperty('correlationObject')
        ? overrides.correlationObject!
        : 'eaque',
    eventType: overrides && overrides.hasOwnProperty('eventType') ? overrides.eventType! : 'est',
    message: overrides && overrides.hasOwnProperty('message') ? overrides.message! : 'aspernatur',
    status: overrides && overrides.hasOwnProperty('status') ? overrides.status! : Status.Failure,
    timestamp: overrides && overrides.hasOwnProperty('timestamp') ? overrides.timestamp! : 'maxime',
  };
};

export const anExecutionConfiguration = (
  overrides?: Partial<ExecutionConfiguration>,
): { __typename: 'ExecutionConfiguration' } & ExecutionConfiguration => {
  return {
    __typename: 'ExecutionConfiguration',
    agencyTargetInstrument:
      overrides && overrides.hasOwnProperty('agencyTargetInstrument')
        ? overrides.agencyTargetInstrument!
        : anInstrumentResponse(),
    agencyTradingAccount:
      overrides && overrides.hasOwnProperty('agencyTradingAccount')
        ? overrides.agencyTradingAccount!
        : 'iste',
    agencyTradingAccountName:
      overrides && overrides.hasOwnProperty('agencyTradingAccountName')
        ? overrides.agencyTradingAccountName!
        : 'quae',
    chargeExchangeFee:
      overrides && overrides.hasOwnProperty('chargeExchangeFee')
        ? overrides.chargeExchangeFee!
        : true,
    counterPortfolioId:
      overrides && overrides.hasOwnProperty('counterPortfolioId')
        ? overrides.counterPortfolioId!
        : 'id',
    counterPortfolioName:
      overrides && overrides.hasOwnProperty('counterPortfolioName')
        ? overrides.counterPortfolioName!
        : 'iure',
    discloseTradingVenue:
      overrides && overrides.hasOwnProperty('discloseTradingVenue')
        ? overrides.discloseTradingVenue!
        : true,
    executionMode:
      overrides && overrides.hasOwnProperty('executionMode')
        ? overrides.executionMode!
        : ExecutionMode.Simple,
    fixedFee: overrides && overrides.hasOwnProperty('fixedFee') ? overrides.fixedFee! : 5.3,
    fixedFeeCurrency:
      overrides && overrides.hasOwnProperty('fixedFeeCurrency')
        ? overrides.fixedFeeCurrency!
        : 'voluptas',
    minFee: overrides && overrides.hasOwnProperty('minFee') ? overrides.minFee! : 3.2,
    minFeeCurrency:
      overrides && overrides.hasOwnProperty('minFeeCurrency')
        ? overrides.minFeeCurrency!
        : 'possimus',
    percentageFee:
      overrides && overrides.hasOwnProperty('percentageFee') ? overrides.percentageFee! : 9.5,
    percentageFeeCurrency:
      overrides && overrides.hasOwnProperty('percentageFeeCurrency')
        ? overrides.percentageFeeCurrency!
        : 'unde',
    percentageFeeCurrencyType:
      overrides && overrides.hasOwnProperty('percentageFeeCurrencyType')
        ? overrides.percentageFeeCurrencyType!
        : CurrencyType.BaseCurrency,
    sorTarget:
      overrides && overrides.hasOwnProperty('sorTarget') ? overrides.sorTarget! : aSorTarget(),
    sorTradingAccountDescs:
      overrides && overrides.hasOwnProperty('sorTradingAccountDescs')
        ? overrides.sorTradingAccountDescs!
        : [aVenueAccountDesc()],
    tradingMode:
      overrides && overrides.hasOwnProperty('tradingMode')
        ? overrides.tradingMode!
        : TradingMode.Agency,
  };
};

export const anExecutionConfigurationInput = (
  overrides?: Partial<ExecutionConfigurationInput>,
): ExecutionConfigurationInput => {
  return {
    agencyTargetInstrumentId:
      overrides && overrides.hasOwnProperty('agencyTargetInstrumentId')
        ? overrides.agencyTargetInstrumentId!
        : 'adipisci',
    agencyTradingAccount:
      overrides && overrides.hasOwnProperty('agencyTradingAccount')
        ? overrides.agencyTradingAccount!
        : 'pariatur',
    chargeExchangeFee:
      overrides && overrides.hasOwnProperty('chargeExchangeFee')
        ? overrides.chargeExchangeFee!
        : true,
    counterPortfolioId:
      overrides && overrides.hasOwnProperty('counterPortfolioId')
        ? overrides.counterPortfolioId!
        : 'esse',
    discloseTradingVenue:
      overrides && overrides.hasOwnProperty('discloseTradingVenue')
        ? overrides.discloseTradingVenue!
        : true,
    executionMode:
      overrides && overrides.hasOwnProperty('executionMode')
        ? overrides.executionMode!
        : ExecutionMode.Simple,
    fixedFee: overrides && overrides.hasOwnProperty('fixedFee') ? overrides.fixedFee! : 8.13,
    fixedFeeCurrency:
      overrides && overrides.hasOwnProperty('fixedFeeCurrency')
        ? overrides.fixedFeeCurrency!
        : 'cumque',
    minFee: overrides && overrides.hasOwnProperty('minFee') ? overrides.minFee! : 8.55,
    minFeeCurrency:
      overrides && overrides.hasOwnProperty('minFeeCurrency')
        ? overrides.minFeeCurrency!
        : 'officiis',
    percentageFee:
      overrides && overrides.hasOwnProperty('percentageFee') ? overrides.percentageFee! : 9.55,
    percentageFeeCurrency:
      overrides && overrides.hasOwnProperty('percentageFeeCurrency')
        ? overrides.percentageFeeCurrency!
        : 'fugiat',
    percentageFeeCurrencyType:
      overrides && overrides.hasOwnProperty('percentageFeeCurrencyType')
        ? overrides.percentageFeeCurrencyType!
        : CurrencyType.BaseCurrency,
    sorTarget:
      overrides && overrides.hasOwnProperty('sorTarget') ? overrides.sorTarget! : aSorTargetInput(),
    sorTradingAccounts:
      overrides && overrides.hasOwnProperty('sorTradingAccounts')
        ? overrides.sorTradingAccounts!
        : ['voluptas'],
    tradingMode:
      overrides && overrides.hasOwnProperty('tradingMode')
        ? overrides.tradingMode!
        : TradingMode.Agency,
  };
};

export const anExecutionReportResponse = (
  overrides?: Partial<ExecutionReportResponse>,
): { __typename: 'ExecutionReportResponse' } & ExecutionReportResponse => {
  return {
    __typename: 'ExecutionReportResponse',
    avgPrice:
      overrides && overrides.hasOwnProperty('avgPrice') ? overrides.avgPrice! : 'perspiciatis',
    clOrderId: overrides && overrides.hasOwnProperty('clOrderId') ? overrides.clOrderId! : 'ipsam',
    clientId: overrides && overrides.hasOwnProperty('clientId') ? overrides.clientId! : 'est',
    createdAt:
      overrides && overrides.hasOwnProperty('createdAt') ? overrides.createdAt! : 'sapiente',
    cumQty: overrides && overrides.hasOwnProperty('cumQty') ? overrides.cumQty! : 3.22,
    currency:
      overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'voluptatem',
    execType:
      overrides && overrides.hasOwnProperty('execType') ? overrides.execType! : ExecType.Calculated,
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'sed',
    fee: overrides && overrides.hasOwnProperty('fee') ? overrides.fee! : 1.86,
    feeCurrency:
      overrides && overrides.hasOwnProperty('feeCurrency') ? overrides.feeCurrency! : 'minima',
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId') ? overrides.instrumentId! : 'labore',
    instrumentType:
      overrides && overrides.hasOwnProperty('instrumentType')
        ? overrides.instrumentType!
        : InstrumentType.Bond,
    lastPrice: overrides && overrides.hasOwnProperty('lastPrice') ? overrides.lastPrice! : 'a',
    lastQty: overrides && overrides.hasOwnProperty('lastQty') ? overrides.lastQty! : 3.72,
    leavesQty: overrides && overrides.hasOwnProperty('leavesQty') ? overrides.leavesQty! : 8.61,
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'qui',
    orderQty: overrides && overrides.hasOwnProperty('orderQty') ? overrides.orderQty! : 0.54,
    orderStatus:
      overrides && overrides.hasOwnProperty('orderStatus')
        ? overrides.orderStatus!
        : OrderStatus.Calculated,
    orderStatusRequestId:
      overrides && overrides.hasOwnProperty('orderStatusRequestId')
        ? overrides.orderStatusRequestId!
        : 'dolore',
    origClOrderId:
      overrides && overrides.hasOwnProperty('origClOrderId')
        ? overrides.origClOrderId!
        : 'corrupti',
    parentOrderId:
      overrides && overrides.hasOwnProperty('parentOrderId') ? overrides.parentOrderId! : 'dolorem',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'voluptate',
    reason: overrides && overrides.hasOwnProperty('reason') ? overrides.reason! : 'aliquid',
    result: overrides && overrides.hasOwnProperty('result') ? overrides.result! : 'et',
    sequenceNumber:
      overrides && overrides.hasOwnProperty('sequenceNumber') ? overrides.sequenceNumber! : 3143,
    side: overrides && overrides.hasOwnProperty('side') ? overrides.side! : Side.Buy,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'error',
    targetVenueAccount:
      overrides && overrides.hasOwnProperty('targetVenueAccount')
        ? overrides.targetVenueAccount!
        : 'soluta',
    targetVenueTicker:
      overrides && overrides.hasOwnProperty('targetVenueTicker')
        ? overrides.targetVenueTicker!
        : 'asperiores',
    targetVenueTimestamp:
      overrides && overrides.hasOwnProperty('targetVenueTimestamp')
        ? overrides.targetVenueTimestamp!
        : 'omnis',
    text: overrides && overrides.hasOwnProperty('text') ? overrides.text! : 'quo',
    timestamp: overrides && overrides.hasOwnProperty('timestamp') ? overrides.timestamp! : 'quo',
    underlyingVenueAccount:
      overrides && overrides.hasOwnProperty('underlyingVenueAccount')
        ? overrides.underlyingVenueAccount!
        : 'facilis',
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 'eum',
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'quia',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'eaque',
  };
};

export const aFee = (overrides?: Partial<Fee>): { __typename: 'Fee' } & Fee => {
  return {
    __typename: 'Fee',
    account: overrides && overrides.hasOwnProperty('account') ? overrides.account! : 'magni',
    accountName:
      overrides && overrides.hasOwnProperty('accountName') ? overrides.accountName! : 'aliquid',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'debitis',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'ducimus',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'optio',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'nesciunt',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'ea',
    parentOrderId:
      overrides && overrides.hasOwnProperty('parentOrderId') ? overrides.parentOrderId! : 'est',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'voluptatem',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName') ? overrides.portfolioName! : 'quis',
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 5.79,
    rootExecution:
      overrides && overrides.hasOwnProperty('rootExecution')
        ? overrides.rootExecution!
        : aRootExecution(),
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : 'quae',
    settledDateTime:
      overrides && overrides.hasOwnProperty('settledDateTime') ? overrides.settledDateTime! : 7.5,
    underlyingExecutionId:
      overrides && overrides.hasOwnProperty('underlyingExecutionId')
        ? overrides.underlyingExecutionId!
        : 'quo',
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 3.84,
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'est',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'culpa',
  };
};

export const aFlatPermission = (
  overrides?: Partial<FlatPermission>,
): { __typename: 'FlatPermission' } & FlatPermission => {
  return {
    __typename: 'FlatPermission',
    groups:
      overrides && overrides.hasOwnProperty('groups') ? overrides.groups! : [aGroupPermission()],
    resource:
      overrides && overrides.hasOwnProperty('resource') ? overrides.resource! : Resource.ApiKey,
    resourceId: overrides && overrides.hasOwnProperty('resourceId') ? overrides.resourceId! : 'ad',
    userScopes:
      overrides && overrides.hasOwnProperty('userScopes') ? overrides.userScopes! : [Scope.Create],
    username: overrides && overrides.hasOwnProperty('username') ? overrides.username! : 'sint',
  };
};

export const aForexSpotPropertiesResponse = (
  overrides?: Partial<ForexSpotPropertiesResponse>,
): { __typename: 'ForexSpotPropertiesResponse' } & ForexSpotPropertiesResponse => {
  return {
    __typename: 'ForexSpotPropertiesResponse',
    baseCurrency:
      overrides && overrides.hasOwnProperty('baseCurrency') ? overrides.baseCurrency! : 'animi',
  };
};

export const aGroupPermission = (
  overrides?: Partial<GroupPermission>,
): { __typename: 'GroupPermission' } & GroupPermission => {
  return {
    __typename: 'GroupPermission',
    groupName: overrides && overrides.hasOwnProperty('groupName') ? overrides.groupName! : 'maxime',
    groupScopes:
      overrides && overrides.hasOwnProperty('groupScopes')
        ? overrides.groupScopes!
        : [Scope.Create],
  };
};

export const aGroupResponse = (
  overrides?: Partial<GroupResponse>,
): { __typename: 'GroupResponse' } & GroupResponse => {
  return {
    __typename: 'GroupResponse',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'ut',
    permissions:
      overrides && overrides.hasOwnProperty('permissions')
        ? overrides.permissions!
        : [aPermission()],
  };
};

export const aGroupsPermissionsForResourceResponse = (
  overrides?: Partial<GroupsPermissionsForResourceResponse>,
): {
  __typename: 'GroupsPermissionsForResourceResponse';
} & GroupsPermissionsForResourceResponse => {
  return {
    __typename: 'GroupsPermissionsForResourceResponse',
    groupName:
      overrides && overrides.hasOwnProperty('groupName') ? overrides.groupName! : 'assumenda',
    scopes: overrides && overrides.hasOwnProperty('scopes') ? overrides.scopes! : [Scope.Create],
  };
};

export const aHedgeResult = (
  overrides?: Partial<HedgeResult>,
): { __typename: 'HedgeResult' } & HedgeResult => {
  return {
    __typename: 'HedgeResult',
    breakEvenFxRate:
      overrides && overrides.hasOwnProperty('breakEvenFxRate') ? overrides.breakEvenFxRate! : 1.07,
    clobOrderId:
      overrides && overrides.hasOwnProperty('clobOrderId') ? overrides.clobOrderId! : 'quis',
    clobRootOrderId:
      overrides && overrides.hasOwnProperty('clobRootOrderId')
        ? overrides.clobRootOrderId!
        : 'aspernatur',
    estimatedPrice:
      overrides && overrides.hasOwnProperty('estimatedPrice') ? overrides.estimatedPrice! : 7.86,
    executionPrice:
      overrides && overrides.hasOwnProperty('executionPrice') ? overrides.executionPrice! : 2.65,
    hedgeOrderAmount:
      overrides && overrides.hasOwnProperty('hedgeOrderAmount')
        ? overrides.hedgeOrderAmount!
        : 0.86,
    hedgeOrderAmountInClobQuoteCurrency:
      overrides && overrides.hasOwnProperty('hedgeOrderAmountInClobQuoteCurrency')
        ? overrides.hedgeOrderAmountInClobQuoteCurrency!
        : 5.86,
    hedgeOrderId:
      overrides && overrides.hasOwnProperty('hedgeOrderId')
        ? overrides.hedgeOrderId!
        : 'asperiores',
    hedgeOrderLimitPrice:
      overrides && overrides.hasOwnProperty('hedgeOrderLimitPrice')
        ? overrides.hedgeOrderLimitPrice!
        : 7.53,
    hedgeOrderQuantity:
      overrides && overrides.hasOwnProperty('hedgeOrderQuantity')
        ? overrides.hedgeOrderQuantity!
        : 5.85,
    hedgeOrderSide:
      overrides && overrides.hasOwnProperty('hedgeOrderSide')
        ? overrides.hedgeOrderSide!
        : Side.Buy,
    hedgeRootOrderId:
      overrides && overrides.hasOwnProperty('hedgeRootOrderId')
        ? overrides.hedgeRootOrderId!
        : 'qui',
    hedgingVenue:
      overrides && overrides.hasOwnProperty('hedgingVenue')
        ? overrides.hedgingVenue!
        : 'voluptates',
    matchId: overrides && overrides.hasOwnProperty('matchId') ? overrides.matchId! : 'praesentium',
    primarySymbolQuote:
      overrides && overrides.hasOwnProperty('primarySymbolQuote')
        ? overrides.primarySymbolQuote!
        : aMarketQuote(),
    secondarySymbolQuote:
      overrides && overrides.hasOwnProperty('secondarySymbolQuote')
        ? overrides.secondarySymbolQuote!
        : aMarketQuote(),
    success: overrides && overrides.hasOwnProperty('success') ? overrides.success! : false,
    timestamp:
      overrides && overrides.hasOwnProperty('timestamp') ? overrides.timestamp! : 'voluptate',
  };
};

export const aHedgingConfigValidationResult = (
  overrides?: Partial<HedgingConfigValidationResult>,
): { __typename: 'HedgingConfigValidationResult' } & HedgingConfigValidationResult => {
  return {
    __typename: 'HedgingConfigValidationResult',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'eveniet',
    resultsPerCurrency:
      overrides && overrides.hasOwnProperty('resultsPerCurrency')
        ? overrides.resultsPerCurrency!
        : [aConfigCurrencyValidationResult()],
  };
};

export const aHedgingConfiguration = (
  overrides?: Partial<HedgingConfiguration>,
): { __typename: 'HedgingConfiguration' } & HedgingConfiguration => {
  return {
    __typename: 'HedgingConfiguration',
    autoHedging:
      overrides && overrides.hasOwnProperty('autoHedging') ? overrides.autoHedging! : true,
    targetAccountId:
      overrides && overrides.hasOwnProperty('targetAccountId')
        ? overrides.targetAccountId!
        : 'nihil',
    targetAccountName:
      overrides && overrides.hasOwnProperty('targetAccountName')
        ? overrides.targetAccountName!
        : 'error',
    thresholdConfiguration:
      overrides && overrides.hasOwnProperty('thresholdConfiguration')
        ? overrides.thresholdConfiguration!
        : [aThresholdConfiguration()],
  };
};

export const aHedgingConfigurationInput = (
  overrides?: Partial<HedgingConfigurationInput>,
): HedgingConfigurationInput => {
  return {
    autoHedging:
      overrides && overrides.hasOwnProperty('autoHedging') ? overrides.autoHedging! : false,
    targetAccountId:
      overrides && overrides.hasOwnProperty('targetAccountId')
        ? overrides.targetAccountId!
        : 'quos',
    thresholdConfiguration:
      overrides && overrides.hasOwnProperty('thresholdConfiguration')
        ? overrides.thresholdConfiguration!
        : [aThresholdConfigurationInput()],
  };
};

export const aHistoricalDataCapabilities = (
  overrides?: Partial<HistoricalDataCapabilities>,
): { __typename: 'HistoricalDataCapabilities' } & HistoricalDataCapabilities => {
  return {
    __typename: 'HistoricalDataCapabilities',
    supportingCollectingAsks:
      overrides && overrides.hasOwnProperty('supportingCollectingAsks')
        ? overrides.supportingCollectingAsks!
        : true,
    supportingCollectingBars:
      overrides && overrides.hasOwnProperty('supportingCollectingBars')
        ? overrides.supportingCollectingBars!
        : true,
    supportingCollectingBidAsks:
      overrides && overrides.hasOwnProperty('supportingCollectingBidAsks')
        ? overrides.supportingCollectingBidAsks!
        : true,
    supportingCollectingBids:
      overrides && overrides.hasOwnProperty('supportingCollectingBids')
        ? overrides.supportingCollectingBids!
        : false,
    supportingCollectingTrades:
      overrides && overrides.hasOwnProperty('supportingCollectingTrades')
        ? overrides.supportingCollectingTrades!
        : true,
  };
};

export const aHistoryOrderStatusesInput = (
  overrides?: Partial<HistoryOrderStatusesInput>,
): HistoryOrderStatusesInput => {
  return {
    includeRelated:
      overrides && overrides.hasOwnProperty('includeRelated') ? overrides.includeRelated! : false,
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'architecto',
  };
};

export const anInstrumentConfiguration = (
  overrides?: Partial<InstrumentConfiguration>,
): { __typename: 'InstrumentConfiguration' } & InstrumentConfiguration => {
  return {
    __typename: 'InstrumentConfiguration',
    executionConfiguration:
      overrides && overrides.hasOwnProperty('executionConfiguration')
        ? overrides.executionConfiguration!
        : anExecutionConfiguration(),
    instrumentGroupConfiguration:
      overrides && overrides.hasOwnProperty('instrumentGroupConfiguration')
        ? overrides.instrumentGroupConfiguration!
        : anInstrumentGroupConfiguration(),
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId')
        ? overrides.instrumentId!
        : 'e310fd56-7ec3-40cd-9411-432f61b59d75',
    pricingConfiguration:
      overrides && overrides.hasOwnProperty('pricingConfiguration')
        ? overrides.pricingConfiguration!
        : anInstrumentPricingConfiguration(),
    tradeable: overrides && overrides.hasOwnProperty('tradeable') ? overrides.tradeable! : false,
  };
};

export const anInstrumentConfigurationInput = (
  overrides?: Partial<InstrumentConfigurationInput>,
): InstrumentConfigurationInput => {
  return {
    executionConfiguration:
      overrides && overrides.hasOwnProperty('executionConfiguration')
        ? overrides.executionConfiguration!
        : anExecutionConfigurationInput(),
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId')
        ? overrides.instrumentId!
        : '9494fe6f-dd6a-47ad-afce-8885f87a1d30',
    pricingConfiguration:
      overrides && overrides.hasOwnProperty('pricingConfiguration')
        ? overrides.pricingConfiguration!
        : anInstrumentPricingConfigurationInput(),
    tradeable: overrides && overrides.hasOwnProperty('tradeable') ? overrides.tradeable! : true,
  };
};

export const anInstrumentEdge = (
  overrides?: Partial<InstrumentEdge>,
): { __typename: 'InstrumentEdge' } & InstrumentEdge => {
  return {
    __typename: 'InstrumentEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'enim',
    node: overrides && overrides.hasOwnProperty('node') ? overrides.node! : anInstrumentResponse(),
  };
};

export const anInstrumentGroupConfiguration = (
  overrides?: Partial<InstrumentGroupConfiguration>,
): { __typename: 'InstrumentGroupConfiguration' } & InstrumentGroupConfiguration => {
  return {
    __typename: 'InstrumentGroupConfiguration',
    executionConfiguration:
      overrides && overrides.hasOwnProperty('executionConfiguration')
        ? overrides.executionConfiguration!
        : anExecutionConfiguration(),
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId')
        ? overrides.instrumentId!
        : '4d1a68c2-959c-4109-bf89-04a015835a29',
    pricingConfiguration:
      overrides && overrides.hasOwnProperty('pricingConfiguration')
        ? overrides.pricingConfiguration!
        : anInstrumentPricingConfiguration(),
    tradeable: overrides && overrides.hasOwnProperty('tradeable') ? overrides.tradeable! : false,
  };
};

export const anInstrumentGroupConfigurationInput = (
  overrides?: Partial<InstrumentGroupConfigurationInput>,
): InstrumentGroupConfigurationInput => {
  return {
    executionConfiguration:
      overrides && overrides.hasOwnProperty('executionConfiguration')
        ? overrides.executionConfiguration!
        : anExecutionConfigurationInput(),
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId')
        ? overrides.instrumentId!
        : '36c1ca79-b393-49f3-ac79-53807f4b145a',
    pricingConfiguration:
      overrides && overrides.hasOwnProperty('pricingConfiguration')
        ? overrides.pricingConfiguration!
        : anInstrumentPricingConfigurationInput(),
    tradeable: overrides && overrides.hasOwnProperty('tradeable') ? overrides.tradeable! : true,
  };
};

export const anInstrumentIdentifiersResponse = (
  overrides?: Partial<InstrumentIdentifiersResponse>,
): { __typename: 'InstrumentIdentifiersResponse' } & InstrumentIdentifiersResponse => {
  return {
    __typename: 'InstrumentIdentifiersResponse',
    adapterTicker:
      overrides && overrides.hasOwnProperty('adapterTicker') ? overrides.adapterTicker! : 'ducimus',
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId') ? overrides.instrumentId! : 'non',
    tradingViewId:
      overrides && overrides.hasOwnProperty('tradingViewId')
        ? overrides.tradingViewId!
        : 'molestiae',
    venueTradingViewId:
      overrides && overrides.hasOwnProperty('venueTradingViewId')
        ? overrides.venueTradingViewId!
        : 'nisi',
  };
};

export const anInstrumentIdsPredicateInput = (
  overrides?: Partial<InstrumentIdsPredicateInput>,
): InstrumentIdsPredicateInput => {
  return {
    instrumentIds:
      overrides && overrides.hasOwnProperty('instrumentIds') ? overrides.instrumentIds! : ['enim'],
    method:
      overrides && overrides.hasOwnProperty('method')
        ? overrides.method!
        : InstrumentIdsPredicateType.Contains,
  };
};

export const anInstrumentKey = (
  overrides?: Partial<InstrumentKey>,
): { __typename: 'InstrumentKey' } & InstrumentKey => {
  return {
    __typename: 'InstrumentKey',
    instrument:
      overrides && overrides.hasOwnProperty('instrument')
        ? overrides.instrument!
        : anInstrumentResponse(),
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'ad',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'voluptas',
  };
};

export const anInstrumentKeyInput = (
  overrides?: Partial<InstrumentKeyInput>,
): InstrumentKeyInput => {
  return {
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId') ? overrides.instrumentId! : 'eligendi',
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'et',
  };
};

export const anInstrumentPricingConfiguration = (
  overrides?: Partial<InstrumentPricingConfiguration>,
): { __typename: 'InstrumentPricingConfiguration' } & InstrumentPricingConfiguration => {
  return {
    __typename: 'InstrumentPricingConfiguration',
    markup: overrides && overrides.hasOwnProperty('markup') ? overrides.markup! : 4.24,
    pricingSource:
      overrides && overrides.hasOwnProperty('pricingSource')
        ? overrides.pricingSource!
        : [anInstrumentKey()],
  };
};

export const anInstrumentPricingConfigurationInput = (
  overrides?: Partial<InstrumentPricingConfigurationInput>,
): InstrumentPricingConfigurationInput => {
  return {
    markup: overrides && overrides.hasOwnProperty('markup') ? overrides.markup! : 0.25,
    pricingSource:
      overrides && overrides.hasOwnProperty('pricingSource')
        ? overrides.pricingSource!
        : [anInstrumentKeyInput()],
  };
};

export const anInstrumentQuotingConfiguration = (
  overrides?: Partial<InstrumentQuotingConfiguration>,
): { __typename: 'InstrumentQuotingConfiguration' } & InstrumentQuotingConfiguration => {
  return {
    __typename: 'InstrumentQuotingConfiguration',
    askMarkup: overrides && overrides.hasOwnProperty('askMarkup') ? overrides.askMarkup! : 1.23,
    bidMarkup: overrides && overrides.hasOwnProperty('bidMarkup') ? overrides.bidMarkup! : 9,
    deactivated:
      overrides && overrides.hasOwnProperty('deactivated') ? overrides.deactivated! : true,
    hedgingSafetyMargin:
      overrides && overrides.hasOwnProperty('hedgingSafetyMargin')
        ? overrides.hedgingSafetyMargin!
        : 8.67,
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId') ? overrides.instrumentId! : 'porro',
    maxQuantityFactor:
      overrides && overrides.hasOwnProperty('maxQuantityFactor')
        ? overrides.maxQuantityFactor!
        : 8.99,
    maximumDepth:
      overrides && overrides.hasOwnProperty('maximumDepth') ? overrides.maximumDepth! : 5.39,
    minQuantityFactor:
      overrides && overrides.hasOwnProperty('minQuantityFactor')
        ? overrides.minQuantityFactor!
        : 8.59,
    priceLevelIncrement:
      overrides && overrides.hasOwnProperty('priceLevelIncrement')
        ? overrides.priceLevelIncrement!
        : 1.05,
    quantityIncrement:
      overrides && overrides.hasOwnProperty('quantityIncrement')
        ? overrides.quantityIncrement!
        : 5.52,
    quoteTTL: overrides && overrides.hasOwnProperty('quoteTTL') ? overrides.quoteTTL! : 5216,
    sourceConfigurations:
      overrides && overrides.hasOwnProperty('sourceConfigurations')
        ? overrides.sourceConfigurations!
        : [aSourceConfiguration()],
  };
};

export const anInstrumentQuotingConfigurationInput = (
  overrides?: Partial<InstrumentQuotingConfigurationInput>,
): InstrumentQuotingConfigurationInput => {
  return {
    askMarkup: overrides && overrides.hasOwnProperty('askMarkup') ? overrides.askMarkup! : 2.62,
    bidMarkup: overrides && overrides.hasOwnProperty('bidMarkup') ? overrides.bidMarkup! : 3.28,
    deactivated:
      overrides && overrides.hasOwnProperty('deactivated') ? overrides.deactivated! : true,
    hedgingSafetyMargin:
      overrides && overrides.hasOwnProperty('hedgingSafetyMargin')
        ? overrides.hedgingSafetyMargin!
        : 6.46,
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId') ? overrides.instrumentId! : 'error',
    maxQuantityFactor:
      overrides && overrides.hasOwnProperty('maxQuantityFactor')
        ? overrides.maxQuantityFactor!
        : 5.49,
    maximumDepth:
      overrides && overrides.hasOwnProperty('maximumDepth') ? overrides.maximumDepth! : 8.03,
    minQuantityFactor:
      overrides && overrides.hasOwnProperty('minQuantityFactor')
        ? overrides.minQuantityFactor!
        : 3.75,
    priceLevelIncrement:
      overrides && overrides.hasOwnProperty('priceLevelIncrement')
        ? overrides.priceLevelIncrement!
        : 5.24,
    quantityIncrement:
      overrides && overrides.hasOwnProperty('quantityIncrement')
        ? overrides.quantityIncrement!
        : 4.42,
    quoteTTL: overrides && overrides.hasOwnProperty('quoteTTL') ? overrides.quoteTTL! : 6524,
    sourceConfigurations:
      overrides && overrides.hasOwnProperty('sourceConfigurations')
        ? overrides.sourceConfigurations!
        : [aSourceConfigurationInput()],
  };
};

export const anInstrumentResponse = (
  overrides?: Partial<InstrumentResponse>,
): { __typename: 'InstrumentResponse' } & InstrumentResponse => {
  return {
    __typename: 'InstrumentResponse',
    archivedAt:
      overrides && overrides.hasOwnProperty('archivedAt') ? overrides.archivedAt! : 'molestiae',
    baseInstrument:
      overrides && overrides.hasOwnProperty('baseInstrument')
        ? overrides.baseInstrument!
        : aBaseInstrumentResponse(),
    createdAt: overrides && overrides.hasOwnProperty('createdAt') ? overrides.createdAt! : 'id',
    forexSpotProperties:
      overrides && overrides.hasOwnProperty('forexSpotProperties')
        ? overrides.forexSpotProperties!
        : aForexSpotPropertiesResponse(),
    instrumentIdentifiers:
      overrides && overrides.hasOwnProperty('instrumentIdentifiers')
        ? overrides.instrumentIdentifiers!
        : anInstrumentIdentifiersResponse(),
    tradingConstraints:
      overrides && overrides.hasOwnProperty('tradingConstraints')
        ? overrides.tradingConstraints!
        : aTradingConstraintsResponse(),
    updatedAt:
      overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 'dolorem',
  };
};

export const anInstrumentsConnection = (
  overrides?: Partial<InstrumentsConnection>,
): { __typename: 'InstrumentsConnection' } & InstrumentsConnection => {
  return {
    __typename: 'InstrumentsConnection',
    edges: overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [anInstrumentEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const anInstrumentsRefreshRequestInput = (
  overrides?: Partial<InstrumentsRefreshRequestInput>,
): InstrumentsRefreshRequestInput => {
  return {
    correlationObject:
      overrides && overrides.hasOwnProperty('correlationObject')
        ? overrides.correlationObject!
        : 'molestias',
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'excepturi',
  };
};

export const anInstrumentsSearchInput = (
  overrides?: Partial<InstrumentsSearchInput>,
): InstrumentsSearchInput => {
  return {
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'quidem',
    archived: overrides && overrides.hasOwnProperty('archived') ? overrides.archived! : false,
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 4453,
    instrumentIdsPredicate:
      overrides && overrides.hasOwnProperty('instrumentIdsPredicate')
        ? overrides.instrumentIdsPredicate!
        : anInstrumentIdsPredicateInput(),
    sortBy:
      overrides && overrides.hasOwnProperty('sortBy')
        ? overrides.sortBy!
        : InstrumentSortBy.CreatedAt,
    sortingOrder:
      overrides && overrides.hasOwnProperty('sortingOrder')
        ? overrides.sortingOrder!
        : SortingOrder.Asc,
    tradeable: overrides && overrides.hasOwnProperty('tradeable') ? overrides.tradeable! : true,
    venueNames:
      overrides && overrides.hasOwnProperty('venueNames') ? overrides.venueNames! : ['veniam'],
    venueType:
      overrides && overrides.hasOwnProperty('venueType') ? overrides.venueType! : VenueType.Client,
  };
};

export const anInternalExchangeConfigurationInput = (
  overrides?: Partial<InternalExchangeConfigurationInput>,
): InternalExchangeConfigurationInput => {
  return {
    account: overrides && overrides.hasOwnProperty('account') ? overrides.account! : 'velit',
    availableOrderTypes:
      overrides && overrides.hasOwnProperty('availableOrderTypes')
        ? overrides.availableOrderTypes!
        : [OrderType.Limit],
    availableTifs:
      overrides && overrides.hasOwnProperty('availableTifs') ? overrides.availableTifs! : [Tif.Day],
  };
};

export const anInternalExchangeConfigurationResponse = (
  overrides?: Partial<InternalExchangeConfigurationResponse>,
): {
  __typename: 'InternalExchangeConfigurationResponse';
} & InternalExchangeConfigurationResponse => {
  return {
    __typename: 'InternalExchangeConfigurationResponse',
    account: overrides && overrides.hasOwnProperty('account') ? overrides.account! : 'nihil',
    accountName:
      overrides && overrides.hasOwnProperty('accountName') ? overrides.accountName! : 'itaque',
    availableOrderTypes:
      overrides && overrides.hasOwnProperty('availableOrderTypes')
        ? overrides.availableOrderTypes!
        : [OrderType.Limit],
    availableTifs:
      overrides && overrides.hasOwnProperty('availableTifs') ? overrides.availableTifs! : [Tif.Day],
  };
};

export const aKeyValue = (overrides?: Partial<KeyValue>): { __typename: 'KeyValue' } & KeyValue => {
  return {
    __typename: 'KeyValue',
    key: overrides && overrides.hasOwnProperty('key') ? overrides.key! : 'pariatur',
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : 'voluptate',
  };
};

export const aKeyValueInput = (overrides?: Partial<KeyValueInput>): KeyValueInput => {
  return {
    key: overrides && overrides.hasOwnProperty('key') ? overrides.key! : 'tenetur',
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : 'quia',
  };
};

export const aKeyValues = (
  overrides?: Partial<KeyValues>,
): { __typename: 'KeyValues' } & KeyValues => {
  return {
    __typename: 'KeyValues',
    key: overrides && overrides.hasOwnProperty('key') ? overrides.key! : 'amet',
    values: overrides && overrides.hasOwnProperty('values') ? overrides.values! : ['voluptatem'],
  };
};

export const aLedgerEntryConnection = (
  overrides?: Partial<LedgerEntryConnection>,
): { __typename: 'LedgerEntryConnection' } & LedgerEntryConnection => {
  return {
    __typename: 'LedgerEntryConnection',
    edges: overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [aLedgerEntryEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aLedgerEntryEdge = (
  overrides?: Partial<LedgerEntryEdge>,
): { __typename: 'LedgerEntryEdge' } & LedgerEntryEdge => {
  return {
    __typename: 'LedgerEntryEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'tempore',
    node: overrides && overrides.hasOwnProperty('node') ? overrides.node! : aLedgerEntryResponse(),
  };
};

export const aLedgerEntryResponse = (
  overrides?: Partial<LedgerEntryResponse>,
): { __typename: 'LedgerEntryResponse' } & LedgerEntryResponse => {
  return {
    __typename: 'LedgerEntryResponse',
    accountId: overrides && overrides.hasOwnProperty('accountId') ? overrides.accountId! : 'vel',
    accountName:
      overrides && overrides.hasOwnProperty('accountName')
        ? overrides.accountName!
        : 'voluptatibus',
    balanceAfter:
      overrides && overrides.hasOwnProperty('balanceAfter') ? overrides.balanceAfter! : 5.35,
    balanceBefore:
      overrides && overrides.hasOwnProperty('balanceBefore') ? overrides.balanceBefore! : 9.01,
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'sed',
    fee: overrides && overrides.hasOwnProperty('fee') ? overrides.fee! : 1.92,
    id:
      overrides && overrides.hasOwnProperty('id')
        ? overrides.id!
        : '0229fd9a-d529-45fa-a781-19dbd5a0b414',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'minus',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'quas',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName')
        ? overrides.portfolioName!
        : 'inventore',
    price: overrides && overrides.hasOwnProperty('price') ? overrides.price! : 4.33,
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 3.94,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'natus',
    transactionId:
      overrides && overrides.hasOwnProperty('transactionId') ? overrides.transactionId! : 'quis',
    type:
      overrides && overrides.hasOwnProperty('type')
        ? overrides.type!
        : LedgerEntryType.AssetTradeBuy,
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 4.77,
  };
};

export const aLedgerEntrySearchInput = (
  overrides?: Partial<LedgerEntrySearchInput>,
): LedgerEntrySearchInput => {
  return {
    accountName:
      overrides && overrides.hasOwnProperty('accountName')
        ? overrides.accountName!
        : ['accusantium'],
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'itaque',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : ['quo'],
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 7002,
    from: overrides && overrides.hasOwnProperty('from') ? overrides.from! : 'voluptatem',
    ledgerEntryType:
      overrides && overrides.hasOwnProperty('ledgerEntryType')
        ? overrides.ledgerEntryType!
        : [LedgerEntryType.AssetTradeBuy],
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'atque',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : ['est'],
    sortingOrder:
      overrides && overrides.hasOwnProperty('sortingOrder')
        ? overrides.sortingOrder!
        : SortingOrder.Asc,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : ['corporis'],
    to: overrides && overrides.hasOwnProperty('to') ? overrides.to! : 'tenetur',
    transactionId:
      overrides && overrides.hasOwnProperty('transactionId') ? overrides.transactionId! : 'et',
  };
};

export const aMarketDataCapabilities = (
  overrides?: Partial<MarketDataCapabilities>,
): { __typename: 'MarketDataCapabilities' } & MarketDataCapabilities => {
  return {
    __typename: 'MarketDataCapabilities',
    fetchingMarketDataSupported:
      overrides && overrides.hasOwnProperty('fetchingMarketDataSupported')
        ? overrides.fetchingMarketDataSupported!
        : true,
    marketScanSupported:
      overrides && overrides.hasOwnProperty('marketScanSupported')
        ? overrides.marketScanSupported!
        : false,
    otcBroker: overrides && overrides.hasOwnProperty('otcBroker') ? overrides.otcBroker! : false,
    streamingLiveMarketDataSupported:
      overrides && overrides.hasOwnProperty('streamingLiveMarketDataSupported')
        ? overrides.streamingLiveMarketDataSupported!
        : false,
    supportedOrderBookLevels:
      overrides && overrides.hasOwnProperty('supportedOrderBookLevels')
        ? overrides.supportedOrderBookLevels!
        : [OrderBookLevel.L1],
    supportedSecurityTypes:
      overrides && overrides.hasOwnProperty('supportedSecurityTypes')
        ? overrides.supportedSecurityTypes!
        : [InstrumentType.Bond],
  };
};

export const aMarketDataResponse = (
  overrides?: Partial<MarketDataResponse>,
): { __typename: 'MarketDataResponse' } & MarketDataResponse => {
  return {
    __typename: 'MarketDataResponse',
    askPrice: overrides && overrides.hasOwnProperty('askPrice') ? overrides.askPrice! : 7.82,
    askSize: overrides && overrides.hasOwnProperty('askSize') ? overrides.askSize! : 5.01,
    bidPrice: overrides && overrides.hasOwnProperty('bidPrice') ? overrides.bidPrice! : 9.58,
    bidSize: overrides && overrides.hasOwnProperty('bidSize') ? overrides.bidSize! : 7.17,
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 0.66,
    instrument:
      overrides && overrides.hasOwnProperty('instrument')
        ? overrides.instrument!
        : anInstrumentResponse(),
    lastPrice: overrides && overrides.hasOwnProperty('lastPrice') ? overrides.lastPrice! : 3.16,
    lastSize: overrides && overrides.hasOwnProperty('lastSize') ? overrides.lastSize! : 7.45,
    marketDataType:
      overrides && overrides.hasOwnProperty('marketDataType')
        ? overrides.marketDataType!
        : MarketDataType.Ask,
    side: overrides && overrides.hasOwnProperty('side') ? overrides.side! : Side.Buy,
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'dolorem',
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount')
        ? overrides.venueAccount!
        : 'consequuntur',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'amet',
    vol: overrides && overrides.hasOwnProperty('vol') ? overrides.vol! : 4.29,
  };
};

export const aMarketQuote = (
  overrides?: Partial<MarketQuote>,
): { __typename: 'MarketQuote' } & MarketQuote => {
  return {
    __typename: 'MarketQuote',
    instrument:
      overrides && overrides.hasOwnProperty('instrument')
        ? overrides.instrument!
        : anInstrumentResponse(),
    marketAskPrice:
      overrides && overrides.hasOwnProperty('marketAskPrice') ? overrides.marketAskPrice! : 0.28,
    marketAskSize:
      overrides && overrides.hasOwnProperty('marketAskSize') ? overrides.marketAskSize! : 8.8,
    marketBidPrice:
      overrides && overrides.hasOwnProperty('marketBidPrice') ? overrides.marketBidPrice! : 9.13,
    marketBidSize:
      overrides && overrides.hasOwnProperty('marketBidSize') ? overrides.marketBidSize! : 1.37,
    markupAskPrice:
      overrides && overrides.hasOwnProperty('markupAskPrice') ? overrides.markupAskPrice! : 5.59,
    markupBidPrice:
      overrides && overrides.hasOwnProperty('markupBidPrice') ? overrides.markupBidPrice! : 2.13,
    timestamp: overrides && overrides.hasOwnProperty('timestamp') ? overrides.timestamp! : 'velit',
  };
};

export const aMatchResponse = (
  overrides?: Partial<MatchResponse>,
): { __typename: 'MatchResponse' } & MatchResponse => {
  return {
    __typename: 'MatchResponse',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'eos',
    makers: overrides && overrides.hasOwnProperty('makers') ? overrides.makers! : [aCounterparty()],
    primarySymbolQuotes:
      overrides && overrides.hasOwnProperty('primarySymbolQuotes')
        ? overrides.primarySymbolQuotes!
        : [aMarketQuote()],
    secondarySymbolQuotes:
      overrides && overrides.hasOwnProperty('secondarySymbolQuotes')
        ? overrides.secondarySymbolQuotes!
        : [aMarketQuote()],
    taker: overrides && overrides.hasOwnProperty('taker') ? overrides.taker! : aCounterparty(),
    timestamp: overrides && overrides.hasOwnProperty('timestamp') ? overrides.timestamp! : 'qui',
  };
};

export const aMatchesResult = (
  overrides?: Partial<MatchesResult>,
): { __typename: 'MatchesResult' } & MatchesResult => {
  return {
    __typename: 'MatchesResult',
    avgPrice: overrides && overrides.hasOwnProperty('avgPrice') ? overrides.avgPrice! : 1.19,
    matches:
      overrides && overrides.hasOwnProperty('matches') ? overrides.matches! : [aMatchResponse()],
    totalAmount:
      overrides && overrides.hasOwnProperty('totalAmount') ? overrides.totalAmount! : 3.14,
    totalQuantity:
      overrides && overrides.hasOwnProperty('totalQuantity') ? overrides.totalQuantity! : 7.48,
  };
};

export const aMatchesSearchInput = (
  overrides?: Partial<MatchesSearchInput>,
): MatchesSearchInput => {
  return {
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'porro',
  };
};

export const aMutation = (overrides?: Partial<Mutation>): { __typename: 'Mutation' } & Mutation => {
  return {
    __typename: 'Mutation',
    activateVenueAccount:
      overrides && overrides.hasOwnProperty('activateVenueAccount')
        ? overrides.activateVenueAccount!
        : aMutationSubmittedResponse(),
    addGroupPermissions:
      overrides && overrides.hasOwnProperty('addGroupPermissions')
        ? overrides.addGroupPermissions!
        : aMutationReturnValue(),
    addTransaction:
      overrides && overrides.hasOwnProperty('addTransaction')
        ? overrides.addTransaction!
        : aMutationSubmittedResponse(),
    addUserPermissions:
      overrides && overrides.hasOwnProperty('addUserPermissions')
        ? overrides.addUserPermissions!
        : aMutationReturnValue(),
    cancelOrder:
      overrides && overrides.hasOwnProperty('cancelOrder')
        ? overrides.cancelOrder!
        : aMutationReturnValue(),
    cancelReplaceOrder:
      overrides && overrides.hasOwnProperty('cancelReplaceOrder')
        ? overrides.cancelReplaceOrder!
        : aMutationReturnValue(),
    connectorAction:
      overrides && overrides.hasOwnProperty('connectorAction')
        ? overrides.connectorAction!
        : aMutationSubmittedResponse(),
    connectorCreate:
      overrides && overrides.hasOwnProperty('connectorCreate')
        ? overrides.connectorCreate!
        : aMutationSubmittedResponse(),
    connectorRemove:
      overrides && overrides.hasOwnProperty('connectorRemove')
        ? overrides.connectorRemove!
        : aMutationSubmittedResponse(),
    connectorSyncData:
      overrides && overrides.hasOwnProperty('connectorSyncData')
        ? overrides.connectorSyncData!
        : aMutationSubmittedResponse(),
    connectorUpdate:
      overrides && overrides.hasOwnProperty('connectorUpdate')
        ? overrides.connectorUpdate!
        : aMutationSubmittedResponse(),
    createApiKey:
      overrides && overrides.hasOwnProperty('createApiKey')
        ? overrides.createApiKey!
        : anApiKeyResponse(),
    createConversionSource:
      overrides && overrides.hasOwnProperty('createConversionSource')
        ? overrides.createConversionSource!
        : aMutationSubmittedResponse(),
    createInstrument:
      overrides && overrides.hasOwnProperty('createInstrument')
        ? overrides.createInstrument!
        : aCreateInstrumentResult(),
    createInternalExchangeConfiguration:
      overrides && overrides.hasOwnProperty('createInternalExchangeConfiguration')
        ? overrides.createInternalExchangeConfiguration!
        : aMutationSubmittedResponse(),
    createPortfolio:
      overrides && overrides.hasOwnProperty('createPortfolio')
        ? overrides.createPortfolio!
        : aMutationSubmittedResponse(),
    createQuotingConfiguration:
      overrides && overrides.hasOwnProperty('createQuotingConfiguration')
        ? overrides.createQuotingConfiguration!
        : aQuotingConfigValidationResult(),
    createRateSubscription:
      overrides && overrides.hasOwnProperty('createRateSubscription')
        ? overrides.createRateSubscription!
        : aMutationSubmittedResponse(),
    createVenueAccount:
      overrides && overrides.hasOwnProperty('createVenueAccount')
        ? overrides.createVenueAccount!
        : aMutationSubmittedResponse(),
    createWalletAccount:
      overrides && overrides.hasOwnProperty('createWalletAccount')
        ? overrides.createWalletAccount!
        : aMutationSubmittedResponse(),
    deactivateApiKey:
      overrides && overrides.hasOwnProperty('deactivateApiKey')
        ? overrides.deactivateApiKey!
        : aMutationReturnValue(),
    deactivateVenueAccount:
      overrides && overrides.hasOwnProperty('deactivateVenueAccount')
        ? overrides.deactivateVenueAccount!
        : aMutationSubmittedResponse(),
    deleteConversionSource:
      overrides && overrides.hasOwnProperty('deleteConversionSource')
        ? overrides.deleteConversionSource!
        : aMutationSubmittedResponse(),
    deletePortfolioConfiguration:
      overrides && overrides.hasOwnProperty('deletePortfolioConfiguration')
        ? overrides.deletePortfolioConfiguration!
        : 'aperiam',
    deletePortfolioGroupConfiguration:
      overrides && overrides.hasOwnProperty('deletePortfolioGroupConfiguration')
        ? overrides.deletePortfolioGroupConfiguration!
        : 'temporibus',
    deletePreTradeCheck:
      overrides && overrides.hasOwnProperty('deletePreTradeCheck')
        ? overrides.deletePreTradeCheck!
        : aMutationReturnValue(),
    deleteQuotingConfiguration:
      overrides && overrides.hasOwnProperty('deleteQuotingConfiguration')
        ? overrides.deleteQuotingConfiguration!
        : 'dolorem',
    deleteRateSubscription:
      overrides && overrides.hasOwnProperty('deleteRateSubscription')
        ? overrides.deleteRateSubscription!
        : aMutationSubmittedResponse(),
    instrumentsRefresh:
      overrides && overrides.hasOwnProperty('instrumentsRefresh')
        ? overrides.instrumentsRefresh!
        : aMutationReturnValue(),
    removeGroupPermissions:
      overrides && overrides.hasOwnProperty('removeGroupPermissions')
        ? overrides.removeGroupPermissions!
        : aMutationReturnValue(),
    removeUserPermissions:
      overrides && overrides.hasOwnProperty('removeUserPermissions')
        ? overrides.removeUserPermissions!
        : aMutationReturnValue(),
    resetConfiguration:
      overrides && overrides.hasOwnProperty('resetConfiguration')
        ? overrides.resetConfiguration!
        : aConfigValidationResult(),
    saveCurrency:
      overrides && overrides.hasOwnProperty('saveCurrency')
        ? overrides.saveCurrency!
        : aMutationSubmittedResponse(),
    savePreTradeCheck:
      overrides && overrides.hasOwnProperty('savePreTradeCheck')
        ? overrides.savePreTradeCheck!
        : aMutationReturnValue(),
    sendOrder:
      overrides && overrides.hasOwnProperty('sendOrder')
        ? overrides.sendOrder!
        : aMutationReturnValue(),
    settlementConfigurationUpdate:
      overrides && overrides.hasOwnProperty('settlementConfigurationUpdate')
        ? overrides.settlementConfigurationUpdate!
        : aMutationSubmittedResponse(),
    settlementLegComplete:
      overrides && overrides.hasOwnProperty('settlementLegComplete')
        ? overrides.settlementLegComplete!
        : aMutationSubmittedResponse(),
    settlementLegInitiate:
      overrides && overrides.hasOwnProperty('settlementLegInitiate')
        ? overrides.settlementLegInitiate!
        : aMutationSubmittedResponse(),
    settlementRunCreate:
      overrides && overrides.hasOwnProperty('settlementRunCreate')
        ? overrides.settlementRunCreate!
        : aCreateSettlementRunResponse(),
    settlementRunDelete:
      overrides && overrides.hasOwnProperty('settlementRunDelete')
        ? overrides.settlementRunDelete!
        : aMutationSubmittedResponse(),
    settlementRunStart:
      overrides && overrides.hasOwnProperty('settlementRunStart')
        ? overrides.settlementRunStart!
        : aMutationSubmittedResponse(),
    settlementTransactionsAllSelect:
      overrides && overrides.hasOwnProperty('settlementTransactionsAllSelect')
        ? overrides.settlementTransactionsAllSelect!
        : aMutationSubmittedResponse(),
    settlementTransactionsSelect:
      overrides && overrides.hasOwnProperty('settlementTransactionsSelect')
        ? overrides.settlementTransactionsSelect!
        : aMutationSubmittedResponse(),
    transfer:
      overrides && overrides.hasOwnProperty('transfer')
        ? overrides.transfer!
        : aMutationSubmittedResponse(),
    updateApiKeyName:
      overrides && overrides.hasOwnProperty('updateApiKeyName')
        ? overrides.updateApiKeyName!
        : aMutationReturnValue(),
    updateConversionSource:
      overrides && overrides.hasOwnProperty('updateConversionSource')
        ? overrides.updateConversionSource!
        : aMutationSubmittedResponse(),
    updateInstrument:
      overrides && overrides.hasOwnProperty('updateInstrument')
        ? overrides.updateInstrument!
        : anUpdateInstrumentResult(),
    updateInstrumentQuotingConfiguration:
      overrides && overrides.hasOwnProperty('updateInstrumentQuotingConfiguration')
        ? overrides.updateInstrumentQuotingConfiguration!
        : aQuotingConfigValidationResult(),
    updateInternalExchangeConfiguration:
      overrides && overrides.hasOwnProperty('updateInternalExchangeConfiguration')
        ? overrides.updateInternalExchangeConfiguration!
        : aMutationSubmittedResponse(),
    updatePortfolio:
      overrides && overrides.hasOwnProperty('updatePortfolio')
        ? overrides.updatePortfolio!
        : aMutationSubmittedResponse(),
    updatePortfolioConfiguration:
      overrides && overrides.hasOwnProperty('updatePortfolioConfiguration')
        ? overrides.updatePortfolioConfiguration!
        : aConfigValidationResult(),
    updatePortfolioExecutionConfiguration:
      overrides && overrides.hasOwnProperty('updatePortfolioExecutionConfiguration')
        ? overrides.updatePortfolioExecutionConfiguration!
        : aConfigValidationResult(),
    updatePortfolioGroupConfiguration:
      overrides && overrides.hasOwnProperty('updatePortfolioGroupConfiguration')
        ? overrides.updatePortfolioGroupConfiguration!
        : 'non',
    updatePortfolioGroupExecutionConfiguration:
      overrides && overrides.hasOwnProperty('updatePortfolioGroupExecutionConfiguration')
        ? overrides.updatePortfolioGroupExecutionConfiguration!
        : 'est',
    updatePortfolioGroupHedgingConfiguration:
      overrides && overrides.hasOwnProperty('updatePortfolioGroupHedgingConfiguration')
        ? overrides.updatePortfolioGroupHedgingConfiguration!
        : 'laborum',
    updatePortfolioGroupInstrumentConfiguration:
      overrides && overrides.hasOwnProperty('updatePortfolioGroupInstrumentConfiguration')
        ? overrides.updatePortfolioGroupInstrumentConfiguration!
        : 'velit',
    updatePortfolioGroupPricingConfiguration:
      overrides && overrides.hasOwnProperty('updatePortfolioGroupPricingConfiguration')
        ? overrides.updatePortfolioGroupPricingConfiguration!
        : 'eius',
    updatePortfolioHedgingConfiguration:
      overrides && overrides.hasOwnProperty('updatePortfolioHedgingConfiguration')
        ? overrides.updatePortfolioHedgingConfiguration!
        : aHedgingConfigValidationResult(),
    updatePortfolioInstrumentConfiguration:
      overrides && overrides.hasOwnProperty('updatePortfolioInstrumentConfiguration')
        ? overrides.updatePortfolioInstrumentConfiguration!
        : aConfigValidationResult(),
    updatePortfolioPricingConfiguration:
      overrides && overrides.hasOwnProperty('updatePortfolioPricingConfiguration')
        ? overrides.updatePortfolioPricingConfiguration!
        : aConfigValidationResult(),
    updateQuotingConfiguration:
      overrides && overrides.hasOwnProperty('updateQuotingConfiguration')
        ? overrides.updateQuotingConfiguration!
        : aQuotingConfigValidationResult(),
    updateUserData:
      overrides && overrides.hasOwnProperty('updateUserData')
        ? overrides.updateUserData!
        : aUserDataResponse(),
    updateVenueAccount:
      overrides && overrides.hasOwnProperty('updateVenueAccount')
        ? overrides.updateVenueAccount!
        : aMutationSubmittedResponse(),
    venueAccountAction:
      overrides && overrides.hasOwnProperty('venueAccountAction')
        ? overrides.venueAccountAction!
        : aMutationSubmittedResponse(),
    venueAccountCreate:
      overrides && overrides.hasOwnProperty('venueAccountCreate')
        ? overrides.venueAccountCreate!
        : aMutationSubmittedResponse(),
    venueAccountPositionCreate:
      overrides && overrides.hasOwnProperty('venueAccountPositionCreate')
        ? overrides.venueAccountPositionCreate!
        : aMutationSubmittedResponse(),
    venueAccountPositionDelete:
      overrides && overrides.hasOwnProperty('venueAccountPositionDelete')
        ? overrides.venueAccountPositionDelete!
        : aMutationSubmittedResponse(),
    venueAccountPositionUpdate:
      overrides && overrides.hasOwnProperty('venueAccountPositionUpdate')
        ? overrides.venueAccountPositionUpdate!
        : aMutationSubmittedResponse(),
    venueAccountUpdate:
      overrides && overrides.hasOwnProperty('venueAccountUpdate')
        ? overrides.venueAccountUpdate!
        : aMutationSubmittedResponse(),
    venueCreate:
      overrides && overrides.hasOwnProperty('venueCreate')
        ? overrides.venueCreate!
        : aMutationSubmittedResponse(),
  };
};

export const aMutationReturnValue = (
  overrides?: Partial<MutationReturnValue>,
): { __typename: 'MutationReturnValue' } & MutationReturnValue => {
  return {
    __typename: 'MutationReturnValue',
    clientId: overrides && overrides.hasOwnProperty('clientId') ? overrides.clientId! : 'accusamus',
  };
};

export const aMutationSubmittedResponse = (
  overrides?: Partial<MutationSubmittedResponse>,
): { __typename: 'MutationSubmittedResponse' } & MutationSubmittedResponse => {
  return {
    __typename: 'MutationSubmittedResponse',
    status: overrides && overrides.hasOwnProperty('status') ? overrides.status! : 'ut',
  };
};

export const aNewOrderSingleRequestInput = (
  overrides?: Partial<NewOrderSingleRequestInput>,
): NewOrderSingleRequestInput => {
  return {
    assetClass:
      overrides && overrides.hasOwnProperty('assetClass')
        ? overrides.assetClass!
        : AssetClass.Forex,
    clOrderId:
      overrides && overrides.hasOwnProperty('clOrderId') ? overrides.clOrderId! : 'tempore',
    currency:
      overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'consequatur',
    expirationDateTime:
      overrides && overrides.hasOwnProperty('expirationDateTime')
        ? overrides.expirationDateTime!
        : 'qui',
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId') ? overrides.instrumentId! : 'quo',
    limitPrice: overrides && overrides.hasOwnProperty('limitPrice') ? overrides.limitPrice! : 0.12,
    orderType:
      overrides && overrides.hasOwnProperty('orderType') ? overrides.orderType! : OrderType.Limit,
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'et',
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 9.3,
    side: overrides && overrides.hasOwnProperty('side') ? overrides.side! : Side.Buy,
    stopPrice: overrides && overrides.hasOwnProperty('stopPrice') ? overrides.stopPrice! : 7.99,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'ut',
    tif: overrides && overrides.hasOwnProperty('tif') ? overrides.tif! : Tif.Day,
    venueAccounts:
      overrides && overrides.hasOwnProperty('venueAccounts')
        ? overrides.venueAccounts!
        : ['repellat'],
  };
};

export const anOrderBook = (
  overrides?: Partial<OrderBook>,
): { __typename: 'OrderBook' } & OrderBook => {
  return {
    __typename: 'OrderBook',
    amount: overrides && overrides.hasOwnProperty('amount') ? overrides.amount! : 'officia',
    count: overrides && overrides.hasOwnProperty('count') ? overrides.count! : 'illo',
    price: overrides && overrides.hasOwnProperty('price') ? overrides.price! : 'cumque',
  };
};

export const anOrderBookResponse = (
  overrides?: Partial<OrderBookResponse>,
): { __typename: 'OrderBookResponse' } & OrderBookResponse => {
  return {
    __typename: 'OrderBookResponse',
    asks: overrides && overrides.hasOwnProperty('asks') ? overrides.asks! : [anOrderBook()],
    bids: overrides && overrides.hasOwnProperty('bids') ? overrides.bids! : [anOrderBook()],
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'doloribus',
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId') ? overrides.instrumentId! : 'dolores',
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'quam',
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'et',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'non',
  };
};

export const anOrderBookSnapshot = (
  overrides?: Partial<OrderBookSnapshot>,
): { __typename: 'OrderBookSnapshot' } & OrderBookSnapshot => {
  return {
    __typename: 'OrderBookSnapshot',
    asks: overrides && overrides.hasOwnProperty('asks') ? overrides.asks! : [anOrderBook()],
    bids: overrides && overrides.hasOwnProperty('bids') ? overrides.bids! : [anOrderBook()],
  };
};

export const anOrderBookSnapshotSearchInput = (
  overrides?: Partial<OrderBookSnapshotSearchInput>,
): OrderBookSnapshotSearchInput => {
  return {
    matchId: overrides && overrides.hasOwnProperty('matchId') ? overrides.matchId! : 'et',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'deleniti',
  };
};

export const anOrderCancelReplaceRequestInput = (
  overrides?: Partial<OrderCancelReplaceRequestInput>,
): OrderCancelReplaceRequestInput => {
  return {
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'eum',
    origClOrderId:
      overrides && overrides.hasOwnProperty('origClOrderId')
        ? overrides.origClOrderId!
        : 'perspiciatis',
    origClientId:
      overrides && overrides.hasOwnProperty('origClientId') ? overrides.origClientId! : 'autem',
    replacingOrder:
      overrides && overrides.hasOwnProperty('replacingOrder')
        ? overrides.replacingOrder!
        : aNewOrderSingleRequestInput(),
  };
};

export const anOrderCancelRequestInput = (
  overrides?: Partial<OrderCancelRequestInput>,
): OrderCancelRequestInput => {
  return {
    clOrderId:
      overrides && overrides.hasOwnProperty('clOrderId') ? overrides.clOrderId! : 'tenetur',
    forceCancel:
      overrides && overrides.hasOwnProperty('forceCancel') ? overrides.forceCancel! : true,
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'aliquam',
    origClOrderId:
      overrides && overrides.hasOwnProperty('origClOrderId') ? overrides.origClOrderId! : 'et',
    origClientId:
      overrides && overrides.hasOwnProperty('origClientId') ? overrides.origClientId! : 'nihil',
  };
};

export const anOrderRequestsPerOrderType = (
  overrides?: Partial<OrderRequestsPerOrderType>,
): { __typename: 'OrderRequestsPerOrderType' } & OrderRequestsPerOrderType => {
  return {
    __typename: 'OrderRequestsPerOrderType',
    key: overrides && overrides.hasOwnProperty('key') ? overrides.key! : OrderType.Limit,
    values:
      overrides && overrides.hasOwnProperty('values') ? overrides.values! : [OrderRequest.Cancel],
  };
};

export const anOrderSnapshot = (
  overrides?: Partial<OrderSnapshot>,
): { __typename: 'OrderSnapshot' } & OrderSnapshot => {
  return {
    __typename: 'OrderSnapshot',
    baseCurrency:
      overrides && overrides.hasOwnProperty('baseCurrency') ? overrides.baseCurrency! : 'labore',
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId') ? overrides.instrumentId! : 'quidem',
    instrumentType:
      overrides && overrides.hasOwnProperty('instrumentType')
        ? overrides.instrumentType!
        : InstrumentType.Bond,
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'cupiditate',
    orderType:
      overrides && overrides.hasOwnProperty('orderType') ? overrides.orderType! : OrderType.Limit,
    parentOrderId:
      overrides && overrides.hasOwnProperty('parentOrderId') ? overrides.parentOrderId! : 'est',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'cumque',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName') ? overrides.portfolioName! : 'fugit',
    postOnly: overrides && overrides.hasOwnProperty('postOnly') ? overrides.postOnly! : false,
    price: overrides && overrides.hasOwnProperty('price') ? overrides.price! : 4.4,
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 4.67,
    quoteCurrency:
      overrides && overrides.hasOwnProperty('quoteCurrency') ? overrides.quoteCurrency! : 'error',
    side: overrides && overrides.hasOwnProperty('side') ? overrides.side! : Side.Buy,
    stopPrice: overrides && overrides.hasOwnProperty('stopPrice') ? overrides.stopPrice! : 4.91,
    tif: overrides && overrides.hasOwnProperty('tif') ? overrides.tif! : Tif.Day,
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'assumenda',
    venueAccountDescs:
      overrides && overrides.hasOwnProperty('venueAccountDescs')
        ? overrides.venueAccountDescs!
        : [aVenueAccountDesc()],
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'aut',
  };
};

export const anOrderStateConnection = (
  overrides?: Partial<OrderStateConnection>,
): { __typename: 'OrderStateConnection' } & OrderStateConnection => {
  return {
    __typename: 'OrderStateConnection',
    edges: overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [anOrderStateEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const anOrderStateEdge = (
  overrides?: Partial<OrderStateEdge>,
): { __typename: 'OrderStateEdge' } & OrderStateEdge => {
  return {
    __typename: 'OrderStateEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'est',
    node: overrides && overrides.hasOwnProperty('node') ? overrides.node! : anOrderStateResponse(),
  };
};

export const anOrderStateResponse = (
  overrides?: Partial<OrderStateResponse>,
): { __typename: 'OrderStateResponse' } & OrderStateResponse => {
  return {
    __typename: 'OrderStateResponse',
    assetClass:
      overrides && overrides.hasOwnProperty('assetClass')
        ? overrides.assetClass!
        : AssetClass.Forex,
    avgPrice: overrides && overrides.hasOwnProperty('avgPrice') ? overrides.avgPrice! : 'ducimus',
    clOrderId: overrides && overrides.hasOwnProperty('clOrderId') ? overrides.clOrderId! : 'rerum',
    clientId: overrides && overrides.hasOwnProperty('clientId') ? overrides.clientId! : 'similique',
    counterPortfolioId:
      overrides && overrides.hasOwnProperty('counterPortfolioId')
        ? overrides.counterPortfolioId!
        : 'deleniti',
    counterPortfolioName:
      overrides && overrides.hasOwnProperty('counterPortfolioName')
        ? overrides.counterPortfolioName!
        : 'perspiciatis',
    createdAt: overrides && overrides.hasOwnProperty('createdAt') ? overrides.createdAt! : 'aut',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'ut',
    expirationDateTime:
      overrides && overrides.hasOwnProperty('expirationDateTime')
        ? overrides.expirationDateTime!
        : 'et',
    extOrderId:
      overrides && overrides.hasOwnProperty('extOrderId') ? overrides.extOrderId! : 'quae',
    filledQty: overrides && overrides.hasOwnProperty('filledQty') ? overrides.filledQty! : 7.77,
    instrument:
      overrides && overrides.hasOwnProperty('instrument')
        ? overrides.instrument!
        : anInstrumentResponse(),
    lastPrice: overrides && overrides.hasOwnProperty('lastPrice') ? overrides.lastPrice! : 'dolor',
    lastQty: overrides && overrides.hasOwnProperty('lastQty') ? overrides.lastQty! : 3.8,
    lastRequestResult:
      overrides && overrides.hasOwnProperty('lastRequestResult')
        ? overrides.lastRequestResult!
        : 'repellat',
    limitPrice: overrides && overrides.hasOwnProperty('limitPrice') ? overrides.limitPrice! : 4.58,
    orderCategory:
      overrides && overrides.hasOwnProperty('orderCategory')
        ? overrides.orderCategory!
        : OrderCategory.AgencyClobOrder,
    orderId:
      overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'necessitatibus',
    orderQty: overrides && overrides.hasOwnProperty('orderQty') ? overrides.orderQty! : 2.48,
    orderStateId:
      overrides && overrides.hasOwnProperty('orderStateId') ? overrides.orderStateId! : 'dolores',
    orderStatus:
      overrides && overrides.hasOwnProperty('orderStatus')
        ? overrides.orderStatus!
        : OrderStatus.Calculated,
    orderType:
      overrides && overrides.hasOwnProperty('orderType') ? overrides.orderType! : OrderType.Limit,
    origClOrderId:
      overrides && overrides.hasOwnProperty('origClOrderId') ? overrides.origClOrderId! : 'non',
    parentOrderId:
      overrides && overrides.hasOwnProperty('parentOrderId') ? overrides.parentOrderId! : 'sed',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'non',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName') ? overrides.portfolioName! : 'est',
    reason: overrides && overrides.hasOwnProperty('reason') ? overrides.reason! : 'consectetur',
    remainingQty:
      overrides && overrides.hasOwnProperty('remainingQty') ? overrides.remainingQty! : 9.42,
    rootOrderId:
      overrides && overrides.hasOwnProperty('rootOrderId') ? overrides.rootOrderId! : 'qui',
    sequenceNumber:
      overrides && overrides.hasOwnProperty('sequenceNumber') ? overrides.sequenceNumber! : 9233,
    side: overrides && overrides.hasOwnProperty('side') ? overrides.side! : Side.Buy,
    stopPrice: overrides && overrides.hasOwnProperty('stopPrice') ? overrides.stopPrice! : 7.52,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'vero',
    tif: overrides && overrides.hasOwnProperty('tif') ? overrides.tif! : Tif.Day,
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 'ut',
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'consequatur',
    venueAccountDescs:
      overrides && overrides.hasOwnProperty('venueAccountDescs')
        ? overrides.venueAccountDescs!
        : [aVenueAccountDesc()],
    venueTimestamp:
      overrides && overrides.hasOwnProperty('venueTimestamp')
        ? overrides.venueTimestamp!
        : 'magnam',
  };
};

export const anOrderStateSearchInput = (
  overrides?: Partial<OrderStateSearchInput>,
): OrderStateSearchInput => {
  return {
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'suscipit',
    collectionPredicates:
      overrides && overrides.hasOwnProperty('collectionPredicates')
        ? overrides.collectionPredicates!
        : [aCollectionPredicateInput()],
    datePredicateInputs:
      overrides && overrides.hasOwnProperty('datePredicateInputs')
        ? overrides.datePredicateInputs!
        : [aDatePredicate()],
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 8500,
    simplePredicates:
      overrides && overrides.hasOwnProperty('simplePredicates')
        ? overrides.simplePredicates!
        : [aSimplePredicateInput()],
    sortingOrder:
      overrides && overrides.hasOwnProperty('sortingOrder')
        ? overrides.sortingOrder!
        : SortingOrder.Asc,
  };
};

export const aPageInfo = (overrides?: Partial<PageInfo>): { __typename: 'PageInfo' } & PageInfo => {
  return {
    __typename: 'PageInfo',
    endCursor: overrides && overrides.hasOwnProperty('endCursor') ? overrides.endCursor! : 'id',
    hasNextPage:
      overrides && overrides.hasOwnProperty('hasNextPage') ? overrides.hasNextPage! : true,
  };
};

export const aPermission = (
  overrides?: Partial<Permission>,
): { __typename: 'Permission' } & Permission => {
  return {
    __typename: 'Permission',
    resource:
      overrides && overrides.hasOwnProperty('resource') ? overrides.resource! : Resource.ApiKey,
    resourceId: overrides && overrides.hasOwnProperty('resourceId') ? overrides.resourceId! : 'nam',
    scope: overrides && overrides.hasOwnProperty('scope') ? overrides.scope! : Scope.Create,
  };
};

export const aPermissionConnection = (
  overrides?: Partial<PermissionConnection>,
): { __typename: 'PermissionConnection' } & PermissionConnection => {
  return {
    __typename: 'PermissionConnection',
    edges: overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [aPermissionEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aPermissionEdge = (
  overrides?: Partial<PermissionEdge>,
): { __typename: 'PermissionEdge' } & PermissionEdge => {
  return {
    __typename: 'PermissionEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'omnis',
    node: overrides && overrides.hasOwnProperty('node') ? overrides.node! : aFlatPermission(),
  };
};

export const aPermissionInput = (overrides?: Partial<PermissionInput>): PermissionInput => {
  return {
    resource:
      overrides && overrides.hasOwnProperty('resource') ? overrides.resource! : Resource.ApiKey,
    resourceId:
      overrides && overrides.hasOwnProperty('resourceId') ? overrides.resourceId! : 'dignissimos',
    scope: overrides && overrides.hasOwnProperty('scope') ? overrides.scope! : Scope.Create,
  };
};

export const aPermissionSearchInput = (
  overrides?: Partial<PermissionSearchInput>,
): PermissionSearchInput => {
  return {
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'architecto',
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 728,
  };
};

export const aPortfolioCashTransfer = (
  overrides?: Partial<PortfolioCashTransfer>,
): { __typename: 'PortfolioCashTransfer' } & PortfolioCashTransfer => {
  return {
    __typename: 'PortfolioCashTransfer',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'voluptas',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'magni',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'sit',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'magnam',
    feePortfolioId:
      overrides && overrides.hasOwnProperty('feePortfolioId') ? overrides.feePortfolioId! : 'harum',
    feePortfolioName:
      overrides && overrides.hasOwnProperty('feePortfolioName')
        ? overrides.feePortfolioName!
        : 'voluptatum',
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 5.28,
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : 'eos',
    settledDateTime:
      overrides && overrides.hasOwnProperty('settledDateTime') ? overrides.settledDateTime! : 4.33,
    sourcePortfolioId:
      overrides && overrides.hasOwnProperty('sourcePortfolioId')
        ? overrides.sourcePortfolioId!
        : 'rerum',
    sourcePortfolioName:
      overrides && overrides.hasOwnProperty('sourcePortfolioName')
        ? overrides.sourcePortfolioName!
        : 'nemo',
    targetPortfolioId:
      overrides && overrides.hasOwnProperty('targetPortfolioId')
        ? overrides.targetPortfolioId!
        : 'eos',
    targetPortfolioName:
      overrides && overrides.hasOwnProperty('targetPortfolioName')
        ? overrides.targetPortfolioName!
        : 'ad',
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 7.55,
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'enim',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'quod',
  };
};

export const aPortfolioConfiguration = (
  overrides?: Partial<PortfolioConfiguration>,
): { __typename: 'PortfolioConfiguration' } & PortfolioConfiguration => {
  return {
    __typename: 'PortfolioConfiguration',
    executionConfiguration:
      overrides && overrides.hasOwnProperty('executionConfiguration')
        ? overrides.executionConfiguration!
        : anExecutionConfiguration(),
    hedgingConfiguration:
      overrides && overrides.hasOwnProperty('hedgingConfiguration')
        ? overrides.hedgingConfiguration!
        : aHedgingConfiguration(),
    id:
      overrides && overrides.hasOwnProperty('id')
        ? overrides.id!
        : 'e6c19e54-c284-4153-a23b-d509b1811772',
    instrumentConfiguration:
      overrides && overrides.hasOwnProperty('instrumentConfiguration')
        ? overrides.instrumentConfiguration!
        : [anInstrumentConfiguration()],
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'deserunt',
    portfolioGroupConfiguration:
      overrides && overrides.hasOwnProperty('portfolioGroupConfiguration')
        ? overrides.portfolioGroupConfiguration!
        : aPortfolioGroupConfiguration(),
    pricingConfiguration:
      overrides && overrides.hasOwnProperty('pricingConfiguration')
        ? overrides.pricingConfiguration!
        : aPricingConfiguration(),
  };
};

export const aPortfolioConfigurationFlat = (
  overrides?: Partial<PortfolioConfigurationFlat>,
): { __typename: 'PortfolioConfigurationFlat' } & PortfolioConfigurationFlat => {
  return {
    __typename: 'PortfolioConfigurationFlat',
    id:
      overrides && overrides.hasOwnProperty('id')
        ? overrides.id!
        : 'e0935240-0f33-4ea9-9cd0-a3017d957155',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'tempora',
  };
};

export const aPortfolioConfigurationInput = (
  overrides?: Partial<PortfolioConfigurationInput>,
): PortfolioConfigurationInput => {
  return {
    executionConfiguration:
      overrides && overrides.hasOwnProperty('executionConfiguration')
        ? overrides.executionConfiguration!
        : anExecutionConfigurationInput(),
    hedgingConfiguration:
      overrides && overrides.hasOwnProperty('hedgingConfiguration')
        ? overrides.hedgingConfiguration!
        : aHedgingConfigurationInput(),
    id:
      overrides && overrides.hasOwnProperty('id')
        ? overrides.id!
        : '72a7a3ea-4300-4945-92de-b864db3e0a40',
    instrumentConfiguration:
      overrides && overrides.hasOwnProperty('instrumentConfiguration')
        ? overrides.instrumentConfiguration!
        : [anInstrumentConfigurationInput()],
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'hic',
    portfolioGroupConfigurationId:
      overrides && overrides.hasOwnProperty('portfolioGroupConfigurationId')
        ? overrides.portfolioGroupConfigurationId!
        : '3e87bb63-8388-4a18-a349-16e61187eb21',
    pricingConfiguration:
      overrides && overrides.hasOwnProperty('pricingConfiguration')
        ? overrides.pricingConfiguration!
        : aPricingConfigurationInput(),
  };
};

export const aPortfolioConnection = (
  overrides?: Partial<PortfolioConnection>,
): { __typename: 'PortfolioConnection' } & PortfolioConnection => {
  return {
    __typename: 'PortfolioConnection',
    edges: overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [aPortfolioEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aPortfolioEdge = (
  overrides?: Partial<PortfolioEdge>,
): { __typename: 'PortfolioEdge' } & PortfolioEdge => {
  return {
    __typename: 'PortfolioEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'voluptatem',
    node: overrides && overrides.hasOwnProperty('node') ? overrides.node! : aPortfolioResponse(),
  };
};

export const aPortfolioGroupConfiguration = (
  overrides?: Partial<PortfolioGroupConfiguration>,
): { __typename: 'PortfolioGroupConfiguration' } & PortfolioGroupConfiguration => {
  return {
    __typename: 'PortfolioGroupConfiguration',
    executionConfiguration:
      overrides && overrides.hasOwnProperty('executionConfiguration')
        ? overrides.executionConfiguration!
        : anExecutionConfiguration(),
    hedgingConfiguration:
      overrides && overrides.hasOwnProperty('hedgingConfiguration')
        ? overrides.hedgingConfiguration!
        : aHedgingConfiguration(),
    id:
      overrides && overrides.hasOwnProperty('id')
        ? overrides.id!
        : '71e4798e-c1f4-45a5-9f94-281e0de87f6b',
    instrumentConfiguration:
      overrides && overrides.hasOwnProperty('instrumentConfiguration')
        ? overrides.instrumentConfiguration!
        : [anInstrumentGroupConfiguration()],
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'quidem',
    portfolioType:
      overrides && overrides.hasOwnProperty('portfolioType')
        ? overrides.portfolioType!
        : PortfolioType.Nostro,
    pricingConfiguration:
      overrides && overrides.hasOwnProperty('pricingConfiguration')
        ? overrides.pricingConfiguration!
        : aPricingConfiguration(),
  };
};

export const aPortfolioGroupConfigurationFlat = (
  overrides?: Partial<PortfolioGroupConfigurationFlat>,
): { __typename: 'PortfolioGroupConfigurationFlat' } & PortfolioGroupConfigurationFlat => {
  return {
    __typename: 'PortfolioGroupConfigurationFlat',
    id:
      overrides && overrides.hasOwnProperty('id')
        ? overrides.id!
        : '261b6ba1-eb06-4c53-a9d1-1754b03b95b5',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'saepe',
    portfolioType:
      overrides && overrides.hasOwnProperty('portfolioType')
        ? overrides.portfolioType!
        : PortfolioType.Nostro,
  };
};

export const aPortfolioGroupConfigurationInput = (
  overrides?: Partial<PortfolioGroupConfigurationInput>,
): PortfolioGroupConfigurationInput => {
  return {
    executionConfiguration:
      overrides && overrides.hasOwnProperty('executionConfiguration')
        ? overrides.executionConfiguration!
        : anExecutionConfigurationInput(),
    hedgingConfiguration:
      overrides && overrides.hasOwnProperty('hedgingConfiguration')
        ? overrides.hedgingConfiguration!
        : aHedgingConfigurationInput(),
    id:
      overrides && overrides.hasOwnProperty('id')
        ? overrides.id!
        : '4ccb1ae5-fd58-42d4-b097-4ddc6fc1c4d9',
    instrumentConfiguration:
      overrides && overrides.hasOwnProperty('instrumentConfiguration')
        ? overrides.instrumentConfiguration!
        : [anInstrumentGroupConfigurationInput()],
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'voluptas',
    portfolioType:
      overrides && overrides.hasOwnProperty('portfolioType')
        ? overrides.portfolioType!
        : PortfolioType.Nostro,
    pricingConfiguration:
      overrides && overrides.hasOwnProperty('pricingConfiguration')
        ? overrides.pricingConfiguration!
        : aPricingConfigurationInput(),
  };
};

export const aPortfolioPredicateInput = (
  overrides?: Partial<PortfolioPredicateInput>,
): PortfolioPredicateInput => {
  return {
    method:
      overrides && overrides.hasOwnProperty('method')
        ? overrides.method!
        : PortfolioPredicateType.Contains,
    searchType:
      overrides && overrides.hasOwnProperty('searchType')
        ? overrides.searchType!
        : PortfolioSearchType.Id,
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : 'omnis',
  };
};

export const aPortfolioResponse = (
  overrides?: Partial<PortfolioResponse>,
): { __typename: 'PortfolioResponse' } & PortfolioResponse => {
  return {
    __typename: 'PortfolioResponse',
    archivedAt:
      overrides && overrides.hasOwnProperty('archivedAt') ? overrides.archivedAt! : 'earum',
    createdAt: overrides && overrides.hasOwnProperty('createdAt') ? overrides.createdAt! : 'at',
    dynamicScopes:
      overrides && overrides.hasOwnProperty('dynamicScopes')
        ? overrides.dynamicScopes!
        : [Scope.Create],
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'doloremque',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'qui',
    portfolioCurrency:
      overrides && overrides.hasOwnProperty('portfolioCurrency')
        ? overrides.portfolioCurrency!
        : 'assumenda',
    portfolioType:
      overrides && overrides.hasOwnProperty('portfolioType')
        ? overrides.portfolioType!
        : PortfolioType.Nostro,
    scopes: overrides && overrides.hasOwnProperty('scopes') ? overrides.scopes! : [Scope.Create],
    tags: overrides && overrides.hasOwnProperty('tags') ? overrides.tags! : [aTag()],
  };
};

export const aPortfolioSearchInput = (
  overrides?: Partial<PortfolioSearchInput>,
): PortfolioSearchInput => {
  return {
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'a',
    archived: overrides && overrides.hasOwnProperty('archived') ? overrides.archived! : false,
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 769,
    portfolioIds:
      overrides && overrides.hasOwnProperty('portfolioIds') ? overrides.portfolioIds! : ['libero'],
    portfolioPredicate:
      overrides && overrides.hasOwnProperty('portfolioPredicate')
        ? overrides.portfolioPredicate!
        : aPortfolioPredicateInput(),
    portfolioType:
      overrides && overrides.hasOwnProperty('portfolioType')
        ? overrides.portfolioType!
        : PortfolioType.Nostro,
    scopes: overrides && overrides.hasOwnProperty('scopes') ? overrides.scopes! : [Scope.Create],
    sortBy:
      overrides && overrides.hasOwnProperty('sortBy')
        ? overrides.sortBy!
        : PortfolioSortBy.CreatedAt,
    sortingOrder:
      overrides && overrides.hasOwnProperty('sortingOrder')
        ? overrides.sortingOrder!
        : SortingOrder.Asc,
    tagValues: overrides && overrides.hasOwnProperty('tagValues') ? overrides.tagValues! : ['qui'],
  };
};

export const aPortfolioTag = (
  overrides?: Partial<PortfolioTag>,
): { __typename: 'PortfolioTag' } & PortfolioTag => {
  return {
    __typename: 'PortfolioTag',
    key: overrides && overrides.hasOwnProperty('key') ? overrides.key! : 'reprehenderit',
    values: overrides && overrides.hasOwnProperty('values') ? overrides.values! : ['fuga'],
  };
};

export const aPositionConnection = (
  overrides?: Partial<PositionConnection>,
): { __typename: 'PositionConnection' } & PositionConnection => {
  return {
    __typename: 'PositionConnection',
    edges: overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [aPositionEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aPositionEdge = (
  overrides?: Partial<PositionEdge>,
): { __typename: 'PositionEdge' } & PositionEdge => {
  return {
    __typename: 'PositionEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'qui',
    node: overrides && overrides.hasOwnProperty('node') ? overrides.node! : aPositionResponse(),
  };
};

export const aPositionResponse = (
  overrides?: Partial<PositionResponse>,
): { __typename: 'PositionResponse' } & PositionResponse => {
  return {
    __typename: 'PositionResponse',
    account: overrides && overrides.hasOwnProperty('account') ? overrides.account! : 'officia',
    accountName:
      overrides && overrides.hasOwnProperty('accountName') ? overrides.accountName! : 'ut',
    availableForTradingQuantity:
      overrides && overrides.hasOwnProperty('availableForTradingQuantity')
        ? overrides.availableForTradingQuantity!
        : 9.7,
    availableForWithdrawalQuantity:
      overrides && overrides.hasOwnProperty('availableForWithdrawalQuantity')
        ? overrides.availableForWithdrawalQuantity!
        : 3.1,
    bookingCurrency:
      overrides && overrides.hasOwnProperty('bookingCurrency') ? overrides.bookingCurrency! : 'qui',
    currency:
      overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'voluptatem',
    grossAveragePrice:
      overrides && overrides.hasOwnProperty('grossAveragePrice')
        ? overrides.grossAveragePrice!
        : 9.95,
    grossCost: overrides && overrides.hasOwnProperty('grossCost') ? overrides.grossCost! : 8.43,
    grossCostSc:
      overrides && overrides.hasOwnProperty('grossCostSc') ? overrides.grossCostSc! : 2.22,
    grossRealizedPnl:
      overrides && overrides.hasOwnProperty('grossRealizedPnl')
        ? overrides.grossRealizedPnl!
        : 3.63,
    grossRealizedPnlSc:
      overrides && overrides.hasOwnProperty('grossRealizedPnlSc')
        ? overrides.grossRealizedPnlSc!
        : 8.81,
    grossUnrealizedPnl:
      overrides && overrides.hasOwnProperty('grossUnrealizedPnl')
        ? overrides.grossUnrealizedPnl!
        : 4.6,
    grossUnrealizedPnlSc:
      overrides && overrides.hasOwnProperty('grossUnrealizedPnlSc')
        ? overrides.grossUnrealizedPnlSc!
        : 0.07,
    marketValue:
      overrides && overrides.hasOwnProperty('marketValue') ? overrides.marketValue! : 4.85,
    marketValueSc:
      overrides && overrides.hasOwnProperty('marketValueSc') ? overrides.marketValueSc! : 7,
    netAveragePrice:
      overrides && overrides.hasOwnProperty('netAveragePrice') ? overrides.netAveragePrice! : 3.62,
    netCost: overrides && overrides.hasOwnProperty('netCost') ? overrides.netCost! : 8.61,
    netCostSc: overrides && overrides.hasOwnProperty('netCostSc') ? overrides.netCostSc! : 9.33,
    netRealizedPnl:
      overrides && overrides.hasOwnProperty('netRealizedPnl') ? overrides.netRealizedPnl! : 4.23,
    netRealizedPnlSc:
      overrides && overrides.hasOwnProperty('netRealizedPnlSc')
        ? overrides.netRealizedPnlSc!
        : 6.67,
    netUnrealizedPnl:
      overrides && overrides.hasOwnProperty('netUnrealizedPnl')
        ? overrides.netUnrealizedPnl!
        : 5.16,
    netUnrealizedPnlSc:
      overrides && overrides.hasOwnProperty('netUnrealizedPnlSc')
        ? overrides.netUnrealizedPnlSc!
        : 4.93,
    notionalQuantity:
      overrides && overrides.hasOwnProperty('notionalQuantity')
        ? overrides.notionalQuantity!
        : 4.32,
    pendingQuantity:
      overrides && overrides.hasOwnProperty('pendingQuantity') ? overrides.pendingQuantity! : 4.44,
    portfolio:
      overrides && overrides.hasOwnProperty('portfolio')
        ? overrides.portfolio!
        : aPortfolioResponse(),
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 7.16,
    settledQuantity:
      overrides && overrides.hasOwnProperty('settledQuantity') ? overrides.settledQuantity! : 4.75,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'quasi',
    unsettledQuantity:
      overrides && overrides.hasOwnProperty('unsettledQuantity')
        ? overrides.unsettledQuantity!
        : 7.49,
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 1.98,
  };
};

export const aPositionSearchInput = (
  overrides?: Partial<PositionSearchInput>,
): PositionSearchInput => {
  return {
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'quia',
    currency:
      overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : ['possimus'],
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 7406,
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'at',
    portfolio: overrides && overrides.hasOwnProperty('portfolio') ? overrides.portfolio! : ['illo'],
    sortingOrder:
      overrides && overrides.hasOwnProperty('sortingOrder')
        ? overrides.sortingOrder!
        : SortingOrder.Asc,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : ['aut'],
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount')
        ? overrides.venueAccount!
        : ['accusantium'],
  };
};

export const aPreTradeCheck = (
  overrides?: Partial<PreTradeCheck>,
): { __typename: 'PreTradeCheck' } & PreTradeCheck => {
  return {
    __typename: 'PreTradeCheck',
    channels:
      overrides && overrides.hasOwnProperty('channels')
        ? overrides.channels!
        : [PreTradeCheckChannels.Api],
    configuration:
      overrides && overrides.hasOwnProperty('configuration')
        ? overrides.configuration!
        : [aPreTradeCheckProperty()],
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'quia',
    level:
      overrides && overrides.hasOwnProperty('level') ? overrides.level! : PreTradeCheckLevel.Block,
    portfolioTags:
      overrides && overrides.hasOwnProperty('portfolioTags') ? overrides.portfolioTags! : [aTag()],
    portfolios:
      overrides && overrides.hasOwnProperty('portfolios')
        ? overrides.portfolios!
        : [aPortfolioResponse()],
    type: overrides && overrides.hasOwnProperty('type') ? overrides.type! : 'quis',
  };
};

export const aPreTradeCheckAuditLog = (
  overrides?: Partial<PreTradeCheckAuditLog>,
): { __typename: 'PreTradeCheckAuditLog' } & PreTradeCheckAuditLog => {
  return {
    __typename: 'PreTradeCheckAuditLog',
    createdAt: overrides && overrides.hasOwnProperty('createdAt') ? overrides.createdAt! : 'autem',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'voluptatem',
    order: overrides && overrides.hasOwnProperty('order') ? overrides.order! : anOrderSnapshot(),
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'corporis',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName') ? overrides.portfolioName! : 'laborum',
    preTradeCheckResults:
      overrides && overrides.hasOwnProperty('preTradeCheckResults')
        ? overrides.preTradeCheckResults!
        : [aPreTradeCheckResult()],
  };
};

export const aPreTradeCheckAuditLogConnection = (
  overrides?: Partial<PreTradeCheckAuditLogConnection>,
): { __typename: 'PreTradeCheckAuditLogConnection' } & PreTradeCheckAuditLogConnection => {
  return {
    __typename: 'PreTradeCheckAuditLogConnection',
    edges:
      overrides && overrides.hasOwnProperty('edges')
        ? overrides.edges!
        : [aPreTradeCheckAuditLogEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aPreTradeCheckAuditLogEdge = (
  overrides?: Partial<PreTradeCheckAuditLogEdge>,
): { __typename: 'PreTradeCheckAuditLogEdge' } & PreTradeCheckAuditLogEdge => {
  return {
    __typename: 'PreTradeCheckAuditLogEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'non',
    node:
      overrides && overrides.hasOwnProperty('node') ? overrides.node! : aPreTradeCheckAuditLog(),
  };
};

export const aPreTradeCheckAuditLogSearchInput = (
  overrides?: Partial<PreTradeCheckAuditLogSearchInput>,
): PreTradeCheckAuditLogSearchInput => {
  return {
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'asperiores',
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 2678,
    from: overrides && overrides.hasOwnProperty('from') ? overrides.from! : 'delectus',
    portfolioIds:
      overrides && overrides.hasOwnProperty('portfolioIds')
        ? overrides.portfolioIds!
        : ['suscipit'],
    to: overrides && overrides.hasOwnProperty('to') ? overrides.to! : 'non',
  };
};

export const aPreTradeCheckInput = (
  overrides?: Partial<PreTradeCheckInput>,
): PreTradeCheckInput => {
  return {
    channels:
      overrides && overrides.hasOwnProperty('channels')
        ? overrides.channels!
        : [PreTradeCheckChannels.Api],
    configuration:
      overrides && overrides.hasOwnProperty('configuration')
        ? overrides.configuration!
        : [aPreTradeCheckPropertyInput()],
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'vel',
    level:
      overrides && overrides.hasOwnProperty('level') ? overrides.level! : PreTradeCheckLevel.Block,
    portfolioTags:
      overrides && overrides.hasOwnProperty('portfolioTags')
        ? overrides.portfolioTags!
        : [aTagInput()],
    portfolios:
      overrides && overrides.hasOwnProperty('portfolios') ? overrides.portfolios! : ['et'],
    type: overrides && overrides.hasOwnProperty('type') ? overrides.type! : 'velit',
  };
};

export const aPreTradeCheckProperty = (
  overrides?: Partial<PreTradeCheckProperty>,
): { __typename: 'PreTradeCheckProperty' } & PreTradeCheckProperty => {
  return {
    __typename: 'PreTradeCheckProperty',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'ducimus',
    type:
      overrides && overrides.hasOwnProperty('type')
        ? overrides.type!
        : PreTradeCheckPropertyType.DateTime,
    values: overrides && overrides.hasOwnProperty('values') ? overrides.values! : ['voluptatem'],
  };
};

export const aPreTradeCheckPropertyInput = (
  overrides?: Partial<PreTradeCheckPropertyInput>,
): PreTradeCheckPropertyInput => {
  return {
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'est',
    type:
      overrides && overrides.hasOwnProperty('type')
        ? overrides.type!
        : PreTradeCheckPropertyType.DateTime,
    values: overrides && overrides.hasOwnProperty('values') ? overrides.values! : ['earum'],
  };
};

export const aPreTradeCheckPropertySchema = (
  overrides?: Partial<PreTradeCheckPropertySchema>,
): { __typename: 'PreTradeCheckPropertySchema' } & PreTradeCheckPropertySchema => {
  return {
    __typename: 'PreTradeCheckPropertySchema',
    format:
      overrides && overrides.hasOwnProperty('format')
        ? overrides.format!
        : PreTradeCheckPropertyFormat.Currency,
    isEnum: overrides && overrides.hasOwnProperty('isEnum') ? overrides.isEnum! : true,
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'enim',
    options: overrides && overrides.hasOwnProperty('options') ? overrides.options! : ['vero'],
    required: overrides && overrides.hasOwnProperty('required') ? overrides.required! : false,
    type:
      overrides && overrides.hasOwnProperty('type')
        ? overrides.type!
        : PreTradeCheckPropertyType.DateTime,
  };
};

export const aPreTradeCheckResult = (
  overrides?: Partial<PreTradeCheckResult>,
): { __typename: 'PreTradeCheckResult' } & PreTradeCheckResult => {
  return {
    __typename: 'PreTradeCheckResult',
    preTradeCheck:
      overrides && overrides.hasOwnProperty('preTradeCheck')
        ? overrides.preTradeCheck!
        : aPreTradeCheck(),
    reason: overrides && overrides.hasOwnProperty('reason') ? overrides.reason! : 'consequatur',
    status:
      overrides && overrides.hasOwnProperty('status')
        ? overrides.status!
        : PreTradeCheckStatus.Approved,
  };
};

export const aPreTradeCheckSchema = (
  overrides?: Partial<PreTradeCheckSchema>,
): { __typename: 'PreTradeCheckSchema' } & PreTradeCheckSchema => {
  return {
    __typename: 'PreTradeCheckSchema',
    configuration:
      overrides && overrides.hasOwnProperty('configuration')
        ? overrides.configuration!
        : [aPreTradeCheckPropertySchema()],
    type: overrides && overrides.hasOwnProperty('type') ? overrides.type! : 'repellendus',
  };
};

export const aPricingConfiguration = (
  overrides?: Partial<PricingConfiguration>,
): { __typename: 'PricingConfiguration' } & PricingConfiguration => {
  return {
    __typename: 'PricingConfiguration',
    markup: overrides && overrides.hasOwnProperty('markup') ? overrides.markup! : 9.85,
    venueAccounts:
      overrides && overrides.hasOwnProperty('venueAccounts')
        ? overrides.venueAccounts!
        : [aVenueAccountDesc()],
  };
};

export const aPricingConfigurationInput = (
  overrides?: Partial<PricingConfigurationInput>,
): PricingConfigurationInput => {
  return {
    markup: overrides && overrides.hasOwnProperty('markup') ? overrides.markup! : 1.59,
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount')
        ? overrides.venueAccount!
        : ['laudantium'],
  };
};

export const aQuery = (overrides?: Partial<Query>): { __typename: 'Query' } & Query => {
  return {
    __typename: 'Query',
    apiKeys: overrides && overrides.hasOwnProperty('apiKeys') ? overrides.apiKeys! : [anApiKey()],
    auditEventLogsByType:
      overrides && overrides.hasOwnProperty('auditEventLogsByType')
        ? overrides.auditEventLogsByType!
        : [anAuditLogEvent()],
    checkAccountName:
      overrides && overrides.hasOwnProperty('checkAccountName')
        ? overrides.checkAccountName!
        : aValidationResponse(),
    checkPortfolioName:
      overrides && overrides.hasOwnProperty('checkPortfolioName')
        ? overrides.checkPortfolioName!
        : aValidationResponse(),
    connectorById:
      overrides && overrides.hasOwnProperty('connectorById')
        ? overrides.connectorById!
        : aConnectorResponse(),
    connectorDetails:
      overrides && overrides.hasOwnProperty('connectorDetails')
        ? overrides.connectorDetails!
        : aConnectorDetailsResponse(),
    connectorList:
      overrides && overrides.hasOwnProperty('connectorList')
        ? overrides.connectorList!
        : [aConnectorResponse()],
    connectorNameCheck:
      overrides && overrides.hasOwnProperty('connectorNameCheck')
        ? overrides.connectorNameCheck!
        : true,
    connectorTemplate:
      overrides && overrides.hasOwnProperty('connectorTemplate')
        ? overrides.connectorTemplate!
        : [aConfigField()],
    connectorTemplateByVenue:
      overrides && overrides.hasOwnProperty('connectorTemplateByVenue')
        ? overrides.connectorTemplateByVenue!
        : [aConnectorConfigField()],
    connectorTypeList:
      overrides && overrides.hasOwnProperty('connectorTypeList')
        ? overrides.connectorTypeList!
        : ['ea'],
    conversionSourceVenueAccounts:
      overrides && overrides.hasOwnProperty('conversionSourceVenueAccounts')
        ? overrides.conversionSourceVenueAccounts!
        : [aConversionSourceVenueAccountResponse()],
    conversionSources:
      overrides && overrides.hasOwnProperty('conversionSources')
        ? overrides.conversionSources!
        : [aConversionSourceResponse()],
    currencySearch:
      overrides && overrides.hasOwnProperty('currencySearch')
        ? overrides.currencySearch!
        : [aCurrencyResponse()],
    effectiveVenueAccounts:
      overrides && overrides.hasOwnProperty('effectiveVenueAccounts')
        ? overrides.effectiveVenueAccounts!
        : anEffectiveVenueAccountsResponse(),
    entitlements:
      overrides && overrides.hasOwnProperty('entitlements')
        ? overrides.entitlements!
        : ['voluptatem'],
    groupNames:
      overrides && overrides.hasOwnProperty('groupNames') ? overrides.groupNames! : ['qui'],
    groupStaticPermissions:
      overrides && overrides.hasOwnProperty('groupStaticPermissions')
        ? overrides.groupStaticPermissions!
        : [aPermission()],
    groupsPermissionsForResource:
      overrides && overrides.hasOwnProperty('groupsPermissionsForResource')
        ? overrides.groupsPermissionsForResource!
        : [aGroupsPermissionsForResourceResponse()],
    hasOwnPermission:
      overrides && overrides.hasOwnProperty('hasOwnPermission')
        ? overrides.hasOwnPermission!
        : false,
    hasUserPermission:
      overrides && overrides.hasOwnProperty('hasUserPermission')
        ? overrides.hasUserPermission!
        : true,
    instrumentSearch:
      overrides && overrides.hasOwnProperty('instrumentSearch')
        ? overrides.instrumentSearch!
        : anInstrumentsConnection(),
    instruments:
      overrides && overrides.hasOwnProperty('instruments')
        ? overrides.instruments!
        : [anInstrumentResponse()],
    internalExchangeConfiguration:
      overrides && overrides.hasOwnProperty('internalExchangeConfiguration')
        ? overrides.internalExchangeConfiguration!
        : anInternalExchangeConfigurationResponse(),
    ledgerEntries:
      overrides && overrides.hasOwnProperty('ledgerEntries')
        ? overrides.ledgerEntries!
        : aLedgerEntryConnection(),
    matches:
      overrides && overrides.hasOwnProperty('matches') ? overrides.matches! : aMatchesResult(),
    orderBookSnapshot:
      overrides && overrides.hasOwnProperty('orderBookSnapshot')
        ? overrides.orderBookSnapshot!
        : anOrderBookSnapshot(),
    orderStates:
      overrides && overrides.hasOwnProperty('orderStates')
        ? overrides.orderStates!
        : [anOrderStateResponse()],
    orderStatesWithPredicates:
      overrides && overrides.hasOwnProperty('orderStatesWithPredicates')
        ? overrides.orderStatesWithPredicates!
        : anOrderStateConnection(),
    permissionSearch:
      overrides && overrides.hasOwnProperty('permissionSearch')
        ? overrides.permissionSearch!
        : aPermissionConnection(),
    portfolioById:
      overrides && overrides.hasOwnProperty('portfolioById')
        ? overrides.portfolioById!
        : aPortfolioResponse(),
    portfolioConfigValidation:
      overrides && overrides.hasOwnProperty('portfolioConfigValidation')
        ? overrides.portfolioConfigValidation!
        : aConfigValidationResult(),
    portfolioConfiguration:
      overrides && overrides.hasOwnProperty('portfolioConfiguration')
        ? overrides.portfolioConfiguration!
        : aPortfolioConfiguration(),
    portfolioConfigurationList:
      overrides && overrides.hasOwnProperty('portfolioConfigurationList')
        ? overrides.portfolioConfigurationList!
        : [aPortfolioConfigurationFlat()],
    portfolioGroupConfiguration:
      overrides && overrides.hasOwnProperty('portfolioGroupConfiguration')
        ? overrides.portfolioGroupConfiguration!
        : aPortfolioGroupConfiguration(),
    portfolioGroupConfigurationList:
      overrides && overrides.hasOwnProperty('portfolioGroupConfigurationList')
        ? overrides.portfolioGroupConfigurationList!
        : [aPortfolioGroupConfigurationFlat()],
    portfolioHedgingConfigValidation:
      overrides && overrides.hasOwnProperty('portfolioHedgingConfigValidation')
        ? overrides.portfolioHedgingConfigValidation!
        : aHedgingConfigValidationResult(),
    portfolioSearch:
      overrides && overrides.hasOwnProperty('portfolioSearch')
        ? overrides.portfolioSearch!
        : aPortfolioConnection(),
    portfolioTags:
      overrides && overrides.hasOwnProperty('portfolioTags')
        ? overrides.portfolioTags!
        : [aPortfolioTag()],
    positionTypes:
      overrides && overrides.hasOwnProperty('positionTypes') ? overrides.positionTypes! : ['totam'],
    positions:
      overrides && overrides.hasOwnProperty('positions')
        ? overrides.positions!
        : aPositionConnection(),
    preTradeCheckAuditLogs:
      overrides && overrides.hasOwnProperty('preTradeCheckAuditLogs')
        ? overrides.preTradeCheckAuditLogs!
        : aPreTradeCheckAuditLogConnection(),
    preTradeCheckFormSchema:
      overrides && overrides.hasOwnProperty('preTradeCheckFormSchema')
        ? overrides.preTradeCheckFormSchema!
        : [aPreTradeCheckSchema()],
    preTradeChecks:
      overrides && overrides.hasOwnProperty('preTradeChecks')
        ? overrides.preTradeChecks!
        : [aPreTradeCheck()],
    quotingConfigValidations:
      overrides && overrides.hasOwnProperty('quotingConfigValidations')
        ? overrides.quotingConfigValidations!
        : [aQuotingConfigValidationResult()],
    quotingConfiguration:
      overrides && overrides.hasOwnProperty('quotingConfiguration')
        ? overrides.quotingConfiguration!
        : [aQuotingConfiguration()],
    rateSubscriptions:
      overrides && overrides.hasOwnProperty('rateSubscriptions')
        ? overrides.rateSubscriptions!
        : [aRateSubscriptionResponse()],
    settlementConfiguration:
      overrides && overrides.hasOwnProperty('settlementConfiguration')
        ? overrides.settlementConfiguration!
        : [aSettlementConfigurationResponse()],
    settlementHistoryRuns:
      overrides && overrides.hasOwnProperty('settlementHistoryRuns')
        ? overrides.settlementHistoryRuns!
        : [aSettlementRunResponse()],
    settlementTransactions:
      overrides && overrides.hasOwnProperty('settlementTransactions')
        ? overrides.settlementTransactions!
        : aSettlementTransactionConnection(),
    symbolSearch:
      overrides && overrides.hasOwnProperty('symbolSearch')
        ? overrides.symbolSearch!
        : aSymbolSearchConnection(),
    systemCurrency:
      overrides && overrides.hasOwnProperty('systemCurrency')
        ? overrides.systemCurrency!
        : 'blanditiis',
    timeline:
      overrides && overrides.hasOwnProperty('timeline')
        ? overrides.timeline!
        : aTimelineConnection(),
    transactionTypes:
      overrides && overrides.hasOwnProperty('transactionTypes')
        ? overrides.transactionTypes!
        : ['sint'],
    transactions:
      overrides && overrides.hasOwnProperty('transactions')
        ? overrides.transactions!
        : aTransactionConnection(),
    transferStates:
      overrides && overrides.hasOwnProperty('transferStates')
        ? overrides.transferStates!
        : aTransferStateConnection(),
    userData:
      overrides && overrides.hasOwnProperty('userData') ? overrides.userData! : aUserDataResponse(),
    userNames: overrides && overrides.hasOwnProperty('userNames') ? overrides.userNames! : ['quo'],
    userStaticPermissions:
      overrides && overrides.hasOwnProperty('userStaticPermissions')
        ? overrides.userStaticPermissions!
        : [aPermission()],
    userWithPermissions:
      overrides && overrides.hasOwnProperty('userWithPermissions')
        ? overrides.userWithPermissions!
        : aUserResponse(),
    usersPermissionsForResource:
      overrides && overrides.hasOwnProperty('usersPermissionsForResource')
        ? overrides.usersPermissionsForResource!
        : [aUsersPermissionsForResourceResponse()],
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount')
        ? overrides.venueAccount!
        : aVenueAccountResponse(),
    venueAccountById:
      overrides && overrides.hasOwnProperty('venueAccountById')
        ? overrides.venueAccountById!
        : aVenueAccount(),
    venueAccountDetails:
      overrides && overrides.hasOwnProperty('venueAccountDetails')
        ? overrides.venueAccountDetails!
        : aVenueAccountDetailsResponse(),
    venueAccountList:
      overrides && overrides.hasOwnProperty('venueAccountList')
        ? overrides.venueAccountList!
        : aVenueAccountConnection(),
    venueAccountNameCheck:
      overrides && overrides.hasOwnProperty('venueAccountNameCheck')
        ? overrides.venueAccountNameCheck!
        : true,
    venueAccountPositions:
      overrides && overrides.hasOwnProperty('venueAccountPositions')
        ? overrides.venueAccountPositions!
        : aVenueAccountPositionsResponse(),
    venueAccounts:
      overrides && overrides.hasOwnProperty('venueAccounts')
        ? overrides.venueAccounts!
        : [aVenueAccountsPerVenue()],
    venueAccountsWithUnsettledCount:
      overrides && overrides.hasOwnProperty('venueAccountsWithUnsettledCount')
        ? overrides.venueAccountsWithUnsettledCount!
        : [aVenueAccountsWithUnsettledCountResponse()],
    venueCapabilities:
      overrides && overrides.hasOwnProperty('venueCapabilities')
        ? overrides.venueCapabilities!
        : [aVenueCapabilitiesResponse()],
    venueList:
      overrides && overrides.hasOwnProperty('venueList')
        ? overrides.venueList!
        : [aVenueResponse()],
    venues: overrides && overrides.hasOwnProperty('venues') ? overrides.venues! : [aVenue()],
    walletAccountSearch:
      overrides && overrides.hasOwnProperty('walletAccountSearch')
        ? overrides.walletAccountSearch!
        : aWalletAccountConnection(),
  };
};

export const aQuotingCalendar = (
  overrides?: Partial<QuotingCalendar>,
): { __typename: 'QuotingCalendar' } & QuotingCalendar => {
  return {
    __typename: 'QuotingCalendar',
    additionalMarkup:
      overrides && overrides.hasOwnProperty('additionalMarkup')
        ? overrides.additionalMarkup!
        : 5.78,
    endTime: overrides && overrides.hasOwnProperty('endTime') ? overrides.endTime! : 'est',
    fromDayOfTheWeek:
      overrides && overrides.hasOwnProperty('fromDayOfTheWeek')
        ? overrides.fromDayOfTheWeek!
        : DayOfTheWeek.Friday,
    quoteTTL: overrides && overrides.hasOwnProperty('quoteTTL') ? overrides.quoteTTL! : 5891,
    quoteTTLUnit:
      overrides && overrides.hasOwnProperty('quoteTTLUnit')
        ? overrides.quoteTTLUnit!
        : QuoteTtlUnit.Days,
    startTime: overrides && overrides.hasOwnProperty('startTime') ? overrides.startTime! : 'dolor',
    toDayOfTheWeek:
      overrides && overrides.hasOwnProperty('toDayOfTheWeek')
        ? overrides.toDayOfTheWeek!
        : DayOfTheWeek.Friday,
  };
};

export const aQuotingCalendarInput = (
  overrides?: Partial<QuotingCalendarInput>,
): QuotingCalendarInput => {
  return {
    additionalMarkup:
      overrides && overrides.hasOwnProperty('additionalMarkup')
        ? overrides.additionalMarkup!
        : 7.12,
    endTime: overrides && overrides.hasOwnProperty('endTime') ? overrides.endTime! : 'aut',
    fromDayOfTheWeek:
      overrides && overrides.hasOwnProperty('fromDayOfTheWeek')
        ? overrides.fromDayOfTheWeek!
        : DayOfTheWeek.Friday,
    quoteTTL: overrides && overrides.hasOwnProperty('quoteTTL') ? overrides.quoteTTL! : 9961,
    quoteTTLUnit:
      overrides && overrides.hasOwnProperty('quoteTTLUnit')
        ? overrides.quoteTTLUnit!
        : QuoteTtlUnit.Days,
    startTime:
      overrides && overrides.hasOwnProperty('startTime') ? overrides.startTime! : 'dolores',
    toDayOfTheWeek:
      overrides && overrides.hasOwnProperty('toDayOfTheWeek')
        ? overrides.toDayOfTheWeek!
        : DayOfTheWeek.Friday,
  };
};

export const aQuotingConfigValidationResult = (
  overrides?: Partial<QuotingConfigValidationResult>,
): { __typename: 'QuotingConfigValidationResult' } & QuotingConfigValidationResult => {
  return {
    __typename: 'QuotingConfigValidationResult',
    clobUid: overrides && overrides.hasOwnProperty('clobUid') ? overrides.clobUid! : 'cupiditate',
    resultsPerInstrument:
      overrides && overrides.hasOwnProperty('resultsPerInstrument')
        ? overrides.resultsPerInstrument!
        : [aConfigInstrumentValidationResult()],
  };
};

export const aQuotingConfiguration = (
  overrides?: Partial<QuotingConfiguration>,
): { __typename: 'QuotingConfiguration' } & QuotingConfiguration => {
  return {
    __typename: 'QuotingConfiguration',
    askMarkup: overrides && overrides.hasOwnProperty('askMarkup') ? overrides.askMarkup! : 6.95,
    bidMarkup: overrides && overrides.hasOwnProperty('bidMarkup') ? overrides.bidMarkup! : 2.99,
    clobUid: overrides && overrides.hasOwnProperty('clobUid') ? overrides.clobUid! : 'aut',
    deactivated:
      overrides && overrides.hasOwnProperty('deactivated') ? overrides.deactivated! : true,
    displayName:
      overrides && overrides.hasOwnProperty('displayName') ? overrides.displayName! : 'fugit',
    hedgingAccountDescs:
      overrides && overrides.hasOwnProperty('hedgingAccountDescs')
        ? overrides.hedgingAccountDescs!
        : [aVenueAccountDesc()],
    hedgingAccounts:
      overrides && overrides.hasOwnProperty('hedgingAccounts')
        ? overrides.hedgingAccounts!
        : ['aliquam'],
    hedgingSafetyMargin:
      overrides && overrides.hasOwnProperty('hedgingSafetyMargin')
        ? overrides.hedgingSafetyMargin!
        : 3.43,
    instrumentConfigurations:
      overrides && overrides.hasOwnProperty('instrumentConfigurations')
        ? overrides.instrumentConfigurations!
        : [anInstrumentQuotingConfiguration()],
    maxQuantityFactor:
      overrides && overrides.hasOwnProperty('maxQuantityFactor')
        ? overrides.maxQuantityFactor!
        : 1.35,
    maximumDepth:
      overrides && overrides.hasOwnProperty('maximumDepth') ? overrides.maximumDepth! : 0.89,
    minQuantityFactor:
      overrides && overrides.hasOwnProperty('minQuantityFactor')
        ? overrides.minQuantityFactor!
        : 7.26,
    nostroPortfolioId:
      overrides && overrides.hasOwnProperty('nostroPortfolioId')
        ? overrides.nostroPortfolioId!
        : 'perferendis',
    nostroPortfolioName:
      overrides && overrides.hasOwnProperty('nostroPortfolioName')
        ? overrides.nostroPortfolioName!
        : 'vel',
    quoteTTL: overrides && overrides.hasOwnProperty('quoteTTL') ? overrides.quoteTTL! : 8192,
    sourceAccountDescs:
      overrides && overrides.hasOwnProperty('sourceAccountDescs')
        ? overrides.sourceAccountDescs!
        : [aVenueAccountDesc()],
    sourceAccounts:
      overrides && overrides.hasOwnProperty('sourceAccounts')
        ? overrides.sourceAccounts!
        : ['dolores'],
    sources:
      overrides && overrides.hasOwnProperty('sources')
        ? overrides.sources!
        : [aQuotingSourceAccountConfig()],
    throttlingPeriod:
      overrides && overrides.hasOwnProperty('throttlingPeriod')
        ? overrides.throttlingPeriod!
        : 3472,
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 'sint',
  };
};

export const aQuotingConfigurationInput = (
  overrides?: Partial<QuotingConfigurationInput>,
): QuotingConfigurationInput => {
  return {
    askMarkup: overrides && overrides.hasOwnProperty('askMarkup') ? overrides.askMarkup! : 8.36,
    bidMarkup: overrides && overrides.hasOwnProperty('bidMarkup') ? overrides.bidMarkup! : 5.07,
    deactivated:
      overrides && overrides.hasOwnProperty('deactivated') ? overrides.deactivated! : false,
    displayName:
      overrides && overrides.hasOwnProperty('displayName') ? overrides.displayName! : 'possimus',
    hedgingAccounts:
      overrides && overrides.hasOwnProperty('hedgingAccounts')
        ? overrides.hedgingAccounts!
        : ['ipsa'],
    hedgingSafetyMargin:
      overrides && overrides.hasOwnProperty('hedgingSafetyMargin')
        ? overrides.hedgingSafetyMargin!
        : 5.09,
    maxQuantityFactor:
      overrides && overrides.hasOwnProperty('maxQuantityFactor')
        ? overrides.maxQuantityFactor!
        : 8.22,
    maximumDepth:
      overrides && overrides.hasOwnProperty('maximumDepth') ? overrides.maximumDepth! : 5.42,
    minQuantityFactor:
      overrides && overrides.hasOwnProperty('minQuantityFactor')
        ? overrides.minQuantityFactor!
        : 2.52,
    nostroPortfolioId:
      overrides && overrides.hasOwnProperty('nostroPortfolioId')
        ? overrides.nostroPortfolioId!
        : 'quia',
    quoteTTL: overrides && overrides.hasOwnProperty('quoteTTL') ? overrides.quoteTTL! : 7414,
    sourceAccounts:
      overrides && overrides.hasOwnProperty('sourceAccounts')
        ? overrides.sourceAccounts!
        : ['soluta'],
    sources:
      overrides && overrides.hasOwnProperty('sources')
        ? overrides.sources!
        : [aQuotingSourceAccountConfigInput()],
    throttlingPeriod:
      overrides && overrides.hasOwnProperty('throttlingPeriod')
        ? overrides.throttlingPeriod!
        : 7427,
  };
};

export const aQuotingSourceAccountConfig = (
  overrides?: Partial<QuotingSourceAccountConfig>,
): { __typename: 'QuotingSourceAccountConfig' } & QuotingSourceAccountConfig => {
  return {
    __typename: 'QuotingSourceAccountConfig',
    accountId: overrides && overrides.hasOwnProperty('accountId') ? overrides.accountId! : 'ea',
    accountName:
      overrides && overrides.hasOwnProperty('accountName') ? overrides.accountName! : 'dolorem',
    calendarEntries:
      overrides && overrides.hasOwnProperty('calendarEntries')
        ? overrides.calendarEntries!
        : [aQuotingCalendar()],
    defaultQuoteTTL:
      overrides && overrides.hasOwnProperty('defaultQuoteTTL') ? overrides.defaultQuoteTTL! : 6835,
    isDroppingQuotesOnDisconnection:
      overrides && overrides.hasOwnProperty('isDroppingQuotesOnDisconnection')
        ? overrides.isDroppingQuotesOnDisconnection!
        : true,
    quoteTTLUnit:
      overrides && overrides.hasOwnProperty('quoteTTLUnit')
        ? overrides.quoteTTLUnit!
        : QuoteTtlUnit.Days,
  };
};

export const aQuotingSourceAccountConfigInput = (
  overrides?: Partial<QuotingSourceAccountConfigInput>,
): QuotingSourceAccountConfigInput => {
  return {
    accountId:
      overrides && overrides.hasOwnProperty('accountId') ? overrides.accountId! : 'consequatur',
    calendarEntries:
      overrides && overrides.hasOwnProperty('calendarEntries')
        ? overrides.calendarEntries!
        : [aQuotingCalendarInput()],
    defaultQuoteTTL:
      overrides && overrides.hasOwnProperty('defaultQuoteTTL') ? overrides.defaultQuoteTTL! : 7247,
    isDroppingQuotesOnDisconnection:
      overrides && overrides.hasOwnProperty('isDroppingQuotesOnDisconnection')
        ? overrides.isDroppingQuotesOnDisconnection!
        : true,
    quoteTTLUnit:
      overrides && overrides.hasOwnProperty('quoteTTLUnit')
        ? overrides.quoteTTLUnit!
        : QuoteTtlUnit.Days,
  };
};

export const aRateSubscriptionResponse = (
  overrides?: Partial<RateSubscriptionResponse>,
): { __typename: 'RateSubscriptionResponse' } & RateSubscriptionResponse => {
  return {
    __typename: 'RateSubscriptionResponse',
    baseCurrency:
      overrides && overrides.hasOwnProperty('baseCurrency') ? overrides.baseCurrency! : 'quaerat',
    health:
      overrides && overrides.hasOwnProperty('health')
        ? overrides.health!
        : RateSubscriptionHealth.Down,
    lastEventTimestamp:
      overrides && overrides.hasOwnProperty('lastEventTimestamp')
        ? overrides.lastEventTimestamp!
        : 'et',
    lastPrice:
      overrides && overrides.hasOwnProperty('lastPrice') ? overrides.lastPrice! : 'explicabo',
    quoteCurrency:
      overrides && overrides.hasOwnProperty('quoteCurrency') ? overrides.quoteCurrency! : 'maxime',
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId')
        ? overrides.venueAccountId!
        : 'maiores',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'officia',
  };
};

export const aReferenceDataCapabilities = (
  overrides?: Partial<ReferenceDataCapabilities>,
): { __typename: 'ReferenceDataCapabilities' } & ReferenceDataCapabilities => {
  return {
    __typename: 'ReferenceDataCapabilities',
    canRetrieveAll:
      overrides && overrides.hasOwnProperty('canRetrieveAll') ? overrides.canRetrieveAll! : true,
    canRetrieveSampleData:
      overrides && overrides.hasOwnProperty('canRetrieveSampleData')
        ? overrides.canRetrieveSampleData!
        : false,
    canRetrieveSampleDataRequests:
      overrides && overrides.hasOwnProperty('canRetrieveSampleDataRequests')
        ? overrides.canRetrieveSampleDataRequests!
        : false,
    retrievableByRequest:
      overrides && overrides.hasOwnProperty('retrievableByRequest')
        ? overrides.retrievableByRequest!
        : [aKeyValues()],
  };
};

export const aResetConfigurationInput = (
  overrides?: Partial<ResetConfigurationInput>,
): ResetConfigurationInput => {
  return {
    configurationLevel:
      overrides && overrides.hasOwnProperty('configurationLevel')
        ? overrides.configurationLevel!
        : ConfigurationLevel.Portfolio,
    configurationType:
      overrides && overrides.hasOwnProperty('configurationType')
        ? overrides.configurationType!
        : ConfigurationType.Execution,
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId')
        ? overrides.instrumentId!
        : '639087e2-b457-40ac-8a97-9d295642f5db',
    resourceId:
      overrides && overrides.hasOwnProperty('resourceId')
        ? overrides.resourceId!
        : '22797b26-f0d3-4b6c-9161-5ee55e1d4a8f',
  };
};

export const aRootExecution = (
  overrides?: Partial<RootExecution>,
): { __typename: 'RootExecution' } & RootExecution => {
  return {
    __typename: 'RootExecution',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'neque',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'ut',
  };
};

export const aSelectAllTransactionsFiltersInput = (
  overrides?: Partial<SelectAllTransactionsFiltersInput>,
): SelectAllTransactionsFiltersInput => {
  return {
    accountId:
      overrides && overrides.hasOwnProperty('accountId') ? overrides.accountId! : ['rerum'],
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : ['iste'],
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'sit',
    from: overrides && overrides.hasOwnProperty('from') ? overrides.from! : 'debitis',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'debitis',
    parentOrderId:
      overrides && overrides.hasOwnProperty('parentOrderId')
        ? overrides.parentOrderId!
        : 'inventore',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : ['possimus'],
    rootExecutionId:
      overrides && overrides.hasOwnProperty('rootExecutionId')
        ? overrides.rootExecutionId!
        : 'doloribus',
    rootOrderId:
      overrides && overrides.hasOwnProperty('rootOrderId') ? overrides.rootOrderId! : 'blanditiis',
    selected: overrides && overrides.hasOwnProperty('selected') ? overrides.selected! : true,
    to: overrides && overrides.hasOwnProperty('to') ? overrides.to! : 'suscipit',
    underlyingExecutionId:
      overrides && overrides.hasOwnProperty('underlyingExecutionId')
        ? overrides.underlyingExecutionId!
        : 'fugit',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'qui',
  };
};

export const aSelectAllTransactionsInput = (
  overrides?: Partial<SelectAllTransactionsInput>,
): SelectAllTransactionsInput => {
  return {
    filters:
      overrides && overrides.hasOwnProperty('filters')
        ? overrides.filters!
        : aSelectAllTransactionsFiltersInput(),
    settlementRunId:
      overrides && overrides.hasOwnProperty('settlementRunId') ? overrides.settlementRunId! : 'ut',
  };
};

export const aSelectTransactionsInput = (
  overrides?: Partial<SelectTransactionsInput>,
): SelectTransactionsInput => {
  return {
    changes:
      overrides && overrides.hasOwnProperty('changes')
        ? overrides.changes!
        : [aTransactionSelect()],
    settlementRunId:
      overrides && overrides.hasOwnProperty('settlementRunId')
        ? overrides.settlementRunId!
        : 'illo',
  };
};

export const aSettlement = (
  overrides?: Partial<Settlement>,
): { __typename: 'Settlement' } & Settlement => {
  return {
    __typename: 'Settlement',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'ut',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'sunt',
    settledTransactionIds:
      overrides && overrides.hasOwnProperty('settledTransactionIds')
        ? overrides.settledTransactionIds!
        : ['consequatur'],
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 5.8,
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'voluptas',
  };
};

export const aSettlementAccountConfiguration = (
  overrides?: Partial<SettlementAccountConfiguration>,
): { __typename: 'SettlementAccountConfiguration' } & SettlementAccountConfiguration => {
  return {
    __typename: 'SettlementAccountConfiguration',
    assetToWalletMap:
      overrides && overrides.hasOwnProperty('assetToWalletMap')
        ? overrides.assetToWalletMap!
        : [aWalletByAsset()],
    automationEnabled:
      overrides && overrides.hasOwnProperty('automationEnabled')
        ? overrides.automationEnabled!
        : false,
    daysExcluded:
      overrides && overrides.hasOwnProperty('daysExcluded') ? overrides.daysExcluded! : ['aut'],
    directionPriority:
      overrides && overrides.hasOwnProperty('directionPriority')
        ? overrides.directionPriority!
        : SettlementDirectionPriority.None,
    legPriority:
      overrides && overrides.hasOwnProperty('legPriority')
        ? overrides.legPriority!
        : SettlementLegPriority.CryptoFirst,
    schedule:
      overrides && overrides.hasOwnProperty('schedule')
        ? overrides.schedule!
        : [aSettlementSchedulePoint()],
    scheduleTZid:
      overrides && overrides.hasOwnProperty('scheduleTZid') ? overrides.scheduleTZid! : 'quo',
    treasuryManagementAutomationEnabled:
      overrides && overrides.hasOwnProperty('treasuryManagementAutomationEnabled')
        ? overrides.treasuryManagementAutomationEnabled!
        : false,
    treasuryThresholds:
      overrides && overrides.hasOwnProperty('treasuryThresholds')
        ? overrides.treasuryThresholds!
        : [aTreasuryThreshold()],
  };
};

export const aSettlementAccountConfigurationInput = (
  overrides?: Partial<SettlementAccountConfigurationInput>,
): SettlementAccountConfigurationInput => {
  return {
    assetToWalletMap:
      overrides && overrides.hasOwnProperty('assetToWalletMap')
        ? overrides.assetToWalletMap!
        : [aWalletByAssetInput()],
    automationEnabled:
      overrides && overrides.hasOwnProperty('automationEnabled')
        ? overrides.automationEnabled!
        : false,
    daysExcluded:
      overrides && overrides.hasOwnProperty('daysExcluded') ? overrides.daysExcluded! : ['dolorem'],
    directionPriority:
      overrides && overrides.hasOwnProperty('directionPriority')
        ? overrides.directionPriority!
        : SettlementDirectionPriority.None,
    legPriority:
      overrides && overrides.hasOwnProperty('legPriority')
        ? overrides.legPriority!
        : SettlementLegPriority.CryptoFirst,
    schedule:
      overrides && overrides.hasOwnProperty('schedule')
        ? overrides.schedule!
        : [aSettlementSchedulePointInput()],
    scheduleTZid:
      overrides && overrides.hasOwnProperty('scheduleTZid') ? overrides.scheduleTZid! : 'rem',
    treasuryManagementAutomationEnabled:
      overrides && overrides.hasOwnProperty('treasuryManagementAutomationEnabled')
        ? overrides.treasuryManagementAutomationEnabled!
        : false,
    treasuryThresholds:
      overrides && overrides.hasOwnProperty('treasuryThresholds')
        ? overrides.treasuryThresholds!
        : [aTreasuryThresholdInput()],
  };
};

export const aSettlementConfigurationInput = (
  overrides?: Partial<SettlementConfigurationInput>,
): SettlementConfigurationInput => {
  return {
    accountId:
      overrides && overrides.hasOwnProperty('accountId') ? overrides.accountId! : 'ratione',
    config:
      overrides && overrides.hasOwnProperty('config')
        ? overrides.config!
        : aSettlementAccountConfigurationInput(),
  };
};

export const aSettlementConfigurationResponse = (
  overrides?: Partial<SettlementConfigurationResponse>,
): { __typename: 'SettlementConfigurationResponse' } & SettlementConfigurationResponse => {
  return {
    __typename: 'SettlementConfigurationResponse',
    accountId: overrides && overrides.hasOwnProperty('accountId') ? overrides.accountId! : 'dolor',
    config:
      overrides && overrides.hasOwnProperty('config')
        ? overrides.config!
        : aSettlementAccountConfiguration(),
  };
};

export const aSettlementLeg = (
  overrides?: Partial<SettlementLeg>,
): { __typename: 'SettlementLeg' } & SettlementLeg => {
  return {
    __typename: 'SettlementLeg',
    amountSc: overrides && overrides.hasOwnProperty('amountSc') ? overrides.amountSc! : 'et',
    asset: overrides && overrides.hasOwnProperty('asset') ? overrides.asset! : 'molestiae',
    auto: overrides && overrides.hasOwnProperty('auto') ? overrides.auto! : false,
    direction:
      overrides && overrides.hasOwnProperty('direction')
        ? overrides.direction!
        : SettlementDirection.Receive,
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'quibusdam',
    quantity:
      overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 'dignissimos',
    status:
      overrides && overrides.hasOwnProperty('status')
        ? overrides.status!
        : SettlementStatus.Canceled,
    transferId: overrides && overrides.hasOwnProperty('transferId') ? overrides.transferId! : 'ea',
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'architecto',
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId') ? overrides.venueAccountId! : 'sed',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'adipisci',
  };
};

export const aSettlementRunResponse = (
  overrides?: Partial<SettlementRunResponse>,
): { __typename: 'SettlementRunResponse' } & SettlementRunResponse => {
  return {
    __typename: 'SettlementRunResponse',
    completionDate:
      overrides && overrides.hasOwnProperty('completionDate') ? overrides.completionDate! : 'harum',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'nesciunt',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'atque',
    legs: overrides && overrides.hasOwnProperty('legs') ? overrides.legs! : [aSettlementLeg()],
    mutationType:
      overrides && overrides.hasOwnProperty('mutationType')
        ? overrides.mutationType!
        : MutationType.Created,
    status:
      overrides && overrides.hasOwnProperty('status')
        ? overrides.status!
        : SettlementStatus.Canceled,
  };
};

export const aSettlementSchedulePoint = (
  overrides?: Partial<SettlementSchedulePoint>,
): { __typename: 'SettlementSchedulePoint' } & SettlementSchedulePoint => {
  return {
    __typename: 'SettlementSchedulePoint',
    day: overrides && overrides.hasOwnProperty('day') ? overrides.day! : DayOfTheWeek.Friday,
    time: overrides && overrides.hasOwnProperty('time') ? overrides.time! : 'maiores',
  };
};

export const aSettlementSchedulePointInput = (
  overrides?: Partial<SettlementSchedulePointInput>,
): SettlementSchedulePointInput => {
  return {
    day: overrides && overrides.hasOwnProperty('day') ? overrides.day! : DayOfTheWeek.Friday,
    time: overrides && overrides.hasOwnProperty('time') ? overrides.time! : 'repellendus',
  };
};

export const aSettlementStreetCashTrade = (
  overrides?: Partial<SettlementStreetCashTrade>,
): { __typename: 'SettlementStreetCashTrade' } & SettlementStreetCashTrade => {
  return {
    __typename: 'SettlementStreetCashTrade',
    baseCurrency:
      overrides && overrides.hasOwnProperty('baseCurrency')
        ? overrides.baseCurrency!
        : 'consequuntur',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'quidem',
    dateTime:
      overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'consectetur',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'sed',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'suscipit',
    extOrderId:
      overrides && overrides.hasOwnProperty('extOrderId') ? overrides.extOrderId! : 'adipisci',
    fee: overrides && overrides.hasOwnProperty('fee') ? overrides.fee! : 3.5,
    feeCurrency:
      overrides && overrides.hasOwnProperty('feeCurrency') ? overrides.feeCurrency! : 'eos',
    intOrderId:
      overrides && overrides.hasOwnProperty('intOrderId') ? overrides.intOrderId! : 'ducimus',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'sunt',
    parentOrderId:
      overrides && overrides.hasOwnProperty('parentOrderId') ? overrides.parentOrderId! : 'eius',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'sapiente',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName') ? overrides.portfolioName! : 'amet',
    price: overrides && overrides.hasOwnProperty('price') ? overrides.price! : 9.62,
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 5.1,
    rootExecutionId:
      overrides && overrides.hasOwnProperty('rootExecutionId')
        ? overrides.rootExecutionId!
        : 'amet',
    rootOrderId:
      overrides && overrides.hasOwnProperty('rootOrderId') ? overrides.rootOrderId! : 'rerum',
    selected: overrides && overrides.hasOwnProperty('selected') ? overrides.selected! : true,
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : false,
    settledDateTime:
      overrides && overrides.hasOwnProperty('settledDateTime') ? overrides.settledDateTime! : 4.3,
    underlyingExecutionId:
      overrides && overrides.hasOwnProperty('underlyingExecutionId')
        ? overrides.underlyingExecutionId!
        : 'minima',
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'sequi',
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'ut',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'voluptas',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'quae',
  };
};

export const aSettlementTransaction = (
  overrides?: Partial<SettlementTransaction>,
): { __typename: 'SettlementTransaction' } & SettlementTransaction => {
  return {
    __typename: 'SettlementTransaction',
    selected: overrides && overrides.hasOwnProperty('selected') ? overrides.selected! : false,
  };
};

export const aSettlementTransactionConnection = (
  overrides?: Partial<SettlementTransactionConnection>,
): { __typename: 'SettlementTransactionConnection' } & SettlementTransactionConnection => {
  return {
    __typename: 'SettlementTransactionConnection',
    edges:
      overrides && overrides.hasOwnProperty('edges')
        ? overrides.edges!
        : [aSettlementTransactionEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aSettlementTransactionEdge = (
  overrides?: Partial<SettlementTransactionEdge>,
): { __typename: 'SettlementTransactionEdge' } & SettlementTransactionEdge => {
  return {
    __typename: 'SettlementTransactionEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'qui',
    node:
      overrides && overrides.hasOwnProperty('node')
        ? overrides.node!
        : aSettlementStreetCashTrade(),
  };
};

export const aSettlementTransactionSearchInput = (
  overrides?: Partial<SettlementTransactionSearchInput>,
): SettlementTransactionSearchInput => {
  return {
    accountId: overrides && overrides.hasOwnProperty('accountId') ? overrides.accountId! : ['quos'],
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'voluptatem',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : ['quo'],
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'alias',
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 8589,
    from: overrides && overrides.hasOwnProperty('from') ? overrides.from! : 'qui',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'earum',
    parentOrderId:
      overrides && overrides.hasOwnProperty('parentOrderId') ? overrides.parentOrderId! : 'eaque',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : ['tempora'],
    rootExecutionId:
      overrides && overrides.hasOwnProperty('rootExecutionId')
        ? overrides.rootExecutionId!
        : 'nostrum',
    rootOrderId:
      overrides && overrides.hasOwnProperty('rootOrderId') ? overrides.rootOrderId! : 'inventore',
    selected: overrides && overrides.hasOwnProperty('selected') ? overrides.selected! : true,
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : false,
    settlementRunId:
      overrides && overrides.hasOwnProperty('settlementRunId')
        ? overrides.settlementRunId!
        : 'expedita',
    sortingOrder:
      overrides && overrides.hasOwnProperty('sortingOrder')
        ? overrides.sortingOrder!
        : SortingOrder.Asc,
    to: overrides && overrides.hasOwnProperty('to') ? overrides.to! : 'vitae',
    underlyingExecutionId:
      overrides && overrides.hasOwnProperty('underlyingExecutionId')
        ? overrides.underlyingExecutionId!
        : 'porro',
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'animi',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'in',
  };
};

export const aSimplePredicateInput = (
  overrides?: Partial<SimplePredicateInput>,
): SimplePredicateInput => {
  return {
    field:
      overrides && overrides.hasOwnProperty('field')
        ? overrides.field!
        : OrderStateStringField.ClOrderId,
    method:
      overrides && overrides.hasOwnProperty('method')
        ? overrides.method!
        : SimplePredicateType.Contains,
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : 'qui',
  };
};

export const aSorTarget = (
  overrides?: Partial<SorTarget>,
): { __typename: 'SorTarget' } & SorTarget => {
  return {
    __typename: 'SorTarget',
    assetClass:
      overrides && overrides.hasOwnProperty('assetClass')
        ? overrides.assetClass!
        : AssetClass.Forex,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'fugit',
  };
};

export const aSorTargetInput = (overrides?: Partial<SorTargetInput>): SorTargetInput => {
  return {
    assetClass:
      overrides && overrides.hasOwnProperty('assetClass')
        ? overrides.assetClass!
        : AssetClass.Forex,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'corrupti',
  };
};

export const aSourceConfiguration = (
  overrides?: Partial<SourceConfiguration>,
): { __typename: 'SourceConfiguration' } & SourceConfiguration => {
  return {
    __typename: 'SourceConfiguration',
    conversionSourceInstrument:
      overrides && overrides.hasOwnProperty('conversionSourceInstrument')
        ? overrides.conversionSourceInstrument!
        : anInstrumentResponse(),
    inverse: overrides && overrides.hasOwnProperty('inverse') ? overrides.inverse! : true,
    sourceInstrument:
      overrides && overrides.hasOwnProperty('sourceInstrument')
        ? overrides.sourceInstrument!
        : anInstrumentResponse(),
  };
};

export const aSourceConfigurationInput = (
  overrides?: Partial<SourceConfigurationInput>,
): SourceConfigurationInput => {
  return {
    conversionSourceInstrumentId:
      overrides && overrides.hasOwnProperty('conversionSourceInstrumentId')
        ? overrides.conversionSourceInstrumentId!
        : 'quaerat',
    inverse: overrides && overrides.hasOwnProperty('inverse') ? overrides.inverse! : false,
    sourceInstrumentId:
      overrides && overrides.hasOwnProperty('sourceInstrumentId')
        ? overrides.sourceInstrumentId!
        : 'aut',
  };
};

export const aStreetAssetTrade = (
  overrides?: Partial<StreetAssetTrade>,
): { __typename: 'StreetAssetTrade' } & StreetAssetTrade => {
  return {
    __typename: 'StreetAssetTrade',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'enim',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'dolor',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'soluta',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'ut',
    extOrderId: overrides && overrides.hasOwnProperty('extOrderId') ? overrides.extOrderId! : 'sed',
    fee: overrides && overrides.hasOwnProperty('fee') ? overrides.fee! : 9.25,
    feeCurrency:
      overrides && overrides.hasOwnProperty('feeCurrency') ? overrides.feeCurrency! : 'sit',
    instrument:
      overrides && overrides.hasOwnProperty('instrument')
        ? overrides.instrument!
        : anInstrumentResponse(),
    intOrderId:
      overrides && overrides.hasOwnProperty('intOrderId') ? overrides.intOrderId! : 'autem',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'eum',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'et',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName') ? overrides.portfolioName! : 'natus',
    price: overrides && overrides.hasOwnProperty('price') ? overrides.price! : 4.37,
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 6.49,
    rootOrderId:
      overrides && overrides.hasOwnProperty('rootOrderId') ? overrides.rootOrderId! : 'omnis',
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : 'ea',
    settledDateTime:
      overrides && overrides.hasOwnProperty('settledDateTime') ? overrides.settledDateTime! : 4.92,
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 2.91,
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'velit',
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'fugit',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'qui',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'ducimus',
  };
};

export const aStreetCashTrade = (
  overrides?: Partial<StreetCashTrade>,
): { __typename: 'StreetCashTrade' } & StreetCashTrade => {
  return {
    __typename: 'StreetCashTrade',
    baseCurrency:
      overrides && overrides.hasOwnProperty('baseCurrency') ? overrides.baseCurrency! : 'excepturi',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'corporis',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'modi',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'molestias',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'ut',
    extOrderId:
      overrides && overrides.hasOwnProperty('extOrderId') ? overrides.extOrderId! : 'nostrum',
    fee: overrides && overrides.hasOwnProperty('fee') ? overrides.fee! : 7.54,
    feeCurrency:
      overrides && overrides.hasOwnProperty('feeCurrency') ? overrides.feeCurrency! : 'occaecati',
    intOrderId:
      overrides && overrides.hasOwnProperty('intOrderId') ? overrides.intOrderId! : 'odit',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'et',
    parentOrderId:
      overrides && overrides.hasOwnProperty('parentOrderId')
        ? overrides.parentOrderId!
        : 'voluptatem',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId')
        ? overrides.portfolioId!
        : 'exercitationem',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName') ? overrides.portfolioName! : 'nulla',
    price: overrides && overrides.hasOwnProperty('price') ? overrides.price! : 8.53,
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 8.99,
    rootExecution:
      overrides && overrides.hasOwnProperty('rootExecution')
        ? overrides.rootExecution!
        : aRootExecution(),
    rootOrderId:
      overrides && overrides.hasOwnProperty('rootOrderId') ? overrides.rootOrderId! : 'ut',
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : 'eaque',
    settledDateTime:
      overrides && overrides.hasOwnProperty('settledDateTime') ? overrides.settledDateTime! : 4.41,
    underlyingExecutionId:
      overrides && overrides.hasOwnProperty('underlyingExecutionId')
        ? overrides.underlyingExecutionId!
        : 'non',
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 1.22,
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'ut',
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'saepe',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'praesentium',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'quia',
  };
};

export const aSubscription = (
  overrides?: Partial<Subscription>,
): { __typename: 'Subscription' } & Subscription => {
  return {
    __typename: 'Subscription',
    cancelReject:
      overrides && overrides.hasOwnProperty('cancelReject')
        ? overrides.cancelReject!
        : aCancelRejectResponse(),
    connectorStates:
      overrides && overrides.hasOwnProperty('connectorStates')
        ? overrides.connectorStates!
        : aConnectorStatesResponse(),
    eventsLogs:
      overrides && overrides.hasOwnProperty('eventsLogs')
        ? overrides.eventsLogs!
        : anEventLogResponse(),
    executionReport:
      overrides && overrides.hasOwnProperty('executionReport')
        ? overrides.executionReport!
        : anExecutionReportResponse(),
    orderBook:
      overrides && overrides.hasOwnProperty('orderBook')
        ? overrides.orderBook!
        : anOrderBookResponse(),
    orderStatesWithSnapshot:
      overrides && overrides.hasOwnProperty('orderStatesWithSnapshot')
        ? overrides.orderStatesWithSnapshot!
        : anOrderStateResponse(),
    positionChanges:
      overrides && overrides.hasOwnProperty('positionChanges')
        ? overrides.positionChanges!
        : aPositionResponse(),
    settlementRuns:
      overrides && overrides.hasOwnProperty('settlementRuns')
        ? overrides.settlementRuns!
        : aSettlementRunResponse(),
    targetStates:
      overrides && overrides.hasOwnProperty('targetStates')
        ? overrides.targetStates!
        : aTargetStatesResponse(),
    tick: overrides && overrides.hasOwnProperty('tick') ? overrides.tick! : aMarketDataResponse(),
    transactionsWithSnapshot:
      overrides && overrides.hasOwnProperty('transactionsWithSnapshot')
        ? overrides.transactionsWithSnapshot!
        : anAccountCashTransfer(),
    transferStatesSubscription:
      overrides && overrides.hasOwnProperty('transferStatesSubscription')
        ? overrides.transferStatesSubscription!
        : aTransferStateResponse(),
  };
};

export const aSymbolPredicateInput = (
  overrides?: Partial<SymbolPredicateInput>,
): SymbolPredicateInput => {
  return {
    method:
      overrides && overrides.hasOwnProperty('method')
        ? overrides.method!
        : SymbolPredicateType.Contains,
    symbols: overrides && overrides.hasOwnProperty('symbols') ? overrides.symbols! : ['culpa'],
  };
};

export const aSymbolSearchConnection = (
  overrides?: Partial<SymbolSearchConnection>,
): { __typename: 'SymbolSearchConnection' } & SymbolSearchConnection => {
  return {
    __typename: 'SymbolSearchConnection',
    edges:
      overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [aSymbolSearchEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aSymbolSearchEdge = (
  overrides?: Partial<SymbolSearchEdge>,
): { __typename: 'SymbolSearchEdge' } & SymbolSearchEdge => {
  return {
    __typename: 'SymbolSearchEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'ratione',
    node: overrides && overrides.hasOwnProperty('node') ? overrides.node! : aSymbolSearchResponse(),
  };
};

export const aSymbolSearchInput = (overrides?: Partial<SymbolSearchInput>): SymbolSearchInput => {
  return {
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'corrupti',
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 8775,
    sortingOrder:
      overrides && overrides.hasOwnProperty('sortingOrder')
        ? overrides.sortingOrder!
        : SortingOrder.Asc,
    symbolPredicate:
      overrides && overrides.hasOwnProperty('symbolPredicate')
        ? overrides.symbolPredicate!
        : aSymbolPredicateInput(),
    venueNames:
      overrides && overrides.hasOwnProperty('venueNames') ? overrides.venueNames! : ['et'],
  };
};

export const aSymbolSearchResponse = (
  overrides?: Partial<SymbolSearchResponse>,
): { __typename: 'SymbolSearchResponse' } & SymbolSearchResponse => {
  return {
    __typename: 'SymbolSearchResponse',
    assetClass:
      overrides && overrides.hasOwnProperty('assetClass')
        ? overrides.assetClass!
        : AssetClass.Forex,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'fugit',
    venues:
      overrides && overrides.hasOwnProperty('venues')
        ? overrides.venues!
        : [aVenueAccountNamesPerVenue()],
  };
};

export const aTag = (overrides?: Partial<Tag>): { __typename: 'Tag' } & Tag => {
  return {
    __typename: 'Tag',
    key: overrides && overrides.hasOwnProperty('key') ? overrides.key! : 'et',
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : 'culpa',
  };
};

export const aTagInput = (overrides?: Partial<TagInput>): TagInput => {
  return {
    key: overrides && overrides.hasOwnProperty('key') ? overrides.key! : 'ut',
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : 'nostrum',
  };
};

export const aTargetCapabilityStatus = (
  overrides?: Partial<TargetCapabilityStatus>,
): { __typename: 'TargetCapabilityStatus' } & TargetCapabilityStatus => {
  return {
    __typename: 'TargetCapabilityStatus',
    capability:
      overrides && overrides.hasOwnProperty('capability')
        ? overrides.capability!
        : Capability.Account,
    healthStatus:
      overrides && overrides.hasOwnProperty('healthStatus')
        ? overrides.healthStatus!
        : HealthStatus.Alive,
    message: overrides && overrides.hasOwnProperty('message') ? overrides.message! : 'atque',
    timestamp: overrides && overrides.hasOwnProperty('timestamp') ? overrides.timestamp! : 'libero',
  };
};

export const aTargetStatesResponse = (
  overrides?: Partial<TargetStatesResponse>,
): { __typename: 'TargetStatesResponse' } & TargetStatesResponse => {
  return {
    __typename: 'TargetStatesResponse',
    capabilities:
      overrides && overrides.hasOwnProperty('capabilities')
        ? overrides.capabilities!
        : [aTargetCapabilityStatus()],
    target: overrides && overrides.hasOwnProperty('target') ? overrides.target! : 'cum',
    targetName:
      overrides && overrides.hasOwnProperty('targetName') ? overrides.targetName! : 'impedit',
  };
};

export const aThresholdConfiguration = (
  overrides?: Partial<ThresholdConfiguration>,
): { __typename: 'ThresholdConfiguration' } & ThresholdConfiguration => {
  return {
    __typename: 'ThresholdConfiguration',
    asset: overrides && overrides.hasOwnProperty('asset') ? overrides.asset! : 'nisi',
    hedgeInstrument:
      overrides && overrides.hasOwnProperty('hedgeInstrument')
        ? overrides.hedgeInstrument!
        : anInstrumentResponse(),
    highThreshold:
      overrides && overrides.hasOwnProperty('highThreshold') ? overrides.highThreshold! : 'ipsam',
    lowThreshold:
      overrides && overrides.hasOwnProperty('lowThreshold') ? overrides.lowThreshold! : 'aut',
    targetExposure:
      overrides && overrides.hasOwnProperty('targetExposure')
        ? overrides.targetExposure!
        : 'maiores',
  };
};

export const aThresholdConfigurationInput = (
  overrides?: Partial<ThresholdConfigurationInput>,
): ThresholdConfigurationInput => {
  return {
    asset: overrides && overrides.hasOwnProperty('asset') ? overrides.asset! : 'ducimus',
    hedgeInstrumentId:
      overrides && overrides.hasOwnProperty('hedgeInstrumentId')
        ? overrides.hedgeInstrumentId!
        : 'nulla',
    highThreshold:
      overrides && overrides.hasOwnProperty('highThreshold')
        ? overrides.highThreshold!
        : 'occaecati',
    lowThreshold:
      overrides && overrides.hasOwnProperty('lowThreshold') ? overrides.lowThreshold! : 'maiores',
    targetExposure:
      overrides && overrides.hasOwnProperty('targetExposure') ? overrides.targetExposure! : 'aut',
  };
};

export const aTifsPerOrderType = (
  overrides?: Partial<TifsPerOrderType>,
): { __typename: 'TifsPerOrderType' } & TifsPerOrderType => {
  return {
    __typename: 'TifsPerOrderType',
    key: overrides && overrides.hasOwnProperty('key') ? overrides.key! : OrderType.Limit,
    values: overrides && overrides.hasOwnProperty('values') ? overrides.values! : [Tif.Day],
  };
};

export const aTimelineConnection = (
  overrides?: Partial<TimelineConnection>,
): { __typename: 'TimelineConnection' } & TimelineConnection => {
  return {
    __typename: 'TimelineConnection',
    edges: overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [aTimelineEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aTimelineEdge = (
  overrides?: Partial<TimelineEdge>,
): { __typename: 'TimelineEdge' } & TimelineEdge => {
  return {
    __typename: 'TimelineEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'est',
    node: overrides && overrides.hasOwnProperty('node') ? overrides.node! : aTimelineResult(),
  };
};

export const aTimelineResult = (
  overrides?: Partial<TimelineResult>,
): { __typename: 'TimelineResult' } & TimelineResult => {
  return {
    __typename: 'TimelineResult',
    data: overrides && overrides.hasOwnProperty('data') ? overrides.data! : aHedgeResult(),
    type:
      overrides && overrides.hasOwnProperty('type')
        ? overrides.type!
        : TimelineEventType.HedgeResult,
  };
};

export const aTimelineSearchInput = (
  overrides?: Partial<TimelineSearchInput>,
): TimelineSearchInput => {
  return {
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'labore',
    eventType:
      overrides && overrides.hasOwnProperty('eventType')
        ? overrides.eventType!
        : [TimelineEventType.HedgeResult],
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 4073,
    includeRelated:
      overrides && overrides.hasOwnProperty('includeRelated') ? overrides.includeRelated! : true,
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'expedita',
    rootOrderId:
      overrides && overrides.hasOwnProperty('rootOrderId') ? overrides.rootOrderId! : 'autem',
    sortingOrder:
      overrides && overrides.hasOwnProperty('sortingOrder')
        ? overrides.sortingOrder!
        : SortingOrder.Asc,
  };
};

export const aTrade = (overrides?: Partial<Trade>): { __typename: 'Trade' } & Trade => {
  return {
    __typename: 'Trade',
    currency:
      overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'recusandae',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'beatae',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'vel',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'aut',
    extOrderId:
      overrides && overrides.hasOwnProperty('extOrderId') ? overrides.extOrderId! : 'ullam',
    fee: overrides && overrides.hasOwnProperty('fee') ? overrides.fee! : 1.18,
    feeCurrency:
      overrides && overrides.hasOwnProperty('feeCurrency') ? overrides.feeCurrency! : 'quos',
    intOrderId:
      overrides && overrides.hasOwnProperty('intOrderId') ? overrides.intOrderId! : 'deleniti',
    price: overrides && overrides.hasOwnProperty('price') ? overrides.price! : 2.97,
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 9.63,
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : 'qui',
    settledDateTime:
      overrides && overrides.hasOwnProperty('settledDateTime') ? overrides.settledDateTime! : 4.4,
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 6.33,
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'vel',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'et',
  };
};

export const aTradingCapabilities = (
  overrides?: Partial<TradingCapabilities>,
): { __typename: 'TradingCapabilities' } & TradingCapabilities => {
  return {
    __typename: 'TradingCapabilities',
    accountingLongAndShortPositionsSeparately:
      overrides && overrides.hasOwnProperty('accountingLongAndShortPositionsSeparately')
        ? overrides.accountingLongAndShortPositionsSeparately!
        : false,
    allowingMultipleRFQsForTheSameSecurity:
      overrides && overrides.hasOwnProperty('allowingMultipleRFQsForTheSameSecurity')
        ? overrides.allowingMultipleRFQsForTheSameSecurity!
        : true,
    allowingMultipleRFQsOnAdapterLevel:
      overrides && overrides.hasOwnProperty('allowingMultipleRFQsOnAdapterLevel')
        ? overrides.allowingMultipleRFQsOnAdapterLevel!
        : false,
    allowingOrdersWhenRFQIsOngoingForTheSameSecurity:
      overrides && overrides.hasOwnProperty('allowingOrdersWhenRFQIsOngoingForTheSameSecurity')
        ? overrides.allowingOrdersWhenRFQIsOngoingForTheSameSecurity!
        : true,
    defaultTIFs:
      overrides && overrides.hasOwnProperty('defaultTIFs')
        ? overrides.defaultTIFs!
        : [aDefaultTif()],
    exchangeTradingSupported:
      overrides && overrides.hasOwnProperty('exchangeTradingSupported')
        ? overrides.exchangeTradingSupported!
        : true,
    marginTradingSupported:
      overrides && overrides.hasOwnProperty('marginTradingSupported')
        ? overrides.marginTradingSupported!
        : true,
    postOnlyOrderSupportedTypesAndTIFs:
      overrides && overrides.hasOwnProperty('postOnlyOrderSupportedTypesAndTIFs')
        ? overrides.postOnlyOrderSupportedTypesAndTIFs!
        : [aTifsPerOrderType()],
    rfqcancelSupported:
      overrides && overrides.hasOwnProperty('rfqcancelSupported')
        ? overrides.rfqcancelSupported!
        : true,
    rfqstreamingSupported:
      overrides && overrides.hasOwnProperty('rfqstreamingSupported')
        ? overrides.rfqstreamingSupported!
        : true,
    rfqsupported:
      overrides && overrides.hasOwnProperty('rfqsupported') ? overrides.rfqsupported! : true,
    supportedOrderRequests:
      overrides && overrides.hasOwnProperty('supportedOrderRequests')
        ? overrides.supportedOrderRequests!
        : [anOrderRequestsPerOrderType()],
    supportedOrderTypes:
      overrides && overrides.hasOwnProperty('supportedOrderTypes')
        ? overrides.supportedOrderTypes!
        : [OrderType.Limit],
    supportedSecurityTypes:
      overrides && overrides.hasOwnProperty('supportedSecurityTypes')
        ? overrides.supportedSecurityTypes!
        : [InstrumentType.Bond],
    supportedTIFs:
      overrides && overrides.hasOwnProperty('supportedTIFs')
        ? overrides.supportedTIFs!
        : [aTifsPerOrderType()],
    supportingCustomizedOrders:
      overrides && overrides.hasOwnProperty('supportingCustomizedOrders')
        ? overrides.supportingCustomizedOrders!
        : false,
    supportingFixLiteralProperties:
      overrides && overrides.hasOwnProperty('supportingFixLiteralProperties')
        ? overrides.supportingFixLiteralProperties!
        : false,
  };
};

export const aTradingConstraintsResponse = (
  overrides?: Partial<TradingConstraintsResponse>,
): { __typename: 'TradingConstraintsResponse' } & TradingConstraintsResponse => {
  return {
    __typename: 'TradingConstraintsResponse',
    contractSize:
      overrides && overrides.hasOwnProperty('contractSize') ? overrides.contractSize! : 'est',
    maxPrice: overrides && overrides.hasOwnProperty('maxPrice') ? overrides.maxPrice! : 'magnam',
    maxQty: overrides && overrides.hasOwnProperty('maxQty') ? overrides.maxQty! : 'magni',
    maxQuoteQty:
      overrides && overrides.hasOwnProperty('maxQuoteQty') ? overrides.maxQuoteQty! : 'quia',
    minNotional:
      overrides && overrides.hasOwnProperty('minNotional') ? overrides.minNotional! : 'est',
    minPrice: overrides && overrides.hasOwnProperty('minPrice') ? overrides.minPrice! : 'atque',
    minQty: overrides && overrides.hasOwnProperty('minQty') ? overrides.minQty! : 'saepe',
    minQuoteQty:
      overrides && overrides.hasOwnProperty('minQuoteQty') ? overrides.minQuoteQty! : 'debitis',
    priceIncr: overrides && overrides.hasOwnProperty('priceIncr') ? overrides.priceIncr! : 'sequi',
    qtyIncr: overrides && overrides.hasOwnProperty('qtyIncr') ? overrides.qtyIncr! : 'corporis',
    quoteQtyIncr:
      overrides && overrides.hasOwnProperty('quoteQtyIncr') ? overrides.quoteQtyIncr! : 'quaerat',
    tradeable: overrides && overrides.hasOwnProperty('tradeable') ? overrides.tradeable! : true,
  };
};

export const aTransactionConnection = (
  overrides?: Partial<TransactionConnection>,
): { __typename: 'TransactionConnection' } & TransactionConnection => {
  return {
    __typename: 'TransactionConnection',
    edges: overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [aTransactionEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aTransactionEdge = (
  overrides?: Partial<TransactionEdge>,
): { __typename: 'TransactionEdge' } & TransactionEdge => {
  return {
    __typename: 'TransactionEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'dolor',
    node: overrides && overrides.hasOwnProperty('node') ? overrides.node! : anAccountCashTransfer(),
  };
};

export const aTransactionInput = (overrides?: Partial<TransactionInput>): TransactionInput => {
  return {
    conversionRates:
      overrides && overrides.hasOwnProperty('conversionRates')
        ? overrides.conversionRates!
        : [aConversionRate()],
    counterPortfolioId:
      overrides && overrides.hasOwnProperty('counterPortfolioId')
        ? overrides.counterPortfolioId!
        : 'magnam',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'quia',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'numquam',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'possimus',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'enim',
    externalOrderId:
      overrides && overrides.hasOwnProperty('externalOrderId')
        ? overrides.externalOrderId!
        : 'accusantium',
    fee: overrides && overrides.hasOwnProperty('fee') ? overrides.fee! : 'laudantium',
    feeAccountId:
      overrides && overrides.hasOwnProperty('feeAccountId') ? overrides.feeAccountId! : 'est',
    feePortfolioId:
      overrides && overrides.hasOwnProperty('feePortfolioId')
        ? overrides.feePortfolioId!
        : 'inventore',
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId')
        ? overrides.instrumentId!
        : 'repellendus',
    isSettled: overrides && overrides.hasOwnProperty('isSettled') ? overrides.isSettled! : true,
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'commodi',
    price: overrides && overrides.hasOwnProperty('price') ? overrides.price! : 'deserunt',
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 'occaecati',
    settlementDate:
      overrides && overrides.hasOwnProperty('settlementDate') ? overrides.settlementDate! : 'vitae',
    sourceAccountId:
      overrides && overrides.hasOwnProperty('sourceAccountId') ? overrides.sourceAccountId! : 'et',
    sourcePortfolioId:
      overrides && overrides.hasOwnProperty('sourcePortfolioId')
        ? overrides.sourcePortfolioId!
        : 'qui',
    type:
      overrides && overrides.hasOwnProperty('type')
        ? overrides.type!
        : TransactionTypeInput.Deposit,
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId')
        ? overrides.venueAccountId!
        : 'perferendis',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'est',
  };
};

export const aTransactionSearchInput = (
  overrides?: Partial<TransactionSearchInput>,
): TransactionSearchInput => {
  return {
    accountId: overrides && overrides.hasOwnProperty('accountId') ? overrides.accountId! : ['sed'],
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'ad',
    currency:
      overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : ['accusantium'],
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'veritatis',
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 1226,
    from: overrides && overrides.hasOwnProperty('from') ? overrides.from! : 'officia',
    orderId: overrides && overrides.hasOwnProperty('orderId') ? overrides.orderId! : 'quaerat',
    parentOrderId:
      overrides && overrides.hasOwnProperty('parentOrderId')
        ? overrides.parentOrderId!
        : 'dignissimos',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : ['inventore'],
    rootExecutionId:
      overrides && overrides.hasOwnProperty('rootExecutionId') ? overrides.rootExecutionId! : 'est',
    rootOrderId:
      overrides && overrides.hasOwnProperty('rootOrderId') ? overrides.rootOrderId! : 'quia',
    sortingOrder:
      overrides && overrides.hasOwnProperty('sortingOrder')
        ? overrides.sortingOrder!
        : SortingOrder.Asc,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : ['laboriosam'],
    to: overrides && overrides.hasOwnProperty('to') ? overrides.to! : 'temporibus',
    transactionType:
      overrides && overrides.hasOwnProperty('transactionType')
        ? overrides.transactionType!
        : [TransactionType.AccountCashTransfer],
    underlyingExecutionId:
      overrides && overrides.hasOwnProperty('underlyingExecutionId')
        ? overrides.underlyingExecutionId!
        : 'et',
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'et',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'accusantium',
  };
};

export const aTransactionSelect = (overrides?: Partial<TransactionSelect>): TransactionSelect => {
  return {
    selected: overrides && overrides.hasOwnProperty('selected') ? overrides.selected! : true,
    transactionId:
      overrides && overrides.hasOwnProperty('transactionId')
        ? overrides.transactionId!
        : 'corporis',
  };
};

export const aTransferInput = (overrides?: Partial<TransferInput>): TransferInput => {
  return {
    asset: overrides && overrides.hasOwnProperty('asset') ? overrides.asset! : 'rerum',
    destination:
      overrides && overrides.hasOwnProperty('destination') ? overrides.destination! : 'voluptatem',
    notes: overrides && overrides.hasOwnProperty('notes') ? overrides.notes! : 'necessitatibus',
    priority:
      overrides && overrides.hasOwnProperty('priority') ? overrides.priority! : Priority.High,
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 'accusamus',
    source: overrides && overrides.hasOwnProperty('source') ? overrides.source! : 'assumenda',
  };
};

export const aTransferStateConnection = (
  overrides?: Partial<TransferStateConnection>,
): { __typename: 'TransferStateConnection' } & TransferStateConnection => {
  return {
    __typename: 'TransferStateConnection',
    edges:
      overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [aTransferStateEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aTransferStateDateTimePredicateInput = (
  overrides?: Partial<TransferStateDateTimePredicateInput>,
): TransferStateDateTimePredicateInput => {
  return {
    field:
      overrides && overrides.hasOwnProperty('field')
        ? overrides.field!
        : TransferStateDateTimePredicateField.EffectiveDatetime,
    method:
      overrides && overrides.hasOwnProperty('method')
        ? overrides.method!
        : TransferStateDateTimePredicateType.Equal,
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : ['et'],
  };
};

export const aTransferStateEdge = (
  overrides?: Partial<TransferStateEdge>,
): { __typename: 'TransferStateEdge' } & TransferStateEdge => {
  return {
    __typename: 'TransferStateEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'occaecati',
    node:
      overrides && overrides.hasOwnProperty('node') ? overrides.node! : aTransferStateResponse(),
  };
};

export const aTransferStateNumberPredicateInput = (
  overrides?: Partial<TransferStateNumberPredicateInput>,
): TransferStateNumberPredicateInput => {
  return {
    field:
      overrides && overrides.hasOwnProperty('field')
        ? overrides.field!
        : TransferStateNumberPredicateField.Amount,
    method:
      overrides && overrides.hasOwnProperty('method')
        ? overrides.method!
        : TransferStateNumberPredicateType.Equal,
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : ['alias'],
  };
};

export const aTransferStatePredicateInput = (
  overrides?: Partial<TransferStatePredicateInput>,
): TransferStatePredicateInput => {
  return {
    field:
      overrides && overrides.hasOwnProperty('field')
        ? overrides.field!
        : TransferStateCollectionPredicateField.Asset,
    method:
      overrides && overrides.hasOwnProperty('method')
        ? overrides.method!
        : TransferStateCollectionPredicateType.In,
    value: overrides && overrides.hasOwnProperty('value') ? overrides.value! : ['est'],
  };
};

export const aTransferStateResponse = (
  overrides?: Partial<TransferStateResponse>,
): { __typename: 'TransferStateResponse' } & TransferStateResponse => {
  return {
    __typename: 'TransferStateResponse',
    asset: overrides && overrides.hasOwnProperty('asset') ? overrides.asset! : 'culpa',
    custodyTrx:
      overrides && overrides.hasOwnProperty('custodyTrx') ? overrides.custodyTrx! : 'dolorem',
    destination:
      overrides && overrides.hasOwnProperty('destination') ? overrides.destination! : 'fugit',
    effectiveDateTime:
      overrides && overrides.hasOwnProperty('effectiveDateTime')
        ? overrides.effectiveDateTime!
        : 'voluptas',
    fee: overrides && overrides.hasOwnProperty('fee') ? overrides.fee! : 'dolorum',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'rerum',
    initiateDateTime:
      overrides && overrides.hasOwnProperty('initiateDateTime')
        ? overrides.initiateDateTime!
        : 'sint',
    initiator: overrides && overrides.hasOwnProperty('initiator') ? overrides.initiator! : 'atque',
    internalId: overrides && overrides.hasOwnProperty('internalId') ? overrides.internalId! : 'id',
    lastUpdateDateTime:
      overrides && overrides.hasOwnProperty('lastUpdateDateTime')
        ? overrides.lastUpdateDateTime!
        : 'nesciunt',
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 'in',
    source: overrides && overrides.hasOwnProperty('source') ? overrides.source! : 'quo',
    status:
      overrides && overrides.hasOwnProperty('status')
        ? overrides.status!
        : TransferStatus.Broadcast,
    trxHash: overrides && overrides.hasOwnProperty('trxHash') ? overrides.trxHash! : 'doloribus',
  };
};

export const aTransferStateSearchInput = (
  overrides?: Partial<TransferStateSearchInput>,
): TransferStateSearchInput => {
  return {
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'sint',
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 8971,
    sortingOrder:
      overrides && overrides.hasOwnProperty('sortingOrder')
        ? overrides.sortingOrder!
        : SortingOrder.Asc,
    transferStateDateTimePredicateInput:
      overrides && overrides.hasOwnProperty('transferStateDateTimePredicateInput')
        ? overrides.transferStateDateTimePredicateInput!
        : [aTransferStateDateTimePredicateInput()],
    transferStateNumberPredicateInput:
      overrides && overrides.hasOwnProperty('transferStateNumberPredicateInput')
        ? overrides.transferStateNumberPredicateInput!
        : [aTransferStateNumberPredicateInput()],
    transferStatePredicateInput:
      overrides && overrides.hasOwnProperty('transferStatePredicateInput')
        ? overrides.transferStatePredicateInput!
        : [aTransferStatePredicateInput()],
  };
};

export const aTreasuryThreshold = (
  overrides?: Partial<TreasuryThreshold>,
): { __typename: 'TreasuryThreshold' } & TreasuryThreshold => {
  return {
    __typename: 'TreasuryThreshold',
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'maxime',
    maximum: overrides && overrides.hasOwnProperty('maximum') ? overrides.maximum! : 'ab',
    minimum: overrides && overrides.hasOwnProperty('minimum') ? overrides.minimum! : 'dolores',
    target: overrides && overrides.hasOwnProperty('target') ? overrides.target! : 'est',
  };
};

export const aTreasuryThresholdInput = (
  overrides?: Partial<TreasuryThresholdInput>,
): TreasuryThresholdInput => {
  return {
    currency: overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'vel',
    maximum: overrides && overrides.hasOwnProperty('maximum') ? overrides.maximum! : 'vel',
    minimum: overrides && overrides.hasOwnProperty('minimum') ? overrides.minimum! : 'perspiciatis',
    target: overrides && overrides.hasOwnProperty('target') ? overrides.target! : 'reprehenderit',
  };
};

export const anUpdateApiKeyNameInput = (
  overrides?: Partial<UpdateApiKeyNameInput>,
): UpdateApiKeyNameInput => {
  return {
    apiKeyId: overrides && overrides.hasOwnProperty('apiKeyId') ? overrides.apiKeyId! : 'est',
    apiKeyName:
      overrides && overrides.hasOwnProperty('apiKeyName') ? overrides.apiKeyName! : 'commodi',
  };
};

export const anUpdateBaseInstrumentInput = (
  overrides?: Partial<UpdateBaseInstrumentInput>,
): UpdateBaseInstrumentInput => {
  return {
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'voluptas',
    inverseContract:
      overrides && overrides.hasOwnProperty('inverseContract') ? overrides.inverseContract! : true,
    symbol: overrides && overrides.hasOwnProperty('symbol') ? overrides.symbol! : 'ut',
  };
};

export const anUpdateConversionSourceInput = (
  overrides?: Partial<UpdateConversionSourceInput>,
): UpdateConversionSourceInput => {
  return {
    priority: overrides && overrides.hasOwnProperty('priority') ? overrides.priority! : 6516,
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'aut',
  };
};

export const anUpdateInstrumentIdentifiersInput = (
  overrides?: Partial<UpdateInstrumentIdentifiersInput>,
): UpdateInstrumentIdentifiersInput => {
  return {
    adapterTicker:
      overrides && overrides.hasOwnProperty('adapterTicker') ? overrides.adapterTicker! : 'vel',
    tradingViewId:
      overrides && overrides.hasOwnProperty('tradingViewId') ? overrides.tradingViewId! : 'nemo',
    venueTradingViewId:
      overrides && overrides.hasOwnProperty('venueTradingViewId')
        ? overrides.venueTradingViewId!
        : 'vel',
  };
};

export const anUpdateInstrumentInput = (
  overrides?: Partial<UpdateInstrumentInput>,
): UpdateInstrumentInput => {
  return {
    archived: overrides && overrides.hasOwnProperty('archived') ? overrides.archived! : false,
    baseInstrument:
      overrides && overrides.hasOwnProperty('baseInstrument')
        ? overrides.baseInstrument!
        : anUpdateBaseInstrumentInput(),
    correlationObject:
      overrides && overrides.hasOwnProperty('correlationObject')
        ? overrides.correlationObject!
        : 'provident',
    instrumentId:
      overrides && overrides.hasOwnProperty('instrumentId') ? overrides.instrumentId! : 'quo',
    instrumentIdentifiers:
      overrides && overrides.hasOwnProperty('instrumentIdentifiers')
        ? overrides.instrumentIdentifiers!
        : anUpdateInstrumentIdentifiersInput(),
    tradingConstraints:
      overrides && overrides.hasOwnProperty('tradingConstraints')
        ? overrides.tradingConstraints!
        : anUpdateTradingConstraintsInput(),
  };
};

export const anUpdateInstrumentResult = (
  overrides?: Partial<UpdateInstrumentResult>,
): { __typename: 'UpdateInstrumentResult' } & UpdateInstrumentResult => {
  return {
    __typename: 'UpdateInstrumentResult',
    status: overrides && overrides.hasOwnProperty('status') ? overrides.status! : 'tempora',
  };
};

export const anUpdatePortfolioInput = (
  overrides?: Partial<UpdatePortfolioInput>,
): UpdatePortfolioInput => {
  return {
    archived: overrides && overrides.hasOwnProperty('archived') ? overrides.archived! : false,
    correlationObject:
      overrides && overrides.hasOwnProperty('correlationObject')
        ? overrides.correlationObject!
        : 'ex',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'sit',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'odio',
    portfolioType:
      overrides && overrides.hasOwnProperty('portfolioType')
        ? overrides.portfolioType!
        : PortfolioType.Nostro,
    tags: overrides && overrides.hasOwnProperty('tags') ? overrides.tags! : [aTagInput()],
  };
};

export const anUpdateTradingConstraintsInput = (
  overrides?: Partial<UpdateTradingConstraintsInput>,
): UpdateTradingConstraintsInput => {
  return {
    contractSize:
      overrides && overrides.hasOwnProperty('contractSize') ? overrides.contractSize! : 'vel',
    maxPrice: overrides && overrides.hasOwnProperty('maxPrice') ? overrides.maxPrice! : 'similique',
    maxQty: overrides && overrides.hasOwnProperty('maxQty') ? overrides.maxQty! : 'qui',
    maxQuoteQty:
      overrides && overrides.hasOwnProperty('maxQuoteQty') ? overrides.maxQuoteQty! : 'aliquid',
    minNotional:
      overrides && overrides.hasOwnProperty('minNotional') ? overrides.minNotional! : 'rerum',
    minPrice: overrides && overrides.hasOwnProperty('minPrice') ? overrides.minPrice! : 'autem',
    minQty: overrides && overrides.hasOwnProperty('minQty') ? overrides.minQty! : 'maiores',
    minQuoteQty:
      overrides && overrides.hasOwnProperty('minQuoteQty') ? overrides.minQuoteQty! : 'sed',
    priceIncr: overrides && overrides.hasOwnProperty('priceIncr') ? overrides.priceIncr! : 'libero',
    qtyIncr: overrides && overrides.hasOwnProperty('qtyIncr') ? overrides.qtyIncr! : 'voluptatem',
    quoteQtyIncr:
      overrides && overrides.hasOwnProperty('quoteQtyIncr')
        ? overrides.quoteQtyIncr!
        : 'reiciendis',
    tradeable: overrides && overrides.hasOwnProperty('tradeable') ? overrides.tradeable! : false,
  };
};

export const anUpdateUserDataInput = (
  overrides?: Partial<UpdateUserDataInput>,
): UpdateUserDataInput => {
  return {
    data: overrides && overrides.hasOwnProperty('data') ? overrides.data! : 'architecto',
  };
};

export const anUpdateVenueAccountInput = (
  overrides?: Partial<UpdateVenueAccountInput>,
): UpdateVenueAccountInput => {
  return {
    archived: overrides && overrides.hasOwnProperty('archived') ? overrides.archived! : true,
    keyValues:
      overrides && overrides.hasOwnProperty('keyValues')
        ? overrides.keyValues!
        : [aKeyValueInput()],
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId') ? overrides.venueAccountId! : 'vel',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'porro',
  };
};

export const aUserDataResponse = (
  overrides?: Partial<UserDataResponse>,
): { __typename: 'UserDataResponse' } & UserDataResponse => {
  return {
    __typename: 'UserDataResponse',
    data: overrides && overrides.hasOwnProperty('data') ? overrides.data! : 'aliquam',
  };
};

export const aUserResponse = (
  overrides?: Partial<UserResponse>,
): { __typename: 'UserResponse' } & UserResponse => {
  return {
    __typename: 'UserResponse',
    groups:
      overrides && overrides.hasOwnProperty('groups') ? overrides.groups! : [aGroupResponse()],
    permissions:
      overrides && overrides.hasOwnProperty('permissions')
        ? overrides.permissions!
        : [aPermission()],
    username: overrides && overrides.hasOwnProperty('username') ? overrides.username! : 'animi',
  };
};

export const aUsersPermissionsForResourceResponse = (
  overrides?: Partial<UsersPermissionsForResourceResponse>,
): { __typename: 'UsersPermissionsForResourceResponse' } & UsersPermissionsForResourceResponse => {
  return {
    __typename: 'UsersPermissionsForResourceResponse',
    scopes: overrides && overrides.hasOwnProperty('scopes') ? overrides.scopes! : [Scope.Create],
    username: overrides && overrides.hasOwnProperty('username') ? overrides.username! : 'ullam',
  };
};

export const aValidationError = (
  overrides?: Partial<ValidationError>,
): { __typename: 'ValidationError' } & ValidationError => {
  return {
    __typename: 'ValidationError',
    field: overrides && overrides.hasOwnProperty('field') ? overrides.field! : 'rerum',
    message: overrides && overrides.hasOwnProperty('message') ? overrides.message! : 'eius',
  };
};

export const aValidationResponse = (
  overrides?: Partial<ValidationResponse>,
): { __typename: 'ValidationResponse' } & ValidationResponse => {
  return {
    __typename: 'ValidationResponse',
    errors:
      overrides && overrides.hasOwnProperty('errors') ? overrides.errors! : [aValidationError()],
    isValid: overrides && overrides.hasOwnProperty('isValid') ? overrides.isValid! : false,
  };
};

export const aVenue = (overrides?: Partial<Venue>): { __typename: 'Venue' } & Venue => {
  return {
    __typename: 'Venue',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'sunt',
    venueType:
      overrides && overrides.hasOwnProperty('venueType') ? overrides.venueType! : VenueType.Client,
  };
};

export const aVenueAccount = (
  overrides?: Partial<VenueAccount>,
): { __typename: 'VenueAccount' } & VenueAccount => {
  return {
    __typename: 'VenueAccount',
    archivedAt:
      overrides && overrides.hasOwnProperty('archivedAt') ? overrides.archivedAt! : 'dolore',
    createdAt: overrides && overrides.hasOwnProperty('createdAt') ? overrides.createdAt! : 'nihil',
    deactivatedAt:
      overrides && overrides.hasOwnProperty('deactivatedAt') ? overrides.deactivatedAt! : 'ea',
    dynamicScopes:
      overrides && overrides.hasOwnProperty('dynamicScopes')
        ? overrides.dynamicScopes!
        : [Scope.Create],
    scopes: overrides && overrides.hasOwnProperty('scopes') ? overrides.scopes! : [Scope.Create],
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId') ? overrides.venueAccountId! : 'ullam',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'dolorem',
  };
};

export const aVenueAccountConnection = (
  overrides?: Partial<VenueAccountConnection>,
): { __typename: 'VenueAccountConnection' } & VenueAccountConnection => {
  return {
    __typename: 'VenueAccountConnection',
    edges:
      overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [aVenueAccountEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aVenueAccountDesc = (
  overrides?: Partial<VenueAccountDesc>,
): { __typename: 'VenueAccountDesc' } & VenueAccountDesc => {
  return {
    __typename: 'VenueAccountDesc',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'assumenda',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'ex',
  };
};

export const aVenueAccountDetailsInput = (
  overrides?: Partial<VenueAccountDetailsInput>,
): VenueAccountDetailsInput => {
  return {
    venueAccount:
      overrides && overrides.hasOwnProperty('venueAccount') ? overrides.venueAccount! : 'dolorem',
  };
};

export const aVenueAccountDetailsResponse = (
  overrides?: Partial<VenueAccountDetailsResponse>,
): { __typename: 'VenueAccountDetailsResponse' } & VenueAccountDetailsResponse => {
  return {
    __typename: 'VenueAccountDetailsResponse',
    dynamicScopes:
      overrides && overrides.hasOwnProperty('dynamicScopes')
        ? overrides.dynamicScopes!
        : [Scope.Create],
    keyValues:
      overrides && overrides.hasOwnProperty('keyValues') ? overrides.keyValues! : [aKeyValue()],
    scopes: overrides && overrides.hasOwnProperty('scopes') ? overrides.scopes! : [Scope.Create],
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId')
        ? overrides.venueAccountId!
        : 'corporis',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'nihil',
    venueName:
      overrides && overrides.hasOwnProperty('venueName') ? overrides.venueName! : 'molestiae',
  };
};

export const aVenueAccountEdge = (
  overrides?: Partial<VenueAccountEdge>,
): { __typename: 'VenueAccountEdge' } & VenueAccountEdge => {
  return {
    __typename: 'VenueAccountEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'dignissimos',
    node: overrides && overrides.hasOwnProperty('node') ? overrides.node! : aVenueAccountResponse(),
  };
};

export const aVenueAccountInput = (overrides?: Partial<VenueAccountInput>): VenueAccountInput => {
  return {
    connectorId:
      overrides && overrides.hasOwnProperty('connectorId') ? overrides.connectorId! : 'aspernatur',
    correlationObject:
      overrides && overrides.hasOwnProperty('correlationObject')
        ? overrides.correlationObject!
        : 'ut',
    integrated: overrides && overrides.hasOwnProperty('integrated') ? overrides.integrated! : false,
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId') ? overrides.venueAccountId! : 'aut',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'quod',
    venueAccountType:
      overrides && overrides.hasOwnProperty('venueAccountType')
        ? overrides.venueAccountType!
        : VenueAccountType.Clob,
    vostroNostro:
      overrides && overrides.hasOwnProperty('vostroNostro')
        ? overrides.vostroNostro!
        : VostroNostro.Nostro,
  };
};

export const aVenueAccountNamesPerVenue = (
  overrides?: Partial<VenueAccountNamesPerVenue>,
): { __typename: 'VenueAccountNamesPerVenue' } & VenueAccountNamesPerVenue => {
  return {
    __typename: 'VenueAccountNamesPerVenue',
    instrument:
      overrides && overrides.hasOwnProperty('instrument')
        ? overrides.instrument!
        : anInstrumentResponse(),
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'non',
    venueAccountDescs:
      overrides && overrides.hasOwnProperty('venueAccountDescs')
        ? overrides.venueAccountDescs!
        : [aVenueAccountDesc()],
  };
};

export const aVenueAccountPosition = (
  overrides?: Partial<VenueAccountPosition>,
): { __typename: 'VenueAccountPosition' } & VenueAccountPosition => {
  return {
    __typename: 'VenueAccountPosition',
    asset: overrides && overrides.hasOwnProperty('asset') ? overrides.asset! : 'ut',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'quod',
    metadata:
      overrides && overrides.hasOwnProperty('metadata') ? overrides.metadata! : [aKeyValue()],
    quantity:
      overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 'praesentium',
  };
};

export const aVenueAccountPositionInput = (
  overrides?: Partial<VenueAccountPositionInput>,
): VenueAccountPositionInput => {
  return {
    asset: overrides && overrides.hasOwnProperty('asset') ? overrides.asset! : 'unde',
    connectorId:
      overrides && overrides.hasOwnProperty('connectorId') ? overrides.connectorId! : 'voluptatem',
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'illum',
    metadata:
      overrides && overrides.hasOwnProperty('metadata') ? overrides.metadata! : [aKeyValueInput()],
  };
};

export const aVenueAccountPositionsResponse = (
  overrides?: Partial<VenueAccountPositionsResponse>,
): { __typename: 'VenueAccountPositionsResponse' } & VenueAccountPositionsResponse => {
  return {
    __typename: 'VenueAccountPositionsResponse',
    positions:
      overrides && overrides.hasOwnProperty('positions')
        ? overrides.positions!
        : [aVenueAccountPosition()],
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId') ? overrides.venueAccountId! : 'sint',
  };
};

export const aVenueAccountResponse = (
  overrides?: Partial<VenueAccountResponse>,
): { __typename: 'VenueAccountResponse' } & VenueAccountResponse => {
  return {
    __typename: 'VenueAccountResponse',
    accountType:
      overrides && overrides.hasOwnProperty('accountType')
        ? overrides.accountType!
        : VenueAccountType.Clob,
    archivedAtDatetime:
      overrides && overrides.hasOwnProperty('archivedAtDatetime')
        ? overrides.archivedAtDatetime!
        : 'architecto',
    connectorId:
      overrides && overrides.hasOwnProperty('connectorId') ? overrides.connectorId! : 'ipsam',
    createdAtDatetime:
      overrides && overrides.hasOwnProperty('createdAtDatetime')
        ? overrides.createdAtDatetime!
        : 'officiis',
    dynamicScopes:
      overrides && overrides.hasOwnProperty('dynamicScopes')
        ? overrides.dynamicScopes!
        : [Scope.Create],
    integrated: overrides && overrides.hasOwnProperty('integrated') ? overrides.integrated! : false,
    scopes: overrides && overrides.hasOwnProperty('scopes') ? overrides.scopes! : [Scope.Create],
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'culpa',
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId')
        ? overrides.venueAccountId!
        : 'voluptatem',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'perspiciatis',
    vostroNostro:
      overrides && overrides.hasOwnProperty('vostroNostro')
        ? overrides.vostroNostro!
        : VostroNostro.Nostro,
  };
};

export const aVenueAccountSearchInput = (
  overrides?: Partial<VenueAccountSearchInput>,
): VenueAccountSearchInput => {
  return {
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'vel',
    archived: overrides && overrides.hasOwnProperty('archived') ? overrides.archived! : false,
    connectorId:
      overrides && overrides.hasOwnProperty('connectorId') ? overrides.connectorId! : 'et',
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 4550,
    sortingOrder:
      overrides && overrides.hasOwnProperty('sortingOrder')
        ? overrides.sortingOrder!
        : SortingOrder.Asc,
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'qui',
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId') ? overrides.venueAccountId! : 'quae',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'sint',
    venueAccountType:
      overrides && overrides.hasOwnProperty('venueAccountType')
        ? overrides.venueAccountType!
        : VenueAccountType.Clob,
    vostroNostro:
      overrides && overrides.hasOwnProperty('vostroNostro')
        ? overrides.vostroNostro!
        : VostroNostro.Nostro,
  };
};

export const aVenueAccountsPerVenue = (
  overrides?: Partial<VenueAccountsPerVenue>,
): { __typename: 'VenueAccountsPerVenue' } & VenueAccountsPerVenue => {
  return {
    __typename: 'VenueAccountsPerVenue',
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'rem',
    venueAccounts:
      overrides && overrides.hasOwnProperty('venueAccounts')
        ? overrides.venueAccounts!
        : [aVenueAccount()],
  };
};

export const aVenueAccountsWithUnsettledCountResponse = (
  overrides?: Partial<VenueAccountsWithUnsettledCountResponse>,
): {
  __typename: 'VenueAccountsWithUnsettledCountResponse';
} & VenueAccountsWithUnsettledCountResponse => {
  return {
    __typename: 'VenueAccountsWithUnsettledCountResponse',
    deactivatedAt:
      overrides && overrides.hasOwnProperty('deactivatedAt')
        ? overrides.deactivatedAt!
        : 'voluptas',
    unsettledTransactionsCount:
      overrides && overrides.hasOwnProperty('unsettledTransactionsCount')
        ? overrides.unsettledTransactionsCount!
        : 6364,
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'qui',
    venueAccountId:
      overrides && overrides.hasOwnProperty('venueAccountId')
        ? overrides.venueAccountId!
        : 'ratione',
    venueAccountName:
      overrides && overrides.hasOwnProperty('venueAccountName')
        ? overrides.venueAccountName!
        : 'inventore',
  };
};

export const aVenueCapabilitiesResponse = (
  overrides?: Partial<VenueCapabilitiesResponse>,
): { __typename: 'VenueCapabilitiesResponse' } & VenueCapabilitiesResponse => {
  return {
    __typename: 'VenueCapabilitiesResponse',
    capabilities:
      overrides && overrides.hasOwnProperty('capabilities')
        ? overrides.capabilities!
        : aDetailedCapabilities(),
    venue: overrides && overrides.hasOwnProperty('venue') ? overrides.venue! : 'voluptatem',
  };
};

export const aVenueResponse = (
  overrides?: Partial<VenueResponse>,
): { __typename: 'VenueResponse' } & VenueResponse => {
  return {
    __typename: 'VenueResponse',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'consequatur',
    type: overrides && overrides.hasOwnProperty('type') ? overrides.type! : VenueType.Client,
  };
};

export const aWalletAccountConnection = (
  overrides?: Partial<WalletAccountConnection>,
): { __typename: 'WalletAccountConnection' } & WalletAccountConnection => {
  return {
    __typename: 'WalletAccountConnection',
    edges:
      overrides && overrides.hasOwnProperty('edges') ? overrides.edges! : [aWalletAccountEdge()],
    pageInfo: overrides && overrides.hasOwnProperty('pageInfo') ? overrides.pageInfo! : aPageInfo(),
  };
};

export const aWalletAccountEdge = (
  overrides?: Partial<WalletAccountEdge>,
): { __typename: 'WalletAccountEdge' } & WalletAccountEdge => {
  return {
    __typename: 'WalletAccountEdge',
    cursor: overrides && overrides.hasOwnProperty('cursor') ? overrides.cursor! : 'doloribus',
    node:
      overrides && overrides.hasOwnProperty('node') ? overrides.node! : aWalletAccountResponse(),
  };
};

export const aWalletAccountResponse = (
  overrides?: Partial<WalletAccountResponse>,
): { __typename: 'WalletAccountResponse' } & WalletAccountResponse => {
  return {
    __typename: 'WalletAccountResponse',
    createdAt: overrides && overrides.hasOwnProperty('createdAt') ? overrides.createdAt! : 'dolor',
    dynamicScopes:
      overrides && overrides.hasOwnProperty('dynamicScopes')
        ? overrides.dynamicScopes!
        : [Scope.Create],
    id: overrides && overrides.hasOwnProperty('id') ? overrides.id! : 'consequatur',
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'sapiente',
    scopes: overrides && overrides.hasOwnProperty('scopes') ? overrides.scopes! : [Scope.Create],
    walletType:
      overrides && overrides.hasOwnProperty('walletType')
        ? overrides.walletType!
        : WalletType.Nostro,
  };
};

export const aWalletAccountSearchInput = (
  overrides?: Partial<WalletAccountSearchInput>,
): WalletAccountSearchInput => {
  return {
    after: overrides && overrides.hasOwnProperty('after') ? overrides.after! : 'velit',
    first: overrides && overrides.hasOwnProperty('first') ? overrides.first! : 8985,
    name: overrides && overrides.hasOwnProperty('name') ? overrides.name! : 'assumenda',
    scopes: overrides && overrides.hasOwnProperty('scopes') ? overrides.scopes! : [Scope.Create],
  };
};

export const aWalletByAsset = (
  overrides?: Partial<WalletByAsset>,
): { __typename: 'WalletByAsset' } & WalletByAsset => {
  return {
    __typename: 'WalletByAsset',
    asset: overrides && overrides.hasOwnProperty('asset') ? overrides.asset! : 'voluptatibus',
    wallet: overrides && overrides.hasOwnProperty('wallet') ? overrides.wallet! : 'numquam',
  };
};

export const aWalletByAssetInput = (
  overrides?: Partial<WalletByAssetInput>,
): WalletByAssetInput => {
  return {
    asset: overrides && overrides.hasOwnProperty('asset') ? overrides.asset! : 'consequatur',
    wallet: overrides && overrides.hasOwnProperty('wallet') ? overrides.wallet! : 'accusantium',
  };
};

export const aWithdrawal = (
  overrides?: Partial<Withdrawal>,
): { __typename: 'Withdrawal' } & Withdrawal => {
  return {
    __typename: 'Withdrawal',
    account: overrides && overrides.hasOwnProperty('account') ? overrides.account! : 'ducimus',
    accountName:
      overrides && overrides.hasOwnProperty('accountName')
        ? overrides.accountName!
        : 'consequuntur',
    currency:
      overrides && overrides.hasOwnProperty('currency') ? overrides.currency! : 'exercitationem',
    dateTime: overrides && overrides.hasOwnProperty('dateTime') ? overrides.dateTime! : 'sequi',
    description:
      overrides && overrides.hasOwnProperty('description') ? overrides.description! : 'laudantium',
    executionId:
      overrides && overrides.hasOwnProperty('executionId') ? overrides.executionId! : 'enim',
    feeAccountId:
      overrides && overrides.hasOwnProperty('feeAccountId') ? overrides.feeAccountId! : 'quo',
    feeAccountName:
      overrides && overrides.hasOwnProperty('feeAccountName')
        ? overrides.feeAccountName!
        : 'fugiat',
    feePortfolioId:
      overrides && overrides.hasOwnProperty('feePortfolioId')
        ? overrides.feePortfolioId!
        : 'explicabo',
    feePortfolioName:
      overrides && overrides.hasOwnProperty('feePortfolioName')
        ? overrides.feePortfolioName!
        : 'quia',
    portfolioId:
      overrides && overrides.hasOwnProperty('portfolioId') ? overrides.portfolioId! : 'libero',
    portfolioName:
      overrides && overrides.hasOwnProperty('portfolioName') ? overrides.portfolioName! : 'unde',
    quantity: overrides && overrides.hasOwnProperty('quantity') ? overrides.quantity! : 2.93,
    settled: overrides && overrides.hasOwnProperty('settled') ? overrides.settled! : 'ab',
    settledDateTime:
      overrides && overrides.hasOwnProperty('settledDateTime') ? overrides.settledDateTime! : 5.33,
    updatedAt: overrides && overrides.hasOwnProperty('updatedAt') ? overrides.updatedAt! : 1.3,
    uuid: overrides && overrides.hasOwnProperty('uuid') ? overrides.uuid! : 'doloremque',
    venueExecutionId:
      overrides && overrides.hasOwnProperty('venueExecutionId')
        ? overrides.venueExecutionId!
        : 'quibusdam',
  };
};
