import { AutocompleteGroupedOption, UseAutocompleteRenderedOption } from '@mui/base';
import { ResultsOrNoResultsContainer } from '../../../focus/ResultsOrNoResultsContainer';
import { AutocompleteListLoading, ResultContainer } from '../multiselect/AutocompleteResult';
import { AutocompleteDropdownItem } from '../multiselect/AutocompleteDropdownItem';
import { VenueIcon } from '@wyden/features/venue-account/VenueIcon';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';

export interface AutocompleteResultsListProps<O> {
  groupedOptions: O[] | AutocompleteGroupedOption<O>[];
  getOptionProps: (params: UseAutocompleteRenderedOption<O>) => React.HTMLAttributes<HTMLLIElement>;
  isOptionEqual: (option: O, filter: O) => boolean;
  onChange: (values: O | O[]) => void;
  selected: O[];
  getTitle: (option: O) => string | undefined | null;
  getSubtitle?: (option: O) => string | undefined | null;
  getLabel?: (option: O, isHidden?: boolean) => string | React.ReactNode | undefined;
  getIcon?: (option: O) => React.ReactNode;
  getOptionId: (option: O) => string | undefined | null;
  multipleSelection?: boolean;
  emptyTextType?: 'noResults' | 'prompt';
}

export function StepsSelectAutocompleteResult<O>(
  props: AutocompleteResultsListProps<O> & { loading: boolean },
) {
  return (
    <ResultContainer $noResults={!props.groupedOptions.length}>
      {props.loading ? <AutocompleteListLoading /> : <AutocompleteResultsList {...props} />}
    </ResultContainer>
  );
}

function AutocompleteResultsList<O>(props: AutocompleteResultsListProps<O>) {
  const optionsWithoutSelected =
    props.groupedOptions.filter(
      (option) =>
        !props.selected.some((selectedOption) => props.isOptionEqual(selectedOption, option as O)),
    ) || [];

  return (
    <ResultsOrNoResultsContainer
      recordsCount={props.groupedOptions.length}
      emptyTextType={props.emptyTextType}
    >
      {optionsWithoutSelected.map((option, index) => {
        const label = props.getLabel?.(option as O) as string;
        return (
          <AutocompleteDropdownItem
            {...props.getOptionProps({ option: option as O, index })}
            key={props.getOptionId(option as O)}
            subtitle={props.getSubtitle?.(option as O) || ''}
            label={
              label === null ? undefined : label?.length ? (
                <VenueIcon venue={label} />
              ) : (
                <ChevronRightIcon />
              )
            }
            icon={props.getIcon?.(option as O)}
          >
            {props.getTitle(option as O)}
          </AutocompleteDropdownItem>
        );
      })}
    </ResultsOrNoResultsContainer>
  );
}
