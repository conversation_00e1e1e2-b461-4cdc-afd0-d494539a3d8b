import { useTranslation } from 'react-i18next';
import { styled } from '@ui/styled';
import { Paragraph } from '@ui/Typography/Paragraph';
import { color } from '@ui/theme/colors';
import { Header } from '@ui/Typography/Header';
import { Checkbox } from '@ui/Checkbox';
import { getSpacing } from '@wyden/utils/styles';
import CloseIcon from '@mui/icons-material/Close';
import { ResetLabel } from './AutocompleteList';
import { Button } from '@ui/Button';

interface CheckboxListProps<O> {
  headerText: string;
  options: O[];
  selected: O[];
  toggleValue: (value: O) => void;
  clearList: () => void;
  isOptionEqual: (option: O, filter: O) => boolean;
  getOptionTitle: (option: O) => string;
  getOptionId: (option: O) => string;
  close: (event: { stopPropagation: () => void }) => void;
}

export function CheckboxList<O>(props: CheckboxListProps<O>) {
  const { t } = useTranslation();
  return (
    <>
      <Header variant={'h5'}>{props.headerText}</Header>
      {props.options.map((option) => (
        <DropdownContainer
          key={props.getOptionId(option)}
          onClick={(e) => {
            props.toggleValue(option);
            if (
              props.selected.length === 1 &&
              props.selected.some((selectedEl) => props.isOptionEqual(option, selectedEl))
            ) {
              props.close(e);
            }
          }}
        >
          <Checkbox
            checked={props.selected?.some((selectedEl) => props.isOptionEqual(option, selectedEl))}
          />
          <FiltersListParagraph>{props.getOptionTitle(option)}</FiltersListParagraph>
        </DropdownContainer>
      ))}
      <FiltersAction>
        <Button onClick={() => props.clearList()}>
          <ResetLabel>
            <CloseIcon /> {t('common.reset')}
          </ResetLabel>
        </Button>
        <Button variant="primary" onClick={(e) => props.close(e)}>
          {t('common.close')}
        </Button>
      </FiltersAction>
    </>
  );
}

const FiltersAction = styled('div')`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const DropdownContainer = styled('li')`
  display: flex;
  gap: ${getSpacing(3)};
  padding: ${getSpacing(1)} ${getSpacing(2)};
  margin: 0;
  cursor: pointer;
  align-items: center;

  & > span {
    margin: 0;
    padding: 0;
  }
`;

const FiltersListParagraph = styled(Paragraph)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
  cursor: pointer;
`;
