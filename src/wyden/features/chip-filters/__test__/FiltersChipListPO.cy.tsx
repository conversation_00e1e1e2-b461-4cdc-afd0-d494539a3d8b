import '../../../../../cypress/support/component';
import { FILTERS_POPOVER_TEST_ID } from '../FiltersChipList';
import { Comparator } from '../filters/Comparator';

export class FiltersChipListPO {
  applyTextAreaChip(chipName: string, value: string, resetBeforeApply = false) {
    cy.findByRole('button', { name: 'Add' }).click();
    cy.findByTestId(FILTERS_POPOVER_TEST_ID).within(() => {
      cy.findByText(chipName).click();
      if (resetBeforeApply) {
        cy.findByRole('button', { name: 'Reset' }).click();
      }
      cy.findByRole('textbox').type(value);
      cy.findByRole('button', { name: 'Apply' }).click();
    });
  }

  applyQuantityChip(chipName: string, comparator: Comparator, value: number, value2?: number) {
    cy.findByRole('button', { name: 'Add' }).click();
    cy.findByTestId(FILTERS_POPOVER_TEST_ID).within(() => {
      cy.findByText(chipName).click();
      cy.findByTestId(`${comparator}-option`).click();
      cy.findByTestId('quantity-input-left').type(`${value}`);
      if (value2 !== undefined) {
        cy.findByTestId('quantity-input-right').type(`${value2}`);
      }
      cy.findByRole('button', { name: 'Apply' }).click();
    });
  }

  applyInstrumentChip(chipName: string, value: string) {
    cy.findByRole('button', { name: 'Add' }).click();
    cy.findByTestId(FILTERS_POPOVER_TEST_ID).within(() => {
      cy.findByText(chipName).click();
      cy.findAllByText(value).eq(0).click();
    });
  }
}
