import { styled } from '@ui/styled';
import {
  PortfolioConfiguration,
  useUpdatePortfolioHedgingConfigurationMutation,
} from '@wyden/services/graphql/generated/graphql';
import { ConfigContainer } from '../broker-desk-configuration-form/CommonItems';
import {
  ConfiguredHedgingForm,
  getHedgingConfigurationFormProps,
  HedgingConfigurationFormSchema,
} from './FormConfigs';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { Notification, useNotification } from '../error-indicators/notification/useNotification';
import { useVenueAccounts } from '@wyden/hooks/useVenueAccounts';
import { HedgingFormContent } from './HedgingFormContent';
import { useContext } from 'react';
import { ConfigContext } from '../broker-desk-configuration-form/useBrokerDeskConfig';

export function HedgingForm({
  configuration,
  configRefetch,
}: {
  configuration?: PortfolioConfiguration | null;
  configRefetch: () => void;
  children?: (fields: {
    [key in keyof z.infer<typeof HedgingConfigurationFormSchema>]: JSX.Element;
  }) => JSX.Element;
}) {
  const { t } = useTranslation();
  const [updateHedgingConfiguration] = useUpdatePortfolioHedgingConfigurationMutation();
  const { addMessage } = useNotification();
  const { getAllAccounts } = useVenueAccounts();
  const { resource } = useContext(ConfigContext);
  const handleSubmit = (values: z.infer<typeof HedgingConfigurationFormSchema>) => {
    if (!configuration && !resource) return;

    updateHedgingConfiguration({
      variables: {
        portfolioId: configuration?.id || resource.id,
        request: {
          targetAccountId: values.targetAccount?.id || '',
          autoHedging: values.autoHedging || false,
          thresholdConfiguration: (
            configuration?.hedgingConfiguration?.thresholdConfiguration || []
          ).map((threshold) => ({
            asset: threshold.asset,
            highThreshold: threshold.highThreshold,
            lowThreshold: threshold.lowThreshold,
            targetExposure: threshold.targetExposure,
            hedgeInstrumentId: threshold.hedgeInstrument?.instrumentIdentifiers?.instrumentId,
          })),
        },
      },
      onError: () => {
        addMessage(Notification.ERROR, t('brokerDesk.configurationSave'));
      },
      onCompleted() {
        addMessage(Notification.SUCCESS, t('brokerDesk.configurationSave'));
        configRefetch();
      },
    });
  };

  const notCurrencyConfigured =
    !configuration?.hedgingConfiguration?.thresholdConfiguration?.length;
  const hedgingConfiguration = configuration?.hedgingConfiguration;

  const defaultValues = hedgingConfiguration
    ? {
        targetAccount:
          hedgingConfiguration?.targetAccountId && hedgingConfiguration.targetAccountName
            ? {
                id: hedgingConfiguration.targetAccountId,
                label: hedgingConfiguration.targetAccountName,
              }
            : undefined,
        autoHedging: hedgingConfiguration?.autoHedging || false,
      }
    : {
        targetAccount: undefined,
        autoHedging: false,
      };

  return (
    <StyledConfigContainer>
      <ConfiguredHedgingForm
        onSubmit={handleSubmit}
        schema={HedgingConfigurationFormSchema}
        props={getHedgingConfigurationFormProps(t, getAllAccounts(), !resource.managable)}
        defaultValues={defaultValues}
      >
        {(fields) => (
          <HedgingFormContent notCurrencyConfigured={notCurrencyConfigured} fields={fields} />
        )}
      </ConfiguredHedgingForm>
    </StyledConfigContainer>
  );
}

const StyledConfigContainer = styled(ConfigContainer)`
  display: flex;
  flex-direction: column;
  width: 60%;
`;
