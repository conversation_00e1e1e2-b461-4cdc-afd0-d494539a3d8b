import { InputAdornment, Tooltip } from '@mui/material';
import { autocompleteClasses } from '@mui/material/Autocomplete';
import { Tab } from '@ui/Tab';
import { Tabs } from '@ui/Tabs';
import { TextField } from '@ui/TextField';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { ReactComponent as AccountIcon } from '@wyden/assets/account.svg';
import { ReactComponent as SearchIcon } from '@wyden/assets/search.svg';
import { ReactComponent as WalletIcon } from '@wyden/assets/wallet.svg';
import { ReactComponent as ListIcon } from '@wyden/assets/list.svg';
import { AccountsList } from '@wyden/features/focus/AccountsList';
import { FocusFilters } from '@wyden/features/focus/FocusFilters';
import { PortfoliosList } from '@wyden/features/focus/PortfoliosList';
import {
  parseWalletToVenueAccount,
  useFocusAutocomplete,
} from '@wyden/features/focus/useFocusAutocomplete';
import {
  FocusAutocompleteList,
  FocusTab,
  useFocusSelectStore,
} from '@wyden/features/focus/useFocusSelectStore';
import {
  selectAccountFocus,
  useFocusStore,
  useFocusActions,
  selectFocus,
} from '@wyden/features/focus/useFocusStore';
import { useFocusListenerStore } from '@wyden/features/focus/focusListenerStore';
import { useVenueAccounts } from '@wyden/hooks/useVenueAccounts';
import { getSpacing } from '@wyden/utils/styles';
import { KeyboardEvent, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CombinedList } from './CombinedList';
import { isTag } from './focus.types';
import { useWorkspaces } from '../useWorkspaces';
import { StyledMenuItem } from '@ui/StyledMenuItem';
import WalletsIcon from '@mui/icons-material/Wallet';
import { WalletsList } from '@wyden/features/focus/WalletsList';
import { useWalletAccountSearchQuery } from '@wyden/services/graphql/generated/graphql';
import ClearIcon from '@mui/icons-material/Clear';
import { FirstResultsLabel } from '@wyden/components/FirstResultsLabel';

export interface FocusPopperProps {
  onClickAway: () => void;
}

export const FOCUS_SEARCH_DATA_TEST_ID = 'focus-search';
export const FOCUS_SEARCH_CLEAR_TEST_ID = 'clear-focus-search';

export function FocusAutocomplete(props: FocusPopperProps) {
  const { t } = useTranslation();
  const { account: persistedAccName } = useFocusStore(selectAccountFocus);
  const { isUnspecifiedFocus } = useFocusStore(selectFocus);
  const { getAllStreetSideAccounts } = useVenueAccounts();
  const { setUnspecifiedFocus } = useFocusActions();
  const { currentWorkspace } = useWorkspaces();
  const selectedVenueAccount = getAllStreetSideAccounts().find(
    (v) => v.venueAccountId === persistedAccName?.venueAccountId,
  );
  const { data: wallets } = useWalletAccountSearchQuery();
  const extractedWallets =
    wallets?.walletAccountSearch?.edges?.map((edge) => parseWalletToVenueAccount(edge.node)) ?? [];
  const selectedWallet = extractedWallets.find(
    (w) => w.venueAccountId === persistedAccName?.venueAccountId,
  );
  const {
    tab,
    portfolioTabOn,
    accountTabOn,
    walletTabOn,
    mainList,
    highlightedOption,
    customTabOn,
  } = useFocusSelectStore();

  useEffect(() => {
    if (selectedVenueAccount) {
      accountTabOn();
    } else if (selectedWallet) {
      walletTabOn();
    } else if (isUnspecifiedFocus) {
      customTabOn();
    } else {
      portfolioTabOn();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const {
    inputProps,
    groupedOptions,
    getRootProps,
    getListboxProps,
    getOptionProps,
    portfoliosLoading,
  } = useFocusAutocomplete({
    onClickAway: props.onClickAway,
  });

  const onKeyDown = (event: KeyboardEvent<HTMLDivElement>) => {
    const originalKeyDown = getRootProps().onKeyDown;
    const isSelectingTagByEnter =
      highlightedOption && event.key === 'Enter' && isTag(highlightedOption);

    if (isSelectingTagByEnter) {
      // TODO adding tag to the filters
      return;
    }

    if (originalKeyDown) {
      originalKeyDown(event);
    }
  };

  const handleSelectAll = () => {
    if (currentWorkspace?.isTrading) return;

    setUnspecifiedFocus();
    useFocusListenerStore.getState().notifyListeners({ type: 'reset', value: null });
    props.onClickAway();
  };

  const handleFocusSelectReset = () => {
    useFocusSelectStore.getState().reset();
  };

  const renderTabContent = () => {
    if (tab === FocusTab.PORTFOLIOS) {
      return (
        <>
          <FocusFilters getOptionProps={getOptionProps} groupedOptions={groupedOptions} />
          <FirstResultsLabel />
          <PortfoliosList
            getOptionProps={getOptionProps}
            groupedOptions={groupedOptions}
            portfoliosLoading={portfoliosLoading}
          />
        </>
      );
    }
    if (tab === FocusTab.ACCOUNTS) {
      return (
        <>
          <FocusFilters getOptionProps={getOptionProps} groupedOptions={groupedOptions} />
          <FirstResultsLabel />
          <AccountsList getOptionProps={getOptionProps} groupedOptions={groupedOptions} />
        </>
      );
    }
    if (tab === FocusTab.WALLETS) {
      return (
        <>
          <FocusFilters getOptionProps={getOptionProps} groupedOptions={groupedOptions} />
          <FirstResultsLabel />
          <WalletsList getOptionProps={getOptionProps} groupedOptions={groupedOptions} />
        </>
      );
    }
    return (
      <Tooltip title={currentWorkspace?.isTrading ? t('focusAutocomplete.selectAllTooltip') : ''}>
        <StyledSelectAllItem onClick={handleSelectAll} $disabled={currentWorkspace?.isTrading}>
          {t('focusAutocomplete.selectAll')}
        </StyledSelectAllItem>
      </Tooltip>
    );
  };

  return (
    <div {...getRootProps()} onKeyDown={onKeyDown}>
      <Listbox {...getListboxProps()}>
        <FocusContainer>
          <TextField
            label={t('focusAutocomplete.search')}
            autoFocus
            skipOptionalLabel
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: (
                <StyledClearIcon
                  fontSize="small"
                  onClick={handleFocusSelectReset}
                  data-testid={FOCUS_SEARCH_CLEAR_TEST_ID}
                />
              ),
            }}
            inputProps={{
              ...inputProps,
              'data-testid': FOCUS_SEARCH_DATA_TEST_ID,
            }}
          />
          <ContentSection>
            {mainList === FocusAutocompleteList.COMBINED_LIST ? (
              <CombinedList
                getOptionProps={getOptionProps}
                groupedOptions={groupedOptions}
                portfoliosLoading={portfoliosLoading}
              />
            ) : (
              <>
                <FocusTabs centered value={tab}>
                  <Tab
                    onClick={() => portfolioTabOn()}
                    label={
                      <TabLabel>
                        <WalletIcon />
                        {t('focusAutocomplete.portfolios')}
                      </TabLabel>
                    }
                    data-testId="focus-portfolios-tab"
                  />
                  <Tab
                    onClick={() => accountTabOn()}
                    label={
                      <TabLabel>
                        <AccountIcon />
                        {t('focusAutocomplete.accounts')}
                      </TabLabel>
                    }
                    data-testId="focus-accounts-tab"
                  />
                  {!currentWorkspace?.isTrading && (
                    <Tab
                      disabled={currentWorkspace?.isTrading}
                      onClick={() => walletTabOn()}
                      label={
                        <TabLabel>
                          <WalletsIcon />
                          {t('focusAutocomplete.wallets')}
                        </TabLabel>
                      }
                      data-testId="focus-wallets-tab"
                    />
                  )}
                  {!currentWorkspace?.isTrading && (
                    <Tab
                      onClick={() => customTabOn()}
                      label={
                        <TabLabel>
                          <ListIcon />
                          {t('focusAutocomplete.custom')}
                        </TabLabel>
                      }
                      data-testId="focus-custom-tab"
                    />
                  )}
                </FocusTabs>
                {renderTabContent()}
              </>
            )}
          </ContentSection>
        </FocusContainer>
      </Listbox>
    </div>
  );
}

const StyledSelectAllItem = styled(StyledMenuItem)<{ $disabled?: boolean }>`
  padding: ${getSpacing(2)};
  color: ${({ theme, $disabled }) =>
    $disabled
      ? color[theme.palette.mode].textElementsTextWeak
      : color[theme.palette.mode].textElementsTextPrimary};
`;

const FocusContainer = styled('div')`
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(3)};

  & li[aria-selected='true'] {
    background-color: ${({ theme }) => color[theme.palette.mode].fillsElementsFillHover};
  }

  & li.${autocompleteClasses.focused} {
    background-color: ${({ theme }) => color[theme.palette.mode].fillsElementsFillHover};
    cursor: pointer;
  }
`;

const Listbox = styled('ul')`
  width: 420px;
  height: 540px;
  padding: ${getSpacing(4)};
  z-index: 3;
  position: absolute;
  background-color: ${({ theme }) => color[theme.palette.mode].fillsSurfaceSurfaceTertiary};
  border-radius: 4px;
  box-shadow: 4px 8px 40px 0px rgba(0, 0, 0, 0.12);
`;

const FocusTabs = styled(Tabs)`
  min-height: 40px;
  max-height: 40px;
  border-bottom: solid 1px ${({ theme }) => color[theme.palette.mode].fillsElementsFillWeak};
  margin-bottom: ${getSpacing(1)};
  button {
    flex: 1;
    justify-content: center;
  }
  .Mui-selected {
    color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary} !important;
  }
`;

const TabLabel = styled('div')`
  display: flex;
  align-items: center;
  gap: ${getSpacing(2)};
  padding-bottom: 8px;
  &:hover {
    color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
  }
`;

const ContentSection = styled('div')`
  display: flex;
  flex-direction: column;
  max-height: 464px;
  overflow-y: hidden;
`;

export const StyledFirstResultsLabel = styled('div')`
  font-size: 11px;
  line-height: 16px;
  padding: ${getSpacing(1)} 0 ${getSpacing(2)} 0;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;

const StyledClearIcon = styled(ClearIcon)`
  cursor: pointer;
`;
