import { ComponentType, ReactNode, useMemo } from 'react';
import { styled } from '@ui/styled';
import { useTranslation } from 'react-i18next';
import { DialogTitle } from '@ui/DialogTitle';
import { DialogContent } from '@ui/DialogContent';
import { DialogActions } from '@ui/DialogActions';
import { Button } from '@ui/Button';
import { z } from 'zod';
import { PortfolioAccountInput } from '@wyden/features/add-manual-transaction/PortfolioAccountInput';
import { InstrumentField } from '@wyden/features/add-manual-transaction/InstrumentField';
import {
  DateTimePicker,
  DateTimePickerProps,
} from '@wyden/features/add-manual-transaction/DateTimePicker';
import { getSpacing } from '@wyden/utils/styles';
import { NumberField } from '@wyden/features/form/NumberField';
import { TextField } from '@wyden/features/form/TextField';
import { createTsForm, createUniqueFieldSchema } from '@ts-react/form';
import {
  CurrencyResponse,
  InstrumentResponseSchema,
  PortfolioResponseSchema,
  PortfolioType,
  Scope,
  TransactionTypeInput,
  VenueAccount,
  VenueAccountSchema,
} from '@wyden/services/graphql/generated/graphql';
import { t, TFunction } from 'i18next';
import { useTransactionActionsStore } from './useTransactionActionsStore';
import {
  CustomChildRenderProp,
  PropType,
  RTFFormProps,
  RTFFormSchemaType,
  RTFFormSubmitFn,
  UnwrapEffects,
} from '@ts-react/form/lib/src/createSchemaForm';
import { FormComponentMapping } from '@ts-react/form/src/createSchemaForm';
import { DeepPartial } from 'react-hook-form';
import { SelectChangeEvent } from '@mui/material';
import { CurrencyField } from './CurrencyField';
import { FeeAccountField } from './FeeAccountField';
import { FeeField } from './FeeField';
import { ConversionRatesField } from './ConversionRatesField';
import { TypeField } from './TypeField';
import { PortfolioField } from '@wyden/features/add-manual-transaction/PortfolioField';
import { WithdrawalAccountField } from '@wyden/features/add-manual-transaction/WithdrawalAccountField';
import { Switch } from '../form/Switch';

const PortfolioFieldDef = createUniqueFieldSchema(
  PortfolioResponseSchema()
    .nullable()
    .refine((value) => value !== null, t('transactionsHistory.portfolioIsRequired')),
  'PORTFOLIO',
);
const AccountFieldDef = createUniqueFieldSchema(
  VenueAccountSchema()
    .nullable()
    .refine((value) => value !== null, t('transactionsHistory.accountIsRequired')),
  'ACCOUNT',
);
const CounterPartyFieldDef = createUniqueFieldSchema(
  z
    .object({
      type: z.enum(['portfolio', 'accountWallet']),
      value: z.any(),
    })
    .refine((value) => value.value, t('transactionsHistory.portfolioAccountIsRequired')),
  'COUNTERPARTY',
);
const SourceFieldDef = createUniqueFieldSchema(
  z
    .object({
      type: z.enum(['portfolio', 'accountWallet']),
      value: z.any(),
    })
    .refine((value) => value.value, t('transactionsHistory.portfolioAccountIsRequired')),
  'SOURCE',
);
const DestinationFieldDef = createUniqueFieldSchema(
  z
    .object({
      type: z.enum(['portfolio', 'accountWallet']),
      value: z.any(),
    })
    .refine((value) => value.value, t('transactionsHistory.portfolioAccountIsRequired')),
  'DESTINATION',
);

const InstrumentFieldDef = createUniqueFieldSchema(
  InstrumentResponseSchema()
    .nullable()
    .refine((value) => value !== null, t('transactionsHistory.instrumentIsRequired')),
  'INSTRUMENTS',
);

const ConversionRatesFieldDef = createUniqueFieldSchema(
  z
    .array(
      z.object({
        baseCurrency: z.string(),
        quoteCurrency: z.string(),
        rate: z.string().nonempty(),
      }),
    )
    .optional()
    .nullable(),
  'ConversionRates',
);

const FeeFieldDef = createUniqueFieldSchema(z.number().optional().nullable(), 'FEE');

export const ExternalTransactionDateTimeField = createUniqueFieldSchema(
  z
    .date({
      required_error: t('common.validation.required'),
    })
    .refine((date) => date instanceof Date, {
      message: t('transactionsHistory.validation.invalidDateFormat'),
    }),
  'EXTERNAL_TRANSACTION_DATE_TIME',
);

export const SettlementDate = createUniqueFieldSchema(
  z
    .date({
      required_error: t('common.validation.required'),
    })
    .optional(),
  'SETTLEMENT_DATE',
);

const TypeFieldDef = createUniqueFieldSchema(
  z.string().nonempty(t('transactionsHistory.transactionTypeIsRequired')),
  'TYPE',
);

const FeeAccountFieldDef = createUniqueFieldSchema(
  z
    .object({
      id: z.string(),
      label: z.string(),
    })
    .optional()
    .nullable(),
  'FEE_ACCOUNT',
);

const FeePortfolioFieldDef = createUniqueFieldSchema(
  PortfolioResponseSchema().optional().nullable(),
  'FEE_PORTFOLIO',
);

const CurrencyFieldDef = createUniqueFieldSchema(
  z.object(
    {
      id: z.string(),
      label: z.string(),
    },
    {
      required_error: t('common.validation.required'),
    },
  ),
  'CURRENCY_TYPE',
);

interface DialogFormProps {
  children?: ReactNode;
  onSubmit: () => void;
  onClose: () => void;
}

const DialogForm = ({ onSubmit, onClose, children }: DialogFormProps) => {
  const { t } = useTranslation();
  const { isSubmitting } = useTransactionActionsStore();
  return (
    <AddNewTransactionForm onSubmit={onSubmit}>
      <DialogTitle onClose={onClose}>{t('transactionsHistory.add')}</DialogTitle>
      <DialogContent>
        <Container>{children}</Container>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>{t('transactionsHistory.dismiss')}</Button>
        <Button disabled={isSubmitting} variant="primary" onClick={onSubmit}>
          {t('transactionsHistory.add')}
        </Button>
      </DialogActions>
    </AddNewTransactionForm>
  );
};

const AddNewTransactionForm = styled('form')``;
const Container = styled('div')`
  display: flex;
  flex-direction: column;
  margin-top: ${getSpacing(2)};
  gap: ${getSpacing(4)};

  > form {
    display: flex;
    flex-direction: column;
    gap: ${getSpacing(2)};
  }
`;
const createMapping = (
  DateTimePickerComponent: ComponentType<DateTimePickerProps> = DateTimePicker,
): FormComponentMapping => {
  return [
    [TypeFieldDef, TypeField],
    [PortfolioFieldDef, PortfolioField],
    [AccountFieldDef, WithdrawalAccountField],
    [SourceFieldDef, PortfolioAccountInput],
    [DestinationFieldDef, PortfolioAccountInput],
    [CounterPartyFieldDef, PortfolioAccountInput],
    [InstrumentFieldDef, InstrumentField],
    [z.number(), NumberField],
    [z.string(), TextField],
    [FeeAccountFieldDef, FeeAccountField],
    [FeePortfolioFieldDef, PortfolioField],
    [ExternalTransactionDateTimeField, DateTimePickerComponent],
    [SettlementDate, DateTimePickerComponent],
    [CurrencyFieldDef, CurrencyField],
    [FeeFieldDef, FeeField],
    [ConversionRatesFieldDef, ConversionRatesField],
    [z.boolean(), Switch],
  ] as const as FormComponentMapping;
};

interface AddNewTransactionDialogProps<SchemaType extends RTFFormSchemaType> {
  DateTimePickerComponent?: ComponentType<DateTimePickerProps>;
  schema: SchemaType;
  onSubmit: RTFFormSubmitFn<SchemaType>;
  defaultValues?: DeepPartial<z.infer<UnwrapEffects<SchemaType>>>;
  props: PropType<FormComponentMapping, SchemaType>;
  children?: CustomChildRenderProp<SchemaType>;
  formProps?: RTFFormProps<FormComponentMapping, SchemaType>['formProps'];
}

export const AddNewTransactionDialog = <SchemaType extends RTFFormSchemaType>({
  DateTimePickerComponent = DateTimePicker,
  schema,
  onSubmit,
  defaultValues,
  props,
  children,
  formProps,
}: AddNewTransactionDialogProps<SchemaType>) => {
  const Form = useMemo(() => {
    const mapping = createMapping(DateTimePickerComponent);
    return createTsForm(mapping as FormComponentMapping, {
      FormComponent: DialogForm,
    });
  }, [DateTimePickerComponent]);

  type TsForm = ReturnType<typeof createTsForm>;
  type TsFormProps = Parameters<TsForm>[0];

  const config: TsFormProps = {
    schema,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSubmit: onSubmit as any,
    defaultValues,
    props,
    children,
    formProps,
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return <Form {...(config as any)} />;
};

export const TradeTransactionFormSchema = z
  .object({
    type: TypeFieldDef,
    portfolio: PortfolioFieldDef,
    description: z.string().optional(),
    portfolioAccount: CounterPartyFieldDef,
    instrument: InstrumentFieldDef,
    quantity: z.number({
      required_error: t('common.validation.required'),
    }),
    price: z.number().positive(t('transactionsHistory.positivePrice')),
    externalTransactionDateTime: ExternalTransactionDateTimeField,
    externalOrderId: z.string().optional(),
    externalTransactionId: z.string().optional(),
    conversionRates: ConversionRatesFieldDef,
    executionId: z.string().optional(),
    settlementDate: SettlementDate,
    settled: z.boolean().optional().nullable(),
  })
  .superRefine((data, ctx) => {
    if (data.settled && !data.settlementDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: t('common.validation.required'),
        path: ['settlementDate'],
      });
    }
  });

export const TransferTransactionFormSchema = z.object({
  type: TypeFieldDef,
  currency: CurrencyFieldDef,
  description: z.string().optional(),
  fee: FeeFieldDef,
  feeAccount: FeeAccountFieldDef,
  feePortfolio: FeePortfolioFieldDef,
  source: SourceFieldDef,
  destination: DestinationFieldDef,
  quantity: z
    .number({
      required_error: t('common.validation.required'),
    })
    .positive(t('transactionsHistory.positiveQuantity')),
  externalTransactionDateTime: ExternalTransactionDateTimeField,
  externalTransactionId: z.string().optional(),
  conversionRates: ConversionRatesFieldDef,
});

export const DepositWithdrawalSchema = z.object({
  type: TypeFieldDef,
  portfolio: PortfolioFieldDef,
  description: z.string().optional(),
  account: AccountFieldDef,
  currency: CurrencyFieldDef,
  fee: FeeFieldDef,
  feeAccount: FeeAccountFieldDef,
  feePortfolio: FeePortfolioFieldDef,
  quantity: z
    .number({
      required_error: t('common.validation.required'),
    })
    .positive(t('transactionsHistory.positiveQuantity')),
  conversionRates: ConversionRatesFieldDef,
  externalTransactionDateTime: ExternalTransactionDateTimeField,
});

export const getTradeManualTransactionProps = (
  t: TFunction,
  handleTypeChange: (type: SelectChangeEvent<unknown>) => void,
) => {
  return {
    type: {
      label: t('transactionsHistory.transactionTypes'),
      skipOptionalLabel: true,
      onChange: handleTypeChange,
      options: [
        { id: TransactionTypeInput.Trade, label: 'Trade' },
        { id: TransactionTypeInput.Deposit, label: 'Deposit' },
        { id: TransactionTypeInput.Withdrawal, label: 'Withdrawal' },
        { id: TransactionTypeInput.Transfer, label: 'Transfer' },
      ],
    },
    portfolio: {
      label: t('transactionsHistory.portfolio'),
      skipOptionalLabel: true,
      showPortfolioCurrency: true,
    },
    portfolioAccount: {
      skipOptionalLabel: true,
      portfolioLabel: t('transactionsHistory.counterPortfolio'),
      skipWallets: true,
      scopes: [Scope.Read, Scope.Trade],
      portfolioType: PortfolioType.Nostro,
    },
    instrument: {
      label: t('transactionsHistory.instrument'),
      skipOptionalLabel: true,
      clearable: true,
      venueNames: [],
    },
    quantity: {
      label: t('transactionsHistory.quantity'),
      skipOptionalLabel: true,
    },
    price: {
      label: t('transactionsHistory.price'),
      skipOptionalLabel: true,
    },
    externalTransactionDateTime: {
      label: t('transactionsHistory.externalTransactionDateTime'),
      skipOptionalLabel: true,
    },
    externalOrderId: {
      label: t('transactionsHistory.externalOrderId'),
      skipOptionalLabel: true,
    },
    externalTransactionId: {
      label: t('transactionsHistory.externalTransactionId'),
    },
    description: {
      label: t('transactionsHistory.description'),
    },
    settled: {
      label: t('transactionsHistory.settled'),
    },
    settlementDate: {
      label: t('transactionsHistory.settlementDate'),
      skipOptionalLabel: true,
    },
    executionId: {
      label: t('transactionsHistory.executionId'),
      skipOptionalLabel: true,
    },
    // the type expectation comes form ts-form(external lib)
    // however this object is correct and can be used as is
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } as any;
};

export const getTransferManualTransactionProps = ({
  t,
  handleTypeChange,
  currencies,
  venueAccounts,
  wallets,
}: {
  t: TFunction;
  handleTypeChange: (type: SelectChangeEvent<unknown>) => void;
  currencies: CurrencyResponse[];
  venueAccounts: VenueAccount[];
  wallets: VenueAccount[];
}) => {
  return {
    type: {
      label: t('transactionsHistory.transactionTypes'),
      skipOptionalLabel: true,
      onChange: handleTypeChange,
      options: [
        { id: TransactionTypeInput.Trade, label: 'Trade' },
        { id: TransactionTypeInput.Deposit, label: 'Deposit' },
        { id: TransactionTypeInput.Withdrawal, label: 'Withdrawal' },
        { id: TransactionTypeInput.Transfer, label: 'Transfer' },
      ] as const,
    },
    currency: {
      label: t('transactionsHistory.currency'),
      skipOptionalLabel: true,
      freeSolo: false,
      options:
        currencies?.map((currency) => ({
          id: currency.symbol,
          label: currency.symbol,
        })) || [],
    },
    feeAccount: {
      label: t('transactionsHistory.feeAccountWallet'),
      freeSolo: false,
      disableClearable: false,
      skipOptionalLabel: true,
      options: [...venueAccounts, ...wallets].map((account) => ({
        id: account.venueAccountId,
        label: account.venueAccountName,
      })),
    },
    feePortfolio: {
      label: t('transactionsHistory.feePortfolio'),
      skipOptionalLabel: true,
    },
    quantity: {
      label: t('transactionsHistory.quantity'),
      skipOptionalLabel: true,
    },
    price: {
      label: t('transactionsHistory.price'),
      skipOptionalLabel: true,
    },
    fee: {
      label: t('transactionsHistory.fee'),
    },
    externalTransactionDateTime: {
      label: t('transactionsHistory.externalTransactionDateTime'),
      skipOptionalLabel: true,
    },
    externalOrderId: {
      label: t('transactionsHistory.externalOrderId'),
      skipOptionalLabel: true,
    },
    externalTransactionId: {
      label: t('transactionsHistory.externalTransactionId'),
    },
    source: {
      label: t('transactionsHistory.source'),
      scopes: [],
    },
    destination: {
      label: t('transactionsHistory.destination'),
      scopes: [],
    },
    description: {
      label: t('transactionsHistory.description'),
    },
    // the type expectation comes form ts-form(external lib)
    // however this object is correct and can be used as is
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } as any;
};

export const getDepositWithdrawalManualTransactionProps = ({
  t,
  handleTypeChange,
  currencies,
  venueAccounts,
  wallets,
}: {
  t: TFunction;
  handleTypeChange: (type: SelectChangeEvent<unknown>) => void;
  currencies: CurrencyResponse[];
  venueAccounts: VenueAccount[];
  wallets: VenueAccount[];
}) => {
  return {
    type: {
      label: t('transactionsHistory.transactionTypes'),
      skipOptionalLabel: true,
      onChange: handleTypeChange,
      options: [
        { id: TransactionTypeInput.Trade, label: 'Trade' },
        { id: TransactionTypeInput.Deposit, label: 'Deposit' },
        { id: TransactionTypeInput.Withdrawal, label: 'Withdrawal' },
        { id: TransactionTypeInput.Transfer, label: 'Transfer' },
      ] as const,
    },
    portfolio: {
      label: t('transactionsHistory.portfolio'),
      skipOptionalLabel: true,
      showPortfolioCurrency: false,
    },
    account: {
      label: t('transactionsHistory.account'),
      skipOptionalLabel: true,
    },
    currency: {
      label: t('transactionsHistory.currency'),
      skipOptionalLabel: true,
      freeSolo: false,
      options:
        currencies?.map((currency) => ({
          id: currency.symbol,
          label: currency.symbol,
        })) || [],
    },
    feeAccount: {
      label: t('transactionsHistory.feeAccountWallet'),
      freeSolo: false,
      disableClearable: false,
      skipOptionalLabel: true,
      options: [...venueAccounts, ...wallets].map((account) => ({
        id: account.venueAccountId,
        label: account.venueAccountName,
      })),
    },
    feePortfolio: {
      label: t('transactionsHistory.feePortfolio'),
      skipOptionalLabel: true,
    },
    quantity: {
      label: t('transactionsHistory.quantity'),
      skipOptionalLabel: true,
    },
    fee: {
      label: t('transactionsHistory.fee'),
    },
    externalTransactionDateTime: {
      label: t('transactionsHistory.externalTransactionDateTime'),
      skipOptionalLabel: true,
    },
    description: {
      label: t('transactionsHistory.description'),
    },
    // the type expectation comes form ts-form(external lib)
    // however this object is correct and can be used as is
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } as any;
};
