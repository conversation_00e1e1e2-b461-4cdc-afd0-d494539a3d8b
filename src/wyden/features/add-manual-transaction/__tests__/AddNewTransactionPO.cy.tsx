import '../../../../../cypress/support/component';
import { ADD_TRANSACTION_MODAL_TEST_ID, TransactionActions } from '../TrasactionActions';
import { CONTEXT_MENU_TEST_ID } from '../../../../ui/ContextDropdown';
import { FormPO } from '../../form/__test__/FormPO.cy';
import { useEffect } from 'react';
import { DateTimePickerProps, formatDisplayValue } from '../DateTimePicker';
import { useTsController } from '@ts-react/form';
import { SnackbarProvider } from 'notistack';
import React from 'react';

export class TransactionFormPO {
  private formPO = new FormPO();

  private withinModal(fn: () => void) {
    return cy.get(`[data-testid="${ADD_TRANSACTION_MODAL_TEST_ID}"]`).within(fn);
  }

  selectDefaultTradeValues() {
    this.selectPortfolio('portfolio_trader_1');
    this.selectCounterPortfolio('portfolio_trader_2');
    this.selectInstrument('ADAUSD');
    this.insertConversionRate('USDassumenda', '1');
    this.insertPrice('1');
    this.insertQuantity('1');
    this.insertExternalOrderId('1');
    this.insertExternalTransactionId('1');
    this.insertExecutionId('1');
  }

  selectDefaultTransferValues() {
    this.selectCurrency('USD');
    this.insertQuantity('1');
    this.insertFee('1');
    this.selectFeePortfolio('portfolio_trader_1');
    this.selectFeeAccount('BitMEX-testnet1');
    this.insertExternalTransactionId('1');
    this.selectSourcePortfolio('portfolio_trader_1');
    this.selectDestinationPortfolio('portfolio_trader_2');
    this.insertConversionRate('USDassumenda', '1');
  }

  selectType(transactionType: string) {
    return this.formPO.selectByLabelText('Transaction Type', transactionType);
  }

  selectPortfolio(portfolio: string) {
    return this.formPO.selectByLabelText('Portfolio', portfolio);
  }

  selectFeePortfolio(feePortfolio: string) {
    return this.formPO.selectByLabelText('Fee portfolio', feePortfolio);
  }

  selectFeeAccount(feeAccount: string) {
    return this.formPO.selectByLabelText('Fee account/wallet', feeAccount);
  }

  selectCurrency(currency: string) {
    return this.formPO.selectByLabelText('Currency', currency);
  }

  selectCounterPortfolio(item: string) {
    return this.formPO.selectByLabelText('Counter-portfolio', item);
  }

  selectSourcePortfolio(item: string) {
    cy.findByTestId('source-input').within(() => {
      cy.findByLabelText('Portfolio').click({
        force: true,
      });
    });

    return cy
      .findByRole('listbox', {
        name: 'Portfolio',
      })
      .within(() => cy.findByText(item).click());
  }

  selectDestinationPortfolio(item: string) {
    cy.findByTestId('destination-input').within(() => {
      cy.findByLabelText('Portfolio').click({
        force: true,
      });
    });

    return cy
      .findByRole('listbox', {
        name: 'Portfolio',
      })
      .within(() => cy.findByText(item).click());
  }

  selectAccountPortfolioOrAccountSelect(item: string) {
    cy.contains('label', 'accountWallet').click();
    return this.formPO.selectByLabelText('Account', item);
  }

  selectInstrument(instrument: string) {
    return this.formPO.selectByLabelText('Instrument', instrument);
  }

  insertConversionRate(name: string, rate: string) {
    return this.withinModal(() => this.formPO.insertByLabelText(name, rate));
  }

  insertQuantity(quantity: string) {
    return this.withinModal(() => this.formPO.insertByLabelText('Quantity', quantity));
  }

  insertFee(fee: string) {
    return this.withinModal(() => this.formPO.insertByLabelText('Fee', fee));
  }

  insertPrice(price: string) {
    return this.withinModal(() => this.formPO.insertByLabelText('Price', price));
  }

  insertExternalOrderId(id: string) {
    return this.withinModal(() => this.formPO.insertByLabelText('External Order ID', id));
  }

  insertExternalTransactionId(id: string) {
    return this.withinModal(() => this.formPO.insertByLabelText('External Transaction ID', id));
  }

  insertExecutionId(id: string) {
    return this.withinModal(() => this.formPO.insertByLabelText('Execution ID', id));
  }

  expectTextToBeVisible(text: string) {
    return this.withinModal(() => cy.contains(text));
  }

  clickDismissButton() {
    return this.withinModal(() => cy.findByText('Dismiss').click());
  }

  submit() {
    return this.withinModal(() => cy.get('button').contains('Add transaction manually').click());
  }

  assertByLabelTextNotVisible(label: string, value: string) {
    cy.findByLabelText(label).click({
      force: true,
    });
    cy.findByRole('listbox', {
      name: label,
    }).within(() => {
      cy.contains(value).should('not.exist');
    });
    cy.get('body').click();
  }

  assertDepositTransactionsNotVisible() {
    return this.assertByLabelTextNotVisible('Transaction Type', 'Deposit');
  }

  selectAccount(item: string) {
    return this.formPO.selectByLabelText('Account/Wallet', item);
  }

  selectDefaultDepositWithdrawalValues() {
    this.selectPortfolio('portfolio_trader_1');
    this.selectAccount('Bitmex1');
    this.selectCurrency('USD');
    this.insertQuantity('1');
    this.insertFee('1');
    this.selectFeeAccount('BitMEX-testnet1');
    this.selectFeePortfolio('portfolio_trader_1');
    this.insertConversionRate('USDassumenda', '1');
  }
}

const MockDateTimePicker = (props: DateTimePickerProps) => {
  const fixedDate = new Date(2023, 11, 10, 11, 11, 11);
  const tsController = useTsController<Date>();

  useEffect(() => {
    if (tsController) {
      tsController.field.onChange(fixedDate);
    } else {
      props.onChange?.(fixedDate);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const error = tsController?.error;

  return (
    <div data-testid="mock-date-picker">
      <input type="text" value={formatDisplayValue(fixedDate)} readOnly />
      {error && (
        <div className="error-message" data-testid="date-picker-error">
          {typeof error === 'string' ? error : error?.errorMessage || 'Invalid date'}
        </div>
      )}
    </div>
  );
};

export class AddNewTransactionPO {
  transactionForm: TransactionFormPO;

  constructor() {
    this.transactionForm = new TransactionFormPO();
  }

  render() {
    cy.viewport(720, 1580);
    return cy.mountWithProviders(
      <SnackbarProvider>
        <TransactionActions DateTimePickerComponent={MockDateTimePicker} />
      </SnackbarProvider>,
    );
  }

  clickAddTransactionButton() {
    return cy.findByText('Add transaction manually').click();
  }

  assertTransactionAddedSuccessfully() {
    return cy.contains('Adding Transaction was successful');
  }

  assertTransactionAddedError() {
    return cy.contains('Add transaction manually failed');
  }

  assertClientError(message: string) {
    return cy.contains(message);
  }

  openContextDropdown() {
    return cy.get(`[data-testid="${CONTEXT_MENU_TEST_ID}"]`).click();
  }

  assertContextDropdownIsClosed() {
    return cy.get(`[data-testid="${ADD_TRANSACTION_MODAL_TEST_ID}"]`).should('not.exist');
  }

  expectTextToBeVisible(text: string) {
    return cy.contains(text).should('be.visible');
  }
}
