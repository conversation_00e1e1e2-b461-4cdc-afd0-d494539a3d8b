import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { TransactionTypeInput } from '@wyden/services/graphql/generated/graphql';

type CounterPartyType = 'portfolio' | 'account';

type TransactionParticipant = {
  name: string;
  type: CounterPartyType;
  currency: string;
};

interface TransactionActionsStore {
  type: TransactionTypeInput;
  isSubmitting: boolean;
  isAddTransactionModalOpen: boolean;
  transactionSubject: {
    baseCurrency: string;
    quoteCurrency: string;
  } | null;
  transactionParticipants: TransactionParticipant[];
  toggleAddTransactionModal: () => void;
  setInitialFormValues: () => void;
  setIsSubmitting: (isSubmitting: boolean) => void;
  setType: (type: TransactionTypeInput) => void;
  reset: () => void;
  setTransactionSubject: (subject: TransactionActionsStore['transactionSubject']) => void;
  addOrUpdateTransactionParticipant: (participant: TransactionParticipant) => void;
  removeTransactionParticipant: (participantName: string) => void;
  clearTransactionParticipants: () => void;
}

const initialFormState = {
  type: TransactionTypeInput.Trade,
  isSubmitting: false,
  transactionSubject: null,
  transactionParticipants: [],
};

const initialState = {
  ...initialFormState,
  isAddTransactionModalOpen: false,
};

export const useTransactionActionsStore = create<TransactionActionsStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      setInitialFormValues: () => set(initialFormState),
      reset: () => set(initialState),
      toggleAddTransactionModal: () => {
        set((state) => ({ isAddTransactionModalOpen: !state.isAddTransactionModalOpen }));
        get().setInitialFormValues();
      },
      setTransactionSubject: (subject: TransactionActionsStore['transactionSubject']) =>
        set({ transactionSubject: subject }),
      addOrUpdateTransactionParticipant: (participant: TransactionParticipant) =>
        set((state) => {
          const existingParticipant = state.transactionParticipants.find(
            (p) => p.name === participant.name,
          );
          return {
            transactionParticipants: existingParticipant
              ? state.transactionParticipants.map((p) =>
                  p.name === participant.name ? participant : p,
                )
              : [...state.transactionParticipants, participant],
          };
        }),
      removeTransactionParticipant: (participantName: string) =>
        set((state) => ({
          transactionParticipants: state.transactionParticipants.filter(
            (p) => p.name !== participantName,
          ),
        })),
      clearTransactionParticipants: () =>
        set({ transactionParticipants: [], transactionSubject: null }),
      setIsSubmitting: (isSubmitting: boolean) => set({ isSubmitting }),
      setType: (type: TransactionTypeInput) => set({ type }),
    }),
    { name: 'TransactionActionsStore' },
  ),
);
