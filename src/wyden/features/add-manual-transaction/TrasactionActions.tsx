import { SelectChangeEvent } from '@mui/material';
import { Alert } from '@ui/Alert';
import { ContextDropdown, ContextDropdownItem } from '@ui/ContextDropdown';
import { Dialog } from '@ui/Dialog';
import { ReactComponent as AddIcon } from '@wyden/assets/add.svg';
import {
  AddNewTransactionDialog,
  DepositWithdrawalSchema,
  TradeTransactionFormSchema,
  TransferTransactionFormSchema,
  getDepositWithdrawalManualTransactionProps,
  getTradeManualTransactionProps,
  getTransferManualTransactionProps,
} from '@wyden/features/add-manual-transaction/AddNewTransactionForm';
import {
  DateTimePicker,
  DateTimePickerProps,
} from '@wyden/features/add-manual-transaction/DateTimePicker';
import { DepositWithdrawalFormParts } from '@wyden/features/add-manual-transaction/form-parts/DepositWithdrawalFormParts';
import {
  AddDepositWithdrawalTransactionValues,
  AddTradeTransactionValues,
  AddTransferTransactionValues,
  useAddTransaction,
} from '@wyden/features/add-manual-transaction/useAddTransaction';
import { useTransactionActionsStore } from '@wyden/features/add-manual-transaction/useTransactionActionsStore';
import { useVenueAccounts } from '@wyden/hooks/useVenueAccounts';
import {
  TransactionTypeInput,
  useWalletAccountSearchQuery,
} from '@wyden/services/graphql/generated/graphql';
import { ComponentType } from 'react';
import { useTranslation } from 'react-i18next';
import { ZodIssueCode } from 'zod';
import { isFulfilledValue } from '../broker-desk-configuration-form/utils';
import { useCurrencies } from '../currencies/useCurrencies';
import { TradeFormPart } from './form-parts/TradeFormPart';
import { TransferFormParts } from './form-parts/TransferFormParts';

interface TransactionActionsProps {
  DateTimePickerComponent?: ComponentType<DateTimePickerProps>;
}

export const ADD_TRANSACTION_MODAL_TEST_ID = 'add-transaction-modal';

export const TransactionActions = (
  props: TransactionActionsProps = {
    DateTimePickerComponent: DateTimePicker,
  },
) => {
  const { t } = useTranslation();
  const { currencies } = useCurrencies();
  const { getAllAccounts } = useVenueAccounts();
  const { data: wallets } = useWalletAccountSearchQuery();
  const walletAccountOptions =
    wallets?.walletAccountSearch?.edges
      ?.map((edge) => edge.node)
      .map((wallet) => {
        return {
          venueAccountId: wallet?.id,
          venueAccountName: wallet?.name,
          walletType: wallet?.walletType,
          venue: wallet?.name,
          //am TODO: [AC-5420] when the wallet model changes (check if there are scopes) adjust it
          scopes: [],
          dynamicScopes: [],
        };
      }) ?? [];
  const { toggleAddTransactionModal, setType, isAddTransactionModalOpen, type } =
    useTransactionActionsStore();

  const { handleSubmit, error, reset } = useAddTransaction();
  const clearTransactionParticipants = useTransactionActionsStore(
    (state) => state.clearTransactionParticipants,
  );

  const handleClose = () => {
    toggleAddTransactionModal();
    reset();
  };

  const handleTypeChange = (type: SelectChangeEvent<unknown>) => {
    setType(type.target.value as TransactionTypeInput);
    clearTransactionParticipants();
    reset();
  };

  const SchemaFromType = {
    [TransactionTypeInput.Trade]: TradeTransactionFormSchema,
    [TransactionTypeInput.Transfer]: TransferTransactionFormSchema.superRefine((value, ctx) => {
      if (isFulfilledValue(value.fee) && !value.feeAccount) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: 'common.validation.required',
          path: ['feeAccount'],
        });
      }
      if (isFulfilledValue(value.fee) && !value.feePortfolio) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: 'common.validation.required',
          path: ['feePortfolio'],
        });
      }
    }),
    [TransactionTypeInput.Withdrawal]: DepositWithdrawalSchema.superRefine((value, ctx) => {
      if (isFulfilledValue(value.fee) && !value.feeAccount) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: 'common.validation.required',
          path: ['feeAccount'],
        });
      }
      if (isFulfilledValue(value.fee) && !value.feePortfolio) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: 'common.validation.required',
          path: ['feePortfolio'],
        });
      }
    }),
    [TransactionTypeInput.Deposit]: DepositWithdrawalSchema.superRefine((value, ctx) => {
      if (isFulfilledValue(value.fee) && !value.feeAccount) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: 'common.validation.required',
          path: ['feeAccount'],
        });
      }
      if (isFulfilledValue(value.fee) && !value.feePortfolio) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: 'common.validation.required',
          path: ['feePortfolio'],
        });
      }
    }),
  }[type];

  const FormParts = {
    [TransactionTypeInput.Trade]: TradeFormPart,
    [TransactionTypeInput.Transfer]: TransferFormParts,
    [TransactionTypeInput.Withdrawal]: DepositWithdrawalFormParts,
    [TransactionTypeInput.Deposit]: DepositWithdrawalFormParts,
  }[type];

  const getFormProps = {
    [TransactionTypeInput.Trade]: () => getTradeManualTransactionProps(t, handleTypeChange),
    [TransactionTypeInput.Transfer]: () =>
      getTransferManualTransactionProps({
        t,
        handleTypeChange,
        currencies,
        venueAccounts: getAllAccounts(),
        wallets: walletAccountOptions,
      }),
    [TransactionTypeInput.Withdrawal]: () =>
      getDepositWithdrawalManualTransactionProps({
        t,
        handleTypeChange,
        currencies,
        venueAccounts: getAllAccounts(),
        wallets: walletAccountOptions,
      }),
    [TransactionTypeInput.Deposit]: () =>
      getDepositWithdrawalManualTransactionProps({
        t,
        handleTypeChange,
        currencies,
        venueAccounts: getAllAccounts(),
        wallets: walletAccountOptions,
      }),
  };

  return (
    <>
      <ContextDropdown>
        <ContextDropdownItem onClick={handleClose}>
          <AddIcon /> {t('transactionsHistory.add')}
        </ContextDropdownItem>
      </ContextDropdown>
      <Dialog
        data-testid={ADD_TRANSACTION_MODAL_TEST_ID}
        open={isAddTransactionModalOpen}
        onClose={handleClose}
      >
        <AddNewTransactionDialog
          DateTimePickerComponent={props.DateTimePickerComponent}
          onSubmit={(values) => {
            return handleSubmit(
              values as
                | AddTradeTransactionValues
                | AddTransferTransactionValues
                | AddDepositWithdrawalTransactionValues,
            );
          }}
          schema={SchemaFromType}
          formProps={
            {
              onClose: handleClose,
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
            } as any
          }
          defaultValues={{
            type: TransactionTypeInput.Trade,
          }}
          props={getFormProps[type]()}
        >
          {(fields) => {
            return (
              <>
                {fields.type}
                {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                <FormParts fields={fields as any} />
                {error && <Alert severity="error">{error?.message}</Alert>}
              </>
            );
          }}
        </AddNewTransactionDialog>
      </Dialog>
    </>
  );
};
