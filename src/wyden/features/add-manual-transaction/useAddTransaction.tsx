import { useTranslation } from 'react-i18next';
import {
  InstrumentResponse,
  PortfolioResponse,
  TransactionInput,
  TransactionTypeInput,
  useAddTransactionMutation,
} from '@wyden/services/graphql/generated/graphql';
import {
  useNotification,
  Notification,
} from '@wyden/features/error-indicators/notification/useNotification';
import { useTransactionActionsStore } from './useTransactionActionsStore';
import { VenueAccountWithVenue } from '@wyden/hooks/useVenueAccounts';

interface Portfolio {
  type: 'portfolio';
  value: { id: string };
}

interface Account {
  type: 'accountWallet';
  value: { venueAccountId: string };
}

export interface AddTradeTransactionValues {
  portfolio: { id: string };
  portfolioAccount?: Portfolio | Account;
  instrument: InstrumentResponse;
  quantity: number;
  price: number;
  fee?: number;
  description?: string;
  destination: Portfolio | Account;
  source: Portfolio | Account;
  feeAccount?: {
    id: string;
    label: string;
  };
  feePortfolio?: PortfolioResponse;
  externalTransactionDateTime: Date;
  externalOrderId?: string;
  externalTransactionId?: string;
  executionId?: string;
  settled?: boolean;
  settlementDate?: Date;
  conversionRates: TransactionInput['conversionRates'];
}

export interface AddTransferTransactionValues {
  quantity: number;
  fee?: number;
  description?: string;
  destination: Portfolio | Account;
  source: Portfolio | Account;
  feeAccount?: {
    id: string;
    label: string;
  };
  feePortfolio?: PortfolioResponse;
  externalTransactionDateTime: Date;
  externalOrderId?: string;
  externalTransactionId?: string;
  currency: { id: string; label: string };
  conversionRates: TransactionInput['conversionRates'];
}

export interface AddDepositWithdrawalTransactionValues {
  portfolio: { id: string };
  account?: VenueAccountWithVenue;
  currency: { id: string; label: string };
  quantity: number;
  fee?: number;
  feeAccount?: {
    id: string;
    label: string;
  };
  description?: string;
  feePortfolio?: PortfolioResponse;
  externalTransactionDateTime: Date;
  conversionRates: TransactionInput['conversionRates'];
}

const getAddTradeTransactionPayload = (values: AddTradeTransactionValues) => {
  return {
    type: TransactionTypeInput.Trade,
    description: values.description,
    portfolioId: values.portfolio.id,
    counterPortfolioId:
      values.portfolioAccount?.type === 'portfolio' ? values.portfolioAccount.value?.id : undefined,
    venueAccountId:
      values.portfolioAccount?.type === 'accountWallet'
        ? values.portfolioAccount.value?.venueAccountId
        : undefined,
    instrumentId: values.instrument.instrumentIdentifiers.instrumentId,
    quantity: values.quantity?.toString(),
    price: values.price?.toString(),
    dateTime: values.externalTransactionDateTime.toISOString(),
    externalOrderId: values.externalOrderId,
    venueExecutionId: values.externalTransactionId,
    conversionRates: values.conversionRates,
    executionId: values.executionId,
    isSettled: values.settled,
    settlementDate: values.settlementDate?.toISOString(),
  };
};

const getAddTransferTransactionPayload = (values: AddTransferTransactionValues) => {
  return {
    type: TransactionTypeInput.Transfer,
    description: values.description,
    dateTime: values.externalTransactionDateTime.toISOString(),
    currency: values.currency?.id,
    quantity: values.quantity.toString(),
    fee: values.fee?.toString(),
    venueExecutionId: values.externalTransactionId,
    portfolioId: values.destination?.type === 'portfolio' ? values.destination.value.id : undefined,
    venueAccountId:
      values.destination?.type === 'accountWallet'
        ? values.destination.value.venueAccountId
        : undefined,
    sourcePortfolioId: values.source?.type === 'portfolio' ? values.source.value.id : undefined,
    sourceAccountId:
      values.source?.type === 'accountWallet' ? values.source.value.venueAccountId : undefined,
    feeAccountId: values.feeAccount?.id,
    feePortfolioId: values.feePortfolio?.id,
    isSettled: false,
    conversionRates: values.conversionRates,
  };
};

const getAddDepositWithdrawalTransactionPayload = (
  values: AddDepositWithdrawalTransactionValues,
  type: typeof TransactionTypeInput.Deposit | TransactionTypeInput.Withdrawal,
) => {
  return {
    type,
    portfolioId: values.portfolio.id,
    description: values.description,
    venueAccountId: values.account?.venueAccountId,
    currency: values.currency?.id,
    quantity: values.quantity.toString(),
    fee: values.fee?.toString(),
    feeAccountId: values.feeAccount?.id,
    feePortfolioId: values.feePortfolio?.id,
    dateTime: values.externalTransactionDateTime.toISOString(),
    isSettled: false,
    conversionRates: values.conversionRates,
  };
};

export const useAddTransaction = () => {
  const { t } = useTranslation();
  const { addMessage } = useNotification();
  const { type } = useTransactionActionsStore();
  const { toggleAddTransactionModal, setIsSubmitting } = useTransactionActionsStore();
  const [addTransaction, { error, reset }] = useAddTransactionMutation();

  const handleSubmit = async (
    values:
      | AddTransferTransactionValues
      | AddTradeTransactionValues
      | AddDepositWithdrawalTransactionValues,
  ) => {
    const getRequest = {
      [TransactionTypeInput.Trade]: () =>
        getAddTradeTransactionPayload(values as AddTradeTransactionValues),
      [TransactionTypeInput.Transfer]: () =>
        getAddTransferTransactionPayload(values as AddTransferTransactionValues),
      [TransactionTypeInput.Withdrawal]: () =>
        getAddDepositWithdrawalTransactionPayload(
          values as AddDepositWithdrawalTransactionValues,
          TransactionTypeInput.Withdrawal,
        ),
      [TransactionTypeInput.Deposit]: () =>
        getAddDepositWithdrawalTransactionPayload(
          values as AddDepositWithdrawalTransactionValues,
          TransactionTypeInput.Deposit,
        ),
    }[type];

    try {
      setIsSubmitting(true);
      await addTransaction({
        variables: {
          request: getRequest(),
        },
      });
      setIsSubmitting(false);
      reset();
      toggleAddTransactionModal();
      addMessage(Notification.SUCCESS, t('transactionsHistory.addSuccess'));
    } catch (
      // eslint-disable-next-line
      err: any
    ) {
      setIsSubmitting(false);
    }
  };

  const returnedError =
    error?.graphQLErrors[0]?.extensions?.classification === 'INTERNAL_ERROR'
      ? {
          message: t('common.errors.serverError'),
        }
      : error;

  return {
    handleSubmit,
    error: returnedError,
    reset,
  };
};
