import { useRates } from './useRates';
import { styled } from '@ui/styled';
import { useTranslation } from 'react-i18next';
import { Paragraph } from '@ui/Typography/Paragraph';
import { getSpacing } from '@wyden/utils/styles';
import { RateInput } from './RateInput';
import { useConversionRatesField } from './useConversionRatesField';

export function ConversionRatesField() {
  const { conversionRates } = useRates();
  const { t } = useTranslation();
  const { removeRate, handleChange, isSubmitted, fieldValue } = useConversionRatesField();

  return (
    <StyledRates>
      {conversionRates.length > 0 && (
        <Paragraph variant="xsmall">{t('transactionsHistory.conversionRates')}</Paragraph>
      )}
      {conversionRates.map((rate) => (
        <RateInput
          key={`${rate.baseCurrency}-${rate.quoteCurrency}`}
          isSubmitted={isSubmitted}
          rate={rate}
          removeRate={removeRate}
          currencyAndornment={rate.quoteCurrency}
          onChange={handleChange(rate)}
          skipOptionalLabel
          ratesValue={fieldValue}
        />
      ))}
    </StyledRates>
  );
}

const StyledRates = styled('div')`
  gap: ${getSpacing(3)};
  display: flex;
  flex-direction: column;
`;
