import { styled } from '@ui/styled';
import { getSpacing } from '@wyden/utils/styles';
import { useTranslation } from 'react-i18next';
import {
  TransactionFilter,
  TransactionsFilterLists,
  useTransactionsFilters,
  useTransactionsFiltersActions,
  useTransactionsFiltersSelectors,
} from './useTransactionFilters';
import { Paragraph } from '@ui/Typography/Paragraph';
import { color } from '@ui/theme/colors';
import { FiltersChipList } from '@wyden/features/chip-filters/FiltersChipList';
import { MultiselectList } from '../chip-filters/filters/multiselect/MultiselectList';
import { useCurrencies } from '../currencies/useCurrencies';
import { TextArea } from '@wyden/features/chip-filters/filters/TextArea';
import { TransactionType, useEnumFormatters } from '@wyden/hooks/useEnumFormatters';
import { formatChipLabels } from '@wyden/helpers/stringsHelpers';
import { selectFocus, useFocusStore } from '../focus/useFocusStore';

const ColumnList = () => {
  const { setSelectedList } = useTransactionsFiltersActions();
  const { t } = useTranslation();

  return (
    <>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionsFilterLists.CURRENCY);
        }}
      >
        {t('transactions.filters.currency')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionsFilterLists.TRANSACTION_TYPES_LIST);
        }}
      >
        {t('transactions.filters.transactionTypes')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionsFilterLists.EXECUTION_ID_INPUT);
        }}
      >
        {t('transactions.filters.executionId')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionsFilterLists.VENUE_EXECUTION_ID_INPUT);
        }}
      >
        {t('transactions.filters.venueExecutionId')}
      </CategoryListParagraph>
    </>
  );
};

interface ListProps {
  close: (e: { stopPropagation: () => void }) => void;
}

const CurrencyInput = (props: ListProps) => {
  const { t } = useTranslation();
  const { setCurrency } = useTransactionsFiltersActions();
  const { selectCurrencyFilters } = useTransactionsFiltersSelectors();
  const currency = useTransactionsFilters(selectCurrencyFilters);

  const { currencies, loading } = useCurrencies();
  const currencyOptions = currencies.map((currency) => currency.symbol);

  return (
    <MultiselectList
      autocomplete
      headerText={t('transactions.filters.currency')}
      options={currencyOptions}
      loading={loading}
      filters={currency ?? []}
      setFilters={(currencies) => {
        setCurrency(currencies);
      }}
      close={props.close}
    />
  );
};

const TransactionTypes = (props: ListProps) => {
  const { t } = useTranslation();
  const types = Object.values(TransactionType);
  const { toggleTransactionType, clearTransactionType } = useTransactionsFiltersActions();
  const { selectToggleTransactionTypeFilters } = useTransactionsFiltersSelectors();
  const transactionType = useTransactionsFilters(selectToggleTransactionTypeFilters);
  const { formatTransactionType } = useEnumFormatters();

  return (
    <MultiselectList
      headerText={t('transactions.filters.transactionTypes')}
      options={types}
      filters={transactionType || []}
      close={props.close}
      toggleFilter={(filter) => {
        toggleTransactionType(filter);
      }}
      clearList={clearTransactionType}
      getTitle={(option) => formatTransactionType(option as TransactionType)}
    />
  );
};

const ExecutionIdInput = (props: ListProps) => {
  const { t } = useTranslation();
  const { setExecutionId } = useTransactionsFiltersActions();
  const { selectExtractedFilters } = useTransactionsFiltersSelectors();
  const { executionId } = useTransactionsFilters(selectExtractedFilters);

  return (
    <TextArea
      value={executionId || ''}
      headerText={t('transactionsHistory.executionId')}
      onApply={setExecutionId}
      close={props.close}
    />
  );
};

const VenueExecutionIdInput = (props: ListProps) => {
  const { t } = useTranslation();
  const { setVenueExecutionId } = useTransactionsFiltersActions();
  const { selectExtractedFilters } = useTransactionsFiltersSelectors();
  const { venueExecutionId } = useTransactionsFilters(selectExtractedFilters);

  return (
    <TextArea
      value={venueExecutionId || ''}
      headerText={t('transactionsHistory.venueExecutionId')}
      onApply={setVenueExecutionId}
      close={props.close}
    />
  );
};

const FiltersListParagraph = styled(Paragraph)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
  cursor: pointer;
`;

const CategoryListParagraph = styled(FiltersListParagraph)`
  padding: ${getSpacing(1)} ${getSpacing(2)};
`;

const lists = {
  [TransactionsFilterLists.COLUMN_LIST]: ColumnList,
  [TransactionsFilterLists.CURRENCY]: CurrencyInput,
  [TransactionsFilterLists.TRANSACTION_TYPES_LIST]: TransactionTypes,
  [TransactionsFilterLists.EXECUTION_ID_INPUT]: ExecutionIdInput,
  [TransactionsFilterLists.VENUE_EXECUTION_ID_INPUT]: VenueExecutionIdInput,
};

export const TRANSACTIONS_FILTERS_TEST_ID = 'transactions-filters';

export const TransactionsFilters = () => {
  const { clear, setSelectedList, columnListSelected, clearFilter } =
    useTransactionsFiltersActions();
  const { selectTransactionFilters } = useTransactionsFiltersSelectors();
  const { filters, selectedList } = useTransactionsFilters(selectTransactionFilters);
  const { t } = useTranslation();
  const { portfolio, account } = useFocusStore(selectFocus);

  const selectedFilters = filters?.filter(({ key }) => key === selectedList);
  // Labels that are used in the chips
  const filtersKeyTranslation = {
    [TransactionsFilterLists.CURRENCY]: t('transactions.filters.currency'),
    [TransactionsFilterLists.TRANSACTION_TYPES_LIST]: t('transactionsHistory.transactionTypes'),
    [TransactionsFilterLists.EXECUTION_ID_INPUT]: t('transactionsHistory.executionId'),
    [TransactionsFilterLists.VENUE_EXECUTION_ID_INPUT]: t('transactionsHistory.venueExecutionId'),
  };
  const List = lists[selectedList];

  // Create focus-based chips
  const focusChips: { key: string; values: string[]; nonEditable?: boolean }[] = [];
  if (portfolio) {
    focusChips.push({
      key: 'portfolio',
      values: [portfolio.name],
      nonEditable: true,
    });
  }
  if (account) {
    focusChips.push({
      key: 'account',
      values: [account.venueAccountName],
      nonEditable: true,
    });
  }

  return (
    <FiltersContainer data-testid={TRANSACTIONS_FILTERS_TEST_ID}>
      <FiltersChipList
        clearSubFilters={columnListSelected}
        filtersCount={filters.length + focusChips.length}
        getGroupedFilters={(editMode) => {
          const regularFilters = filters.filter(({ key }) => {
            if (selectedList === TransactionsFilterLists.COLUMN_LIST || editMode) {
              return true;
            }
            return key !== selectedList;
          });
          return [...focusChips, ...regularFilters];
        }}
        onEdit={(key) => {
          setSelectedList(key as TransactionsFilterLists);
        }}
        onRemove={(key) => clearFilter(key as keyof TransactionFilter)}
        onReset={clear}
        keySelectionModeOn={selectedList === TransactionsFilterLists.COLUMN_LIST}
        selectedKey={selectedList}
        selectedValues={selectedFilters.map(({ values }) => values).flat()}
        isKeyAdded={
          selectedList !== TransactionsFilterLists.COLUMN_LIST &&
          filters.some(({ key }) => key === selectedList)
        }
        translateKey={(key) => {
          if (key === 'portfolio') return t('focusAutocomplete.portfolio');
          if (key === 'account') return t('focusAutocomplete.account');
          return (
            filtersKeyTranslation[key as Exclude<TransactionsFilterLists, 'columnList'>] || key
          );
        }}
        transformDisplayChips={formatChipLabels}
      >
        {(close) => <List close={close} />}
      </FiltersChipList>
    </FiltersContainer>
  );
};

const FiltersContainer = styled('div')`
  display: flex;
  flex-direction: column;
  padding: ${getSpacing(1)};
`;
