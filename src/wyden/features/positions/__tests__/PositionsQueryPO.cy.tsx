import React from 'react';
import '../../../../../cypress/support/component';
import { FILTERS_POPOVER_TEST_ID } from '../../chip-filters/FiltersChipList';
import { ColumnSettingsPO } from '../../grid/__test__/ColumnSettingsPO.cy';
import { POSITION_FILTERS_TEST_ID } from '../PositionsFilters';
import { POSITIONS_COLUMNS_PICKER_TEST_ID } from '../PositionsGrid';
import { PositionsQuery } from '../PositionsQuery';

const Positions = () => {
  return <PositionsQuery />;
};

export class PositionsQueryPO {
  private selectedWidget = '#test-widget';
  columnSettings = new ColumnSettingsPO();

  render() {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(<Positions />);
  }

  renderMultiple(multipleWidgets = true) {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(<Positions />, { multiply: multipleWidgets });
  }

  gridHeaders() {
    return cy.findAllByRole('columnheader');
  }

  toggleSettings() {
    cy.findByTestId('column-settings-button').click({ force: true });
  }

  settingsHeader() {
    return cy.findAllByText('Reset');
  }

  allTableRows() {
    return cy.findAllByRole('row');
  }

  loadedSection() {
    return cy.findByTestId(/sync-positions-data/);
  }

  row(number: number) {
    return {
      instrumentId: () => cy.findAllByRole('gridcell').eq(1 + number * 10),
      quantity: () => cy.findAllByRole('gridcell').eq(2 + number * 10),
    };
  }

  clickColumn(name: string) {
    cy.get(`[data-testid="${POSITIONS_COLUMNS_PICKER_TEST_ID}"`).within(() => {
      cy.findByText('Positions').click({ force: true });
      cy.findByText(name).click({ force: true });
    });
  }

  openFiltersPopover() {
    return cy
      .get(`${this.selectedWidget} [data-testid="${POSITION_FILTERS_TEST_ID}"]`)
      .contains('Add')
      .click();
  }

  clickOutsideFiltersPopover() {
    return cy.get('body').click();
  }

  selectInstrument(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Instrument').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  expectFilterToBeSelected(value: string) {
    return cy
      .get(`${this.selectedWidget} [data-testid="${POSITION_FILTERS_TEST_ID}"]`)
      .contains(`${value}`)
      .should('be.visible');
  }

  selectCurrency(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Currency').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  resetFilters() {
    cy.get(`${this.selectedWidget} [data-testid="${POSITION_FILTERS_TEST_ID}"]`)
      .contains('Reset')
      .click();
  }

  expectFilterToBeNotSelected(value: string) {
    return cy
      .get(`${this.selectedWidget} [data-testid="${POSITION_FILTERS_TEST_ID}"]`)
      .contains(`${value}`)
      .should('not.exist');
  }

  expectResetButtonToNotBeVisible() {
    return cy
      .get(`${this.selectedWidget} [data-testid="${POSITION_FILTERS_TEST_ID}"]`)
      .contains('Reset')
      .should('not.exist');
  }

  expectTextToBeVisible(text: string) {
    return cy.findByText(text);
  }

  selectSecondTestWidget() {
    cy.get(`${this.selectedWidget}`).contains('Second Test Widget').click();
    this.selectedWidget = '#test-widget-1';
  }

  selectActiveTestWidget() {
    cy.get(`${this.selectedWidget}`).contains('Active Test Widget').click();
    this.selectedWidget = '#test-widget';
  }

  selectAccountsFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Accounts').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectWalletsFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Wallets').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }
}
