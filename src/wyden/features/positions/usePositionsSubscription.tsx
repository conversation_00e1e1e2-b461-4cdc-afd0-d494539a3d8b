import { usePositionChangesSubscription } from '@wyden/services/graphql/generated/graphql';
import { useTranslation } from 'react-i18next';
import { useNetworkStore } from '@wyden/features/error-indicators/network-indicators/useNetworkStore';
import { NetworkEvents } from '@wyden/features/error-indicators/network-indicators/events';
import { useEventLogs } from '@wyden/features/error-indicators/event-logs/useEventLogs';
import { selectFocus, useFocusStore } from '../focus/useFocusStore';

type CustomParams = {
  portfolio?: string[];
  venueAccount?: string[];
  first?: number;
  skip?: boolean;
};

export function usePositionsSubscription(customParams: CustomParams = {}) {
  const { upsertWsConnection } = useNetworkStore();
  const { portfolio, account } = useFocusStore(selectFocus);

  const { addEventLog } = useEventLogs();
  const { t } = useTranslation();

  const { skip, ...restParams } = customParams;

  usePositionChangesSubscription({
    variables: {
      search: {
        first: 1000000,
        portfolio: portfolio ? [portfolio?.id] : [],
        venueAccount: account ? [account.venueAccountId] : [],
        ...restParams,
      },
    },
    skip,
    shouldResubscribe: true,
    // To check onData go to customMessageReceivers.tsx
    onError: (error) => {
      upsertWsConnection('positionChanges', { errorType: 'CLIENT_ERROR', error });
      addEventLog({
        type: NetworkEvents.POSITION_CHANGES_SUBSCRIPTION,
        message: t('eventLogs.requestFailed', {
          name: t('positions.positionChangesSubscriptionError', {
            error: error.message,
          }),
        }),
        timestamp: new Date().getTime(),
      });
    },
  });
}
