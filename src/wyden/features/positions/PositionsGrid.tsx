import { WydenGrid } from '@wyden/features/grid/WydenGrid';
import { NoRows } from '@wyden/features/positions/NoRows';
import { PositionResponse } from '@wyden/services/graphql/generated/graphql';
import { GridReadyEvent } from 'ag-grid-community';
import { useColumnManager } from '../grid/ColumnManager';
import { positionIdBuilder } from './useLivePositionsStore';

export const POSITIONS_GRID_TEST_ID = 'positions-grid';
export const POSITIONS_COLUMNS_PICKER_TEST_ID = 'positions-columns-picker';
export const LEDGER_ENTRIES_COLUMNS_PICKER_TEST_ID = 'ledger-entries-columns-picker';

type Props = {
  positionsSource:
    | { source: 'QUERY'; datasource: undefined }
    | { source: 'REALTIME'; positions: PositionResponse[] };
  onGridReady: (params: GridReadyEvent) => void;
  isDataLoading?: boolean;
};

export const PositionsGrid = ({ positionsSource, onGridReady, isDataLoading }: Props) => {
  const {
    visibleColumnDefinitions,
    handleColumnMoved,
    onGridReady: columnOnGridReady,
  } = useColumnManager();

  return (
    <WydenGrid
      data-testid={POSITIONS_GRID_TEST_ID}
      isDataLoading={isDataLoading}
      rowModelType={positionsSource.source === 'REALTIME' ? 'clientSide' : 'infinite'}
      getRowId={(el) => positionIdBuilder(el.data)}
      columnDefs={visibleColumnDefinitions}
      onColumnMoved={handleColumnMoved}
      onGridReady={(params) => {
        onGridReady(params);
        columnOnGridReady && columnOnGridReady(params);
      }}
      rowData={
        positionsSource.source === 'QUERY' ? positionsSource.datasource : positionsSource.positions
      }
      noRowsOverlayComponent={NoRows}
    />
  );
};
