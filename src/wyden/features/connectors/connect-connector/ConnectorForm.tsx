import { createTsForm, createUniqueFieldSchema } from '@ts-react/form';
import { Select } from '@wyden/features/form/Select';
import { TextField } from '@wyden/features/form/TextField';
import { z } from 'zod';
import { DialogForm, DialogFormProps } from '../../form/DialogForm';
import { KeyValuesField, ConfigFieldEntry } from '../../config-field/ConfigFieldEntry';
import { useConnectorStore } from '../useConnectorStore';
import { EditConnectorFormComponent } from '../edit-connector/EditConnectorForm';

const VenueField = createUniqueFieldSchema(
  z.string({
    required_error: 'connectors.connectorForm.venueIsRequired',
  }),
  'venue',
);

const ConnectorFormSchemaZObject = {
  venue: VenueField,
  connectorName: z.string({
    required_error: 'connectors.connectorForm.connectorNameIsRequired',
  }),
  keyValues: KeyValuesField,
};

export const ConnectorFormSchema = z
  .object(ConnectorFormSchemaZObject)
  .superRefine((values, ctx) => {
    values.keyValues.forEach((val, index) => {
      const allOtherValueKeys = values.keyValues
        .filter((_val, cbIndex) => cbIndex !== index)
        .map((val) => val.key);

      if (allOtherValueKeys.includes(val.key)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: '',
          path: ['keyValues', index, 'key'],
        });
      }
    });
  });

export const mapping = [
  [VenueField, Select],
  [z.string(), TextField],
  [KeyValuesField, ConfigFieldEntry],
] as const;

const ConnectDialogForm = (props: DialogFormProps) => {
  const { toggleConnectorDialog } = useConnectorStore();
  return (
    <DialogForm
      {...props}
      toggleDialog={toggleConnectorDialog}
      titleTranslationId="connectors.newConnector"
      submitButtonTranslationId="connectors.connect"
    />
  );
};

export const ConnectorForm = createTsForm(mapping, {
  FormComponent: ConnectDialogForm,
});

export const EditConnectorForm = createTsForm(mapping, {
  FormComponent: EditConnectorFormComponent,
});
