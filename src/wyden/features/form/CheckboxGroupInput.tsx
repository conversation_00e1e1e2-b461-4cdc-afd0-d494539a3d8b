import { FormControl, FormControlLabel, FormGroup, FormHelperText, FormLabel } from '@mui/material';
import { useTsController } from '@ts-react/form';
import { Checkbox } from '@ui/Checkbox';
import { styled } from '@ui/styled';
import { useTranslation } from 'react-i18next';

export interface CheckboxGroupInputProps {
  label?: string;
  options: { label: string; id: string; disabled?: boolean }[];
  onChange?: (value: string) => void;
}

export const CheckboxGroupInput = (props: CheckboxGroupInputProps) => {
  const { field, error } = useTsController();
  const errorMessageKey = error?.errorMessage || '';
  const { t } = useTranslation();
  const fieldValue = field.value as string[];

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValues = fieldValue.includes(event.target.value)
      ? fieldValue.filter((v) => v !== event.target.value)
      : [...fieldValue, event.target.value];

    field.onChange(newValues);
  };

  return (
    <FormControl fullWidth error={!!error}>
      <FormLabel error={!!error} id={field.name}>
        {props.label}
      </FormLabel>
      <FormGroup onChange={handleChange}>
        {props.options?.map((option) => (
          <FormControlLabel
            key={option.id}
            value={option.id}
            label={option.label}
            control={
              <Checkbox
                disabled={option.disabled}
                size="medium"
                checked={fieldValue.includes(option.id)}
              />
            }
          />
        ))}
      </FormGroup>
      {error && <StyledError error={!!error}>{t(errorMessageKey)}</StyledError>}
    </FormControl>
  );
};

const StyledError = styled(FormHelperText)`
  margin: 0;
`;
