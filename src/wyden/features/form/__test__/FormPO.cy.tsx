import '../../../../../cypress/support/component';

export class FormPO {
  insertByLabelText(label: string, value: string) {
    return cy.findByLabelText(label).type(value);
  }

  checkByLabelText(label: string) {
    return cy.findByLabelText(label).click();
  }

  async selectByLabelText(label: string | RegExp, value: string) {
    cy.findByLabelText(label).click({
      force: true,
    });
    cy.findByRole('listbox', {
      name: label,
    }).within(() => cy.findByText(value).click());
  }

  async clearByLabelText(label: string | RegExp) {
    cy.findByLabelText(label)
      .click({
        force: true,
      })
      .clear();
  }

  async selectMultiByLabelText(label: string | RegExp, value: string[]) {
    cy.findByLabelText(label).click();
    value.forEach((v) => {
      cy.findByRole('listbox', {
        name: label,
      }).within(() => {
        cy.findByText(v).click();
      });
    });

    cy.findByRole('listbox', {
      name: label,
    }).type('{esc}');
  }

  async selectByTestId(id: string | RegExp, value: string, label: string) {
    cy.findByTestId(id).click();
    cy.findByRole('listbox', {
      name: label,
    }).within(() => cy.findByText(value).click());
  }

  async selectByLabelTextWithOptionsLengthCheck(label: string, value: string, length: number) {
    cy.findByLabelText(label).click();
    cy.findByRole('listbox', {
      name: label,
    }).within(() => {
      cy.findAllByRole('option').should('have.length', length);
      cy.findByText(value).click();
    });
  }

  getDropdownOptionsByLabel(label: string) {
    cy.findByLabelText(label).click();
    return cy
      .findByRole('listbox', {
        name: label,
      })
      .within(() => cy.findAllByRole('option'));
  }

  toggleByLabelText(label: string) {
    return cy.findByLabelText(label).click();
  }

  async checkSelectOptionsLength(label: string, length: number) {
    cy.findByLabelText(label).click();
    cy.findByRole('listbox', {
      name: label,
    }).within(() => cy.findAllByRole('option').should('have.length', length));
    cy.findByRole('listbox', {
      name: label,
    }).type('{esc}');
  }

  insertDate(label: string, date: string) {
    cy.findByLabelText(label).type(date);
  }
}
