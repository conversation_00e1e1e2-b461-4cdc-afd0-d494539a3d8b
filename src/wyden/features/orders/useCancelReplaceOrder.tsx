import { ERRORS } from '@wyden/constants';
import { useEventLogs } from '@wyden/features/error-indicators/event-logs/useEventLogs';
import { NetworkEvents } from '@wyden/features/error-indicators/network-indicators/events';
import { useNetworkStore } from '@wyden/features/error-indicators/network-indicators/useNetworkStore';
import {
  AssetClass,
  OrderType,
  Side,
  Tif,
  useCancelReplaceOrderMutation,
  VenueAccountDesc,
} from '@wyden/services/graphql/generated/graphql';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';
import { useOrdersStore } from './useOrdersStore';

export interface OrderModifyFormValues {
  assetClass?: string | undefined; // needed for AC-2397
  orderId?: string | undefined;
  clOrderId?: string | null | undefined;
  clientId?: string | null | undefined;
  currency?: string | null | undefined;
  expirationDateTime?: string | null | undefined;
  instrumentId?: string | null | undefined;
  limitPrice?: number | undefined;
  orderType?: string | undefined;
  portfolioId?: string | undefined;
  quantity: number;
  side?: string | undefined;
  stopPrice?: number | undefined;
  symbol?: string | undefined; // needed for AC-2397
  tif?: string | null | undefined;
  venueAccounts?: VenueAccountDesc[] | null | undefined;
}

export const useCancelReplaceOrder = () => {
  const { orderToModify } = useOrdersStore();
  const { upsertRequest } = useNetworkStore();
  const { addEventLog } = useEventLogs();
  const { t } = useTranslation();
  const clOrderId = uuidv4();

  const [cancelReplaceOrder] = useCancelReplaceOrderMutation({
    onError: (err) => {
      upsertRequest('cancelReplaceOrder', { pending: false, error: ERRORS.CLIENT_ERROR, err });
      addEventLog({
        type: NetworkEvents.CANCEL_ORDER_QUERY,
        message: t('eventLogs.requestFailed', { name: t('orders.cancelReplaceOrder') }),
        timestamp: new Date().getTime(),
      });
    },
  });

  return {
    cancelReplaceOrder: async (values: OrderModifyFormValues) => {
      const venueAccountIds = values.venueAccounts?.map((account) => account.id);

      try {
        await cancelReplaceOrder({
          variables: {
            request: {
              orderId: orderToModify?.orderId,
              origClientId: orderToModify?.clientId,
              origClOrderId: orderToModify?.clOrderId,
              replacingOrder: {
                assetClass: values.assetClass as AssetClass, // needed for AC-2397
                clOrderId,
                currency: values.currency,
                expirationDateTime: values.expirationDateTime,
                instrumentId: values.instrumentId,
                limitPrice: values.limitPrice,
                orderType: values.orderType as OrderType,
                portfolioId: values.portfolioId ?? '',
                quantity: values.quantity,
                side: values.side as Side,
                stopPrice: values.stopPrice,
                symbol: values.symbol, // needed for AC-2397
                tif: values.tif as Tif,
                venueAccounts: venueAccountIds,
              },
            },
          },
        });
      } catch (e: unknown) {
        upsertRequest('cancelReplaceOrder', {
          pending: false,
          error: ERRORS.NETWORK_ERROR,
        });
      }
    },
  } as const;
};
