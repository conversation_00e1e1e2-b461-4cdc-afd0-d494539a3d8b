import { createTsForm, createUniqueFieldSchema } from '@ts-react/form';
import { NumberField } from '@wyden/features/form/NumberField';
import { InstrumentResponse, OrderStateResponse } from '@wyden/services/graphql/generated/graphql';
import { TFunction } from 'i18next';
import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { z } from 'zod';
import { countFractionDigits } from '../../helpers/countFractionDigits';
import { formatNumber } from '../../helpers/formatNumber';
import { DialogForm, DialogFormProps } from '../form/DialogForm';
import { DisplayedLabel } from '../form/DisplayedLabel';
import { LabeledValue } from '../form/LabeledValue';
import { QuantityField } from './QuantityField';
import { OrderModifyFormValues } from './useCancelReplaceOrder';
import { useOrdersStore } from './useOrdersStore';
import { StopLimitPriceField } from './StopLimitPriceField';
import { useLiveOrdersStore } from '../grid/order-state/useLiveOrdersStore';
import { isFulfilledValue } from '../broker-desk-configuration-form/utils';
import { VenueAccountsField } from './VenueAccountsField';

const QuantityChange = createUniqueFieldSchema(z.string().optional(), 'quantityChange');
const LimitChange = createUniqueFieldSchema(z.string().optional(), 'limitChange');
const StopChange = createUniqueFieldSchema(z.string().optional(), 'stopChange');
const QuantityDef = createUniqueFieldSchema(
  z
    .number({
      required_error: 'modifyOrderForm.quantityIsRequired',
    })
    .nonnegative(),
  'quantity',
);

const LimitPriceDef = createUniqueFieldSchema(z.number().optional().nullable(), 'limitPrice');

const StopPriceDef = createUniqueFieldSchema(z.number().optional().nullable(), 'stopPrice');

const VenueAccountDescSchema = z.object({
  id: z.string(),
  name: z.string(),
});

export const modifyOrderFormSchemaZObject = {
  assetClass: z.string().optional(),
  clOrderId: z.string().nullable(),
  currency: z.string().nullable(),
  expirationDateTime: z.string().optional().nullable(),
  filledQty: z.number().optional(),
  instrumentId: z.string().optional().nullable(),
  limitPrice: LimitPriceDef,
  limitChange: LimitChange,
  orderType: z.string({
    required_error: 'modifyOrderForm.orderTypeIsRequired',
  }),
  origClOrderId: z.string().optional().nullable(),
  portfolioId: z.string({
    required_error: 'modifyOrderForm.portfolioIsRequired',
  }),
  quantity: QuantityDef,
  quantityChange: QuantityChange,
  side: z.string({
    required_error: 'modifyOrderForm.sideIsRequired',
  }),
  stopPrice: StopPriceDef,
  stopChange: StopChange,
  symbol: z.string().optional(),
  tif: z.string().nullable(),
  venueAccounts: z.array(VenueAccountDescSchema).optional().nullable(),
};

export const modifyOrderFormSchema = z.object(modifyOrderFormSchemaZObject).refine(
  (formData) => {
    const orderToModify = useOrdersStore.getState().orderToModify;
    const filled = useLiveOrdersStore.getState().orders.get(orderToModify?.orderId || '')
      ?.filledQty;

    return formData.quantity && isFulfilledValue(filled)
      ? formData.quantity > (filled as number)
      : true;
  },
  {
    message: 'modifyOrderForm.qtyCanNotBeLowerThanFilled',
    path: ['quantity'],
  },
);

const mapping = [
  [z.array(VenueAccountDescSchema), VenueAccountsField],
  [z.string(), LabeledValue],
  [z.number(), NumberField],
  [QuantityChange, DisplayedLabel],
  [LimitChange, DisplayedLabel],
  [StopChange, DisplayedLabel],
  [QuantityDef, QuantityField],
  [LimitPriceDef, StopLimitPriceField],
  [StopPriceDef, StopLimitPriceField],
] as const;

export const getModifyOrderFormProps = (
  t: TFunction<'translation', 'translation'>,
  instrument?: InstrumentResponse | null,
  isCashOrder?: boolean,
) => ({
  assetClass: { label: '' },
  clOrderId: { label: '' },
  currency: { label: '' },
  expirationDateTime: { label: '' },
  filledQty: {
    label: t(isCashOrder ? 'modifyOrderForm.filledAmount' : 'modifyOrderForm.filledQty'),
    disabled: true,
  },
  instrumentId: { label: t('modifyOrderForm.instrument') },
  limitPrice: {
    label: t('modifyOrderForm.limitPrice'),
    instrument,
  },
  limitChange: { labelId: 'modifyOrderForm.limitChange' },
  orderType: { label: '' },
  origClOrderId: { label: '' },
  portfolioId: { label: t('modifyOrderForm.portfolioId') },
  quantity: {
    label: t(isCashOrder ? 'modifyOrderForm.amount' : 'modifyOrderForm.quantity'),
    isCash: isCashOrder,
    instrument,
  },
  quantityChange: {
    labelId: isCashOrder ? 'modifyOrderForm.amountChange' : 'modifyOrderForm.quantityChange',
  },
  side: { label: t('modifyOrderForm.side') },
  stopPrice: {
    instrument,
    label: t('modifyOrderForm.stopPrice'),
  },
  stopChange: { labelId: 'modifyOrderForm.stopChange' },
  symbol: { label: '' },
  tif: { label: '' },
  venueAccounts: { label: t('modifyOrderForm.venueAccounts') },
});

export const transformToModificationForm = (
  orderState?: OrderStateResponse,
): OrderModifyFormValues => {
  return {
    ...orderState,
    currency: orderState?.currency,
    limitPrice: orderState?.limitPrice ?? undefined,
    stopPrice: orderState?.stopPrice ?? undefined,
    instrumentId: orderState?.instrument?.instrumentIdentifiers.instrumentId,
    quantity: orderState?.orderQty || 0,
    venueAccounts: orderState?.venueAccountDescs,
    assetClass: (orderState?.assetClass as string) ?? undefined,
    symbol: orderState?.symbol ?? undefined,
  };
};

const ModifyOrderDialogForm = (props: DialogFormProps) => {
  const { orderToModify, setOrderToModify } = useOrdersStore();
  const { watch, setValue } = useFormContext();

  const originalQuantity = orderToModify?.orderQty;
  const originalLimitPrice = orderToModify?.limitPrice;
  const originalStopPrice = orderToModify?.stopPrice;
  const quantity = watch('quantity');
  const limitPrice = watch('limitPrice');
  const stopPrice = watch('stopPrice');

  const quoteCurrency = orderToModify?.instrument?.baseInstrument?.quoteCurrency;
  const currency = orderToModify?.currency;

  const qtyScale = countFractionDigits(
    orderToModify?.instrument?.tradingConstraints.qtyIncr || undefined,
  );
  const priceScale = countFractionDigits(
    orderToModify?.instrument?.tradingConstraints.priceIncr || undefined,
  );

  useEffect(() => {
    if (originalQuantity) {
      const val = quantity - originalQuantity;
      const quantityChangeFormattedVal = formatNumber(val, qtyScale, {
        code: currency ? currency : '',
      });
      setValue('quantityChange', val ? quantityChangeFormattedVal : '');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [quantity, originalQuantity, currency, qtyScale]);

  useEffect(() => {
    if (originalLimitPrice) {
      const val = limitPrice - originalLimitPrice;
      const limitChangeFormattedVal = formatNumber(val, priceScale, {
        code: quoteCurrency ? quoteCurrency : '',
      });
      setValue('limitChange', val ? limitChangeFormattedVal : '');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [limitPrice, originalLimitPrice, priceScale, quoteCurrency]);

  useEffect(() => {
    if (originalStopPrice) {
      const val = stopPrice - originalStopPrice;
      const stopChangeFormattedVal = formatNumber(val, priceScale, {
        code: quoteCurrency ? quoteCurrency : '',
      });
      setValue('stopChange', val ? stopChangeFormattedVal : '-');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stopPrice, originalStopPrice, priceScale, quoteCurrency]);

  return (
    <DialogForm
      {...props}
      toggleDialog={() => setOrderToModify(undefined)}
      titleTranslationId="modifyOrderForm.modifyOrder"
      submitButtonTranslationId="modifyOrderForm.modifyOrder"
    />
  );
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
export const ModifyOrderForm = createTsForm(mapping, {
  FormComponent: ModifyOrderDialogForm,
});
