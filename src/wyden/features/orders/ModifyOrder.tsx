import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import { Tooltip } from '@mui/material';
import { IconButton } from '@ui/IconButton';
import { ICellRendererParams } from 'ag-grid-community';
import { TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';
import { finalStatusFilter } from '../../helpers/finalStatusFilter';
import { OrderStateResponse, OrderStatus } from '../../services/graphql/generated/graphql';
import { useAuthStore } from '../../services/keycloak/auth/useAuthStore';
import { UserInfo } from '../../services/keycloak/auth/userInfo';
import { useOrdersStore } from './useOrdersStore';

function prepareDisablingExplanantions(
  user: string | UserInfo | null,
  orderState: OrderStateResponse | undefined,
  t: TFunction<'translation', undefined>,
) {
  const isFinished = orderState?.orderStatus && finalStatusFilter(orderState.orderStatus);
  const isPendingNew = orderState?.orderStatus === OrderStatus.PendingNew;
  const isChild = Boolean(orderState?.parentOrderId);

  const disabledTooltipMessages: string[] = [];
  if (isFinished) {
    disabledTooltipMessages.push(
      `${t('modifyOrderForm.modifyingOrderWithFinalStatusIsNotAvailable')}`,
    );
  }
  if (isPendingNew) {
    disabledTooltipMessages.push(
      `${t('modifyOrderForm.modifyingOrderWithPendingNewStatusIsNotAvailable')}`,
    );
  }
  if (isChild) {
    disabledTooltipMessages.push(`${t('modifyOrderForm.modifyingChildOrderIsNotAvailable')}`);
  }
  return disabledTooltipMessages;
}

export const MODIFY_ORDER_TEST_ID = 'modify-order-element';

export const ModifyOrder = ({ data: orderState }: ICellRendererParams<OrderStateResponse>) => {
  const { user } = useAuthStore();
  const { t } = useTranslation();
  const { setOrderToModify } = useOrdersStore();

  const disabledTooltipMessages = prepareDisablingExplanantions(user, orderState, t);
  const disabled = disabledTooltipMessages.length > 0;

  const joinedMessages = (
    <div>
      {disabledTooltipMessages.map((m) => (
        <>
          {m}
          <br />
        </>
      ))}
    </div>
  );

  return (
    <Tooltip title={disabled ? joinedMessages : `${t('common.edit')}`} arrow>
      <span>
        <IconButton
          enabled-tooltip-message={
            disabled ? disabledTooltipMessages.join('\n') : `${t('common.edit')}`
          }
          onClick={() => orderState && setOrderToModify(orderState)}
          disabled={disabled}
          data-testid={MODIFY_ORDER_TEST_ID}
        >
          <EditOutlinedIcon fontSize="small" />
        </IconButton>
      </span>
    </Tooltip>
  );
};
