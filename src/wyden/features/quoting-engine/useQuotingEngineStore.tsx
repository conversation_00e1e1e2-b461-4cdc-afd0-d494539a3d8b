import { InstrumentResponse } from '@wyden/services/graphql/generated/graphql';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

type QuotingEngineStore = {
  editingInstrument: InstrumentResponse | null;
  setEditingInstrument: (instrument: InstrumentResponse | null) => void;
  dirtyFields: string[];
  setDirtyFields: (fields: string[]) => void;
  hasInvalidTimeValues: boolean;
  setHasInvalidTimeValues: (hasInvalidTimeValues: boolean) => void;
};

export const useQuotingEngineStore = create<QuotingEngineStore>()(
  devtools(
    (set) => ({
      editingInstrument: null,
      setEditingInstrument: (instrument: InstrumentResponse | null) =>
        set({ editingInstrument: instrument }),
      dirtyFields: [],
      setDirtyFields: (fields) => set({ dirtyFields: fields }),
      hasInvalidTimeValues: false,
      setHasInvalidTimeValues: (hasInvalidTimeValues: boolean) => set({ hasInvalidTimeValues }),
    }),
    { name: 'QuotingEngineStore' },
  ),
);
