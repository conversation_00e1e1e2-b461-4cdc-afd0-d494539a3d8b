import { describe, it, expect } from 'vitest';
import { getChangedValues } from '../VenueAccountSettingsConfirmationModal';
import { VenueAccountDetailsResponse } from '@wyden/services/graphql/generated/graphql';
import i18n from '@wyden/i18n';

describe('getChangedValues', () => {
  const mockAccountDetails: VenueAccountDetailsResponse = {
    venueAccountName: 'Original Venue',
    keyValues: [
      { key: 'existingKey', value: 'originalValue' },
      { key: 'removedKey', value: 'removedValue' },
    ],
    scopes: [],
    dynamicScopes: [],
    venueAccountId: '123',
    venueName: 'Test Venue',
  };

  it('should return empty array when values.keyValues is not provided', () => {
    const result = getChangedValues({}, mockAccountDetails, undefined);
    expect(result).toEqual([]);
  });

  it('should detect added key-value pairs', () => {
    const values = {
      keyValues: [{ key: 'newKey', value: 'newValue' }],
    };
    const result = getChangedValues(values, mockAccountDetails, undefined);
    expect(result).toContainEqual({
      key: 'newKey',
      oldValue: undefined,
      newValue: 'newValue',
    });
  });

  it('should detect modified key-value pairs', () => {
    const values = {
      keyValues: [{ key: 'existingKey', value: 'modifiedValue' }],
    };
    const result = getChangedValues(values, mockAccountDetails, undefined);
    expect(result).toContainEqual({
      key: 'existingKey',
      oldValue: 'originalValue',
      newValue: 'modifiedValue',
    });
  });

  it('should detect removed key-value pairs', () => {
    const values = {
      keyValues: [{ key: 'existingKey', value: 'originalValue' }],
    };
    const result = getChangedValues(values, mockAccountDetails, undefined);
    expect(result).toContainEqual({
      key: 'removedKey',
      oldValue: 'removedValue',
      newValue: '',
    });
  });

  it('should detect venue account name changes', () => {
    const values = {
      keyValues: [],
      venueAccountName: 'New Venue Name',
    };
    const result = getChangedValues(values, mockAccountDetails, undefined);
    expect(result).toContainEqual({
      key: i18n.t('venueAccounts.accountForm.venueAccount'),
      oldValue: 'Original Venue',
      newValue: 'New Venue Name',
    });
  });

  it('should handle null accountDetails', () => {
    const values = {
      keyValues: [{ key: 'newKey', value: 'newValue' }],
    };
    const result = getChangedValues(values, null, undefined);
    expect(result).toEqual([
      {
        key: 'newKey',
        oldValue: undefined,
        newValue: 'newValue',
      },
    ]);
  });

  it('should handle undefined accountDetails', () => {
    const values = {
      keyValues: [{ key: 'newKey', value: 'newValue' }],
    };
    const result = getChangedValues(values, undefined, undefined);
    expect(result).toEqual([
      {
        key: 'newKey',
        oldValue: undefined,
        newValue: 'newValue',
      },
    ]);
  });

  it('should handle isAddedFromButton flag', () => {
    const values = {
      keyValues: [{ key: 'newKey', value: 'newValue', isAddedFromButton: true }],
    };
    const result = getChangedValues(values, mockAccountDetails, undefined);
    expect(result).toContainEqual({
      key: 'newKey',
      oldValue: undefined,
      newValue: 'newValue',
    });
  });
});
