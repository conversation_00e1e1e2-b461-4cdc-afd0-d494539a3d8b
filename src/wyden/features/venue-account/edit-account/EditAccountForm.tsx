import { DialogFormProps } from '../../form/DialogForm';
import { styled } from '@ui/styled';
import { getSpacing } from '@wyden/utils/styles';
import { Button } from '@ui/Button';
import { useTranslation } from 'react-i18next';
import { Header } from '@ui/Typography/Header';
import { BaseSyntheticEvent, ReactNode } from 'react';
import { useFormContext } from 'react-hook-form';

interface AdditionalFormProps {
  children: ReactNode;
  onSubmit: (event?: BaseSyntheticEvent) => void;
}

export const EditAccountFormComponent = (props: DialogFormProps & AdditionalFormProps) => {
  const { onSubmit, children } = props;
  const { formState, reset } = useFormContext();
  const isTouched = Object.keys(formState.dirtyFields).length > 0;
  const { t } = useTranslation();
  return (
    <StyledForm
      {...props}
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit();
        reset({}, { keepValues: true, keepDirty: false });
      }}
    >
      <Header variant="h5">{t('venueAccounts.accountSettings')}</Header>
      <StyledContentContainer>{children}</StyledContentContainer>

      <StyledActions>
        <Button variant="primary" type="submit" size="lg" disabled={!isTouched}>
          {t('common.saveChanges')}
        </Button>
      </StyledActions>
    </StyledForm>
  );
};

const StyledForm = styled('form')`
  padding-top: ${getSpacing(2)};
`;

const StyledContentContainer = styled('div')`
  margin: ${getSpacing(3)} 0;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing(6)};
  margin-top: ${getSpacing(6)};
  padding-right: ${({ theme }) => theme.spacing(6)};
  max-width: 640px;
`;

const StyledActions = styled('div')`
  margin-top: ${getSpacing(6)};
`;
