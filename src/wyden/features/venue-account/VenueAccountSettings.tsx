import { NotFound } from '@ui/NotFound';
import { styled } from '@ui/styled';
import { Tab } from '@ui/Tab';
import { Tabs } from '@ui/Tabs';
import { color } from '@ui/theme/colors';
import { Header } from '@ui/Typography/Header';
import { useVenueAccounts } from '@wyden/hooks/useVenueAccounts';
import {
  Scope,
  Venue,
  VenueAccountDetailsResponse,
} from '@wyden/services/graphql/generated/graphql';
import { getSpacing } from '@wyden/utils/styles';
import { TFunction } from 'i18next';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { infer as zodInfer } from 'zod';
import { useVenues } from '../venues/useVenues';
import {
  flatAccountRequest,
  getManuallyAddedItems,
  mapConnectorTemplateToKeyValues,
  mergeWithOverrides,
} from './connect-venue-account/ConnectVenueAccountDialog';
import {
  EditAccountForm,
  VenueAccountFormSchema,
} from './connect-venue-account/ConnectVenueAccountForm';
import { useConnectorTemplate } from './connector-template/useConnectorTemplate';
import { useVenueAccountManagement } from './useVenueAccountManagement';
import {
  getChangedValues,
  VenueAccountSettingsConfirmationModal,
} from './VenueAccountSettingsConfirmationModal';
import { VenueIcon } from './VenueIcon';

type FormType = zodInfer<typeof VenueAccountFormSchema>;

type VenueAccountSettingsProps = {
  accountDetails: VenueAccountDetailsResponse | null | undefined;
  refetchAccountDetails: () => Promise<unknown>;
};

export function VenueAccountSettings({
  accountDetails,
  refetchAccountDetails,
}: VenueAccountSettingsProps) {
  const { t } = useTranslation();
  const [tabValue, setValue] = useState(0);
  const { streetSideVenues } = useVenues();
  const { venueAccountsRefetch, getAllAccounts } = useVenueAccounts();
  const { updateVenueAccount } = useVenueAccountManagement();
  const handleChange = (_event: React.SyntheticEvent, newValue: number) => setValue(newValue);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [pendingValues, setPendingValues] = useState<Record<string, unknown> | null>(null);

  const {
    updateVenue,
    connectorTemplate,
    loading: isConnectorTemplateLoading,
  } = useConnectorTemplate();

  useEffect(() => {
    accountDetails?.venueName && updateVenue(accountDetails?.venueName);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accountDetails]);

  const hasManage = accountDetails?.scopes?.includes(Scope.Manage);

  const getVenueAccountFormProps = (t: TFunction<'translation', undefined>, venues: Venue[]) => ({
    venueAccountName: {
      label: t('venueAccounts.accountForm.venueAccount'),
      disabled: !hasManage,
      required: true,
    },
    venue: {
      label: t('venueAccounts.accountForm.venue'),
      options: venues.map((venue: Venue) => ({ id: venue.name, label: venue.name })),
      disabled: true,
      required: true,
    },
    archived: {
      label: t('venueAccounts.accountForm.archived'),
    },
    keyValues: { required: false, disabled: !hasManage },
  });

  const accountDetailsCurrentValues = flatAccountRequest(accountDetails);

  const mergedKeyValues = [
    ...mergeWithOverrides(
      accountDetailsCurrentValues.keyValues,
      mapConnectorTemplateToKeyValues(connectorTemplate),
    ),
    ...getManuallyAddedItems(
      accountDetailsCurrentValues.keyValues,
      mapConnectorTemplateToKeyValues(connectorTemplate),
    ).map((item) => ({ ...item, isAddedFromButton: true })),
  ];

  const defaultValues = {
    ...accountDetailsCurrentValues,
    keyValues: mergedKeyValues.sort((a, b) => a.key.localeCompare(b.key)),
    archived: Boolean(
      getAllAccounts().find((account) => account.venueAccountId === accountDetails?.venueAccountId)
        ?.archivedAt,
    ),
  };

  // We create formMethods here to control the form externally (e.g. reset after successful update).
  // This allows us to trigger imperatively form.reset() from outside the form component,
  // which isn't possible with the default behavior of createTsForm.
  const formMethods = useForm<FormType>({
    defaultValues: {
      ...defaultValues,
      venue: defaultValues.venue as FormType['venue'],
    } as FormType,
    mode: 'onChange',
  });

  const handleSubmit = async (values: Record<string, unknown>) => {
    setPendingValues(values);
    setShowConfirmationModal(true);
  };

  const submitChanges = async (values: Record<string, unknown>) => {
    await updateVenueAccount({
      ...values,
      venue: accountDetails?.venueName || '',
      venueAccountName: (values?.venueAccountName as string) || '',
      venueAccountId: accountDetails?.venueAccountId,
    });

    venueAccountsRefetch();
    refetchAccountDetails();

    formMethods.reset(values);

    setShowConfirmationModal(false);
    setPendingValues(null);
  };

  return (
    <StyledContainer>
      <HeaderContainer>
        <Header variant="h2">{accountDetails?.venueAccountName}</Header>
        <StyledVenueSection>
          <StyledVenueIcon venue={accountDetails?.venueName} onlyIcon />
          <StyledVenueName>{accountDetails?.venueName}</StyledVenueName>
        </StyledVenueSection>
      </HeaderContainer>
      <StyledHeader variant="h6">
        {t('common.id')}: {accountDetails?.venueAccountId}
      </StyledHeader>
      <StyledTabs withDivider value={tabValue} onChange={handleChange}>
        <Tab label={t('common.settings')} />
      </StyledTabs>
      {!isConnectorTemplateLoading && accountDetails && (
        <>
          <EditAccountForm
            form={formMethods}
            onSubmit={handleSubmit}
            props={getVenueAccountFormProps(t, streetSideVenues)}
            schema={VenueAccountFormSchema}
            defaultValues={defaultValues}
          />
          <VenueAccountSettingsConfirmationModal
            open={showConfirmationModal}
            onClose={() => {
              setShowConfirmationModal(false);
              setPendingValues(null);
            }}
            onConfirm={() => pendingValues && submitChanges(pendingValues)}
            changedValues={
              pendingValues
                ? getChangedValues(pendingValues, accountDetails, connectorTemplate)
                : []
            }
          />
        </>
      )}
      {!accountDetails && <NotFound />}
    </StyledContainer>
  );
}

const StyledTabs = styled(Tabs)`
  .MuiButtonBase-root {
    padding: ${getSpacing(2)};
  }

  button.Mui-selected {
    color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary} !important;
  }
`;

const StyledContainer = styled('div')`
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(2)};
  max-height: 100%;
  height: 100%;
`;

const HeaderContainer = styled('div')`
  display: flex;
  flex-direction: row;
  gap: ${getSpacing(4)};
`;

const StyledVenueSection = styled('div')`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: ${getSpacing(2)};
  align-content: center;
`;

const StyledVenueIcon = styled(VenueIcon)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;

const StyledHeader = styled(Header)`
  font-size: 11px;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
  margin-bottom: ${getSpacing(2)};
`;

const StyledVenueName = styled('div')`
  font-size: 14px;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;
