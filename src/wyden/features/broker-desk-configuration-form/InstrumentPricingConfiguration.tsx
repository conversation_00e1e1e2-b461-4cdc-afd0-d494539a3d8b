import { useTranslation } from 'react-i18next';
import { useBrokerDeskStore } from './useBrokerDeskStore';
import { useBrokerDeskConfig } from './useBrokerDeskConfig';
import {
  PricingConfigurationForm,
  getInstrumentPricingConfigurationFormProps,
  InstrumentPricingConfigurationSchema,
} from './FormConfigs';
import { z } from 'zod';
import { LoadingDataContainer } from '../LoadingDataContainer';
import { VenueAccountWithVenue } from '@wyden/hooks/useVenueAccounts';
import {
  convertInstrumentExecutionConfigReadToWrite,
  getInstrumentPricingConfigurationDefaultValues,
  getPercentageFieldToSend,
  isFulfilledValue,
} from './utils';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { InstrumentPricingConfigurationFormContent } from './InstrumentPricingConfigurationFormContent';
import { ConfigurationFormHeader } from './CommonItems';
import {
  ConfigurationType,
  InstrumentConfiguration,
  InstrumentGroupConfiguration,
  PortfolioConfiguration,
  PortfolioGroupConfiguration,
} from '@wyden/services/graphql/generated/graphql';

type Props = {
  instrumentConfiguration?: InstrumentConfiguration | InstrumentGroupConfiguration | null;
  parentInstrumentConfiguration?: InstrumentConfiguration | InstrumentGroupConfiguration | null;
  portfolioOrGroupConfig: PortfolioConfiguration | PortfolioGroupConfiguration | undefined | null;
  parentConfiguration?: PortfolioConfiguration | PortfolioGroupConfiguration | null;
  getVenueForAccountId: (id: string) => string | null;
  getAccountForId: (id: string | undefined) => VenueAccountWithVenue | undefined;
};

export function InstrumentPricingConfiguration({
  portfolioOrGroupConfig,
  parentConfiguration,
  getAccountForId,
  getVenueForAccountId,
  parentInstrumentConfiguration,
  instrumentConfiguration,
}: Props) {
  const { t } = useTranslation();
  const editingInstrument = useBrokerDeskStore((state) => state.editingInstrument);
  const editingInstrumentId =
    editingInstrument && editingInstrument?.instrumentIdentifiers?.instrumentId;
  const pricingConfiguration = instrumentConfiguration?.pricingConfiguration;
  const tradable = useBrokerDeskStore((state) => state.tradable);
  const tradableTouched = useBrokerDeskStore((state) => state.tradableTouched);

  const defaultValues = getInstrumentPricingConfigurationDefaultValues({
    instrumentConfig: pricingConfiguration,
    portfolioConfig: portfolioOrGroupConfig?.pricingConfiguration,
    portfolioGroupConfig: parentConfiguration?.pricingConfiguration,
    parentInstrumentConfig: parentInstrumentConfiguration?.pricingConfiguration,
    getVenueForAccountId,
    getAccountForId,
  });

  const form = useForm<z.infer<typeof InstrumentPricingConfigurationSchema>>({
    resolver: zodResolver(InstrumentPricingConfigurationSchema),
    defaultValues,
  });
  const {
    updateInstrumentConfiguration,
    getInstrumentConfiguration,
    configurationLoading,
    resetInstrumentConfiguration,
    shouldSendInstrumentPricingField,
    resource,
  } = useBrokerDeskConfig({
    resetInstrumentPricingForm: form.reset,
  });

  const handlePricingConfigurationSubmit = (
    values: z.infer<typeof InstrumentPricingConfigurationSchema>,
  ) => {
    if (editingInstrumentId) {
      const targetInstrumentId = values.instrument?.instrumentIdentifiers?.instrumentId;
      const pricingSourceVenue = values.venueAccount?.id;

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const execConfig =
        getInstrumentConfiguration(editingInstrumentId)?.executionConfiguration || {};

      updateInstrumentConfiguration({
        instrumentId: editingInstrumentId,
        tradeable:
          isFulfilledValue(instrumentConfiguration?.tradeable) || tradableTouched
            ? tradable
            : undefined,
        executionConfiguration: convertInstrumentExecutionConfigReadToWrite(execConfig),
        pricingConfiguration: {
          markup: shouldSendInstrumentPricingField('markup')
            ? getPercentageFieldToSend(values.markup)
            : undefined,
          pricingSource:
            (shouldSendInstrumentPricingField('instrument') ||
              shouldSendInstrumentPricingField('venueAccount') ||
              shouldSendInstrumentPricingField('pricingSource')) &&
            targetInstrumentId &&
            pricingSourceVenue
              ? [{ instrumentId: targetInstrumentId, venueAccount: pricingSourceVenue }]
              : undefined,
        },
      });
    }
  };

  const resetConfig = () =>
    editingInstrumentId &&
    resetInstrumentConfiguration(ConfigurationType.Pricing, editingInstrumentId);

  return (
    <LoadingDataContainer
      loading={configurationLoading}
      data={portfolioOrGroupConfig !== undefined ? true : false}
    >
      <ConfigurationFormHeader onReset={resetConfig} title={t('brokerDesk.configuration')} />
      <PricingConfigurationForm
        form={form}
        onSubmit={handlePricingConfigurationSubmit}
        props={getInstrumentPricingConfigurationFormProps(
          t,
          {
            configName: 'pricing',
            scope: resource.type === 'portfolio' ? 'Instrument' : 'PortfolioGroupInstrument',
          },
          Boolean(!resource.managable),
        )}
        schema={InstrumentPricingConfigurationSchema}
      >
        {(fields) => <InstrumentPricingConfigurationFormContent fields={fields} />}
      </PricingConfigurationForm>
    </LoadingDataContainer>
  );
}
