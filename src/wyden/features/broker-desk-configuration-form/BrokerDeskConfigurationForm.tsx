import { styled } from '@ui/styled';
import { ExecutionConfiguration as ExecutionComponent } from './ExecutionConfiguration';
import { PricingConfiguration as PricingComponent } from './PricingConfiguration';
import {
  PortfolioConfiguration,
  PortfolioGroupConfiguration,
  PortfolioResponse,
} from '@wyden/services/graphql/generated/graphql';
import { VenueAccountWithVenue } from '@wyden/hooks/useVenueAccounts';

type Props = {
  configuration?: PortfolioConfiguration | PortfolioGroupConfiguration | null;
  parentConfiguration?: PortfolioConfiguration | PortfolioGroupConfiguration | null;
  portfolios: PortfolioResponse[];
  getAccountForId: (id: string | undefined) => VenueAccountWithVenue | undefined;
  getVenueForAccountId: (id: string) => string | null;
};

export function BrokerDeskConfigurationForm({
  configuration,
  parentConfiguration,
  getAccountForId,
  portfolios,
  getVenueForAccountId,
}: Props) {
  return (
    <FormConfigContainer>
      <ExecutionComponent
        configuration={configuration}
        parentConfiguration={parentConfiguration}
        getAccountForId={getAccountForId}
        portfolios={portfolios}
        getVenueForAccountId={getVenueForAccountId}
      />
      <PricingComponent
        configuration={configuration}
        parentConfiguration={parentConfiguration}
        getAccountForId={getAccountForId}
      />
    </FormConfigContainer>
  );
}

export const FormConfigContainer = styled('div')`
  display: flex;
  flex-direction: column;
  min-width: 400px;
  width: 50%;
  gap: 40px;
  max-height: 100%;
  overflow: auto;
`;
