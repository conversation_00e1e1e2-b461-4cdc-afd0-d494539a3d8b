import { graphql } from 'msw';
import { worker } from '../../../../../mocks/browser';
import { aPortfolioResponse } from '../../../services/graphql/generated/mocks';
import { LedgersPO } from './LedgersPO';
import {
  LedgerEntryType,
  Scope,
  UpdateUserDataInputSchema,
} from '../../../services/graphql/generated/graphql';
import { useLedgerFilters } from '../useLedgerFilters';
import { ledgerEntriesCorrectFiltersMock, ledgerEntriesMock } from './mocks';

describe('Ledgers', () => {
  const PO = new LedgersPO();

  beforeEach(() => {
    useLedgerFilters.getState().clear();
    worker.use(
      graphql.query('LedgerEntries', (req, res, ctx) => {
        return res(ctx.data(ledgerEntriesMock));
      }),
    );
  });

  it('renders queried ledgers elements', () => {
    PO.render();

    PO.row(0).id().should('contain.text', '1');
    PO.row(0).type().should('contain.text', 'Deposit');
    PO.row(0).portfolioName().should('contain.text', 'BANK_Portfolio');
    PO.row(0).accountName().should('contain.text', 'BitMEX-testnet1');
    PO.row(0).symbol().should('contain.text', 'BTCUSD');
    PO.row(0).quantity().should('contain.text', '10');
    PO.row(0).currency().should('contain.text', 'BTCUSD');
    PO.row(0).transactionId().should('contain.text', 'transaction-1');
    PO.row(0).updatedAt().should('contain.text', '01/02/2021 00:00:00');
    PO.row(0).balanceBefore().should('contain.text', '9.02');

    PO.row(1).id().should('contain.text', '2');
    PO.row(1).type().should('contain.text', 'Asset Trade Sell');
    PO.row(1).portfolioName().should('contain.text', 'BANK_Portfolio_2');
    PO.row(1).accountName().should('contain.text', 'BitMEX-testnet2');
    PO.row(1).symbol().should('contain.text', 'ADAUSD');
    PO.row(1).quantity().should('contain.text', '20');
    PO.row(1).currency().should('contain.text', 'ADAUSD');
    PO.row(1).transactionId().should('contain.text', 'transaction-2');
    PO.row(1).updatedAt().should('contain.text', '01/02/2021 00:00:00');
    PO.row(1).balanceBefore().should('contain.text', '9.02');

    PO.row(2).id().should('contain.text', '3');
    PO.row(2).type().should('contain.text', 'Asset Trade Buy');
    PO.row(2).portfolioName().should('contain.text', 'BANK_Portfolio_2');
    PO.row(2).accountName().should('contain.text', 'BitMEX-testnet3');
    PO.row(2).symbol().should('contain.text', 'ETHUSD');
    PO.row(2).quantity().should('contain.text', '30');
    PO.row(2).currency().should('contain.text', 'ETHUSD');
    PO.row(2).transactionId().should('contain.text', 'transaction-3');
    PO.row(2).updatedAt().should('contain.text', '01/02/2021 00:00:00');
    PO.row(2).balanceBefore().should('contain.text', '9.02');
  });

  it('adds and removes columns properly', () => {
    PO.render();

    PO.columnSettings.openSettings();
    PO.columnSettings.toggleColumn('Price');
    PO.columnSettings.toggleColumn('Currency');
    PO.columnSettings.saveConfig();

    PO.expectTextToBeVisible('Price');
    PO.expectTextToBeHidden('Currency');
  });

  it.skip('should display persisted filters', () => {
    worker.use(
      graphql.query('UserData', (req, res, ctx) => {
        const userData = {
          timestamp: Date.now(),
          widgets: [
            {
              id: 'test-widget',
              type: 'BASE',
              filters: {
                ledgerEntryType: [LedgerEntryType.AssetTradeBuy, LedgerEntryType.Deposit],
                transactionId: 'some-transaction-id',
                currency: ['BTCUSD', 'ADAUSD'],
                accounts: [],
                portfolios: [],
                wallets: [],
              },
            },
          ],
          workspaces: [
            {
              name: 'current-workspace',
              id: 'current-workspace',
              json: {},
              isTrading: false,
              portfolio: aPortfolioResponse({
                id: 'portfolio_trader_1',
                name: 'portfolio_trader_1',
                scopes: [Scope.Manage, Scope.Trade, Scope.Read],
              }),
            },
          ],
        };
        return res(
          ctx.data({
            userData: { data: JSON.stringify(userData) },
          }),
        );
      }),
    );
    PO.render();
    PO.expectTextToBeVisible('BTCUSD, ADAUSD');
    PO.expectTextToBeVisible('some-transaction-id');
    PO.expectTextToBeVisible(`${LedgerEntryType.AssetTradeBuy}, ${LedgerEntryType.Deposit}`);
  });

  it('should save transaction id to user data', () => {
    worker.use(
      graphql.mutation('UpdateUserData', (req, res, ctx) => {
        const { success, error } = UpdateUserDataInputSchema().safeParse(req.variables.request);
        if (!success) {
          return res(ctx.errors([error]));
        }

        return res(ctx.data({ success: true }));
      }),
    );

    PO.render();
    PO.insertTransactionId('inserted-transaction-id');
  });

  describe('filters', () => {
    beforeEach(() => {
      worker.use(
        graphql.query('UserData', (req, res, ctx) => {
          const userData = {
            timestamp: Date.now(),
            workspaces: [
              {
                name: 'current-workspace',
                id: 'current-workspace',
                json: {},
                isTrading: false,
                portfolio: aPortfolioResponse({
                  id: 'portfolio_trader_1',
                  name: 'portfolio_trader_1',
                  scopes: [Scope.Manage, Scope.Trade, Scope.Read],
                }),
              },
            ],
          };
          return res(
            ctx.data({
              userData: { data: JSON.stringify(userData) },
            }),
          );
        }),
      );
      PO.render();
    });
    it('should display filters after clicking on Add button', () => {
      PO.openFiltersPopover();
      PO.expectTextToBeVisible('Accounts');
      PO.expectTextToBeVisible('Wallets');
      PO.expectTextToBeVisible('Portfolios');
      PO.expectTextToBeVisible('Type');
      PO.expectTextToBeVisible('Transaction ID');
      PO.expectTextToBeVisible('Order ID');
      PO.expectTextToBeVisible('Currency');
      PO.expectTextToBeVisible('Created from');
      PO.expectTextToBeVisible('Created to');
    });

    it('should select accounts filter', () => {
      worker.use(
        graphql.query('LedgerEntries', (req, res, ctx) => {
          const filtersAreCorrectlyAppliedToRequest =
            req.variables.search?.accountName?.includes('BitMEX-testnet1') &&
            req.variables.search?.accountName?.includes('Bank') &&
            req.variables.search?.portfolioId?.includes('portfolio_trader_2') &&
            req.variables.search?.ledgerEntryType?.includes('ASSET_TRADE_BUY') &&
            req.variables.search?.transactionId === 'transaction-1' &&
            req.variables.search?.orderId === 'order-1' &&
            req.variables.search?.currency?.includes('BTC') &&
            typeof req.variables.search?.to === 'string' &&
            typeof req.variables.search?.from === 'string';
          if (filtersAreCorrectlyAppliedToRequest) {
            return res(ctx.data(ledgerEntriesCorrectFiltersMock));
          } else {
            return res(ctx.data(ledgerEntriesMock));
          }
        }),
      );
      PO.selectWalletsFilter('Bank');
      PO.selectAccountsFilter('BitMEX-testnet1');
      PO.selectPortfoliosFilter('portfolio_trader_2');
      PO.selectTransactionIdFilter('transaction-1');
      PO.selectOrderIdFilter('order-1');
      PO.selectCurrencyFilter('BTC');
      PO.selectCreatedFromFilter('3h ago');
      PO.selectCreatedToFilter('3h ago');
      PO.selectTypeFilter('Asset Trade Buy');

      PO.expectTextToBeVisible('transaction-1');
      PO.expectTextToBeVisible('order-1');
      PO.expectTextToBeVisible('Bank');
      PO.expectTextToBeVisible('BTC');

      PO.expectTextToBeVisible('Correct filters');

      PO.resetFilters();
      PO.expectFilterToBeNotSelected('transaction-1');
      PO.expectFilterToBeNotSelected('order-1');
      PO.expectFilterToBeNotSelected('BitMEX-testnet1');
      PO.expectFilterToBeNotSelected('Bank');
      PO.expectFilterToBeNotSelected('portfolio_trader_2');
      PO.expectFilterToBeNotSelected('BTC');
      PO.expectFilterToBeNotSelected('Asset Trade Buy');
    });
  });
});
