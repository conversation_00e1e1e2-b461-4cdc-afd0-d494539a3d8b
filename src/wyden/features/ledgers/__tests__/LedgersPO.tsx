import '../../../../../cypress/support/component';
import { Ledger } from '../Ledgers';
import { ColumnSettingsPO } from '../../grid/__test__/ColumnSettingsPO.cy';
import { FormPO } from '@wyden/features/form/__test__/FormPO.cy';
import { LEDGERS_FILTERS_TEST_ID } from '@wyden/features/ledgers/LedgersFilters';
import { FILTERS_POPOVER_TEST_ID } from '@wyden/features/chip-filters/FiltersChipList';

const COLUMN_NUMBER = 11;

export class LedgersPO {
  columnSettings = new ColumnSettingsPO();
  private readonly formPO = new FormPO();

  render() {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(<Ledger />);
  }

  expectTextToBeVisible(name: string) {
    return cy.findAllByText(name).should('have.length.greaterThan', 0);
  }

  expectTextToBeHidden(name: string) {
    return cy.findAllByText(name).should('not.exist');
  }

  rowContainesText(row: number, text: string) {
    return cy
      .get(`[row-index="${row + 1}"]`)
      .eq(1)
      .within(() => {
        cy.findAllByText(text).should('have.length.greaterThan', 0);
      });
  }

  row(colIndex: number) {
    return {
      id: () => cy.findAllByRole('gridcell').eq(0 + colIndex * COLUMN_NUMBER),
      type: () => cy.findAllByRole('gridcell').eq(1 + colIndex * COLUMN_NUMBER),
      portfolioName: () => cy.findAllByRole('gridcell').eq(2 + colIndex * COLUMN_NUMBER),
      accountName: () => cy.findAllByRole('gridcell').eq(3 + colIndex * COLUMN_NUMBER),
      symbol: () => cy.findAllByRole('gridcell').eq(4 + colIndex * COLUMN_NUMBER),
      quantity: () => cy.findAllByRole('gridcell').eq(5 + colIndex * COLUMN_NUMBER),
      currency: () => cy.findAllByRole('gridcell').eq(6 + colIndex * COLUMN_NUMBER),
      transactionId: () => cy.findAllByRole('gridcell').eq(7 + colIndex * COLUMN_NUMBER),
      updatedAt: () => cy.findAllByRole('gridcell').eq(8 + colIndex * COLUMN_NUMBER),
      balanceBefore: () => cy.findAllByRole('gridcell').eq(9 + colIndex * COLUMN_NUMBER),
      balanceAfter: () => cy.findAllByRole('gridcell').eq(9 + colIndex * COLUMN_NUMBER),
    };
  }
  openFiltersPopover() {
    return cy.get(`[data-testid="${LEDGERS_FILTERS_TEST_ID}"]`).contains('Add').click();
  }

  insertTransactionId(insertedTransactionId: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Transaction ID').click();
    this.formPO.insertByLabelText('Transaction ID', insertedTransactionId);
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Apply').click();
  }

  clickOutsideFiltersPopover() {
    return cy.get('body').click();
  }

  expectFilterToBeNotSelected(value: string) {
    return cy
      .get(`[data-testid="${LEDGERS_FILTERS_TEST_ID}"]`)
      .contains(`${value}`)
      .should('not.exist');
  }

  resetFilters() {
    cy.get(`[data-testid="${LEDGERS_FILTERS_TEST_ID}"]`).contains('Reset').click();
  }

  selectAccountsFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Accounts').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectWalletsFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Wallets').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectPortfoliosFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Portfolios').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectTypeFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Type').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectTransactionIdFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Transaction ID').click();
    this.formPO.insertByLabelText('Transaction ID', value);
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Apply').click();
  }

  selectOrderIdFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Order ID').click();
    this.formPO.insertByLabelText('Order ID', value);
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Apply').click();
  }

  selectCurrencyFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Currency').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectCreatedFromFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Created from').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
  }

  selectCreatedToFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Created to').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
  }
}
