import { LedgerEntryType } from '@wyden/services/graphql/generated/graphql';
import {
  aLedgerEntryConnection,
  aLedgerEntryEdge,
  aLedgerEntryResponse,
  aPageInfo,
} from '@wyden/services/graphql/generated/mocks';

export const ledgerEntriesMock = {
  ledgerEntries: aLedgerEntryConnection({
    edges: [
      aLedgerEntryEdge({
        node: aLedgerEntryResponse({
          id: '1',
          quantity: 10,
          type: LedgerEntryType.Deposit,
          portfolioName: 'BANK_Portfolio',
          currency: 'BTCUSD',
          accountName: 'BitMEX-testnet1',
          transactionId: 'transaction-1',
          symbol: 'BTCUSD',
          updatedAt: *************,
          balanceAfter: 5.34,
          balanceBefore: 9.02,
        }),
      }),
      aLedgerEntryEdge({
        node: aLedgerEntryResponse({
          id: '2',
          quantity: 20,
          type: LedgerEntryType.AssetTradeSell,
          portfolioName: 'BANK_Portfolio_2',
          currency: 'ADAUSD',
          accountName: 'BitMEX-testnet2',
          transactionId: 'transaction-2',
          symbol: 'ADAUSD',
          updatedAt: *************,
          balanceAfter: 5.34,
          balanceBefore: 9.02,
        }),
      }),
      aLedgerEntryEdge({
        node: aLedgerEntryResponse({
          id: '3',
          quantity: 30,
          type: LedgerEntryType.AssetTradeBuy,
          portfolioName: 'BANK_Portfolio_2',
          currency: 'ETHUSD',
          accountName: 'BitMEX-testnet3',
          transactionId: 'transaction-3',
          symbol: 'ETHUSD',
          updatedAt: *************,
          balanceAfter: 5.34,
          balanceBefore: 9.02,
        }),
      }),
    ],
    pageInfo: aPageInfo({ hasNextPage: false, endCursor: null }),
  }),
};

export const ledgerEntriesCorrectFiltersMock = {
  ledgerEntries: aLedgerEntryConnection({
    edges: [
      aLedgerEntryEdge({
        node: aLedgerEntryResponse({
          id: '9',
          quantity: 90,
          type: LedgerEntryType.Deposit,
          portfolioName: 'BANK_Portfolio',
          currency: 'BTCUSD',
          accountName: 'BitMEX-testnet1',
          transactionId: 'Correct filters',
          symbol: 'BTCUSD',
          updatedAt: *************,
          balanceAfter: 5.34,
          balanceBefore: 9.02,
        }),
      }),
    ],
    pageInfo: aPageInfo({ hasNextPage: false, endCursor: null }),
  }),
};
