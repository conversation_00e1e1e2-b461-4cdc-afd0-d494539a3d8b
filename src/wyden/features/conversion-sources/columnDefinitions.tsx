import i18n from '@wyden/i18n';
import { ColDef } from 'ag-grid-community';
import { ActionsRenderer } from './renderers';
import { ConversionSourceResponse } from '../../services/graphql/generated/graphql';
import { buildObjectKeyValidator, getGenericField } from '@wyden/features/grid/utils';

const validKey = buildObjectKeyValidator<ConversionSourceResponse>();

export const venueKey = 'venue';
export const actionsKey = 'actions';
export const priorityKey = validKey('priority');
export const venueAccountKey = validKey('venueAccount');
export const venueAccountNameKey = validKey('venueAccountName');

export const conversionSourcesDataColumnDefinitions: ColDef<ConversionSourceResponse>[] = [
  getGenericField(priorityKey, {
    sortIndex: 1,
    headerName: i18n.t('grid.columnHeader.priority') as string,
    rowDrag: true,
    sortable: false,
  }),
  getGenericField(venueAccountNameKey, {
    sortable: false,
    headerName: i18n.t('grid.columnHeader.account') as string,
  }),
  getGenericField(venueAccountKey, {
    sortable: false,
    headerName: i18n.t('grid.columnHeader.id') as string,
  }),
  getGenericField(actionsKey, {
    headerName: '',
    minWidth: 78,
    width: 78,
    maxWidth: 78,
    cellRenderer: ActionsRenderer,
    pinned: 'right',
    lockPinned: true,
    sortable: false,
  }),
];
