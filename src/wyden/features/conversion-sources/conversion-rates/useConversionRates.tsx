import { useTranslation } from 'react-i18next';

import { ERRORS } from '@wyden/constants';
import { useEventLogs } from '@wyden/features/error-indicators/event-logs/useEventLogs';
import { NetworkEvents } from '@wyden/features/error-indicators/network-indicators/events';
import { useNetworkStore } from '@wyden/features/error-indicators/network-indicators/useNetworkStore';
import { useRateSubscriptionsQuery } from '@wyden/services/graphql/generated/graphql';

export const useConversionRates = () => {
  const { t } = useTranslation();
  const { addEventLog } = useEventLogs();
  const { upsertRequest } = useNetworkStore();

  const {
    data: conversionRatesData,
    error: conversionRatesError,
    loading: conversionRatesLoading,
    refetch: conversionRatesRefetch,
  } = useRateSubscriptionsQuery({
    onError: (err) => {
      upsertRequest('rateSubscriptions', {
        pending: false,
        error: ERRORS.CLIENT_ERROR,
        err,
      });
      addEventLog({
        type: NetworkEvents.CONVERSION_RATE_SUBSCRIPTION,
        message: t('eventLogs.requestFailed', {
          name: t('conversionRates.conversionRatesQuery'),
        }),
        timestamp: Date.now(),
      });
    },
    pollInterval: 5000,
  });

  return {
    conversionRatesLoading,
    conversionRatesRefetch,
    conversionRatesError,
    conversionRatesData: conversionRatesData?.rateSubscriptions,
  };
};
