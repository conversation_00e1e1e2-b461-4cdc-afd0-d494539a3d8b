import { Paragraph } from '@ui/Typography/Paragraph';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { DATE_FORMATS } from '@wyden/date';
import { FiltersChipList } from '@wyden/features/chip-filters/FiltersChipList';
import { DatePicker } from '@wyden/features/chip-filters/filters/DatePicker';
import { TextArea } from '@wyden/features/chip-filters/filters/TextArea';
import { MultiselectList } from '@wyden/features/chip-filters/filters/multiselect/MultiselectList';
import { formatChipLabels } from '@wyden/helpers/stringsHelpers';
import { getSpacing } from '@wyden/utils/styles';
import format from 'date-fns/format';
import { useTranslation } from 'react-i18next';
import { SettlementTransactionsAccountsList } from '../../chip-filters/AccountsListFilter';
import { SettlementTransactionsPortfoliosList } from '../../chip-filters/PortfoliosListFilter';
import { SettlementTransactionsWalletsList } from '../../chip-filters/WalletsListFilter';
import { RadioSelector } from '../../chip-filters/filters/RadioSelector';
import { useCurrencies } from '../../currencies/useCurrencies';
import {
  SettlementTransactionsFilterLists,
  useSettlementTransactionsFilters,
} from './useSettlementTransactionFilters';

export const SETTLEMENT_TRANSACTIONS_FILTERS_TEST_ID = 'settlement-transactions-filters';

const ColumnList = () => {
  const { setSelectedList } = useSettlementTransactionsFilters();
  const { t } = useTranslation();

  return (
    <>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(SettlementTransactionsFilterLists.ACCOUNTS_LIST);
        }}
      >
        {t('filters.accounts')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(SettlementTransactionsFilterLists.WALLETS_LIST);
        }}
      >
        {t('filters.wallets')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(SettlementTransactionsFilterLists.PORTFOLIOS_LIST);
        }}
      >
        {t('filters.portfolios')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(SettlementTransactionsFilterLists.CREATED_FROM_INPUT);
        }}
      >
        {t('transactionsHistory.createdFrom')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(SettlementTransactionsFilterLists.CREATED_TO_INPUT);
        }}
      >
        {t('transactionsHistory.createdTo')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(SettlementTransactionsFilterLists.ORDER_ID_INPUT);
        }}
      >
        {t('transactionsHistory.orderId')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(SettlementTransactionsFilterLists.ROOT_EXECUTION_ID_INPUT);
        }}
      >
        {t('transactionsHistory.rootExecutionId')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(SettlementTransactionsFilterLists.EXECUTION_ID_INPUT);
        }}
      >
        {t('transactionsHistory.executionId')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(SettlementTransactionsFilterLists.VENUE_EXECUTION_ID_INPUT);
        }}
      >
        {t('transactionsHistory.venueExecutionId')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(SettlementTransactionsFilterLists.CURRENCY_INPUT);
        }}
      >
        {t('transactionsHistory.currency')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(SettlementTransactionsFilterLists.IS_SETTLED_INPUT);
        }}
      >
        {t('settlement.transactions.settled')}
      </CategoryListParagraph>
    </>
  );
};

const CategoryListParagraph = styled(Paragraph)`
  padding: ${getSpacing(1)} ${getSpacing(2)};
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
  cursor: pointer;
`;

interface ListProps {
  close: (e: { stopPropagation: () => void }) => void;
}

const CreatedFromInput = () => {
  const { t } = useTranslation();
  const { setCreatedFrom } = useSettlementTransactionsFilters();
  const { filters } = useSettlementTransactionsFilters();
  const selectedCreatedTo = filters.createdTo ?? undefined;
  const selectedCreatedFrom = filters.createdFrom ?? undefined;

  return (
    <DatePicker
      headerText={t('common.createdFrom')}
      onChange={(date) => {
        setCreatedFrom(date);
      }}
      maxDate={selectedCreatedTo}
      selectedDate={selectedCreatedFrom}
      label={t('common.createdFrom')}
    />
  );
};

const CreatedToInput = () => {
  const { t } = useTranslation();
  const { setCreatedTo } = useSettlementTransactionsFilters();
  const { filters } = useSettlementTransactionsFilters();
  const selectedCreatedTo = filters.createdTo ?? undefined;
  const selectedCreatedFrom = filters.createdFrom ?? undefined;

  return (
    <DatePicker
      headerText={t('common.createdTo')}
      onChange={(date) => {
        setCreatedTo(date);
      }}
      selectedDate={selectedCreatedTo}
      minDate={selectedCreatedFrom}
      label={t('common.createdTo')}
    />
  );
};

const OrderIdInput = (props: ListProps) => {
  const { t } = useTranslation();
  const {
    setOrderId,
    filters: { orderId },
  } = useSettlementTransactionsFilters();
  return (
    <TextArea
      value={orderId || ''}
      headerText={t('transactionsHistory.orderId')}
      onApply={setOrderId}
      close={props.close}
    />
  );
};

const RootExecutionIdInput = (props: ListProps) => {
  const { t } = useTranslation();
  const {
    setRootExecutionId,
    filters: { rootExecutionId },
  } = useSettlementTransactionsFilters();
  return (
    <TextArea
      value={rootExecutionId || ''}
      headerText={t('transactionsHistory.rootExecutionId')}
      onApply={setRootExecutionId}
      close={props.close}
    />
  );
};

const ExecutionIdInput = (props: ListProps) => {
  const { t } = useTranslation();
  const {
    setExecutionId,
    filters: { executionId },
  } = useSettlementTransactionsFilters();
  return (
    <TextArea
      value={executionId || ''}
      headerText={t('transactionsHistory.executionId')}
      onApply={setExecutionId}
      close={props.close}
    />
  );
};

const VenueExecutionIdInput = (props: ListProps) => {
  const { t } = useTranslation();
  const {
    setVenueExecutionId,
    filters: { venueExecutionId },
  } = useSettlementTransactionsFilters();
  return (
    <TextArea
      value={venueExecutionId || ''}
      headerText={t('transactionsHistory.venueExecutionId')}
      onApply={setVenueExecutionId}
      close={props.close}
    />
  );
};

const CurrencyInput = (props: ListProps) => {
  const { t } = useTranslation();

  const {
    setCurrency,
    filters: { currency },
  } = useSettlementTransactionsFilters();

  const { loading, currencies } = useCurrencies();
  const currencyOptions = currencies.map((currency) => currency.symbol);

  return (
    <MultiselectList
      autocomplete
      headerText={t('transactionsHistory.currency')}
      options={currencyOptions}
      loading={loading}
      filters={currency}
      setFilters={(filters) => {
        setCurrency(filters);
      }}
      close={props.close}
    />
  );
};

const IsSettledInput = (props: ListProps) => {
  const { t } = useTranslation();
  const {
    setIsSettled,
    filters: { settled },
  } = useSettlementTransactionsFilters();

  return (
    <RadioSelector
      headerText={t('settlement.transactions.settled')}
      options={[
        { label: t('settlement.transactions.settled'), value: true },
        { label: t('settlement.transactions.unsettled'), value: false },
      ]}
      value={settled}
      onChange={setIsSettled}
      close={props.close}
    />
  );
};

const lists = {
  [SettlementTransactionsFilterLists.COLUMN_LIST]: ColumnList,
  [SettlementTransactionsFilterLists.ACCOUNTS_LIST]: SettlementTransactionsAccountsList,
  [SettlementTransactionsFilterLists.WALLETS_LIST]: SettlementTransactionsWalletsList,
  [SettlementTransactionsFilterLists.PORTFOLIOS_LIST]: SettlementTransactionsPortfoliosList,
  [SettlementTransactionsFilterLists.CREATED_FROM_INPUT]: CreatedFromInput,
  [SettlementTransactionsFilterLists.CREATED_TO_INPUT]: CreatedToInput,
  [SettlementTransactionsFilterLists.ORDER_ID_INPUT]: OrderIdInput,
  [SettlementTransactionsFilterLists.ROOT_EXECUTION_ID_INPUT]: RootExecutionIdInput,
  [SettlementTransactionsFilterLists.EXECUTION_ID_INPUT]: ExecutionIdInput,
  [SettlementTransactionsFilterLists.VENUE_EXECUTION_ID_INPUT]: VenueExecutionIdInput,
  [SettlementTransactionsFilterLists.CURRENCY_INPUT]: CurrencyInput,
  [SettlementTransactionsFilterLists.IS_SETTLED_INPUT]: IsSettledInput,
};

export const SettlementTransactionsFilters = () => {
  const { t } = useTranslation();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const isProvidedAndNotEmpty = (val: any): boolean => {
    if (val === undefined || val === null || val === '') return false;
    if (typeof val === 'string' || Array.isArray(val)) return val.length > 0;
    return true;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const isDefinedOrNonEmptyArray = (val: any) =>
    Array.isArray(val) ? val.length > 0 : val !== undefined && val !== null;

  const { filters, clear, selectedList, setSelectedList, columnListSelected, onRemoveFilter } =
    useSettlementTransactionsFilters((state) => {
      // Define type-specific transformers
      const transformers = {
        portfolios: (items) =>
          items
            .map((item) => (item && typeof item === 'object' && 'name' in item ? item.name : null))
            .filter(Boolean),

        accounts: (items) =>
          items
            .map((item) =>
              item && typeof item === 'object' && 'venueAccountName' in item
                ? item.venueAccountName
                : null,
            )
            .filter(Boolean),

        wallets: (items) =>
          items
            .map((item) =>
              item && typeof item === 'object' && 'venueAccountName' in item
                ? item.venueAccountName
                : null,
            )
            .filter(Boolean),

        createdFrom: (value) =>
          [
            value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)))
              ? format(new Date(value), DATE_FORMATS.DEFAULT_WITH_TIME)
              : null,
          ].filter(Boolean),

        createdTo: (value) =>
          [
            value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)))
              ? format(new Date(value), DATE_FORMATS.DEFAULT_WITH_TIME)
              : null,
          ].filter(Boolean),

        currency: (value) => value,

        // Default transformer for other filter types
        default: (value) => (isProvidedAndNotEmpty(value) ? [value] : []),
      };

      const filters = Object.entries(state.filters)
        .map(([key, value]) => {
          // Handle array types
          if (Array.isArray(value) && ['portfolios', 'accounts', 'wallets'].includes(key)) {
            return {
              key,
              values: transformers[key as keyof typeof transformers](value),
            };
          }

          // Handle date types
          if (['createdFrom', 'createdTo'].includes(key) && value) {
            return {
              key,
              values: transformers[key as keyof typeof transformers](value),
            };
          }

          // Handle special direct values
          if (['transactionType', 'currency'].includes(key)) {
            return {
              key,
              values: transformers[key as keyof typeof transformers](value),
            };
          }

          // Default case
          return {
            key,
            values: transformers.default(value),
          };
        })
        .filter(({ values }) => isDefinedOrNonEmptyArray(values));

      return {
        ...state,
        filters,
      };
    });

  const List = lists[selectedList];
  const filtersKeyTranslation = {
    [SettlementTransactionsFilterLists.ACCOUNTS_LIST]: t('filters.accounts'),
    [SettlementTransactionsFilterLists.WALLETS_LIST]: t('filters.wallets'),
    [SettlementTransactionsFilterLists.PORTFOLIOS_LIST]: t('filters.portfolios'),
    [SettlementTransactionsFilterLists.CREATED_FROM_INPUT]: t('transactionsHistory.createdFrom'),
    [SettlementTransactionsFilterLists.CREATED_TO_INPUT]: t('transactionsHistory.createdTo'),
    [SettlementTransactionsFilterLists.ORDER_ID_INPUT]: t('transactionsHistory.orderId'),
    [SettlementTransactionsFilterLists.ROOT_EXECUTION_ID_INPUT]: t(
      'transactionsHistory.rootExecutionId',
    ),
    [SettlementTransactionsFilterLists.EXECUTION_ID_INPUT]: t('transactionsHistory.executionId'),
    [SettlementTransactionsFilterLists.VENUE_EXECUTION_ID_INPUT]: t(
      'transactionsHistory.venueExecutionId',
    ),
    [SettlementTransactionsFilterLists.CURRENCY_INPUT]: t('transactionsHistory.currency'),
    [SettlementTransactionsFilterLists.IS_SETTLED_INPUT]: t('settlement.transactions.settled'),
  };
  const selectedFilters = filters.filter(({ key }) => key === selectedList);
  return (
    <Filters data-testid={SETTLEMENT_TRANSACTIONS_FILTERS_TEST_ID}>
      <FiltersChipList
        clearSubFilters={() => {
          columnListSelected();
        }}
        filtersCount={filters.length}
        getGroupedFilters={(editMode) =>
          filters.filter(({ key }) => {
            if (selectedList === SettlementTransactionsFilterLists.COLUMN_LIST || editMode) {
              return true;
            }
            return key !== selectedList;
          })
        }
        onEdit={(key) => {
          setSelectedList(key as SettlementTransactionsFilterLists);
        }}
        onRemove={(key) => onRemoveFilter(key)}
        onReset={clear}
        keySelectionModeOn={selectedList === SettlementTransactionsFilterLists.COLUMN_LIST}
        selectedKey={selectedList}
        selectedValues={selectedFilters.map(({ values }) => values).flat()}
        isKeyAdded={
          selectedList !== SettlementTransactionsFilterLists.COLUMN_LIST &&
          filters.some(({ key }) => key === selectedList)
        }
        translateKey={(key) => {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          return key in filtersKeyTranslation ? filtersKeyTranslation[key] : key;
        }}
        transformDisplayChips={formatChipLabels}
      >
        {(close) => <List close={close} />}
      </FiltersChipList>
    </Filters>
  );
};

const Filters = styled('div')`
  display: flex;
  flex-direction: column;
  padding: ${getSpacing(1)};
`;
