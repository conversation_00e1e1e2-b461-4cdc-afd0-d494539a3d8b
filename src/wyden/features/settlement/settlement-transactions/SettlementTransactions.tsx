import SyncIcon from '@mui/icons-material/Sync';
import { Tooltip } from '@mui/material';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { FullScreenButton } from '@wyden/components/FullScreenButton';
import { TutorialButton } from '@wyden/features/tutorial/TutorialButton';
import { transactionsSteps } from '@wyden/features/tutorial/tutorialData';
import { Widget, WidgetBody } from '@wyden/features/widgets-renderer/widget/Widget';
import { WidgetHeader } from '@wyden/features/widgets-renderer/widget/WidgetHeader';
import { useAnchor } from '@wyden/hooks/useAnchor';
import { SettlementStreetCashTradeSchema } from '@wyden/services/graphql/generated/graphql';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getBrowserLocale } from '../../../helpers/locale';
import { ColumnManagerProvider } from '../../grid/ColumnManager';
import { ColumnSettings } from '../../grid/ColumnSettings';
import { InfiniteScrollGridWrapper } from '../../grid/InfiniteScrollGridWrapper';
import {
  defaultSettlementTransactionsColumnVisibleFields,
  useSettlementTransactionsColumnDefinitions,
} from './columnDefinitions';
import { SettlementTransactionsFilters } from './SettlementTransactionsFilters';
import { SettlementTransactionsGrid } from './SettlementTransactionsGrid';
import { useSettlementTransactions } from './useSettlementTransactions';
import { useLiveSettlementRunsStore } from '../settlement-opened/useLiveSettlementRunsStore';
import { Button } from '@ui/Button';

const CONTAINER_NAME = 'settlement-transactions-widget';
export const SETTLEMENT_TRANSACTIONS_GRID_TEST_ID = 'settlement-transactions-grid';

export const SettlementTransactions = () => {
  const { t } = useTranslation();
  const locale = getBrowserLocale();
  const [loadedTime, setLoadedTime] = useState(new Date().toLocaleTimeString(locale));
  const columnSettingsModalProps = useAnchor('column-settings');
  const [hasNewData, setHasNewData] = useState(false);
  const { onGridReady, refresh, initialLoading, error, showNoRows, reloading } =
    useSettlementTransactions();

  useEffect(() => {
    useLiveSettlementRunsStore.subscribe(() => setHasNewData(true));
  }, []);

  return (
    <ColumnManagerProvider
      type={'BASE'}
      defaultVisibleFields={defaultSettlementTransactionsColumnVisibleFields}
      validationSchema={SettlementStreetCashTradeSchema()}
      columnDefinitions={useSettlementTransactionsColumnDefinitions()}
    >
      <Widget actionsVisible={columnSettingsModalProps.open || hasNewData}>
        <WidgetHeader>
          <WidgetHeaderSection>
            <Tooltip
              data-testid="loaded-section"
              arrow
              title={t('widget.loaded', {
                time: loadedTime,
              })}
            >
              <div>
                <StyledRefreshButton
                  $hasNewData={hasNewData}
                  data-testid="sync-positions-data"
                  onClick={() => {
                    refresh();
                    setLoadedTime(new Date().toLocaleTimeString(locale));
                    setHasNewData(false);
                  }}
                >
                  <SyncIcon />
                  <span>{t('common.refreshData')}</span>
                </StyledRefreshButton>
              </div>
            </Tooltip>
            <ColumnSettings {...columnSettingsModalProps} />
            <TutorialButton steps={transactionsSteps} />
            <FullScreenButton />
          </WidgetHeaderSection>
        </WidgetHeader>
        <WidgetBody className={CONTAINER_NAME}>
          <SettlementTransactionsFilters />
          <InfiniteScrollGridWrapper
            error={error}
            initialLoading={initialLoading}
            reloading={reloading}
            showNoRows={showNoRows}
            refresh={refresh}
            noRowsInfo={t('transactionsHistory.noTransactions')}
          >
            <SettlementTransactionsGrid
              onGridReady={onGridReady}
              isDataLoading={!initialLoading && reloading}
              disableSelections
            />
          </InfiniteScrollGridWrapper>
        </WidgetBody>
      </Widget>
    </ColumnManagerProvider>
  );
};

const WidgetHeaderSection = styled('div')`
  display: flex;
  align-items: center;
`;

const StyledRefreshButton = styled(Button)<{ $hasNewData: boolean }>`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary} !important;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  white-space: nowrap;
  min-height: ${({ $hasNewData }) => ($hasNewData ? '40px' : '30px')};
  border-radius: 50px;
  position: relative;
  display: flex;
  max-height: 30px;
  align-items: center;
  justify-content: ${({ $hasNewData }) => ($hasNewData ? 'flex-start' : 'center')};
  gap: 2px;
  margin-left: ${({ $hasNewData }) => ($hasNewData ? '0' : '2px')};

  &:hover {
    background-color: ${({ $hasNewData, theme }) =>
      $hasNewData ? undefined : color[theme.palette.mode].fillsElementsFillHover} !important;
    color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary} !important;
  }

  aspect-ratio: ${({ $hasNewData }) => ($hasNewData ? 'auto' : '1/1')};

  @keyframes slideIn {
    0% {
      width: 30px;
      min-width: 30px;
      aspect-ratio: 1/1;
      padding: 0;
      margin-left: 2px;
      justify-content: center;
    }
    100% {
      width: 130px;
      min-width: 130px;
      height: auto;
      min-height: 30px;
      aspect-ratio: auto;
      padding: 8px 12px 8px 14px;
      margin-left: 0;
      justify-content: flex-start;
    }
  }

  @keyframes slideOut {
    0% {
      width: 130px;
      min-width: 130px;
      height: auto;
      min-height: 40px;
      aspect-ratio: auto;
      padding: 8px 12px 8px 14px;
      margin-left: 0;
      justify-content: flex-start;
    }
    100% {
      width: 30px;
      min-width: 30px;
      height: 30px;
      min-height: 30px;
      aspect-ratio: 1/1;
      padding: 0;
      margin-left: 2px;
      justify-content: center;
      background-color: transparent;

      hover {
        background-color: ${({ theme }) =>
          color[theme.palette.mode].fillsAccentFillAccentLight} !important;
      }
    }
  }

  animation: ${({ $hasNewData }) =>
    $hasNewData
      ? 'slideIn 0.45s cubic-bezier(0.25, 0.1, 0.25, 1) forwards'
      : 'slideOut 0.45s cubic-bezier(0.25, 0.1, 0.25, 1) forwards'};

  svg {
    margin-right: ${({ $hasNewData }) => ($hasNewData ? '4px' : '0')};
    transition: all 0.3s ease-in-out;
    position: relative;
    z-index: 2;
    flex-shrink: 0;
    font-size: ${({ $hasNewData }) => ($hasNewData ? '20px' : '18px')};

    path {
      fill: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
    }
  }

  span {
    opacity: ${({ $hasNewData }) => ($hasNewData ? '1' : '0')};
    overflow: hidden;
    transition:
      opacity 0.3s ease-in-out,
      max-width 0.45s cubic-bezier(0.25, 0.1, 0.25, 1);
    max-width: ${({ $hasNewData }) => ($hasNewData ? '100px' : '0')};
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    animation: ${({ $hasNewData }) =>
      $hasNewData
        ? 'fadeIn 0.45s cubic-bezier(0.25, 0.1, 0.25, 1) forwards'
        : 'fadeOut 0.35s cubic-bezier(0.25, 0.1, 0.25, 1) forwards'};
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
      transform: translateX(8px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fadeOut {
    0% {
      opacity: 1;
      transform: translateX(0);
    }
    100% {
      opacity: 0;
      transform: translateX(8px);
    }
  }
`;
