import {
  aPageInfo,
  aSettlementStreetCashTrade,
  aSettlementTransactionConnection,
  aSettlementTransactionEdge,
} from '@wyden/services/graphql/generated/mocks';

export const SETTLEMENT_TRANSACTIONS_MOCK = {
  data: {
    settlementTransactions: aSettlementTransactionConnection({
      edges: [
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: false,
            orderId: 'order-1',
            uuid: 'transaction-1',
            baseCurrency: 'BTC',
            portfolioName: 'Portfolio 1',
            description: 'Buy',
            quantity: 1.5,
            dateTime: '2021-03-20T10:00:00.000Z',
            price: 50000,
            currency: 'USD',
            portfolioId: 'portfolio-1',
            venueAccount: 'venue-1',
            venueAccountName: 'Venue 1',
            intOrderId: 'int-order-1',
            extOrderId: 'ext-order-1',
            executionId: 'execution-1',
            venueExecutionId: 'venue-execution-1',
            rootOrderId: 'root-order-1',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-1',
        }),
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: false,
            orderId: 'order-2',
            uuid: 'transaction-2',
            baseCurrency: 'ETH',
            portfolioName: 'Portfolio 2',
            description: 'Sell',
            quantity: 10,
            dateTime: '2021-03-20T11:00:00.000Z',
            price: 2000,
            currency: 'USD',
            portfolioId: 'portfolio-2',
            venueAccount: 'venue-2',
            venueAccountName: 'Venue 2',
            intOrderId: 'int-order-2',
            extOrderId: 'ext-order-2',
            executionId: 'execution-2',
            venueExecutionId: 'venue-execution-2',
            rootOrderId: 'root-order-2',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-2',
        }),
      ],
      pageInfo: aPageInfo({
        hasNextPage: false,
        endCursor: 'cursor-2',
      }),
    }),
  },
};

export const SETTLEMENT_TRANSACTIONS_MOCK_WITH_CORRECT_FILTERS = {
  data: {
    settlementTransactions: aSettlementTransactionConnection({
      edges: [
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: false,
            orderId: 'Correct filters',
            uuid: 'transaction-1',
            baseCurrency: 'BTC',
            portfolioName: 'Portfolio 1',
            description: 'Buy',
            quantity: 1.5,
            dateTime: '2021-03-20T10:00:00.000Z',
            price: 50000,
            currency: 'USD',
            portfolioId: 'portfolio-1',
            venueAccount: 'venue-1',
            venueAccountName: 'Venue 1',
            intOrderId: 'int-order-1',
            extOrderId: 'ext-order-1',
            executionId: 'execution-1',
            venueExecutionId: 'venue-execution-1',
            rootOrderId: 'root-order-1',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-1',
        }),
      ],
      pageInfo: aPageInfo({
        hasNextPage: false,
        endCursor: 'cursor-2',
      }),
    }),
  },
};

export const SETTLEMENT_TRANSACTIONS_WITH_SELECTIONS_MOCK = {
  data: {
    settlementTransactions: aSettlementTransactionConnection({
      edges: [
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: true,
            orderId: 'order-1',
            uuid: 'transaction-1',
            baseCurrency: 'BTC',
            portfolioName: 'Portfolio 1',
            description: 'Buy',
            quantity: 1.5,
            dateTime: '2021-03-20T10:00:00.000Z',
            price: 50000,
            currency: 'USD',
            portfolioId: 'portfolio-1',
            venueAccount: 'venue-1',
            venueAccountName: 'Venue 1',
            intOrderId: 'int-order-1',
            extOrderId: 'ext-order-1',
            executionId: 'execution-1',
            venueExecutionId: 'venue-execution-1',
            rootOrderId: 'root-order-1',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-1',
        }),
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: false,
            orderId: 'order-2',
            uuid: 'transaction-2',
            baseCurrency: 'ETH',
            portfolioName: 'Portfolio 2',
            description: 'Sell',
            quantity: 10,
            dateTime: '2021-03-20T11:00:00.000Z',
            price: 2000,
            currency: 'USD',
            portfolioId: 'portfolio-2',
            venueAccount: 'venue-2',
            venueAccountName: 'Venue 2',
            intOrderId: 'int-order-2',
            extOrderId: 'ext-order-2',
            executionId: 'execution-2',
            venueExecutionId: 'venue-execution-2',
            rootOrderId: 'root-order-2',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-2',
        }),
      ],
      pageInfo: aPageInfo({
        hasNextPage: false,
        endCursor: 'cursor-2',
      }),
    }),
  },
};

export const SETTLEMENT_TRANSACTIONS_SEVEN_SELECTED_MOCK = {
  data: {
    settlementTransactions: aSettlementTransactionConnection({
      edges: [
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: true,
            orderId: 'order-1',
            uuid: 'transaction-1',
            baseCurrency: 'BTC',
            portfolioName: 'Portfolio 1',
            description: 'Buy',
            quantity: 1.5,
            dateTime: '2021-03-20T10:00:00.000Z',
            price: 50000,
            currency: 'USD',
            portfolioId: 'portfolio-1',
            venueAccount: 'venue-1',
            venueAccountName: 'Venue 1',
            intOrderId: 'int-order-1',
            extOrderId: 'ext-order-1',
            executionId: 'execution-1',
            venueExecutionId: 'venue-execution-1',
            rootOrderId: 'root-order-1',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-1',
        }),
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: true,
            orderId: 'order-2',
            uuid: 'transaction-2',
            baseCurrency: 'ETH',
            portfolioName: 'Portfolio 2',
            description: 'Buy',
            quantity: 20,
            dateTime: '2021-03-20T10:30:00.000Z',
            price: 2000,
            currency: 'USD',
            portfolioId: 'portfolio-2',
            venueAccount: 'venue-2',
            venueAccountName: 'Venue 2',
            intOrderId: 'int-order-2',
            extOrderId: 'ext-order-2',
            executionId: 'execution-2',
            venueExecutionId: 'venue-execution-2',
            rootOrderId: 'root-order-2',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-2',
        }),
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: true,
            orderId: 'order-3',
            uuid: 'transaction-3',
            baseCurrency: 'USDT',
            portfolioName: 'Portfolio 3',
            description: 'Buy',
            quantity: 5000,
            dateTime: '2021-03-20T11:00:00.000Z',
            price: 1,
            currency: 'USD',
            portfolioId: 'portfolio-3',
            venueAccount: 'venue-3',
            venueAccountName: 'Venue 3',
            intOrderId: 'int-order-3',
            extOrderId: 'ext-order-3',
            executionId: 'execution-3',
            venueExecutionId: 'venue-execution-3',
            rootOrderId: 'root-order-3',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-3',
        }),
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: true,
            orderId: 'order-4',
            uuid: 'transaction-4',
            baseCurrency: 'XRP',
            portfolioName: 'Portfolio 4',
            description: 'Buy',
            quantity: 1000,
            dateTime: '2021-03-20T12:00:00.000Z',
            price: 0.5,
            currency: 'USD',
            portfolioId: 'portfolio-4',
            venueAccount: 'venue-4',
            venueAccountName: 'Venue 4',
            intOrderId: 'int-order-4',
            extOrderId: 'ext-order-4',
            executionId: 'execution-4',
            venueExecutionId: 'venue-execution-4',
            rootOrderId: 'root-order-4',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-4',
        }),
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: true,
            orderId: 'order-5',
            uuid: 'transaction-5',
            baseCurrency: 'ADA',
            portfolioName: 'Portfolio 5',
            description: 'Buy',
            quantity: 2000,
            dateTime: '2021-03-20T13:00:00.000Z',
            price: 0.3,
            currency: 'USD',
            portfolioId: 'portfolio-5',
            venueAccount: 'venue-5',
            venueAccountName: 'Venue 5',
            intOrderId: 'int-order-5',
            extOrderId: 'ext-order-5',
            executionId: 'execution-5',
            venueExecutionId: 'venue-execution-5',
            rootOrderId: 'root-order-5',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-5',
        }),
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: true,
            orderId: 'order-6',
            uuid: 'transaction-6',
            baseCurrency: 'DOT',
            portfolioName: 'Portfolio 6',
            description: 'Buy',
            quantity: 100,
            dateTime: '2021-03-20T14:00:00.000Z',
            price: 20,
            currency: 'USD',
            portfolioId: 'portfolio-6',
            venueAccount: 'venue-6',
            venueAccountName: 'Venue 6',
            intOrderId: 'int-order-6',
            extOrderId: 'ext-order-6',
            executionId: 'execution-6',
            venueExecutionId: 'venue-execution-6',
            rootOrderId: 'root-order-6',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-6',
        }),
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: true,
            orderId: 'order-7',
            uuid: 'transaction-7',
            baseCurrency: 'LINK',
            portfolioName: 'Portfolio 7',
            description: 'Buy',
            quantity: 50,
            dateTime: '2021-03-20T15:00:00.000Z',
            price: 30,
            currency: 'USD',
            portfolioId: 'portfolio-7',
            venueAccount: 'venue-7',
            venueAccountName: 'Venue 7',
            intOrderId: 'int-order-7',
            extOrderId: 'ext-order-7',
            executionId: 'execution-7',
            venueExecutionId: 'venue-execution-7',
            rootOrderId: 'root-order-7',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-7',
        }),
      ],
      pageInfo: aPageInfo({
        hasNextPage: false,
        endCursor: null,
      }),
    }),
  },
};

export const SETTLEMENT_TRANSACTIONS_THREE_SELECTED_MOCK = {
  data: {
    settlementTransactions: aSettlementTransactionConnection({
      edges: [
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: true,
            orderId: 'order-1',
            uuid: 'transaction-1',
            baseCurrency: 'BTC',
            portfolioName: 'Portfolio 1',
            description: 'Buy',
            quantity: 1.5,
            dateTime: '2021-03-20T10:00:00.000Z',
            price: 50000,
            currency: 'USD',
            portfolioId: 'portfolio-1',
            venueAccount: 'venue-1',
            venueAccountName: 'Venue 1',
            intOrderId: 'int-order-1',
            extOrderId: 'ext-order-1',
            executionId: 'execution-1',
            venueExecutionId: 'venue-execution-1',
            rootOrderId: 'root-order-1',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-1',
        }),
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: false,
            orderId: 'order-2',
            uuid: 'transaction-2',
            baseCurrency: 'ETH',
            portfolioName: 'Portfolio 2',
            description: 'Buy',
            quantity: 20,
            dateTime: '2021-03-20T10:30:00.000Z',
            price: 2000,
            currency: 'USD',
            portfolioId: 'portfolio-2',
            venueAccount: 'venue-2',
            venueAccountName: 'Venue 2',
            intOrderId: 'int-order-2',
            extOrderId: 'ext-order-2',
            executionId: 'execution-2',
            venueExecutionId: 'venue-execution-2',
            rootOrderId: 'root-order-2',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-2',
        }),
        aSettlementTransactionEdge({
          node: aSettlementStreetCashTrade({
            selected: true,
            orderId: 'order-3',
            uuid: 'transaction-3',
            baseCurrency: 'USDT',
            portfolioName: 'Portfolio 3',
            description: 'Buy',
            quantity: 5000,
            dateTime: '2021-03-20T11:00:00.000Z',
            price: 1,
            currency: 'USD',
            portfolioId: 'portfolio-3',
            venueAccount: 'venue-3',
            venueAccountName: 'Venue 3',
            intOrderId: 'int-order-3',
            extOrderId: 'ext-order-3',
            executionId: 'execution-3',
            venueExecutionId: 'venue-execution-3',
            rootOrderId: 'root-order-3',
            settled: true,
            settledDateTime: *************,
          }),
          cursor: 'cursor-3',
        }),
      ],
      pageInfo: aPageInfo({
        hasNextPage: false,
        endCursor: null,
      }),
    }),
  },
};
