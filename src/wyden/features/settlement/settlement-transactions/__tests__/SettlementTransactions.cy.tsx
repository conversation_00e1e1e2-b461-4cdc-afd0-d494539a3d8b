import '../../../../../../cypress/support/component';
import { SettlementTransactionsPO } from './SettlementTransactionsPO.cy';
import {
  SETTLEMENT_TRANSACTIONS_MOCK,
  SETTLEMENT_TRANSACTIONS_MOCK_WITH_CORRECT_FILTERS,
} from './mocks';
import { graphql } from 'msw';
import { worker } from '../../../../../../mocks/browser';

describe('SettlementTransactions', () => {
  const settlementTransactionsPO = new SettlementTransactionsPO();

  beforeEach(() => {
    settlementTransactionsPO.render();
  });

  it('should scroll down to all records in settlement transactions', () => {
    settlementTransactionsPO.scrollDownToEnd();
  });

  it('should display settlement transactions columns', () => {
    settlementTransactionsPO.expectAGGridTextToBeVisible('Order ID');
    settlementTransactionsPO.expectAGGridTextToBeVisible('Transaction ID');
    settlementTransactionsPO.expectAGgridTextToBeVisible('Fee');
    settlementTransactionsPO.expectAGgridTextToBeVisible('Settled');
    settlementTransactionsPO.expectAGgridTextToBeVisible('Base Currency');
    settlementTransactionsPO.expectAGgridTextToBeVisible('Currency');
    settlementTransactionsPO.expectAGgridTextToBeVisible('Portfolio Name');
    settlementTransactionsPO.expectAGgridTextToBeVisible('Account Name');
    settlementTransactionsPO.expectAGgridTextToBeVisible('Quantity');
  });

  it('should display selected filters', () => {
    worker.use(
      graphql.query('SettlementTransactions', (req, res, ctx) => {
        const { search } = req.variables;

        const isAccountPresentInSearchParams = search?.accountId?.includes('BitMEX-testnet1');
        const isWalletPresentInSearchParams = search?.accountId?.includes('Bank');
        const isCurrencyPresentInSearchParams = search?.currency?.includes('USD');
        const isPortfolioPresentInSearchParams =
          search?.portfolioId?.includes('portfolio_trader_1');
        const isCreatedFromPresentInSearchParams = typeof search?.from === 'string';
        const isCreatedToPresentInSearchParams = typeof search?.to === 'string';
        const isOrderIdPresentInSearchParams = search?.orderId === 'Test Order ID';
        const isExecutionIdPresentInSearchParams = search?.executionId === 'Test Execution ID';
        const isVenueExecutionIdPresentInSearchParams =
          search?.venueExecutionId === 'Test Venue Execution Id';
        const isRootExecutionIdPresentInSearchParams =
          search?.rootExecutionId === 'Test Root Execution Id';

        const filtersAreCorrectlyAppliedToRequest =
          isOrderIdPresentInSearchParams &&
          isAccountPresentInSearchParams &&
          isCurrencyPresentInSearchParams &&
          isWalletPresentInSearchParams &&
          isCreatedFromPresentInSearchParams &&
          isCreatedToPresentInSearchParams &&
          isExecutionIdPresentInSearchParams &&
          isVenueExecutionIdPresentInSearchParams &&
          isRootExecutionIdPresentInSearchParams &&
          isPortfolioPresentInSearchParams;

        if (filtersAreCorrectlyAppliedToRequest) {
          return res(ctx.data(SETTLEMENT_TRANSACTIONS_MOCK_WITH_CORRECT_FILTERS.data));
        } else {
          return res(ctx.data(SETTLEMENT_TRANSACTIONS_MOCK.data));
        }
      }),
    );

    settlementTransactionsPO.selectAccountsFilter('BitMEX-testnet1');
    settlementTransactionsPO.selectPortfoliosFilter('portfolio_trader_1');
    settlementTransactionsPO.selectWalletsFilter('Bank');
    settlementTransactionsPO.selectDateFilter('Created from', '3h ago');
    settlementTransactionsPO.selectDateFilter('Created to', '3h ago');
    settlementTransactionsPO.setOrderIdFilter('Correct filters');
    settlementTransactionsPO.insertOrderIDByLabel('Execution ID', 'Test Execution ID');
    settlementTransactionsPO.insertOrderIDByLabel('Venue Execution Id', 'Test Venue Execution Id');
    settlementTransactionsPO.insertOrderIDByLabel('Root Execution Id', 'Test Root Execution Id');
    settlementTransactionsPO.selectCurrencyFilter('USD');

    settlementTransactionsPO.expectFilterToBeSelected('BitMEX-testnet1');
    settlementTransactionsPO.expectFilterToBeSelected('Bank');
    settlementTransactionsPO.expectFilterToBeSelected('Correct filters');
    settlementTransactionsPO.expectFilterToBeSelected('Test Execution ID');
    settlementTransactionsPO.expectFilterToBeSelected('Test Venue Execution Id');
    settlementTransactionsPO.expectFilterToBeSelected('Test Root Execution Id');
    settlementTransactionsPO.expectFilterToBeSelected('USD');

    settlementTransactionsPO.expectTextToBeVisible('Correct filters');
  });
});
