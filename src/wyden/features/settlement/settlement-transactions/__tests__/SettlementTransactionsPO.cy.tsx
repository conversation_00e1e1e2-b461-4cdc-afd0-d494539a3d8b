import React from 'react';
import '../../../../../../cypress/support/component';
import {
  SETTLEMENT_TRANSACTIONS_GRID_TEST_ID,
  SettlementTransactions,
} from '../SettlementTransactions';
import { FormPO } from '../../../form/__test__/FormPO.cy';
import { FiltersChipListPO } from '../../../chip-filters/__test__/FiltersChipListPO.cy';
import { FILTERS_POPOVER_TEST_ID } from '../../../chip-filters/FiltersChipList';
import { SETTLEMENT_TRANSACTIONS_FILTERS_TEST_ID } from '../SettlementTransactionsFilters';

export class SettlementTransactionsPO {
  private readonly formPO = new FormPO();
  private readonly chipsPo = new FiltersChipListPO();
  private selectedWidget = '#test-widget';

  render() {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(<SettlementTransactions />);
  }

  expectTextToBeVisible(text: string, options?: { ignore?: string }) {
    return cy.findAllByText(text, options).should('have.length.gte', 1);
  }

  expectAGgridTextToBeVisible(text: string, options?: { ignore?: string }) {
    return cy.get(`[data-testid="${SETTLEMENT_TRANSACTIONS_GRID_TEST_ID}"`).within(() => {
      cy.findByText(text, options).should('be.visible');
    });
  }

  expectTextToBeNotVisible(text: string) {
    return cy.contains(text).should('not.exist');
  }

  expectAGGridTextToBeVisible(text: string) {
    cy.get(`[data-testid="${SETTLEMENT_TRANSACTIONS_GRID_TEST_ID}"`).within(() => {
      this.expectTextToBeVisible(text);
    });
  }

  scrollDownToEnd() {
    cy.get(`[data-testid="${SETTLEMENT_TRANSACTIONS_GRID_TEST_ID}"`).scrollTo('bottom', {
      ensureScrollable: false,
    });
  }

  expectAGgridDataToBeVisible(data: string[][]) {
    return cy.get(`[data-testid="${SETTLEMENT_TRANSACTIONS_GRID_TEST_ID}"`).within(() => {
      data.forEach((row) => {
        row.forEach((cell) => {
          this.expectTextToBeVisible(cell);
        });
      });
    });
  }

  expectAGgridDataToBeNotVisible(data: string[][]) {
    return cy.get(`[data-testid="${SETTLEMENT_TRANSACTIONS_GRID_TEST_ID}"`).within(() => {
      data.forEach((row) => {
        row.forEach((cell) => {
          this.expectTextToBeNotVisible(cell);
        });
      });
    });
  }

  insertFromDate(date: string) {
    this.formPO.insertDate('Updated from', date);
  }

  insertToDate(date: string) {
    this.formPO.insertDate('Updated to', date);
  }

  setOrderIdFilter(value: string) {
    this.chipsPo.applyTextAreaChip('Order ID', value);
  }

  selectInstrument(value: string) {
    return this.formPO.selectByLabelText('Instrument', value);
  }

  insertTransactionId(value: string) {
    return this.formPO.insertByLabelText('Order ID', value);
  }

  clickClearDateRange() {
    return cy.contains('Clear').click();
  }

  clickOutsideFiltersPopover() {
    return cy.get('body').click();
  }

  openFiltersPopover() {
    return cy
      .get(`${this.selectedWidget} [data-testid="${SETTLEMENT_TRANSACTIONS_FILTERS_TEST_ID}"]`)
      .contains('Add')
      .click();
  }

  selectAccountsFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Accounts').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectPortfoliosFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Portfolios').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectWalletsFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Wallets').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectDateFilter(filterName: string, value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(filterName).click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  insertOrderIDByLabel(label: string, value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(label).click();
    this.formPO.insertByLabelText(label, value);
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Apply').click();
  }

  selectCurrencyFilter(value: string) {
    this.openFiltersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Currency').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  expectFilterToBeSelected(value: string) {
    return cy
      .get(`${this.selectedWidget} [data-testid="${SETTLEMENT_TRANSACTIONS_FILTERS_TEST_ID}"]`)
      .contains(`${value}`)
      .should('be.visible');
  }
}
