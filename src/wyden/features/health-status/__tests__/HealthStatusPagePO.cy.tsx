import React from 'react';
import { HealthStatusPage } from '../HealthStatusPage';
import '../../../../../cypress/support/component';

const CELLS_COUNT = 7;

export class HealthStatusPagePO {
  render() {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(<HealthStatusPage />);
  }

  row(count: number) {
    const rowCount = count - 1;

    return {
      rowStatus: {
        isAlive: () =>
          cy
            .findAllByRole('button')
            .eq(rowCount)
            .should('have.attr', 'data-testid', 'health-status-alive'),
        isUnhealthy: () =>
          cy
            .findAllByRole('button')
            .eq(rowCount)
            .should('have.attr', 'data-testid', 'health-status-unhealthy'),
        isDead: () =>
          cy
            .findAllByRole('button')
            .eq(rowCount)
            .should('have.attr', 'data-testid', 'health-status-dead'),
      },
      accountName: {
        toHaveText: (text: string) =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 3)
            .findByText(text),
      },
      id: {
        toHaveText: (text: string) =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 4)
            .findByText(text),
      },
      venue: {
        toHaveText: (text: string) =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 5)
            .findByText(text),
      },
      marketData: {
        isAlive: () =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 6)
            .findByTestId('health-status-alive'),
        isUnhealthy: () =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 6)
            .findByTestId('health-status-warning'),
        isDead: () =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 6)
            .findByTestId('health-status-dead'),
      },
      referenceData: {
        isAlive: () =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 7)
            .findByTestId('health-status-alive'),
        isUnhealthy: () =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 7)
            .findByTestId('health-status-warning'),
        isDead: () =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 7)
            .findByTestId('health-status-dead'),
      },
      trading: {
        isAlive: () =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 8)
            .findByTestId('health-status-alive'),
        isUnhealthy: () =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 8)
            .findByTestId('health-status-warning'),
        isDead: () =>
          cy
            .findAllByRole('gridcell')
            .eq(rowCount * CELLS_COUNT + 8)
            .findByTestId('health-status-dead'),
      },
    };
  }
}
