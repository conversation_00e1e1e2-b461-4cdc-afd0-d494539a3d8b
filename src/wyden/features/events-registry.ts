import { OrderEntryEvents, OrderEntryEventsTranslation } from '@wyden/features/order-form/events';
import { WatchlistEvents, WatchlistEventsTranslation } from '@wyden/features/watchlist/events';
import {
  OrdersHistoryEvents,
  OrdersHistoryTranslation,
} from '@wyden/features/orders-history/events';
import {
  NetworkEventsTranslation,
  NetworkEvents,
} from '@wyden/features/error-indicators/network-indicators/events';

export const EVENTS_TYPES = {
  ...OrderEntryEvents,
  ...WatchlistEvents,
  ...OrdersHistoryEvents,
  ...NetworkEvents,
  BACKEND_EVENTS: 'backend_events',
} as const;
export const EVENTS_TYPES_TRANSLATIONS = {
  ...OrderEntryEventsTranslation,
  ...WatchlistEventsTranslation,
  ...OrdersHistoryTranslation,
  ...NetworkEventsTranslation,
  [EVENTS_TYPES.BACKEND_EVENTS]: 'eventLogs.backendEvent',
};
