import AddIcon from '@mui/icons-material/Add';
import { ReactComponent as ArchiveIcon } from '@wyden/assets/archive.svg';
import { Search } from '@ui/Search';
import { Tab } from '@ui/Tab';
import { Tabs } from '@ui/Tabs';
import { VerticalSpacer } from '@ui/VerticalSpacer';
import { HorizontalSpacer } from '@ui/HorizontalSpacer';
import { styled } from '@ui/styled';
import { ErrorBoundary } from '@wyden/features/error-indicators/error-boundary/ErrorBoundary';
import { CreateInstrumentDialog } from '@wyden/features/instruments/add-instrument/CreateInstrumentDialog';
import { EditInstrumentDialog } from '@wyden/features/instruments/edit-instrument/EditInstrumentDialog';
import { useInstrumentsStore } from '@wyden/features/instruments/useInstrumentsStore';
import { GetRowIdParams } from 'ag-grid-community';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../../../ui/Button';
import { Header } from '../../../ui/Typography/Header';
import { Resource, Scope, VenueType } from '../../services/graphql/generated/graphql';
import { FullWorkspaceComponentContainer } from '../FullWorkspaceComponentContainer';
import { InfiniteScrollGridWrapper } from '../grid/InfiniteScrollGridWrapper';
import { StyledHeaderWrapper } from '../settings-navigation/SettingsNavigation';
import {
  clientInstrumentsColumnDefinitions,
  defaultInstrumentsColumnVisibleFields,
  instrumentsColumnsValidationSchema,
  streetInstrumentsColumnDefinitions,
} from './columnDefinitions';
import { useInstrumentDataObserver } from './useInstrumentDataObserver';
import { InstruTab, useInstrumentsGrid } from './useInstrumentsGrid';
import { useGridApi } from '@wyden/hooks/useGridApi';
import { usePermissions } from '@wyden/hooks/usePermissions';
import { ColumnSettings } from '../grid/ColumnSettings';
import { ColumnManagerProvider } from '../grid/ColumnManager';
import { useAnchor } from '@wyden/hooks/useAnchor';
import { StreetInstrumentsGrid } from './StreetInstrumentsGrid';
import { ClientInstrumentsGrid } from './ClientInstrumentsGrid';
import { InternalExchangeGrid } from './InternalExchangeGrid';
import { getSpacing } from '@wyden/utils/styles';
import { useEntitlements } from '../entitlements/useEntitlements';
import { ENTITLEMENTS } from '@wyden/constants';

const getRowId = (params: GetRowIdParams) => params.data.instrumentIdentifiers.instrumentId;

export function InstrumentsPage() {
  const { t } = useTranslation();
  const [tab, setTab] = useState<InstruTab>('street');
  const { toggleAddInstrumentDialog, isDataLoading } = useInstrumentsStore();
  const handleTabChange = (_event: React.SyntheticEvent, newValue: InstruTab) => setTab(newValue);
  const { checkIfEntitled } = useEntitlements();
  const { gridApi: clientApi, onGridReady: clientApiReady } = useGridApi();
  const { gridApi: clobApi, onGridReady: clobApiReady } = useGridApi();
  const tabRef = useRef(tab);
  const {
    onGridReady,
    initialLoading,
    error,
    showNoRows,
    reloading,
    refresh,
    clientInstruments,
    clientInstrumentSearchVal,
    streetInstrumentSearchInputVal,
    handleClientInstrumentChange,
    handleStreetInstrumentSearchInput,
    handleInternalExchangeSearch,
    internalExchangeInstrumentSearchVal,
    internalExchangeInstruments,
    toggleArchived,
    showArchived,
  } = useInstrumentsGrid(tab);

  useInstrumentDataObserver(tabRef, clientApi, clobApi, refresh);
  const columnSettingsModalProps = useAnchor('column-settings');
  const { checkIfPermitted } = usePermissions();

  const canSeeClientInstruments = checkIfPermitted({
    resource: Resource.ClientInstrument,
    scope: Scope.Read,
  });

  useEffect(() => {
    tabRef.current = tab;
  }, [tab]);

  return (
    <ErrorBoundary errorInfo={t('instruments.errorBoundaryTitle')}>
      <FullWorkspaceComponentContainer>
        <StyledHeaderWrapper>
          <Header variant="h2">{t('tabs.instruments')}</Header>
        </StyledHeaderWrapper>
        <VerticalSpacer space={3} />
        <StyledMainSection>
          <Tabs withDivider value={tab} onChange={handleTabChange}>
            <Tab data-testid="street-instruments" label={t('common.street')} value={'street'} />
            {canSeeClientInstruments && (
              <Tab data-testid="client-instruments" label={t('common.client')} value={'client'} />
            )}
            {checkIfEntitled(ENTITLEMENTS['Wyden Exchange']) && (
              <Tab
                data-testid="wyden-instruments"
                label={t('common.wydenExchange')}
                value={'wydenExchange'}
              />
            )}
          </Tabs>
          {tab === 'street' && (
            <>
              <ColumnManagerProvider
                type={'BASE'}
                defaultVisibleFields={defaultInstrumentsColumnVisibleFields}
                validationSchema={instrumentsColumnsValidationSchema}
                columnDefinitions={streetInstrumentsColumnDefinitions}
                gridName="street-instruments-grid"
              >
                <StyledLineWithSearchAndAddButton>
                  <StyledSearchWrapper>
                    <Search
                      fullWidth
                      value={streetInstrumentSearchInputVal}
                      onChange={handleStreetInstrumentSearchInput}
                      autoFocus={false}
                      placeholder={t('instruments.findInstrument')}
                    />
                  </StyledSearchWrapper>
                  <StyledShowArchive>
                    <Button variant={showArchived ? 'primary' : 'ghost'} onClick={toggleArchived}>
                      <ArchiveIcon />
                      {t('common.showArchive')}
                    </Button>
                  </StyledShowArchive>
                  <HorizontalSpacer space={1} />
                  <ColumnSettings {...columnSettingsModalProps} />
                </StyledLineWithSearchAndAddButton>
                <InfiniteScrollGridWrapper
                  error={error}
                  initialLoading={initialLoading}
                  reloading={reloading}
                  showNoRows={showNoRows}
                  refresh={refresh}
                  noRowsInfo={t('instruments.noInstruments')}
                >
                  <StreetInstrumentsGrid
                    isDataLoading={isDataLoading || (!initialLoading && reloading)}
                    key={'street-instruments'}
                    getRowId={getRowId}
                    onGridReady={onGridReady}
                  />
                </InfiniteScrollGridWrapper>
              </ColumnManagerProvider>
            </>
          )}
          {tab === 'client' && (
            <>
              <ColumnManagerProvider
                type={'BASE'}
                defaultVisibleFields={defaultInstrumentsColumnVisibleFields}
                validationSchema={instrumentsColumnsValidationSchema}
                columnDefinitions={clientInstrumentsColumnDefinitions}
                gridName="client-instruments-grid"
              >
                <StyledLineWithSearchAndAddButton>
                  <StyledSearchWrapper>
                    <Search
                      fullWidth
                      onChange={handleClientInstrumentChange}
                      autoFocus={false}
                      value={clientInstrumentSearchVal}
                      placeholder={t('instruments.findInstrument')}
                    />
                  </StyledSearchWrapper>
                  <StyledShowArchive>
                    <Button variant={showArchived ? 'primary' : 'ghost'} onClick={toggleArchived}>
                      <ArchiveIcon />
                      {t('common.showArchive')}
                    </Button>
                  </StyledShowArchive>
                  <HorizontalSpacer space={3} />
                  <StyledAddButtonAndColumnManagerContainer>
                    <Button
                      id="add-instrument-button"
                      disabled-tooltip-message={t('instruments.noPermissionToAdd')}
                      disabled={
                        !checkIfPermitted({
                          resource: Resource.ClientInstrument,
                          scope: Scope.Create,
                        })
                      }
                      onClick={() => toggleAddInstrumentDialog(VenueType.Client)}
                    >
                      <AddIcon />
                      {t('instruments.createInstrumentButton')}
                    </Button>
                    <ColumnSettings {...columnSettingsModalProps} />
                  </StyledAddButtonAndColumnManagerContainer>
                </StyledLineWithSearchAndAddButton>
                <ClientInstrumentsGrid
                  isDataLoading={isDataLoading}
                  quickFilterText={clientInstrumentSearchVal}
                  rowData={clientInstruments}
                  getRowId={getRowId}
                  onGridReady={(params) => {
                    onGridReady(params);
                    clientApiReady(params);
                  }}
                />
              </ColumnManagerProvider>
            </>
          )}
          {tab === 'wydenExchange' && (
            <>
              <ColumnManagerProvider
                type={'BASE'}
                defaultVisibleFields={defaultInstrumentsColumnVisibleFields}
                validationSchema={instrumentsColumnsValidationSchema}
                columnDefinitions={clientInstrumentsColumnDefinitions}
                gridName="internal-exchange-grid"
              >
                <StyledLineWithSearchAndAddButton>
                  <StyledSearchWrapper>
                    <Search
                      fullWidth
                      onChange={handleInternalExchangeSearch}
                      autoFocus={false}
                      value={internalExchangeInstrumentSearchVal}
                      placeholder={t('instruments.findInstrument')}
                    />
                  </StyledSearchWrapper>
                  <StyledShowArchive>
                    <Button variant={showArchived ? 'primary' : 'ghost'} onClick={toggleArchived}>
                      <ArchiveIcon />
                      {t('common.showArchive')}
                    </Button>
                  </StyledShowArchive>
                  <HorizontalSpacer space={2} />
                  <StyledAddButtonAndColumnManagerContainer>
                    <Button
                      id="add-instrument-button"
                      disabled-tooltip-message={t('instruments.noPermissionToAdd')}
                      disabled={
                        !checkIfPermitted({
                          resource: Resource.ClobInstrument,
                          scope: Scope.Create,
                        })
                      }
                      onClick={() => toggleAddInstrumentDialog(VenueType.Clob)}
                    >
                      <AddIcon />
                      {t('instruments.createInstrumentButton')}
                    </Button>
                    <ColumnSettings {...columnSettingsModalProps} />
                  </StyledAddButtonAndColumnManagerContainer>
                </StyledLineWithSearchAndAddButton>
                <InternalExchangeGrid
                  isDataLoading={isDataLoading}
                  quickFilterText={internalExchangeInstrumentSearchVal}
                  rowData={internalExchangeInstruments}
                  getRowId={getRowId}
                  onGridReady={(params) => {
                    onGridReady(params);
                    clobApiReady(params);
                  }}
                />
              </ColumnManagerProvider>
            </>
          )}
        </StyledMainSection>
      </FullWorkspaceComponentContainer>
      <CreateInstrumentDialog />
      <EditInstrumentDialog />
    </ErrorBoundary>
  );
}

const StyledMainSection = styled('div')`
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;
`;

const StyledSearchWrapper = styled('div')`
  min-width: 400px;
  display: flex;
`;

const StyledAddButtonAndColumnManagerContainer = styled('div')`
  display: flex;
  gap: ${getSpacing(1)};
`;

const StyledShowArchive = styled('div')`
  margin-left: auto;
`;

const StyledLineWithSearchAndAddButton = styled('div')`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;
