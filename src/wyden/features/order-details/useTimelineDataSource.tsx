import {
  OrderStateResponse,
  SortingOrder,
  TimelineEdge,
  useTimelineLazyQuery,
} from '@wyden/services/graphql/generated/graphql';
import { GridApi, IServerSideGetRowsParams } from 'ag-grid-community';
import { useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { ERRORS } from '../../constants';
import { useEventLogs } from '../error-indicators/event-logs/useEventLogs';
import { NetworkEvents } from '../error-indicators/network-indicators/events';
import { useNetworkStore } from '../error-indicators/network-indicators/useNetworkStore';
import { useTimelineStore } from './useTimelineStore';
import { isEqual } from 'lodash';

interface UseTimelineDataSourceProps {
  rootOrderId: string | null | undefined;
}

const OMITTED_ORDER_STATE_KEYS: (keyof OrderStateResponse)[] = [
  'orderStateId',
  'updatedAt',
  'venueTimestamp',
];

export const isOrderStateResponse = (data: unknown): data is OrderStateResponse => {
  return (
    !!data &&
    typeof data === 'object' &&
    'orderId' in data &&
    'orderStateId' in data &&
    'updatedAt' in data
  );
};

export const omitFields = <T extends object>(obj: T, keys: (keyof T)[]): Partial<T> => {
  return Object.fromEntries(
    Object.entries(obj).filter(([k]) => !keys.includes(k as keyof T)),
  ) as Partial<T>;
};

export const isEqualIgnoringFields = <T extends object>(a: T, b: T, keysToOmit: (keyof T)[]) => {
  return isEqual(omitFields(a, keysToOmit), omitFields(b, keysToOmit));
};

export const isLogicallySameOrderState = (
  a: OrderStateResponse,
  b: OrderStateResponse,
): boolean => {
  return isEqualIgnoringFields(a, b, OMITTED_ORDER_STATE_KEYS);
};

export const useTimelineDataSource = ({ rootOrderId }: UseTimelineDataSourceProps) => {
  const { t } = useTranslation();
  const { upsertRequest } = useNetworkStore();
  const { addEventLog } = useEventLogs();
  const after = useRef<string | null>();
  const hasNextPage = useRef<boolean>(true);
  const rowCount = useRef<number>(1);
  const endCursor = useRef<string | null>();
  const sortingOrder = useRef<string | null>();
  const api = useRef<GridApi>();

  const { orderId, timelineEvents: filteredEvents, includeRelated } = useTimelineStore();

  const [getTimeline] = useTimelineLazyQuery({
    variables: {
      search: {
        orderId: orderId || '',
        sortingOrder: sortingOrder.current as SortingOrder.Asc | SortingOrder.Desc,
        first: 50,
        eventType: filteredEvents.length ? filteredEvents : undefined,
        includeRelated,
        ...(includeRelated && { rootOrderId: rootOrderId || '' }),
      },
    },
    fetchPolicy: 'network-only',
    onError: (err) => {
      upsertRequest('timeline', {
        pending: false,
        error: ERRORS.CLIENT_ERROR,
        err,
      });
      addEventLog({
        type: NetworkEvents.TIMELINE_QUERY,
        message: t('eventLogs.requestFailed', {
          name: t('ordersHistory.orderDetails.timelineQuery'),
        }),
        timestamp: new Date().getTime(),
      });
    },
  });

  // Resets when one of the filters change to start new pagination
  useEffect(() => {
    after.current = undefined;
    rowCount.current = 1;
    hasNextPage.current = true;
  }, [orderId, filteredEvents, includeRelated, getTimeline, sortingOrder]);

  const dataSource = useMemo(
    () => ({
      getRows: async (params: IServerSideGetRowsParams) => {
        if (!hasNextPage.current) return;

        const dateTimeSortingOrder =
          (params.request.sortModel
            .find((model) => model.colId === 'dateTime')
            ?.sort?.toUpperCase() as SortingOrder.Asc | SortingOrder.Desc) || null;
        const isSortingAction =
          sortingOrder.current !== undefined && sortingOrder.current !== dateTimeSortingOrder;

        if (isSortingAction) {
          endCursor.current = undefined;
          api.current?.setRowCount(0);
          api.current?.purgeInfiniteCache();
        }

        sortingOrder.current = dateTimeSortingOrder || null;

        /**
         * filters order states using the last one - since BE has several event sources and returns all of them,
         * filter the same ones (orderStateId, updatedAt, venueTimestamp etc. do not affect the logical sense)
         */
        const filterLatestOrderStates = (edges: TimelineEdge[]) => {
          type TimelineNode = (typeof edges)[number]['node'];

          const latestEntries = new Map<OrderStateResponse['orderId'], TimelineNode>();

          return edges.filter((edge) => {
            const { node } = edge;
            const { data } = node;

            if (!data) return true; // No data → include

            if (!isOrderStateResponse(data)) return true; // Not OrderStateResponse → include

            const existing = latestEntries.get(data.orderId);

            if (!existing) {
              latestEntries.set(data.orderId, node);
              return true;
            }

            const existingData = existing.data as OrderStateResponse;

            if (!data.updatedAt || !existingData.updatedAt) {
              latestEntries.set(data.orderId, node);
              return true;
            }

            if (isLogicallySameOrderState(data, existingData)) {
              if (data.updatedAt >= existingData.updatedAt) {
                latestEntries.set(data.orderId, node);
              }
              return false;
            }

            latestEntries.set(data.orderId, node);
            return true;
          });
        };

        try {
          const data = await getTimeline({
            variables: {
              search: {
                orderId: orderId || '',
                sortingOrder: dateTimeSortingOrder,
                first: 50,
                eventType: filteredEvents.length ? filteredEvents : undefined,
                includeRelated,
                ...(includeRelated && { rootOrderId: rootOrderId || '' }),
                after: after.current,
              },
            },
          });

          if (data.error) {
            params.fail();
          } else if (data?.data?.timeline) {
            const rowData = filterLatestOrderStates(data.data.timeline.edges).map((edge) => ({
              ...edge.node,
              cursor: edge.cursor,
            }));

            const returnedHasNextPage = data.data.timeline.pageInfo.hasNextPage;

            after.current = data.data.timeline.pageInfo.endCursor;
            hasNextPage.current = returnedHasNextPage;
            // We need to do this to avoid soawn of the spinners when the data length is less than cacheBlockSize
            // We also want to have one more row than the actual data to trigger the infinite scroll (if there is next page)
            rowCount.current = returnedHasNextPage
              ? rowCount.current + rowData.length
              : rowCount.current + rowData.length - 1;

            params.success({
              rowData,
              rowCount: rowCount.current,
            });

            // In SSRM we need to do this explicitly
            if (rowCount.current === 0) {
              params.api.showNoRowsOverlay();
            }
          }
        } catch (err) {
          params.fail();
        }
      },
    }),
    [orderId, filteredEvents, includeRelated, getTimeline, rootOrderId],
  );

  return dataSource;
};
