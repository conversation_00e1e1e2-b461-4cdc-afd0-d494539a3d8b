import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface ExchangeAccountStore {
  createExchangeAccountDialogOpen: boolean;
  toggleCreateExchangeAccountDialog: () => void;
  isDataLoading: boolean;
  dataLoaded: () => void;
  dataLoading: () => void;
}

const EXCHANGE_ACCOUNT_ACTIONS = {
  TOGGLE_CREATE_EXCHANGE_ACCOUNT_DIALOG: 'toggleCreateExchangeAccountDialog',
  DATA_LOADED: 'dataLoaded',
  DATA_LOADING: 'dataLoading',
} as const;

export const useExchangeAccountStore = create<ExchangeAccountStore>()(
  devtools(
    (set) => ({
      createExchangeAccountDialogOpen: false,
      isDataLoading: false,
      dataLoaded: () =>
        set(
          () => ({
            isDataLoading: false,
          }),
          false,
          EXCHANGE_ACCOUNT_ACTIONS.DATA_LOADED,
        ),
      dataLoading: () =>
        set(
          () => ({
            isDataLoading: true,
          }),
          false,
          EXCHANGE_ACCOUNT_ACTIONS.DATA_LOADING,
        ),
      toggleCreateExchangeAccountDialog: () =>
        set(
          (state) => ({
            createExchangeAccountDialogOpen: !state.createExchangeAccountDialogOpen,
          }),
          false,
          EXCHANGE_ACCOUNT_ACTIONS.TOGGLE_CREATE_EXCHANGE_ACCOUNT_DIALOG,
        ),
    }),
    { name: 'ExchangeAccountStore' },
  ),
);
