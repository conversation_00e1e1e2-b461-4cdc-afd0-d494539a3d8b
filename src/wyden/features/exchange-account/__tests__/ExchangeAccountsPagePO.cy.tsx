import React from 'react';
import '../../../../../cypress/support/component';
import { ExchangeAccountsPage } from '../ExchangeAccountsPage';
import { NetworkSnackbar } from '../../error-indicators/network-indicators/NetworkSnackbar';
import { ExchangeAccountFormPO } from './ExchangeAccountFormPO.cy';
import { SnackbarProvider } from 'notistack';
import { EXCHANGE_ACCOUNT_ACTIONS_TEST_ID } from '../renderers';

export class ExchangeAccountsPagePO {
  exchangeAccountFormPO = new ExchangeAccountFormPO();

  render() {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(
      <SnackbarProvider>
        <NetworkSnackbar />
        <ExchangeAccountsPage />
      </SnackbarProvider>,
    );
  }

  expectTextToBeVisible(text: string, options?: { ignore?: string }) {
    return cy.findByText(text, options);
  }

  expectTextToNotBeVisible(text: string) {
    return cy.findByText(text).should('not.exist');
  }

  expectElementToNotBeVisible(text: string) {
    return cy.findByText(text).should('not.exist');
  }

  expectAllDataLoaded() {
    return cy.findByText('Exchange account 1').should('exist');
  }

  expectCreateNewExchangeAccountButtonToBeDisabled() {
    const button = cy.findByRole('button', { name: 'Create account' });
    return button.should('be.disabled');
  }

  clickCreateNewExchangeAccount() {
    return cy.findByRole('button', { name: 'Create account' }).click();
  }

  clickExchangeAccountActions(number: number) {
    return cy
      .findAllByTestId('context-menu')
      .eq(number - 1)
      .click();
  }

  hoverOnExchangeAccountActions() {
    return cy
      .findAllByTestId(EXCHANGE_ACCOUNT_ACTIONS_TEST_ID)
      .trigger('mouseover', { force: true });
  }

  clickOnArchiveExchangeAccountAction() {
    return cy.findByTestId(EXCHANGE_ACCOUNT_ACTIONS_TEST_ID).click();
  }

  clickOnYesArchiveExchangeAccountButton() {
    return cy.findByText('Yes, archive account').click();
  }

  clickOnUnarchiveExchangeAccountButton() {
    return cy.findByText('Yes, unarchive account').click();
  }
}
