import { create } from 'zustand';
import { selectWidgetData, UserStore, useUserStore } from '@wyden/hooks/useUserStore';
import { AuthStore, useAuthStore } from '@wyden/services/keycloak/auth/useAuthStore';
import { apolloClient } from '@wyden/services/graphql/apollo-client';
import {
  UpdateUserDataDocument,
  UpdateUserDataInput,
} from '@wyden/services/graphql/generated/graphql';
import {
  PossibleWidgetTypes,
  UserData,
  userDataZodType,
  WIDGET_TYPES,
  widgetsUnionZod,
} from '@wyden/hooks/useUserData';
import { logger } from '@wyden/utils/logger';

interface UserStoreSynchronizer {
  getUserStoreState: () => UserStore;
  getAuthStoreState: () => AuthStore;
  getApolloClient: () => typeof apolloClient;

  updateUserData: <T extends PossibleWidgetTypes>(
    id: string,
    newWidgetData: Omit<T, 'id' | 'type'>,
    widgetType: WIDGET_TYPES,
  ) => Promise<void>;

  getWidgetData: (id: string) => PossibleWidgetTypes;
}

export const useUserStoreSynchronizer = create<UserStoreSynchronizer>((set, get) => ({
  getUserStoreState: () => useUserStore.getState(),
  getAuthStoreState: () => useAuthStore.getState(),
  getApolloClient: () => apolloClient,

  updateUserData: async (id, newWidgetData, widgetType) => {
    const userStoreState = get().getUserStoreState();
    const authStoreState = get().getAuthStoreState();
    const authClient = get().getApolloClient();

    const widgetData = createWidgetData(
      id,
      newWidgetData,
      widgetType,
      userStoreState,
      authStoreState,
    );

    const userData = createdUserData(id, widgetData, userStoreState, authStoreState);

    if (userData) await updateUserData(userData, authClient);
  },
  getWidgetData: (id) => {
    const userStoreState = get().getUserStoreState();
    const authStoreState = get().getAuthStoreState();
    return getWidgetData(id, userStoreState, authStoreState);
  },
}));

function createWidgetData(
  id: string,
  newWidgetData: Omit<PossibleWidgetTypes, 'id' | 'type'>,
  widgetType: WIDGET_TYPES,
  userStoreState: UserStore,
  authStoreState: AuthStore,
): PossibleWidgetTypes | undefined {
  const widget = getWidgetData(id, userStoreState, authStoreState);

  const newWidgetDataForUpdate = mergeWidgetData(id, newWidgetData, widgetType, widget);
  const WidgetDataValidated = validateWidgetData(newWidgetDataForUpdate);

  if (!WidgetDataValidated.success) {
    logger.error('Widget data is not valid', WidgetDataValidated.error);
    return;
  }

  return WidgetDataValidated.data;
}

function createdUserData(
  id: string,
  widgetData: PossibleWidgetTypes | undefined,
  userStoreState: UserStore,
  authStoreState: AuthStore,
) {
  const widgets = getWidgets(authStoreState, userStoreState);

  const newWidgets = updateUserWidgets(widgets, id, widgetData);

  const userDataValidated = validateUserData(newWidgets, widgets);

  if (!userDataValidated.success) {
    logger.error('User data is not valid', userDataValidated.error);
    return;
  }

  return prepareDataForWrite(userDataValidated.data);
}

function getWidgets(authStoreState: AuthStore, userStoreState: UserStore) {
  const host = window?.location?.hostname;
  const preferredUsername =
    authStoreState.user !== 'INITIAL' && authStoreState.user !== null
      ? authStoreState.user?.preferred_username
      : 'default';
  const username = `${preferredUsername}-${host}`;
  const userData = userStoreState.users.find((user) => user.username === username);
  return userData?.data?.widgets || [];
}

function mergeWidgetData(
  id: string,
  newWidgetData: Omit<PossibleWidgetTypes, 'id' | 'type'>,
  widgetType: WIDGET_TYPES,
  widgetData?: PossibleWidgetTypes,
) {
  return widgetData
    ? { ...widgetData, ...newWidgetData }
    : { id, type: widgetType, ...newWidgetData };
}

function validateWidgetData(newWidgetDataForUpdate: PossibleWidgetTypes) {
  return widgetsUnionZod.safeParse(newWidgetDataForUpdate);
}

function getWidgetData(
  id: string,
  userStoreState: UserStore,
  authStoreState: AuthStore,
): PossibleWidgetTypes {
  const host = window?.location?.hostname;
  const preferredUsername =
    authStoreState.user !== 'INITIAL' && authStoreState.user !== null
      ? authStoreState.user?.preferred_username
      : 'default';
  return JSON.parse(selectWidgetData(`${preferredUsername}-${host}`, id)(userStoreState));
}

function updateUserWidgets(
  widgets: PossibleWidgetTypes[],
  id: string,
  widgetData: PossibleWidgetTypes | undefined,
) {
  const userWidgets = widgets || [];
  if (!widgetData) {
    return userWidgets;
  }
  const widgetsWithoutCurrent = userWidgets.filter((widget) => widget.id !== id);
  return [...widgetsWithoutCurrent, widgetData];
}

function validateUserData(newWidgets: PossibleWidgetTypes[], widgets: PossibleWidgetTypes[]) {
  const newData = widgets ? { ...widgets, widgets: newWidgets } : { widgets: newWidgets };
  return userDataZodType.safeParse(newData);
}

const parseUserDataWrite = (data: object) => JSON.stringify(data);

function prepareDataForWrite(validatedData: UserData) {
  const timestamp = new Date().getTime();
  return parseUserDataWrite({ ...validatedData, timestamp });
}

async function updateUserData(
  dataToWrite: UpdateUserDataInput['data'],
  client: typeof apolloClient,
) {
  await client.mutate({
    mutation: UpdateUserDataDocument,
    variables: {
      request: {
        data: dataToWrite,
      },
    },
  });
}
