import { PreTradeChecksPO } from './PreTradeChecksPO.cy';
import {
  orderTypePreValidationErrorMock,
  portfolioTagsMock,
  preTradeCheckFormSchemaMock,
  preTradeChecksQueryMock,
  ptcToEditMock,
  refreshedPreTradeChecksQueryMock,
} from './PreTradeChecksQuery.mock';
import { graphql } from 'msw';
import { worker } from '../../../../../../mocks/browser';
import {
  AssetClass,
  PreTradeCheckInputSchema,
} from '../../../../services/graphql/generated/graphql';
import { usePreTradeCheckStore } from '../usePreTradeCheckStore';
import { aSymbolSearchResponse } from '../../../../services/graphql/generated/mocks';

describe('<PreTradeChecksPage />', () => {
  const PO = new PreTradeChecksPO();
  beforeEach(() => {
    usePreTradeCheckStore.getState().setInitialState();
    worker.use(
      graphql.query('PreTradeChecks', (req, res, ctx) => {
        return res(
          ctx.data({
            preTradeChecks: preTradeChecksQueryMock.data.preTradeChecks,
          }),
        );
      }),
      graphql.query('SymbolSearch', (req, res, ctx) => {
        return res(
          ctx.data({
            symbolSearch: {
              pageInfo: {
                hasNextPage: false,
                endCursor: '',
              },
              edges: [
                {
                  node: aSymbolSearchResponse({
                    Symbol: 'BMEXUSDT',
                    assetClass: AssetClass.Forex,
                    venues: [],
                  }),
                  cursor: 'BMEXUSDT@FOREX@Bank',
                },
                {
                  node: aSymbolSearchResponse({
                    Symbol: 'ADAUSD',
                    assetClass: AssetClass.Forex,
                    venues: [],
                  }),
                  cursor: 'ADAUSD@FOREX@WydenMock',
                },
                {
                  node: aSymbolSearchResponse({
                    Symbol: 'BTCUSD',
                    assetClass: AssetClass.Forex,
                    venues: [],
                  }),
                  cursor: 'BTCUSD@FOREX@BitMEX',
                },
                {
                  node: aSymbolSearchResponse({
                    Symbol: 'BTCUSDT',
                    assetClass: AssetClass.Forex,
                    venues: [],
                  }),
                  cursor: 'BTCUSDT@FOREX@BitMEX',
                },
                {
                  node: aSymbolSearchResponse({
                    Symbol: 'ETHUSD',
                    assetClass: AssetClass.Forex,
                    venues: [],
                  }),
                  cursor: 'ETHUSD@FOREX@BitMEX',
                },
              ],
            },
          }),
        );
      }),
      graphql.query('PreTradeCheckFormSchema', (req, res, ctx) => {
        return res(
          ctx.data({
            preTradeCheckFormSchema: preTradeCheckFormSchemaMock.data.preTradeCheckFormSchema,
          }),
        );
      }),
      graphql.query('PortfolioTags', (req, res, ctx) => {
        return res(
          ctx.data({
            portfolioTags: portfolioTagsMock,
          }),
        );
      }),
      graphql.mutation('DeletePreTradeCheck', (req, res, ctx) => {
        return res(
          ctx.data({
            deletePreTradeCheck: {
              clientId: 'trader',
            },
          }),
        );
      }),
      graphql.mutation('SavePreTradeCheck', (req, res, ctx) => {
        const { success, error } = PreTradeCheckInputSchema().safeParse(req.variables.request);
        if (!success) {
          return res(ctx.errors([error]));
        }

        return res(
          ctx.data({
            clientMutationId: req.variables.clientMutationId,
          }),
        );
      }),
    );
    PO.render();
  });

  it(`Should display PTC's`, () => {
    PO.expectTextToBeVisible('Portfolios');
    PO.expectTextToBeVisible('Swiss Bank, German Bank');

    PO.expectTextToBeVisible('Portfolio Tags');
    PO.expectTextToBeVisible('region: EMEA');

    PO.expectTextToBeVisible('Check level');
    PO.expectTextToBeVisible('Block');
    PO.expectTextToBeVisible('-');
    PO.expectTextToBeVisible('Warning');

    PO.expectTextToBeVisible('Check type');
    PO.expectTextToBeVisible('Max Position');

    PO.expectTextToBeVisible('Channels');
    PO.expectTextToBeVisible('UI, API');

    PO.expectTextToBeVisible('Block');
    PO.expectTextToBeVisible('Warning');

    PO.expectTextToBeVisible('Configurations');
    PO.expectTextToBeVisible(
      'Symbol: BTC, Max Value Currency: USD, Max Quantity Warn: 10, Max Value Block: 1000000',
    );
  });
  it(`Should display error`, () => {
    worker.use(
      graphql.query('PreTradeChecks', (req, res, ctx) => {
        return res(
          ctx.errors([
            {
              message: "Apologies, we are currently experiencing an issue loading the PTC's",
              path: ['preTradeChecks'],
              extensions: {
                code: 'INTERNAL_SERVER_ERROR',
              },
              locations: [
                {
                  line: 2,
                  column: 3,
                },
              ],
            },
          ]),
          ctx.status(500),
        );
      }),
    );

    PO.expectTextToBeVisible(`An error occurred while loading data`);
    PO.expectTextToBeVisible(`Something went wrong`);
  });
  describe('Adding PTC', () => {
    it('should add order type PTC', () => {
      PO.openAddPTCModal();
      PO.selectCheckType('Order Type');
      PO.selectCheckLevel('Block');
      PO.selectPortfolio('portfolio_trader_1');
      PO.selectPortfolioTags('region: EMEA');
      PO.selectPortfolioTags('region: AMER');
      PO.selectChannel('UI');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.selectMultiSelect('Allowlist', 'Market');
      PO.selectMultiSelect('Allowlist', 'Limit');
      PO.selectMultiSelect('Blocklist', 'Stop');
      PO.selectMultiSelect('Blocklist', 'Stop-Limit');
      PO.createPTC();
      PO.expectTextToBeVisible('Adding Pre-trade check was successful');
    });
    it('should not be able to type an option in allow list', () => {
      worker.use(
        graphql.query('PreTradeCheckFormSchema', (req, res, ctx) => {
          return res(
            ctx.data({
              preTradeCheckFormSchema: [
                {
                  type: 'Order Type',
                  configuration: [
                    {
                      type: 'STRING_LIST',
                      name: 'allowlist',
                      required: true,
                      isEnum: true,
                      options: ['MARKET', 'STOP', 'LIMIT', 'STOP_LIMIT'],
                      __typename: 'PreTradeCheckPropertySchema',
                    },
                  ],
                  __typename: 'PreTradeCheckSchema',
                },
              ],
            }),
          );
        }),
      );
      PO.openAddPTCModal();
      PO.selectCheckType('Order Type');
      PO.selectCheckLevel('Block');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.typeMultiselect('Allowlist', 'test');
      PO.clickOnPTCForm();
      PO.createPTC();
      PO.expectTextToBeVisible('Required');
    });

    it(`should refresh PTC's after adding PTC`, () => {
      worker.use(
        graphql.query('PreTradeChecks', (req, res, ctx) => {
          return res.once(
            ctx.data({
              preTradeChecks: preTradeChecksQueryMock.data.preTradeChecks,
            }),
          );
        }),
        graphql.query('PreTradeChecks', (req, res, ctx) => {
          return res(
            ctx.data({
              preTradeChecks: refreshedPreTradeChecksQueryMock.data.preTradeChecks,
            }),
          );
        }),
      );
      PO.expectTextToBeVisible(
        'Symbol: BTC, Max Value Currency: USD, Max Quantity Warn: 10, Max Value Block: 1000000',
      );
      PO.openAddPTCModal();
      PO.selectCheckType('Order Type');
      PO.selectCheckLevel('Block');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.selectMultiSelect('Allowlist', 'Market');
      PO.createPTC();
      PO.expectTextToBeVisible('Adding Pre-trade check was successful');
      PO.expectTextToNotBeVisible(
        'Symbol: BTC, Max Value Currency: USD, Max Quantity Warn: 10, Max Value Block: 1000000',
      );
      PO.expectTextToBeVisible(`Allowlist: Market`);
    });

    it('should add order type and display error message in modal (BAD_REQUEST, code: 4XX)', () => {
      worker.use(
        graphql.mutation('SavePreTradeCheck', (req, res, ctx) => {
          return res(
            ctx.errors(orderTypePreValidationErrorMock.errors),
            ctx.data(orderTypePreValidationErrorMock.data),
          );
        }),
      );
      PO.openAddPTCModal();
      PO.selectCheckType('Order Type');
      PO.selectCheckLevel('Block');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.selectMultiSelect('Allowlist', 'Market');
      PO.selectMultiSelect('Allowlist', 'Stop');
      PO.selectMultiSelect('Blocklist', 'Market');
      PO.selectMultiSelect('Blocklist', 'Stop');
      PO.clickOnPTCForm();
      PO.createPTC();
      PO.expectTextToBeVisible('allowOrBlockDefined: One of [allowlist, blocklist] must be set');
      PO.closePTCModal();
      PO.openAddPTCModal();
      PO.expectTextToNotBeVisible('allowOrBlockDefined: One of [allowlist, blocklist] must be set');
    });

    it('should add order type PTC and display fail messaged (INTERNAL_SERVER_ERROR, code: 5XX)', () => {
      worker.use(
        graphql.mutation('SavePreTradeCheck', (req, res, ctx) => {
          return res(
            ctx.errors([
              {
                message: 'Apologies, we are currently experiencing an issue adding the PTC',
                extensions: {
                  classification: 'INTERNAL_ERROR',
                },
              },
            ]),
          );
        }),
      );

      PO.openAddPTCModal();
      PO.selectCheckLevel('Warning');
      PO.selectCheckType('Order Type');
      PO.selectPortfolio('portfolio_trader_1');
      PO.selectChannel('UI');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.selectMultiSelect('Allowlist', 'Market');
      PO.selectMultiSelect('Blocklist', 'Stop');
      PO.createPTC();
      PO.expectTextToBeVisible('Adding Pre-trade check failed');
    });
    it('should not add order type PTC because of missing required field', () => {
      PO.openAddPTCModal();
      PO.selectChannel('UI');
      PO.selectChannel('API');
      PO.createPTC();

      PO.expectTextToBeVisible('Check type is required');
      PO.expectTextToBeVisible('Check level is required');
      PO.expectTextToBeVisible('At least one channel is required');
      PO.expectTextToNotBeVisibleInModal('Configuration');
    });
    it('should add order type PTC with only required field', () => {
      PO.openAddPTCModal();
      PO.selectCheckType('Order Type');
      PO.selectCheckLevel('Warning');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.createPTC();
      PO.expectTextToBeVisible('Adding Pre-trade check was successful');
    });
    it('should add instrument PTC', () => {
      PO.openAddPTCModal();
      PO.selectCheckType('Instrument');
      PO.selectCheckLevel('Warning');
      PO.selectPortfolio('portfolio_trader_1');
      PO.selectChannel('API');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.selectMultiSelect('Allowlist', 'ADA');
      PO.selectMultiSelect('Allowlist', 'BTC');
      PO.selectMultiSelect('Blocklist', 'ADA');
      PO.selectMultiSelect('Blocklist', 'BTC');
      PO.createPTC();
      PO.expectTextToBeVisible('Adding Pre-trade check was successful');
    });
    it('should create instrument PTC with only required field', () => {
      PO.openAddPTCModal();

      PO.selectCheckType('Instrument');
      PO.selectCheckLevel('Warning');
      PO.selectPortfolio('portfolio_trader_1');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.createPTC();
      PO.expectTextToBeVisible('Adding Pre-trade check was successful');
    });
    it('should add max order size PTC', () => {
      PO.openAddPTCModal();
      PO.selectCheckType('Max Order Size');
      PO.selectCheckLevel('Warning');
      PO.selectPortfolio('portfolio_trader_1');
      PO.selectChannel('UI');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.insertNumber('Max Quantity Block', '1000000');
      PO.selectMultiSelect('Symbols', 'BTC');
      PO.insertNumber('Max Quantity Warn', '10');
      PO.createPTC();
      PO.expectTextToBeVisible('Adding Pre-trade check was successful');
    });
    it('should add max order size PTC with only required field', () => {
      PO.openAddPTCModal();
      PO.selectCheckType('Max Order Size');
      PO.selectCheckLevel('Warning');
      PO.selectPortfolio('portfolio_trader_1');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.createPTC();
      PO.expectTextToBeVisible('Adding Pre-trade check was successful');
    });

    it('should add max position PTC', () => {
      PO.openAddPTCModal();
      PO.selectCheckType('Max Position');
      PO.selectCheckLevel('Warning');
      PO.selectPortfolio('portfolio_trader_1');
      PO.selectChannel('UI');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.selectMultiSelect('Symbol', 'BTC');
      PO.insertNumber('Max Value Warn', '1000000');
      PO.insertNumber('Max Value Currency', '1000000');
      PO.insertNumber('Max Quantity Warn', '1000000');
      PO.insertNumber('Max Value Block', '1000000');
      PO.insertNumber('Max Quantity Block', '1000000');
      PO.createPTC();
      PO.expectTextToBeVisible('Adding Pre-trade check was successful');
    });
    it('should add max position PTC with only required field', () => {
      PO.openAddPTCModal();
      PO.selectCheckType('Max Position');
      PO.selectCheckLevel('Warning');
      PO.selectPortfolio('portfolio_trader_1');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.selectMultiSelect('Symbol', 'BTC');
      PO.createPTC();
      PO.expectTextToBeVisible('Adding Pre-trade check was successful');
    });
    it('should not add max position PTC because of missing required field', () => {
      PO.openAddPTCModal();
      PO.selectCheckType('Max Position');
      PO.selectCheckLevel('Warning');
      PO.selectPortfolio('portfolio_trader_1');
      PO.selectChannel('UI');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.createPTC();
      PO.expectTextToBeVisible('Required');
    });
    it('should reset the form when modal is closed', () => {
      PO.openAddPTCModal();
      PO.selectCheckType('Max Position');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.closePTCModal();
      PO.openAddPTCModal();
      PO.expectTextToNotBeVisibleInModal('Configuration');
    });
    it('should dismiss the modal', () => {
      PO.openAddPTCModal();
      PO.selectCheckType('Max Position');
      PO.expectTextToBeVisibleInModal('UI');
      PO.closePTCModal();
      PO.expectTextToNotBeVisible('UI');
    });
  });
  describe('Editing PTC', () => {
    it('should allow to edit PTC without changing any field, especially Symbol', () => {
      PO.openEditPTCModal('ptc1');
      PO.updatePTC();
      PO.expectTextToBeVisible('Updating Pre-trade check was successful');
    });

    it('should not allow to edit PTC after clearing Symbol', () => {
      PO.openEditPTCModal('ptc1');
      PO.clearMultiSelect('Symbol');
      PO.updatePTC();
      PO.expectTextToNotBeVisible('Updating Pre-trade check was successful');
    });

    it('should PTC be prefilled with actual values', () => {
      worker.use(
        graphql.query('PreTradeChecks', (req, res, ctx) => {
          return res.once(
            ctx.data({
              preTradeChecks: ptcToEditMock.data.preTradeChecks,
            }),
          );
        }),
      );
      PO.openEditPTCModal('ptc1');
      PO.expectRadioToBeChecked('Block');
      PO.expectTextToBeVisibleInDisabledInputInModal('ptc1');
      PO.expectTextToBeVisibleInModal('portfolio_trader_1');
      PO.expectTextToBeVisibleInModal('region: EMEA');
      PO.expectTextToBeVisibleInModal('region: AMER');
      PO.expectCheckboxToBeChecked('UI');
      PO.expectCheckboxToBeChecked('API');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.expectTextToBeVisibleInModal('Market');
      PO.expectTextToBeVisibleInModal('Limit');
      PO.expectTextToBeVisibleInModal('Stop');
      PO.expectTextToBeVisibleInModal('Stop-Limit');
    });

    it('should edit PTC', () => {
      worker.use(
        graphql.mutation('SavePreTradeCheck', (req, res, ctx) => {
          const { success, error } = PreTradeCheckInputSchema().safeParse(req.variables.request);
          if (!success) {
            return res(ctx.errors([error]));
          }
          if (req.variables.request.id !== ptcToEditMock.data.preTradeChecks[0].id) {
            return res(
              ctx.errors([
                {
                  message: 'ID to update must match with selected PTC',
                },
              ]),
            );
          }

          return res(
            ctx.data({
              clientMutationId: req.variables.clientMutationId,
            }),
          );
        }),
        graphql.query('PreTradeChecks', (req, res, ctx) => {
          return res.once(
            ctx.data({
              preTradeChecks: ptcToEditMock.data.preTradeChecks,
            }),
          );
        }),
      );
      PO.openEditPTCModal('ptc1');
      PO.expectTextToBeVisibleInDisabledInputInModal('ptc1');
      PO.selectCheckLevel('Block');
      PO.selectPortfolio('portfolio_trader_1');
      PO.selectPortfolioTags('region: EMEA');
      PO.selectPortfolioTags('region: AMER');
      PO.selectChannel('UI');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.selectMultiSelect('Allowlist', 'Market');
      PO.selectMultiSelect('Allowlist', 'Limit');
      PO.selectMultiSelect('Blocklist', 'Stop');
      PO.selectMultiSelect('Blocklist', 'Stop-Limit');
      PO.updatePTC();
      PO.expectTextToBeVisible('Updating Pre-trade check was successful');
    });

    it('should not send empty array in allowlist', () => {
      worker.use(
        graphql.mutation('SavePreTradeCheck', (req, res, ctx) => {
          const { success, error } = PreTradeCheckInputSchema().safeParse(req.variables.request);
          if (!success) {
            return res(ctx.errors([error]));
          }
          if (
            req.variables.request.configuration.find((v) => v.name === 'blocklist')?.values
              ?.length === 0
          ) {
            return res(
              ctx.errors([
                {
                  message: 'Empyt blocklist should not be sent',
                },
              ]),
            );
          }

          return res(
            ctx.data({
              clientMutationId: req.variables.clientMutationId,
            }),
          );
        }),
        graphql.query('PreTradeChecks', (req, res, ctx) => {
          return res.once(
            ctx.data({
              preTradeChecks: ptcToEditMock.data.preTradeChecks,
            }),
          );
        }),
      );
      PO.openEditPTCModal('ptc1');
      PO.expectTextToBeVisibleInDisabledInputInModal('ptc1');
      PO.selectMultiSelect('Blocklist', 'Stop');
      PO.selectMultiSelect('Blocklist', 'Stop-Limit');
      PO.updatePTC();
      PO.expectTextToBeVisible('Updating Pre-trade check was successful');
    });

    it(`should refresh PTC's after editing PTC`, () => {
      worker.use(
        graphql.query('PreTradeChecks', (req, res, ctx) => {
          return res.once(
            ctx.data({
              preTradeChecks: preTradeChecksQueryMock.data.preTradeChecks,
            }),
          );
        }),
        graphql.query('PreTradeChecks', (req, res, ctx) => {
          return res(
            ctx.data({
              preTradeChecks: refreshedPreTradeChecksQueryMock.data.preTradeChecks,
            }),
          );
        }),
      );
      PO.expectTextToBeVisible(
        'Symbol: BTC, Max Value Currency: USD, Max Quantity Warn: 10, Max Value Block: 1000000',
      );
      PO.openEditPTCModal('ptc1');
      PO.expectTextToBeVisibleInDisabledInputInModal('ptc1');
      PO.selectCheckType('Order Type');
      PO.selectCheckLevel('Block');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.selectMultiSelect('Allowlist', 'Market');
      PO.updatePTC();
      PO.expectTextToBeVisible('Updating Pre-trade check was successful');
      PO.expectTextToNotBeVisible(
        'Symbol: BTC, Max Value Currency: USD, Max Quantity Warn: 10, Max Value Block: 1000000',
      );
      PO.expectTextToBeVisible(`Allowlist: Market`);
      PO.openEditPTCModal('ptc1');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.expectTextToBeVisibleInModal('Market');
    });

    it('should edit order type and display error message in modal (BAD_REQUEST, code: 4XX)', () => {
      worker.use(
        graphql.mutation('SavePreTradeCheck', (req, res, ctx) => {
          return res(
            ctx.errors(orderTypePreValidationErrorMock.errors),
            ctx.data(orderTypePreValidationErrorMock.data),
          );
        }),
      );
      PO.openEditPTCModal('ptc1');
      PO.expectTextToBeVisibleInDisabledInputInModal('ptc1');
      PO.selectCheckType('Order Type');
      PO.selectCheckLevel('Block');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.selectMultiSelect('Allowlist', 'Market');
      PO.selectMultiSelect('Allowlist', 'Stop');
      PO.selectMultiSelect('Blocklist', 'Market');
      PO.selectMultiSelect('Blocklist', 'Stop');
      PO.clickOnPTCForm();
      PO.updatePTC();
      PO.expectTextToBeVisible('allowOrBlockDefined: One of [allowlist, blocklist] must be set');
      PO.closePTCModal();
      PO.openEditPTCModal('ptc1');
      PO.expectTextToNotBeVisible('allowOrBlockDefined: One of [allowlist, blocklist] must be set');
    });

    it('should edit PTC and display fail messaged (INTERNAL_SERVER_ERROR, code: 5XX)', () => {
      worker.use(
        graphql.mutation('SavePreTradeCheck', (req, res, ctx) => {
          return res(
            ctx.errors([
              {
                message: 'Apologies, we are currently experiencing an issue adding the PTC',
                extensions: {
                  classification: 'INTERNAL_ERROR',
                },
              },
            ]),
          );
        }),
      );
      PO.openEditPTCModal('ptc1');
      PO.expectTextToBeVisibleInDisabledInputInModal('ptc1');
      PO.selectCheckType('Order Type');
      PO.selectCheckLevel('Warning');
      PO.selectCheckType('Order Type');
      PO.selectPortfolio('portfolio_trader_1');
      PO.selectChannel('UI');
      PO.expectTextToBeVisibleInModal('Configuration');
      PO.selectMultiSelect('Allowlist', 'Market');
      PO.selectMultiSelect('Blocklist', 'Stop');
      PO.updatePTC();
      PO.expectTextToBeVisible('Updating Pre-trade check failed');
    });

    it('ensure proper values are set when opening the form for the second time', () => {
      PO.openEditPTCModal('ptc1');
      PO.expectTextToBeVisibleInDisabledInputInModal('ptc1');
      PO.expectTextToBeVisibleInModal('Max Position');
      PO.closePTCModal();
      cy.get('body').click();
      PO.openEditPTCModal('ptc2');
      PO.expectTextToBeVisibleInDisabledInputInModal('ptc2');
      PO.expectTextToBeVisibleInModal('Order Type');
    });
  });
  describe('Deleting PTC', () => {
    it('should delete PTC', () => {
      PO.openDeletePTCModal('ptc1');
      PO.expectTextToIncludeInDeleteModal('ptc1');
      PO.confirmDelete();
      PO.expectTextToBeVisible('Delete');
      PO.expectTextToBeVisible('Deleting Pre-trade check was successful');
    });
    it('should not delete PTC', () => {
      worker.use(
        graphql.mutation('DeletePreTradeCheck', (req, res, ctx) => {
          return res(
            ctx.errors([
              {
                message: 'Apologies, we are currently experiencing an issue deleting the PTC',
                extensions: {
                  classification: 'INTERNAL_ERROR',
                },
              },
            ]),
          );
        }),
      );
      cy.wait(1000);
      PO.openDeletePTCModal('ptc1');
      PO.expectTextToIncludeInDeleteModal('ptc1');
      PO.confirmDelete();
      PO.expectTextToBeVisible('Delete');
      PO.expectTextToBeVisible('Deleting Pre-trade check failed');
    });
    it(`should refresh PTC's after deleting PTC`, () => {
      worker.use(
        graphql.query('PreTradeChecks', (req, res, ctx) => {
          return res.once(
            ctx.data({
              preTradeChecks: preTradeChecksQueryMock.data.preTradeChecks,
            }),
          );
        }),
        graphql.query('PreTradeChecks', (req, res, ctx) => {
          return res(
            ctx.data({
              preTradeChecks: refreshedPreTradeChecksQueryMock.data.preTradeChecks,
            }),
          );
        }),
      );
      PO.expectTextToBeVisible(
        'Symbol: BTC, Max Value Currency: USD, Max Quantity Warn: 10, Max Value Block: 1000000',
      );
      PO.openDeletePTCModal('ptc1');
      PO.expectTextToIncludeInDeleteModal('ptc1');
      PO.confirmDelete();
      PO.expectTextToNotBeVisible(
        'Symbol: BTC, Max Value Currency: USD, Max Quantity Warn: 10, Max Value Block: 1000000',
      );
      PO.expectTextToBeVisible(`Allowlist: Market`);
    });
  });
});
