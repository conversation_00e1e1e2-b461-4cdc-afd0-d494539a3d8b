import '../../../../../../cypress/support/component';
import { FormPO } from '../../../form/__test__/FormPO.cy';
import { MODAL_CLOSE_DATA_TEST_ID } from '../../../../../ui/DialogTitle';
import { PRE_TRADE_CHECK_MODAL_TEST_ID } from '../add-ptc/AddPTCDialog';
import { PRE_TRADE_CHECK_DELETE_MODAL_TEST_ID } from '../DeletePTCDialog';
import { DELETE_PTC_BUTTON, EDIT_PRE_TRADE_CHECK_BUTTON_TEST_ID } from '../column-definitions';
import { PreTradeChecksPage } from '../PreTradeChecksPage';
import { SnackbarProvider } from 'notistack';

export class PreTradeChecksPO {
  private formPO = new FormPO();

  render() {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(
      <SnackbarProvider autoHideDuration={500} maxSnack={3}>
        <PreTradeChecksPage />
      </SnackbarProvider>,
    );
  }

  expectTextToBeVisible(text: string) {
    return cy.findByText(text).should('be.visible');
  }

  expectTextToNotBeVisible(text: string) {
    return cy.findByText(text).should('not.exist');
  }

  expectTextToBeVisibleInModal(text: string) {
    return cy
      .get(`[data-testid="${PRE_TRADE_CHECK_MODAL_TEST_ID}"]`)
      .contains(text)
      .should('be.visible');
  }

  expectTextToBeVisibleInDisabledInputInModal(text: string) {
    return cy
      .get(`[data-testid="${PRE_TRADE_CHECK_MODAL_TEST_ID}"]`)
      .find('input:disabled')
      .invoke('val')
      .should('eq', text);
  }

  expectCheckboxToBeChecked(label: string) {
    cy.get(`[data-testid="${PRE_TRADE_CHECK_MODAL_TEST_ID}"]`)
      .findByLabelText(label)
      .should('be.checked');
  }

  expectRadioToBeChecked(label: string) {
    cy.get(`[data-testid="${PRE_TRADE_CHECK_MODAL_TEST_ID}"]`)
      .findByLabelText(label)
      .should('be.checked');
  }

  expectTextToNotBeVisibleInModal(text: string) {
    return cy
      .get(`[data-testid="${PRE_TRADE_CHECK_MODAL_TEST_ID}"]`)
      .contains(text)
      .should('not.exist');
  }

  closePTCModal() {
    return cy.get(`[data-testid="${MODAL_CLOSE_DATA_TEST_ID}"]`).click();
  }

  openAddPTCModal() {
    return cy.get('button').contains('Create new check').click();
  }

  dismissAddPTCModal() {
    return cy.get('button').contains('Dismiss').click();
  }

  selectCheckType(checkType: string) {
    return this.formPO.selectByLabelText('Check type', checkType);
  }

  selectPortfolio(portfolio: string) {
    return this.formPO.selectByLabelText('Portfolio', portfolio);
  }

  selectPortfolioTags(portfolioTag: string) {
    return this.formPO.selectByLabelText('Portfolio Tags', portfolioTag);
  }

  selectChannel(channel: string) {
    return this.formPO.checkByLabelText(channel);
  }

  typeMultiselect(label: string, value: string) {
    return cy.findByLabelText(label).type(value).type('{enter}');
  }

  clickOnPTCForm() {
    return cy.get('form').click();
  }

  selectMultiSelect(label: string, value: string) {
    return this.formPO.selectByLabelText(label, value);
  }

  clearMultiSelect(label: string) {
    return this.formPO.clearByLabelText(label);
  }

  createPTC() {
    return cy.findByRole('button', { name: 'Create Check' }).click();
  }

  insertNumber(name: string, value: string) {
    return this.formPO.insertByLabelText(name, value);
  }

  insertString(name: string, value: string) {
    return this.formPO.insertByLabelText(name, value);
  }

  selectCheckLevel(value: string) {
    return cy.contains('label', value).click();
  }

  openEditPTCModal(rowId: string) {
    return cy
      .get(`[row-id="${rowId}"] [data-testid="${EDIT_PRE_TRADE_CHECK_BUTTON_TEST_ID}"]`)
      .click();
  }

  updatePTC() {
    return cy.findByRole('button', { name: 'Update Check' }).click();
  }

  openDeletePTCModal(id: string) {
    return cy.get(`[row-id="${id}"] [data-testid="${DELETE_PTC_BUTTON}"]`).click();
  }

  expectTextToIncludeInDeleteModal(text: string) {
    return cy
      .get(`[data-testid="${PRE_TRADE_CHECK_DELETE_MODAL_TEST_ID}"]`)
      .contains(text)
      .should('be.visible');
  }

  confirmDelete() {
    return cy.get('button').contains('Delete').click();
  }
}
