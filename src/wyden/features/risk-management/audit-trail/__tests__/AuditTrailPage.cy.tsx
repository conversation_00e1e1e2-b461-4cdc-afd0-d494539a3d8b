import { worker } from '../../../../../../mocks/browser';
import { graphql } from 'msw';
import '../../../../../../cypress/support/component';
import { AuditTrailPO } from './AuditTrailPO.cy';
import {
  preTradeCheckAuditLogsMock,
  preTradeCheckAuditLogsMockWithCorrectFilters,
} from './AuditLogsMock.mocks';
import { aPreTradeCheckAuditLogConnection } from '../../../../services/graphql/generated/mocks';

describe('Audit trail', () => {
  const PO = new AuditTrailPO();

  beforeEach(() => {
    worker.use(
      graphql.query('PreTradeCheckAuditLogs', (req, res, ctx) => {
        return res(ctx.data({ preTradeCheckAuditLogs: preTradeCheckAuditLogsMock }));
      }),
    );
    PO.render();
  });

  it(`Should display audit trial rows`, () => {
    PO.expectTextToBeVisible('Created At [UTC+0]');
    PO.expectTextToBeVisible('15/03/2024 17:25:26');

    PO.expectTextToBeVisible('Portfolio Id');
    PO.expectTextToBeVisible('portfolio_trader_1 Bank');

    PO.expectTextToBeVisible('Order Id');
    PO.expectTextToBeVisible('order1');
    PO.expectTextToBeVisible('order2');

    PO.expectTextToBeVisible('Check Types');
    PO.expectTextToBeVisible('OrderType, MaxPosition, MaxPosition');
    PO.expectTextToBeVisible('MaxPosition');

    PO.hasLevelInRow(0, 'Block');
    PO.hasLevelInRow(1, 'Warning');
  });
  it(`Should display error`, () => {
    worker.use(
      graphql.query('PreTradeCheckAuditLogs', (req, res, ctx) => {
        return res(
          ctx.errors([
            {
              message:
                'Apologies, we are currently experiencing an issue loading the PreTradeCheckAuditLogs',
              path: ['preTradeCheckAuditLogs'],
              extensions: {
                code: 'INTERNAL_SERVER_ERROR',
              },
              locations: [
                {
                  line: 2,
                  column: 3,
                },
              ],
            },
          ]),
          ctx.status(500),
        );
      }),
    );

    PO.expectTextToBeVisible(`An error occurred while loading data`);
    PO.expectTextToBeVisible(`Something went wrong`);
  });

  it(`Should display no data`, () => {
    worker.use(
      graphql.query('PreTradeCheckAuditLogs', (req, res, ctx) => {
        return res(ctx.data(aPreTradeCheckAuditLogConnection({ edges: [] })));
      }),
    );

    PO.expectTextToBeVisible(`No audit trail data`);
  });

  it(`Should displays details dialog with proper information`, () => {
    PO.openDetailModal('auditLog1');
    // Order Details
    PO.expectDetailToBeVisible('Audit Trail Details');
    PO.expectDetailToBeVisible('15/03/2024 17:25:26');
    PO.expectDetailToBeVisible('Order Details');
    PO.expectDetailToBeVisible('Instrument Id');
    PO.expectDetailToBeVisible('1000SATSFDUSD@Simulator');
    PO.expectDetailToBeVisible('Side');
    PO.expectDetailToBeVisible('BUY');
    PO.expectDetailToBeVisible('Quantity');
    PO.expectDetailToBeVisible('10');
    PO.expectDetailToBeVisible('Portfolio Id');
    PO.expectDetailToBeVisible('portfolio_trader_1 Bank');
    PO.expectDetailToBeVisible('Order Id');
    PO.expectDetailToBeVisible('order1');
    PO.expectDetailToBeVisible('Pre-trade Check Details');
    // First Pre-trade Check
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Type');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Order Type');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Status');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Approved With Warning');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Portfolios');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'portfolio_trader_1 Bank');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Portfolios Tags');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'region: EMEA, region: AMER');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Reason');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Not allowed order type');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Configuration');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Allowlist:');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'MARKET, LIMIT');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Blocklist:');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'STOP, STOP_LIMIT');
    // Second Pre-trade Check
    PO.expectPreTradeCheckDetailToBeVisible(1, 'Type');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'Max Position');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'Status');
    PO.expectPreTradeCheckDetailToBeVisible(0, 'Approved With Warning');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'Portfolios');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'portfolio_trader_1 Bank');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'Portfolios Tags');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'region: EMEA, region: AMER');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'Reason');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'Max position limit reached');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'Configuration');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'Allowlist:');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'MARKET, LIMIT');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'Blocklist:');
    PO.expectPreTradeCheckDetailToBeVisible(1, 'STOP, STOP_LIMIT');
    // Third Pre-trade Check
    PO.expectPreTradeCheckDetailToNotBeVisible(2, 'Configuration');
  });

  it.only('should display filters with correct parameters', () => {
    worker.use(
      graphql.query('PreTradeCheckAuditLogs', (req, res, ctx) => {
        const { request } = req.variables;
        const isPortfolioIdPresentInSearchParams =
          request?.portfolioIds?.includes('Portfolio Id test');
        const isCreatedFromPresentInSearchParams = typeof request?.from === 'string';
        const isCreatedToPresentInSearchParams = typeof request?.to === 'string';
        if (
          isPortfolioIdPresentInSearchParams &&
          isCreatedFromPresentInSearchParams &&
          isCreatedToPresentInSearchParams
        ) {
          return res(
            ctx.data({ preTradeCheckAuditLogs: preTradeCheckAuditLogsMockWithCorrectFilters }),
          );
        } else {
          return res(ctx.data({ preTradeCheckAuditLogs: preTradeCheckAuditLogsMock }));
        }
        return res(ctx.data({ preTradeCheckAuditLogs: preTradeCheckAuditLogsMock }));
      }),
    );

    PO.selectPortfolioFilter('Portfolio Id test');
    PO.selectDateFilter('Created From', '3h ago');
    PO.selectDateFilter('Created To', '3h ago');
    PO.expectTextToBeVisible('Correct filters');
  });
});
