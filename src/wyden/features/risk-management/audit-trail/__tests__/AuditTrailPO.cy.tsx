import React from 'react';
import { AuditTrailPage } from '../AuditTrailPage';
import '../../../../../../cypress/support/component';
import { AUDIT_LOG_DETAIL_ICON_TEST_ID } from '../column-definitions';
import { PRE_TRADE_CHECK_MODAL_TEST_ID } from '../../pre-trade-check/add-ptc/AddPTCDialog';
import { PRE_TRADE_CHECK_DETAILS_TEST_ID } from '../AuditTrailDetailDialog';
import { SnackbarProvider } from 'notistack';
import { AUDIT_TRAIL_FILTERS_TEST_ID } from '../AuditTrialFilters';
import { FormPO } from '@wyden/features/form/__test__/FormPO.cy';

const COLUMNS_COUNT = 7;

export class AuditTrailPO {
  private readonly formPO = new FormPO();

  render() {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(
      <SnackbarProvider>
        <AuditTrailPage />
      </SnackbarProvider>,
    );
  }

  expectTextToBeVisible(text: string | RegExp) {
    return cy.findAllByText(text).should('have.length.greaterThan', 0);
  }
  openDetailModal(rowId: string) {
    return cy.get(`[row-id="${rowId}"] [data-testid="${AUDIT_LOG_DETAIL_ICON_TEST_ID}"]`).click();
  }
  expectDetailToBeVisible(detail: string) {
    return cy.get(`[data-testid="${PRE_TRADE_CHECK_MODAL_TEST_ID}"]`).within(() => {
      cy.findAllByText(detail).should('have.length.greaterThan', 0);
    });
  }
  hasLevelInRow(rowIndex: number, level: string) {
    const levelCellIndex = rowIndex * COLUMNS_COUNT + 5;

    return cy
      .findAllByRole('gridcell')
      .eq(levelCellIndex)
      .within(() => {
        cy.findAllByText(level).should('have.length.greaterThan', 0);
      });
  }

  expectPreTradeCheckDetailToBeVisible(pretradeCheck: number, detail: string) {
    return cy
      .findAllByTestId(PRE_TRADE_CHECK_DETAILS_TEST_ID)
      .eq(pretradeCheck)
      .within(() => {
        cy.findAllByText(detail).should('have.length.greaterThan', 0);
      });
  }

  expectPreTradeCheckDetailToNotBeVisible(pretradeCheck: number, detail: string) {
    return cy
      .findAllByTestId(PRE_TRADE_CHECK_DETAILS_TEST_ID)
      .eq(pretradeCheck)
      .within(() => {
        cy.findAllByText(detail).should('have.length', 0);
      });
  }

  clickOutsideFiltersPopover() {
    return cy.get('body').click();
  }

  selectPortfolioFilter(portfolioId: string) {
    cy.get(`[data-testid="${AUDIT_TRAIL_FILTERS_TEST_ID}"]`).contains('Portfolio Id').click();
    this.formPO.insertByLabelText('Portfolio Id', portfolioId);
    cy.get(`[role="tooltip"]`).contains('Apply').click();
  }

  selectDateFilter(filterName: string, value: string) {
    cy.get(`[data-testid="${AUDIT_TRAIL_FILTERS_TEST_ID}"]`).contains(filterName).click();
    cy.get(`[role="tooltip"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }
}
