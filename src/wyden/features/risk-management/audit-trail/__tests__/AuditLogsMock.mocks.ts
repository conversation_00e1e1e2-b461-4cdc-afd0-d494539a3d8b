import {
  OrderType,
  PreTradeCheckChannels,
  PreTradeCheckLevel,
  PreTradeCheckPropertyType,
  PreTradeCheckStatus,
} from '@wyden/services/graphql/generated/graphql';
import {
  anOrderSnapshot,
  aPageInfo,
  aPortfolioResponse,
  aPreTradeCheck,
  aPreTradeCheckAuditLog,
  aPreTradeCheckAuditLogConnection,
  aPreTradeCheckAuditLogEdge,
  aPreTradeCheckProperty,
  aPreTradeCheckResult,
  aTag,
} from '@wyden/services/graphql/generated/mocks';

export const preTradeCheckAuditLogsMock = aPreTradeCheckAuditLogConnection({
  edges: [
    aPreTradeCheckAuditLogEdge({
      node: aPreTradeCheckAuditLog({
        id: 'auditLog1',
        createdAt: '2024-03-15T17:25:26',
        portfolioId: 'portfolio_trader_1 Bank',
        order: anOrderSnapshot({
          orderId: 'order1',
          orderType: OrderType.Market,
          quantity: 10,
          price: 10,
          instrumentId: '1000SATSFDUSD@FOREX@Simulator',
        }),
        preTradeCheckResults: [
          aPreTradeCheckResult({
            status: PreTradeCheckStatus.ApprovedWithWarning,
            reason: 'Not allowed order type',
            preTradeCheck: aPreTradeCheck({
              id: 'ptc1',
              type: 'OrderType',
              level: PreTradeCheckLevel.Block,
              portfolios: [
                aPortfolioResponse({
                  id: 'portfolio_trader_1 Bank',
                  name: 'portfolio_trader_1 Bank',
                }),
              ],
              portfolioTags: [
                aTag({
                  key: 'region',
                  value: 'EMEA',
                }),
                aTag({
                  key: 'region',
                  value: 'AMER',
                }),
              ],
              channels: [PreTradeCheckChannels.Ui, PreTradeCheckChannels.Api],
              configuration: [
                aPreTradeCheckProperty({
                  name: 'allowlist',
                  type: PreTradeCheckPropertyType.StringList,
                  values: ['MARKET', 'LIMIT'],
                }),
                aPreTradeCheckProperty({
                  name: 'blocklist',
                  type: PreTradeCheckPropertyType.StringList,
                  values: ['STOP', 'STOP_LIMIT'],
                }),
              ],
            }),
          }),
          aPreTradeCheckResult({
            status: PreTradeCheckStatus.ApprovedWithWarning,
            reason: 'Max position limit reached',
            preTradeCheck: aPreTradeCheck({
              id: 'ptc2',
              type: 'MaxPosition',
              level: PreTradeCheckLevel.Warn,
              portfolios: [
                aPortfolioResponse({
                  id: 'portfolio_trader_1 Bank',
                  name: 'portfolio_trader_1 Bank',
                }),
              ],
              portfolioTags: [
                aTag({
                  key: 'region',
                  value: 'EMEA',
                }),
                aTag({
                  key: 'region',
                  value: 'AMER',
                }),
              ],
              channels: [PreTradeCheckChannels.Ui, PreTradeCheckChannels.Api],
              configuration: [
                aPreTradeCheckProperty({
                  name: 'allowlist',
                  type: PreTradeCheckPropertyType.StringList,
                  values: ['MARKET', 'LIMIT'],
                }),
                aPreTradeCheckProperty({
                  name: 'blocklist',
                  type: PreTradeCheckPropertyType.StringList,
                  values: ['STOP', 'STOP_LIMIT'],
                }),
              ],
            }),
          }),
          aPreTradeCheckResult({
            status: PreTradeCheckStatus.ApprovedWithWarning,
            reason: 'Max position limit reached',
            preTradeCheck: aPreTradeCheck({
              id: 'ptc3',
              type: 'MaxPosition',
              level: PreTradeCheckLevel.Warn,
              portfolios: [
                aPortfolioResponse({
                  id: 'portfolio_trader_3 Bank',
                  name: 'portfolio_trader_3 Bank',
                }),
              ],
              portfolioTags: [
                aTag({
                  key: 'region',
                  value: 'EMEA',
                }),
                aTag({
                  key: 'region',
                  value: 'AMER',
                }),
              ],
              channels: [PreTradeCheckChannels.Ui, PreTradeCheckChannels.Api],
              configuration: [],
            }),
          }),
        ],
      }),
    }),
    aPreTradeCheckAuditLogEdge({
      node: aPreTradeCheckAuditLog({
        id: 'auditLog2',
        createdAt: '2024-03-15T17:25:26',
        portfolioId: 'portfolio_trader_2 Bank',
        order: anOrderSnapshot({ orderId: 'order2' }),
        preTradeCheckResults: [
          aPreTradeCheckResult({
            status: PreTradeCheckStatus.ApprovedWithWarning,
            reason: 'Max position limit reached',
            preTradeCheck: aPreTradeCheck({
              id: 'ptc2',
              type: 'MaxPosition',
              level: PreTradeCheckLevel.Warn,
              portfolios: [
                aPortfolioResponse({
                  id: 'portfolio_trader_2 Bank',
                  name: 'portfolio_trader_2 Bank',
                }),
              ],
              portfolioTags: [],
              channels: [PreTradeCheckChannels.Ui, PreTradeCheckChannels.Api],
              configuration: [],
            }),
          }),
        ],
      }),
    }),
  ],
  pageInfo: aPageInfo({
    hasNextPage: false,
    endCursor: 'endCursor',
  }),
});

export const preTradeCheckAuditLogsMockWithCorrectFilters = aPreTradeCheckAuditLogConnection({
  edges: [
    aPreTradeCheckAuditLogEdge({
      node: aPreTradeCheckAuditLog({
        id: 'auditLog2',
        createdAt: '2024-03-15T17:25:26',
        portfolioId: 'portfolio_trader_2 Bank',
        order: anOrderSnapshot({ orderId: 'Correct filters' }),
        preTradeCheckResults: [
          aPreTradeCheckResult({
            status: PreTradeCheckStatus.ApprovedWithWarning,
            reason: 'Max position limit reached',
            preTradeCheck: aPreTradeCheck({
              id: 'ptc2',
              type: 'MaxPosition',
              level: PreTradeCheckLevel.Warn,
              portfolios: [
                aPortfolioResponse({
                  id: 'portfolio_trader_2 Bank',
                  name: 'portfolio_trader_2 Bank',
                }),
              ],
              portfolioTags: [],
              channels: [PreTradeCheckChannels.Ui, PreTradeCheckChannels.Api],
              configuration: [],
            }),
          }),
        ],
      }),
    }),
  ],
  pageInfo: aPageInfo({
    hasNextPage: false,
    endCursor: 'endCursor',
  }),
});
