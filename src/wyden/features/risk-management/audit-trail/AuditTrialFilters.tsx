import { Button } from '@ui/Button';
import { styled } from '@ui/styled';
import { ReactComponent as CloseIcon } from '@wyden/assets/close.svg';
import { ReactComponent as TagIcon } from '@wyden/assets/tag.svg';
import { DATE_FORMATS } from '@wyden/date';
import { ResetLabel } from '@wyden/features/chip-filters/ResetLabel';
import { StaticChip } from '@wyden/features/chip-filters/StaticChip';
import { DatePicker } from '@wyden/features/chip-filters/filters/DatePicker';
import { TextArea } from '@wyden/features/chip-filters/filters/TextArea';
import { useAuditStore } from '@wyden/features/risk-management/audit-trail/useAuditStore';
import { getSpacing } from '@wyden/utils/styles';
import format from 'date-fns/format';
import { useTranslation } from 'react-i18next';

export const AUDIT_TRAIL_FILTERS_TEST_ID = 'audit-trail-filters';

export const AuditTrialFilters = () => {
  const { t } = useTranslation();
  const { setPortfolioId, setCreatedTo, setCreatedFrom, filters, clearFilters } = useAuditStore();
  return (
    <ChipContainer data-testid={AUDIT_TRAIL_FILTERS_TEST_ID}>
      {filters !== undefined ? (
        <Button onClick={clearFilters} size="xs">
          <ResetLabel>
            <CloseIcon /> {t('common.reset')}
          </ResetLabel>
        </Button>
      ) : (
        <TagIcon />
      )}
      <StaticChip
        id={'portfolioId'}
        options={(close) => (
          <TextArea
            value={''}
            onApply={setPortfolioId}
            close={close}
            headerText={t('riskManagement.auditTrailFilters.portfolioId')}
          />
        )}
      >
        {t('riskManagement.auditTrailFilters.portfolioId')}:{' '}
        {filters?.portfolioId || t('common.all')}
      </StaticChip>
      <StaticChip
        id={'createdFrom'}
        options={() => (
          <DatePicker
            headerText={t('common.createdFrom')}
            onChange={(date) => date && setCreatedFrom(date)}
            selectedDate={filters?.createdFrom}
            maxDate={filters?.createdTo}
            label={t('common.createdFrom')}
          />
        )}
      >
        {t('riskManagement.auditTrailFilters.createdFrom')}:{' '}
        {filters?.createdFrom ? format(filters?.createdFrom, DATE_FORMATS.DEFAULT_WITH_TIME) : '-'}
      </StaticChip>
      <StaticChip
        id={'createdTo'}
        options={() => (
          <DatePicker
            headerText={t('common.createdTo')}
            onChange={(date) => date && setCreatedTo(date)}
            selectedDate={filters?.createdTo}
            minDate={filters?.createdFrom}
            label={t('common.createdTo')}
          />
        )}
      >
        {t('riskManagement.auditTrailFilters.createdTo')}:{' '}
        {filters?.createdTo ? format(filters?.createdTo, DATE_FORMATS.DEFAULT_WITH_TIME) : '-'}
      </StaticChip>
    </ChipContainer>
  );
};

const ChipContainer = styled('div')`
  display: flex;
  gap: ${getSpacing(1)};
  margin: ${getSpacing(2)} 0;
`;
