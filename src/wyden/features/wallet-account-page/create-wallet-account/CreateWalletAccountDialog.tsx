import { Dialog } from '@ui/Dialog';
import { styled } from '@ui/styled';
import { getSpacing } from '@wyden/utils/styles';
import { TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';
import { useWalletAccountManagement } from '../useWalletAccountManagement';
import { useWalletAccountStore } from '../useWalletAccountStore';
import { CreateWalletAccountForm, WalletAccountFormSchema } from './CreateWalletAccountForm';
import { Resource, Scope, WalletType } from '../../../services/graphql/generated/graphql';
import { usePermissions } from '../../../hooks/usePermissions';

export const CreateWalletAccountDialog = () => {
  const { createWalletAccount } = useWalletAccountManagement();
  const {
    createWalletAccountDialogOpen: createWalletDialogOpen,
    toggleCreateWalletAccountDialog: toggleCreateWalletDialog,
  } = useWalletAccountStore();
  const { t } = useTranslation();
  const { checkIfPermitted } = usePermissions();

  const getWalletAccountFormProps = (
    t: TFunction<'translation', undefined>,
    generalWalletPermitted: boolean = true,
    nostroPermitted: boolean = true,
    vostroPermitted: boolean = true,
  ) => ({
    name: {
      label: t('common.name'),
      required: true,
      id: 'name',
    },
    walletType: {
      label: t('walletAccountData.walletAccountForm.walletType'),
      options: [
        {
          id: WalletType.Nostro,
          label: t('common.nostro'),
          disabled: !generalWalletPermitted && !nostroPermitted,
        },
        {
          id: WalletType.Vostro,
          label: t('common.vostro'),
          disabled: !generalWalletPermitted && !vostroPermitted,
        },
      ],
      required: true,
    },
  });

  return (
    <StyledDialog open={createWalletDialogOpen} onClose={toggleCreateWalletDialog}>
      <CreateWalletAccountForm
        onSubmit={(values) => {
          createWalletAccount(values);
          toggleCreateWalletDialog();
        }}
        props={getWalletAccountFormProps(
          t,
          checkIfPermitted({
            resource: Resource.Wallet,
            scope: Scope.Read,
          }),
          checkIfPermitted({
            resource: Resource.WalletNostro,
            scope: Scope.Create,
          }),
          checkIfPermitted({
            resource: Resource.WalletVostro,
            scope: Scope.Create,
          }),
        )}
        schema={WalletAccountFormSchema}
      />
    </StyledDialog>
  );
};

const StyledDialog = styled(Dialog)`
  padding-top: ${getSpacing(3)};
`;
