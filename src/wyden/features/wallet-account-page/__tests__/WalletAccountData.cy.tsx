import { graphql } from 'msw';
import { worker } from '../../../../../mocks/browser';
import { Resource, Scope } from '../../../services/graphql/generated/graphql';
import { aWalletAccountResponse } from '../../../services/graphql/generated/mocks';
import { WalletAccountDataPO } from './WalletAccountDataPO.cy';

describe('<WalletAccountData />', () => {
  let walletAccountData: WalletAccountDataPO;

  beforeEach(() => {
    walletAccountData = new WalletAccountDataPO();

    worker.use(
      graphql.query('WalletAccountSearch', (req, res, ctx) => {
        return res(
          ctx.data({
            walletAccountSearch: {
              edges: [
                {
                  node: aWalletAccountResponse({
                    name: 'Wallet account 1',
                    createdAt: '**********',
                    scopes: [Scope.Read],
                  }),
                },
                {
                  node: aWalletAccountResponse({
                    name: 'Wallet account 2',
                    createdAt: '**********',
                    scopes: [Scope.Read],
                  }),
                },
                {
                  node: aWalletAccountResponse({
                    name: 'Wallet account 3',
                    createdAt: '**********',
                    scopes: [Scope.Read, Scope.Trade],
                  }),
                },
              ],
            },
          }),
        );
      }),
    );

    walletAccountData.render();
  });

  it('Should render wallet accounts entities in grid', () => {
    walletAccountData.expectTextToBeVisible('Wallet account 1');
    walletAccountData.expectTextToBeVisible('Wallet account 2');
    walletAccountData.expectTextToBeVisible('Wallet account 3');
  });

  it(`Should display error`, () => {
    worker.use(
      graphql.query('WalletAccountSearch', (req, res, ctx) => {
        return res(
          ctx.errors([
            {
              message: 'Apologies, we are currently experiencing an issue loading wallets',
              path: ['walletSearch'],
              extensions: {
                code: 'INTERNAL_SERVER_ERROR',
              },
              locations: [
                {
                  line: 2,
                  column: 3,
                },
              ],
            },
          ]),
          ctx.status(500),
        );
      }),
    );

    walletAccountData.expectTextToBeVisible(`An error occurred while loading data`);
    walletAccountData.expectTextToBeVisible(`Something went wrong`);
  });

  it("Should disable 'Create new wallet' button when no proper permission", () => {
    walletAccountData.render();

    worker.use(
      graphql.query('WalletAccountSearch', (req, res, ctx) => {
        return res(
          ctx.data({
            walletAccountSearch: {
              edges: [
                {
                  node: aWalletAccountResponse({
                    name: 'Wallet account 1',
                    createdAt: '**********',
                  }),
                },
              ],
            },
          }),
        );
      }),
      graphql.query('StaticPermissions', (req, res, ctx) => {
        return res(
          ctx.data({
            userStaticPermissions: [],
            groupStaticPermissions: [],
          }),
        );
      }),
    );

    walletAccountData.expectTextToBeVisible('Wallet account 1');
    walletAccountData.expectAllDataLoaded();
    walletAccountData.expectCreateNewWalletButtonToBeDisabled();
  });

  it('Should create wallet when proper permission', () => {
    worker.use(
      graphql.query('WalletAccountSearch', (req, res, ctx) => {
        return res.once(
          ctx.data({
            walletAccountSearch: {
              edges: [
                {
                  node: aWalletAccountResponse({
                    name: 'Wallet account 1',
                    createdAt: '**********',
                  }),
                },
              ],
            },
          }),
        );
      }),
      graphql.query('WalletAccountSearch', (req, res, ctx) => {
        return res(
          ctx.data({
            walletAccountSearch: {
              edges: [
                {
                  node: aWalletAccountResponse({
                    name: 'Wallet account 1',
                  }),
                },
                {
                  node: aWalletAccountResponse({
                    name: 'Wallet Account Name',
                  }),
                },
              ],
            },
          }),
        );
      }),
      graphql.query('StaticPermissions', (req, res, ctx) => {
        return res(
          ctx.data({
            userStaticPermissions: [
              {
                resource: Resource.Wallet,
                scope: Scope.Create,
                resourceId: null,
              },
            ],
            groupStaticPermissions: [],
          }),
        );
      }),
      graphql.mutation('CreateWalletAccount', (req, res, ctx) => {
        return res(
          ctx.data({
            createWalletAccount: {
              status: 'OK',
            },
          }),
        );
      }),
    );

    walletAccountData.clickCreateNewWallet();
    walletAccountData.walletAccountFormPO.insertWalletName('Wallet Account Name');
    walletAccountData.walletAccountFormPO.clickSubmitWallet();
    walletAccountData.expectElementToNotBeVisible('Create wallet');
    // walletAccountData.expectTextToBeVisible('Adding wallet succeeded');
    // walletAccountData.expectTextToBeVisible('Wallet account 1');
    // walletAccountData.expectTextToBeVisible('Wallet Account Name');
  });
});
