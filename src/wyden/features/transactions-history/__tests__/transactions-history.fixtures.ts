import { aClientCashTrade } from '../../../services/graphql/generated/mocks';
const toValues = (
  data: {
    exchangeName?: string;
    instrumentId?: string;
    type?: string;
    quantity: string;
    portfolioId?: string;
    portfolioName?: string;
    price?: string;
    dateTime?: string;
    currency?: string;
    baseCurrency?: string;
    feeCurrency?: string;
    orderId?: string;
    settled?: string;
  }[],
): string[][] => {
  return data.map((item) => Object.values(item));
};

export const TRANSACTIONS_HISTORY_FIXTURES = {
  initial: {
    stub: {
      loading: false,
      portfolios: [],
      types: [],
      transactionsHistory: [],
      instruments: [],
    },
  },
  formattedDate: {
    stub: {
      loading: false,
      portfolios: [],
      types: [],
      instruments: ['DOGEUSD', 'BTCUSD', 'ETHUSD'],
    },
    expected: {
      visibleRows: toValues([
        {
          currency: 'USDT',
          quantity: 'USD 1',
          portfolioName: 'Maciej1692193907',
          price: 'USDT 26,245.66',
        },
      ]),
    },
  },
  common: {
    stub: {
      loading: false,
      portfolios: [],
      types: [],
      instruments: ['DOGEUSD', 'BTCUSD', 'ETHUSD'],
    },
    selectedDateRange: {
      startDate: '03/14/2021',
      endDate: '03/16/2021',
    },
    expected: {
      visibleRows: toValues([
        {
          currency: 'USDT',
          quantity: '0.00001',
          portfolioName: 'Maciej1692193907',
          price: 'USDT 26,245.66',
        },
      ]),
      invisibleRows: toValues([
        {
          exchangeName: 'Kraken',
          instrumentId: 'ETHUSD',
          type: 'SELL',
          quantity: '78',
          portfolioId: 'portfolio_trader_2',
          price: '250',
          dateTime: '13/03/2021 15:00',
        },
        {
          exchangeName: 'Coinbase',
          instrumentId: 'DOGEUSD',
          type: 'LIMIT',
          quantity: '89',
          portfolioId: 'portfolio_trader_2',
          price: '300',
          dateTime: '17/03/2021 15:00',
        },
      ]),
    },
  },
  clearDateRange: {
    stub: {},
    selectedDateRange: {
      startDate: '03/14/2021',
      endDate: '03/16/2021',
    },
  },
  byClientTransactionsId: {
    stub: {},
    selectedClientId: 'fa69be69-6795-447a-8c41-5a27405ab69a',
  },
};

export const TRANSACTION_EDGE = {
  node: {
    ...aClientCashTrade(),
    price: 26245.66,
    currency: 'USDT',
    feeCurrency: 'DOGE',
    baseCurrency: 'DOGE',
    quantity: '0.00001',
    portfolioName: 'Maciej1692193907',
    orderId: 'fa69be69-6795-447a-8c41-5a27405ab69a',
    updatedAt: new Date('03/15/2021').getTime().toString(),
    settled: 'false',
  },
};

export const TRANSACTION_EDGE_WITH_CORRECT_FILTERS = {
  node: {
    ...aClientCashTrade(),
    price: 26245.66,
    currency: 'USDT',
    feeCurrency: 'DOGE',
    baseCurrency: 'DOGE',
    quantity: '0.00001',
    portfolioName: 'Maciej1692193907',
    orderId: 'Correct filters',
    updatedAt: new Date('03/15/2021').getTime().toString(),
  },
};

const PAGE_INFO = {
  hasNextPage: false,
  endCursor: '22',
};

export const PAGINATED_TRANSACTIONS = {
  transactions: {
    edges: [TRANSACTION_EDGE],
    pageInfo: PAGE_INFO,
  },
};

export const PAGINATED_TRANSACTIONS_WITH_CORRECT_FILTERS = {
  transactions: {
    edges: [TRANSACTION_EDGE_WITH_CORRECT_FILTERS],
    pageInfo: PAGE_INFO,
  },
};

export const EXPECTED_PAGINATED_TRANSACTIONS = toValues([
  {
    price: 'USDT 26,245.66',
    currency: 'USDT',
    feeCurrency: 'DOGE',
    baseCurrency: 'DOGE',
    quantity: 'DOGE 0.00001',
    portfolioName: 'Maciej1692193907',
    orderId: 'fa69be69-6795-447a-8c41-5a27405ab69a',
    settled: 'Unsettled',
  },
]);
