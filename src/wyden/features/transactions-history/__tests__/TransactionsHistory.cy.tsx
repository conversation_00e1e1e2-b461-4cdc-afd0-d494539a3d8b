import '../../../../../cypress/support/component';
import { TransactionsHistoryPO } from './TransactionsHistoryPO.cy';
import {
  EXPECTED_PAGINATED_TRANSACTIONS,
  PAGINATED_TRANSACTIONS,
  PAGINATED_TRANSACTIONS_WITH_CORRECT_FILTERS,
  TRANSACTIONS_HISTORY_FIXTURES,
} from './transactions-history.fixtures';
import { graphql } from 'msw';
import { transactionsResponseMocks } from './transactions-response.mocks';
import { worker } from '../../../../../mocks/browser';
import { aPortfolioResponse } from '../../../services/graphql/generated/mocks';
import { Scope } from '../../../services/graphql/generated/graphql';
import { useTransactionHistoryFilters } from '../useTransactionHistoryFilters';

describe('TransactionsHistory', () => {
  const transactionsHistoryPO = new TransactionsHistoryPO();

  beforeEach(() => {
    worker.use(
      graphql.query('TransactionTypes', (req, res, ctx) => {
        return res(
          ctx.data({
            data: {
              transactionTypes: [
                'CLIENT_CASH_TRADE',
                'STREET_CASH_TRADE',
                'CLIENT_ASSET_TRADE',
                'STREET_ASSET_TRADE',
              ],
            },
          }),
        );
      }),
      graphql.query('Transactions', (req, res, ctx) => {
        if (!req.variables?.search?.after && !!req.variables?.search?.first) {
          return res(
            ctx.data({
              ...transactionsResponseMocks[0].response.data,
            }),
          );
        }
        const response =
          transactionsResponseMocks.find(
            (v) =>
              v.parameters.search.first === req.variables?.search?.first &&
              v.parameters.search.after === req.variables?.search?.after,
          )?.response.data || {};
        return res(
          ctx.data({
            ...response,
          }),
        );
      }),
    );
    transactionsHistoryPO.render();
  });

  it('should scroll down to all records in transactions history', () => {
    // TODO [AC-1371]
    // mock pagination
    // ag grid infinite scroll end of data
    transactionsHistoryPO.scrollDownToEnd();
  });

  it('should display order history columns', () => {
    transactionsHistoryPO.expectAGGridTextToBeVisible('Order ID');
    transactionsHistoryPO.expectAGGridTextToBeVisible('Base Currency');
    transactionsHistoryPO.expectAGgridTextToBeVisible('Quantity');
    transactionsHistoryPO.expectAGgridTextToBeVisible('Currency');
    transactionsHistoryPO.expectAGgridTextToBeVisible('Price');
    transactionsHistoryPO.expectAGgridTextToBeVisible('Fee');
    transactionsHistoryPO.expectAGgridTextToBeVisible('Portfolio Name');
    transactionsHistoryPO.expectAGgridTextToBeVisible('Counter Portfolio Name');
    transactionsHistoryPO.expectAGgridTextToBeVisible('Venue Account Name');
    transactionsHistoryPO.expectAGgridTextToBeVisible('Date Time [UTC+0]');
  });

  it('should display transactions history with formatted dates', () => {
    transactionsHistoryPO.expectAGgridDataToBeVisible(
      TRANSACTIONS_HISTORY_FIXTURES.formattedDate.expected.visibleRows,
    );
  });

  it('should display no transactions', () => {
    worker.use(graphql.query('Transactions', (req, res, ctx) => res(ctx.data({}))));
    transactionsHistoryPO.render();

    transactionsHistoryPO.expectTextToBeVisible('No transactions to display');
  });

  it('should display transactions history only for DOGEUSD', () => {
    worker.use(
      graphql.query('Transactions', (req, res, ctx) => res(ctx.data(PAGINATED_TRANSACTIONS))),
    );

    transactionsHistoryPO.expectAGgridDataToBeVisible(EXPECTED_PAGINATED_TRANSACTIONS);
  });

  it('should display transactions history only for - 2 client Order ID', () => {
    worker.use(
      graphql.query('Transactions', (req, res, ctx) => res(ctx.data(PAGINATED_TRANSACTIONS))),
    );

    transactionsHistoryPO.setOrderIdFilter(
      TRANSACTIONS_HISTORY_FIXTURES.byClientTransactionsId.selectedClientId,
    );

    transactionsHistoryPO.expectAGgridDataToBeVisible(EXPECTED_PAGINATED_TRANSACTIONS);
    transactionsHistoryPO.expectAGgridDataToBeNotVisible(
      TRANSACTIONS_HISTORY_FIXTURES.common.expected.invisibleRows,
    );
  });

  it.skip('should display persisted filters', () => {
    useTransactionHistoryFilters.getState().reset();

    worker.use(
      graphql.query('UserData', (req, res, ctx) => {
        const userData = {
          timestamp: Date.now(),
          widgets: [
            {
              id: 'test-widget',
              type: 'BASE',
              filters: {
                createdFrom: '2021-03-14',
                createdTo: '2021-03-16',
                orderId: 'some-order-id',
                currency: ['ETH', 'BTC'],
                transactionType: ['CLIENT_CASH_TRADE'],
                accounts: [],
                portfolios: [],
                wallets: [],
              },
            },
          ],
          workspaces: [
            {
              name: 'current-workspace',
              id: 'current-workspace',
              json: {},
              isTrading: false,
              portfolio: aPortfolioResponse({
                id: 'portfolio_trader_1',
                name: 'portfolio_trader_1',
                scopes: [Scope.Manage, Scope.Trade, Scope.Read],
              }),
            },
          ],
        };
        return res(
          ctx.data({
            userData: { data: JSON.stringify(userData) },
          }),
        );
      }),
    );

    transactionsHistoryPO.expectTextToBeVisible('some-order-id');
    transactionsHistoryPO.expectTextToBeVisible('ETH, BTC');
    transactionsHistoryPO.expectTextToBeVisible('CLIENT_CASH_TRADE');
    transactionsHistoryPO.expectTextToBeVisible('14/03/2021');
    transactionsHistoryPO.expectTextToBeVisible('16/03/2021');
  });

  it('should display selected filters', () => {
    worker.use(
      graphql.query('Transactions', (req, res, ctx) => {
        const { search } = req.variables;

        const isAccountPresentInSearchParams = search?.accountId?.includes('BitMEX-testnet1');
        const isPortfolioPresentInSearchParams =
          search?.portfolioId?.includes('portfolio_trader_1');
        const isWalletPresentInSearchParams = search?.accountId?.includes('Bank');
        const isCurrencyPresentInSearchParams = search?.currency?.includes('USD');
        const isCreatedFromPresentInSearchParams = typeof search?.from === 'string';
        const isCreatedToPresentInSearchParams = typeof search?.to === 'string';
        const isOrderIdPresentInSearchParams = search?.orderId === 'Test Order ID';
        const isExecutionIdPresentInSearchParams = search?.executionId === 'Test Execution ID';
        const isVenueExecutionIdPresentInSearchParams =
          search?.venueExecutionId === 'Test Venue Execution Id';
        const isRootExecutionIdPresentInSearchParams =
          search?.rootExecutionId === 'Test Root Execution Id';
        const isTransactionIdPresentInSearchParams = search?.uuid === 'Test Transaction ID';

        const filtersAreCorrectlyAppliedToRequest =
          isOrderIdPresentInSearchParams &&
          isAccountPresentInSearchParams &&
          isCurrencyPresentInSearchParams &&
          isPortfolioPresentInSearchParams &&
          isWalletPresentInSearchParams &&
          isCreatedFromPresentInSearchParams &&
          isCreatedToPresentInSearchParams &&
          isExecutionIdPresentInSearchParams &&
          isVenueExecutionIdPresentInSearchParams &&
          isRootExecutionIdPresentInSearchParams &&
          isTransactionIdPresentInSearchParams;

        if (filtersAreCorrectlyAppliedToRequest) {
          return res(ctx.data(PAGINATED_TRANSACTIONS_WITH_CORRECT_FILTERS));
        } else {
          return res(ctx.data(PAGINATED_TRANSACTIONS));
        }
      }),
    );

    transactionsHistoryPO.selectAccountsFilter('BitMEX-testnet1');
    transactionsHistoryPO.selectWalletsFilter('Bank');
    transactionsHistoryPO.selectDateFilter('Created from', '3h ago');
    transactionsHistoryPO.selectDateFilter('Created to', '3h ago');
    transactionsHistoryPO.setOrderIdFilter('Test Order ID');
    transactionsHistoryPO.insertOrderIDByLabel('Execution ID', 'Test Execution ID');
    transactionsHistoryPO.insertOrderIDByLabel('Venue Execution Id', 'Test Venue Execution Id');
    transactionsHistoryPO.insertOrderIDByLabel('Root Execution Id', 'Test Root Execution Id');
    transactionsHistoryPO.insertOrderIDByLabel('Transaction ID', 'Test Transaction ID');
    transactionsHistoryPO.selectCurrencyFilter('USD');

    transactionsHistoryPO.expectFilterToBeSelected('BitMEX-testnet1');
    transactionsHistoryPO.expectFilterToBeSelected('Bank');
    transactionsHistoryPO.expectFilterToBeSelected('Test Order ID');
    transactionsHistoryPO.expectFilterToBeSelected('Test Execution ID');
    transactionsHistoryPO.expectFilterToBeSelected('Test Venue Execution Id');
    transactionsHistoryPO.expectFilterToBeSelected('Test Root Execution Id');
    transactionsHistoryPO.expectFilterToBeSelected('Test Transaction ID');
    transactionsHistoryPO.expectFilterToBeSelected('USD');

    transactionsHistoryPO.expectTextToBeVisible('Correct filters');
  });
});
