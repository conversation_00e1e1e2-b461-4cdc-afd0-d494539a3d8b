import { VenueAccountWithVenue } from '@wyden/hooks/useVenueAccounts';
import {
  PortfolioResponse,
  PortfolioResponseSchema,
  TransactionType,
  VenueAccountSchema,
} from '@wyden/services/graphql/generated/graphql';
import { z } from 'zod';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface TransactionHistoryFilter {
  accounts: VenueAccountWithVenue[];
  wallets: VenueAccountWithVenue[];
  portfolios: PortfolioResponse[];
  currency: string[];
  orderId: string;
  rootExecutionId: string;
  executionId: string;
  venueExecutionId: string;
  createdTo: Date | null;
  createdFrom: Date | null;
  transactionType: TransactionType[];
  transactionId: string;
}

export const TransactionHistoryFiltersSchema = z.object({
  accounts: z.array(VenueAccountSchema().extend({ venue: z.string() })),
  wallets: z.array(VenueAccountSchema().extend({ venue: z.string() })),
  portfolios: z.array(PortfolioResponseSchema()),
  currency: z.array(z.string()).optional(),
  orderId: z.string().optional(),
  rootExecutionId: z.string().optional(),
  executionId: z.string().optional(),
  venueExecutionId: z.string().optional(),
  createdTo: z.date().nullable().optional(),
  createdFrom: z.date().nullable().optional(),
  transactionType: z.array(z.nativeEnum(TransactionType)).optional(),
});

export enum TransactionHistoryFilterLists {
  COLUMN_LIST = 'columnList',
  ACCOUNTS_LIST = 'accounts',
  WALLETS_LIST = 'wallets',
  PORTFOLIOS_LIST = 'portfolios',
  TRANSACTION_TYPES_LIST = 'transactionType',
  CREATED_FROM_INPUT = 'createdFrom',
  CREATED_TO_INPUT = 'createdTo',
  ORDER_ID_INPUT = 'orderId',
  ROOT_EXECUTION_ID_INPUT = 'rootExecutionId',
  EXECUTION_ID_INPUT = 'executionId',
  VENUE_EXECUTION_ID_INPUT = 'venueExecutionId',
  CURRENCY_INPUT = 'currency',
  TRANSACTION_ID_INPUT = 'transactionId',
}

interface TransactionHistoryFiltersStore {
  selectedList: TransactionHistoryFilterLists;
  setSelectedList: (list: TransactionHistoryFilterLists) => void;
  filters: TransactionHistoryFilter;
  setAccounts: (accounts: VenueAccountWithVenue[]) => void;
  setWallets: (wallets: VenueAccountWithVenue[]) => void;
  setPortfolios: (portfolios: PortfolioResponse[]) => void;
  setCurrency: (currency: string[]) => void;
  toggleTransactionType: (type: TransactionType) => void;
  clearTransactionType: () => void;
  setOrderId: (intOrderId: string) => void;
  setTransactionId: (transactionId: string) => void;
  setRootExecutionId: (rootExecutionId: string) => void;
  setExecutionId: (executionId: string) => void;
  setVenueExecutionId: (venueExecutionId: string) => void;
  setCreatedTo: (createdTo: Date | null) => void;
  setCreatedFrom: (createdFrom: Date | null) => void;
  onRemoveFilter: (filter: string) => void;
  clear: () => void;
  columnListSelected: () => void;
  setInitialState: (
    portfolios: PortfolioResponse[],
    accounts: VenueAccountWithVenue[],
    wallets: VenueAccountWithVenue[],
  ) => void;
  reset: () => void;
  isInitialized?: boolean;
}

const initialValues = {
  accounts: [],
  wallets: [],
  portfolios: [],
  currency: [],
  orderId: '',
  rootExecutionId: '',
  executionId: '',
  venueExecutionId: '',
  createdFrom: null,
  createdTo: null,
  transactionType: [],
  transactionId: '',
};

export const useTransactionHistoryFilters = create<TransactionHistoryFiltersStore>()(
  devtools(
    (set) => ({
      selectedList: TransactionHistoryFilterLists.COLUMN_LIST,
      isInitialized: false,
      setSelectedList: (list) => set(() => ({ selectedList: list })),
      filters: initialValues,
      setCreatedTo: (createdTo) =>
        set((state) => ({
          filters: {
            ...state.filters,
            createdTo,
          },
        })),

      setCreatedFrom: (createdFrom) =>
        set((state) => ({
          filters: {
            ...state.filters,
            createdFrom,
          },
        })),

      setAccounts: (accounts) =>
        set((state) => ({
          filters: {
            ...state.filters,
            accounts,
          },
        })),
      setWallets: (wallets) =>
        set((state) => ({
          filters: {
            ...state.filters,
            wallets,
          },
        })),
      setPortfolios: (portfolios) =>
        set((state) => ({
          filters: {
            ...state.filters,
            portfolios,
          },
        })),
      setCurrency: (currency) =>
        set((state) => ({
          filters: {
            ...state.filters,
            currency,
          },
        })),
      toggleTransactionType: (type) =>
        set((state) => {
          const transactionType = [...(state.filters.transactionType || [])];
          const index = transactionType.indexOf(type);
          if (index > -1) {
            transactionType.splice(index, 1);
          } else {
            transactionType.push(type);
          }
          return {
            filters: {
              ...state.filters,
              transactionType,
            },
          };
        }),
      clearTransactionType: () =>
        set((state) => {
          return {
            filters: {
              ...state.filters,
              transactionType: [],
            },
          };
        }),
      setOrderId: (orderId) =>
        set((state) => ({
          filters: {
            ...state.filters,
            orderId,
          },
        })),
      setTransactionId: (transactionId) =>
        set((state) => ({
          filters: {
            ...state.filters,
            transactionId,
          },
        })),
      setRootExecutionId: (rootExecutionId) =>
        set((state) => ({
          filters: {
            ...state.filters,
            rootExecutionId,
          },
        })),
      setExecutionId: (executionId) =>
        set((state) => ({
          filters: {
            ...state.filters,
            executionId,
          },
        })),
      setVenueExecutionId: (venueExecutionId) =>
        set((state) => ({
          filters: {
            ...state.filters,
            venueExecutionId,
          },
        })),
      clear: () => {
        return set(() => ({ filters: { ...initialValues } }));
      },
      columnListSelected: () =>
        set(() => ({ selectedList: TransactionHistoryFilterLists.COLUMN_LIST })),
      onRemoveFilter: (filter) => {
        set((state) => {
          return {
            ...state,
            filters: {
              ...state.filters,
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              [filter]: filter in initialValues ? initialValues[filter] : null,
            },
          };
        });
      },
      setInitialState: (portfolios, accounts, wallets) => {
        set((state) => {
          if (state.isInitialized) return state;

          return {
            filters: {
              ...initialValues,
              portfolios,
              accounts,
              wallets,
            },
            isInitialized: true,
          };
        });
      },
      reset: () => set(() => ({ filters: initialValues, isInitialized: false })),
    }),
    { name: 'TransactionHistoryFilters' },
  ),
);
