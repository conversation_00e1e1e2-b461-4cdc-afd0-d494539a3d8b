import { useTheme } from '@mui/material';
import LinearProgress from '@mui/material/LinearProgress';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { selectAGGridTheme, useThemeStore } from '@ui/useThemeStore';
import { INFINITE_SCROLL_PAGE_SIZE } from '@wyden/constants';
import {
  ColDef,
  ColGroupDef,
  ColumnApi,
  FilterChangedEvent,
  GetRowIdParams,
  GridApi,
  GridReadyEvent,
  IServerSideDatasource,
  IsFullWidthRowParams,
  ModelUpdatedEvent,
  RowDataUpdatedEvent,
  RowDragEndEvent,
  RowModelType,
  RowNode,
  RowStyle,
} from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import 'ag-grid-enterprise';
import { AgGridReact } from 'ag-grid-react';
import { ResizeSensor } from 'css-element-queries';
import debounce from 'lodash/debounce';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

export interface WydenGridProps<T> {
  onGridReady?: (params: GridReadyEvent) => void;
  onFirstDataRendered?: (params: GridReadyEvent) => void;
  getRowId?: (params: GetRowIdParams) => string;
  getRowClass?: (params: { data: unknown }) => string;
  getRowStyle?: (params: { data: unknown }) => RowStyle | undefined;
  getRowHeight?: (params: { data: unknown }) => number | undefined | null;
  columnDefs: (ColDef<T> | ColGroupDef<T>)[] | null;
  defaultColDefs?: (ColDef<T> | ColGroupDef<T>) | null;
  onFilterChanged?: (data: FilterChangedEvent<T>) => void;
  rowData?: T[];
  noRowsOverlayComponent?: () => JSX.Element;
  onColumnMoved?: (params: { api: GridApi; columnApi: ColumnApi }) => void;
  isExternalFilterPresent?: () => boolean;
  doesExternalFilterPass?: (node: RowNode) => boolean;
  'data-testid'?: string;
  masterDetail?: boolean;
  treeData?: boolean;
  // It is deprecated instead detailCellRenderer should be used but, we probably will remove master details anyway so not touching it.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  detailCellRendererParams?: any;
  isFullWidthRow?: (params: IsFullWidthRowParams) => boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  fullWidthCellRenderer?: any;
  autoGroupColumnDef?: ColDef;
  getDataPath?: (data: T) => string[];
  suppressRowClickSelection?: boolean;
  isDataLoading?: boolean;
  rowModelType?: RowModelType;
  embedFullWidthRows?: boolean;
  quickFilterText?: string;
  alwaysMultiSort?: boolean;
  detailCellRenderer?: unknown;
  enableCellTextSelection?: boolean;
  rowDragManaged?: boolean;
  rowDragEntireRow?: boolean;
  onRowDragEnd?: (e: RowDragEndEvent) => void;
  serverSideDatasource?: IServerSideDatasource;
  serverSideInfiniteScroll?: boolean;
  onModelUpdated?: (data: ModelUpdatedEvent) => void;
  onRowDataUpdated?: (data: RowDataUpdatedEvent<T>) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  components?: any;
}

const internalGetRowId = (params: GetRowIdParams) => params.data.id;

export function WydenGrid<T>(props: WydenGridProps<T>) {
  const theme = useTheme();
  const gridRef = useRef<AgGridReact>(null);
  const themeClassName = useThemeStore(selectAGGridTheme);
  const resizedElement = useRef<HTMLDivElement>(null);
  const cellTextSelectionIsEnabled = props.enableCellTextSelection === false ? false : true;
  const [rowData, setRowData] = useState<T[]>(props.rowData || []);
  const rowDataRef = useRef<T[]>(props.rowData || []);

  const wydenGridStyles = {
    position: 'relative',
    height: '100%',
    width: '100%',
    '--ag-font-size': '11px',
    '--ag-cell-horizontal-padding': '5px',
    '--ag-border-color': 'transparent',
    '--ag-data-color': color[theme.palette.mode].textElementsTextSecondary,
    '--ag-background-color': color[theme.palette.mode].fillsSurfaceSurfaceSecondary,
    '--ag-row-hover-color': color[theme.palette.mode].fillsElementsFillHover,
    '--ag-row-border-color': color[theme.palette.mode].borderElementsBorderWeak,
    '--ag-header-background-color': color[theme.palette.mode].fillsSurfaceSurfacePrimary,
    '--ag-odd-row-background-color': color[theme.palette.mode].fillsSurfaceSurfaceTertiary,
    '--ag-checkbox-checked-color': color[theme.palette.mode].textSemanticTextErrorPrimary,
    '--ag-checkbox-unchecked-color': color[theme.palette.mode].fillsElementsFillPrimary,
  } as const;

  const defaultColDef = useMemo(
    () =>
      props.defaultColDefs || {
        filter: true,
        sortable: true,
        menuTabs: [],
      },
    [props.defaultColDefs],
  );

  const autoSize = (columnApi: ColumnApi, gridApi: GridApi) => {
    columnApi.autoSizeAllColumns();
    gridApi.sizeColumnsToFit();
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedAutosize = useCallback(
    debounce((params: GridReadyEvent) => {
      autoSize(params.columnApi, params.api);
    }, 200),
    [],
  );

  const onGridReady = (params: GridReadyEvent) => {
    // tslint:disable-next-line: no-unused-expression
    if (resizedElement.current) {
      new ResizeSensor(resizedElement.current, () => debouncedAutosize(params));
    }

    props.onGridReady && props.onGridReady(params);
  };

  const getRowId = props.getRowId || internalGetRowId;

  useEffect(() => {
    gridRef.current?.api?.onFilterChanged();
  }, [props.doesExternalFilterPass]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const isCypressRunning = !!(window as any).Cypress;

  // Mechanism below disable no rows overlay flickering
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSetRowData = useCallback(
    debounce((data: T[]) => {
      setRowData(data);
      rowDataRef.current = data;
    }, 300),
    [],
  );

  useEffect(() => {
    const rowDataToSet = props.rowData || [];
    const existingDataIsEmpty = !rowDataRef?.current?.length;
    const incomingDataIsEmpty = !rowDataToSet?.length;

    if (existingDataIsEmpty && incomingDataIsEmpty) {
      return;
    }

    debouncedSetRowData(rowDataToSet);
  }, [props.rowData, debouncedSetRowData]);

  return (
    <div
      data-testid={props['data-testid']}
      className={themeClassName}
      style={wydenGridStyles}
      ref={resizedElement}
    >
      <StyledProgress $isPending={!!props.isDataLoading} />
      <AgGridReact<T>
        ref={gridRef}
        rowHeight={32}
        masterDetail={props.masterDetail}
        headerHeight={40}
        getRowClass={props.getRowClass}
        getRowStyle={props.getRowStyle}
        getRowHeight={props.getRowHeight}
        animateRows
        detailCellRendererParams={props.detailCellRendererParams}
        isFullWidthRow={props.isFullWidthRow}
        fullWidthCellRenderer={props.fullWidthCellRenderer}
        embedFullWidthRows={props.embedFullWidthRows}
        onFilterChanged={props.onFilterChanged}
        maintainColumnOrder
        enableCellChangeFlash={false}
        enableBrowserTooltips
        getRowId={getRowId}
        rowData={rowData}
        rowSelection={'multiple'}
        onGridReady={onGridReady}
        onFirstDataRendered={props.onFirstDataRendered}
        columnDefs={props.columnDefs}
        defaultColDef={defaultColDef}
        onColumnMoved={props.onColumnMoved}
        noRowsOverlayComponent={props.noRowsOverlayComponent}
        isExternalFilterPresent={props.isExternalFilterPresent}
        serverSideInfiniteScroll={props.serverSideInfiniteScroll}
        doesExternalFilterPass={props.doesExternalFilterPass}
        paginationPageSize={50}
        onModelUpdated={props.onModelUpdated}
        rowModelType={props.rowModelType}
        cacheOverflowSize={2}
        infiniteInitialRowCount={INFINITE_SCROLL_PAGE_SIZE}
        cacheBlockSize={INFINITE_SCROLL_PAGE_SIZE}
        serverSideDatasource={props.serverSideDatasource}
        suppressColumnVirtualisation={isCypressRunning}
        suppressRowVirtualisation={isCypressRunning}
        alwaysMultiSort={props.alwaysMultiSort !== undefined ? props.alwaysMultiSort : true}
        suppressRowClickSelection={props.suppressRowClickSelection}
        enableCellTextSelection={cellTextSelectionIsEnabled}
        suppressCopyRowsToClipboard
        suppressDragLeaveHidesColumns
        autoGroupColumnDef={props.autoGroupColumnDef}
        getDataPath={props.getDataPath}
        treeData={props.treeData}
        quickFilterText={props.quickFilterText}
        detailCellRenderer={props.masterDetail ? props.detailCellRenderer : undefined}
        onRowDragEnd={props.onRowDragEnd}
        rowDragManaged={props.rowDragManaged}
        rowDragEntireRow={props.rowDragEntireRow}
        onRowDataUpdated={props.onRowDataUpdated}
        components={props.components}
      />
    </div>
  );
}

const StyledProgress = styled(LinearProgress)<{ $isPending: boolean }>`
  position: absolute;
  top: 30px;
  z-index: 1;
  display: ${({ $isPending }) => ($isPending ? 'block' : 'none')};
  width: 100%;
`;
