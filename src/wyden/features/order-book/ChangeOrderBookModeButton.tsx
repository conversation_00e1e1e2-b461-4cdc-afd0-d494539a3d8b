import AutoGraphIcon from '@mui/icons-material/AutoGraph';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import SwapVertIcon from '@mui/icons-material/SwapVert';
import { IconButton, Tooltip } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';

type ChangeOrderBookModeButtonProps = {
  obModeSetting: 'HORIZONTAL' | 'VERTICAL' | 'AUTO';
  setObModeSetting: (value: 'HORIZONTAL' | 'VERTICAL' | 'AUTO') => void;
};
type SettingsType = {
  setTo: 'HORIZONTAL' | 'VERTICAL' | 'AUTO';
  tooltipMessage: string;
  icon: JSX.Element;
};

export function ChangeOrderBookModeButton({
  obModeSetting,
  setObModeSetting,
}: ChangeOrderBookModeButtonProps) {
  const { t } = useTranslation();
  let x: SettingsType;
  switch (obModeSetting) {
    case 'HORIZONTAL':
      x = {
        setTo: 'VERTICAL',
        tooltipMessage: t('orderbook.changeToVertical'),
        icon: <SwapHorizIcon />,
      };
      break;
    case 'VERTICAL':
      x = {
        setTo: 'AUTO',
        tooltipMessage: t('orderbook.changeToAuto'),
        icon: <SwapVertIcon />,
      };
      break;
    case 'AUTO':
      x = {
        setTo: 'HORIZONTAL',
        tooltipMessage: t('orderbook.changeToHorizontal'),
        icon: <AutoGraphIcon />,
      };
      break;
  }

  return (
    <StyledTooltip title={x.tooltipMessage} arrow>
      <IconButton onClick={() => setObModeSetting(x.setTo)}>{x.icon}</IconButton>
    </StyledTooltip>
  );
}

const StyledTooltip = styled(Tooltip)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;
