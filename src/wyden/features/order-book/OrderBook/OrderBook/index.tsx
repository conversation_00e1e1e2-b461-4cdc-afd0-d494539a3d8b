import { CircularProgress } from '@mui/material';
import { Paragraph } from '@ui/Typography/Paragraph';
import { useEventLogs } from '@wyden/features/error-indicators/event-logs/useEventLogs';
import { NetworkEvents } from '@wyden/features/error-indicators/network-indicators/events';
import { useNetworkStore } from '@wyden/features/error-indicators/network-indicators/useNetworkStore';
import { useSimpleOrderForm } from '@wyden/features/order-form/useSimpleOrderForm';
import { useWorkspaces } from '@wyden/features/useWorkspaces';
import { useVenueAccounts } from '@wyden/hooks/useVenueAccounts';
import { useAppDispatch, useAppSelector } from '@wyden/redux-hooks';
import { OrderBook, useOrderBookSubscription } from '@wyden/services/graphql/generated/graphql';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StyledOverlay } from '../../../../../ui/StyledOverlay';
import { countFractionDigits } from '../../../../helpers/countFractionDigits';
import {
  getInstrumentId,
  getInstrumentReadableIdentifier,
  getInstrumentVenueName,
} from '../../../../helpers/instrument';
import { useCapabilities } from '../../../../hooks/useCapabilities';
import { InstrumentData } from '../../../app-header/useInstrumentStore';
import { isClientSideInstrument } from '../../../instrument-search/useInstrumentSearch';
import { DepthVisualizer } from '../DepthVisualizer/DepthVisualizer';
import { Spread } from '../Spread/Spread';
import { PriceLevelRow, PriceLevelRowContainer } from './PriceLevelRow/PriceLevelRow';
import { TitleRow } from './TitleRow/TitleRow';
import {
  calculateAndDrawOrderBook,
  clearOrdersState,
  selectAsks,
  selectBids,
  selectDateTime,
  selectIsInitialized,
  setRowsCount,
} from './orderbookSlice';
import { Container, StyledParagraph, TableContainer, TitleContainer } from './styles';

export enum OrderBookSide {
  BID,
  ASK,
}

export interface CustomOBSubscribeParameters {
  venueAccount?: string;
  portfolioId?: string;
  instrumentId: string;
  portfolioName?: string;
  accountName?: string;
}

interface OrderBookProps {
  instrument: InstrumentData | null;
  obMode: 'VERTICAL' | 'HORIZONTAL';
  rowCounts: number;
  customSubscribeParameters?: CustomOBSubscribeParameters;
  triggerObReload: () => void;
}

export const transformBidsOrAkskFromServer = (bidsOrAsksFromServer: Array<OrderBook | null>) =>
  bidsOrAsksFromServer.map((bidOrAsk) => [Number(bidOrAsk?.price), Number(bidOrAsk?.amount)]) || [];

export const DynamicOrderBook = ({
  instrument,
  obMode,
  rowCounts,
  customSubscribeParameters,
  triggerObReload,
}: OrderBookProps) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { addEventLog } = useEventLogs();
  const { currentWorkspace } = useWorkspaces();
  const { getVenueForAccountId } = useVenueAccounts();
  const { venueCapabilities } = useCapabilities();
  const instrumentId = getInstrumentId(instrument);
  const isClientInstrument = isClientSideInstrument(instrument);
  const { upsertWsConnection } = useNetworkStore();
  const [hoveredPriceLevelRow, setHoveredPriceLevelRow] = useState({ isAsk: false, idx: -1 });
  const bids: number[][] = useAppSelector(selectBids);
  const asks: number[][] = useAppSelector(selectAsks);
  const dateTime: string = useAppSelector(selectDateTime);
  const isInitialized: boolean = useAppSelector(selectIsInitialized);
  const dispatchedProductId = useRef<string | null>(null);
  const verticalOB = obMode === 'VERTICAL';
  const {
    focusBuyButton,
    focusSellButton,
    values: { account: orderEntryAccount, portfolio: orderEntryPortfolio },
  } = useSimpleOrderForm({ instrument });
  const customParamsPriceSource =
    customSubscribeParameters?.venueAccount || customSubscribeParameters?.portfolioId;
  const priceSource =
    customParamsPriceSource ||
    (isClientInstrument ? orderEntryPortfolio?.id : orderEntryAccount?.venueAccountName);

  const readableCustomPriceSource = customSubscribeParameters
    ? `${customSubscribeParameters?.venueAccount} / ${customSubscribeParameters?.portfolioName}`
    : null;
  const readablePriceSource = isClientInstrument
    ? orderEntryPortfolio?.name
    : orderEntryAccount?.venueAccountName;

  // am TODO: It looks like dispatchedProductId unnecessary/unused
  useEffect(() => {
    const instrumentIdWithPriceSource = instrumentId + priceSource;
    if (dispatchedProductId.current !== instrumentIdWithPriceSource) {
      dispatch(clearOrdersState());
      dispatchedProductId.current = instrumentIdWithPriceSource ?? null;
    }
  }, [instrumentId, priceSource, dispatch]);

  const variables = customSubscribeParameters || {
    venueAccount: isClientInstrument ? undefined : orderEntryAccount?.venueAccountId,
    instrumentId,
    portfolioId: isClientInstrument ? orderEntryPortfolio?.id : undefined,
  };

  const accountFromDifferentVenueThanInstrument =
    !isClientInstrument &&
    getInstrumentVenueName(instrument) !== getVenueForAccountId(variables.venueAccount || '');

  const { error, loading } = useOrderBookSubscription({
    variables,
    skip:
      accountFromDifferentVenueThanInstrument ||
      !variables.instrumentId ||
      (!priceSource && !customParamsPriceSource),
    onError: (error) => {
      if (error?.message.includes('Jwt expired')) {
        triggerObReload();
      }
      // It is to destroy ob and reestablish subscribe hook again after error
      upsertWsConnection('orderBook', { errorType: 'CLIENT_ERROR', error });
      addEventLog({
        type: NetworkEvents.ORDERBOOK_SUBSCRIPTION,
        message: t('eventLogs.WSFailed', { name: t('marketData.orderBook') }),
        timestamp: Date.now(),
      });
      return null;
    },
    onData: ({ data }) => {
      const bidsFromServer = data?.data?.orderBook?.bids || [];
      const asksFromServer = data?.data?.orderBook?.asks || [];
      const transformedBids = transformBidsOrAkskFromServer(bidsFromServer);
      const transformedAsks = transformBidsOrAkskFromServer(asksFromServer);
      const dateTime = data?.data?.orderBook?.dateTime;
      if (!isInitialized && (transformedBids?.length || transformedAsks?.length)) {
        const rowsCountToSet =
          transformedBids?.length > transformedAsks?.length
            ? transformedBids?.length
            : asks?.length;
        dispatch(setRowsCount(rowsCountToSet || 20));
      }
      dispatch(clearOrdersState());
      dispatch(
        calculateAndDrawOrderBook({ bids: transformedBids, asks: transformedAsks, dateTime }),
      );
    },
    shouldResubscribe: true,
  });

  const ErrorOverlay = (
    <StyledOverlay $visible={Boolean(error)}>
      <span>
        {`${t('orderbook.noData', {
          productId: `${getInstrumentReadableIdentifier(instrument)} / ${
            readableCustomPriceSource || readablePriceSource
          }`,
        })}`}
      </span>
    </StyledOverlay>
  );

  const buildPriceLevels = (
    levels: number[][] = [[1, 1]],
    side: OrderBookSide,
  ): React.ReactNode => {
    if (error) {
      return null;
    }
    const sortedLevelsByPrice: number[][] = [...levels].sort(
      (currentLevel: number[], nextLevel: number[]): number => {
        let result = 0;
        if (side === OrderBookSide.BID || verticalOB) {
          result = nextLevel[0] - currentLevel[0];
        } else {
          result = currentLevel[0] - nextLevel[0];
        }
        return result;
      },
    );

    let sortedAndAlignedLevelsByPrice = sortedLevelsByPrice;

    if (verticalOB && side === OrderBookSide.ASK && sortedLevelsByPrice.length > rowCounts) {
      sortedAndAlignedLevelsByPrice = sortedLevelsByPrice.slice(
        sortedLevelsByPrice.length - rowCounts,
      );
    }

    const venue = getInstrumentVenueName(instrument);
    const capabilitiesForVenue = (venue && venueCapabilities.get(venue.toLowerCase())) || {};
    const isOTC = capabilitiesForVenue.marketDataCapabilities?.otcBroker;

    return sortedAndAlignedLevelsByPrice.map((level, idx) => {
      const price = level[0];
      const depth = level[3];
      const calculatedTotal: number = level[2];
      const size: number = level[1];
      const total: number = !isOTC ? calculatedTotal : level[1];
      const reversedIdx = rowCounts - idx;

      if (idx < rowCounts) {
        return (
          <PriceLevelRowContainer key={idx + depth}>
            <DepthVisualizer key={depth} depth={depth} side={side} obMode={obMode} />
            <PriceLevelRow
              idx={idx}
              reversedIdx={reversedIdx}
              instrument={instrument}
              size={size}
              total={total}
              key={size + total}
              price={price}
              side={side}
              focusBuyButton={focusBuyButton}
              focusSellButton={focusSellButton}
              currentWorkspace={currentWorkspace}
              obMode={obMode}
              hoveredPriceLevelRow={hoveredPriceLevelRow}
              setHoveredPriceLevelRow={setHoveredPriceLevelRow}
            />
          </PriceLevelRowContainer>
        );
      }
    });
  };

  return (
    <>
      <TitleContainer>
        <StyledParagraph>{!!dateTime && `${t('common.updatedAt')}: ${dateTime}`}</StyledParagraph>
      </TitleContainer>
      <Container $isLoading={loading} $isVerticalOneColumnOBMode={verticalOB}>
        {!priceSource && (
          <StyledOverlay $visible>
            <Paragraph>
              {t('orderbook.noPortfolioOrAccount', {
                priceSource: `${isClientInstrument ? 'portfolio' : 'account'}`,
                instrumentSide: `${isClientInstrument ? 'client' : 'street'}`,
              })}
            </Paragraph>
          </StyledOverlay>
        )}
        {priceSource &&
          (loading ? (
            <StyledOverlay $visible>
              <CircularProgress color="inherit" />
            </StyledOverlay>
          ) : !error ? (
            <>
              <TableContainer $isVerticalOneColumnOBMode={verticalOB}>
                {!verticalOB && (
                  <TitleRow
                    isVerticalMobileMode={verticalOB}
                    isAskOrderType={false}
                    instrument={instrument}
                  />
                )}
                <div>{buildPriceLevels(bids, OrderBookSide.BID)}</div>
              </TableContainer>
              <Spread
                bids={bids}
                asks={asks}
                isVerticalOneColumnOBMode={verticalOB}
                priceIncr={countFractionDigits(
                  instrument?.tradingConstraints.priceIncr || undefined,
                )}
              />
              <TableContainer
                $isVerticalOneColumnOBMode={verticalOB}
                onMouseLeave={() => {
                  setHoveredPriceLevelRow({ idx: -1, isAsk: false });
                }}
              >
                <TitleRow
                  isVerticalMobileMode={verticalOB}
                  isAskOrderType={true}
                  instrument={instrument}
                />
                <div>{buildPriceLevels(asks, OrderBookSide.ASK)}</div>
              </TableContainer>
            </>
          ) : (
            ErrorOverlay
          ))}
      </Container>
    </>
  );
};
