import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';

import { OrderBookSide as PriceLevelSide } from '../..';
import {
  aBaseInstrumentResponse,
  anInstrumentResponse,
} from '../../../../../../services/graphql/generated/mocks';
import { PriceLevelRow } from '../PriceLevelRow';

const voidFunctionMock = () => {
  return undefined;
};

describe('PriceLevelRow', () => {
  it('renders price level row properly - not reversed', () => {
    render(
      <PriceLevelRow
        idx={1}
        instrument={null}
        size={500}
        price={1000}
        total={50000}
        reversedIdx={7}
        obMode={'HORIZONTAL'}
        hoveredPriceLevelRow={{ idx: 1, isAsk: true }}
        setHoveredPriceLevelRow={() => {
          return null;
        }}
        side={PriceLevelSide.BID}
        currentWorkspace={undefined}
        focusBuyButton={voidFunctionMock}
        focusSellButton={voidFunctionMock}
      />,
    );
    const priceElement = screen.getByTestId('price-level-row');
    const pElements = priceElement.querySelectorAll('p');
    expect(pElements[2]).toHaveClass('price');
  });

  it('renders price level row properly - reversed', () => {
    render(
      <PriceLevelRow
        idx={1}
        instrument={null}
        size={500}
        price={1000}
        total={50000}
        reversedIdx={7}
        obMode={'HORIZONTAL'}
        hoveredPriceLevelRow={{ idx: 1, isAsk: true }}
        setHoveredPriceLevelRow={() => {
          return null;
        }}
        side={PriceLevelSide.ASK}
        currentWorkspace={undefined}
        focusBuyButton={voidFunctionMock}
        focusSellButton={voidFunctionMock}
      />,
    );
    const priceElement = screen.getByTestId('price-level-row');
    const pElements = priceElement.querySelectorAll('p');
    expect(pElements[0]).toHaveClass('price');
  });

  it('formats values with set precision', () => {
    const quoteCurrency = 'USD';
    render(priceLevelRow(quoteCurrency, 2));
    const priceElement = screen.getByTestId('price-level-row');
    const pElements = priceElement.querySelectorAll('p');
    expect(pElements[0].innerHTML).toEqual('123.46');
    expect(pElements[1].innerHTML).toEqual('555.46');
    expect(pElements[2].innerHTML).toEqual('444.46');
  });

  it('formats currency with set precision in non-ISO case', () => {
    const quoteCurrency = 'USDT';
    render(priceLevelRow(quoteCurrency, 2));
    const priceElement = screen.getByTestId('price-level-row');
    const pElements = priceElement.querySelectorAll('p');
    expect(pElements[0].innerHTML).toEqual('123.46');
  });

  it('padding with zeroes', () => {
    const quoteCurrency = 'USDT';
    render(priceLevelRow(quoteCurrency, 8));
    const priceElement = screen.getByTestId('price-level-row');
    const pElements = priceElement.querySelectorAll('p');
    expect(pElements[0].innerHTML).toEqual('123.45600000');
    expect(pElements[1].innerHTML).toEqual('555.46');
    expect(pElements[2].innerHTML).toEqual('444.46');
  });
});

function priceLevelRow(quoteCurrency: string, priceScale: number) {
  return (
    <PriceLevelRow
      idx={1}
      instrument={anInstrumentResponse({
        tradingConstraints: { priceIncr: (1 / Math.pow(10, priceScale)).toFixed(10) },
        baseInstrument: aBaseInstrumentResponse({ quoteCurrency }),
      })}
      size={555.456}
      price={123.456}
      total={444.456}
      reversedIdx={7}
      obMode={'HORIZONTAL'}
      hoveredPriceLevelRow={{ idx: 1, isAsk: true }}
      setHoveredPriceLevelRow={() => {
        return null;
      }}
      side={PriceLevelSide.ASK}
      currentWorkspace={undefined}
      focusBuyButton={voidFunctionMock}
      focusSellButton={voidFunctionMock}
    />
  );
}
