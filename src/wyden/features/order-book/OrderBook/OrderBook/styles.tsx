import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { Paragraph } from '../../../../../ui/Typography/Paragraph';
import { getSpacing } from '../../../../utils/styles';

export const Container = styled('div')<{
  $isLoading: boolean;
  $isVerticalOneColumnOBMode: boolean;
}>`
  display: flex;
  flex-direction: ${({ $isVerticalOneColumnOBMode }) =>
    $isVerticalOneColumnOBMode ? 'column-reverse' : 'row'};
  justify-content: center;
  align-items: flex-start;
  border-color: #263946;
  margin-top: ${({ $isVerticalOneColumnOBMode }) => ($isVerticalOneColumnOBMode ? '0' : '16px')};
  border: solid
    ${({ theme, $isLoading }) =>
      $isLoading ? color[theme.palette.mode].borderElementsBorderWeak : 'transparent'}
    1px;
`;

export const TableContainer = styled('div')<{ $isVerticalOneColumnOBMode: boolean }>`
  display: flex;
  width: ${({ $isVerticalOneColumnOBMode }) => ($isVerticalOneColumnOBMode ? '100%' : '50%')};
  flex-direction: column;
  background-color: ${({ theme }) => color[theme.palette.mode].fillsSurfaceSurfacePrimary};
`;

export const TitleContainer = styled('div')`
  text-align: right;
  margin-bottom: 10px;
`;

export const StyledParagraph = styled(Paragraph)`
  font-size: 12px;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
  font-weight: 500;
  min-height: 18px;
  line-height: 18px;
  margin-left: ${getSpacing(1)};
  font-variant-numeric: tabular-nums;
`;
