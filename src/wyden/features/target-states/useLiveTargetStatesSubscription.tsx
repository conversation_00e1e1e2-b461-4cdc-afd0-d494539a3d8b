import { useTranslation } from 'react-i18next';
import { useTargetStatesSubscription } from '../../services/graphql/generated/graphql';
import { useEventLogs } from '../error-indicators/event-logs/useEventLogs';
import { NetworkEvents } from '../error-indicators/network-indicators/events';
import { useNetworkStore } from '../error-indicators/network-indicators/useNetworkStore';

export const useLiveTargetStatesSubscription = () => {
  const { upsertWsConnection } = useNetworkStore();
  const { addEventLog } = useEventLogs();
  const { t } = useTranslation();

  useTargetStatesSubscription({
    // to see onData check customMessageReceivers.ts
    variables: {},
    onError: (error) => {
      upsertWsConnection('targetStates', { errorType: 'CLIENT_ERROR', error });
      addEventLog({
        type: NetworkEvents.POSITION_CHANGES_SUBSCRIPTION,
        message: t('eventLogs.requestFailed', {
          name: t('targetStates.targetStatesSubscriptionError', {
            error: error.message,
          }),
        }),
        timestamp: new Date().getTime(),
      });
    },
  });
};
