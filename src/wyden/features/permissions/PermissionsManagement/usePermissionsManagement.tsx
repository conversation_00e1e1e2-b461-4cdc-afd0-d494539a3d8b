import { ERRORS } from '@wyden/constants';
import { useEventLogs } from '@wyden/features/error-indicators/event-logs/useEventLogs';
import { NetworkEvents } from '@wyden/features/error-indicators/network-indicators/events';
import { useNetworkStore } from '@wyden/features/error-indicators/network-indicators/useNetworkStore';
import {
  GroupsPermissionsForResourceResponse,
  Permission,
  Resource,
  Scope,
  UsersPermissionsForResourceResponse,
  useAddGroupPermissionsMutation,
  useAddUserPermissionsMutation,
  usePermissionsForResourceQuery,
  useRemoveGroupPermissionsMutation,
  useRemoveUserPermissionsMutation,
} from '@wyden/services/graphql/generated/graphql';
import { useTranslation } from 'react-i18next';
import { usePermissionsStore } from '../permissionsStore';
import { PermissionsFormShape } from './PermissionsForm';
import { selectAuthenticatedUser, useAuthStore } from '@wyden/services/keycloak/auth/useAuthStore';
import { useGridApiContext } from '@wyden/hooks/useGridApi';
import { useState } from 'react';

export function usePermissionsManagement() {
  const { editingPermission } = usePermissionsStore();
  const { user: currentUser } = useAuthStore(selectAuthenticatedUser);
  const api = useGridApiContext();
  const { addEventLog } = useEventLogs();
  const { upsertRequest } = useNetworkStore();
  const { t } = useTranslation();
  const [refetching, setRefetching] = useState(false);
  const isStaticPermission = Boolean(!editingPermission?.resourceId);
  const scopesKey = isStaticPermission ? 'scopes' : 'dynamicScopes';

  const [addUserPermissions, { loading: isLoadingAddUserPermission }] =
    useAddUserPermissionsMutation({
      onError: (err) => {
        upsertRequest('addUserPermissions', {
          pending: false,
          error: ERRORS.NETWORK_ERROR,
          err,
        });
        addEventLog({
          type: NetworkEvents.ADD_USER_PERMISSIONS,
          message: t('eventLogs.requestFailed', { name: t('permissions.addUserPermissions') }),
          timestamp: new Date().getTime(),
        });
      },
    });
  const [addGroupPermissions, { loading: isLoadingAddGroupPermission }] =
    useAddGroupPermissionsMutation({
      onError: (err) => {
        upsertRequest('addGroupPermissions', {
          pending: false,
          error: ERRORS.NETWORK_ERROR,
          err,
        });
        addEventLog({
          type: NetworkEvents.ADD_GROUP_PERMISSIONS,
          message: t('eventLogs.requestFailed', { name: t('permissions.addGroupPermissions') }),
          timestamp: new Date().getTime(),
        });
      },
    });
  const [removeUserPermission, { loading: isLoadingRemoveUserPermission }] =
    useRemoveUserPermissionsMutation({
      onError: (err) => {
        upsertRequest('removeUserPermissions', {
          pending: false,
          error: ERRORS.NETWORK_ERROR,
          err,
        });
        addEventLog({
          type: NetworkEvents.REMOVE_USER_PERMISSIONS,
          message: t('eventLogs.requestFailed', { name: t('permissions.removeUserPermissions') }),
          timestamp: new Date().getTime(),
        });
      },
    });
  const [removeGroupPermission, { loading: isLoadingRemoveGroupPermission }] =
    useRemoveGroupPermissionsMutation({
      onError: (err) => {
        upsertRequest('removeGroupPermissions', {
          pending: false,
          error: ERRORS.NETWORK_ERROR,
          err,
        });
        addEventLog({
          type: NetworkEvents.REMOVE_GROUP_PERMISSIONS,
          message: t('eventLogs.requestFailed', { name: t('permissions.removeGroupPermissions') }),
          timestamp: new Date().getTime(),
        });
      },
    });

  const {
    data,
    refetch,
    loading: isPermissionsForResourceLoading,
  } = usePermissionsForResourceQuery({
    variables: {
      resourceId: editingPermission?.resourceId || '',
      resource: editingPermission?.resource as Resource,
    },
  });

  const groupsPermissions = data?.groupsPermissionsForResource || [];
  const usersPermissions = data?.usersPermissionsForResource || [];

  const getSavePermissions = (data: PermissionsFormShape): Permission[] => {
    if (!editingPermission) return [];

    return ([Scope.Read, Scope.Trade, Scope.Manage, Scope.Create] as const)
      .filter((scope) => Boolean(data[scope]))
      .map((scope) => ({
        resource: editingPermission?.resource,
        resourceId: editingPermission?.resourceId,
        scope,
      }));
  };

  const handleUsersSubmit = (data: PermissionsFormShape) => {
    return addUserPermissions({
      variables: {
        request: {
          username: data.select || '',
          permissions: getSavePermissions(data),
        },
      },
      onCompleted: async () => {
        setRefetching(true);
        await refetch();
        setRefetching(false);
        const savingScopes = getSavePermissions(data).map((permission) => permission.scope);
        const togglingForSelf = data.select === currentUser?.preferred_username;

        if (!togglingForSelf || !editingPermission) return;

        const nodeData = api?.current?.api?.getRowNode(
          editingPermission?.resourceId || editingPermission?.resource,
        )?.data;
        const nodeScopesWithoutNew = nodeData?.[scopesKey]?.filter(
          (scope: Scope) => !savingScopes.includes(scope),
        );

        api?.current?.api
          ?.getRowNode(editingPermission?.resourceId || editingPermission?.resource)
          ?.updateData({
            ...nodeData,
            [scopesKey]: [...nodeScopesWithoutNew, ...savingScopes],
          });
      },
    });
  };

  const handleGroupsSubmit = (data: PermissionsFormShape) => {
    return addGroupPermissions({
      variables: {
        request: {
          groupName: data.select || '',
          permissions: getSavePermissions(data),
        },
      },
      onCompleted: async () => {
        setRefetching(true);
        await refetch();
        setRefetching(false);

        const savingScopes = getSavePermissions(data).map((permission) => permission.scope);
        const togglingForSelf = currentUser?.groups?.includes(data.select || '');

        if (!togglingForSelf || !editingPermission) return;

        const nodeData = api?.current?.api?.getRowNode(
          editingPermission?.resourceId || editingPermission?.resource,
        )?.data;
        const nodeScopesWithoutNew = nodeData?.[scopesKey]?.filter(
          (scope: Scope) => !savingScopes.includes(scope),
        );

        api?.current?.api
          ?.getRowNode(editingPermission?.resourceId || editingPermission?.resource)
          ?.updateData({
            ...nodeData,
            [scopesKey]: [...nodeScopesWithoutNew, ...savingScopes],
          });
      },
    });
  };

  const toggleUserPermission = (
    user: UsersPermissionsForResourceResponse,
    scope: Scope,
    groupPermissions: GroupsPermissionsForResourceResponse[],
  ) => {
    const togglingForSelf = user.username === currentUser?.preferred_username;
    const hasThatScopeInResource = user?.scopes?.includes(scope);

    if (!editingPermission) return;

    const payload = {
      variables: {
        request: {
          username: user.username || '',
          permissions: [
            {
              resource: editingPermission?.resource,
              resourceId: editingPermission?.resourceId,
              scope,
            },
          ],
        },
      },
    };

    if (hasThatScopeInResource) {
      return removeUserPermission({
        ...payload,
        onCompleted: async () => {
          setRefetching(true);
          await refetch();
          setRefetching(false);

          if (!togglingForSelf) return;

          const selfGroupsPermissions = groupPermissions.filter(
            (group) => currentUser?.groups?.includes(group.groupName || ''),
          );
          const hasPermissionInGroup = selfGroupsPermissions.some((group) =>
            group.scopes.includes(scope),
          );

          if (hasPermissionInGroup) return;

          const nodeData = api?.current?.api?.getRowNode(
            editingPermission?.resourceId || editingPermission?.resource,
          )?.data;
          const newScopes = nodeData?.[scopesKey]?.filter(
            (insideScope: Scope) => insideScope !== scope,
          );
          api?.current?.api
            ?.getRowNode(editingPermission?.resourceId || editingPermission?.resource)
            ?.updateData({ ...nodeData, [scopesKey]: newScopes });
        },
      });
    } else {
      return addUserPermissions({
        ...payload,
        onCompleted: async () => {
          setRefetching(true);
          await refetch();
          setRefetching(false);

          if (!togglingForSelf) return;

          const nodeData = api?.current?.api?.getRowNode(
            editingPermission?.resourceId || editingPermission?.resource,
          )?.data;
          const resourceAlreadyHaveThatPermission = nodeData?.[scopesKey]?.includes(scope);

          if (resourceAlreadyHaveThatPermission) return;

          const newScopes = [...(nodeData?.[scopesKey] || []), scope];
          api?.current?.api
            ?.getRowNode(editingPermission?.resourceId || editingPermission?.resource)
            ?.updateData({ ...nodeData, [scopesKey]: newScopes });
        },
      });
    }
  };

  const toggleGroupPermission = (
    group: GroupsPermissionsForResourceResponse,
    scope: Scope,
    userPermissions: UsersPermissionsForResourceResponse[],
  ) => {
    const hasGroupThatScope = group?.scopes?.includes(scope);
    const togglingForSelf = currentUser?.groups?.includes(group.groupName || '');

    if (!editingPermission) return;

    const payload = {
      variables: {
        request: {
          groupName: group.groupName || '',
          permissions: [
            {
              resource: editingPermission?.resource,
              resourceId: editingPermission?.resourceId,
              scope,
            },
          ],
        },
      },
    };

    if (hasGroupThatScope) {
      return removeGroupPermission({
        ...payload,
        onCompleted: async () => {
          setRefetching(true);
          await refetch();
          setRefetching(false);

          if (!togglingForSelf) return;

          const selfUserPermissions = userPermissions.filter(
            (user) => user.username === currentUser?.preferred_username,
          );
          const hasPermissionInUser = selfUserPermissions.some((user) =>
            user.scopes.includes(scope),
          );

          if (hasPermissionInUser) return;

          const nodeData = api?.current?.api?.getRowNode(
            editingPermission?.resourceId || editingPermission?.resource,
          )?.data;
          const newScopes = nodeData?.[scopesKey]?.filter(
            (insideScope: Scope) => insideScope !== scope,
          );

          api?.current?.api
            ?.getRowNode(editingPermission?.resourceId || editingPermission?.resource)
            ?.updateData({ ...nodeData, [scopesKey]: newScopes });
        },
      });
    } else {
      return addGroupPermissions({
        ...payload,
        onCompleted: async () => {
          setRefetching(true);
          await refetch();
          setRefetching(false);

          if (!togglingForSelf) return;

          const nodeData = api?.current?.api?.getRowNode(
            editingPermission?.resourceId || editingPermission?.resource,
          )?.data;
          const alreadyPermissionForTheResource = nodeData?.[scopesKey]?.includes(scope);

          if (alreadyPermissionForTheResource) return;

          const newScopes = [...(nodeData?.[scopesKey] || []), scope];
          api?.current?.api
            ?.getRowNode(editingPermission?.resourceId || editingPermission?.resource)
            ?.updateData({ ...nodeData, [scopesKey]: newScopes });
        },
      });
    }
  };

  return {
    isLoadingAddUserPermission,
    isLoadingRemoveUserPermission,
    isLoadingAddGroupPermission,
    isLoadingRemoveGroupPermission,
    isPermissionsForResourceLoading,
    handleGroupsSubmit,
    handleUsersSubmit,
    toggleUserPermission,
    toggleGroupPermission,
    groupsPermissions,
    usersPermissions,
    refetching,
  };
}
