import { EventLogs } from '../EventLogs';
import { ReactNode } from 'react';
import { screen, within } from '@testing-library/react';
import { fireEvent } from '@testing-library/dom';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from '@wyden/test-config/utils/renderWithProviders';

export class EventLogsPO {
  render(testEventTrigger?: ReactNode) {
    return renderWithProviders(
      <>
        {testEventTrigger}
        <EventLogs />
      </>,
    );
  }

  expectTextToBeDisplay(text: string) {
    return screen.findByText(text);
  }

  async triggerMouseOver(text: string) {
    const element = await screen.getByText(text);
    fireEvent.mouseOver(element);
  }

  async selectEventLogType(eventLog: string) {
    const select = await screen.findByLabelText('Filter events');
    await userEvent.click(select);

    const optionsPopup = await screen.findByRole('listbox', {
      name: 'Filter events',
    });

    await userEvent.click(within(optionsPopup).getByText(eventLog));
  }
}
