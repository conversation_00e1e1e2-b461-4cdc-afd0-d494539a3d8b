import { OrderStatus } from '@wyden/services/graphql/generated/graphql';
import {
  anInstrumentIdentifiersResponse,
  anInstrumentResponse,
  anOrderStateResponse,
} from '../../../services/graphql/generated/mocks';

const toValues = (
  data: {
    symbol: string;
    updatedAt?: string;
  }[],
): string[][] => data.map((item) => Object.values(item));

export const ORDER_HISTORY_FIXTURES = {
  initial: {
    stub: {
      loading: false,
      ordersHistory: [],
      instruments: [],
    },
  },
  filledOrders: {
    stub: {},
    expected: {
      visibleRows: toValues([
        {
          symbol: 'BTCUSDT',
        },
      ]),
      invisibleRows: toValues([
        {
          symbol: 'ETHUSDT',
        },
        {
          symbol: 'DOGEUSDT',
        },
      ]),
    },
  },
  byInstrument: {
    stub: {},
    selectedInstrument: 'DOGEUSD',
    expected: {
      visibleRows: toValues([
        {
          symbol: 'DOGEUSDT',
        },
      ]),
      invisibleRows: toValues([
        {
          symbol: 'BTCUSD',
        },
        {
          symbol: 'ETHUSD}',
        },
      ]),
    },
  },
  clearedInstrument: {
    stub: {},
    selectedInstrument: 'DOGEUSD',
    expected: {
      visibleRows: toValues([
        {
          symbol: 'DOGEUSDT',
        },
        {
          symbol: 'BTCUSDT',
        },
        {
          symbol: 'ETHUSDT',
        },
      ]),
      invisibleRows: toValues([]),
    },
  },
  duplicateInstruments: {
    stub: {},
    selectedInstrument: 'DOGEUSD',
    expected: {
      visibleRows: ['DOGEUSD', 'ETHUSD'],
    },
  },
  byDateRange: {
    stub: {},
    selectedDateRange: {
      startDate: '03/14/2021',
      endDate: '03/16/2021',
    },
    expected: {
      visibleRows: toValues([
        {
          symbol: 'BTCUSDT',
        },
      ]),
      invisibleRows: toValues([
        {
          symbol: 'ETHUSDT',
        },
        {
          symbol: 'DODGEUSDT',
        },
      ]),
    },
  },
  formattedDate: {
    stub: {
      ordersHistory: [
        {
          orderId: '1',
          clientId: '1',
          portfolioId: 'portfolio_trader_1',
          instrumentId: 'BTCUSD',
          orderStatus: OrderStatus.Filled,
          updatedAt: new Date('03/15/2021').getTime().toString(),
        },
      ],
    },
    expected: {
      visibleRows: toValues([
        {
          symbol: 'BTCUSDT',
          updatedAt: '15/03/2021 00:00:00',
        },
      ]),
    },
  },
  clearDateRange: {
    stub: {
      loading: false,
      portfolios: [],
      instruments: ['DOGEUSD', 'BTCUSD', 'ETHUSD'],
    },
    selectedDateRange: {
      startDate: '03/14/2021',
      endDate: '03/16/2021',
    },
    expected: {
      visibleRows: toValues([
        {
          symbol: 'BTCUSDT',
        },
        {
          symbol: 'ETHUSDT',
        },
        {
          symbol: 'DOGEUSDT',
        },
      ]),
    },
  },
  byClientOrderId: {
    stub: {
      loading: false,
      portfolios: [],
      instruments: ['DOGEUSD', 'BTCUSD', 'ETHUSD'],
    },
    selectedClientId: '2',
    expected: {
      visibleRows: toValues([
        {
          symbol: 'ETHUSDT',
        },
      ]),
      invisibleRows: toValues([
        {
          symbol: 'BTCUSDT',
        },
        {
          symbol: 'DOGEUSDT',
        },
      ]),
    },
  },
  byPortfolio: {
    stub: {
      loading: false,
      portfolios: ['portfolio_trader_1', 'portfolio_trader_2'],
      instruments: ['DOGEUSD', 'BTCUSD', 'ETHUSD'],
    },
    expected: {
      visibleRows: toValues([
        {
          symbol: 'ETHUSD',
        },
      ]),
      invisibleRows: toValues([
        {
          symbol: 'BTCUSD',
        },
        {
          symbol: 'DOGEUSD',
        },
      ]),
    },
  },
};

const PAGINATED_ORDER_HISTORY_EDGE = {
  node: {
    ...anOrderStateResponse({
      instrument: anInstrumentResponse({
        instrumentIdentifiers: anInstrumentIdentifiersResponse({
          instrumentId: 'BTCUSDT@FOREX@BitMEX',
        }),
      }),
    }),
    symbol: 'BTCUSDT',
    venue: 'BitMEX',
    orderStatus: OrderStatus.Filled,
    updatedAt: new Date('03/15/2021').getTime().toString(),
  },
};

const PAGE_INFO = {
  hasNextPage: false,
  endCursor: '2023-10-19T13:59:29.823761Z',
};

export const PAGINATED_ORDER_HISTORY = {
  orderStatesWithPredicates: {
    edges: [PAGINATED_ORDER_HISTORY_EDGE],
    pageInfo: PAGE_INFO,
  },
};

export const PAGINATED_ORDER_HISTORY_WITH_3_ORDER_TYPES = {
  orderStatesWithPredicates: {
    edges: [
      {
        node: {
          ...PAGINATED_ORDER_HISTORY_EDGE.node,
          orderId: 3,
          parentOrderId: 'parentOrderId',
          orderCategory: 'CHILD',
          instrument: anInstrumentResponse({
            instrumentIdentifiers: anInstrumentIdentifiersResponse({
              instrumentId: 'BTCUSDT@FOREX@BitMEX',
            }),
          }),
          symbol: 'BTCUSDT',
          venue: 'BitMEX',
          updatedAt: new Date('03/17/2021').getTime().toString(),
        },
      },
      {
        node: {
          ...PAGINATED_ORDER_HISTORY_EDGE.node,
          orderId: 4,
          parentOrderId: 'parentOrderId',
          orderCategory: 'SIMPLE',
          instrument: anInstrumentResponse({
            instrumentIdentifiers: anInstrumentIdentifiersResponse({
              instrumentId: 'BTCUSDT@FOREX@BitMEX',
            }),
          }),
          symbol: 'BTCUSDT',
          venue: 'BitMEX',
          updatedAt: new Date('03/17/2021').getTime().toString(),
        },
      },
      {
        node: {
          ...PAGINATED_ORDER_HISTORY_EDGE.node,
          orderId: 5,
          orderCategory: 'PARENT',
          instrument: anInstrumentResponse({
            instrumentIdentifiers: anInstrumentIdentifiersResponse({
              instrumentId: 'BTCUSDT@FOREX@BitMEX',
            }),
          }),
          symbol: 'BTCUSDT',
          venue: 'BitMEX',
          updatedAt: new Date('03/17/2021').getTime().toString(),
        },
      },
    ],
    pageInfo: PAGE_INFO,
  },
};

export const PAGINATED_ORDER_HISTORY_WITH_CORRECT_FILTERS = {
  orderStatesWithPredicates: {
    edges: [
      {
        node: {
          ...PAGINATED_ORDER_HISTORY_EDGE.node,
          orderId: 9,
          parentOrderId: 'parentOrderId',
          orderCategory: 'CHILD',
          instrument: anInstrumentResponse({
            instrumentIdentifiers: anInstrumentIdentifiersResponse({
              instrumentId: 'BTCUSDT@FOREX@BitMEX',
            }),
          }),
          symbol: 'Correct filters',
          venue: 'BitMEX',
          updatedAt: new Date('03/17/2021').getTime().toString(),
        },
      },
    ],
    pageInfo: PAGE_INFO,
  },
};
