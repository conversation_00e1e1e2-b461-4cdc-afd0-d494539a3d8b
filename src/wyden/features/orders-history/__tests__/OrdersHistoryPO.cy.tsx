import React from 'react';
import '../../../../../cypress/support/component';
import { FILTERS_POPOVER_TEST_ID } from '../../chip-filters/FiltersChipList';
import { FormPO } from '../../form/__test__/FormPO.cy';
import { ColumnSettingsPO } from '../../grid/__test__/ColumnSettingsPO.cy';
import { ORDER_HISTORY_ACTIONS_RENDERER_TEST_ID } from '../OrderActionsRenderer';
import { ORDER_HISTORY_FILTERS_TEST_ID } from '../OrderHistoryFilters';
import {
  ORDERS_HISTORY_COLUMNS_PICKER_TEST_ID,
  ORDERS_HISTORY_GRID_TEST_ID,
} from '../OrderHistoryGrid';
import { OrdersHistory } from '../OrdersHistory';

export class OrdersHistoryPO {
  private readonly formPO = new FormPO();
  private selectedWidget = '#test-widget';
  columnSettings = new ColumnSettingsPO();

  render(multipleWidgets = true) {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(<OrdersHistory />, { multiply: multipleWidgets });
  }

  expectTextToBeVisible(text: string, options?: { timeout?: number }) {
    return cy.contains(text, options);
  }

  expectAGgridTextToBeVisible(text: string, options?: { ignore?: string }) {
    return cy
      .get(`${this.selectedWidget} [data-testid="${ORDERS_HISTORY_GRID_TEST_ID}"`)
      .within(() => {
        cy.findByText(text, options).should('exist');
      });
  }

  expectTextToBeNotVisible(text: string) {
    return cy.contains(text).should('not.exist');
  }

  expectAGGridTextToBeVisible(text: string) {
    cy.get(`${this.selectedWidget} [data-testid="${ORDERS_HISTORY_GRID_TEST_ID}"`).within(() => {
      this.expectTextToBeVisible(text, { timeout: 10000 });
    });
  }

  expectAGgridDataToBeVisible(data: string[][]) {
    return cy
      .get(`${this.selectedWidget} [data-testid="${ORDERS_HISTORY_GRID_TEST_ID}"`)
      .within(() => {
        data.forEach((row) => {
          row.forEach((cell) => {
            this.expectTextToBeVisible(cell, { timeout: 10000 });
          });
        });
      });
  }

  expectAGgridDataToBeNotVisible(data: string[][]) {
    return cy
      .get(`${this.selectedWidget} [data-testid="${ORDERS_HISTORY_GRID_TEST_ID}"`)
      .within(() => {
        data.forEach((row) => {
          row.forEach((cell) => {
            this.expectTextToBeNotVisible(cell);
          });
        });
      });
  }

  expectContextMenuToContain(orderId: string, numberOfItems: number) {
    cy.get(`${this.selectedWidget}`)
      .get('div.base-Popper-root > div')
      .children()
      .should('have.length', numberOfItems);
  }

  openFilersPopover() {
    return cy
      .get(`${this.selectedWidget} [data-testid="${ORDER_HISTORY_FILTERS_TEST_ID}"]`)
      .contains('Add')
      .click();
  }

  clickOutsideFiltersPopover() {
    return cy.get('body').click();
  }

  selectAccountsFilter(value: string) {
    this.openFilersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Accounts').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectWalletsFilter(value: string) {
    this.openFilersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Wallets').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectDateFilter(filterName: string, value: string) {
    this.openFilersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(filterName).click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectInstrument(value: string) {
    this.openFilersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Instrument').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectOrderStatus(value: string) {
    this.openFilersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Order Status').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectOrderCategory(value: string) {
    this.openFilersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Category').click();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains(value).click();
    this.clickOutsideFiltersPopover();
  }

  selectSecondTestWidget() {
    cy.get(`${this.selectedWidget}`).contains('Second Test Widget').click();
    this.selectedWidget = '#test-widget-1';
  }

  selectActiveTestWidget() {
    cy.get(`${this.selectedWidget}`).contains('Active Test Widget').click();
    this.selectedWidget = '#test-widget';
  }

  insertOrderID(value: string) {
    this.openFilersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Order ID').click();
    this.formPO.insertByLabelText('Order ID', value);
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Apply').click();
  }

  insertParentOrderID(value: string) {
    this.openFilersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Parent Order ID').click();
    this.formPO.insertByLabelText('Parent Order ID', value);
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Apply').click();
  }

  insertExternalOrderID(value: string) {
    this.openFilersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('External Order ID').click();
    this.formPO.insertByLabelText('External Order ID', value);
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Apply').click();
  }

  insertClientOrderID(value: string) {
    this.openFilersPopover();
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Client Order ID').click();
    this.formPO.insertByLabelText('Client Order ID', value);
    cy.get(`[data-testid="${FILTERS_POPOVER_TEST_ID}"]`).contains('Apply').click();
  }

  // msw how to validate request parameters in gql
  toggleSettings() {
    cy.get(`${this.selectedWidget}`).findByTestId('open-settings').click({ force: true });
  }

  selectColumn(name: string) {
    cy.get(`[data-testid="${ORDERS_HISTORY_COLUMNS_PICKER_TEST_ID}"`).within(() => {
      cy.findByText(name).click();
    });
  }

  toggleActionsRenderer(orderId: string) {
    cy.get(`${this.selectedWidget}`)
      .findByTestId(ORDER_HISTORY_ACTIONS_RENDERER_TEST_ID(orderId))
      .click();
  }

  selectParentOrderAction() {
    cy.get('body').contains('Find parent order').click();
    this.clickOutsideFiltersPopover();
  }

  expectFilterToBeSelected(value: string) {
    return cy
      .get(`${this.selectedWidget} [data-testid="${ORDER_HISTORY_FILTERS_TEST_ID}"]`)
      .contains(`${value}`)
      .should('be.visible');
  }

  expectFilterToBeNotSelected(value: string) {
    return cy
      .get(`${this.selectedWidget} [data-testid="${ORDER_HISTORY_FILTERS_TEST_ID}"]`)
      .contains(`${value}`)
      .should('not.exist');
  }

  expectResetButtonToNotBeVisible() {
    return cy
      .get(`${this.selectedWidget} [data-testid="${ORDER_HISTORY_FILTERS_TEST_ID}"]`)
      .contains('Reset')
      .should('not.exist');
  }

  resetFilters() {
    cy.get(`${this.selectedWidget} [data-testid="${ORDER_HISTORY_FILTERS_TEST_ID}"]`)
      .contains('Reset')
      .click();
  }
}
