import { graphql } from 'msw';
import '../../../../../cypress/support/component';
import { worker } from '../../../../../mocks/browser';
import {
  DatePredicateType,
  OrderStateCollectionField,
  OrderStateDateField,
  OrderStateSearchInput,
  OrderStateStringField,
  Scope,
  VenueType,
} from '../../../services/graphql/generated/graphql';
import {
  aBaseInstrumentResponse,
  aPortfolioResponse,
  aTradingConstraintsResponse,
  anInstrumentResponse,
} from '../../../services/graphql/generated/mocks';
import {
  avgPriceKey,
  filledQtyKey,
  lastQtyKey,
  orderQtyKey,
  orderStatusKey,
  reasonKey,
  remainingQtyKey,
  sideKey,
  symbolKey,
  venueKey,
} from '../orderHistoryColumnDefinitions';
import { useOrderHistoryFilters } from '../useOrderHistoryFilters';
import { OrdersHistoryPO } from './OrdersHistoryPO.cy';
import {
  ORDER_HISTORY_FIXTURES,
  PAGINATED_ORDER_HISTORY,
  PAGINATED_ORDER_HISTORY_WITH_3_ORDER_TYPES,
  PAGINATED_ORDER_HISTORY_WITH_CORRECT_FILTERS,
} from './order-history.fixtures';

const formatKeyForLabel = (fieldName: string) => {
  const words = fieldName.split(/(?=[A-Z])/);
  return words.map((word) => word[0].toUpperCase() + word.slice(1).toLowerCase()).join(' ');
};

const dogeInstrument = {
  baseInstrument: {
    assetClass: 'FOREX',
    description: 'DOGEUSD@WydenMock',
    symbol: 'DOGEUSD',
    quoteCurrency: 'USD',
    feeCurrency: 'USD',
    settlementCurrency: 'ADA',
    inverseContract: true,
    venueType: 'STREET',
    venueName: 'WydenMock',
    __typename: 'BaseInstrumentResponse',
  },
  forexSpotProperties: {
    baseCurrency: 'ADA',
    __typename: 'ForexSpotPropertiesResponse',
  },
  tradingConstraints: {
    ...aTradingConstraintsResponse(),
    priceIncr: '1',
    qtyIncr: '1',
    minQty: null,
    maxQty: null,
    tradeable: true,
    __typename: 'TradingConstraintsResponse',
  },
  instrumentIdentifiers: {
    adapterTicker: 'ADAUSD',
    instrumentId: 'ADAUSD@FOREX@WydenMock',
    __typename: 'InstrumentIdentifiersResponse',
  },
  archivedAt: null,
  __typename: 'InstrumentResponse',
};

describe('OrdersHistory', () => {
  const ordersHistoryPO = new OrdersHistoryPO();

  beforeEach(() => {
    useOrderHistoryFilters.getState().reset();
    ordersHistoryPO.render();
  });

  it('should display order history default columns', () => {
    ordersHistoryPO.expectAGgridTextToBeVisible('Order ID');
    ordersHistoryPO.expectAGgridTextToBeVisible(formatKeyForLabel(orderStatusKey));
    ordersHistoryPO.expectAGgridTextToBeVisible(formatKeyForLabel(symbolKey));
    ordersHistoryPO.expectAGgridTextToBeVisible(formatKeyForLabel(venueKey));
    ordersHistoryPO.expectAGgridTextToBeVisible(formatKeyForLabel(sideKey));
    ordersHistoryPO.expectAGgridTextToBeVisible(formatKeyForLabel(orderQtyKey));
    ordersHistoryPO.expectAGgridTextToBeVisible(formatKeyForLabel(avgPriceKey));
    ordersHistoryPO.expectAGgridTextToBeVisible(formatKeyForLabel(filledQtyKey));
    ordersHistoryPO.expectAGgridTextToBeVisible('Updated At [UTC+0]');
  });

  it('should adds and removes properly headers to order history', () => {
    ordersHistoryPO.render(false);
    ordersHistoryPO.columnSettings.openSettings();

    // show columns
    ordersHistoryPO.columnSettings.toggleColumn(formatKeyForLabel(remainingQtyKey));
    ordersHistoryPO.columnSettings.toggleColumn(formatKeyForLabel(lastQtyKey));
    ordersHistoryPO.columnSettings.toggleColumn(formatKeyForLabel(orderQtyKey));
    ordersHistoryPO.columnSettings.toggleColumn(formatKeyForLabel(reasonKey));

    // hide default columns
    ordersHistoryPO.columnSettings.toggleColumn(formatKeyForLabel(orderStatusKey));
    ordersHistoryPO.expectAGgridTextToBeVisible(formatKeyForLabel(symbolKey));
    ordersHistoryPO.expectAGgridTextToBeVisible(formatKeyForLabel(venueKey));
    ordersHistoryPO.columnSettings.toggleColumn(formatKeyForLabel(sideKey));
    ordersHistoryPO.columnSettings.toggleColumn(formatKeyForLabel(orderQtyKey));
    ordersHistoryPO.columnSettings.toggleColumn(formatKeyForLabel(avgPriceKey));
    ordersHistoryPO.columnSettings.toggleColumn(formatKeyForLabel(filledQtyKey));

    ordersHistoryPO.columnSettings.saveConfig();

    ordersHistoryPO.expectAGGridTextToBeVisible(formatKeyForLabel(remainingQtyKey));
    ordersHistoryPO.expectAGGridTextToBeVisible(formatKeyForLabel(lastQtyKey));
    ordersHistoryPO.expectAGGridTextToBeVisible(formatKeyForLabel(orderQtyKey));
    ordersHistoryPO.expectAGGridTextToBeVisible(formatKeyForLabel(reasonKey));
  });

  it('should display no orders', () => {
    ordersHistoryPO.render();
    ordersHistoryPO.expectTextToBeVisible('No orders to display');
  });

  it('should display only filled orders', () => {
    worker.use(
      graphql.query('OrderStatesWithPredicates', (req, res, ctx) =>
        res(ctx.data(PAGINATED_ORDER_HISTORY)),
      ),
    );

    ordersHistoryPO.expectAGgridDataToBeVisible(
      ORDER_HISTORY_FIXTURES.filledOrders.expected.visibleRows,
    );
    ordersHistoryPO.expectAGgridDataToBeNotVisible(
      ORDER_HISTORY_FIXTURES.filledOrders.expected.invisibleRows,
    );
  });

  it('should display selected filters', () => {
    const isValuePresentInSimplePredicates = (
      searchVariables: OrderStateSearchInput,
      field: string,
      value: string,
    ) => {
      return searchVariables.simplePredicates?.some(
        (predicate) => predicate?.field === field && predicate?.value === value,
      );
    };

    const isValuePresentInCollectionPredicates = (
      searchVariables: OrderStateSearchInput,
      field: string,
      value: string,
    ) => {
      return searchVariables.collectionPredicates?.some(
        (predicate) => predicate?.field === field && predicate?.value.includes(value),
      );
    };

    const isFieldPresentInDatePredicates = (
      searchVariables: OrderStateSearchInput,
      field: string,
      method: string,
    ) => {
      return searchVariables.datePredicateInputs?.some(
        (predicate) => predicate?.field === field && predicate?.method === method,
      );
    };

    worker.use(
      graphql.query('InstrumentSearch', (req, res, ctx) => {
        return res(
          ctx.data({
            instrumentSearch: {
              edges: [
                {
                  node: dogeInstrument,
                  cursor: '1',
                },
              ],
              pageInfo: {
                hasNextPage: false,
                endCursor: '1',
              },
            },
          }),
        );
      }),
      graphql.query('OrderStatesWithPredicates', (req, res, ctx) => {
        const isAccountPresentInSearchParams = isValuePresentInSimplePredicates(
          req.variables.search,
          OrderStateStringField.VenueAccountId,
          'BitMEX-testnet1',
        );
        const isWalletPresentInSearchParams = isValuePresentInSimplePredicates(
          req.variables.search,
          OrderStateStringField.VenueAccountId,
          'Bank',
        );
        const isInstrumentPresentInSearchParams = isValuePresentInCollectionPredicates(
          req.variables.search,
          OrderStateStringField.InstrumentId,
          'ADAUSD@FOREX@WydenMock',
        );

        const isOrderCategoryPresentInSearchParams = isValuePresentInCollectionPredicates(
          req.variables.search,
          OrderStateCollectionField.OrderCategory,
          'AGENCY_ORDER',
        );

        const isCreatedFromPresentInSearchParams = isFieldPresentInDatePredicates(
          req.variables.search,
          OrderStateDateField.CreatedAt,
          DatePredicateType.From,
        );

        const isCreatedToPresentInSearchParams = isFieldPresentInDatePredicates(
          req.variables.search,
          OrderStateDateField.CreatedAt,
          DatePredicateType.To,
        );

        const isUpdatedFromPresentInSearchParams = isFieldPresentInDatePredicates(
          req.variables.search,
          OrderStateDateField.UpdatedAt,
          DatePredicateType.From,
        );

        const isUpdatedToPresentInSearchParams = isFieldPresentInDatePredicates(
          req.variables.search,
          OrderStateDateField.UpdatedAt,
          DatePredicateType.To,
        );

        const isOrderIdPresentInSearchParams = isValuePresentInSimplePredicates(
          req.variables.search,
          OrderStateStringField.OrderId,
          'Order ID Test',
        );

        const isParentOrderIdPresentInSearchParams = isValuePresentInSimplePredicates(
          req.variables.search,
          OrderStateStringField.ParentOrderId,
          'Parent Order ID Test',
        );

        const isExternalOrderIdPresentInSearchParams = isValuePresentInSimplePredicates(
          req.variables.search,
          OrderStateStringField.ExtOrderId,
          'External Order ID Test',
        );

        const isClientOrderIdPresentInSearchParams = isValuePresentInSimplePredicates(
          req.variables.search,
          OrderStateStringField.ClOrderId,
          'Client Order ID Test',
        );

        const filtersAreCorrectlyAppliedToRequest =
          isAccountPresentInSearchParams &&
          isWalletPresentInSearchParams &&
          isInstrumentPresentInSearchParams &&
          isOrderCategoryPresentInSearchParams &&
          isCreatedFromPresentInSearchParams &&
          isCreatedToPresentInSearchParams &&
          isUpdatedFromPresentInSearchParams &&
          isUpdatedToPresentInSearchParams &&
          isOrderIdPresentInSearchParams &&
          isParentOrderIdPresentInSearchParams &&
          isExternalOrderIdPresentInSearchParams &&
          isClientOrderIdPresentInSearchParams;
        if (filtersAreCorrectlyAppliedToRequest) {
          return res(ctx.data(PAGINATED_ORDER_HISTORY_WITH_CORRECT_FILTERS));
        } else {
          return res(ctx.data(PAGINATED_ORDER_HISTORY_WITH_3_ORDER_TYPES));
        }
      }),
    );
    ordersHistoryPO.selectAccountsFilter('BitMEX-testnet1');
    ordersHistoryPO.expectFilterToBeSelected('BitMEX-testnet1');

    ordersHistoryPO.selectWalletsFilter('Bank');
    ordersHistoryPO.expectFilterToBeSelected('Bank');

    ordersHistoryPO.selectInstrument('DOGEUSD');
    ordersHistoryPO.expectFilterToBeSelected('DOGEUSD@WydenMock');

    ordersHistoryPO.selectOrderStatus('Filled');
    ordersHistoryPO.expectFilterToBeSelected('Filled');

    ordersHistoryPO.selectOrderCategory('Agency order');
    ordersHistoryPO.expectFilterToBeSelected('Agency order');

    ordersHistoryPO.selectDateFilter('Created from', '3h ago');
    ordersHistoryPO.selectDateFilter('Created to', '3h ago');

    ordersHistoryPO.selectDateFilter('Updated from', '3h ago');
    ordersHistoryPO.selectDateFilter('Updated to', '3h ago');

    ordersHistoryPO.insertOrderID('Order ID Test');
    ordersHistoryPO.expectFilterToBeSelected('Order ID Test');
    ordersHistoryPO.insertParentOrderID('Parent Order ID Test');
    ordersHistoryPO.expectFilterToBeSelected('Parent Order ID Test');

    ordersHistoryPO.insertExternalOrderID('External Order ID Test');
    ordersHistoryPO.expectFilterToBeSelected('External Order ID Test');
    ordersHistoryPO.insertClientOrderID('Client Order ID Test');
    ordersHistoryPO.expectFilterToBeSelected('Client Order ID Test');

    ordersHistoryPO.expectTextToBeVisible('Correct filters');

    ordersHistoryPO.resetFilters();
    ordersHistoryPO.expectFilterToBeNotSelected('BitMEX-testnet1');
    ordersHistoryPO.expectFilterToBeNotSelected('Bank');
    ordersHistoryPO.expectFilterToBeNotSelected('portfolio_trader_1');
    ordersHistoryPO.expectFilterToBeNotSelected('Agency order');
    ordersHistoryPO.expectFilterToBeNotSelected('DOGEUSD@WydenMock');
    ordersHistoryPO.expectFilterToBeNotSelected('Filled');
    ordersHistoryPO.expectFilterToBeNotSelected('Order ID Test');
    ordersHistoryPO.expectFilterToBeNotSelected('Parent Order ID Test');
    ordersHistoryPO.expectFilterToBeNotSelected('External Order ID Test');
    ordersHistoryPO.expectFilterToBeNotSelected('Client Order ID Test');

    ordersHistoryPO.expectTextToBeNotVisible('Correct filters');
    ordersHistoryPO.expectResetButtonToNotBeVisible();
  });

  it('should render actions for Child order', () => {
    worker.use(
      graphql.query('OrderStatesWithPredicates', (req, res, ctx) =>
        res(ctx.data(PAGINATED_ORDER_HISTORY_WITH_3_ORDER_TYPES)),
      ),
    );
    ordersHistoryPO.toggleActionsRenderer('3');

    ordersHistoryPO.expectTextToBeVisible('Find all transactions');
    ordersHistoryPO.expectTextToBeVisible('Find all positions');
    ordersHistoryPO.expectTextToBeVisible('Find parent order');
    ordersHistoryPO.expectTextToBeVisible('Find all siblings');
    ordersHistoryPO.expectTextToBeVisible('Find related orders');
    ordersHistoryPO.expectContextMenuToContain('4', 7);
  });

  it('should render actions for Normal order', () => {
    worker.use(
      graphql.query('OrderStatesWithPredicates', (req, res, ctx) =>
        res(ctx.data(PAGINATED_ORDER_HISTORY_WITH_3_ORDER_TYPES)),
      ),
    );
    ordersHistoryPO.toggleActionsRenderer('4');

    ordersHistoryPO.expectTextToBeVisible('Find all transactions');
    ordersHistoryPO.expectTextToBeVisible('Find all positions');
    ordersHistoryPO.expectTextToBeVisible('Find related orders');
    ordersHistoryPO.expectContextMenuToContain('5', 7);
  });

  it('should render actions for Parent order', () => {
    worker.use(
      graphql.query('OrderStatesWithPredicates', (req, res, ctx) =>
        res(ctx.data(PAGINATED_ORDER_HISTORY_WITH_3_ORDER_TYPES)),
      ),
    );
    ordersHistoryPO.toggleActionsRenderer('5');

    ordersHistoryPO.expectTextToBeVisible('Find all transactions');
    ordersHistoryPO.expectTextToBeVisible('Find all positions');
    ordersHistoryPO.expectTextToBeVisible('Find child orders');
    ordersHistoryPO.expectTextToBeVisible('Find related orders');
    ordersHistoryPO.expectContextMenuToContain('6', 7);
  });

  it('should add orderId filter with parentOrderId value after clicking on row item action', () => {
    worker.use(
      graphql.query('OrderStatesWithPredicates', (req, res, ctx) =>
        res(ctx.data(PAGINATED_ORDER_HISTORY_WITH_3_ORDER_TYPES)),
      ),
    );
    ordersHistoryPO.toggleActionsRenderer('3');
    ordersHistoryPO.selectParentOrderAction();

    ordersHistoryPO.expectFilterToBeSelected('parentOrderId');
  });

  it('should display different filters on both widget instances', () => {
    worker.use(
      graphql.query('InstrumentSearch', (req, res, ctx) => {
        return res(
          ctx.data({
            instrumentSearch: {
              edges: [
                {
                  node: dogeInstrument,
                  cursor: '1',
                },
              ],
              pageInfo: {
                hasNextPage: false,
                endCursor: '1',
              },
            },
          }),
        );
      }),
      graphql.query('OrderStatesWithPredicates', (req, res, ctx) =>
        res(ctx.data(PAGINATED_ORDER_HISTORY_WITH_3_ORDER_TYPES)),
      ),
    );
    ordersHistoryPO.selectInstrument('DOGEUSD');

    ordersHistoryPO.selectSecondTestWidget();

    ordersHistoryPO.expectFilterToBeNotSelected('DOGEUSD@WydenMock');
    ordersHistoryPO.insertOrderID('TEST');
    ordersHistoryPO.expectFilterToBeSelected('TEST');

    ordersHistoryPO.selectActiveTestWidget();
    ordersHistoryPO.expectFilterToBeNotSelected('TEST');
    ordersHistoryPO.expectFilterToBeSelected('DOGEUSD@WydenMock');
  });

  it.skip('should display persisted filters', () => {
    worker.use(
      graphql.query('InstrumentSearch', (req, res, ctx) => {
        return res(
          ctx.data({
            instrumentSearch: {
              edges: [
                {
                  node: dogeInstrument,
                  cursor: '1',
                },
              ],
              pageInfo: {
                hasNextPage: false,
                endCursor: '1',
              },
            },
          }),
        );
      }),
      graphql.query('OrderStatesWithPredicates', (req, res, ctx) =>
        res(ctx.data(PAGINATED_ORDER_HISTORY_WITH_3_ORDER_TYPES)),
      ),
      graphql.query('UserData', (req, res, ctx) => {
        const userData = {
          timestamp: Date.now(),
          widgets: [
            {
              id: 'test-widget',
              type: 'BASE',
              filters: {
                instruments: [
                  anInstrumentResponse({
                    baseInstrument: aBaseInstrumentResponse({
                      symbol: 'DOGEUSD',
                      venueName: 'WydenMock',
                      venueType: VenueType.Client,
                    }),
                  }),
                ],
                portfolios: [],
                accounts: [],
                wallets: [],
                orderId: 'some-order-id',
                parentOrderId: 'some-parent-order-id',
                orderStatus: ['FILLED'],
                orderCategory: ['PARENT'],
                portfolio: '',
                venueAccount: '',
                createdFrom: null,
                createdTo: null,
                updatedTo: null,
                updatedFrom: null,
              },
            },
          ],
          workspaces: [
            {
              name: 'current-workspace',
              id: 'current-workspace',
              json: {},
              isTrading: false,
              portfolio: aPortfolioResponse({
                id: 'portfolio_trader_1',
                name: 'portfolio_trader_1',
                scopes: [Scope.Manage, Scope.Trade, Scope.Read],
              }),
            },
          ],
        };
        return res(
          ctx.data({
            userData: { data: JSON.stringify(userData) },
          }),
        );
      }),
    );
    ordersHistoryPO.render(false);
    ordersHistoryPO.expectFilterToBeSelected('some-order-id');
    ordersHistoryPO.expectFilterToBeSelected('some-parent-order-id');
    ordersHistoryPO.expectFilterToBeSelected('FILLED');
    ordersHistoryPO.expectFilterToBeSelected('PARENT');
    ordersHistoryPO.expectFilterToBeSelected('DOGEUSD@WydenMock');
  });
});
