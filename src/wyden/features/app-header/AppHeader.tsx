import { Link } from '@mui/material';
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

import { useNestReact } from '@nest-react/hooks/useNestReact';
import { HorizontalSpacer } from '@ui/HorizontalSpacer';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { ReactComponent as SideNavigationClosed } from '@wyden/assets/side-navigation-closed.svg';
import { ReactComponent as SideNavigationOpened } from '@wyden/assets/side-navigation-opened.svg';
import { ReactComponent as Logo } from '@wyden/assets/wyden-rgb.svg';
import { useWorkspaces } from '@wyden/features/useWorkspaces';
import { useFullscreenMode } from '@wyden/hooks/useFullScreenMode';
import { getColorBasedOnTheme } from '@wyden/utils/styles';
import { useSideMenuStore } from '../app-side-menu/useSideMenuStore';
import { FocusSelect } from '../focus/FocusSelect';
import { Kpis } from '../kpis/Kpis';
import { IssuesButton } from './IssuesButton';
import { NetworkProgress } from './NetworkProgress';
import { RecordingModeButton } from '@wyden/features/sesion-recording/RecordingModeButton';
import { TutorialModeButton } from './TutorialModeButton';
import { UserDropdown } from './UserDropdown';
import { IconButton } from '@ui/IconButton';
import { VideoTutorialButtons } from '../video-tutorial/VideoTutorialButtons';

const HEADER_TEST_ID = 'header';
const TOP_SECTION_TEST_ID = 'top-section';

export const AppHeader = () => {
  const location = useLocation();
  const { app } = useNestReact();
  const { exitFullScreenMode } = useFullscreenMode();
  const { toggleSideMenu, isSideMenuOpen } = useSideMenuStore();
  const queryParams = new URLSearchParams(window.location.search);
  const { isSettingsPage, isRiskPage, isSettlementPage, isPlayerPage } = useWorkspaces();
  const renderWithoutFocusSelect = isSettingsPage || isRiskPage || isSettlementPage || isPlayerPage;
  const isWydenDevOrLocalhost =
    window.location.hostname === 'wyden-dev.wyden.io' || window.location.hostname === 'localhost';
  const recordingModeEnabled =
    queryParams.get('experimentalRecordingMode') === 'true' || isWydenDevOrLocalhost;

  useEffect(() => {
    exitFullScreenMode();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  return (
    <>
      <Header data-testid={HEADER_TEST_ID}>
        <StyledLogoAndMenuSection>
          <MenuIconButton
            onClick={() => {
              toggleSideMenu();
              app.layoutManager.doLayout();
              exitFullScreenMode();
            }}
          >
            {isSideMenuOpen ? <SideNavigationOpened /> : <SideNavigationClosed />}
          </MenuIconButton>

          <StyledLink href="/">
            <HeaderLogo />
          </StyledLink>
        </StyledLogoAndMenuSection>

        <StyledButtonsContainer>
          <IssuesButton />
          <TutorialModeButton />
          <VideoTutorialButtons isWydenDevOrLocalhost={isWydenDevOrLocalhost} />
          {recordingModeEnabled === true && <RecordingModeButton />}
          <UserDropdown />
          <HorizontalSpacer space={3} />
        </StyledButtonsContainer>
        <NetworkProgress />
      </Header>
      {renderWithoutFocusSelect ? null : (
        <TopSection $isSideMenuOpen={isSideMenuOpen} data-testid={TOP_SECTION_TEST_ID}>
          <FocusSelect />
          <Kpis />
        </TopSection>
      )}
    </>
  );
};

const StyledButtonsContainer = styled('div')`
  display: flex;
`;

const StyledLogoAndMenuSection = styled('div')`
  display: flex;
`;

const TopSection = styled('div')<{ $isSideMenuOpen: boolean }>`
  height: 38px;
  display: flex;
  align-items: center;
  padding: 10px 20px 0 ${({ $isSideMenuOpen }) => ($isSideMenuOpen ? '96px' : '14px')};
`;

const MenuIconButton = styled('div')`
  cursor: pointer;
  margin: 6px 20px 0 20px;
`;

const Header = styled('header')`
  width: 100%;
  display: flex;
  position: relative;
  height: 60px;
  align-items: center;
  justify-content: space-between;
  margin: 0;
  padding: 0;
  background: ${getColorBasedOnTheme(
    color['light'].fillsSurfaceSurfaceSecondary,
    color['dark'].fillsSurfaceSurfaceSecondary,
  )};
`;

const HeaderLogo = styled(Logo)`
  margin: 0 6px;
  height: 52px;
`;

const StyledLink = styled(Link)`
  max-height: 56px;
  color: ${getColorBasedOnTheme(
    color['light'].fillsElementsFillPrimary,
    color['dark'].fillsElementsFillPrimary,
  )};
`;

export const HeaderMenuButton = styled(IconButton)`
  margin-right: 8px;
  margin-top: 6px;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
  &:hover {
    color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
    background: transparent;
  }
`;
