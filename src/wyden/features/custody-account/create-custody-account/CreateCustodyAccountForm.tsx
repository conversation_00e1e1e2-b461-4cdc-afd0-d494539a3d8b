import { createTsForm, createUniqueFieldSchema } from '@ts-react/form';
import { TextField } from '@wyden/features/form/TextField';
import { z } from 'zod';
import { DialogForm, DialogFormProps } from '../../form/DialogForm';
import { RadioButtonInput } from '../../form/RadioButtonInput';
import { useCustodyAccountStore } from '../useCustodyAccountStore';
import { VostroNostro } from '../../../services/graphql/generated/graphql';
import { CheckboxInput } from '@wyden/features/form/Checkbox';

const VostroNostroFieldDef = createUniqueFieldSchema(
  z.enum([VostroNostro.Nostro, VostroNostro.Vostro]),
  'VostroNostro',
);

const CustodyAccountFormSchemaZObject = {
  venueAccountName: z.string({
    required_error: 'custody.custodyAccounts.custodyAccountForm.nameIsRequired',
  }),
  vostroNostro: VostroNostroFieldDef,
  integrated: z.boolean().optional().nullable(),
};

export const CustodyAccountFormSchema = z.object(CustodyAccountFormSchemaZObject);

export const custodyAccountMapping = [
  [z.string(), TextField],
  [VostroNostroFieldDef, RadioButtonInput],
  [z.boolean(), CheckboxInput],
] as const;

const CreateDialogForm = (props: DialogFormProps) => {
  const { toggleCreateCustodyAccountDialog } = useCustodyAccountStore();
  return (
    <DialogForm
      {...props}
      toggleDialog={toggleCreateCustodyAccountDialog}
      titleTranslationId="custody.custodyAccounts.custodyAccountForm.createCustodyAccount"
      submitButtonTranslationId="custody.custodyAccounts.custodyAccountForm.createCustodyAccountSubmit"
    />
  );
};

export const CreateCustodyAccountForm = createTsForm(custodyAccountMapping, {
  FormComponent: CreateDialogForm,
});
