import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import debounce from 'lodash/debounce';
import { INFINITE_SCROLL_PAGE_SIZE } from '@wyden/constants';
import {
  useVenueAccountListLazyQuery,
  VenueAccountType,
} from '@wyden/services/graphql/generated/graphql';
import { GridApi, GridReadyEvent, IDatasource } from 'ag-grid-community';

export const useCustodyAccountDataGrid = () => {
  const searchValRef = useRef<string | null>('');
  const archivedRef = useRef<boolean | null>(false);
  const [showArchived, setShowArchived] = useState<boolean>(false);
  const [searchVal, setSearchVal] = useState<string>('');
  const [searchInputVal, setSearchInputVal] = useState<string>('');
  const [lazyQuery, { error }] = useVenueAccountListLazyQuery();
  const [initialLoading, setInitialLoading] = useState(true);
  const [reloading, setReloading] = useState(false);
  const endCursor = useRef<string | null>();
  const sortingOrder = useRef<string | null>();
  const api = useRef<GridApi>();
  const [showNoRows, setShowNoRows] = useState(false);
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => setSearchVal(e.target.value);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedHandleSearch = useCallback(
    debounce((e: React.ChangeEvent<HTMLInputElement>) => {
      handleSearch(e);
    }, 300),
    [],
  );

  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInputVal(e.target.value);
    debouncedHandleSearch(e);
  };

  const toggleArchived = () => {
    setShowArchived((prev) => !prev);
  };

  useEffect(() => {
    setReloading(true);
    searchValRef.current = searchVal;
    archivedRef.current = showArchived ?? false;

    // Preventing ugly rows flickering, we want to make sure overlay is rendered before operations
    setTimeout(() => {
      endCursor.current = undefined;
      api.current?.setRowCount(0);
      api.current?.purgeInfiniteCache();
    }, 0);
  }, [searchVal, showArchived]);

  const datasource: IDatasource = useMemo(() => {
    return {
      getRows(params) {
        lazyQuery({
          variables: {
            input: {
              first: INFINITE_SCROLL_PAGE_SIZE,
              after: endCursor.current,
              venueAccountName: searchValRef.current !== '' ? searchValRef.current : undefined,
              venueAccountType: VenueAccountType.Custody,
              archived: archivedRef.current ?? false,
            },
          },
          fetchPolicy: 'network-only',
        }).then((res) => {
          if (!res.error) {
            const dataToLoad = res?.data?.venueAccountList?.edges?.map((edge) => edge.node) ?? [];
            const lastRow = res?.data?.venueAccountList?.pageInfo?.hasNextPage
              ? -1
              : params.startRow + dataToLoad.length;

            endCursor.current = res?.data?.venueAccountList?.pageInfo?.endCursor;

            if (params.startRow === 0 && dataToLoad.length === 0) {
              setShowNoRows(true);
            } else {
              setShowNoRows(false);
            }

            params.successCallback(dataToLoad, lastRow);

            setTimeout(() => {
              setInitialLoading(false);
              setReloading(false);
            }, 0);
          }
        });
      },
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const refresh = () => {
    setReloading(true);
    // Preventing ugly rows flickering, we want to make sure overlay is rendered before operations
    setTimeout(() => {
      sortingOrder.current = undefined;
      endCursor.current = undefined;
      api.current?.setRowCount(0);
      api.current?.purgeInfiniteCache();
    }, 0);
  };

  const onGridReady = useCallback(
    (params: GridReadyEvent) => {
      if (params.api) {
        params.api.setDatasource(datasource);
        api.current = params.api;
      }
    },
    [datasource],
  );

  return {
    onGridReady,
    refresh,
    initialLoading,
    error,
    showNoRows,
    reloading,
    searchVal: searchInputVal,
    handleSearch: handleSearchInput,
    toggleArchived,
    showArchived,
  };
};
