import { useState } from 'react';
import { styled } from '@ui/styled';
import { ReactComponent as ArrowBackIcon } from '@wyden/assets/arrow-back.svg';
import { GoBackButton } from '@wyden/components/GoBackButton';
import { ErrorBoundary } from '@wyden/features/error-indicators/error-boundary/ErrorBoundary';
import { getSpacing } from '@wyden/utils/styles';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { useVenueAccountQuery } from '../../services/graphql/generated/graphql';
import { Notification, useNotification } from '../error-indicators/notification/useNotification';
import { FullWorkspaceComponentContainer } from '../FullWorkspaceComponentContainer';
import { LoadingDataContainer } from '../LoadingDataContainer';
import { CustodyAccountSettings } from './CustodyAccountSettings';
import { color } from '@ui/theme/colors';
import { Header } from '@ui/Typography/Header';
import { VenueIcon } from '../venue-account/VenueIcon';
import { CustodyPositions } from './custody-account-positions/CustodyAccountPositions';
import { Tab } from '@ui/Tab';
import { Tabs } from '@ui/Tabs';
import { NotFound } from '@ui/NotFound';
import { Badge } from '@ui/Badge';
import { Label } from '@ui/Typography/Label';

export const CUSTODY_ACCOUNT_SETTINGS_TAB_TEST_ID = 'custody-account-settings-tab';
export const CUSTODY_ACCOUNT_POSITIONS_TAB_TEST_ID = 'custody-account-positions-tab';

export function CustodyAccountPage({
  overrideAccountId,
  selectedTab = 0,
}: {
  overrideAccountId?: string;
  selectedTab?: number;
}) {
  const params = useParams();
  const [positionsCounter, setPositionsCounter] = useState(0);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const goBack = () => navigate(-1);
  const { addMessage } = useNotification();
  const custodyAccountId = overrideAccountId ?? params.custodyAccountId;
  const [tabValue, setTabValue] = useState(selectedTab);
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => setTabValue(newValue);

  const { data, loading, error, refetch } = useVenueAccountQuery({
    skip: !custodyAccountId,
    variables: {
      id: custodyAccountId!,
    },
    onError: () => {
      addMessage(Notification.ERROR, t('custody.custodyAccounts.preMessage'));
    },
  });
  const account = data?.venueAccount;

  return (
    <StyledPageWrapper>
      <StyledGoBackButtonWrapper>
        <GoBackButton variant="ghost" onClick={goBack}>
          <ArrowBackIcon />
          {t('common.back')}
        </GoBackButton>
      </StyledGoBackButtonWrapper>
      <ErrorBoundary errorInfo={t('venueAccounts.errorBoundaryTitle')}>
        <FullWorkspaceComponentContainer>
          <LoadingDataContainer loading={loading} data={data} error={error}>
            <HeaderContainer>
              <Header variant="h2">{account?.venueAccountName}</Header>
              <StyledVenueSection>
                <StyledVenueIcon venue={account?.venueAccountName} onlyIcon />
                <StyledVenueName>{account?.venueAccountName}</StyledVenueName>
              </StyledVenueSection>
            </HeaderContainer>
            <StyledHeader variant="h6">
              {t('common.id')}: {account?.venueAccountId}
            </StyledHeader>
            <StyledTabs withDivider value={tabValue} onChange={handleTabChange}>
              <Tab
                label={
                  <StyledBadgeWrapper>
                    <Label>{t('tabs.positions')}</Label>
                    <StyledBadgeContent>
                      <StyledBadge
                        color="primary"
                        badgeContent={positionsCounter}
                        max={999}
                        isActive={true}
                      ></StyledBadge>
                    </StyledBadgeContent>
                  </StyledBadgeWrapper>
                }
                data-testid={CUSTODY_ACCOUNT_POSITIONS_TAB_TEST_ID}
              />
              <Tab
                label={t('common.settings')}
                data-testid={CUSTODY_ACCOUNT_SETTINGS_TAB_TEST_ID}
              />
            </StyledTabs>
            {account && tabValue === 0 && (
              <CustodyPositions account={account} setPositionsCounter={setPositionsCounter} />
            )}
            {account && tabValue === 1 && (
              <CustodyAccountSettings account={account} refetchAccount={refetch} />
            )}
            {!account && <NotFound />}
          </LoadingDataContainer>
        </FullWorkspaceComponentContainer>
      </ErrorBoundary>
    </StyledPageWrapper>
  );
}

const StyledPageWrapper = styled('div')`
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-y: auto;
`;

const StyledGoBackButtonWrapper = styled('div')`
  margin-bottom: ${getSpacing(2)};
`;

const StyledTabs = styled(Tabs)`
  .MuiButtonBase-root {
    padding: ${getSpacing(2)};
  }

  button.Mui-selected {
    color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary} !important;
  }
`;

const HeaderContainer = styled('div')`
  display: flex;
  flex-direction: row;
  gap: ${getSpacing(4)};
`;

const StyledVenueSection = styled('div')`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: ${getSpacing(2)};
  align-content: center;
`;

const StyledVenueIcon = styled(VenueIcon)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;

const StyledHeader = styled(Header)`
  font-size: 11px;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
  margin-bottom: ${getSpacing(2)};
`;

const StyledVenueName = styled('div')`
  font-size: 14px;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;

const StyledBadgeWrapper = styled('div')`
  display: flex;
  gap: ${getSpacing(4)};
`;

const StyledBadgeContent = styled('div')`
  display: flex;
`;

const StyledBadge = styled(Badge)`
  .MuiBadge-badge {
    top: 10px;
    padding-top: 1px;
  }
`;
