import React from 'react';
import '../../../../../cypress/support/component';
import { CUSTODY_ACCOUNT_SETTINGS_TAB_TEST_ID, CustodyAccountPage } from '../CustodyAccountPage';
import { SnackbarProvider } from 'notistack';
import { NetworkSnackbar } from '../../error-indicators/network-indicators/NetworkSnackbar';

export class CustodyAccountDetailsPO {
  render() {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(
      <SnackbarProvider>
        <NetworkSnackbar />
        <CustodyAccountPage overrideAccountId="1" selectedTab={1} />
      </SnackbarProvider>,
    );
  }

  expectAccountHeader(name: string) {
    return cy.findByRole('heading', { level: 2, name });
  }

  expectAccountId(id: string) {
    return cy.findByText(new RegExp(`ID:.*${id}`, 'i'));
  }

  editAccountName(newName: string) {
    return cy
      .findByLabelText(/account name/i)
      .clear()
      .type(newName);
  }

  clickSaveChanges() {
    return cy.findByRole('button', { name: /save changes/i }).click();
  }

  expectSuccessToast(message: string) {
    return cy.findByText(message).should('exist');
  }

  expectTextToBeVisible(text: string) {
    return cy.contains(text).should('be.visible');
  }

  clickOnSettingsTab() {
    return cy.findByTestId(CUSTODY_ACCOUNT_SETTINGS_TAB_TEST_ID).click();
  }
}
