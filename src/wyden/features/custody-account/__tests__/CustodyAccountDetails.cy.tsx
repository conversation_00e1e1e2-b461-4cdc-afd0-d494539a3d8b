import { graphql } from 'msw';
import { worker } from '../../../../../mocks/browser';
import { Scope, VostroNostro } from '../../../services/graphql/generated/graphql';
import { aVenueAccountResponse } from '../../../services/graphql/generated/mocks';
import { CustodyAccountDetailsPO } from './CustodyAccountDetailsPO.cy';

describe('<CustodyAccountPage /> - Details View', () => {
  let custodyAccountDetails: CustodyAccountDetailsPO;

  beforeEach(() => {
    custodyAccountDetails = new CustodyAccountDetailsPO();

    worker.use(
      graphql.query('VenueAccount', (req, res, ctx) => {
        return res(
          ctx.data({
            venueAccount: aVenueAccountResponse({
              venueAccountId: '123',
              venueAccountName: 'Test Custody Account',
              vostroNostro: VostroNostro.Vostro,
              scopes: [Scope.Read, Scope.Manage],
              archivedAtDatetime: null,
              integrated: false,
            }),
          }),
        );
      }),
    );

    custodyAccountDetails.render();
  });

  it('Should render the custody account details correctly', () => {
    custodyAccountDetails.clickOnSettingsTab();
    custodyAccountDetails.expectAccountHeader('Test Custody Account');
    custodyAccountDetails.expectAccountId('123');
  });

  it('Should update custody account name successfully', () => {
    worker.use(
      graphql.mutation('VenueAccountUpdate', (req, res, ctx) => {
        return res(
          ctx.data({
            venueAccountUpdate: {
              status: 'OK',
            },
          }),
        );
      }),
    );

    custodyAccountDetails.clickOnSettingsTab();
    custodyAccountDetails.editAccountName('New Custody Account');
    custodyAccountDetails.clickSaveChanges();
    custodyAccountDetails.expectSuccessToast('Update custody account was successful');
  });

  it('Should show error toast on 500 when saving changes', () => {
    worker.use(
      graphql.mutation('VenueAccountUpdate', (req, res, ctx) => {
        return res(ctx.status(500), ctx.errors([{ message: 'Internal Server Error' }]));
      }),
    );

    custodyAccountDetails.editAccountName('Failing Update');
    custodyAccountDetails.clickSaveChanges();
    custodyAccountDetails.expectTextToBeVisible('Updating account failed');
  });

  it('Should show 404 when custody account does not exist', () => {
    worker.use(
      graphql.query('VenueAccount', (req, res, ctx) => {
        return res(ctx.data({ venueAccount: null }));
      }),
    );

    custodyAccountDetails.render();
    custodyAccountDetails.expectTextToBeVisible('Not found');
  });

  it('Should show generic error on 400', () => {
    worker.use(
      graphql.query('VenueAccount', (req, res, ctx) => {
        return res(ctx.status(400), ctx.errors([{ message: 'Bad Request' }]));
      }),
    );

    custodyAccountDetails.render();
    custodyAccountDetails.expectTextToBeVisible('Something went wrong');
  });
});
