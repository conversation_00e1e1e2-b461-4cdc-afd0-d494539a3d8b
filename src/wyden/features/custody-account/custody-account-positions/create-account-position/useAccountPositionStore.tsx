import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface AccountPositionStore {
  accountPositionDialogOpen: boolean;
  toggleAccountPositionDialog: () => void;
}

const ACCOUNT_POSITION_ACTIONS = {
  TOGGLE_ADD_ACCOUNT_POSITION_DIALOG: 'toggleAccountPositionDialog',
} as const;

export const useAccountPositionStore = create<AccountPositionStore>()(
  devtools(
    (set) => ({
      accountPositionDialogOpen: false,
      isDataLoading: false,
      toggleAccountPositionDialog: () =>
        set(
          (state) => ({
            accountPositionDialogOpen: !state.accountPositionDialogOpen,
          }),
          false,
          ACCOUNT_POSITION_ACTIONS.TOGGLE_ADD_ACCOUNT_POSITION_DIALOG,
        ),
    }),
    { name: 'AccountPositionStore' },
  ),
);
