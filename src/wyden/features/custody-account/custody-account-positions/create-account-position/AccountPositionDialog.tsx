import { TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';

import { Dialog } from '@ui/Dialog';
import { styled } from '@ui/styled';
import { getSpacing } from '@wyden/utils/styles';
import { useAccountPositionManagement } from './useAccountPositionManagement';
import { useAccountPositionStore } from './useAccountPositionStore';
import { AccountPositionFormSchema, CreateAccountPositionForm } from './AccountPositionForm';
import { ConnectorResponse } from '../../../../services/graphql/generated/graphql';
import { useConnectors } from '@wyden/hooks/useConnectors';

export const AccountPositionDialog = ({ venueAccountId }: { venueAccountId: string }) => {
  const { t } = useTranslation();
  const { createAccountPosition } = useAccountPositionManagement({ venueAccountId });
  const { accountPositionDialogOpen, toggleAccountPositionDialog } = useAccountPositionStore();
  const { getAllConnectors } = useConnectors();

  const getCustodyAccountFormProps = (
    t: TFunction<'translation', undefined>,
    connectors: ConnectorResponse[],
  ) => ({
    currency: {
      label: t('custody.custodyAccounts.positions.positionForm.currency'),
      required: true,
      id: 'currency',
    },
    connector: {
      label: t('custody.custodyAccounts.positions.positionForm.connector'),
      options: connectors
        .filter((connector) => connector.connectorId && connector.connectorName)
        .map((connector) => ({
          id: connector.connectorId || '',
          label: connector.connectorName || '',
          venue: connector.venue || '',
          deactivatedAtDateTime: connector.deactivatedAtDateTime || null,
        })),
      freeSolo: false,
      required: false,
      disableClearable: false,
    },
    walletId: {
      label: t('custody.custodyAccounts.positions.positionForm.walletId'),
      required: false,
      id: 'walletId',
    },
  });

  return (
    <StyledDialog open={accountPositionDialogOpen} onClose={toggleAccountPositionDialog}>
      <CreateAccountPositionForm
        onSubmit={(values) => {
          createAccountPosition({ ...values, venueAccountId });
          toggleAccountPositionDialog();
        }}
        props={getCustodyAccountFormProps(t, getAllConnectors())}
        schema={AccountPositionFormSchema}
      />
    </StyledDialog>
  );
};

const StyledDialog = styled(Dialog)`
  padding-top: ${getSpacing(3)};
`;
