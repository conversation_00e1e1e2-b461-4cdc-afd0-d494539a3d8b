import { useVenueAccountPositionCreateMutation } from '@wyden/services/graphql/generated/graphql';
import { useTranslation } from 'react-i18next';
import {
  Notification,
  useNotification,
} from '../../../error-indicators/notification/useNotification';

export interface AccountPositionFormValues {
  venueAccountId: string;
  currency: string;
  walletId?: string | null | undefined;
  connector: {
    id: string;
    label: string;
    deactivatedAt?: string | null | undefined;
    venue: string;
  };
}

export const useAccountPositionManagement = ({ venueAccountId }: { venueAccountId: string }) => {
  const { t } = useTranslation();
  const { addMessage } = useNotification();

  const [venueAccountPositionCreate, { error: creatingAccountPositionError }] =
    useVenueAccountPositionCreateMutation({
      onError: () => {
        addMessage(
          Notification.ERROR,
          t('custody.custodyAccounts.positions.positionForm.preMessage'),
        );
      },
      onCompleted: () => {
        addMessage(
          Notification.SUCCESS,
          t('custody.custodyAccounts.positions.positionForm.preMessage'),
        );
      },
    });

  return {
    createAccountPosition: async (values: AccountPositionFormValues) => {
      await venueAccountPositionCreate({
        variables: {
          venueAccountId,
          input: {
            walletId: values.walletId,
            currency: values.currency,
            connectorId: values.connector?.id,
          },
        },
      });
    },
    createAccountPositionError: creatingAccountPositionError,
  } as const;
};
