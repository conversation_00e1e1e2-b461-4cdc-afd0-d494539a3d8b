import { createTsForm, createUniqueFieldSchema } from '@ts-react/form';
import { TextField } from '@wyden/features/form/TextField';
import { z } from 'zod';
import { DialogForm, DialogFormProps } from '../../../form/DialogForm';
import { useAccountPositionStore } from './useAccountPositionStore';
import { ConnectorAutocomplete } from '@wyden/features/exchange-account/create-exchange-account/ConnectorAutocomplete';

const ConnectorFieldDef = createUniqueFieldSchema(
  z
    .object({
      id: z.string(),
      label: z.string(),
      venue: z.string(),
      deactivatedAt: z.string().optional().nullable(),
    })
    .optional()
    .nullable(),
  'connector',
);

const AccountPositionFormSchemaZObject = {
  currency: z.string({
    required_error: 'custody.custodyAccounts.positions.positionForm.currencyIsRequired',
  }),
  connector: ConnectorFieldDef,
  walletId: z.string().optional().nullable(),
};

export const AccountPositionFormSchema = z.object(AccountPositionFormSchemaZObject);

export const mapping = [
  [z.string(), TextField],
  [ConnectorFieldDef, ConnectorAutocomplete],
] as const;

const CreateAccountPositionDialogForm = (props: DialogFormProps) => {
  const { toggleAccountPositionDialog } = useAccountPositionStore();
  return (
    <DialogForm
      {...props}
      toggleDialog={toggleAccountPositionDialog}
      titleTranslationId="custody.custodyAccounts.positions.positionForm.createPosition"
      submitButtonTranslationId="custody.custodyAccounts.positions.positionForm.createPosition"
    />
  );
};

export const CreateAccountPositionForm = createTsForm(mapping, {
  FormComponent: CreateAccountPositionDialogForm,
});
