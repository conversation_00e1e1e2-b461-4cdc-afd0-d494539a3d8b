import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { GetRowIdParams } from 'ag-grid-community';

import { styled } from '@ui/styled';
import { NotFound } from '@ui/NotFound';
import AddIcon from '@mui/icons-material/Add';
import {
  Resource,
  Scope,
  useVenueAccountPositionsQuery,
  VenueAccountPosition,
  VenueAccountResponse,
} from '@wyden/services/graphql/generated/graphql';
import { getSpacing } from '@wyden/utils/styles';
import { Search, StyledSearchWrapper } from '@ui/Search';
import { LoadingDataContainer } from '../../LoadingDataContainer';
import { WydenGrid } from '../../grid/WydenGrid';
import { custodyAccountPositionsColumnDefinitions } from './columnDefinitions';
import {
  Notification,
  useNotification,
} from '@wyden/features/error-indicators/notification/useNotification';
import { Button } from '@ui/Button';
import { usePermissions } from '@wyden/hooks/usePermissions';
import { useAccountPositionStore } from './create-account-position/useAccountPositionStore';
import { AccountPositionDialog } from './create-account-position/AccountPositionDialog';

type VenueAccountSettingsProps = {
  account: VenueAccountResponse | undefined | null;
  setPositionsCounter: (counter: number) => void;
};

export const ADD_CUSTODY_ACCOUNT_POSITION_BUTTON_TEST_ID = 'add-custody-account-position-button';

export function CustodyPositions({ account, setPositionsCounter }: VenueAccountSettingsProps) {
  const { t } = useTranslation();
  const { addMessage } = useNotification();
  const { checkIfPermitted } = usePermissions();
  const { toggleAccountPositionDialog } = useAccountPositionStore();
  const [searchVal, setSearchVal] = useState<string | undefined>('');
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => setSearchVal(e.target.value);
  const getCustodyAccountPositionsRow = (params: GetRowIdParams<VenueAccountPosition>) =>
    params.data?.currency;
  const { data, loading, error, refetch } = useVenueAccountPositionsQuery({
    skip: !account?.venueAccountId,
    variables: { id: account?.venueAccountId ?? '' },
    onError: () => {
      addMessage(Notification.ERROR, t('custody.custodyAccounts.positions.preMessage'));
    },
  });

  useEffect(() => {
    setPositionsCounter(data?.venueAccountPositions?.positions?.length || 0);
  }, [data, setPositionsCounter]);

  return (
    <StyledContainer>
      {!account ? (
        <NotFound />
      ) : (
        <>
          <SearchContainer>
            <StyledSearchWrapper>
              <Search
                fullWidth
                onChange={handleSearch}
                autoFocus={false}
                placeholder={t('custody.custodyAccounts.positions.findByPositionName')}
              />
            </StyledSearchWrapper>
            <Button
              disabled={
                !checkIfPermitted({
                  resource: Resource.Connector,
                  scope: Scope.Create,
                })
              }
              disabled-tooltip-message={t('connectors.noPermissionToAdd')}
              onClick={toggleAccountPositionDialog}
              data-testid={ADD_CUSTODY_ACCOUNT_POSITION_BUTTON_TEST_ID}
            >
              <AddIcon />
              {t('custody.custodyAccounts.positions.addPosition')}
            </Button>
          </SearchContainer>
          <LoadingDataContainer loading={loading} data={data} refetch={refetch} error={error}>
            <WydenGrid
              isDataLoading={loading}
              data-testid="custody-account-positions-data-grid"
              getRowId={getCustodyAccountPositionsRow}
              rowData={data?.venueAccountPositions?.positions || []}
              columnDefs={custodyAccountPositionsColumnDefinitions}
              quickFilterText={searchVal}
            />
          </LoadingDataContainer>
          <AccountPositionDialog venueAccountId={account.venueAccountId || ''} />
        </>
      )}
    </StyledContainer>
  );
}

const StyledContainer = styled('div')`
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(2)};
  padding-top: ${getSpacing(2)};
  max-height: 100%;
  height: 100%;
`;

const SearchContainer = styled('div')`
  display: flex;
  justify-content: space-between;
  padding-top: ${getSpacing(2)};
  padding-bottom: ${getSpacing(2)};
`;
