import { styled } from '@ui/styled';
import { InstrumentIcon } from '@wyden/components/InstrumentIcon';
import { VenueAccountPosition } from '@wyden/services/graphql/generated/graphql';
import { getSpacing } from '@wyden/utils/styles';
import { ICellRendererParams } from 'ag-grid-community';

export const PositionIconRenderer = ({ data }: ICellRendererParams<VenueAccountPosition>) => (
  <StyledIconWrapper>
    <InstrumentIcon baseCurrency={data?.currency} /> {data?.currency}
  </StyledIconWrapper>
);

const StyledIconWrapper = styled('div')`
  padding: 3px 2px 3px 1px;
  display: flex;
  align-items: center;
  gap: ${getSpacing(2)};
`;
