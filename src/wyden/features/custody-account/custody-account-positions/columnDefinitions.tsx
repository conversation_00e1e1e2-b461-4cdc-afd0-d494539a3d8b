import i18n from '@wyden/i18n';
import { ColDef } from 'ag-grid-community';

import { VenueAccountPosition } from '../../../services/graphql/generated/graphql';
import { buildObjectKeyValidator, getGenericField } from '@wyden/features/grid/utils';
import { PositionIconRenderer } from './renderers';

const validKey = buildObjectKeyValidator<VenueAccountPosition>();

export const currencyKey = validKey('currency');
export const connectorNameKey = validKey('connectorName');

export const custodyAccountPositionsColumnDefinitions: ColDef<VenueAccountPosition | null>[] = [
  getGenericField(currencyKey, {
    headerName: i18n.t('grid.columnHeader.asset'),
    sort: 'asc',
    sortable: true,
    cellRenderer: PositionIconRenderer,
  }),
  getGenericField(connectorNameKey, {
    headerName: i18n.t('grid.columnHeader.connectedTo'),
  }),
];
