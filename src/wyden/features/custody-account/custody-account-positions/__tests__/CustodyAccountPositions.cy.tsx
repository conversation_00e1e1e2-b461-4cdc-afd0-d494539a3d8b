import { graphql } from 'msw';
import { worker } from '../../../../../../mocks/browser';
import { CustodyAccountPositionsPO } from './CustodyAccountPositionsPO.cy';
import { custodyAccountPositionsMockResponse } from './mocks';

describe('<CustodyPositions />', () => {
  const PO = new CustodyAccountPositionsPO();

  beforeEach(() => {
    worker.use(
      graphql.query('VenueAccountPositions', (req, res, ctx) => {
        return res(ctx.data(custodyAccountPositionsMockResponse));
      }),
    );
    PO.render();
  });

  it('should render custody account positions', () => {
    PO.expectTextToBeVisible('BTC');
    PO.expectTextToBeVisible('DOGE');
    PO.expectTextToBeVisible('ETH');
  });

  it('should show error message when custody account positions fetch fails with error (5XX)', () => {
    worker.use(
      graphql.query('VenueAccountPositions', (req, res, ctx) => {
        return res(
          ctx.status(500),
          ctx.errors([
            {
              message: 'Internal Server Error',
            },
          ]),
        );
      }),
    );

    PO.expectTextToBeVisible('Fetching custody account positions failed');
    PO.expectTextToBeVisible('An error occurred while loading data');
    PO.expectTextToBeVisible('Something went wrong');
  });

  it('should not allow to add position without currency ', () => {
    PO.clickOnAddCustodyAccountPosition();
    PO.expectTextToBeVisible('Create position');
    PO.clickOnSubmitAddCustodyAccountPositionButton();
    PO.expectTextToBeVisible('Create position');
    PO.clickOnCloseModal();
  });

  it('should add position and display success notification', () => {
    worker.use(
      graphql.mutation('VenueAccountPositionCreate', (req, res, ctx) => {
        return res(
          ctx.data({
            status: 'SUCCESS',
          }),
        );
      }),
    );
    PO.clickOnAddCustodyAccountPosition();
    PO.expectTextToBeVisible('Create position');
    PO.insertByLabelText('Currency', 'BTC');
    PO.selectConnector('Bitmex Connector');
    PO.clickOnSubmitAddCustodyAccountPositionButton();
    PO.expectTextToBeVisible('Adding account position was successful');
    PO.expectTextToNotBeVisible('Create position');
  });

  it('should show error message when adding position fails with error (5XX)', () => {
    worker.use(
      graphql.mutation('VenueAccountPositionCreate', (req, res, ctx) => {
        return res(ctx.status(500), ctx.errors([{ message: 'Internal Server Error' }]));
      }),
    );
    PO.clickOnAddCustodyAccountPosition();
    PO.expectTextToBeVisible('Create position');
    PO.insertByLabelText('Currency', 'BTC');
    PO.selectConnector('Bitmex Connector');
    PO.clickOnSubmitAddCustodyAccountPositionButton();
    PO.expectTextToBeVisible('Adding account position failed');
    PO.expectTextToNotBeVisible('Create position');
  });

  it('should show error message when adding position fails with error (4XX)', () => {
    worker.use(
      graphql.mutation('VenueAccountPositionCreate', (req, res, ctx) => {
        return res(ctx.status(400), ctx.errors([{ message: 'Bad Request' }]));
      }),
    );
    PO.clickOnAddCustodyAccountPosition();
    PO.expectTextToBeVisible('Create position');
    PO.insertByLabelText('Currency', 'BTC');
    PO.selectConnector('Bitmex Connector');
    PO.clickOnSubmitAddCustodyAccountPositionButton();
    PO.expectTextToBeVisible('Adding account position failed');
    PO.expectTextToNotBeVisible('Create position');
  });
});
