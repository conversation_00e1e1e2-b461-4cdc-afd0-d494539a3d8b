import { Scope, VostroNostro } from '@wyden/services/graphql/generated/graphql';
import {
  aVenueAccountPosition,
  aVenueAccountPositionsResponse,
  aVenueAccountResponse,
} from '@wyden/services/graphql/generated/mocks';

export const anAccount = aVenueAccountResponse({
  venueAccountName: 'Custody account 1',
  venueAccountId: '1',
  vostroNostro: VostroNostro.Nostro,
  scopes: [Scope.Read],
  archivedAtDatetime: null,
});

export const custodyAccountPositionsMockResponse = {
  venueAccountPositions: aVenueAccountPositionsResponse({
    venueAccountId: 'fireblocksVenueAccountId',
    positions: [
      aVenueAccountPosition({
        currency: 'BTC',
        metadata: [
          {
            key: 'WALLET_ID',
            value: '10b850c4-bcce-4a70-b457-9b1e4f2542bc',
            __typename: 'KeyValue',
          },
        ],
      }),
      aVenueAccountPosition({
        currency: 'ETH',
        metadata: [
          {
            key: 'WALLET_ID',
            value: 'ee0d1234-2060-4ae4-9091-add40cc9052f',
            __typename: 'KeyValue',
          },
        ],
      }),
      aVenueAccountPosition({
        currency: 'DOGE',
        metadata: [
          {
            key: 'WALLET_ID',
            value: 'e98b4770-a37f-4462-b0e9-75c57182588f',
            __typename: 'KeyValue',
          },
        ],
      }),
    ],
  }),
};
