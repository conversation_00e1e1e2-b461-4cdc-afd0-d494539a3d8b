import React from 'react';
import '../../../../../../cypress/support/component';
import { SnackbarProvider } from 'notistack';
import {
  ADD_CUSTODY_ACCOUNT_POSITION_BUTTON_TEST_ID,
  CustodyPositions,
} from '../CustodyAccountPositions';
import { anAccount } from './mocks';
import { MODAL_CLOSE_DATA_TEST_ID } from '../../../../../ui/DialogTitle';
import { FormPO } from '../../../form/__test__/FormPO.cy';

export class CustodyAccountPositionsPO {
  private formPO = new FormPO();

  render() {
    cy.viewport(1020, 1080);
    return cy.mountWithProviders(
      <SnackbarProvider autoHideDuration={500} maxSnack={3}>
        <CustodyPositions account={anAccount} setPositionsCounter={() => {}} />
      </SnackbarProvider>,
    );
  }

  expectTextToBeVisible(text: string) {
    return cy.contains(text).should('be.visible');
  }

  expectTextToNotBeVisible(text: string) {
    return cy.findByText(text).should('not.exist');
  }

  clickOnAddCustodyAccountPosition() {
    cy.get(`[data-testid="${ADD_CUSTODY_ACCOUNT_POSITION_BUTTON_TEST_ID}"]`).click();
  }

  clickOnSubmitAddCustodyAccountPositionButton() {
    cy.get('button[type="submit"]').contains('Create position').click();
  }

  clickOnCloseModal() {
    cy.get(`[data-testid="${MODAL_CLOSE_DATA_TEST_ID}"]`).click();
  }

  selectConnector(value: string) {
    this.formPO.selectByLabelText('Connector', value);
  }

  insertByLabelText(label: string, value: string) {
    this.formPO.insertByLabelText(label, value);
  }
}
