import { createTsForm } from '@ts-react/form';
import { TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';

import { styled } from '@ui/styled';
import { NotFound } from '@ui/NotFound';
import { getSpacing } from '@wyden/utils/styles';
import { VenueAccountResponse, VostroNostro } from '@wyden/services/graphql/generated/graphql';
import { EditAccountFormComponent } from '../venue-account/edit-account/EditAccountForm';
import {
  CustodyAccountFormSchema,
  custodyAccountMapping,
} from './create-custody-account/CreateCustodyAccountForm';
import {
  CustodyAccountFormValues,
  useCustodyAccountManagement,
} from './useCustodyAccountManagement';

type VenueAccountSettingsProps = {
  account: VenueAccountResponse | undefined | null;
  refetchAccount: () => Promise<unknown>;
};

export function CustodyAccountSettings({ account, refetchAccount }: VenueAccountSettingsProps) {
  const { t } = useTranslation();
  const { updateCustodyAccount } = useCustodyAccountManagement();

  const getCustodyAccountFormProps = (t: TFunction<'translation', undefined>) => ({
    venueAccountName: {
      label: t('custody.custodyAccounts.custodyAccountForm.accountName'),
      disabled: false,
      required: true,
    },
    vostroNostro: {
      label: t('custody.custodyAccounts.custodyAccountForm.vostroNostro'),
      disabled: true,
      required: false,
      options: [
        {
          id: VostroNostro.Nostro,
          label: t('common.nostro'),
          // disabled: !generalCustodyAccountPermitted,
        },
        {
          id: VostroNostro.Vostro,
          label: t('common.vostro'),
          // disabled: !generalCustodyAccountPermitted,
        },
      ],
    },
    integrated: {
      label: t('custody.custodyAccounts.custodyAccountForm.integrated'),
      disabled: true,
      required: false,
    },
  });

  const defaultValues = {
    ...account,
    venueAccountName: account?.venueAccountName ?? undefined,
    vostroNostro: account?.vostroNostro ?? undefined,
  };

  const handleSubmit = async (values: CustodyAccountFormValues) => {
    await updateCustodyAccount({
      ...values,
    });
    refetchAccount();
  };

  return (
    <StyledContainer>
      {account && (
        <EditCustodyAccountForm
          onSubmit={handleSubmit}
          props={getCustodyAccountFormProps(t)}
          schema={CustodyAccountFormSchema}
          defaultValues={defaultValues}
        />
      )}
      {!account && <NotFound />}
    </StyledContainer>
  );
}

export const EditCustodyAccountForm = createTsForm(custodyAccountMapping, {
  FormComponent: EditAccountFormComponent,
});

const StyledContainer = styled('div')`
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(2)};
  max-height: 100%;
  height: 100%;
`;
