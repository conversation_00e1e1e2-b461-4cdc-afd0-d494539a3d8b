import { createTsForm } from '@ts-react/form';
import { NotFound } from '@ui/NotFound';
import { styled } from '@ui/styled';
import { Tab } from '@ui/Tab';
import { Tabs } from '@ui/Tabs';
import { color } from '@ui/theme/colors';
import { Header } from '@ui/Typography/Header';
import { VenueAccountResponse, VostroNostro } from '@wyden/services/graphql/generated/graphql';
import { getSpacing } from '@wyden/utils/styles';
import { TFunction } from 'i18next';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { EditAccountFormComponent } from '../venue-account/edit-account/EditAccountForm';
import { VenueIcon } from '../venue-account/VenueIcon';
import {
  CustodyAccountFormSchema,
  custodyAccountMapping,
} from './create-custody-account/CreateCustodyAccountForm';
import {
  CustodyAccountFormValues,
  useCustodyAccountManagement,
} from './useCustodyAccountManagement';

type VenueAccountSettingsProps = {
  account: VenueAccountResponse | undefined | null;
  refetchAccount: () => Promise<unknown>;
};

export function CustodyAccountSettings({ account, refetchAccount }: VenueAccountSettingsProps) {
  const { t } = useTranslation();
  const [tabValue, setValue] = useState(0);
  const { updateCustodyAccount } = useCustodyAccountManagement();
  const handleChange = (_event: React.SyntheticEvent, newValue: number) => setValue(newValue);

  const getCustodyAccountFormProps = (t: TFunction<'translation', undefined>) => ({
    venueAccountName: {
      label: t('custody.custodyAccounts.custodyAccountForm.accountName'),
      disabled: false,
      required: true,
    },
    vostroNostro: {
      label: t('custody.custodyAccounts.custodyAccountForm.vostroNostro'),
      disabled: true,
      required: false,
      options: [
        {
          id: VostroNostro.Nostro,
          label: t('common.nostro'),
          // disabled: !generalCustodyAccountPermitted,
        },
        {
          id: VostroNostro.Vostro,
          label: t('common.vostro'),
          // disabled: !generalCustodyAccountPermitted,
        },
      ],
    },
    integrated: {
      label: t('custody.custodyAccounts.custodyAccountForm.integrated'),
      disabled: true,
      required: false,
    },
  });

  const defaultValues = {
    ...account,
    venueAccountName: account?.venueAccountName ?? undefined,
    vostroNostro: account?.vostroNostro ?? undefined,
  };

  const handleSubmit = async (values: CustodyAccountFormValues) => {
    await updateCustodyAccount({
      ...values,
    });
    refetchAccount();
  };

  return (
    <StyledContainer>
      <HeaderContainer>
        <Header variant="h2">{account?.venueAccountName}</Header>
        <StyledVenueSection>
          <StyledVenueIcon venue={account?.venueAccountName} onlyIcon />
          <StyledVenueName>{account?.venueAccountName}</StyledVenueName>
        </StyledVenueSection>
      </HeaderContainer>
      <StyledHeader variant="h6">
        {t('common.id')}: {account?.venueAccountId}
      </StyledHeader>
      <StyledTabs withDivider value={tabValue} onChange={handleChange}>
        <Tab label={t('common.settings')} />
      </StyledTabs>
      {account && (
        <>
          <EditCustodyAccountForm
            onSubmit={handleSubmit}
            props={getCustodyAccountFormProps(t)}
            schema={CustodyAccountFormSchema}
            defaultValues={defaultValues}
          />
        </>
      )}
      {!account && <NotFound />}
    </StyledContainer>
  );
}

export const EditCustodyAccountForm = createTsForm(custodyAccountMapping, {
  FormComponent: EditAccountFormComponent,
});

const StyledTabs = styled(Tabs)`
  .MuiButtonBase-root {
    padding: ${getSpacing(2)};
  }

  button.Mui-selected {
    color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary} !important;
  }
`;

const StyledContainer = styled('div')`
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(2)};
  max-height: 100%;
  height: 100%;
`;

const HeaderContainer = styled('div')`
  display: flex;
  flex-direction: row;
  gap: ${getSpacing(4)};
`;

const StyledVenueSection = styled('div')`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: ${getSpacing(2)};
  align-content: center;
`;

const StyledVenueIcon = styled(VenueIcon)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;

const StyledHeader = styled(Header)`
  font-size: 11px;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
  margin-bottom: ${getSpacing(2)};
`;

const StyledVenueName = styled('div')`
  font-size: 14px;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;
