import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import * as Sentry from '@sentry/react';
import { logger } from '../logger';

vi.mock('@sentry/react', () => ({
  isInitialized: vi.fn(() => true),
  addBreadcrumb: vi.fn(),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
}));

const originalConsole = { ...console };
beforeEach(() => {
  console.debug = vi.fn();
  console.info = vi.fn();
  console.warn = vi.fn();
  console.error = vi.fn();
  vi.clearAllMocks();
  logger.clearLogs();
});

afterEach(() => {
  Object.assign(console, originalConsole);
});

describe('Logger', () => {
  describe('console logging', () => {
    it('should log debug messages to console with WYDEN LOG prefix', () => {
      const message = 'Debug message';
      const metadata = { userId: '123' };

      logger.debug(message, metadata);

      expect(console.debug).toHaveBeenCalledWith(
        expect.stringMatching(/^WYDEN LOG: \[.*\] \[DEBUG\] Debug message$/),
        metadata,
      );
    });

    it('should log info messages to console with WYDEN LOG prefix', () => {
      const message = 'Info message';

      logger.info(message);

      expect(console.info).toHaveBeenCalledWith(
        expect.stringMatching(/^WYDEN LOG: \[.*\] \[INFO\] Info message$/),
      );
    });

    it('should log warn messages to console with WYDEN LOG prefix', () => {
      const message = 'Warning message';

      logger.warn(message);

      expect(console.warn).toHaveBeenCalledWith(
        expect.stringMatching(/^WYDEN LOG: \[.*\] \[WARN\] Warning message$/),
      );
    });

    it('should log error messages to console with WYDEN LOG prefix', () => {
      const message = 'Error message';

      logger.error(message);

      expect(console.error).toHaveBeenCalledWith(
        expect.stringMatching(/^WYDEN LOG: \[.*\] \[ERROR\] Error message$/),
      );
    });

    it('should include metadata in console logs', () => {
      const message = 'Message with metadata';
      const metadata = { userId: '123', action: 'save' };

      logger.info(message, metadata);

      expect(console.info).toHaveBeenCalledWith(
        expect.stringMatching(/^WYDEN LOG: \[.*\] \[INFO\] Message with metadata$/),
        metadata,
      );
    });
  });

  describe('sentry logging', () => {
    it('should send error messages to Sentry', () => {
      const message = 'Error message';
      const metadata = { userId: '123' };

      logger.error(message, metadata);

      expect(Sentry.addBreadcrumb).toHaveBeenCalledWith({
        message: 'WYDEN LOG: Error message',
        level: 'error',
        timestamp: expect.any(Number),
        data: metadata,
      });

      expect(Sentry.captureMessage).toHaveBeenCalledWith('WYDEN LOG: Error message', {
        level: 'error',
        tags: {
          source: 'wyden-logger',
        },
        extra: metadata,
      });
    });

    it('should send warning messages to Sentry', () => {
      const message = 'Warning message';

      logger.warn(message);

      expect(Sentry.addBreadcrumb).toHaveBeenCalledWith({
        message: 'WYDEN LOG: Warning message',
        level: 'warning',
        timestamp: expect.any(Number),
        data: {},
      });

      expect(Sentry.captureMessage).toHaveBeenCalledWith('WYDEN LOG: Warning message', {
        level: 'warning',
        tags: {
          source: 'wyden-logger',
        },
        extra: {},
      });
    });

    it('should only add breadcrumbs for debug and info messages', () => {
      logger.debug('Debug message');
      logger.info('Info message');

      expect(Sentry.addBreadcrumb).toHaveBeenCalledTimes(2);
      expect(Sentry.captureMessage).not.toHaveBeenCalled();
    });

    it('should handle Sentry not initialized', () => {
      vi.mocked(Sentry.isInitialized).mockReturnValue(false);

      logger.error('Error message');

      expect(Sentry.addBreadcrumb).not.toHaveBeenCalled();
      expect(Sentry.captureMessage).not.toHaveBeenCalled();
    });
  });

  describe('log persistence', () => {
    it('should store logs in memory', () => {
      logger.info('First log');
      logger.warn('Second log');
      logger.error('Third log');

      const logs = logger.getLogs();
      expect(logs).toHaveLength(3);
      expect(logs[0].message).toBe('First log');
      expect(logs[0].level).toBe('info');
      expect(logs[1].message).toBe('Second log');
      expect(logs[1].level).toBe('warn');
      expect(logs[2].message).toBe('Third log');
      expect(logs[2].level).toBe('error');
    });

    it('should include timestamp and metadata in stored logs', () => {
      const beforeTime = Date.now();
      const metadata = { userId: '123', action: 'test' };

      logger.info('Test message', metadata);

      const logs = logger.getLogs();
      const log = logs[0];

      expect(log.timestamp).toBeGreaterThanOrEqual(beforeTime);
      expect(log.timestamp).toBeLessThanOrEqual(Date.now());
      expect(log.userId).toBe('123');
      expect(log.action).toBe('test');
    });

    it('should return copy of logs array to prevent mutation', () => {
      logger.info('Test log');

      const logs1 = logger.getLogs();
      const logs2 = logger.getLogs();

      expect(logs1).not.toBe(logs2); // Different array instances
      expect(logs1).toEqual(logs2); // Same content
    });
  });

  describe('log count', () => {
    it('should return correct log count', () => {
      expect(logger.getLogCount()).toBe(0);

      logger.info('Log 1');
      expect(logger.getLogCount()).toBe(1);

      logger.warn('Log 2');
      logger.error('Log 3');
      expect(logger.getLogCount()).toBe(3);
    });
  });

  describe('clear logs', () => {
    it('should clear all stored logs', () => {
      logger.info('Log 1');
      logger.warn('Log 2');
      expect(logger.getLogCount()).toBe(2);

      logger.clearLogs();

      expect(logger.getLogCount()).toBe(0);
      expect(logger.getLogs()).toHaveLength(0);
    });
  });

  describe('log filtering', () => {
    beforeEach(() => {
      // Setup test data
      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warning message');
      logger.error('Error message');
      logger.info('API call succeeded', { endpoint: '/api/graphql' });
      logger.error('API call failed', { endpoint: '/api/orders' });
    });

    describe('level filtering', () => {
      it('should filter by single level', () => {
        const errorLogs = logger.getLogs({ level: 'error' });
        expect(errorLogs).toHaveLength(2);
        expect(errorLogs.every((log) => log.level === 'error')).toBe(true);
      });

      it('should filter by multiple levels', () => {
        const criticalLogs = logger.getLogs({ level: ['warn', 'error'] });
        expect(criticalLogs).toHaveLength(3);
        expect(criticalLogs.every((log) => log.level === 'warn' || log.level === 'error')).toBe(
          true,
        );
      });

      it('should return empty array for non-existent level', () => {
        const logs = logger.getLogs({ level: 'debug' });
        expect(logs).toHaveLength(1);
        expect(logs[0].level).toBe('debug');
      });
    });

    describe('time range filtering', () => {
      it('should filter by end time', () => {
        // Get current timestamp of existing logs
        const existingLogs = logger.getLogs();
        const latestTimestamp = Math.max(...existingLogs.map((log) => log.timestamp));

        const olderLogs = logger.getLogs({ endTime: latestTimestamp });
        expect(olderLogs).toHaveLength(6); // Original 6 logs before cutoff
        expect(olderLogs.every((log) => log.timestamp <= latestTimestamp)).toBe(true);
      });

      it('should filter by time range', () => {
        const existingLogs = logger.getLogs();
        const earliestTimestamp = Math.min(...existingLogs.map((log) => log.timestamp));
        const latestTimestamp = Math.max(...existingLogs.map((log) => log.timestamp));

        const logs = logger.getLogs({
          startTime: earliestTimestamp,
          endTime: latestTimestamp,
        });
        expect(logs).toHaveLength(6); // All existing logs should be included
        expect(
          logs.every(
            (log) => log.timestamp >= earliestTimestamp && log.timestamp <= latestTimestamp,
          ),
        ).toBe(true);
      });
    });

    describe('message content filtering', () => {
      it('should filter by message content (case insensitive)', () => {
        const apiLogs = logger.getLogs({ messageContains: 'api' });
        expect(apiLogs).toHaveLength(2);
        expect(apiLogs.every((log) => log.message.toLowerCase().includes('api'))).toBe(true);
      });

      it('should filter by partial message content', () => {
        const successLogs = logger.getLogs({ messageContains: 'succeeded' });
        expect(successLogs).toHaveLength(1);
        expect(successLogs[0].message).toBe('API call succeeded');
      });

      it('should return empty array for non-matching content', () => {
        const logs = logger.getLogs({ messageContains: 'nonexistent' });
        expect(logs).toHaveLength(0);
      });
    });

    describe('combined filtering', () => {
      it('should apply multiple filters together', () => {
        const filteredLogs = logger.getLogs({
          level: ['info', 'error'],
          messageContains: 'API',
        });

        expect(filteredLogs).toHaveLength(2);
        expect(
          filteredLogs.every(
            (log) =>
              (log.level === 'info' || log.level === 'error') &&
              log.message.toLowerCase().includes('api'),
          ),
        ).toBe(true);
      });

      it('should return empty array when no logs match all filters', () => {
        const logs = logger.getLogs({
          level: 'debug',
          messageContains: 'API',
        });

        expect(logs).toHaveLength(0);
      });
    });
  });

  describe('memory management', () => {
    it('should limit stored logs to prevent memory issues', () => {
      // This test simulates the MAX_STORED_LOGS limit
      // Since MAX_STORED_LOGS is 1000, we'll test with a smaller number to verify the concept
      const initialCount = logger.getLogCount();

      // Add many logs
      for (let i = 0; i < 50; i++) {
        logger.info(`Log ${i}`);
      }

      expect(logger.getLogCount()).toBe(initialCount + 50);

      // Verify logs are in order (newest logs preserved)
      const logs = logger.getLogs();
      expect(logs[logs.length - 1].message).toBe('Log 49');
      expect(logs[logs.length - 2].message).toBe('Log 48');
    });
  });

  describe('edge cases', () => {
    it('should handle non-string messages', () => {
      const num = 42;

      logger.error(num);

      const logs = logger.getLogs();
      expect(logs[0].message).toBe('42');
    });

    it('should handle null and undefined metadata', () => {
      logger.info('Test message', null);
      logger.warn('Another message', undefined);

      const logs = logger.getLogs();
      expect(logs).toHaveLength(2);
      // Should not throw errors
    });

    it('should handle complex metadata objects', () => {
      const complexMetadata = {
        user: { id: 123, name: 'John' },
        array: [1, 2, 3],
        nested: { deep: { value: 'test' } },
      };

      logger.info('Complex metadata test', complexMetadata);

      const logs = logger.getLogs();
      const log = logs[0];
      expect(log.user).toEqual({ id: 123, name: 'John' });
      expect(log.array).toEqual([1, 2, 3]);
      expect(log.nested).toEqual({ deep: { value: 'test' } });
    });
  });
});
