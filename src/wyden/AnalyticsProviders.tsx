import * as Sentry from '@sentry/react';
import { ReactElement } from 'react';
import { PostHogProvider } from 'posthog-js/react';
import { logger } from './utils/logger';

interface AnalyticsProvidersProps {
  children?: ReactElement;
}

const options = {
  api_host: 'https://eu.i.posthog.com',
};

const AnalyticsInitializer = (props: AnalyticsProvidersProps) => {
  try {
    if (!Sentry.isInitialized()) {
      Sentry.init({
        dsn: 'https://<EMAIL>/4507566645641296',
        integrations: [
          Sentry.browserTracingIntegration(),
          Sentry.replayIntegration({
            maskAllText: false,
            maskAllInputs: false,
            blockAllMedia: false,
          }),
        ],
        tracesSampleRate: 1.0,
        // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
        tracePropagationTargets: [
          /^https:\/\/wyden-dev.wyden\.io/,
          /^https:\/\/wyden-qa.wyden\.io/,
          /^https:\/\/wyden-uat.wyden\.io/,
          /^https:\/\/wyden-demo.wyden\.io/,
        ],
        replaysSessionSampleRate: 0.1,
        replaysOnErrorSampleRate: 1.0,
      });
    }
  } catch (error) {
    logger.error('Error initializing remote services:', error);
  }

  return (
    <PostHogProvider apiKey={'phc_faacHsIe38T3ZInToCzOgQkRR87fYHoFqMwNkkrIrs'} options={options}>
      {props.children}
    </PostHogProvider>
  );
};
const domainPatterns = [
  /^https:\/\/wyden-dev.wyden\.io/,
  /^https:\/\/wyden-qa.wyden\.io/,
  /^https:\/\/wyden-uat.wyden\.io/,
  /^https:\/\/wyden-demo.wyden\.io/,
];
const testDomainPatterns = () => {
  const currentDomain = window.location.href;

  for (const domainPattern of domainPatterns) {
    if (domainPattern.test(currentDomain)) {
      return true;
    }
  }
  return false;
};

export const AnalyticsProviders = (props: AnalyticsProvidersProps) => {
  return (
    <>
      {testDomainPatterns() ? (
        <AnalyticsInitializer>{props.children}</AnalyticsInitializer>
      ) : (
        props.children
      )}
    </>
  );
};
