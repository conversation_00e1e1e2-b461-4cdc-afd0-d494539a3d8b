import { describe, expect, it } from 'vitest';
import { convertInstrumentIdToAssetClass, convertInstrumentIdToInstrument } from '../converters';

describe('converters', () => {
  it('convertInstrumentIdToSymbol real cases', () => {
    expect(convertInstrumentIdToInstrument('foo@bar@baz')).toBe('foo@baz');
    expect(convertInstrumentIdToInstrument('FOO@BAR@BAZ')).toBe('FOO@BAZ');
  });
  it('convertInstrumentIdToSymbol edge cases', () => {
    expect(convertInstrumentIdToInstrument(null)).toBe(null);
    expect(convertInstrumentIdToInstrument(undefined)).toBe(undefined);
    expect(convertInstrumentIdToInstrument('')).toBe('');
    expect(convertInstrumentIdToInstrument('foo')).toBe('foo');
    expect(convertInstrumentIdToInstrument('foo@bar')).toBe('foo@bar');
    expect(convertInstrumentIdToInstrument('foo@bar@')).toBe('foo@bar@');
    expect(convertInstrumentIdToInstrument('foo@bar@baz@')).toBe('foo@bar@baz@');
    expect(convertInstrumentIdToInstrument('foo@bar@baz@bye')).toBe('foo@bar@baz@bye');
  });
  it('convertInstrumentIdToAssetClass real cases', () => {
    expect(convertInstrumentIdToAssetClass('foo@bar@baz')).toBe('bar');
    expect(convertInstrumentIdToAssetClass('FOO@BAR@BAZ')).toBe('BAR');
    expect(convertInstrumentIdToAssetClass('FOO@FOREX@BAZ')).toBe('SPOT'); //AC-2095
  });
  it('convertInstrumentIdToAssetClass edge cases', () => {
    expect(convertInstrumentIdToAssetClass(null)).toBe(null);
    expect(convertInstrumentIdToAssetClass(undefined)).toBe(undefined);
    expect(convertInstrumentIdToAssetClass('')).toBe('');
    expect(convertInstrumentIdToAssetClass('foo')).toBe('foo');
    expect(convertInstrumentIdToAssetClass('foo@bar')).toBe('foo@bar');
    expect(convertInstrumentIdToAssetClass('foo@bar@')).toBe('foo@bar@');
    expect(convertInstrumentIdToAssetClass('foo@bar@baz@')).toBe('foo@bar@baz@');
    expect(convertInstrumentIdToAssetClass('foo@bar@baz@bye')).toBe('foo@bar@baz@bye');
  });
});
