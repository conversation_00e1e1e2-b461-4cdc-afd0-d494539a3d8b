#appender=com.p6spy.engine.spy.appender.Slf4JLogger
#logMessageFormat=com.p6spy.engine.spy.appender.CustomLineFormat
#customLogMessageFormat=Time: %(executionTime)ms | SQL: %(sqlSingleLine)

# Basic configuration
appender=com.p6spy.engine.spy.appender.Slf4JLogger
logMessageFormat=io.wyden.booking.reporting.utils.P6SpyMessageFormatter
customLogMessageFormat=Time: %(executionTime)ms | SQL: %(sql)

# Enable multiline formatting
#driverlist=org.postgresql.Driver
#dateformat=yyyy-MM-dd HH:mm:ss
#logfile=spy.log
#append=true
#excludecategories=info,debug,batch
#excludecategories=info,debug,result
#useprefix=false
