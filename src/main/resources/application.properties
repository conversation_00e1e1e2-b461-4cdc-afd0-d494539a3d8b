spring.application.name = fix-actor
spring.profiles.active = prod

server.port = 8090

fix.socketConnect.host = 0.0.0.0
fix.socketConnect.port = 9876
fix.userName=db92891c-38fa-4be3-9c86-cad33b0a29f7
fix.password=!1DhC^*2n5nnGl#pYrMdTdg1*70@@4Lq
fix.targetCompId = ATFIXSERVER

springdoc.swagger-ui.enabled=false
springdoc.packagesToScan=io.wyden.test.fixactor.controller
springdoc.pathsToMatch=/v3, /**

management.endpoints.web.exposure.include=health,prometheus,metrics,loggers
management.endpoint.health.group.liveness.include=livenessState
management.endpoint.health.group.readiness.include=readinessState,fixSocketConnection
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.metrics.tags.wyden_service=fix-actor
