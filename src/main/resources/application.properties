spring.application.name=booking-reporting

server.port=8045

rabbitmq.username=booking-reporting
rabbitmq.password=password
rabbitmq.virtualHost=/
rabbitmq.host=localhost
# default RabbitMQ port for non-TLS connections: 5672, default port for TLS connections: 5671
rabbitmq.port=5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls=
rabbitmq.producer-count=5

spring.datasource.url=**************************************************
spring.datasource.username=booking_reporting
spring.datasource.password=password

spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.jdbc.time_zone=UTC

spring.flyway.enabled=true
spring.flyway.locations=classpath:psql/migration/schema,classpath:psql/migration/data
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=1

management.endpoints.web.exposure.include=health,prometheus,metrics,loggers
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.endpoint.health.group.liveness.include=livenessState,rabbit,clusterRunning,diskSpace,hazelcast
management.endpoint.health.group.readiness.include=readinessState
management.metrics.tags.wyden_service=booking-reporting

hz.addressList=localhost
hz.outboundPortDefinition=

## disable p6spy logging by default, enable in dev profile
decorator.datasource.p6spy.enable-logging=false

access.gateway.host=http://localhost:8089
#license.manager.host=https://license.wyden.io
#reference.data.url=http://localhost:8098
tracing.collector.endpoint=http://localhost:4317

# BookingCompleted gap recovery configuration
booking.snapshotter.host=http://localhost:8043
booking.completed.recovery.batch-size=100
booking.completed.recovery.onstartup=false
booking.completed.onerror.break=false

# PositionSnapshot gap recovery configuration
booking.pnl.host=http://localhost:8080
position.snapshot.recovery.batch-size=100
position.snapshot.recovery.onstartup=false
position.snapshot.onerror.break=false

rabbitmq.booking-reporting-queue.booking-completed.name=booking.booking-reporting-queue.BOOKING-COMPLETED
rabbitmq.booking-reporting-queue.position-snapshot.name=booking.booking-reporting-queue.v2.POSITION-SNAPSHOT

## booking-reporting-specific configuration
booking-reporting.query-limits.portfolio=100000
booking-reporting.default-currency=USD
booking-reporting.stablecoins=USDT, USD; \
    USDC, USD; \
    TUSD, USD; \
    BUSD, USD; \
    USDD, USD; \
    USDP, USD; \
    GUSD, USD;

## booking-engine has the same emitter, only one should be enabled at a time
booking-engine.publisher.transaction-created.enabled=false
## booking-engine has the same emitter, only one should be enabled at a time
booking-engine.publisher.position-snapshot.enabled=false
