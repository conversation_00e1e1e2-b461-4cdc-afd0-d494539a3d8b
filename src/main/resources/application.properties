spring.application.name=websocket-server

server.port=8400

management.endpoint.health.enabled=true
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.metrics.tags.wyden_service=websocket-server
management.endpoint.health.group.liveness.include=livenessState,diskSpace,rabbit
management.endpoint.health.group.readiness.include=readinessState

# comma-separated list of hz member hosts
hz.addressList=localhost
hz.outboundPortDefinition=

rabbitmq.username = websocket-server
rabbitmq.password = password
rabbitmq.virtualHost = /
rabbitmq.host = localhost
# default RabbitMQ port for non-TLS connections: 5672, default port for TLS connections: 5671
rabbitmq.port = 5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls =

rabbitmq.target-registry-state-changed-queue = websocket-server-queue.target-registry.%s.TARGET-STATE-CHANGED
rabbitmq.booking-engine-position-updated-queue = websocket-server-queue.booking.%s.POSITION-UPDATED

ws.authentication.timeout = 5s
nonce.validation.window = 30s

spring.cloud.vault.host=localhost
spring.cloud.vault.token=my-token
spring.cloud.vault.scheme=http
spring.cloud.vault.kv.application-name=websocket-server
spring.cloud.vault.reactive.enabled=false

access.gateway.host=http://localhost:8089
target.registry.host=http://localhost:8066
target.registry.target.states.url=${target.registry.host}/targets/states
booking.engine.host=http://localhost:8100
booking.engine.positions.search.url=${booking.engine.host}/positions/search

server.error.include-stacktrace=never

websocket.query-limit=100
