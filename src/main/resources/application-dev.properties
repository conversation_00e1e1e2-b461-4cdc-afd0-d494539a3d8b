# logging
logging.level.root=info
logging.level.io.wyden=debug
logging.level.io.wyden.cloudutils.telemetry=warn

#logging.level.org.hibernate.SQL=debug
#logging.level.org.hibernate.orm.jdbc.bind=trace
#logging.level.org.hibernate.stat=DEBUG
#logging.level.org.springframework.orm.jpa=DEBUG
#logging.level.org.springframework.transaction=DEBUG
#logging.level.org.springframework.jdbc.core=DEBUG

# P6Spy configuration
decorator.datasource.p6spy.enable-logging=true
decorator.datasource.p6spy.multiline=true
decorator.datasource.p6spy.logging=slf4j
