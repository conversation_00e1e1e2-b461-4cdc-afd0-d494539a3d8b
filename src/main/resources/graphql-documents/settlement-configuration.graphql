query ($tzId: String) {
    settlementConfiguration(tzId: $tzId) {
        accountId
        config {
            automationEnabled
            schedule {
                day
                time
            }
            scheduleTZid
            daysExcluded
            assetToWalletMap {
                asset
                wallet
            }
            directionPriority
            legPriority
            treasuryManagementAutomationEnabled
            treasuryThresholds {
                currency
                minimum
                target
                maximum
            }
        }
    }
}