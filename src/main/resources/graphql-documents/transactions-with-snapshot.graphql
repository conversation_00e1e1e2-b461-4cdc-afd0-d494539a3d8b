subscription ($search: TransactionSearchInput!) {
    transactionsWithSnapshot(search: $search) {
        __typename
        ... on ClientCashTrade {
            uuid
            updatedAt
            executionId
            venueExecutionId
            fee
            feeCurrency
            description
            quantity
            price
            currency
            intOrderId
            extOrderId
            orderId
            baseCurrency
            portfolioId
            portfolioName
            counterPortfolioId
            counterPortfolioName
            dateTime
            settled
            settledDateTime
            rootExecution {
                orderId
                executionId
            }
        }
        ... on StreetCashTrade {
            uuid
            updatedAt
            executionId
            venueExecutionId
            fee
            feeCurrency
            description
            quantity
            price
            currency
            intOrderId
            extOrderId
            orderId
            baseCurrency
            portfolioId
            portfolioName
            venueAccount
            dateTime
            settled
            settledDateTime
            rootExecution {
                orderId
                executionId
            }
        }
        ... on ClientAssetTrade {
            uuid
            updatedAt
            executionId
            venueExecutionId
            fee
            feeCurrency
            description
            quantity
            price
            currency
            intOrderId
            extOrderId
            orderId
            portfolioId
            portfolioName
            counterPortfolioId
            counterPortfolioName
            dateTime
            settled
            settledDateTime
            rootExecution {
                orderId
                executionId
            }
        }
        ... on StreetAssetTrade {
            uuid
            updatedAt
            executionId
            venueExecutionId
            fee
            feeCurrency
            description
            quantity
            price
            currency
            intOrderId
            extOrderId
            orderId
            portfolioId
            portfolioName
            venueAccount
            dateTime
            settled
            settledDateTime
#            rootExecution {
#                orderId
#                executionId
#            }
        }
        ... on Deposit {
            dateTime
            uuid
            updatedAt
            executionId
            venueExecutionId
            description
            quantity
            currency
            portfolioId
            portfolioName
            account
            accountName
            settled
            settledDateTime
            feeAccountId
            feeAccountName
            feePortfolioId
            feePortfolioName
        }
        ... on Withdrawal {
            dateTime
            uuid
            updatedAt
            executionId
            venueExecutionId
            description
            quantity
            currency
            portfolioId
            portfolioName
            account
            accountName
            settled
            settledDateTime
            feeAccountId
            feeAccountName
            feePortfolioId
            feePortfolioName
        }
        ... on AccountCashTransfer {
            dateTime
            uuid
            updatedAt
            executionId
            venueExecutionId
            description
            quantity
            currency
            sourceAccountId
            sourceAccountName
            targetAccountId
            targetAccountName
            settled
            settledDateTime
            feeAccountId
            feeAccountName
            feePortfolioId
            feePortfolioName
        }
        ... on PortfolioCashTransfer {
            dateTime
            uuid
            updatedAt
            executionId
            venueExecutionId
            description
            quantity
            currency
            sourcePortfolioId
            sourcePortfolioName
            targetPortfolioId
            targetPortfolioName
            settled
            settledDateTime
            feePortfolioId
            feePortfolioName
        }
        ... on Settlement {
            dateTime
            uuid
            updatedAt
            description
            settledTransactionIds
        }
        ... on Fee {
            dateTime
            uuid
            updatedAt
            description
            executionId
            venueExecutionId
            quantity
            currency
            portfolioId
            account
            settled
            settledDateTime
            feeOrderId: orderId
            parentOrderId
            underlyingExecutionId
            rootExecution {
                orderId
                executionId
            }
        }
    }
}
