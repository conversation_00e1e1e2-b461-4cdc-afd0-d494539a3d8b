-- Create consolidated event tracking table for all event types
CREATE TABLE event_tracking (
    id                      BIGSERIAL PRIMARY KEY,
    event_type              VARCHAR(50) NOT NULL UNIQUE,
    last_processed_sequence BIGINT NOT NULL DEFAULT 0,
    updated_at              TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- <PERSON><PERSON> indexes for faster lookups
CREATE INDEX idx_event_tracking_event_type ON event_tracking (event_type);
CREATE INDEX idx_event_tracking_sequence ON event_tracking (last_processed_sequence);

-- Insert initial tracking records for both event types
INSERT INTO event_tracking (event_type, last_processed_sequence) VALUES ('booking_completed', 0);
INSERT INTO event_tracking (event_type, last_processed_sequence) VALUES ('position_snapshot', 0);