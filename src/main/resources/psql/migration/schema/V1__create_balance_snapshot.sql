CREATE TABLE balance (
                                  id BIGSERIAL PRIMARY KEY,

                                  symbol TEXT NOT NULL,
                                  account_id TEXT,
                                  portfolio_id TEXT,

                                  quantity NUMERIC,
                                  pending_quantity NUMERIC,
                                  available_for_trading_quantity NUMERIC,
                                  available_for_withdrawal_quantity NUMERIC,
                                  settled_quantity NUMERIC,
                                  unsettled_quantity NUMERIC,

                                  currency TEXT,
                                  last_applied_ledger_entry_id TEXT,
                                  sequence_number BIGINT NOT NULL,

                                  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                                  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                                  created_by TEXT NOT NULL,
                                  updated_by TEXT NOT NULL,

                                  CONSTRAINT unique_symbol_account_portfolio UNIQUE (symbol, account_id, portfolio_id)
);