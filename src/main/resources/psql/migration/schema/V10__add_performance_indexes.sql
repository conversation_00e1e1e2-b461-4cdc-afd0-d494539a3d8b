-- Indexes for the 'transaction' table
CREATE INDEX IF NOT EXISTS idx_transaction_portfolio_id ON transaction (portfolio_id);
CREATE INDEX IF NOT EXISTS idx_transaction_account_id ON transaction (account_id);
CREATE INDEX IF NOT EXISTS idx_transaction_order_id ON transaction (order_id);
CREATE INDEX IF NOT EXISTS idx_transaction_uuid ON transaction (uuid);
CREATE INDEX IF NOT EXISTS idx_transaction_type ON transaction (transaction_type);
CREATE INDEX IF NOT EXISTS idx_transaction_date_time ON transaction (date_time);
CREATE INDEX IF NOT EXISTS idx_transaction_sequence_number ON transaction (sequence_number);

-- Indexes for the 'ledger_entry' table
CREATE INDEX IF NOT EXISTS idx_ledger_entry_portfolio_id ON ledger_entry (portfolio_id);
CREATE INDEX IF NOT EXISTS idx_ledger_entry_account_id ON ledger_entry (account_id);
CREATE INDEX IF NOT EXISTS idx_ledger_entry_transaction_id ON ledger_entry (transaction_id);
CREATE INDEX IF NOT EXISTS idx_ledger_entry_reservation_ref ON ledger_entry (reservation_ref);
CREATE INDEX IF NOT EXISTS idx_ledger_entry_symbol ON ledger_entry (symbol);
CREATE INDEX IF NOT EXISTS idx_ledger_entry_date_time ON ledger_entry (date_time);
CREATE INDEX IF NOT EXISTS idx_ledger_entry_sequence_number ON ledger_entry (sequence_number);

-- Indexes for the 'reservation' table
CREATE INDEX IF NOT EXISTS idx_reservation_portfolio_id ON reservation (portfolio_id);
CREATE INDEX IF NOT EXISTS idx_reservation_account_id ON reservation (account_id);
CREATE INDEX IF NOT EXISTS idx_reservation_reservation_ref ON reservation (reservation_ref);
CREATE INDEX IF NOT EXISTS idx_reservation_uuid ON reservation (uuid);
CREATE INDEX IF NOT EXISTS idx_reservation_date_time ON reservation (date_time);
CREATE INDEX IF NOT EXISTS idx_reservation_sequence_number ON reservation (sequence_number);

-- Indexes for 'balance' and 'position' tables (sequence_number was missing)
CREATE INDEX IF NOT EXISTS idx_balance_sequence_number ON balance (sequence_number);
CREATE INDEX IF NOT EXISTS idx_position_sequence_number ON position (sequence_number);
-- Indexes for (portfolioId, symbol) and (accountId, symbol) already exist on balance and position
