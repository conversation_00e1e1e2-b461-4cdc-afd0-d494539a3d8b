CREATE TABLE position
(
    -- Audit fields
    id                           BIGSERIAL PRIMARY KEY,
    created_at                   TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at                   TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    created_by                   TEXT                     NOT NULL,
    updated_by                   TEXT                     NOT NULL,
    version                      BIGINT,

    -- Position identifiers (inherited from PositionIdentifiers)
    portfolio_id                 TEXT,
    account_id                   TEXT,
    portfolio_type               TEXT CHECK ( portfolio_type IN ('VOSTRO', 'NOSTRO')),
    account_type                 TEXT CHECK ( account_type IN ('WALLET', 'EXCHANGE', 'CUSTODY', 'CLOB')),
    account_wallet_type          TEXT CHECK ( account_wallet_type IN ('VOSTRO', 'NOSTRO')),

    -- Instrument fields
    symbol                       TEXT                     NOT NULL,
    currency                     TEXT,
    portfolio_currency           TEXT,
    account_currency             TEXT,
    inverse_contract             BOOLEAN,
    contract_size                NUMERIC,

    -- Core position fields
    quantity                     NUMERIC,
    notional_quantity            NUMERIC,

    -- Cost fields
    net_cost                     NUMERIC,
    net_cost_sc                  NUMERIC,
    gross_cost                   NUMERIC,
    gross_cost_sc                NUMERIC,

    -- Realized P&L fields
    net_realized_pnl             NUMERIC,
    net_realized_pnl_sc          NUMERIC,
    gross_realized_pnl           NUMERIC,
    gross_realized_pnl_sc        NUMERIC,

    -- Average price fields
    net_average_price            NUMERIC,
    gross_average_price          NUMERIC,

    -- Market value fields
    market_value                 NUMERIC,
    market_value_sc              NUMERIC,

    -- Unrealized P&L fields
    net_unrealized_pnl           NUMERIC,
    net_unrealized_pnl_sc        NUMERIC,
    gross_unrealized_pnl         NUMERIC,
    gross_unrealized_pnl_sc      NUMERIC,

    -- Tracking fields
    last_applied_ledger_entry_id BIGINT,
    sequence_number              BIGINT                   NOT NULL,

    CONSTRAINT position_unique_symbol_account_portfolio UNIQUE (symbol, account_id, portfolio_id)
);
