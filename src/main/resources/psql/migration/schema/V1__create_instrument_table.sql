CREATE TABLE instrument
(
    id               INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    created_at       TIMESTAMP(6),
    symbol           VARCHAR(255)   NOT NULL,
    currency         VARCHAR(255)   NOT NULL,
    inverse_contract BOOLEAN        NOT NULL,
    contract_size    DECIMAL(19, 8) NOT NULL,
    instrument_type  VARCHAR(50)    NOT NULL,
    CONSTRAINT unique_symbol_type UNIQUE (symbol, instrument_type)
);

COMMENT ON TABLE instrument IS 'Table for storing instrument data';
COMMENT ON COLUMN instrument.id IS 'Unique identifier for the instrument';
COMMENT ON COLUMN instrument.symbol IS 'Symbol of the instrument';
COMMENT ON COLUMN instrument.currency IS 'Currency of the instrument';
COMMENT ON COLUMN instrument.inverse_contract IS 'Flag indicating if the instrument is an inverse contract';
COMMENT ON COLUMN instrument.contract_size IS 'Contract size of the instrument';
COMMENT ON COLUMN instrument.instrument_type IS 'Type of the instrument (ASSET, CURRENCY, etc.)';

-- Add indexes on instrument_type to optimize queries for specific types
CREATE INDEX idx_instrument_type ON instrument (instrument_type);
