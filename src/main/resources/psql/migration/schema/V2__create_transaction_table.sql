create table transaction
(
    id                      bigserial primary key,
    created_at              timestamp(6),
    uuid                    text,
    reservation_ref         text,
    sequence_number         bigint  not null,
    date_time               timestamp(6) with time zone,
    quantity                numeric,
    leaves_quantity         numeric,
    price                   numeric,

    currency                text,
    base_currency           text,
    asset                   text,

    portfolio_id            text,
    counter_portfolio_id    text,
    account_id              text,
    source_portfolio_id     text,
    target_portfolio_id     text,
    source_account_id       text,
    target_account_id       text,
    fee_portfolio_id        text,
    fee_account_id          text,

    execution_id            text,
    venue_execution_id      text,
    int_order_id            text,
    ext_order_id            text,
    order_id                text,
    parent_order_id         text,
    root_order_id           text,
    underlying_execution_id text,
    root_execution_id       text,
    client_root_order_id    text,

    description             text,
    fees                    jsonb,
    is_live                 boolean not null,
    is_settled              boolean not null,
    settlement_date_time    timestamp(6) with time zone,
    settlement_id           text,
    settlement_type         text check (settlement_type in ('INSTANT_SETTLEMENT', 'DEFERRED_SETTLEMENT')),
    transaction_type        text,
    exec_type               text check (exec_type in
                                        ('PENDING_NEW', 'NEW', 'PARTIAL_FILL', 'FILL', 'PENDING_CANCEL', 'CANCELED', 'REJECTED', 'EXPIRED'))
);
