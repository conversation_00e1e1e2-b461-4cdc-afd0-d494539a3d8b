-- Remove the current constraint
ALTER TABLE position DROP CONSTRAINT position_unique_symbol_account_portfolio;

-- Add partial unique indexes
CREATE UNIQUE INDEX position_unique_symbol_portfolio
    ON position (symbol, portfolio_id)
    WHERE portfolio_id IS NOT NULL AND account_id IS NULL;

CREATE UNIQUE INDEX position_unique_symbol_account
    ON position (symbol, account_id)
    WHERE account_id IS NOT NULL AND portfolio_id IS NULL;

-- Remove the current constraint
ALTER TABLE balance DROP CONSTRAINT unique_symbol_account_portfolio;

-- Add partial unique indexes
CREATE UNIQUE INDEX balance_unique_symbol_portfolio
    ON balance (symbol, portfolio_id)
    WHERE portfolio_id IS NOT NULL AND account_id IS NULL;

CREATE UNIQUE INDEX balance_unique_symbol_account
    ON balance (symbol, account_id)
    WHERE account_id IS NOT NULL AND portfolio_id IS NULL;
