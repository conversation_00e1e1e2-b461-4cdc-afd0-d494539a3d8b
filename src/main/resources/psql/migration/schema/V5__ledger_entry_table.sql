create table ledger_entry
(
    id                  bigserial primary key,
    created_at          timestamp(6),
    updated_at          timestamp(6),
    version             bigint,

    quantity            numeric,
    price               numeric,
    sequence_number     bigint  not null,
    ledger_entry_type   text,
    symbol              text,
    portfolio_id        text,
    account_id          text,
    reservation_ref     text,
    transaction_id      text,
    date_time           timestamp(6) with time zone,
    balance_before      numeric,
    balance_after       numeric,
    is_settled          boolean not null,
    fees                jsonb,
    portfolio_type      text check ( portfolio_type in ('VOSTRO', 'NOSTRO')),
    account_type        text check ( account_type in ('WALLET', 'EXCHANGE', 'CUSTODY', 'CLOB')),
    account_wallet_type text check ( account_wallet_type in ('VOSTRO', 'NOSTRO'))
);
