create table reservation
(
    id                         bigserial primary key,
    created_at                 timestamp(6),
    uuid                       text,
    reservation_ref            text,
    sequence_number            bigint,
    date_time                  timestamp(6) with time zone,
    quantity                   numeric,
    price                      numeric,
    stop_price                 numeric,
    currency                   text,
    base_currency              text,
    asset                      text,
    portfolio_id               text,
    counter_portfolio_id       text,
    source_portfolio_id        text,
    target_portfolio_id        text,
    fee_portfolio_id           text,
    account_id                 text,
    source_account_id          text,
    target_account_id          text,
    fee_account_id             text,
    transaction_type           text,
    fees                       jsonb,
    portfolio_type             text check ( portfolio_type in ('VOSTRO', 'NOSTRO')),
    counter_portfolio_type     text check ( counter_portfolio_type in ('VOSTRO', 'NOSTRO')),
    source_portfolio_type      text check ( source_portfolio_type in ('VOSTRO', 'NOSTRO')),
    target_portfolio_type      text check ( target_portfolio_type in ('VOSTRO', 'NOSTRO')),
    fee_portfolio_type         text check ( fee_portfolio_type in ('VOSTRO', 'NOSTRO')),
    account_type               text check ( account_type in ('<PERSON><PERSON><PERSON>', 'EXCHANGE', 'CUSTODY', 'CLOB')),
    source_account_type        text check ( source_account_type in ('WALLET', 'EXCHANGE', 'CUSTODY', 'CLOB')),
    target_account_type        text check ( target_account_type in ('WALLET', 'EXCHANGE', 'CUSTODY', 'CLOB')),
    fee_account_type           text check ( fee_account_type in ('WALLET', 'EXCHANGE', 'CUSTODY', 'CLOB')),
    account_wallet_type        text check ( account_wallet_type in ('VOSTRO', 'NOSTRO')),
    source_account_wallet_type text check ( source_account_wallet_type in ('VOSTRO', 'NOSTRO')),
    target_account_wallet_type text check ( target_account_wallet_type in ('VOSTRO', 'NOSTRO')),
    fee_account_wallet_type    text check ( fee_account_wallet_type in ('VOSTRO', 'NOSTRO'))
);
