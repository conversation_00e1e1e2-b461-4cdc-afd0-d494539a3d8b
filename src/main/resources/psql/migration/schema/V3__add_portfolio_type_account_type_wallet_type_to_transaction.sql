alter table transaction
    add column portfolio_type text check ( portfolio_type in ('<PERSON>OSTR<PERSON>', 'NOSTRO'));

alter table transaction
    add column counter_portfolio_type text check ( counter_portfolio_type in ('VOSTRO', 'NOSTRO'));

alter table transaction
    add column source_portfolio_type text check ( source_portfolio_type in ('V<PERSON><PERSON><PERSON>', 'NOSTRO'));

alter table transaction
    add column target_portfolio_type text check ( target_portfolio_type in ('VOSTRO', 'NOSTRO'));

alter table transaction
    add column fee_portfolio_type text check ( fee_portfolio_type in ('VOSTRO', 'NOSTRO'));

alter table transaction
    add column account_type text check ( account_type in ('WALLET', 'EXCHANGE', 'CUSTODY', 'CLOB'));

alter table transaction
    add column source_account_type text check ( source_account_type in ('<PERSON>LL<PERSON>', 'EXCHANGE', 'CUSTODY', 'CLOB'));

alter table transaction
    add column target_account_type text check ( target_account_type in ('WALL<PERSON>', 'EXCHANGE', 'CUSTODY', 'CLOB'));

alter table transaction
    add column fee_account_type text check ( fee_account_type in ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CUSTODY', 'C<PERSON>OB'));

alter table transaction
    add column account_wallet_type text check ( account_wallet_type in ('VOSTRO', 'NOSTRO'));

alter table transaction
    add column source_account_wallet_type text check ( source_account_wallet_type in ('VOSTRO', 'NOSTRO'));

alter table transaction
    add column target_account_wallet_type text check ( target_account_wallet_type in ('VOSTRO', 'NOSTRO'));

alter table transaction
    add column fee_account_wallet_type text check ( fee_account_wallet_type in ('VOSTRO', 'NOSTRO'));
