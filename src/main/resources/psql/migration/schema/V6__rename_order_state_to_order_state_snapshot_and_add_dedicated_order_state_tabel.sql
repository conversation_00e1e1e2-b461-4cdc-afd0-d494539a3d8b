alter table order_state
    RENAME TO order_state_snapshot;

create table order_state
(
    id                       text primary key,
    order_id                 text,
    client_id                text,
    cl_order_id              text,
    orig_cl_order_id         text,
    root_order_id            text                 default null,
    orig_order_id            text                 default null,
    portfolio_id             text                 default null,
    counter_portfolio_id     text                 default null,
    order_status             text,
    order_qty                text,
    limit_price              text,
    stop_price               text,
    tif                      text,
    filled_qty               text,
    remaining_qty            text,
    last_qty                 text,
    avg_price                text,
    last_price               text,
    reason                   text,
    side                     text,
    instrument_id            text,
    target                   text,
    venue_timestamp          timestamptz          default null,
    created_at               timestamptz not null default now(),
    updated_at               timestamptz          default null,
    last_request_result      text,
    sequence_number          bigint,
    expire_time              timestamptz          default null,
    order_category           text,
    parent_order_id          text,
    order_type               text,
    symbol                   text,
    instrument_type          text,
    venue_accounts           text[],
    underlying_venue_account text,
    currency                 text                 default null
);

create index order_state_order_id on order_state (order_id);
create index order_state_root_order_id on order_state (root_order_id);
