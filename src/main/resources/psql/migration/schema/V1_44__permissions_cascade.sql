ALTER TABLE clients_permissions
    DROP CONSTRAINT fk_client,
    ADD CONSTRAINT fk_client FOREIGN KEY(client_id)
        REFERENCES wyden_client(id) ON DELETE CASCADE;

ALTER TABLE groups_permissions
    DROP CONSTRAINT fk_group,
    ADD CONSTRAINT fk_group FOREIGN KEY(group_id)
        REFERENCES wyden_group(id) ON DELETE CASCADE;

ALTER TABLE clients_permissions
    DROP CONSTRAINT fk_clients_permission,
    ADD CONSTRAINT fk_clients_permission FOREIGN KEY(permission_id)
        REFERENCES wyden_permission(id) ON DELETE CASCADE;

ALTER TABLE groups_permissions
    DROP CONSTRAINT fk_groups_permission,
    ADD CONSTRAINT fk_groups_permission FOREIGN KEY(permission_id)
        REFERENCES wyden_permission(id) ON DELETE CASCADE;
