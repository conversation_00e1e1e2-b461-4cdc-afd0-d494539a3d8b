create table order_state
(
    order_id  text primary key,
    client_id  text,
    cl_order_id  text,
    orig_cl_order_id  text,
    portfolio_id  text,
    order_status  text,
    order_qty  text,
    limit_price  text,
    stop_price  text,
    tif text,
    filled_qty text,
    remaining_qty text,
    last_qty text,
    avg_price text,
    last_price text,
    reason text,
    side text,
    instrument_id text,
    target text,
    venue_timestamp timestamptz default null,
    created_at timestamptz not null default now(),
    updated_at timestamptz default null,
    last_request_result text,
    sequence_number bigint,
    expire_time timestamptz default null,
    order_category text,
    parent_order_id text,
    order_type text,
    symbol text,
    instrument_type text,
    venue_accounts text[],
    underlying_venue_account text
);
