/*
 * Copyright 2019 <PERSON><PERSON><PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package exchange.core2.core.orderbook;

import exchange.core2.collections.art.LongAdaptiveRadixTreeMap;
import exchange.core2.collections.art.LongObjConsumer;
import exchange.core2.collections.objpool.ObjectsPool;
import exchange.core2.core.common.CoreSymbolSpecification;
import exchange.core2.core.common.IOrder;
import exchange.core2.core.common.L2MarketData;
import exchange.core2.core.common.MatcherEventType;
import exchange.core2.core.common.MatcherTradeEvent;
import exchange.core2.core.common.Order;
import exchange.core2.core.common.OrderAction;
import exchange.core2.core.common.cmd.CommandResultCode;
import exchange.core2.core.common.cmd.OrderCommand;
import exchange.core2.core.common.cmd.OrderCommandType;
import exchange.core2.core.orderbook.instantmatch.InstantMatchStrategyFactory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import net.openhft.chronicle.bytes.BytesIn;
import net.openhft.chronicle.bytes.BytesOut;
import net.openhft.chronicle.bytes.WriteBytesMarshallable;
import org.agrona.collections.Long2ObjectHashMap;
import org.agrona.collections.MutableInteger;
import org.agrona.collections.MutableLong;
import org.eclipse.collections.impl.map.mutable.primitive.LongObjectHashMap;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

@Slf4j
public final class OrderBookDirectImpl implements IOrderBook {
    private MatchingContext matchingContext;

    public OrderBookDirectImpl(final CoreSymbolSpecification symbolSpec,
                               final ObjectsPool objectsPool,
                               final OrderBookEventsHelper eventsHelper) {

        LongAdaptiveRadixTreeMap<Bucket> askPriceBuckets = new LongAdaptiveRadixTreeMap<>(objectsPool);
        LongAdaptiveRadixTreeMap<Bucket> bidPriceBuckets = new LongAdaptiveRadixTreeMap<>(objectsPool);
        LongAdaptiveRadixTreeMap<DirectOrder> orderIdIndex = new LongAdaptiveRadixTreeMap<>(objectsPool);

        this.matchingContext = new MatchingContext(eventsHelper, orderIdIndex, objectsPool, askPriceBuckets, bidPriceBuckets, symbolSpec);
    }

    public OrderBookDirectImpl(final BytesIn bytes,
                               final ObjectsPool objectsPool,
                               final OrderBookEventsHelper eventsHelper) {

        CoreSymbolSpecification symbolSpec = new CoreSymbolSpecification(bytes);
        LongAdaptiveRadixTreeMap<Bucket> askPriceBuckets = new LongAdaptiveRadixTreeMap<>(objectsPool);
        LongAdaptiveRadixTreeMap<Bucket> bidPriceBuckets = new LongAdaptiveRadixTreeMap<>(objectsPool);
        LongAdaptiveRadixTreeMap<DirectOrder> orderIdIndex = new LongAdaptiveRadixTreeMap<>(objectsPool);

        this.matchingContext = new MatchingContext(eventsHelper, orderIdIndex, objectsPool, askPriceBuckets, bidPriceBuckets, symbolSpec);

        final int size = bytes.readInt();
        for (int i = 0; i < size; i++) {
            DirectOrder order = new DirectOrder(bytes);
            insertOrder(order, null);
            orderIdIndex.put(order.orderId, order);
        }
    }

    @Override
    public void newOrder(final OrderCommand cmd) {

        switch (cmd.orderType) {
            case GTC:
                newOrderPlaceGtc(cmd);
                break;
            case IOC:
                newOrderMatchIoc(cmd);
                break;
            case FOK:
                newOrderMatchFok(cmd);
                break;
            default:
                log.warn("Unsupported order type: {}", cmd);
                matchingContext.eventsHelper.attachRejectEvent(cmd, cmd.size);
        }
    }

    private void newOrderPlaceGtc(final OrderCommand cmd) {

        // check if order is marketable there are matching orders
        final long filledSize = tryMatchInstantly(cmd, cmd);
        if (filledSize == cmd.size) {
            // completed before being placed - can just return
            return;
        }

        final long orderId = cmd.orderId;
        if (matchingContext.orderIdIndex.get(orderId) != null) { // containsKey for hashtable
            // duplicate order id - can match, but can not place
            matchingContext.eventsHelper.attachRejectEvent(cmd, cmd.size - filledSize);
            log.warn("duplicate order id: {}", cmd);
            return;
        }

        final long price = cmd.price;

        final long size;
        if (cmd.maxAmount == 0) {
            size = cmd.size;
        } else {
            long remainingAmount = cmd.maxAmount;
            long takenAmount = 0;
            MatcherTradeEvent matcherTradeEvent = cmd.matcherEvent;
            while (matcherTradeEvent != null) {
                if (matcherTradeEvent.eventType == MatcherEventType.TRADE) {
                    remainingAmount -= matcherTradeEvent.price * matcherTradeEvent.size;
                    takenAmount += matcherTradeEvent.price * matcherTradeEvent.size;
                }
                matcherTradeEvent = matcherTradeEvent.nextEvent;
            }
            size = Math.min(cmd.size, remainingAmount / price + filledSize);
            if (size < cmd.size) {
                matchingContext.eventsHelper.attachRejectEvent(cmd, cmd.size - size);
            }
            cmd.acceptedAmount = takenAmount + (size - filledSize) * price;
            cmd.size = size;
            if (remainingAmount < cmd.price) {
                return;
            }
        }

        // normally placing regular GTC order
        final DirectOrder orderRecord = matchingContext.objectsPool.get(ObjectsPool.DIRECT_ORDER, (Supplier<DirectOrder>) DirectOrder::new);

        orderRecord.orderId = orderId;
        orderRecord.price = price;
        orderRecord.size = size;
        orderRecord.action = cmd.action;
        orderRecord.uid = cmd.uid;
        orderRecord.timestamp = cmd.timestamp;
        orderRecord.orderUuidMostSigBits = cmd.orderUuidMostSigBits;
        orderRecord.orderUuidLeastSigBits = cmd.orderUuidLeastSigBits;
        orderRecord.filled = filledSize;
        orderRecord.maxAmount = cmd.maxAmount;
        orderRecord.acceptedAmount = cmd.acceptedAmount;

        matchingContext.orderIdIndex.put(orderId, orderRecord);
        insertOrder(orderRecord, null);
    }

    private void newOrderMatchIoc(final OrderCommand cmd) {

        final long filledSize = tryMatchInstantly(cmd, cmd);
        final long rejectedSize = cmd.size - filledSize;

        if (rejectedSize != 0) {
            // was not matched completely - send reject for not-completed IoC order
            matchingContext.eventsHelper.attachRejectEvent(cmd, rejectedSize);
        }
        cmd.size = filledSize;
    }

    private void newOrderMatchFok(final OrderCommand cmd) {

        if (canFullyMatch(cmd)) {
            tryMatchInstantly(cmd, cmd);
        } else {
            // unable to matched completely - send reject for entire FoK order
            matchingContext.eventsHelper.attachRejectEvent(cmd, cmd.size);

            cmd.size = 0;
            cmd.acceptedAmount = 0;
        }
    }

    private boolean canFullyMatch(final OrderCommand cmd) {

        final boolean isBidAction = cmd.action == OrderAction.BID;
        DirectOrder makerOrder = (cmd.action == OrderAction.BID) ? matchingContext.bestAskOrder : matchingContext.bestBidOrder;

        long remainingSize = cmd.getSize() - cmd.getFilled();
        long remainingAmount = cmd.getMaxAmount() == 0 ? Long.MAX_VALUE : cmd.getMaxAmount();

        // iterate through all orders
        while (makerOrder != null && (isBidAction ? makerOrder.price <= cmd.price : makerOrder.price >= cmd.price)) {
            final Bucket bucket = makerOrder.parent;

            final long tradeSize = Math.min(Math.min(remainingSize, bucket.volume), remainingAmount / makerOrder.price);

            remainingSize -= tradeSize;
            remainingAmount -= tradeSize * makerOrder.price;

            if (remainingSize == 0) {
                return true;
            } else if (tradeSize < bucket.volume) {
                // can exist as bucket was not fully consumed
                return false;
            }

            // switch to next order (can be null)
            makerOrder = bucket.tail.prev;
        }
        return false;
    }

    private long tryMatchInstantly(final IOrder takerOrder,
                                   final OrderCommand triggerCmd) {
        int version = triggerCmd.getVersion();
        return InstantMatchStrategyFactory.getStrategy(version).tryMatchInstantly(takerOrder, triggerCmd, matchingContext);
    }

    @Override
    public boolean isSelfMatch(OrderCommand cmd) {

        final OrderCommandType commandType = cmd.command;
        if (commandType == OrderCommandType.PLACE_ORDER) {
            return isSelfMatch(cmd, cmd);
        } else if (commandType == OrderCommandType.MOVE_ORDER) {
            return isSelfMatch(matchingContext.orderIdIndex.get(cmd.orderId), cmd);
        } else {
            return false;
        }
    }

    private boolean isSelfMatch(final IOrder takerOrder, final OrderCommand triggerCmd) {

        if (!matchingContext.symbolSpec.isPreventSelfMatch()) {
            // self trade prevention disabled
            return false;
        }

        if (takerOrder == null) {
            // will be rejected by moveOrder as order does not exist
            return false;
        }

        final boolean isBidAction = takerOrder.getAction() == OrderAction.BID;
        final long takerOrderPrice = triggerCmd.price;

        DirectOrder makerOrder;
        if (isBidAction) {
            makerOrder = matchingContext.bestAskOrder;
            if (makerOrder == null || makerOrder.price > takerOrderPrice) {
                // top ask is not hit
                return false;
            }
        } else {
            makerOrder = matchingContext.bestBidOrder;
            if (makerOrder == null || makerOrder.price < takerOrderPrice) {
                // top bid is not hit
                return false;
            }
        }

        if (triggerCmd.getMaxAmount() == 0) {
            return isSelfMatchBySize(takerOrder, makerOrder, isBidAction, takerOrderPrice);
        } else {
            return isSelfMatchByAmount(takerOrder, makerOrder, isBidAction, takerOrderPrice);
        }
    }

    private static boolean isSelfMatchBySize(IOrder takerOrder, DirectOrder makerOrder, boolean isBidAction, long takerOrderPrice) {
        long remainingSize = takerOrder.getSize() - takerOrder.getFilled();
        do {
            if (takerOrder.getUid() == makerOrder.getUid()) {
                return true;
            } else {
                remainingSize -= Math.min(remainingSize, makerOrder.size - makerOrder.filled);
                makerOrder = makerOrder.prev;
            }
        } while (makerOrder != null && remainingSize > 0 && (isBidAction ? makerOrder.price <= takerOrderPrice : makerOrder.price >= takerOrderPrice));
        return false;
    }

    private static boolean isSelfMatchByAmount(IOrder takerOrder, DirectOrder makerOrder, boolean isBidAction, long takerOrderPrice) {
        long remainingAmount = takerOrder.getMaxAmount() - (takerOrder.getFilled() * takerOrder.getPrice());
        do {
            if (takerOrder.getUid() == makerOrder.getUid()) {
                return true;
            } else {
                remainingAmount -= Math.min(remainingAmount, ((makerOrder.size - makerOrder.filled) * makerOrder.price));
                makerOrder = makerOrder.prev;
            }
        } while (makerOrder != null && remainingAmount > 0 && (isBidAction ? makerOrder.price <= takerOrderPrice : makerOrder.price >= takerOrderPrice));
        return false;
    }

    @Override
    public CommandResultCode cancelOrder(OrderCommand cmd) {

        final DirectOrder order = matchingContext.orderIdIndex.get(cmd.orderId);
        if (order == null || order.uid != cmd.uid) {
            return CommandResultCode.MATCHING_UNKNOWN_ORDER_ID;
        }
        matchingContext.orderIdIndex.remove(cmd.orderId);
        matchingContext.objectsPool.put(ObjectsPool.DIRECT_ORDER, order);

        final Bucket freeBucket = removeOrder(order);
        if (freeBucket != null) {
            matchingContext.objectsPool.put(ObjectsPool.DIRECT_BUCKET, freeBucket);
        }

        // fill action fields (for events handling)
        cmd.action = order.getAction();

        cmd.orderUuidMostSigBits = order.orderUuidMostSigBits;
        cmd.orderUuidLeastSigBits = order.orderUuidLeastSigBits;

        cmd.matcherEvent = matchingContext.eventsHelper.sendReduceEvent(order, order.getSize() - order.getFilled(), true);

        return CommandResultCode.SUCCESS;
    }

    @Override
    public CommandResultCode reduceOrder(OrderCommand cmd) {

        final long orderId = cmd.orderId;
        final long requestedReduceSize = cmd.size;
        if (requestedReduceSize <= 0) {
            return CommandResultCode.MATCHING_REDUCE_FAILED_WRONG_SIZE;
        }

        final DirectOrder order = matchingContext.orderIdIndex.get(orderId);
        if (order == null || order.uid != cmd.uid) {
            return CommandResultCode.MATCHING_UNKNOWN_ORDER_ID;
        }

        final long remainingSize = order.size - order.filled;
        final long reduceBy = Math.min(remainingSize, requestedReduceSize);
        final boolean canRemove = reduceBy == remainingSize;

        if (canRemove) {

            matchingContext.orderIdIndex.remove(orderId);
            matchingContext.objectsPool.put(ObjectsPool.DIRECT_ORDER, order);

            final Bucket freeBucket = removeOrder(order);
            if (freeBucket != null) {
                matchingContext.objectsPool.put(ObjectsPool.DIRECT_BUCKET, freeBucket);
            }

        } else {
            order.size -= reduceBy;
            order.parent.volume -= reduceBy;
        }

        cmd.matcherEvent = matchingContext.eventsHelper.sendReduceEvent(order, reduceBy, canRemove);

        // fill action fields (for events handling)
        cmd.action = order.getAction();

        return CommandResultCode.SUCCESS;
    }

    @Override
    public CommandResultCode moveOrder(OrderCommand cmd) {

        // order lookup
        final DirectOrder orderToMove = matchingContext.orderIdIndex.get(cmd.orderId);
        if (orderToMove == null || orderToMove.uid != cmd.uid) {
            return CommandResultCode.MATCHING_UNKNOWN_ORDER_ID;
        }

        // remove order
        final Bucket freeBucket = removeOrder(orderToMove);

        // update price
        orderToMove.price = cmd.price;

        // fill action fields (for events handling)
        cmd.action = orderToMove.getAction();

        // try match with new price as a taker order
        final long filled = tryMatchInstantly(orderToMove, cmd);
        if (filled == orderToMove.size) {
            // order was fully matched - removing
            matchingContext.orderIdIndex.remove(cmd.orderId);
            // returning free object back to the pool
            matchingContext.objectsPool.put(ObjectsPool.DIRECT_ORDER, orderToMove);
            if (freeBucket != null) {
                matchingContext.objectsPool.put(ObjectsPool.DIRECT_BUCKET, freeBucket);
            }
            return CommandResultCode.SUCCESS;
        }

        // not filled completely, inserting into new position
        orderToMove.filled = filled;

        // insert into a new place
        insertOrder(orderToMove, freeBucket);

        return CommandResultCode.SUCCESS;
    }

    private Bucket removeOrder(final DirectOrder order) {

        final Bucket bucket = order.parent;
        bucket.volume -= order.size - order.filled;
        bucket.numOrders--;
        Bucket bucketRemoved = null;

        if (bucket.tail == order) {
            // if we removing tail order -> change bucket tail reference
            if (order.next == null || order.next.parent != bucket) {
                // if no next or next order has different parent -> then it was the last bucket -> remove record
                final LongAdaptiveRadixTreeMap<Bucket> buckets = order.action == OrderAction.ASK ? matchingContext.askPriceBuckets : matchingContext.bidPriceBuckets;
                buckets.remove(order.price);
                bucketRemoved = bucket;
            } else {
                // otherwise at least one order always having the same parent left -> update tail reference to it
                bucket.tail = order.next; // always not null
            }
        }

        // update neighbor orders
        if (order.next != null) {
            order.next.prev = order.prev; // can be null
        }
        if (order.prev != null) {
            order.prev.next = order.next; // can be null
        }

        // check if best ask/bid were referring to the order we just removed
        if (order == matchingContext.bestAskOrder) {
            matchingContext.bestAskOrder = order.prev; // can be null
        } else if (order == matchingContext.bestBidOrder) {
            matchingContext.bestBidOrder = order.prev; // can be null
        }

        return bucketRemoved;
    }

    private void insertOrder(final DirectOrder order, final Bucket freeBucket) {

        final boolean isAsk = order.action == OrderAction.ASK;
        final LongAdaptiveRadixTreeMap<Bucket> buckets = isAsk ? matchingContext.askPriceBuckets : matchingContext.bidPriceBuckets;
        final Bucket toBucket = buckets.get(order.price);

        if (toBucket != null) {

            // can put bucket back to the pool (because target bucket already exists)
            if (freeBucket != null) {
                matchingContext.objectsPool.put(ObjectsPool.DIRECT_BUCKET, freeBucket);
            }

            toBucket.volume += order.size - order.filled;
            toBucket.numOrders++;
            final DirectOrder oldTail = toBucket.tail; // always exists, not null
            final DirectOrder prevOrder = oldTail.prev; // can be null
            // update neighbors
            toBucket.tail = order;
            oldTail.prev = order;
            if (prevOrder != null) {
                prevOrder.next = order;
            }
            // update self
            order.next = oldTail;
            order.prev = prevOrder;
            order.parent = toBucket;

        } else {

            // insert a new bucket (reuse existing)
            final Bucket newBucket = freeBucket != null
                ? freeBucket
                : matchingContext.objectsPool.get(ObjectsPool.DIRECT_BUCKET, Bucket::new);

            newBucket.tail = order;
            newBucket.volume = order.size - order.filled;
            newBucket.numOrders = 1;
            order.parent = newBucket;
            buckets.put(order.price, newBucket);
            final Bucket lowerBucket = isAsk ? buckets.getLowerValue(order.price) : buckets.getHigherValue(order.price);
            if (lowerBucket != null) {
                // attache new bucket and event to the lower entry
                DirectOrder lowerTail = lowerBucket.tail;
                final DirectOrder prevOrder = lowerTail.prev; // can be null
                // update neighbors
                lowerTail.prev = order;
                if (prevOrder != null) {
                    prevOrder.next = order;
                }
                // update self
                order.next = lowerTail;
                order.prev = prevOrder;
            } else {

                // if no floor entry, then update best order
                final DirectOrder oldBestOrder = isAsk ? matchingContext.bestAskOrder : matchingContext.bestBidOrder; // can be null

                if (oldBestOrder != null) {
                    oldBestOrder.next = order;
                }

                if (isAsk) {
                    matchingContext.bestAskOrder = order;
                } else {
                    matchingContext.bestBidOrder = order;
                }

                // update self
                order.next = null;
                order.prev = oldBestOrder;
            }
        }
    }

    @Override
    public int getOrdersNum(OrderAction action) {
        final LongAdaptiveRadixTreeMap<Bucket> buckets = action == OrderAction.ASK ? matchingContext.askPriceBuckets : matchingContext.bidPriceBuckets;
        final MutableInteger accum = new MutableInteger();
        buckets.forEach((p, b) -> accum.value += b.numOrders, Integer.MAX_VALUE);
        return accum.value;
    }

    @Override
    public long getTotalOrdersVolume(OrderAction action) {
        final LongAdaptiveRadixTreeMap<Bucket> buckets = action == OrderAction.ASK ? matchingContext.askPriceBuckets : matchingContext.bidPriceBuckets;
        final MutableLong accum = new MutableLong();
        buckets.forEach((p, b) -> accum.value += b.volume, Integer.MAX_VALUE);
        return accum.value;
    }

    @Override
    public IOrder getOrderById(final long orderId) {
        return matchingContext.orderIdIndex.get(orderId);
    }

    @Override
    public void validateInternalState() {
        final Long2ObjectHashMap<DirectOrder> ordersInChain = new Long2ObjectHashMap<>(matchingContext.orderIdIndex.size(Integer.MAX_VALUE), 0.8f);
        validateChain(true, ordersInChain);
        validateChain(false, ordersInChain);
        matchingContext.orderIdIndex.forEach((k, v) -> {
            if (ordersInChain.remove(k) != v) {
                thrw("chained orders does not contain orderId=" + k);
            }
        }, Integer.MAX_VALUE);

        if (ordersInChain.size() != 0) {
            thrw("orderIdIndex does not contain each order from chains");
        }
    }

    private void validateChain(boolean asksChain, Long2ObjectHashMap<DirectOrder> ordersInChain) {

        // buckets index
        final LongAdaptiveRadixTreeMap<Bucket> buckets = asksChain ? matchingContext.askPriceBuckets : matchingContext.bidPriceBuckets;
        final LongObjectHashMap<Bucket> bucketsFoundInChain = new LongObjectHashMap<>();
        buckets.validateInternalState();

        DirectOrder order = asksChain ? matchingContext.bestAskOrder : matchingContext.bestBidOrder;

        if (order != null && order.next != null) {
            thrw("best order has not-null next reference");
        }

        long lastPrice = -1;
        long expectedBucketVolume = 0;
        int expectedBucketOrders = 0;
        DirectOrder lastOrder = null;

        while (order != null) {

            if (ordersInChain.containsKey(order.orderId)) {
                thrw("duplicate orderid in the chain");
            }
            ordersInChain.put(order.orderId, order);

            expectedBucketVolume += order.size - order.filled;
            expectedBucketOrders++;

            if (lastOrder != null && order.next != lastOrder) {
                thrw("incorrect next reference");
            }
            if (order.parent.tail.price != order.price) {
                thrw("price of parent.tail differs");
            }
            if (lastPrice != -1 && order.price != lastPrice) {
                if (asksChain ^ order.price > lastPrice) {
                    thrw("unexpected price change direction");
                }
                if (order.next.parent == order.parent) {
                    thrw("unexpected price change within same bucket");
                }
            }

            if (order.parent.tail == order) {
                if (order.parent.volume != expectedBucketVolume) {
                    thrw("bucket volume does not match orders chain sizes");
                }
                if (order.parent.numOrders != expectedBucketOrders) {
                    thrw("bucket numOrders does not match orders chain length");
                }
                if (order.prev != null && order.prev.price == order.price) {
                    thrw("previous bucket has the same price");
                }
                expectedBucketVolume = 0;
                expectedBucketOrders = 0;
            }

            final Bucket knownBucket = bucketsFoundInChain.get(order.price);
            if (knownBucket == null) {
                bucketsFoundInChain.put(order.price, order.parent);
            } else if (knownBucket != order.parent) {
                thrw("found two different buckets having same price");
            }

            if (asksChain ^ order.action == OrderAction.ASK) {
                thrw("not expected order action");
            }

            lastPrice = order.price;
            lastOrder = order;
            order = order.prev;
        }

        // validate last order
        if (lastOrder != null && lastOrder.parent.tail != lastOrder) {
            thrw("last order is not a tail");
        }

        buckets.forEach((price, bucket) -> {
            if (bucketsFoundInChain.remove(price) != bucket) thrw("bucket in the price-tree not found in the chain");
        }, Integer.MAX_VALUE);

        if (!bucketsFoundInChain.isEmpty()) {
            thrw("found buckets in the chain that not discoverable from the price-tree");
        }
    }

    private void thrw(final String msg) {
        throw new IllegalStateException(msg);
    }

    @Override
    public List<Order> findUserOrders(long uid) {

        matchingContext.orderConsumer.init(uid);
        matchingContext.orderIdIndex.forEach(matchingContext.orderConsumer, Integer.MAX_VALUE);
        return matchingContext.orderConsumer.getOrders();
    }

    @Override
    public List<Order> findAllOrders() {

        matchingContext.orderConsumer.init(0);
        matchingContext.orderIdIndex.forEach(matchingContext.orderConsumer, Integer.MAX_VALUE);
        return matchingContext.orderConsumer.getOrders();
    }

    @Override
    public CoreSymbolSpecification getSymbolSpec() {
        return matchingContext.symbolSpec;
    }

    @Override
    public Stream<DirectOrder> askOrdersStream(boolean sortedIgnore) {
        return StreamSupport.stream(new OrdersSpliterator(matchingContext.bestAskOrder), false);
    }

    @Override
    public Stream<DirectOrder> bidOrdersStream(boolean sortedIgnore) {
        return StreamSupport.stream(new OrdersSpliterator(matchingContext.bestBidOrder), false);
    }

    @Override
    public void fillAsks(final int size, L2MarketData data) {
        data.askSize = 0;
        matchingContext.askPriceBuckets.forEach((p, bucket) -> {
            final int i = data.askSize++;
            data.askPrices[i] = bucket.tail.price;
            data.askVolumes[i] = bucket.volume;
            data.askOrders[i] = bucket.numOrders;
        }, size);
    }

    @Override
    public void fillBids(final int size, L2MarketData data) {
        data.bidSize = 0;
        matchingContext.bidPriceBuckets.forEachDesc((p, bucket) -> {
            final int i = data.bidSize++;
            data.bidPrices[i] = bucket.tail.price;
            data.bidVolumes[i] = bucket.volume;
            data.bidOrders[i] = bucket.numOrders;
        }, size);
    }

    @Override
    public int getTotalAskBuckets(final int limit) {
        return matchingContext.askPriceBuckets.size(limit);
    }

    @Override
    public int getTotalBidBuckets(final int limit) {
        return matchingContext.bidPriceBuckets.size(limit);
    }

    @Override
    public void writeMarshallable(BytesOut bytes) {
        matchingContext.symbolSpec.writeMarshallable(bytes);
        bytes.writeInt(matchingContext.orderIdIndex.size(Integer.MAX_VALUE));
        askOrdersStream(true).forEach(order -> order.writeMarshallable(bytes));
        bidOrdersStream(true).forEach(order -> order.writeMarshallable(bytes));
    }

    @Getter
    @Setter
    public final class MatchingContext {
        // heads (nullable)
        private OrderBookDirectImpl.DirectOrder bestAskOrder = null;
        private OrderBookDirectImpl.DirectOrder bestBidOrder = null;

        private OrderBookEventsHelper eventsHelper;

        // index: orderId -> order
        private LongAdaptiveRadixTreeMap<OrderBookDirectImpl.DirectOrder> orderIdIndex;

        // Object pools
        private ObjectsPool objectsPool;

        // buckets
        private LongAdaptiveRadixTreeMap<OrderBookDirectImpl.Bucket> askPriceBuckets;
        private LongAdaptiveRadixTreeMap<OrderBookDirectImpl.Bucket> bidPriceBuckets;

        // symbol specification
        private CoreSymbolSpecification symbolSpec;

        private OrderConsumer orderConsumer = new OrderConsumer();

        public MatchingContext(OrderBookEventsHelper eventsHelper,
                               LongAdaptiveRadixTreeMap<OrderBookDirectImpl.DirectOrder> orderIdIndex,
                               ObjectsPool objectsPool,
                               LongAdaptiveRadixTreeMap<OrderBookDirectImpl.Bucket> askPriceBuckets,
                               LongAdaptiveRadixTreeMap<OrderBookDirectImpl.Bucket> bidPriceBuckets,
                               CoreSymbolSpecification symbolSpec) {
            this.eventsHelper = eventsHelper;
            this.orderIdIndex = orderIdIndex;
            this.objectsPool = objectsPool;
            this.askPriceBuckets = askPriceBuckets;
            this.bidPriceBuckets = bidPriceBuckets;
            this.symbolSpec = symbolSpec;
        }
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static final class DirectOrder implements WriteBytesMarshallable, IOrder {

        private static final int CURRENT_VERSION = 2; // Increment this when adding new fields
        private static final long VERSION_MARKER = 0xFFFFFFFFL; // Special marker to detect versioned format

        @Getter
        private long orderId;

        @Getter
        private long price;

        @Getter
        private long size;

        @Getter
        private long maxAmount;

        @Getter
        private long acceptedAmount;

        @Getter
        @Setter
        private long filled;

        // required for PLACE_ORDER only;
        @Getter
        private OrderAction action;

        @Getter
        private long uid;

        @Getter
        private long timestamp;

        @Getter
        private long orderUuidMostSigBits;

        @Getter
        private long orderUuidLeastSigBits;

        // fast orders structure

        @Getter
        private Bucket parent;

        // next order (towards the matching direction, price grows for asks)
        @Getter
        @Setter
        private DirectOrder next;

        // previous order (to the tail of the queue, lower priority and worst price, towards the matching direction)
        @Getter
        private DirectOrder prev;

        DirectOrder(BytesIn bytes) {
            // Try to read version marker
            long firstLong = bytes.readLong();
            int version;

            if (firstLong == VERSION_MARKER) {
                // New format with version
                version = bytes.readInt();
                this.orderId = bytes.readLong();
            } else {
                // Old format - first long is actually orderId
                version = 1;
                this.orderId = firstLong;
            }

            this.price = bytes.readLong();  // price
            this.size = bytes.readLong(); // size
            this.maxAmount = bytes.readLong(); // maxAmount
            this.filled = bytes.readLong(); // filled
            this.action = OrderAction.of(bytes.readByte());
            this.uid = bytes.readLong(); // uid
            this.timestamp = bytes.readLong(); // timestamp

            // Handle new fields based on version
            if (version >= 2) {
                this.orderUuidMostSigBits = bytes.readLong();
                this.orderUuidLeastSigBits = bytes.readLong();
            } else {
                // Default values for old format
                this.orderUuidMostSigBits = 0L;
                this.orderUuidLeastSigBits = 0L;
            }
        }

        @Override
        public void writeMarshallable(BytesOut bytes) {
            // Write version marker and version
            bytes.writeLong(VERSION_MARKER);
            bytes.writeInt(CURRENT_VERSION);

            bytes.writeLong(orderId);
            bytes.writeLong(price);
            bytes.writeLong(size);
            bytes.writeLong(maxAmount);
            bytes.writeLong(filled);
            bytes.writeByte(action.getCode());
            bytes.writeLong(uid);
            bytes.writeLong(timestamp);
            bytes.writeLong(orderUuidMostSigBits);
            bytes.writeLong(orderUuidLeastSigBits);
        }

        @Override
        public String toString() {
            return "[" + orderId + " " + (action == OrderAction.ASK ? 'A' : 'B')
                + price + ":" + size + "F" + filled + "MA" + maxAmount
                + " U" + uid + "]";
        }

        @Override
        public int hashCode() {
            return Objects.hash(orderId, action, price, size, filled, uid);
        }

        /**
         * timestamp is not included into hashCode() and equals() for repeatable results
         */
        @Override
        public boolean equals(Object o) {
            if (o == this) return true;
            if (o == null) return false;
            if (!(o instanceof DirectOrder)) return false;

            DirectOrder other = (DirectOrder) o;

            // ignore userCookie && timestamp
            return orderId == other.orderId
                && action == other.action
                && price == other.price
                && size == other.size
                && filled == other.filled
                && uid == other.uid;
        }

        @Override
        public int stateHash() {
            return Objects.hash(orderId, action, price, size, filled, uid);
        }
    }

    @ToString
    @Getter
    @Setter
    public static class Bucket {
        long volume;
        int numOrders;
        DirectOrder tail;
    }

    private class OrderConsumer implements LongObjConsumer<DirectOrder> {

        private final List<Order> list = new ArrayList<>();
        private long uid;

        public void init(long uid) {
            this.uid = uid;
            list.clear();
        }

        public List<Order> getOrders() {
            return list;
        }

        @Override
        public void accept(long orderId, DirectOrder directOrder) {

            if (uid == 0 || directOrder.uid == uid) {

                Order order = matchingContext.objectsPool.get(ObjectsPool.ORDER, (Supplier<Order>) Order::new);
                order.orderId = orderId;
                order.price = directOrder.price;
                order.size = directOrder.size;
                order.maxAmount = directOrder.maxAmount;
                order.acceptedAmount = directOrder.acceptedAmount;
                order.filled = directOrder.filled;
                order.action = directOrder.action;
                order.uid = directOrder.uid;
                order.timestamp = directOrder.timestamp;
                order.orderUuidMostSigBits = directOrder.orderUuidMostSigBits;;
                order.orderUuidLeastSigBits = directOrder.orderUuidLeastSigBits;

                list.add(order);
            }
        }
    }
}
