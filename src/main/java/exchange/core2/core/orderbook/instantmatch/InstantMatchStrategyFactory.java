package exchange.core2.core.orderbook.instantmatch;

public class InstantMatchStrategyFactory {

    private static final InstantMatchStrategyV1 STRATEGY_V1 = new InstantMatchStrategyV1();
    private static final InstantMatchStrategyV2 STRATEGY_V2 = new InstantMatchStrategyV2();

    public static InstantMatchStrategy getStrategy(final int version) {
        switch (version) {
            case 1:
                return STRATEGY_V1;
            default:
                return STRATEGY_V2;
        }
    }
}
