package exchange.core2.core.orderbook.instantmatch;

import exchange.core2.collections.art.LongAdaptiveRadixTreeMap;
import exchange.core2.collections.objpool.ObjectsPool;
import exchange.core2.core.common.IOrder;
import exchange.core2.core.common.MatcherTradeEvent;
import exchange.core2.core.common.OrderAction;
import exchange.core2.core.common.cmd.OrderCommand;
import exchange.core2.core.orderbook.OrderBookDirectImpl;

public class InstantMatchStrategyV1 implements InstantMatchStrategy {

    @Override
    public long tryMatchInstantly(final IOrder takerOrder, final OrderCommand triggerCmd, final OrderBookDirectImpl.MatchingContext matchingContext) {
        final boolean isBidAction = takerOrder.getAction() == OrderAction.BID;

        final long limitPrice = takerOrder.getPrice();

        OrderBookDirectImpl.DirectOrder makerOrder;
        if (isBidAction) {
            makerOrder = matchingContext.getBestAskOrder();
            if (makerOrder == null || makerOrder.getPrice() > limitPrice) {
                return takerOrder.getFilled();
            }
        } else {
            makerOrder = matchingContext.getBestBidOrder();
            if (makerOrder == null || makerOrder.getPrice() < limitPrice) {
                return takerOrder.getFilled();
            }
        }

        long remainingSize = takerOrder.getSize() - takerOrder.getFilled();
        long remainingAmount = takerOrder.getMaxAmount() == 0 ? Long.MAX_VALUE : takerOrder.getMaxAmount();

        if (remainingSize == 0) {
            return takerOrder.getFilled();
        }

        OrderBookDirectImpl.DirectOrder priceBucketTail = makerOrder.getParent().getTail();

        MatcherTradeEvent eventsTail = triggerCmd.matcherEvent;

        // iterate through all orders
        do {

            // calculate exact volume can fill for this order
            final long tradeSize = Math.min(Math.min(remainingSize, makerOrder.getSize() - makerOrder.getFilled()), remainingAmount / makerOrder.getPrice());

            makerOrder.setFilled(makerOrder.getFilled() + tradeSize);
            makerOrder.getParent().setVolume(makerOrder.getParent().getVolume() - tradeSize);
            remainingSize -= tradeSize;
            remainingAmount -= tradeSize * makerOrder.getPrice();

            // remove from order book filled orders
            final boolean makerCompleted = makerOrder.getSize() == makerOrder.getFilled();
            if (makerCompleted) {
                makerOrder.getParent().setNumOrders(makerOrder.getParent().getNumOrders() - 1);
            }

            final MatcherTradeEvent tradeEvent = matchingContext.getEventsHelper().sendTradeEvent(makerOrder, makerCompleted, remainingSize == 0, tradeSize);

            if (eventsTail == null) {
                triggerCmd.matcherEvent = tradeEvent;
            } else {
                eventsTail.nextEvent = tradeEvent;
            }
            eventsTail = tradeEvent;

            if (!makerCompleted) {
                // maker not completed -> no unmatched volume left, can exit matching loop
                break;
            }

            // if completed can remove maker order
            matchingContext.getOrderIdIndex().remove(makerOrder.getOrderId());
            matchingContext.getObjectsPool().put(ObjectsPool.DIRECT_ORDER, makerOrder);


            if (makerOrder == priceBucketTail) {
                // reached current price tail -> remove bucket reference
                final LongAdaptiveRadixTreeMap<OrderBookDirectImpl.Bucket> buckets = isBidAction ? matchingContext.getAskPriceBuckets() : matchingContext.getBidPriceBuckets();
                buckets.remove(makerOrder.getPrice());
                matchingContext.getObjectsPool().put(ObjectsPool.DIRECT_BUCKET, makerOrder.getParent());

                // set next price tail (if there is next price)
                if (makerOrder.getPrev() != null) {
                    priceBucketTail = makerOrder.getPrev().getParent().getTail();
                }
            }

            // switch to next order
            makerOrder = makerOrder.getPrev(); // can be null

        } while (makerOrder != null
            && remainingAmount > 0
            && remainingSize > 0
            && (isBidAction ? makerOrder.getPrice() <= limitPrice : makerOrder.getPrice() >= limitPrice));

        // break chain after last order
        if (makerOrder != null) {
            makerOrder.setNext(null);
        }

        // update best orders reference
        if (isBidAction) {
            matchingContext.setBestAskOrder(makerOrder);
        } else {
            matchingContext.setBestBidOrder(makerOrder);
        }

        triggerCmd.acceptedAmount = takerOrder.getMaxAmount() - remainingAmount;

        // return filled amount
        return takerOrder.getSize() - remainingSize;
    }
}
