package exchange.core2.core.orderbook.instantmatch;

import exchange.core2.core.common.IOrder;
import exchange.core2.core.common.cmd.OrderCommand;
import exchange.core2.core.orderbook.OrderBookDirectImpl;

public interface InstantMatchStrategy {
    long tryMatchInstantly(final IOrder takerOrder,
                           final OrderCommand triggerCmd,
                           final OrderBookDirectImpl.MatchingContext matchingContext);
}
