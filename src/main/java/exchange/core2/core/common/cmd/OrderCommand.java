/*
 * Copyright 2019 <PERSON><PERSON><PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package exchange.core2.core.common.cmd;

import exchange.core2.core.common.*;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@SuppressWarnings({"java:S1104", "java:S125"})
public final class OrderCommand implements IOrder {

    public OrderCommandType command;

    @Getter
    public long orderId;

    @Getter
    public long orderUuidMostSigBits;

    @Getter
    public long orderUuidLeastSigBits;

    public int symbol;

    @Getter
    public long price;

    @Getter
    public long size;

    @Getter
    public long acceptedAmount;

    @Getter
    public long maxAmount;

    // required for PLACE_ORDER only;
    // for CANCEL/MOVE contains original order action (filled by orderbook)
    @Getter
    public OrderAction action;

    public OrderType orderType;

    @Getter
    public long uid;

    @Getter
    public long timestamp;

    @Getter
    public int version;

    public int userCookie;

    public int serviceFlags;

    // result code of command execution - can also be used for saving intermediate state
    public CommandResultCode resultCode;

    // trade events chain
    public MatcherTradeEvent matcherEvent;

    // optional market data
    public L2MarketData marketData;

    public static OrderCommand newOrder(OrderType orderType, long orderId, long uid, long price, long size, long maxAmount, OrderAction action) {
        return newOrder(orderType, orderId, uid, price, size, maxAmount, action, 1);
    }

    @SuppressWarnings("java:S107")
    public static OrderCommand newOrder(OrderType orderType, long orderId, long uid, long price, long size, long maxAmount, OrderAction action, int version) {
        OrderCommand cmd = new OrderCommand();
        cmd.command = OrderCommandType.PLACE_ORDER;
        cmd.orderId = orderId;
        cmd.uid = uid;
        cmd.price = price;
        cmd.size = size;
        cmd.maxAmount = maxAmount;
        cmd.action = action;
        cmd.orderType = orderType;
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;
        cmd.version = version;
        return cmd;
    }

    public static OrderCommand cancel(long orderId, long uid) {
        return cancel(orderId, uid, 1);
    }

    public static OrderCommand cancel(long orderId, long uid, int version) {
        OrderCommand cmd = new OrderCommand();
        cmd.command = OrderCommandType.CANCEL_ORDER;
        cmd.orderId = orderId;
        cmd.uid = uid;
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;
        cmd.version = version;
        return cmd;
    }

    public static OrderCommand reduce(long orderId, long uid, long reduceSize) {
        return reduce(orderId, uid, reduceSize, 1);
    }

    public static OrderCommand reduce(long orderId, long uid, long reduceSize, int version) {
        OrderCommand cmd = new OrderCommand();
        cmd.command = OrderCommandType.REDUCE_ORDER;
        cmd.orderId = orderId;
        cmd.uid = uid;
        cmd.size = reduceSize;
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;
        cmd.version = version;
        return cmd;
    }

    public static OrderCommand update(long orderId, long uid, long price) {
        return update(orderId, uid, price, 1);
    }

    public static OrderCommand update(long orderId, long uid, long price, int version) {
        OrderCommand cmd = new OrderCommand();
        cmd.command = OrderCommandType.MOVE_ORDER;
        cmd.orderId = orderId;
        cmd.uid = uid;
        cmd.price = price;
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;
        cmd.version = version;
        return cmd;
    }

    /**
     * Handles full MatcherTradeEvent chain, without removing/revoking them
     *
     * @param handler - MatcherTradeEvent handler
     */
    public void processMatcherEvents(Consumer<MatcherTradeEvent> handler) {
        MatcherTradeEvent mte = this.matcherEvent;
        while (mte != null) {
            handler.accept(mte);
            mte = mte.nextEvent;
        }
    }

    /**
     * Produces garbage
     * For testing only !!!
     *
     * @return list of events
     */
    public List<MatcherTradeEvent> extractEvents() {
        List<MatcherTradeEvent> list = new ArrayList<>();
        processMatcherEvents(list::add);
        return list;
    }

    @Override
    public long getFilled() {
        return 0;
    }

    @Override
    public int stateHash() {
        throw new UnsupportedOperationException("Command does not represents state");
    }
}
