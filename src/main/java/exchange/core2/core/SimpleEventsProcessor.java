package exchange.core2.core;

import exchange.core2.core.common.MatcherEventType;
import exchange.core2.core.common.MatcherTradeEvent;
import exchange.core2.core.common.cmd.OrderCommand;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.agrona.collections.MutableBoolean;
import org.agrona.collections.MutableInteger;
import org.agrona.collections.MutableLong;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Getter
@Slf4j
public class SimpleEventsProcessor {

    private final MutableBoolean takerOrderCompleted = new MutableBoolean(false);
    private final MutableBoolean hasTrades = new MutableBoolean(false);
    private final MutableLong totalSize = new MutableLong(0L);
    private final MutableLong totalAmount = new MutableLong(0L);
    private final MutableInteger tradesCount = new MutableInteger(0);
    private final List<MatcherTradeEvent> matcherTradeEvents = new ArrayList<>();

    private final IEventsHandler eventsHandler;
    private final int tradesBatchSize;

    private long tradeId;
    private long matchId;

    private void resetMutables() {
        takerOrderCompleted.set(false);
        hasTrades.set(false);
        totalSize.set(0);
        totalAmount.set(0);
        tradesCount.set(0);
        matcherTradeEvents.clear();
    }

    public void accept(OrderCommand cmd, long seq, boolean silent) {

        if (!silent) {
            sendCommandResult(cmd, seq);
        }

        sendTradeEvents(cmd);
        sendMarketData(cmd, seq);
    }

    private void sendMarketData(OrderCommand cmd, long seq) {
        if (cmd.marketData != null) {
            eventsHandler.orderBook(cmd.symbol, cmd.marketData, cmd.timestamp, seq);
        }
    }

    private void sendTradeEvents(OrderCommand cmd) {
        resetMutables();

        if (cmd.matcherEvent == null) {
            return;
        }

        if (cmd.matcherEvent.eventType == MatcherEventType.REDUCE) {
            eventsHandler.reduceEvent(
                cmd.symbol,
                cmd.matcherEvent.size,
                cmd.matcherEvent.activeOrderCompleted,
                cmd.orderUuidMostSigBits,
                cmd.orderUuidLeastSigBits,
                cmd.matcherEvent.price,
                cmd.orderId,
                cmd.uid,
                cmd.timestamp);

            if (cmd.matcherEvent.nextEvent != null) {
                throw new IllegalStateException("Only single REDUCE event is expected");
            }
            return;
        }

        MatcherTradeEvent evt = cmd.matcherEvent;
        while (evt != null) {
            if (evt.eventType == MatcherEventType.TRADE) {
                hasTrades.value = true;
                tradesCount.increment();

                totalSize.value += evt.size;
                totalAmount.value += evt.price * evt.size;

                if (evt.activeOrderCompleted) {
                    takerOrderCompleted.value = true;
                }

                evt.tradeId = tradeId++;
                matcherTradeEvents.add(evt);

                if (matcherTradeEvents.size() >= tradesBatchSize) {
                    matchId++;
                    sendTradeBatch(cmd, matcherTradeEvents, matchId);
                    resetMutables();
                }
            } else if (evt.eventType == MatcherEventType.REJECT) {
                eventsHandler.rejectEvent(
                        cmd.symbol,
                        evt.size,
                        cmd.orderUuidMostSigBits,
                        cmd.orderUuidLeastSigBits,
                        evt.price,
                        cmd.orderId,
                        cmd.uid,
                        cmd.timestamp);

                if (evt.activeOrderCompleted) {
                    takerOrderCompleted.value = true;
                }
            }

            evt = evt.nextEvent;
        }

        if (!matcherTradeEvents.isEmpty()) {
            matchId++;
            sendTradeBatch(cmd, matcherTradeEvents, matchId);
        }
    }

    private void sendTradeBatch(OrderCommand cmd, List<MatcherTradeEvent> batch, long matchId) {
        eventsHandler.tradeEvent(
            cmd.symbol,
            totalAmount.value / totalSize.value,
            totalSize.value,
            cmd.orderId,
            cmd.uid,
            tradeId++,
            matchId,
            cmd.action,
            takerOrderCompleted.value,
            cmd.orderUuidMostSigBits,
            cmd.orderUuidLeastSigBits,
            cmd.timestamp,
            tradesCount.value,
            batch);
    }

    private void sendCommandResult(OrderCommand cmd, long seq) {

        switch (cmd.command) {
            case PLACE_ORDER:
                eventsHandler.placeOrderResult(cmd.price, cmd.size, cmd.acceptedAmount, cmd.orderId, cmd.action, cmd.orderType, cmd.uid, cmd.symbol, cmd.resultCode, cmd.serviceFlags, cmd.orderUuidMostSigBits, cmd.orderUuidLeastSigBits, cmd.timestamp, seq);
                break;

            case MOVE_ORDER:
                eventsHandler.moveOrderResult(cmd.orderId, cmd.price, cmd.uid, cmd.symbol, cmd.resultCode, cmd.serviceFlags, cmd.orderUuidMostSigBits, cmd.orderUuidLeastSigBits, cmd.timestamp, seq);
                break;

            case CANCEL_ORDER:
                eventsHandler.cancelOrderResult(cmd.orderId, cmd.uid, cmd.symbol, cmd.resultCode, cmd.serviceFlags, cmd.orderUuidMostSigBits, cmd.orderUuidLeastSigBits, cmd.timestamp, seq);
                break;

            case REDUCE_ORDER:
                eventsHandler.reduceOrderResult(cmd.orderId, cmd.uid, cmd.symbol, cmd.size, cmd.resultCode, cmd.serviceFlags, cmd.orderUuidMostSigBits, cmd.orderUuidLeastSigBits, cmd.timestamp, seq);
                break;

            case ORDER_BOOK_REQUEST:
                eventsHandler.orderBookResult(cmd.symbol, (int) cmd.size, cmd.resultCode, cmd.timestamp, seq);
                break;

            default:

        }
    }

    public void loadSnapshot(long tradeId, long matchId) {
        this.tradeId = tradeId;
        this.matchId = matchId;

    }

}
