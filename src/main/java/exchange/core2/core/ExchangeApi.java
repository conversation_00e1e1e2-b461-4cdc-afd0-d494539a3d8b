package exchange.core2.core;

import org.eclipse.collections.impl.map.mutable.primitive.IntObjectHashMap;

import java.util.Optional;

import exchange.core2.collections.objpool.ObjectsPool;
import exchange.core2.core.common.MatcherTradeEvent;
import exchange.core2.core.common.OrderAction;
import exchange.core2.core.common.OrderType;
import exchange.core2.core.common.api.binary.BinaryDataCommand;
import exchange.core2.core.common.api.reports.ReportQuery;
import exchange.core2.core.common.api.reports.ReportResult;
import exchange.core2.core.common.cmd.CommandResultCode;
import exchange.core2.core.common.cmd.OrderCommand;
import exchange.core2.core.common.cmd.OrderCommandType;
import exchange.core2.core.orderbook.IOrderBook;
import exchange.core2.core.processors.MatchingEngineRouter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public final class ExchangeApi {

    private final OrderCommand cmd = new OrderCommand();

    private final MatchingEngineRouter router;
    private final SimpleEventsProcessor eventsProcessor;
    private final ObjectsPool objectsPool;

    private MatcherTradeEvent currentEvent;
    private MatcherTradeEvent nextEvent;

    @SuppressWarnings("java:S107")
    public void placeOrder(
            final long orderId,
            final OrderAction action,
            final OrderType orderType,
            final int symbol,
            final long uid,
            final long price,
            final long size,
            final long maxAmount,
            final int serviceFlags,
            final long orderUuidMostSigBits,
            final long orderUuidLeastSigBits,
            final long timestamp,
            final long correlation,
            final boolean silent,
            final int version) {

        cmd.command = OrderCommandType.PLACE_ORDER;
        cmd.price = price;
        cmd.size = size;
        cmd.maxAmount = maxAmount;
        cmd.orderId = orderId;
        cmd.action = action;
        cmd.orderType = orderType;
        cmd.symbol = symbol;
        cmd.uid = uid;
        cmd.serviceFlags = serviceFlags;
        cmd.orderUuidMostSigBits = orderUuidMostSigBits;
        cmd.orderUuidLeastSigBits = orderUuidLeastSigBits;
        cmd.timestamp = timestamp;
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;
        cmd.version = version;

        processCommand(correlation, silent);
    }

    @SuppressWarnings("java:S107")
    public void moveOrder(
            final long orderId,
            final int symbol,
            final long uid,
            final long newPrice,
            final int serviceFlags,
            final long timestamp,
            final long correlation,
            final boolean silent,
            final int version) {

        cmd.command = OrderCommandType.MOVE_ORDER;
        cmd.price = newPrice;
        cmd.orderId = orderId;
        cmd.symbol = symbol;
        cmd.uid = uid;
        cmd.serviceFlags = serviceFlags;
        cmd.timestamp = timestamp;
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;
        cmd.version = version;

        processCommand(correlation, silent);
    }

    @SuppressWarnings("java:S107")
    public void reduceOrder(
            final long orderId,
            final int symbol,
            final long uid,
            final long reduceSize,
            final int serviceFlags,
            final long timestamp,
            final long correlation,
            final boolean silent,
            final int version) {

        cmd.command = OrderCommandType.REDUCE_ORDER;
        cmd.orderId = orderId;
        cmd.symbol = symbol;
        cmd.uid = uid;
        cmd.size = reduceSize;
        cmd.serviceFlags = serviceFlags;
        cmd.timestamp = timestamp;
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;
        cmd.version = version;

        processCommand(correlation, silent);
    }

    @SuppressWarnings("java:S107")
    public void cancelOrder(
            final long orderId,
            final int symbol,
            final long uid,
            final int serviceFlags,
            final long timestamp,
            final long correlation,
            final boolean silent,
            final int version) {

        cmd.command = OrderCommandType.CANCEL_ORDER;
        cmd.orderId = orderId;
        cmd.symbol = symbol;
        cmd.uid = uid;
        cmd.serviceFlags = serviceFlags;
        cmd.timestamp = timestamp;
        cmd.resultCode = CommandResultCode.VALID_FOR_MATCHING_ENGINE;
        cmd.version = version;

        processCommand(correlation, silent);
    }

    public void requestOrderBook(
            final int symbol,
            final long depth,
            final long timestamp,
            final long correlation,
            final int version) {

        cmd.command = OrderCommandType.ORDER_BOOK_REQUEST;
        cmd.symbol = symbol;
        cmd.size = depth;
        cmd.timestamp = timestamp;
        cmd.resultCode = CommandResultCode.NEW;
        cmd.version = version;

        processCommand(correlation, false);
    }

    public void persistState(
            final long timestamp,
            final long correlation,
            final boolean silent,
            final int version) {

        cmd.command = OrderCommandType.PERSIST_STATE_MATCHING;
        cmd.orderId = 0l;
        cmd.symbol = -1;
        cmd.uid = 0;
        cmd.price = 0;
        cmd.timestamp = timestamp;
        cmd.resultCode = CommandResultCode.NEW;
        cmd.version = version;

        processCommand(correlation, silent);
    }

    public void submitBinaryData(final BinaryDataCommand cmd) {
        router.handleBinaryMessage(cmd);
    }

    public <R extends ReportResult> Optional<R> submitReportQuery(ReportQuery<R> reportQuery) {
        return router.handleReportQuery(reportQuery);
    }

    public final IntObjectHashMap<IOrderBook> getOrderBooks() {
        return router.getOrderBooks();
    }

    private void processCommand(long correlation, boolean silent) {

        router.processOrder(correlation, cmd);

        eventsProcessor.accept(cmd, correlation, silent);

        currentEvent = cmd.matcherEvent;
        if (currentEvent != null) {
            do {
                nextEvent = currentEvent.nextEvent;
                currentEvent.eventType = null;
                currentEvent.nextEvent = null;
                objectsPool.put(ObjectsPool.MATCHER_TRADE_EVENT, currentEvent);
                currentEvent = nextEvent;
            } while (currentEvent != null);
        }

        cmd.userCookie = 0;
        cmd.timestamp = 0;
        cmd.resultCode = null;
        cmd.orderId = 0;
        cmd.symbol = 0;
        cmd.price = 0;
        cmd.size = 0;
        cmd.maxAmount = 0;
        cmd.acceptedAmount = 0;
        cmd.action = null;
        cmd.orderType = null;
        cmd.uid = 0;
        cmd.timestamp = 0;
        cmd.userCookie = 0;
        cmd.serviceFlags = 0;
        cmd.orderUuidMostSigBits = 0;
        cmd.orderUuidLeastSigBits = 0;
        cmd.resultCode = null;
        cmd.matcherEvent = null;
        cmd.marketData = null;
    }
}
