package io.wyden.orderhistory.model;

import java.time.OffsetDateTime;
import java.util.List;

@Deprecated
public record OrderStateEntity(
    String id,
    String orderId,
    String origOrderId,
    String clientId,
    String clOrderId,
    String origClOrderId,
    String portfolioId,
    String orderStatus,
    String orderQty,
    String currency,
    String limitPrice,
    String stopPrice,
    String tif,
    String filledQty,
    String remainingQty,
    String lastQty,
    String avgPrice,
    String lastPrice,
    String reason,
    String side,
    String instrumentId,
    OffsetDateTime venueTimestamp, // ISO 8601 format, UTC
    OffsetDateTime createdAt, // ISO 8601 format, UTC
    OffsetDateTime updatedAt, // ISO 8601 format, UTC
    String lastRequestResult,
    Integer sequenceNumber,
    OffsetDateTime expireTime, // ISO 8601 format, UTC
    String orderCategory,
    String parentOrderId,
    String orderType,
    String symbol,
    String instrumentType,
    List<String> venueAccounts,
    String underlyingVenueAccount,
    String rootOrderId, // For chained Orders, this references the root Order in the chain
    String counterPortfolioId,
    String portfolioType,
    String counterPortfolioType,
    String extOrderId
) {

    public static final class OrderStateEntityBuilder {
        private String id;
        private String orderId;
        private String origOrderId;
        private String clientId;
        private String clOrderId;
        private String origClOrderId;
        private String portfolioId;
        private String orderStatus;
        private String orderQty;
        private String currency;
        private String limitPrice;
        private String stopPrice;
        private String tif;
        private String filledQty;
        private String remainingQty;
        private String lastQty;
        private String avgPrice;
        private String lastPrice;
        private String reason;
        private String side;
        private String instrumentId;
        private OffsetDateTime venueTimestamp;
        private OffsetDateTime createdAt;
        private OffsetDateTime updatedAt;
        private String lastRequestResult;
        private Integer sequenceNumber;
        private OffsetDateTime expireTime;
        private String orderCategory;
        private String parentOrderId;
        private String orderType;
        private String symbol;
        private String instrumentType;
        private List<String> venueAccounts;
        private String underlyingVenueAccount;
        private String rootOrderId;
        private String counterPortfolioId;
        private String portfolioType;
        private String counterPortfolioType;
        private String extOrderId;

        public OrderStateEntityBuilder() {
        }

        private OrderStateEntityBuilder(OrderStateEntity other) {
            this.id = other.id();
            this.orderId = other.orderId();
            this.origOrderId = other.origOrderId();
            this.clientId = other.clientId();
            this.clOrderId = other.clOrderId();
            this.origClOrderId = other.origClOrderId();
            this.portfolioId = other.portfolioId();
            this.orderStatus = other.orderStatus();
            this.orderQty = other.orderQty();
            this.currency = other.currency();
            this.limitPrice = other.limitPrice();
            this.stopPrice = other.stopPrice();
            this.tif = other.tif();
            this.filledQty = other.filledQty();
            this.remainingQty = other.remainingQty();
            this.lastQty = other.lastQty();
            this.avgPrice = other.avgPrice();
            this.lastPrice = other.lastPrice();
            this.reason = other.reason();
            this.side = other.side();
            this.instrumentId = other.instrumentId();
            this.venueTimestamp = other.venueTimestamp();
            this.createdAt = other.createdAt();
            this.updatedAt = other.updatedAt();
            this.lastRequestResult = other.lastRequestResult();
            this.sequenceNumber = other.sequenceNumber();
            this.expireTime = other.expireTime();
            this.orderCategory = other.orderCategory();
            this.parentOrderId = other.parentOrderId();
            this.orderType = other.orderType();
            this.symbol = other.symbol();
            this.instrumentType = other.instrumentType();
            this.venueAccounts = other.venueAccounts();
            this.underlyingVenueAccount = other.underlyingVenueAccount();
            this.rootOrderId = other.rootOrderId();
            this.counterPortfolioId = other.counterPortfolioId();
            this.portfolioType = other.portfolioType();
            this.counterPortfolioType = other.counterPortfolioType();
            this.extOrderId = other.extOrderId();
        }

        public static OrderStateEntityBuilder builder() {
            return new OrderStateEntityBuilder();
        }

        public static OrderStateEntityBuilder toBuilder(OrderStateEntity entity) {
            return new OrderStateEntityBuilder(entity);
        }

        public OrderStateEntityBuilder id(String id) {
            this.id = id;
            return this;
        }

        public OrderStateEntityBuilder orderId(String orderId) {
            this.orderId = orderId;
            return this;
        }

        public OrderStateEntityBuilder origOrderId(String origOrderId) {
            this.origOrderId = origOrderId;
            return this;
        }

        public OrderStateEntityBuilder clientId(String clientId) {
            this.clientId = clientId;
            return this;
        }

        public OrderStateEntityBuilder clOrderId(String clOrderId) {
            this.clOrderId = clOrderId;
            return this;
        }

        public OrderStateEntityBuilder origClOrderId(String origClOrderId) {
            this.origClOrderId = origClOrderId;
            return this;
        }

        public OrderStateEntityBuilder portfolioId(String portfolioId) {
            this.portfolioId = portfolioId;
            return this;
        }

        public OrderStateEntityBuilder orderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
            return this;
        }

        public OrderStateEntityBuilder orderQty(String orderQty) {
            this.orderQty = orderQty;
            return this;
        }

        public OrderStateEntityBuilder currency(String currency) {
            this.currency = currency;
            return this;
        }

        public OrderStateEntityBuilder limitPrice(String limitPrice) {
            this.limitPrice = limitPrice;
            return this;
        }

        public OrderStateEntityBuilder stopPrice(String stopPrice) {
            this.stopPrice = stopPrice;
            return this;
        }

        public OrderStateEntityBuilder tif(String tif) {
            this.tif = tif;
            return this;
        }

        public OrderStateEntityBuilder filledQty(String filledQty) {
            this.filledQty = filledQty;
            return this;
        }

        public OrderStateEntityBuilder remainingQty(String remainingQty) {
            this.remainingQty = remainingQty;
            return this;
        }

        public OrderStateEntityBuilder lastQty(String lastQty) {
            this.lastQty = lastQty;
            return this;
        }

        public OrderStateEntityBuilder avgPrice(String avgPrice) {
            this.avgPrice = avgPrice;
            return this;
        }

        public OrderStateEntityBuilder lastPrice(String lastPrice) {
            this.lastPrice = lastPrice;
            return this;
        }

        public OrderStateEntityBuilder reason(String reason) {
            this.reason = reason;
            return this;
        }

        public OrderStateEntityBuilder side(String side) {
            this.side = side;
            return this;
        }

        public OrderStateEntityBuilder instrumentId(String instrumentId) {
            this.instrumentId = instrumentId;
            return this;
        }

        public OrderStateEntityBuilder venueTimestamp(OffsetDateTime venueTimestamp) {
            this.venueTimestamp = venueTimestamp;
            return this;
        }

        public OrderStateEntityBuilder createdAt(OffsetDateTime createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public OrderStateEntityBuilder updatedAt(OffsetDateTime updatedAt) {
            this.updatedAt = updatedAt;
            return this;
        }

        public OrderStateEntityBuilder lastRequestResult(String lastRequestResult) {
            this.lastRequestResult = lastRequestResult;
            return this;
        }

        public OrderStateEntityBuilder sequenceNumber(Integer sequenceNumber) {
            this.sequenceNumber = sequenceNumber;
            return this;
        }

        public OrderStateEntityBuilder expireTime(OffsetDateTime expireTime) {
            this.expireTime = expireTime;
            return this;
        }

        public OrderStateEntityBuilder orderCategory(String orderCategory) {
            this.orderCategory = orderCategory;
            return this;
        }

        public OrderStateEntityBuilder parentOrderId(String parentOrderId) {
            this.parentOrderId = parentOrderId;
            return this;
        }

        public OrderStateEntityBuilder orderType(String orderType) {
            this.orderType = orderType;
            return this;
        }

        public OrderStateEntityBuilder symbol(String symbol) {
            this.symbol = symbol;
            return this;
        }

        public OrderStateEntityBuilder instrumentType(String instrumentType) {
            this.instrumentType = instrumentType;
            return this;
        }

        public OrderStateEntityBuilder venueAccounts(List<String> venueAccounts) {
            this.venueAccounts = venueAccounts;
            return this;
        }

        public OrderStateEntityBuilder underlyingVenueAccount(String underlyingVenueAccount) {
            this.underlyingVenueAccount = underlyingVenueAccount;
            return this;
        }

        public OrderStateEntityBuilder rootOrderId(String rootOrderId) {
            this.rootOrderId = rootOrderId;
            return this;
        }

        public OrderStateEntityBuilder counterPortfolioId(String counterPortfolioId) {
            this.counterPortfolioId = counterPortfolioId;
            return this;
        }

        public OrderStateEntityBuilder portfolioType(String portfolioType) {
            this.portfolioType = portfolioType;
            return this;
        }

        public OrderStateEntityBuilder counterPortfolioType(String counterPortfolioType) {
            this.counterPortfolioType = counterPortfolioType;
            return this;
        }

        public OrderStateEntityBuilder extOrderId(String extOrderId) {
            this.extOrderId = extOrderId;
            return this;
        }

        public OrderStateEntity build() {
            return new OrderStateEntity(id, orderId, origOrderId, clientId, clOrderId, origClOrderId, portfolioId, orderStatus, orderQty, currency, limitPrice,
                stopPrice, tif, filledQty, remainingQty, lastQty, avgPrice, lastPrice, reason, side, instrumentId,
                venueTimestamp, createdAt, updatedAt, lastRequestResult, sequenceNumber, expireTime, orderCategory, parentOrderId,
                orderType, symbol, instrumentType, venueAccounts, underlyingVenueAccount, rootOrderId, counterPortfolioId, portfolioType, counterPortfolioType, extOrderId);
        }
    }
}

