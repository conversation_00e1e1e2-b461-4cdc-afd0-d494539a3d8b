package io.wyden.orderhistory.model;

import java.time.OffsetDateTime;

@Deprecated
public record CancelReplaceRequestEntity(
    String newOrderId,
    String origOrderId,
    OffsetDateTime createdAt, // ISO 8601 format, UTC
    byte[] cancelReplaceRequest
) {

    public static final class CancelReplaceRequestEntityBuilder {
        private String newOrderId;
        private String origOrderId;
        private OffsetDateTime createdAt;
        private byte[] cancelReplaceRequest;

        public CancelReplaceRequestEntityBuilder() {
        }

        private CancelReplaceRequestEntityBuilder(CancelReplaceRequestEntity other) {
            this.newOrderId = other.newOrderId();
            this.origOrderId = other.origOrderId();
            this.createdAt = other.createdAt();
            this.cancelReplaceRequest = other.cancelReplaceRequest();
        }

        public static CancelReplaceRequestEntityBuilder builder() {
            return new CancelReplaceRequestEntityBuilder();
        }

        public static CancelReplaceRequestEntityBuilder toBuilder(CancelReplaceRequestEntity other) {
            return new CancelReplaceRequestEntityBuilder(other);
        }

        public CancelReplaceRequestEntityBuilder newOrderId(String newOrderId) {
            this.newOrderId = newOrderId;
            return this;
        }

        public CancelReplaceRequestEntityBuilder origOrderId(String origOrderId) {
            this.origOrderId = origOrderId;
            return this;
        }

        public CancelReplaceRequestEntityBuilder createdAt(OffsetDateTime createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public CancelReplaceRequestEntityBuilder cancelReplaceRequest(byte[] cancelReplaceRequest) {
            this.cancelReplaceRequest = cancelReplaceRequest;
            return this;
        }

        public CancelReplaceRequestEntity build() {
            return new CancelReplaceRequestEntity(newOrderId, origOrderId, createdAt, cancelReplaceRequest);
        }
    }
}
