package io.wyden.orderhistory.model;

import java.util.Collection;

public record CollectionPredicateInput(PredicateType method, Field field, Collection<String> value) implements PredicateInput{
    private static final String VALUES_PLACEHOLDER = "ARRAY[?]";
    private static final String ACCEPT_NULL_VALUES = "%s IS NULL OR";
    private static final String CONDITION_STRING = "%s %s";
    private static final String CONDITION_WITH_NULL_VALUES_STRING = "(%s %s %s)";

    public enum PredicateType {
        IN("= ANY(%s)");

        final String operator;

        PredicateType(String operator) {
            this.operator = operator;
        }
    }

    public enum Field {
        INSTRUMENT_ID,
        ORDER_ID,
        CL_ORDER_ID,
        ORDER_STATUS,
        ORDER_CATEGORY,
        PORTFOLIO_ID,
        PARENT_ORDER_ID,
        VENUE_ACCOUNT_ID("venue_accounts", "::text[] && (%s)", true);

        final String columnName;
        final String operatorSuffix;
        final boolean arrayField;

        Field() {
            this.columnName = name();
            this.operatorSuffix = VALUES_PLACEHOLDER;
            this.arrayField = false;
        }

        Field(String columnName, String operatorSuffix, boolean arrayField) {
            this.columnName = columnName;
            this.operatorSuffix = operatorSuffix;
            this.arrayField = arrayField;
        }
    }

    @Override
    public String fieldName() {
        return field.name();
    }

    @Override
    public String fieldColumnName() {
        return field.columnName.toLowerCase();
    }

    public boolean isArrayField() {
        return field.arrayField;
    }

    public String asPostgresCondition() {
        if(field.arrayField) {
            return String.format(CONDITION_STRING, VALUES_PLACEHOLDER, String.format(this.field().operatorSuffix, fieldColumnName()));
        } else {
            return String.format(CONDITION_STRING, fieldColumnName(), String.format(method().operator, field().operatorSuffix));
        }
    }

    @Override
    public String toString() {
        return "CollectionPredicateInput{" +
            "method=" + method +
            ", field=" + field +
            ", value=" + value +
            '}';
    }
}
