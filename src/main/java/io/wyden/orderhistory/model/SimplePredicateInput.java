package io.wyden.orderhistory.model;

public record SimplePredicateInput(PredicateType method, Field field, String value) implements PredicateInput {
    private static final String VALUE_PLACEHOLDER = "?";
    private static final String ACCEPT_NULL_VALUES = "%s IS NULL OR";
    private static final String CONDITION_STRING = "%s %s %s";
    private static final String CONDITION_WITH_NULL_VALUES_STRING = "(%s %s %s %s)";

    public enum PredicateType {
        CONTAINS("ILIKE"),
        EQUAL("="),
        NOT_EQUAL("!=");

        final String operator;

        PredicateType(String operator) {
            this.operator = operator;
        }
    }

    public enum Field {
        PORTFOLIO_ID,
        PORTFOLIO_TYPE("portfolio_type", true),
        VENUE_ACCOUNT_ID("venue_accounts", "ANY (%s)", true),
        INSTRUMENT_ID,
        ORDER_ID,
        CL_ORDER_ID,
        TIF,
        RESULT("last_request_result"),
        R<PERSON>SO<PERSON>,
        ORDER_STATUS,
        PARENT_ORDER_ID,
        ROOT_ORDER_ID,
        EXT_ORDER_ID;

        final String columnName;
        final String operatorSuffix;
        final boolean arrayField;
        final boolean acceptNullValues;

        Field() {
            this.columnName = name();
            this.operatorSuffix = VALUE_PLACEHOLDER;
            this.arrayField = false;
            this.acceptNullValues = false;
        }

        Field(String columnName) {
            this.columnName = columnName;
            this.operatorSuffix = VALUE_PLACEHOLDER;
            this.arrayField = false;
            this.acceptNullValues = false;
        }

        Field(String columnName, boolean acceptNullsValues) {
            this.columnName = columnName;
            this.operatorSuffix = VALUE_PLACEHOLDER;
            this.arrayField = false;
            this.acceptNullValues = acceptNullsValues;
        }
        Field(String columnName, String operatorSuffix, boolean arrayField) {
            this.columnName = columnName;
            this.operatorSuffix = operatorSuffix;
            this.arrayField = arrayField;
            this.acceptNullValues = false;
        }
    }

    @Override
    public String fieldName() {
        return field.name();
    }

    @Override
    public String fieldColumnName() {
        return field.columnName.toLowerCase();
    }

    public boolean isArrayField() {
        return field.arrayField;
    }

    public boolean acceptNullValues() {
        return field.acceptNullValues;
    }

    public String asPostgresCondition() {
        if(field.arrayField) {
            return String.format(CONDITION_STRING, VALUE_PLACEHOLDER, this.method().operator, String.format(this.field().operatorSuffix, fieldColumnName()));
        } else if (field().acceptNullValues) {
            return String.format(CONDITION_WITH_NULL_VALUES_STRING, String.format(ACCEPT_NULL_VALUES, fieldColumnName()), fieldColumnName(), method().operator, field().operatorSuffix);
        } else {
            return String.format(CONDITION_STRING, fieldColumnName(), method().operator, field().operatorSuffix);
        }
    }

    @Override
    public String toString() {
        return "SimplePredicateInput{" +
            "method=" + method +
            ", field=" + field +
            ", value='" + value + '\'' +
            '}';
    }
}
