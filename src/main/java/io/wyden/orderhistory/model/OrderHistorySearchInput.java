package io.wyden.orderhistory.model;

import jakarta.annotation.Nullable;

import java.util.Collection;

public record OrderHistorySearchInput(
    Collection<SimplePredicateInput> simplePredicates,
    Collection<CollectionPredicateInput> collectionPredicates,
    Collection<DatePredicateInput> datePredicateInputs,
    @Nullable Integer first,
    @Nullable String after,
    @Nullable SortingOrder sortingOrder,
    @Nullable SortingField sortingField) {

    /**
     * Creates a new instance of {@link OrderHistorySearchInput} using the existing predicate
     * inputs and sorting order but resets the cursor (after) to null.
     */
    public OrderHistorySearchInput withoutCursor() {
        return new OrderHistorySearchInput(
            simplePredicates(),
            collectionPredicates(),
            datePredicateInputs(),
            first(),
            null,
            sortingOrder(),
            sortingField());
    }

    public OrderHistorySearchInput withSortingField(SortingField sortingField) {
        return new OrderHistorySearchInput(
            simplePredicates(),
            collectionPredicates(),
            datePredicateInputs(),
            first(),
            after(),
            sortingOrder(),
            sortingField);
    }
}
