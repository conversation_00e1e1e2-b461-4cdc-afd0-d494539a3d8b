package io.wyden.orderhistory.model;

import jakarta.validation.constraints.NotNull;

import java.time.OffsetDateTime;

public record DatePredicateInput(PredicateType method, Field field, @NotNull String value) implements PredicateInput {
    private static final String VALUE_PLACEHOLDER = "?";
    private static final String CONDITION_STRING = "%s %s %s";

    public enum PredicateType {
        FROM(">="),
        TO("<="),
        AFTER(">"),
        BEFORE("<");

        final String operator;

        PredicateType(String operator) {
            this.operator = operator;
        }
    }

    public enum Field {
        CREATED_AT,
        UPDATED_AT
    }

    @Override
    public String fieldName() {
        return field.name();
    }

    @Override
    public String fieldColumnName() {
        return fieldName().toLowerCase();
    }

    public String asPostgresCondition() {
        return String.format(CONDITION_STRING, fieldName().toLowerCase(), method().operator, VALUE_PLACEHOLDER);
    }

    public OffsetDateTime valueAsOffsetDateTime() {
        return OffsetDateTime.parse(value);
    }

    @Override
    public String toString() {
        return "DatePredicateInput{" +
            "method=" + method +
            ", field=" + field +
            ", value=" + value +
            '}';
    }
}
