package io.wyden.orderhistory.model;

public enum SortingField {
    CREATED_AT("created_at", "createdAt"),
    UPDATED_AT("updated_at", "updatedAt"),
    MESSAGE_TIMESTAMP("message_timestamp", "messageTimestamp"),
    LATEST_MESSAGE_TIMESTAMP("latest_message_timestamp", "latestMessageTimestamp");

    final String columnName;
    final String fieldName;

    SortingField(String columnName, String fieldName) {
        this.columnName = columnName;
        this.fieldName = fieldName;
    }

    public String columnName() {
        return columnName;
    }

    public String fieldName() {
        return fieldName;
    }
}
