package io.wyden.orderhistory.service.inbound;

import com.rabbitmq.client.AMQP;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.orderhistory.service.OrderHistoryService;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientRequestType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static io.wyden.orderhistory.infrastructure.telemetry.Meters.tradingClientRequestIncomingLatencyTimer;

@Deprecated
@Component
public class ClientRequestHandler implements MessageConsumer<ClientRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClientRequestHandler.class);

    private final OrderHistoryService orderHistoryService;
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public ClientRequestHandler(OrderHistoryService orderHistoryService,
                                Tracing otlTracing,
                                MeterRegistry meterRegistry) {
        this.orderHistoryService = orderHistoryService;
        this.otlTracing = otlTracing;
        this.meterRegistry = meterRegistry;
    }

    @Override
    public ConsumptionResult consume(ClientRequest clientRequest, AMQP.BasicProperties properties) {
        try (var ignore = otlTracing.createSpan("clientrequest.consume", SpanKind.INTERNAL)) {
            consumeInner(clientRequest);
            return ConsumptionResult.consumed();
        } catch (Exception e) {
            LOGGER.error("Exception when consuming Client Request: {}", e.getMessage(), e);
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    private void consumeInner(ClientRequest clientRequest) {
        LOGGER.info("Received ClientRequest (clientId:{}, orderId:{}, origOrderId:{})", clientRequest.getClientId(), clientRequest.getOrderId(), clientRequest.getOrigOrderId());
        ClientRequestType requestType = clientRequest.getRequestType();
        switch (requestType) {
            case ORDER_SINGLE -> recordLatencyIn(latencyTimer(requestType)).of(() -> orderHistoryService.processClientNewOrderRequest(clientRequest));
            case CANCEL_REPLACE -> recordLatencyIn(latencyTimer(requestType)).of(() -> orderHistoryService.processClientCancelReplaceRequest(clientRequest));
            case CANCEL -> LOGGER.debug("Received ClientRequest of type CANCEL");
            default -> LOGGER.debug("ClientRequest of type %s not supported".formatted(requestType));
        }
    }

    protected Timer latencyTimer(ClientRequestType requestType) {
        try {
            return tradingClientRequestIncomingLatencyTimer(meterRegistry, requestType);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}
