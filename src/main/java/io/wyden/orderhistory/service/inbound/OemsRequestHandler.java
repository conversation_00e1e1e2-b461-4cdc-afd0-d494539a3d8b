package io.wyden.orderhistory.service.inbound;

import com.rabbitmq.client.AMQP;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.orderhistory.service.OrderHistoryService;
import io.wyden.published.oems.OemsRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static io.wyden.orderhistory.infrastructure.telemetry.Meters.tradingOemsRequestIncomingLatencyTimer;

@Deprecated
@Component
public class OemsRequestHandler implements MessageConsumer<OemsRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OemsRequestHandler.class);

    private final OrderHistoryService orderHistoryService;
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public OemsRequestHandler(OrderHistoryService orderHistoryService,
                              Tracing otlTracing,
                              MeterRegistry meterRegistry) {
        this.orderHistoryService = orderHistoryService;
        this.otlTracing = otlTracing;
        this.meterRegistry = meterRegistry;
    }

    @Override
    public ConsumptionResult consume(OemsRequest oemsRequest, AMQP.BasicProperties properties) {
        try (var ignore = otlTracing.createSpan("oemsrequest.consume", SpanKind.INTERNAL)) {
            consumeInner(oemsRequest);
            return ConsumptionResult.consumed();
        } catch (Exception e) {
            LOGGER.error("Exception when consuming Oems Request: {}", e.getMessage(), e);
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    private void consumeInner(OemsRequest oemsRequest) {
        LOGGER.info("Received OemsRequest (clientId:{}, orderId:{}, parentOrderId:{})", oemsRequest.getClientId(), oemsRequest.getOrderId(), oemsRequest.getParentOrderId());
        OemsRequest.OemsRequestType requestType = oemsRequest.getRequestType();
        switch (requestType) {
            case ORDER_SINGLE -> recordLatencyIn(latencyTimer(requestType)).of(() -> orderHistoryService.processOemsRequest(oemsRequest));
            case CANCEL -> LOGGER.debug("Received OemsRequest of type CANCEL");
            default -> LOGGER.debug("OemsRequest of type %s not supported".formatted(requestType));
        }
    }

    protected Timer latencyTimer(OemsRequest.OemsRequestType requestType) {
        try {
            return tradingOemsRequestIncomingLatencyTimer(meterRegistry, requestType);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}
