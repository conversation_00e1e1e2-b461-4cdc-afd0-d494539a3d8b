package io.wyden.orderhistory.service.inbound;

import com.rabbitmq.client.AMQP;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.orderhistory.service.OrderHistoryService;
import io.wyden.published.oems.OemsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static io.wyden.orderhistory.infrastructure.telemetry.Meters.tradingOemsResponseIncomingLatencyTimer;

@Deprecated
@Component
public class OemsResponseHandler implements MessageConsumer<OemsResponse> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OemsResponseHandler.class);

    private final OrderHistoryService orderHistoryService;
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public OemsResponseHandler(OrderHistoryService orderHistoryService,
                               Tracing otlTracing,
                               MeterRegistry meterRegistry) {
        this.orderHistoryService = orderHistoryService;
        this.otlTracing = otlTracing;
        this.meterRegistry = meterRegistry;
    }

    @Override
    public ConsumptionResult consume(OemsResponse oemsResponse, AMQP.BasicProperties properties) {
        try (var ignore = otlTracing.createSpan("oemsresponse.consume", SpanKind.INTERNAL)) {
            consumeInner(oemsResponse);
            return ConsumptionResult.consumed();
        } catch (Exception e) {
            LOGGER.error("Exception when consuming Oems Response: {}", e.getMessage(), e);
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    private void consumeInner(OemsResponse oemsResponse) {
        LOGGER.info("Received OemsResponse (clientId:{}, orderId:{}, parentOrderId:{})", oemsResponse.getClientId(), oemsResponse.getOrderId(), oemsResponse.getParentOrderId());
        OemsResponse.OemsResponseType responseType = oemsResponse.getResponseType();
        switch (responseType) {
            case EXECUTION_REPORT -> recordLatencyIn(latencyTimer(responseType)).of(() -> orderHistoryService.processOemsResponse(oemsResponse));
            case CANCEL_REJECT -> LOGGER.debug("Received OemsResponse of type CANCEL_REJECT");
            default -> LOGGER.debug("OemsResponse of type %s not supported".formatted(responseType));
        }
    }

    protected Timer latencyTimer(OemsResponse.OemsResponseType responseType) {
        try {
            return tradingOemsResponseIncomingLatencyTimer(meterRegistry, responseType);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}
