package io.wyden.orderhistory.service.inbound;

import com.rabbitmq.client.AMQP;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.orderhistory.service.OrderHistoryService;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientResponseType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static io.wyden.orderhistory.infrastructure.telemetry.Meters.tradingClientResponseIncomingLatencyTimer;

@Deprecated
@Component
public class ClientResponseHandler implements MessageConsumer<ClientResponse> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClientResponseHandler.class);

    private final OrderHistoryService orderHistoryService;
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public ClientResponseHandler(OrderHistoryService orderHistoryService,
                                 Tracing otlTracing,
                                 Telemetry telemetry) {
        this.orderHistoryService = orderHistoryService;
        this.otlTracing = otlTracing;
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    @Override
    public ConsumptionResult consume(ClientResponse clientResponse, AMQP.BasicProperties properties) {
        try (var ignore = otlTracing.createSpan("clientresponse.consume", SpanKind.INTERNAL)) {
            consumeInner(clientResponse);
            return ConsumptionResult.consumed();
        } catch (Exception e) {
            LOGGER.error("Exception when consuming Client Response: {}", e.getMessage(), e);
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    private void consumeInner(ClientResponse clientResponse) {
        LOGGER.info("Received ClientResponse (clientId:{}, orderId:{}, origOrderId:{})", clientResponse.getClientId(), clientResponse.getOrderId(), clientResponse.getOrigOrderId());
        ClientResponseType responseType = clientResponse.getResponseType();
        switch (responseType) {
            case EXECUTION_REPORT -> recordLatencyIn(latencyTimer(responseType)).of(() -> orderHistoryService.processClientExecutionReport(clientResponse));
            case CANCEL_REJECT -> recordLatencyIn(latencyTimer(responseType)).of(() -> orderHistoryService.processClientCancelReject(clientResponse));
            default -> LOGGER.debug("ClientResponse of type %s not supported".formatted(responseType));
        }
    }

    protected Timer latencyTimer(ClientResponseType responseType) {
        try {
            return tradingClientResponseIncomingLatencyTimer(meterRegistry, responseType);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}
