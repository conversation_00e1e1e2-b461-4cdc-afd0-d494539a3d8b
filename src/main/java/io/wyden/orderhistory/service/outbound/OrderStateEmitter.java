package io.wyden.orderhistory.service.outbound;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.published.reporting.OrderState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class OrderStateEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStateEmitter.class);

    private final RabbitExchange<OrderState> orderStateExchange;

    public OrderStateEmitter(RabbitExchange<OrderState> orderStateExchange) {
        this.orderStateExchange = orderStateExchange;
    }

    public void emit(OrderState orderState) {
        Map<String, String> headers = Map.of(OemsHeader.MESSAGE_TYPE.getHeaderName(), OrderState.class.getSimpleName());
        LOGGER.info("Emitting OrderState: \n{}", orderState);
        this.orderStateExchange.publishWithHeaders(orderState, headers);
    }
}
