package io.wyden.orderhistory.service.utils;

import com.google.common.collect.Ordering;
import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.checkerframework.checker.nullness.qual.Nullable;

@Deprecated
public final class OrderStateComparator {

    private static final Ordering<@Nullable String> STRING_COMPARATOR = Ordering.natural().nullsLast();

    private OrderStateComparator() {
    }

    //TODO FIXME !!!
    public static boolean hasChanged(OrderStateEntity current, ClientResponse response) {
        return true;
        /*return ComparisonChain.start()
            .compare(current.orderStatus(), clientToOrderStatus(response.getOrderStatus()).name(), STRING_COMPARATOR)
            .compare(current.orderQty(), response.getOrderQty(), STRING_COMPARATOR)
            .compare(current.filledQty(), response.getCumQty(), STRING_COMPARATOR)
            .compare(current.remainingQty(), response.getLeavesQty(), STRING_COMPARATOR)
            .compare(current.lastQty(), response.getLastQty(), STRING_COMPARATOR)
            .compare(current.avgPrice(), response.getAvgPrice(), STRING_COMPARATOR)
            .compare(current.lastPrice(), response.getLastPrice(), STRING_COMPARATOR)
            .compare(current.reason(), response.getReason(), STRING_COMPARATOR)
            .compare(current.lastRequestResult(), response.getRequestResult().getValueDescriptor().getName(), STRING_COMPARATOR)
            .compare(current.underlyingVenueAccount(), response.getUnderlyingVenueAccount(), STRING_COMPARATOR)
            .compare(current.counterPortfolioId(), response.getCounterPortfolioId(), STRING_COMPARATOR)
            .compare(current.currency(), StringUtils.isNotEmpty(response.getCurrency()) ? response.getCurrency() : current.currency(), STRING_COMPARATOR)
            .result() != 0;*/
    }

    public static boolean hasChanged(OrderStateEntity current, OemsRequest request) {
        return true;
        /*return ComparisonChain.start()
            .compare(current.remainingQty(), request.getQuantity(), STRING_COMPARATOR)
            .compare(current.currency(), StringUtils.firstNonEmpty(request.getCurrency(), request.getBaseCurrency()), STRING_COMPARATOR)
            .compare(current.orderCategory(), request.getOrderCategory().name(), STRING_COMPARATOR)
            .compare(current.parentOrderId(), request.getParentOrderId(), STRING_COMPARATOR)
            .compare(current.rootOrderId(), request.getRootOrderId(), STRING_COMPARATOR)
            .compare(current.expireTime(), mapExpireTime(request.getExpireTime()), Ordering.natural().nullsLast())
            .result() != 0;*/
    }

    public static boolean hasChanged(OrderStateEntity current, OemsResponse response) {
        return true;
        /*return ComparisonChain.start()
            .compare(current.orderStatus(), oemsToOrderStatus(response.getOrderStatus()).name(), STRING_COMPARATOR)
            .compare(current.orderQty(), response.getOrderQty(), STRING_COMPARATOR)
            .compare(current.filledQty(), response.getCumQty(), STRING_COMPARATOR)
            .compare(current.remainingQty(), response.getLeavesQty(), STRING_COMPARATOR)
            .compare(current.lastQty(), response.getLastQty(), STRING_COMPARATOR)
            .compare(current.avgPrice(), response.getAvgPrice(), STRING_COMPARATOR)
            .compare(current.lastPrice(), response.getLastPrice(), STRING_COMPARATOR)
            .compare(current.reason(), response.getReason(), STRING_COMPARATOR)
            .compare(current.lastRequestResult(), response.getRequestResult().getValueDescriptor().getName(), STRING_COMPARATOR)
            .result() != 0;*/
    }
}
