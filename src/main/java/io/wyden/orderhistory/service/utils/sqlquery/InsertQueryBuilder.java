package io.wyden.orderhistory.service.utils.sqlquery;

import org.apache.logging.log4j.util.Strings;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.Objects;
import java.util.StringJoiner;

@Deprecated
public final class InsertQueryBuilder extends AbstractQueryBuilder {

    private final StringJoiner columns = new StringJoiner(", ", "(", ")");
    private final StringJoiner valuesPlaceholder = new StringJoiner(", ", "(", ")");

    public InsertQueryBuilder(String tableName) {
        super(tableName);
    }

    public static InsertQueryBuilder builder(String tableName) {
        return new InsertQueryBuilder(tableName);
    }

    public static InsertQueryBuilder builder(String tableName, String id) {
        InsertQueryBuilder insertQueryBuilder = new InsertQueryBuilder(tableName);
        insertQueryBuilder.addColumn("id");
        insertQueryBuilder.addValue(id);
        return insertQueryBuilder;
    }

    @Override
    public InsertQueryBuilder addParamIfFieldExist(String paramName, String paramValue) {
        if (Strings.isNotEmpty(paramValue)) {
            addColumn(paramName);
            addValuePlaceholder();
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public InsertQueryBuilder addParamIfFieldExist(String paramName, Integer paramValue) {
        if (QueryUtils.isNotNullAndGreaterThatZero(paramValue)) {
            addColumn(paramName);
            addValuePlaceholder();
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public InsertQueryBuilder addParamIfFieldExist(String paramName, OffsetDateTime paramValue) {
        if (Objects.nonNull(paramValue)) {
            addColumn(paramName);
            addValuePlaceholder();
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public InsertQueryBuilder addParamIfFieldExist(String paramName, Collection<String> paramValue) {
        if (Objects.nonNull(paramValue) && !paramValue.isEmpty()) {
            addColumn(paramName);
            addValuePlaceholder();
            addValue(paramValue.toArray(String[]::new));
        }
        return this;
    }

    @Override
    public InsertQueryBuilder addParamIfFieldExist(String paramName, byte[] paramValue) {
        if (Objects.nonNull(paramValue)) {
            addColumn(paramName);
            addValuePlaceholder();
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public InsertQueryBuilder addWhereCondition(String paramName, String paramValue) {
        throw new UnsupportedOperationException("Insert query builder does not support where clause");
    }

    @Override
    public InsertQueryBuilder addWhereCondition(String paramName, Collection<String> paramValue) {
        throw new UnsupportedOperationException("Insert query builder does not support where clause");
    }

    @Override
    public InsertQueryBuilder addWhereCondition(String paramName, OffsetDateTime paramValue) {
        throw new UnsupportedOperationException("Insert query builder does not support where clause");
    }

    @Override
    public InsertQueryBuilder addSortingOrder(String paramName, String paramValue) {
        throw new UnsupportedOperationException("Insert query builder does not support sorting order");
    }

    @Override
    public InsertQueryBuilder addFetchFirst(Integer paramValue) {
        throw new UnsupportedOperationException("Insert query builder does not support fetch first");
    }

    public SqlQuery build() {
        String query = String.format("INSERT INTO %s %s VALUES %s", tableName, columns, valuesPlaceholder);
        return new SqlQuery(query, values.toArray());
    }

    private void addColumn(String column) {
        this.columns.add(column);
    }

    private void addValuePlaceholder() {
        this.valuesPlaceholder.add("?");
    }

    private void addValue(Object value) {
        this.values.add(value);
    }
}
