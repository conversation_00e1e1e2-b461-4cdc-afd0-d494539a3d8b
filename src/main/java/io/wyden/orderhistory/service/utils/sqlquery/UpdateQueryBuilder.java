package io.wyden.orderhistory.service.utils.sqlquery;

import org.apache.logging.log4j.util.Strings;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.Objects;
import java.util.StringJoiner;

@Deprecated
public final class UpdateQueryBuilder extends AbstractQueryBuilder {

    private final StringJoiner columns = new StringJoiner(", ");
    private final StringJoiner whereClause = new StringJoiner("", " AND ", "");

    public UpdateQueryBuilder(String tableName) {
        super(tableName);
    }

    public static UpdateQueryBuilder builder(String tableName) {
        return new UpdateQueryBuilder(tableName);
    }

    @Override
    public UpdateQueryBuilder addParamIfFieldExist(String paramName, String paramValue) {
        if (Strings.isNotEmpty(paramValue)) {
            addColumn(paramName);
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public UpdateQueryBuilder addParamIfFieldExist(String paramName, Integer paramValue) {
        if (QueryUtils.isNotNullAndGreaterThatZero(paramValue)) {
            addColumn(paramName);
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public UpdateQueryBuilder addParamIfFieldExist(String paramName, OffsetDateTime paramValue) {
        if (Objects.nonNull(paramValue)) {
            addColumn(paramName);
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public UpdateQueryBuilder addParamIfFieldExist(String paramName, Collection<String> paramValue) {
        if (!paramValue.isEmpty()) {
            addColumn(paramName);
            addValue(paramValue.toArray(String[]::new));
        }
        return this;
    }

    @Override
    public UpdateQueryBuilder addParamIfFieldExist(String paramName, byte[] paramValue) {
        if (Objects.nonNull(paramValue)) {
            addColumn(paramName);
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public UpdateQueryBuilder addWhereCondition(String paramName, String paramValue) {
        if (Strings.isNotEmpty(paramValue)) {
            addWhereCondition(paramName);
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public UpdateQueryBuilder addWhereCondition(String paramName, Collection<String> paramValue) {
        if (!paramValue.isEmpty()) {
            addWhereCondition(paramName);
            addValue(paramValue.toArray(String[]::new));
        }
        return this;
    }

    @Override
    public UpdateQueryBuilder addWhereCondition(String paramName, OffsetDateTime paramValue) {
        if (Objects.nonNull(paramValue)) {
            addWhereCondition(paramName);
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public UpdateQueryBuilder addSortingOrder(String paramName, String paramValue) {
        throw new UnsupportedOperationException("Update query builder does not support sorting order");
    }

    @Override
    public UpdateQueryBuilder addFetchFirst(Integer paramValue) {
        throw new UnsupportedOperationException("Update query builder does not support fetch first");
    }

    private void addColumn(String column) {
        this.columns.add(String.join(" ", column, "=", "?"));
    }

    private void addValue(Object value) {
        this.values.add(value);
    }

    private void addWhereCondition(String column) {
        this.whereClause.add(String.join(" ", column, "=", "?"));
    }

    public SqlQuery build() {
        String query = String.format("UPDATE %s SET %s WHERE 1=1 %s", tableName, columns, whereClause);
        return new SqlQuery(query, values.toArray());
    }
}
