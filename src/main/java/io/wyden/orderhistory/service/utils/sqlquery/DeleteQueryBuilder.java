package io.wyden.orderhistory.service.utils.sqlquery;

import org.apache.logging.log4j.util.Strings;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.StringJoiner;

@Deprecated
public final class DeleteQueryBuilder extends AbstractQueryBuilder {

    private final StringJoiner whereClause = new StringJoiner(" ", " ", "");

    public DeleteQueryBuilder(String tableName) {
        super(tableName);
    }

    public static DeleteQueryBuilder builder(String tableName) {
        return new DeleteQueryBuilder(tableName);
    }

    @Override
    public DeleteQueryBuilder addParamIfFieldExist(String paramName, String paramValue) {
        throw new UnsupportedOperationException("Delete query builder does not support params");
    }

    @Override
    public DeleteQueryBuilder addParamIfFieldExist(String paramName, Integer paramValue) {
        throw new UnsupportedOperationException("Delete query builder does not support params");
    }

    @Override
    public DeleteQueryBuilder addParamIfFieldExist(String paramName, OffsetDateTime paramValue) {
        throw new UnsupportedOperationException("Delete query builder does not support params");
    }

    @Override
    public DeleteQueryBuilder addParamIfFieldExist(String paramName, Collection<String> paramValue) {
        throw new UnsupportedOperationException("Delete query builder does not support params");
    }

    @Override
    public DeleteQueryBuilder addParamIfFieldExist(String paramName, byte[] paramValue) {
        throw new UnsupportedOperationException("Delete query builder does not support params");
    }

    @Override
    public DeleteQueryBuilder addWhereCondition(String condition, String paramValue) {
        if (Strings.isNotEmpty(paramValue)) {
            addWhereCondition(condition);
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public DeleteQueryBuilder addWhereCondition(String condition, Collection<String> paramValue) {
        throw new UnsupportedOperationException("Delete query builder does not support where clause");
    }

    @Override
    public DeleteQueryBuilder addWhereCondition(String paramName, OffsetDateTime paramValue) {
        throw new UnsupportedOperationException("Delete query builder does not support where clause");
    }

    @Override
    public DeleteQueryBuilder addSortingOrder(String paramName, String paramValue) {
        throw new UnsupportedOperationException("Delete query builder does not support sorting order");
    }

    @Override
    public DeleteQueryBuilder addFetchFirst(Integer paramValue) {
        throw new UnsupportedOperationException("Delete query builder does not support fetch first");
    }

    public SqlQuery build() {
        String queryBuilder = String.format("DELETE FROM %s WHERE 1=1%s", tableName, whereClause);
        return new SqlQuery(queryBuilder, values.toArray());
    }

    private void addValue(Object value) {
        this.values.add(value);
    }

    private void addWhereCondition(String condition) {
        this.whereClause.add("AND " + condition);
    }
}
