package io.wyden.orderhistory.service.utils;

import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.PageInfo;

import java.util.Collection;
import java.util.List;
import java.util.function.Function;

public final class Paging {

    private Paging() {
    }

    public static <T> CursorConnection wrap(Collection<T> collection,
                                            Function<T, String> cursorExtractor,
                                            Function<T, CursorNode> nodeExtractor,
                                            boolean hasNextPage) {
        List<CursorEdge> cursorEdges = collection.stream()
            .map(item -> CursorEdge.newBuilder()
                .setCursor(cursorExtractor.apply(item))
                .setNode(nodeExtractor.apply(item))
                .build())
            .toList();

        String endCursor = getLastCursor(cursorEdges);
        PageInfo pageInfo = PageInfo.newBuilder()
            .setEndCursor(endCursor)
            .setHasNextPage(hasNextPage)
            .setPageSize(cursorEdges.size())
            .setTotalSizeStatus(PageInfo.TotalSizeStatus.SIZE_KNOWN)
            .build();

        return CursorConnection.newBuilder()
            .addAllEdges(cursorEdges)
            .setPageInfo(pageInfo)
            .build();
    }

    private static String getLastCursor(List<CursorEdge> securityEdges) {
        CursorEdge lastSecurity = Paging.getLast(securityEdges);
        if (lastSecurity == null) {
            return "";
        }
        return lastSecurity.getCursor();
    }

    private static <T> T getLast(List<T> list) {
        if (list.isEmpty()) {
            return null;
        }
        return list.getLast();
    }
}
