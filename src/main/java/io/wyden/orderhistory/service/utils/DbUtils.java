package io.wyden.orderhistory.service.utils;

import io.wyden.orderhistory.model.CancelReplaceRequestEntity;
import io.wyden.orderhistory.model.CollectionPredicateInput;
import io.wyden.orderhistory.model.DatePredicateInput;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.orderhistory.model.SimplePredicateInput;
import io.wyden.orderhistory.model.SortingOrder;
import io.wyden.orderhistory.service.utils.sqlquery.DeleteQueryBuilder;
import io.wyden.orderhistory.service.utils.sqlquery.InsertQueryBuilder;
import io.wyden.orderhistory.service.utils.sqlquery.SelectQueryBuilder;
import io.wyden.orderhistory.service.utils.sqlquery.SqlQuery;
import io.wyden.orderhistory.service.utils.sqlquery.UpdateQueryBuilder;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public final class DbUtils {

    @Deprecated private static final String ORDER_STATE = "order_state";
    @Deprecated private static final String ORDER_STATE_SNAPSHOT = "order_state_snapshot";
    @Deprecated private static final String CANCEL_REPLACE_REQUEST_CACHE = "cancel_replace_request_cache";
    @Deprecated private static final String ORDER_BY_UPDATED_AT = DatePredicateInput.Field.UPDATED_AT.name();
    @Deprecated private static final String ORDER_BY_SEQUENCE_NUMBER = "sequence_number";

    private DbUtils() {
        // empty
    }

    public static OffsetDateTime parseUnifiedDate(String value) {
        String unifiedDate = unifyDate(value);
        return OffsetDateTime.parse(unifiedDate);
    }

    private static String unifyDate(String value) {
        ZonedDateTime zonedDateTime;
        if (StringUtils.isNotEmpty(value)) {
            try {
                Instant instantFromEpoch = parseInstant(value);
                zonedDateTime = ZonedDateTime.ofInstant(instantFromEpoch, ZoneId.of("Z"));
            } catch (NumberFormatException e) {
                try {
                    zonedDateTime = ZonedDateTime.parse(value, DateTimeFormatter.ISO_DATE_TIME);
                } catch (Exception exception) {
                    zonedDateTime = ZonedDateTime.parse("1970-01-01T00:00:00.000000+00:00");
                }
            }
        } else {
            zonedDateTime = ZonedDateTime.parse("1970-01-01T00:00:00.000000+00:00");
        }
        return zonedDateTime.format(DateTimeFormatter.ISO_INSTANT);
    }

    private static Instant parseInstant(String value) {
        int epochLength = value.length();
        long epochLong = Long.parseLong(value);
        if (epochLength > 13) {
            return epochMicroToInstantOfMicro(epochLong);
        }
        return epochMilliToInstantOfMicro(epochLong);
    }

    private static Instant epochMicroToInstantOfMicro(Long epochMicro) {
        return Instant.ofEpochSecond(0L, TimeUnit.MICROSECONDS.toNanos(epochMicro));
    }

    private static Instant epochMilliToInstantOfMicro(Long epochMilli) {
        long epochMicro = epochMilli * 1000;
        return epochMicroToInstantOfMicro(epochMicro);
    }

    public static long instantToEpochMicro(Instant i) {
        return TimeUnit.SECONDS.toMicros(i.getEpochSecond()) + TimeUnit.NANOSECONDS.toMicros(i.getNano());
    }

    @Deprecated
    public static SqlQuery createInsertOrderStateSnapshotQuery(OrderStateEntity orderStateEntity) {
        InsertQueryBuilder insertQueryBuilder = InsertQueryBuilder.builder(ORDER_STATE_SNAPSHOT);
        return addAllOrderStateParamsIfFieldExists(insertQueryBuilder, orderStateEntity);
    }

    @Deprecated
    public static SqlQuery createInsertOrderStateQuery(OrderStateEntity orderStateEntity) {
        InsertQueryBuilder insertQueryBuilder = InsertQueryBuilder.builder(ORDER_STATE)
            .addParamIfFieldExist("id", orderStateEntity.id());
        return addAllOrderStateParamsIfFieldExists(insertQueryBuilder, orderStateEntity);
    }

    @Deprecated
    public static SqlQuery createUpdateOrderStateSnapshotQuery(OrderStateEntity orderStateEntity) {
        return UpdateQueryBuilder.builder(ORDER_STATE_SNAPSHOT)
            .addParamIfFieldExist("order_status", orderStateEntity.orderStatus())
            .addParamIfFieldExist("order_qty", orderStateEntity.orderQty())
            .addParamIfFieldExist("currency", orderStateEntity.currency())
            .addParamIfFieldExist("filled_qty", orderStateEntity.filledQty())
            .addParamIfFieldExist("remaining_qty", orderStateEntity.remainingQty())
            .addParamIfFieldExist("last_qty", orderStateEntity.lastQty())
            .addParamIfFieldExist("avg_price", orderStateEntity.avgPrice())
            .addParamIfFieldExist("last_price", orderStateEntity.lastPrice())
            .addParamIfFieldExist("reason", orderStateEntity.reason())
            .addParamIfFieldExist("last_request_result", orderStateEntity.lastRequestResult())
            .addParamIfFieldExist("updated_at", orderStateEntity.updatedAt())
            .addParamIfFieldExist("venue_timestamp", orderStateEntity.venueTimestamp())
            .addParamIfFieldExist("sequence_number", orderStateEntity.sequenceNumber())
            .addParamIfFieldExist("underlying_venue_account", orderStateEntity.underlyingVenueAccount())
            .addParamIfFieldExist("root_order_id", orderStateEntity.rootOrderId())
            .addParamIfFieldExist("counter_portfolio_id", orderStateEntity.counterPortfolioId())
            .addParamIfFieldExist("order_category", orderStateEntity.orderCategory())
            .addParamIfFieldExist("parent_order_id", orderStateEntity.parentOrderId())
            .addParamIfFieldExist("ext_order_id", orderStateEntity.extOrderId())
            .addWhereCondition("order_id", orderStateEntity.orderId())
            .build();
    }

    @Deprecated
    public static SqlQuery createSelectOrderStateSnapshotQuery(String orderId) {
        return SelectQueryBuilder.builder(ORDER_STATE_SNAPSHOT)
            .addWhereCondition("order_id = ?", orderId)
            .buildSelect();
    }

    @Deprecated
    public static SqlQuery createSelectOrderStateSnapshotQuery(OrderHistorySearchInput orderHistorySearch) {
        SelectQueryBuilder selectQueryBuilder = searchInputToSelectQueryBuilder(orderHistorySearch, ORDER_STATE_SNAPSHOT);
        addAfter(selectQueryBuilder, orderHistorySearch);
        addQueryOrderAndLimit(selectQueryBuilder, orderHistorySearch, ORDER_BY_UPDATED_AT);
        return selectQueryBuilder.buildSelect();
    }

    @Deprecated
    public static SqlQuery createSelectOrderStateQuery(OrderHistorySearchInput orderHistorySearch) {
        SelectQueryBuilder selectQueryBuilder = searchInputToSelectQueryBuilder(orderHistorySearch, ORDER_STATE);
        addAfter(selectQueryBuilder, orderHistorySearch);
        addQueryOrderAndLimit(selectQueryBuilder, orderHistorySearch, ORDER_BY_SEQUENCE_NUMBER);
        return selectQueryBuilder.buildSelect();
    }

    @Deprecated
    public static SqlQuery createCountOrderStateSnapshotQuery(OrderHistorySearchInput orderHistorySearch) {
        SelectQueryBuilder selectQueryBuilder = searchInputToSelectQueryBuilder(orderHistorySearch, ORDER_STATE_SNAPSHOT);
        addAfter(selectQueryBuilder, orderHistorySearch);
        return selectQueryBuilder.buildCount();
    }

    @Deprecated
    public static SqlQuery createCountOrderStateQuery(OrderHistorySearchInput orderHistorySearch) {
        SelectQueryBuilder selectQueryBuilder = searchInputToSelectQueryBuilder(orderHistorySearch, ORDER_STATE);
        addAfter(selectQueryBuilder, orderHistorySearch);
        return selectQueryBuilder.buildCount();
    }

    @Deprecated
    public static SqlQuery createDeleteOrderStateSnapshotQuery() {
        return DeleteQueryBuilder.builder(ORDER_STATE_SNAPSHOT).build();
    }

    @Deprecated
    public static SqlQuery createDeleteOrderStateQuery() {
        return DeleteQueryBuilder.builder(ORDER_STATE).build();
    }

    @Deprecated
    public static SqlQuery createInsertCancelReplaceRequestQuery(CancelReplaceRequestEntity cancelReplaceRequestEntity) {
        return InsertQueryBuilder.builder(CANCEL_REPLACE_REQUEST_CACHE)
            .addParamIfFieldExist("new_order_id", cancelReplaceRequestEntity.newOrderId())
            .addParamIfFieldExist("orig_order_id", cancelReplaceRequestEntity.origOrderId())
            .addParamIfFieldExist("created_at", cancelReplaceRequestEntity.createdAt())
            .addParamIfFieldExist("cancel_replace_request", cancelReplaceRequestEntity.cancelReplaceRequest())
            .build();
    }

    @Deprecated
    public static SqlQuery createSelectCancelReplaceRequestQuery(String newOrderId, String origOrderId) {
        return SelectQueryBuilder.builder(CANCEL_REPLACE_REQUEST_CACHE)
            .addWhereCondition("new_order_id = ?", newOrderId)
            .addWhereCondition("orig_order_id = ?", origOrderId)
            .buildSelect();
    }

    @Deprecated
    public static SqlQuery createDeleteCancelReplaceRequestQuery(CancelReplaceRequestEntity cancelReplaceRequestEntity) {
        return DeleteQueryBuilder.builder(CANCEL_REPLACE_REQUEST_CACHE)
            .addWhereCondition("new_order_id = ?", cancelReplaceRequestEntity.newOrderId())
            .addWhereCondition("orig_order_id = ?", cancelReplaceRequestEntity.origOrderId())
            .build();
    }

    public static DatePredicateInput unifyDatesInDatePredicateInput(DatePredicateInput datePredicateInput) {
        return new DatePredicateInput(datePredicateInput.method(), datePredicateInput.field(), unifyDate(datePredicateInput.value()));
    }

    @Deprecated
    private static SqlQuery addAllOrderStateParamsIfFieldExists(InsertQueryBuilder builder, OrderStateEntity orderStateEntity) {
        return builder
            .addParamIfFieldExist("order_id", orderStateEntity.orderId())
            .addParamIfFieldExist("orig_order_id", orderStateEntity.origOrderId())
            .addParamIfFieldExist("client_id", orderStateEntity.clientId())
            .addParamIfFieldExist("cl_order_id", orderStateEntity.clOrderId())
            .addParamIfFieldExist("orig_cl_order_id", orderStateEntity.origClOrderId())
            .addParamIfFieldExist("portfolio_id", orderStateEntity.portfolioId())
            .addParamIfFieldExist("portfolio_type", orderStateEntity.portfolioType())
            .addParamIfFieldExist("order_status", orderStateEntity.orderStatus())
            .addParamIfFieldExist("order_qty", orderStateEntity.orderQty())
            .addParamIfFieldExist("currency", orderStateEntity.currency())
            .addParamIfFieldExist("limit_price", orderStateEntity.limitPrice())
            .addParamIfFieldExist("stop_price", orderStateEntity.stopPrice())
            .addParamIfFieldExist("tif", orderStateEntity.tif())
            .addParamIfFieldExist("filled_qty", orderStateEntity.filledQty())
            .addParamIfFieldExist("remaining_qty", orderStateEntity.remainingQty())
            .addParamIfFieldExist("last_qty", orderStateEntity.lastQty())
            .addParamIfFieldExist("avg_price", orderStateEntity.avgPrice())
            .addParamIfFieldExist("last_price", orderStateEntity.lastPrice())
            .addParamIfFieldExist("reason", orderStateEntity.reason())
            .addParamIfFieldExist("side", orderStateEntity.side())
            .addParamIfFieldExist("instrument_id", orderStateEntity.instrumentId())
            .addParamIfFieldExist("venue_timestamp", orderStateEntity.venueTimestamp())
            .addParamIfFieldExist("created_at", orderStateEntity.createdAt())
            .addParamIfFieldExist("updated_at", orderStateEntity.updatedAt())
            .addParamIfFieldExist("last_request_result", orderStateEntity.lastRequestResult())
            .addParamIfFieldExist("sequence_number", orderStateEntity.sequenceNumber())
            .addParamIfFieldExist("expire_time", orderStateEntity.expireTime())
            .addParamIfFieldExist("order_category", orderStateEntity.orderCategory())
            .addParamIfFieldExist("parent_order_id", orderStateEntity.parentOrderId())
            .addParamIfFieldExist("order_type", orderStateEntity.orderType())
            .addParamIfFieldExist("symbol", orderStateEntity.symbol())
            .addParamIfFieldExist("instrument_type", orderStateEntity.instrumentType())
            .addParamIfFieldExist("venue_accounts", orderStateEntity.venueAccounts())
            .addParamIfFieldExist("underlying_venue_account", orderStateEntity.underlyingVenueAccount())
            .addParamIfFieldExist("root_order_id", orderStateEntity.rootOrderId())
            .addParamIfFieldExist("counter_portfolio_id", orderStateEntity.counterPortfolioId())
            .addParamIfFieldExist("counter_portfolio_type", orderStateEntity.counterPortfolioType())
            .addParamIfFieldExist("ext_order_id", orderStateEntity.extOrderId())
            .build();
    }

    @Deprecated
    private static @NotNull SelectQueryBuilder searchInputToSelectQueryBuilder(OrderHistorySearchInput orderHistorySearch, String tableName) {
        Collection<SimplePredicateInput> simplePredicateInputs = orderHistorySearch.simplePredicates();
        Collection<CollectionPredicateInput> collectionPredicateInputs = orderHistorySearch.collectionPredicates();
        Collection<DatePredicateInput> datePredicateInputs = orderHistorySearch.datePredicateInputs();

        SelectQueryBuilder selectQueryBuilder = SelectQueryBuilder.builder(tableName);

        simplePredicateInputs
            .forEach(input -> selectQueryBuilder.addWhereCondition(input.asPostgresCondition(), input.value()));
        collectionPredicateInputs
            .forEach(input -> selectQueryBuilder.addWhereCondition(input.asPostgresCondition(), input.value()));
        datePredicateInputs
            .forEach(input -> selectQueryBuilder.addWhereCondition(input.asPostgresCondition(), input.valueAsOffsetDateTime()));

        return selectQueryBuilder;
    }

    @Deprecated
    private static void addAfter(SelectQueryBuilder selectQueryBuilder, OrderHistorySearchInput orderHistorySearch) {
        @Nullable String after = orderHistorySearch.after();
        @Nullable SortingOrder sortingOrder = orderHistorySearch.sortingOrder();

        if (StringUtils.isNotEmpty(after)) {
            if (Objects.nonNull(sortingOrder) && sortingOrder.equals(SortingOrder.DESC)) {
                DatePredicateInput beforeDatePredicateInput = new DatePredicateInput(DatePredicateInput.PredicateType.BEFORE, DatePredicateInput.Field.UPDATED_AT, unifyDate(after));
                selectQueryBuilder.addWhereCondition(beforeDatePredicateInput.asPostgresCondition(), beforeDatePredicateInput.valueAsOffsetDateTime());
            } else {
                DatePredicateInput afterDatePredicateInput = new DatePredicateInput(DatePredicateInput.PredicateType.AFTER, DatePredicateInput.Field.UPDATED_AT, unifyDate(after));
                selectQueryBuilder.addWhereCondition(afterDatePredicateInput.asPostgresCondition(), afterDatePredicateInput.valueAsOffsetDateTime());
            }
        }
    }

    @Deprecated
    private static void addQueryOrderAndLimit(SelectQueryBuilder selectQueryBuilder, OrderHistorySearchInput orderHistorySearch, String sortByField) {
        @Nullable Integer first = orderHistorySearch.first(); // limit page size
        @Nullable SortingOrder sortingOrder = orderHistorySearch.sortingOrder();

        selectQueryBuilder.addSortingOrder(sortByField, sortingOrder != null ? String.join(" ", sortingOrder.name()) : " ASC");

        if (Objects.nonNull(first)) {
            selectQueryBuilder.addFetchFirst(first);
        }
    }
}
