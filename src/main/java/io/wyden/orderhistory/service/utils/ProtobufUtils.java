package io.wyden.orderhistory.service.utils;

import io.wyden.orderhistory.model.MessageType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.oems.OemsTIF;
import io.wyden.published.reporting.AssetClass;
import io.wyden.published.reporting.OrderCategory;
import io.wyden.published.reporting.OrderStatus;
import io.wyden.published.reporting.OrderType;
import io.wyden.published.reporting.Side;
import io.wyden.published.reporting.TIF;

import java.util.Optional;

public final class ProtobufUtils {

    private ProtobufUtils() {
        // Empty
    }

    public static OrderStatus toOrderStatus(ClientOrderStatus clientOrderStatus) {
        return switch (clientOrderStatus) {
            case ORDER_STATUS_UNSPECIFIED -> OrderStatus.ORDER_STATUS_UNSPECIFIED;
            case NEW -> OrderStatus.NEW;
            case PARTIALLY_FILLED -> OrderStatus.PARTIALLY_FILLED;
            case FILLED -> OrderStatus.FILLED;
            case DONE_FOR_DAY -> OrderStatus.DONE_FOR_DAY;
            case CANCELED -> OrderStatus.CANCELED;
            case REPLACED -> OrderStatus.REPLACED;
            case PENDING_CANCEL -> OrderStatus.PENDING_CANCEL;
            case STOPPED -> OrderStatus.STOPPED;
            case REJECTED -> OrderStatus.REJECTED;
            case SUSPENDED -> OrderStatus.SUSPENDED;
            case PENDING_NEW -> OrderStatus.PENDING_NEW;
            case CALCULATED -> OrderStatus.CALCULATED;
            case EXPIRED -> OrderStatus.EXPIRED;
            case ACCEPTED_FOR_BIDDING -> OrderStatus.ACCEPTED_FOR_BIDDING;
            case PENDING_REPLACE -> OrderStatus.PENDING_REPLACE;
            case UNRECOGNIZED -> OrderStatus.UNRECOGNIZED;
        };
    }

    public static OrderStatus toOrderStatus(OemsOrderStatus oemsOrderStatus) {
        return switch (oemsOrderStatus) {
            case ORDER_STATUS_UNSPECIFIED -> OrderStatus.ORDER_STATUS_UNSPECIFIED;
            case STATUS_NEW -> OrderStatus.NEW;
            case STATUS_PARTIALLY_FILLED -> OrderStatus.PARTIALLY_FILLED;
            case STATUS_FILLED -> OrderStatus.FILLED;
            case STATUS_CANCELED -> OrderStatus.CANCELED;
            case STATUS_PENDING_CANCEL -> OrderStatus.PENDING_CANCEL;
            case STATUS_REJECTED -> OrderStatus.REJECTED;
            case STATUS_PENDING_NEW -> OrderStatus.PENDING_NEW;
            case STATUS_EXPIRED -> OrderStatus.EXPIRED;
            case UNRECOGNIZED -> OrderStatus.UNRECOGNIZED;
        };
    }

    public static OrderCategory toOrderCategory(String orderCategory) {
        return Optional.ofNullable(OrderCategory.getDescriptor().findValueByName(orderCategory))
            .map(OrderCategory::valueOf)
            .orElse(OrderCategory.forNumber(0));
    }

    public static OrderStatus toOrderStatus(String orderStatus) {
        return Optional.ofNullable(OrderStatus.getDescriptor().findValueByName(orderStatus))
            .map(OrderStatus::valueOf)
            .orElse(OrderStatus.forNumber(0));
    }

    public static TIF toTiff(OemsTIF tif) {
        return toTiff(tif.name());
    }

    public static TIF toTiff(ClientTIF tif) {
        return toTiff(tif.name());
    }

    public static TIF toTiff(String tif) {
        return Optional.ofNullable(TIF.getDescriptor().findValueByName(tif))
            .map(TIF::valueOf)
            .orElse(TIF.forNumber(0));
    }

    public static Side toSide(ClientSide side) {
        return toSide(side.name());
    }

    public static Side toSide(OemsSide side) {
        return toSide(side.name());
    }

    public static Side toSide(String side) {
        return Optional.ofNullable(Side.getDescriptor().findValueByName(side))
            .map(Side::valueOf)
            .orElse(Side.forNumber(0));
    }

    public static OrderType toOrderType(String orderType) {
        return Optional.ofNullable(OrderType.getDescriptor().findValueByName(orderType))
            .map(OrderType::valueOf)
            .orElse(OrderType.forNumber(0));
    }

    public static AssetClass toInstrumentType(String instrumentType) {
        return Optional.ofNullable(AssetClass.getDescriptor().findValueByName(instrumentType))
            .map(AssetClass::valueOf)
            .orElse(AssetClass.forNumber(0));
    }

    public static MessageType toMessageType(String messageType) {
        return switch (messageType) {
            case "OemsResponse" -> MessageType.OEMS_RESPONSE;
            case "ClientResponse" -> MessageType.CLIENT_RESPONSE;
            default -> null;
        };
    }
}
