package io.wyden.orderhistory.service.utils;

import com.google.protobuf.InvalidProtocolBufferException;
import io.wyden.orderhistory.model.CancelReplaceRequestEntity;
import io.wyden.published.client.ClientRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;

@Deprecated
public final class CancelReplaceRequestMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(CancelReplaceRequestMapper.class);

    public static CancelReplaceRequestEntity requestToEntity(ClientRequest request) {
        return CancelReplaceRequestEntity.CancelReplaceRequestEntityBuilder.builder()
            .newOrderId(request.getOrderId())
            .origOrderId(request.getOrigOrderId())
            .createdAt(StringUtils.isNotEmpty(request.getCreatedAt()) ? ZonedDateTime.parse(request.getCreatedAt()).toOffsetDateTime() : OffsetDateTime.now())
            .cancelReplaceRequest(request.toByteArray())
            .build();
    }

    public static ClientRequest entityToClientRequest(CancelReplaceRequestEntity cancelReplaceRequestEntity) {
        return clientRequestFromByteArray(cancelReplaceRequestEntity.cancelReplaceRequest());
    }

    private static ClientRequest clientRequestFromByteArray(byte[] requestAsBytes) {
        try {
            return ClientRequest.parseFrom(requestAsBytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("Error when trying to parse ClientRequest from string: %s".formatted(Arrays.toString(requestAsBytes)), e);
        }
        return null;
    }
}
