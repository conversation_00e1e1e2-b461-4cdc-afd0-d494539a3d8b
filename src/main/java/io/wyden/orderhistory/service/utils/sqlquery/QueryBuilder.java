package io.wyden.orderhistory.service.utils.sqlquery;

import java.time.OffsetDateTime;
import java.util.Collection;

@Deprecated
interface QueryBuilder {

    QueryBuilder addParamIfFieldExist(String paramName, String paramValue);
    QueryBuilder addParamIfFieldExist(String paramName, Integer paramValue);
    QueryBuilder addParamIfFieldExist(String paramName, OffsetDateTime paramValue);
    QueryBuilder addParamIfFieldExist(String paramName, Collection<String> paramValue);
    QueryBuilder addParamIfFieldExist(String paramName, byte[] paramValue);
    QueryBuilder addWhereCondition(String paramName, String paramValue);
    QueryBuilder addWhereCondition(String paramName, Collection<String> paramValue);
    QueryBuilder addWhereCondition(String paramName, OffsetDateTime paramValue);
    QueryBuilder addSortingOrder(String paramName, String paramValue);
    QueryBuilder addFetchFirst(Integer paramValue);
}
