package io.wyden.orderhistory.service.utils;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.reporting.OrderState;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.ZonedDateTime;
import java.util.UUID;

import static io.wyden.orderhistory.service.utils.ProtobufUtils.toInstrumentType;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toOrderCategory;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toOrderStatus;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toOrderType;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toSide;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toTiff;

public class OrderStateMapperExtensions {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStateMapperExtensions.class);

    /**
     * Build OrderState from OemsRequest and OemsResponse
     */
    public static OrderState buildFromOemsRequestAndResponse(OemsRequest request, OemsResponse response) {
        try {
            OrderState.Builder orderStateBuilder = OrderState.newBuilder();

            // Order details from Request
            orderStateBuilder
                .setId(UUID.randomUUID().toString())
                .setOrderId(request.getOrderId())
                .setRootOrderId(request.getRootOrderId())
                .setParentOrderId(request.getParentOrderId())
                .setClientId(request.getClientId())
                .setPortfolioId(request.getPortfolioId())
                .setOrderType(toOrderType(request.getOrderType().name()))
                .setLimitPrice(request.getPrice())
                .setStopPrice(request.getStopPrice())
                .setInstrumentId(request.getInstrumentId())
                .setSymbol(request.getSymbol())
                .setInstrumentType(toInstrumentType(request.getInstrumentType().name()))
                .addAllVenueAccounts(request.getVenueAccountsList())
                .setSide(toSide(request.getSide()))
                .setTif(toTiff(request.getTif()))
                .setExpireTime(ObjectUtils.firstNonNull(request.getExpireTime(), StringUtils.EMPTY))
                .setCreatedAt(request.hasMetadata() ? request.getMetadata().getCreatedAt() : getCurrentIsoTime());

            // Execution details from Response
            orderStateBuilder
                .setClOrderId(response.getClientRootOrderId())
                .setOrderStatus(toOrderStatus(response.getOrderStatus()))
                .setOrderQty(response.getOrderQty())
                .setFilledQty(response.getCumQty())
                .setRemainingQty(response.getLeavesQty())
                .setLastQty(response.getLastQty())
                .setAvgPrice(response.getAvgPrice())
                .setLastPrice(response.getLastPrice())
                .setCurrency(StringUtils.firstNonEmpty(response.getCurrency(), response.getBaseCurrency()))
                .setReason(response.getReason())
                .setLastRequestResult(response.getRequestResult().getValueDescriptor().getName())
                .setUpdatedAt(response.hasMetadata() ? response.getMetadata().getCreatedAt() : getCurrentIsoTime())
                .setOrderCategory(toOrderCategory(response.getOrderCategory().name()))
                .setVenueTimestamp(response.getVenueTimestamp())
                .setExtOrderId(response.getExtId());

            return orderStateBuilder.build();
        } catch (Exception e) {
            LOGGER.error("Failed to build OrderState from OemsRequest and OemsResponse: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to build OrderState", e);
        }
    }

    /**
     * Build OrderState from ClientRequest and ClientResponse
     */
    public static OrderState buildFromClientRequestAndResponse(ClientRequest request, ClientResponse response) {
        try {
            OrderState.Builder orderStateBuilder = OrderState.newBuilder();

            // Order details from Request
            orderStateBuilder
                .setId(UUID.randomUUID().toString())
                .setOrderId(request.getOrderId())
                .setRootOrderId(request.getOrderId())
                .setClientId(request.getClientId())
                .setClOrderId(request.getClOrderId())
                .setOrigClOrderId(request.getOrigClOrderId())
                .setPortfolioId(request.getPortfolioId())
                .setLimitPrice(request.getPrice())
                .setStopPrice(request.getStopPrice())
                .setSide(toSide(request.getSide()))
                .setTif(toTiff(request.getTif()))
                .setOrderType(toOrderType(request.getOrderType().name()))
                .setExpireTime(ObjectUtils.firstNonNull(request.getExpireTime(), StringUtils.EMPTY))
                .addAllVenueAccounts(request.getVenueAccountsList())
                .setCreatedAt(request.hasMetadata() ? request.getMetadata().getCreatedAt() : getCurrentIsoTime());
            if (StringUtils.isNotEmpty(request.getInstrumentId())) {
                orderStateBuilder.setInstrumentId(request.getInstrumentId());
            } else if (StringUtils.isNotEmpty(request.getSymbol()) && StringUtils.isEmpty(request.getInstrumentId())) {
                orderStateBuilder.setSymbol(request.getSymbol());
                orderStateBuilder.setInstrumentType(toInstrumentType(request.getInstrumentType().name()));
            } else {
                LOGGER.warn("No instrument id or symbol provided, skipping ClientRequest");
                throw new IllegalArgumentException("No instrument id or symbol provided");
            }

            // Execution details from Response
            orderStateBuilder
                .setOrderStatus(toOrderStatus(response.getOrderStatus()))
                .setOrderQty(response.getOrderQty())
                .setFilledQty(response.getCumQty())
                .setRemainingQty(response.getLeavesQty())
                .setLastQty(response.getLastQty())
                .setAvgPrice(response.getAvgPrice())
                .setLastPrice(response.getLastPrice())
                .setCurrency(response.getCurrency())
                .setReason(response.getText())
                .setLastRequestResult(response.getRequestResult().getValueDescriptor().getName())
                .setUpdatedAt(response.hasMetadata() ? response.getMetadata().getCreatedAt() : getCurrentIsoTime())
                .setOrderCategory(toOrderCategory(response.getOrderCategory().name()))
                .setVenueTimestamp(response.getTargetVenueTimestamp())
                .setUnderlyingVenueAccount(response.getUnderlyingVenueAccount())
                .setCounterPortfolioId(response.getCounterPortfolioId())
                .setSequenceNumber(response.getSequenceNumber());

            return orderStateBuilder.build();
        } catch (Exception e) {
            LOGGER.error("Failed to build OrderState from ClientRequest and ClientResponse: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to build OrderState", e);
        }
    }

    private static String getCurrentIsoTime() {
        return DateUtils.toIsoUtcTime(ZonedDateTime.now());
    }
}
