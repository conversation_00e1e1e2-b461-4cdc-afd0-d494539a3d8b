package io.wyden.orderhistory.service.utils.sqlquery;

import org.apache.logging.log4j.util.Strings;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.Objects;
import java.util.StringJoiner;

@Deprecated
public final class SelectQueryBuilder extends AbstractQueryBuilder {

    private final StringJoiner whereClause = new StringJoiner(" ", " ", "");
    private final StringJoiner orderClause = new StringJoiner(" ", "", "");
    private final StringJoiner fetchFirstClause = new StringJoiner(" ", "", "");

    public SelectQueryBuilder(String tableName) {
        super(tableName);
    }

    public static SelectQueryBuilder builder(String tableName) {
        return new SelectQueryBuilder(tableName);
    }

    @Override
    public SelectQueryBuilder addParamIfFieldExist(String paramName, String paramValue) {
        throw new UnsupportedOperationException("Select query builder does not support params");
    }

    @Override
    public SelectQueryBuilder addParamIfFieldExist(String paramName, Integer paramValue) {
        throw new UnsupportedOperationException("Select query builder does not support params");
    }

    @Override
    public SelectQueryBuilder addParamIfFieldExist(String paramName, OffsetDateTime paramValue) {
        throw new UnsupportedOperationException("Select query builder does not support params");
    }

    @Override
    public SelectQueryBuilder addParamIfFieldExist(String paramName, Collection<String> paramValue) {
        throw new UnsupportedOperationException("Select query builder does not support params");
    }

    @Override
    public SelectQueryBuilder addParamIfFieldExist(String paramName, byte[] paramValue) {
        throw new UnsupportedOperationException("Select query builder does not support params");
    }

    @Override
    public SelectQueryBuilder addWhereCondition(String condition, String paramValue) {
        if (Strings.isNotEmpty(paramValue)) {
            addWhereCondition(condition);
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public SelectQueryBuilder addWhereCondition(String condition, Collection<String> paramValue) {
        if (!paramValue.isEmpty()) {
            addWhereCondition(condition);
            addValue(paramValue.toArray(String[]::new));
        }
        return this;
    }

    @Override
    public SelectQueryBuilder addWhereCondition(String paramName, OffsetDateTime paramValue) {
        if (Objects.nonNull(paramValue)) {
            addWhereCondition(paramName);
            addValue(paramValue);
        }
        return this;
    }

    @Override
    public SelectQueryBuilder addSortingOrder(String paramName, String paramValue) {
        if (Objects.nonNull(paramValue)) {
            addOrderClause(paramName, paramValue);
        }
        return this;
    }

    @Override
    public SelectQueryBuilder addFetchFirst(Integer paramValue) {
        if (QueryUtils.isNotNullAndGreaterThatZero(paramValue)) {
            addFetchFirstClause(paramValue);
        }
        return this;
    }

    public SqlQuery buildSelect() {
        String queryBuilder = String.format("SELECT * FROM %s WHERE 1=1%s%s%s", tableName, whereClause, orderClause, fetchFirstClause);
        return new SqlQuery(queryBuilder, values.toArray());
    }

    public SqlQuery buildCount() {
        String queryBuilder = String.format("SELECT COUNT(*) FROM %s WHERE 1=1%s", tableName, whereClause);
        return new SqlQuery(queryBuilder, values.toArray());
    }

    private void addValue(Object value) {
        this.values.add(value);
    }

    private void addWhereCondition(String condition) {
        this.whereClause.add("AND " + condition);
    }

    private void addOrderClause(String fieldName, String order) {
        this.orderClause.add(String.format(" ORDER BY %s %s", fieldName, order));
    }

    private void addFetchFirstClause(Integer value) {
        this.fetchFirstClause.add(String.format(" FETCH FIRST %s ROWS ONLY", value));
    }
}
