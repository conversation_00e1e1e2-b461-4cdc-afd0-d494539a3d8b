package io.wyden.orderhistory.service;

import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.reporting.OrderState;

import java.util.List;

public interface OrderHistoryQueryOperations {
    List<OrderState> getOrderStateSnapshots(OrderHistorySearchInput orderHistorySearchInput);

    CursorConnection getOrderStateSnapshotsPaged(OrderHistorySearchInput orderHistorySearchInput);

    List<OrderState> getOrderStates(OrderHistorySearchInput orderHistorySearchInput);

    CursorConnection getOrderStatesPaged(OrderHistorySearchInput orderHistorySearchInput);
}
