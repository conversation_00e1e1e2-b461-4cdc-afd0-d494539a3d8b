package io.wyden.orderhistory.service;

import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.orderhistory.repository.OrderStateRepository;
import io.wyden.orderhistory.repository.PortfolioProvider;
import io.wyden.orderhistory.service.outbound.OrderStateEmitter;
import io.wyden.orderhistory.service.utils.OrderStateComparator;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.Portfolio;
import jakarta.annotation.Nullable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static io.wyden.orderhistory.service.utils.OrderStateMapper.asOrderState;
import static io.wyden.orderhistory.service.utils.OrderStateMapper.entityToProto;
import static io.wyden.orderhistory.service.utils.OrderStateMapper.merge;
import static io.wyden.orderhistory.service.utils.OrderStateMapper.updateOrderStateStatus;
import static io.wyden.orderhistory.service.utils.OrderStateMapper.updateOrderStateToCanceled;

@Deprecated
@Service
public class OrderStateProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStateProcessor.class);

    private final OrderStateRepository orderStateRepository;
    private final PortfolioProvider portfolioProvider;
    private final OrderStateEmitter orderStateEmitter;

    public OrderStateProcessor(OrderStateRepository orderStateRepository,
                               PortfolioProvider portfolioProvider,
                               OrderStateEmitter orderStateEmitter) {
        this.orderStateRepository = orderStateRepository;
        this.portfolioProvider = portfolioProvider;
        this.orderStateEmitter = orderStateEmitter;

    }

    public void processNewState(ClientRequest clientRequest) {
        Portfolio portfolio = getPortfolio(clientRequest.getPortfolioId());
        Optional<OrderStateEntity> orderState = Optional.ofNullable(asOrderState(clientRequest, portfolio));
        orderState.ifPresentOrElse(this::saveAndEmitOrderState,
            () -> LOGGER.info("Could not parse ClientRequest ({}) as OrderState", clientRequest));
    }

    public void processCancelReplaceNewState(ClientRequest clientRequest) {
        Portfolio portfolio = getPortfolio(clientRequest.getPortfolioId());
        Optional<OrderStateEntity> orderState = Optional.ofNullable(asOrderState(clientRequest, portfolio));
        orderState.ifPresentOrElse(this::saveOrderStateAndSnapshot,
            () -> LOGGER.info("Could not parse ClientRequest ({}) as OrderState", clientRequest));
    }

    public void processNewState(OemsRequest oemsRequest) {
        LOGGER.debug("OrderState for order with id: {} not found. Processing OemsRequest with responseId: {}",
            oemsRequest.getOrderId(), oemsRequest.hasMetadata() ? oemsRequest.getMetadata().getResponseId() : StringUtils.EMPTY);
        Portfolio portfolio = getPortfolio(oemsRequest.getPortfolioId());
        OrderStateEntity orderState = asOrderState(oemsRequest, portfolio);
        saveAndEmitOrderState(orderState);
    }

    public void processNewState(OemsResponse oemsResponse) {
        LOGGER.debug("OrderState for order with id: {} not found. Processing OemsResponse with responseId: {}",
            oemsResponse.getOrderId(), oemsResponse.hasMetadata() ? oemsResponse.getMetadata().getResponseId() : StringUtils.EMPTY);
        Portfolio portfolio = getPortfolio(oemsResponse.getPortfolioId());
        OrderStateEntity orderState = asOrderState(oemsResponse, portfolio);
        saveAndEmitOrderState(orderState);
    }

    public void processExistingState(OrderStateEntity currentState, ClientResponse response) {
        if (OrderStateComparator.hasChanged(currentState, response)) {
            LOGGER.debug("Changes detected for order: {}", currentState.orderId());
            Portfolio counterPortfolio = getPortfolio(response.getCounterPortfolioId());
            OrderStateEntity merged = merge(currentState, response, counterPortfolio);
            updateAndEmitOrderState(merged);
        } else {
            LOGGER.debug("No changes detected for order: {}", currentState.orderId());
        }
    }

    public void processExistingState(OrderStateEntity currentState, OemsRequest request) {
        if (OrderStateComparator.hasChanged(currentState, request)) {
            LOGGER.debug("Changes detected for order: {}", currentState.orderId());
            OrderStateEntity merged = merge(currentState, request);
            updateAndEmitOrderState(merged);
        } else {
            LOGGER.debug("No changes detected for order: {}", currentState.orderId());
        }
    }

    public void processExistingState(OrderStateEntity currentState, OemsResponse response) {
        if (OrderStateComparator.hasChanged(currentState, response)) {
            LOGGER.debug("Changes detected for order: {}", currentState.orderId());
            OrderStateEntity merged = merge(currentState, response);
            updateAndEmitOrderState(merged);
        } else {
            LOGGER.debug("No changes detected for order: {}", currentState.orderId());
        }
    }

    public void updateStateToCanceled(OrderStateEntity currentState) {
        LOGGER.debug("Setting status CANCELED for order: {}", currentState.orderId());
        OrderStateEntity updatedOrderState = updateOrderStateToCanceled(currentState);
        updateAndEmitOrderState(updatedOrderState);
    }

    public void updateStateToRejected(OrderStateEntity currentState, ClientResponse response) {
        LOGGER.debug("Setting status REJECTED for order: {}", currentState.orderId());
        OrderStateEntity updatedOrderState = updateOrderStateStatus(currentState, response.getOrderStatus());
        updateAndEmitOrderState(updatedOrderState);
    }

    private void saveAndEmitOrderState(OrderStateEntity state) {
        saveOrderStateSnapshot(state);
        saveOrderState(state);
        emitOrderState(state);
    }

    private void updateAndEmitOrderState(OrderStateEntity state) {
        updateOrderStateSnapshot(state);
        saveOrderState(state);
        emitOrderState(state);
    }

    private void saveOrderStateAndSnapshot(OrderStateEntity state) {
        saveOrderStateSnapshot(state);
        saveOrderState(state);
    }

    private void saveOrderStateSnapshot(OrderStateEntity state) {
        orderStateRepository.saveOrderStateSnapshot(state);
    }

    private void saveOrderState(OrderStateEntity state) {
        orderStateRepository.saveOrderState(state);
    }

    private void updateOrderStateSnapshot(OrderStateEntity state) {
        orderStateRepository.updateOrderStateSnapshot(state);
    }

    private void emitOrderState(OrderStateEntity state) {
        orderStateEmitter.emit(entityToProto(state));
    }

    private @Nullable Portfolio getPortfolio(@Nullable  String portfolioId) {
        if (StringUtils.isNotEmpty(portfolioId)) {
            return portfolioProvider.find(portfolioId).orElse(null);
        }
        return null;
    }
}
