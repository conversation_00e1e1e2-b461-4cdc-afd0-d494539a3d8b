package io.wyden.orderhistory.repository;

import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.client.PortfoliosCacheFacade;

import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class PortfolioProvider {

    private final PortfoliosCacheFacade portfoliosCacheFacade;

    public PortfolioProvider(PortfoliosCacheFacade portfoliosCacheFacade) {
        this.portfoliosCacheFacade = portfoliosCacheFacade;
    }

    public Optional<Portfolio> find(String portfolioId) {
        return portfoliosCacheFacade.find(portfolioId);
    }
}
