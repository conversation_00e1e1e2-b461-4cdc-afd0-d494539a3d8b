package io.wyden.orderhistory.repository;

import io.wyden.orderhistory.model.CancelReplaceRequestEntity;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.orderhistory.service.utils.sqlquery.SqlQuery;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static io.wyden.orderhistory.service.utils.DbUtils.createCountOrderStateQuery;
import static io.wyden.orderhistory.service.utils.DbUtils.createCountOrderStateSnapshotQuery;
import static io.wyden.orderhistory.service.utils.DbUtils.createDeleteCancelReplaceRequestQuery;
import static io.wyden.orderhistory.service.utils.DbUtils.createDeleteOrderStateQuery;
import static io.wyden.orderhistory.service.utils.DbUtils.createDeleteOrderStateSnapshotQuery;
import static io.wyden.orderhistory.service.utils.DbUtils.createInsertCancelReplaceRequestQuery;
import static io.wyden.orderhistory.service.utils.DbUtils.createInsertOrderStateQuery;
import static io.wyden.orderhistory.service.utils.DbUtils.createInsertOrderStateSnapshotQuery;
import static io.wyden.orderhistory.service.utils.DbUtils.createSelectCancelReplaceRequestQuery;
import static io.wyden.orderhistory.service.utils.DbUtils.createSelectOrderStateQuery;
import static io.wyden.orderhistory.service.utils.DbUtils.createSelectOrderStateSnapshotQuery;
import static io.wyden.orderhistory.service.utils.DbUtils.createUpdateOrderStateSnapshotQuery;

@Deprecated
@Repository
@Qualifier("postgresOrderStateRepository")
public class PostgresOrderStateRepository implements OrderStateRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(PostgresOrderStateRepository.class);

    private final JdbcTemplate jdbcTemplate;

    public PostgresOrderStateRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public Optional<OrderStateEntity> findOrderStateSnapshotByOrderId(String orderId) {
        LOGGER.debug("Searching OrderStateSnapshot with orderId: {}", orderId);
        SqlQuery selectSqlQuery = createSelectOrderStateSnapshotQuery(orderId);
        LOGGER.trace("Select OrderStateSnapshot query: {}, values: {}", selectSqlQuery.query(), Arrays.toString(selectSqlQuery.values()));
        return jdbcTemplate.query(selectSqlQuery.query(), orderStateSnapshotRowMapper(), selectSqlQuery.values()).stream().findFirst();
    }

    @Override
    public List<OrderStateEntity> findOrderStateSnapshotsBySearchInput(OrderHistorySearchInput orderHistorySearch) {
        LOGGER.debug("Searching OrderStateSnapshots with orderHistorySearch: {}", orderHistorySearch);
        SqlQuery selectSqlQuery = createSelectOrderStateSnapshotQuery(orderHistorySearch);
        LOGGER.trace("Select OrderStateSnapshot query: \n{}, values: {} \n for input orderHistorySearch: {}", selectSqlQuery.query(), selectSqlQuery.values(), orderHistorySearch);
        return jdbcTemplate.query(selectSqlQuery.query(), orderStateSnapshotRowMapper(), selectSqlQuery.values());
    }

    @Override
    public List<OrderStateEntity> findOrderStatesBySearchInput(OrderHistorySearchInput orderHistorySearch) {
        LOGGER.debug("Searching OrderStates with orderHistorySearch: {}", orderHistorySearch);
        SqlQuery selectSqlQuery = createSelectOrderStateQuery(orderHistorySearch);
        LOGGER.trace("Select OrderState query: {}, values: {}", selectSqlQuery.query(), Arrays.toString(selectSqlQuery.values()));
        return jdbcTemplate.query(selectSqlQuery.query(), orderStateRowMapper(), selectSqlQuery.values());
    }

    @Override
    public Integer countOrderStateSnapshotsBySearchInput(OrderHistorySearchInput orderHistorySearch) {
        LOGGER.debug("Counting all OrderStateSnapshots with orderHistorySearch: {}", orderHistorySearch);
        SqlQuery countSqlQuery = createCountOrderStateSnapshotQuery(orderHistorySearch);
        LOGGER.trace("Select OrderStateSnapshot count query: \n{}, values: {} \n for input orderHistorySearch: {}", countSqlQuery.query(), countSqlQuery.values(), orderHistorySearch);
        return jdbcTemplate.queryForObject(countSqlQuery.query(), Integer.class, countSqlQuery.values());
    }

    @Override
    public Integer countOrderStatesBySearchInput(OrderHistorySearchInput orderHistorySearchInput) {
        LOGGER.debug("Counting all OrderStates with orderHistorySearch: {}", orderHistorySearchInput);
        SqlQuery countSqlQuery = createCountOrderStateQuery(orderHistorySearchInput);
        LOGGER.trace("Select OrderState count query: \n{}, values: {} \n for input orderHistorySearch: {}", countSqlQuery.query(), countSqlQuery.values(), orderHistorySearchInput);
        return jdbcTemplate.queryForObject(countSqlQuery.query(), Integer.class, countSqlQuery.values());
    }

    @Override
    public void saveOrderStateSnapshot(OrderStateEntity orderStateEntity) {
        SqlQuery insertOrderStateSnapshot = createInsertOrderStateSnapshotQuery(orderStateEntity);
        LOGGER.trace("Insert OrderStateSnapshot query: {}, values: {}", insertOrderStateSnapshot.query(), Arrays.toString(insertOrderStateSnapshot.values()));
        int rows = jdbcTemplate.update(insertOrderStateSnapshot.query(), insertOrderStateSnapshot.values());
        Assert.state(rows == 1, "Failed to create OrderState: %s".formatted(orderStateEntity));
        LOGGER.debug("Stored new OrderStateSnapshot: {}", orderStateEntity);
    }

    @Override
    public void saveOrderState(OrderStateEntity orderStateEntity) {
        SqlQuery insertOrderState = createInsertOrderStateQuery(orderStateEntity);
        LOGGER.trace("Insert OrderState query: {}, values: {}", insertOrderState.query(), Arrays.toString(insertOrderState.values()));
        int rows = jdbcTemplate.update(insertOrderState.query(), insertOrderState.values());
        Assert.state(rows == 1, "Failed to create OrderState: %s".formatted(orderStateEntity));
        LOGGER.debug("Stored new OrderState: {}", orderStateEntity);
    }

    @Override
    public void updateOrderStateSnapshot(OrderStateEntity orderStateEntity) {
        SqlQuery updateSqlQuery = createUpdateOrderStateSnapshotQuery(orderStateEntity);
        LOGGER.trace("Update OrderStateSnapshot query: {}, values: {}", updateSqlQuery.query(), Arrays.toString(updateSqlQuery.values()));
        int rows = jdbcTemplate.update(updateSqlQuery.query(), updateSqlQuery.values());
        Assert.state(rows == 1, "Failed to update OrderState: %s".formatted(orderStateEntity));
        LOGGER.debug("Updated OrderStateSnapshot: {}", orderStateEntity);
    }

    @Override
    public void deleteAllOrderStateSnapshots() {
        LOGGER.warn("Deleting all OrderStateSnapshots");
        SqlQuery deleteSqlQuery = createDeleteOrderStateSnapshotQuery();
        jdbcTemplate.execute(deleteSqlQuery.query());
    }

    @Override
    public void deleteAllOrderStates() {
        LOGGER.warn("Deleting all OrderStates");
        SqlQuery deleteSqlQuery = createDeleteOrderStateQuery();
        jdbcTemplate.execute(deleteSqlQuery.query());
    }

    @Override
    public Optional<CancelReplaceRequestEntity> findCancelReplaceRequestByOrderId(String newOrderId, String origOrderId) {
        LOGGER.debug("Searching CancelReplaceRequest with newOrderId: {} and origOrderId: {}", newOrderId, origOrderId);
        SqlQuery selectSqlQuery = createSelectCancelReplaceRequestQuery(newOrderId, origOrderId);
        LOGGER.trace("Select CancelReplaceRequest query: {}, values: {}", selectSqlQuery.query(), Arrays.toString(selectSqlQuery.values()));
        return jdbcTemplate.query(selectSqlQuery.query(), cancelReplaceRequestRowMapper(), selectSqlQuery.values()).stream().findFirst();
    }

    @Override
    public void saveCancelReplaceRequest(CancelReplaceRequestEntity cancelReplaceRequestEntity) {
        SqlQuery insertSqlQuery = createInsertCancelReplaceRequestQuery(cancelReplaceRequestEntity);
        LOGGER.trace("Insert CancelReplaceRequestEntity query: {}, values: {}", insertSqlQuery.query(), Arrays.toString(insertSqlQuery.values()));
        int rows = jdbcTemplate.update(insertSqlQuery.query(), insertSqlQuery.values());
        Assert.state(rows == 1, "Failed to create CancelReplaceRequestEntity: %s".formatted(cancelReplaceRequestEntity));
        LOGGER.debug("Stored new CancelReplaceRequestEntity: {}", cancelReplaceRequestEntity);
    }

    @Override
    public void deleteCancelReplaceRequest(CancelReplaceRequestEntity cancelReplaceRequestEntity) {
        SqlQuery deleteSqlQuery = createDeleteCancelReplaceRequestQuery(cancelReplaceRequestEntity);
        LOGGER.trace("Delete CancelReplaceRequestEntity query: {}, values: {}", deleteSqlQuery.query(), Arrays.toString(deleteSqlQuery.values()));
        int rows = jdbcTemplate.update(deleteSqlQuery.query(), deleteSqlQuery.values());
        Assert.state(rows == 1, "Failed to remove CancelReplaceRequestEntity: %s".formatted(cancelReplaceRequestEntity));
        LOGGER.debug("Deleted CancelReplaceRequest with newOrderId: {} and origOrderId: {}", cancelReplaceRequestEntity.newOrderId(), cancelReplaceRequestEntity.origOrderId());
    }

    private RowMapper<OrderStateEntity> orderStateSnapshotRowMapper() {
        return (rs, rowNum) -> applyResultSetToOrderStateEntityBuilder(rs)
            .build();
    }

    private RowMapper<OrderStateEntity> orderStateRowMapper() {
        return (rs, rowNum) -> applyResultSetToOrderStateEntityBuilder(rs)
            .id(rs.getString("id"))
            .build();
    }

    private static OrderStateEntity.@NotNull OrderStateEntityBuilder applyResultSetToOrderStateEntityBuilder(ResultSet rs) throws SQLException {
        return OrderStateEntity.OrderStateEntityBuilder.builder()
            .orderId(rs.getString("order_id"))
            .origOrderId(rs.getString("orig_order_id"))
            .clientId(rs.getString("client_id"))
            .clOrderId(rs.getString("cl_order_id"))
            .origClOrderId(rs.getString("orig_cl_order_id"))
            .portfolioId(rs.getString("portfolio_id"))
            .portfolioType(rs.getString("portfolio_type"))
            .orderStatus(rs.getString("order_status"))
            .orderQty(rs.getString("order_qty"))
            .currency(rs.getString("currency"))
            .limitPrice(rs.getString("limit_price"))
            .stopPrice(rs.getString("stop_price"))
            .tif(rs.getString("tif"))
            .filledQty(rs.getString("filled_qty"))
            .remainingQty(rs.getString("remaining_qty"))
            .lastQty(rs.getString("last_qty"))
            .avgPrice(rs.getString("avg_price"))
            .lastPrice(rs.getString("last_price"))
            .reason(rs.getString("reason"))
            .side(rs.getString("side"))
            .instrumentId(rs.getString("instrument_id"))
            .venueTimestamp(Objects.nonNull(rs.getObject("venue_timestamp", OffsetDateTime.class)) ? rs.getObject("venue_timestamp", OffsetDateTime.class) : null)
            .createdAt(rs.getObject("created_at", OffsetDateTime.class))
            .updatedAt(Objects.nonNull(rs.getObject("updated_at", OffsetDateTime.class)) ? rs.getObject("updated_at", OffsetDateTime.class) : null)
            .lastRequestResult(rs.getString("last_request_result"))
            .sequenceNumber(rs.getInt("sequence_number"))
            .expireTime(Objects.nonNull(rs.getObject("expire_time", OffsetDateTime.class)) ? rs.getObject("expire_time", OffsetDateTime.class) : null)
            .orderCategory(rs.getString("order_category"))
            .parentOrderId(rs.getString("parent_order_id"))
            .orderType(rs.getString("order_type"))
            .symbol(rs.getString("symbol"))
            .instrumentType(rs.getString("instrument_type"))
            .venueAccounts(rs.getArray("venue_accounts") != null ? Arrays.asList((String[]) rs.getArray("venue_accounts").getArray()) : List.of())
            .underlyingVenueAccount(rs.getString("underlying_venue_account"))
            .rootOrderId(rs.getString("root_order_id"))
            .counterPortfolioId(rs.getString("counter_portfolio_id"))
            .counterPortfolioType(rs.getString("counter_portfolio_type"))
            .extOrderId(rs.getString("ext_order_id"));

    }

    private RowMapper<CancelReplaceRequestEntity> cancelReplaceRequestRowMapper() {
        return (rs, rowNum) -> CancelReplaceRequestEntity.CancelReplaceRequestEntityBuilder.builder()
            .newOrderId(rs.getString("new_order_id"))
            .origOrderId(rs.getString("orig_order_id"))
            .createdAt(rs.getObject("created_at", OffsetDateTime.class))
            .cancelReplaceRequest(rs.getBytes("cancel_replace_request"))
            .build();
    }

    public List<String> findDistinctOrderIdsOrderedByOldest(int offset, int limit) {
        LOGGER.debug("Fetching distinct order IDs with offset: {}, limit: {}", offset, limit);
        String sql = """
            SELECT order_id 
            FROM order_state_snapshot 
            ORDER BY created_at ASC 
            OFFSET ? LIMIT ?
            """;
        return jdbcTemplate.queryForList(sql, String.class, offset, limit);
    }

    public List<OrderStateEntity> findAllOrderStatesByOrderIdOrderedBySequence(String orderId) {
        LOGGER.debug("Fetching all order states for order: {}", orderId);
        String sql = """
            SELECT * FROM order_state 
            WHERE order_id = ? 
            ORDER BY sequence_number ASC, updated_at ASC
            """;
        return jdbcTemplate.query(sql, orderStateRowMapper(), orderId);
    }
}