package io.wyden.orderhistory.repository;

import io.wyden.cloudutils.telemetry.Telemetry;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Deprecated
@Configuration
public class RepositoryConfig {

    @Bean
    @ConditionalOnProperty(name = "db.engine", havingValue = "psql", matchIfMissing = true)
    public OrderStateRepository orderStateRepository(@Qualifier("postgresOrderStateRepository") OrderStateRepository orderStateRepository,
                                                     Telemetry telemetry) {
        return new MeteredPostgresOrderStateRepository(orderStateRepository, telemetry);
    }
}
