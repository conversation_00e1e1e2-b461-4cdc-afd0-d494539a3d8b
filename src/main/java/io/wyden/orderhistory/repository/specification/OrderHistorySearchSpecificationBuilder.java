package io.wyden.orderhistory.repository.specification;

import io.wyden.orderhistory.model.CollectionPredicateInput;
import io.wyden.orderhistory.model.DatePredicateInput;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.SimplePredicateInput;
import io.wyden.orderhistory.model.SortingField;
import io.wyden.orderhistory.model.SortingOrder;
import io.wyden.orderhistory.service.utils.DbUtils;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.apache.commons.text.CaseUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

import static io.wyden.orderhistory.service.utils.DbUtils.parseUnifiedDate;

public class OrderHistorySearchSpecificationBuilder {

    /**
     * Builds a complete JPA Specification from OrderHistorySearchInput
     */
    public static <T> Specification<T> buildSpecification(OrderHistorySearchInput searchInput) {
        List<Specification<T>> specifications = new ArrayList<>();

        // Add simple predicates
        searchInput.simplePredicates().forEach(predicate ->
            specifications.add(buildSimpleSpecification(predicate))
        );

        // Add collection predicates
        searchInput.collectionPredicates().forEach(predicate ->
            specifications.add(buildCollectionSpecification(predicate))
        );

        // Add date predicates
        searchInput.datePredicateInputs()
            .stream().map(DbUtils::unifyDatesInDatePredicateInput)
            .forEach(predicate ->
            specifications.add(buildDateSpecification(predicate))
        );

        // Add cursor-based pagination
        if (StringUtils.hasText(searchInput.after())) {
            specifications.add(buildAfterSpecification(searchInput.after(), searchInput.sortingOrder(), searchInput.sortingField()));
        }

        // Combine all specifications with AND
        return specifications.stream()
            .reduce(Specification::and)
            .orElse(Specification.where(null));
    }

    /**
     * Builds specification for simple predicates
     */
    private static <T> Specification<T> buildSimpleSpecification(SimplePredicateInput predicate) {
        return (root, query, cb) -> {
            String fieldName = toCamelCase(predicate.fieldColumnName());
            Path<String> path = root.get(fieldName);

            // Handle array fields
            if (predicate.isArrayField()) {
                return buildArrayPredicate(root, cb, predicate);
            }

            // Handle null acceptance
            if (predicate.acceptNullValues()) {
                Predicate mainPredicate = buildSimplePredicate(cb, path, predicate);
                Predicate nullPredicate = cb.isNull(path);
                return cb.or(nullPredicate, mainPredicate);
            }

            // Standard predicate
            return buildSimplePredicate(cb, path, predicate);
        };
    }

    /**
     * Builds the actual predicate based on the type
     */
    private static Predicate buildSimplePredicate(CriteriaBuilder cb, Path<String> path, SimplePredicateInput predicate) {
        return switch (predicate.method()) {
            case CONTAINS -> cb.like(cb.lower(path), "%" + predicate.value().toLowerCase() + "%");
            case EQUAL -> cb.equal(path, predicate.value());
            case NOT_EQUAL -> cb.notEqual(path, predicate.value());
        };
    }

    /**
     * Builds predicate for array fields (PostgreSQL specific)
     */
    private static <T> Predicate buildArrayPredicate(Root<T> root, CriteriaBuilder cb, SimplePredicateInput predicate) {
        // For array fields PostgreSQL-specific functions needs to be used
        String fieldName = toCamelCase(predicate.fieldColumnName());
        Expression<Boolean> arrayContains = cb.function(
            "sql",
            Boolean.class,
            cb.literal("? @> ARRAY[?]::text[]"),
            root.get(fieldName),
            cb.literal(predicate.value())
        );

        if (predicate.method() == SimplePredicateInput.PredicateType.EQUAL) {
            return cb.isTrue(arrayContains);
        } else if (predicate.method() == SimplePredicateInput.PredicateType.NOT_EQUAL) {
            return cb.isFalse(arrayContains);
        } else {
            throw new IllegalArgumentException("Unsupported predicate type for array field: " + predicate.method());
        }
    }

    /**
     * Builds specification for collection predicates
     */
    private static <T> Specification<T> buildCollectionSpecification(CollectionPredicateInput predicate) {
        return (root, query, cb) -> {
            String fieldName = toCamelCase(predicate.fieldColumnName());

            // Handle array fields differently
            if (predicate.isArrayField()) {
                // For array overlap operations in PostgreSQL
                Expression<Boolean> arrayOverlaps = cb.function(
                    "sql",
                    Boolean.class,
                    cb.literal("(? && ?)"),
                    root.get(fieldName),
                    cb.literal(predicate.value().toArray(new String[0]))
                );
                return cb.isTrue(arrayOverlaps);
            }

            // Standard IN clause
            Path<String> path = root.get(fieldName);
            return path.in(predicate.value());
        };
    }

    /**
     * Builds specification for date predicates
     */
    private static <T> Specification<T> buildDateSpecification(DatePredicateInput predicate) {
        return (root, query, cb) -> {
            String fieldName = toCamelCase(predicate.fieldColumnName());
            Path<OffsetDateTime> path = root.get(fieldName);
            OffsetDateTime dateValue = predicate.valueAsOffsetDateTime();

            return switch (predicate.method()) {
                case FROM -> cb.greaterThanOrEqualTo(path, dateValue);
                case TO -> cb.lessThanOrEqualTo(path, dateValue);
                case AFTER -> cb.greaterThan(path, dateValue);
                case BEFORE -> cb.lessThan(path, dateValue);
            };
        };
    }

    /**
     * Builds specification for cursor-based pagination
     */
    private static <T> Specification<T> buildAfterSpecification(String after, SortingOrder sortingOrder, SortingField sortingField) {
        return (root, query, cb) -> {
            OffsetDateTime cursorDate = parseUnifiedDate(after);
            Path<OffsetDateTime> sortingFieldPath = root.get(sortingField.fieldName());

            if (sortingOrder == SortingOrder.DESC) {
                return cb.lessThan(sortingFieldPath, cursorDate);
            } else {
                return cb.greaterThan(sortingFieldPath, cursorDate);
            }
        };
    }

    /**
     * Converts snake_case field names to camelCase for JPA
     */
    private static String toCamelCase(String snakeCase) {
        return CaseUtils.toCamelCase(snakeCase, false, '_');
    }
}