package io.wyden.orderhistory.repository;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.orderhistory.infrastructure.telemetry.Meters;
import io.wyden.orderhistory.model.CancelReplaceRequestEntity;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.OrderStateEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;

@Deprecated
@Repository
public class MeteredPostgresOrderStateRepository implements OrderStateRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(MeteredPostgresOrderStateRepository.class);
    
    private final OrderStateRepository orderStateRepository;
    private final MeterRegistry meterRegistry;

    public MeteredPostgresOrderStateRepository(@Qualifier("postgresOrderStateRepository") OrderStateRepository orderStateRepository, 
                                               Telemetry telemetry) {
        this.orderStateRepository = orderStateRepository;
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    @Override
    public Optional<OrderStateEntity> findOrderStateSnapshotByOrderId(String orderId) {
        LOGGER.trace("findOrderStateSnapshotByOrderId");
        return recordLatencyIn(latencyTimer(Meters.OrderStateSnapshotQueryType.FIND_BY_ID))
            .of(() -> orderStateRepository.findOrderStateSnapshotByOrderId(orderId));
    }

    @Override
    public List<OrderStateEntity> findOrderStateSnapshotsBySearchInput(OrderHistorySearchInput orderHistorySearch) {
        LOGGER.trace("findOrderStateSnapshotsBySearchInput");
        return recordLatencyIn(latencyTimer(Meters.OrderStateSnapshotQueryType.FIND_BY_PROPERTIES))
            .of(() -> orderStateRepository.findOrderStateSnapshotsBySearchInput(orderHistorySearch));
    }

    @Override
    public List<OrderStateEntity> findOrderStatesBySearchInput(OrderHistorySearchInput orderHistorySearch) {
        LOGGER.trace("findOrderStatesBySearchInput");
        return recordLatencyIn(latencyTimer(Meters.OrderStateQueryType.FIND_BY_PROPERTIES))
            .of(() -> orderStateRepository.findOrderStatesBySearchInput(orderHistorySearch));
    }

    @Override
    public Integer countOrderStateSnapshotsBySearchInput(OrderHistorySearchInput orderHistorySearch) {
        LOGGER.trace("countOrderStateSnapshotsBySearchInput");
        return recordLatencyIn(latencyTimer(Meters.OrderStateSnapshotQueryType.COUNT_BY_PROPERTIES))
            .of(() -> orderStateRepository.countOrderStateSnapshotsBySearchInput(orderHistorySearch));
    }

    @Override
    public Integer countOrderStatesBySearchInput(OrderHistorySearchInput orderHistorySearchInput) {
        LOGGER.trace("countOrderStatesBySearchInput");
        return recordLatencyIn(latencyTimer(Meters.OrderStateQueryType.COUNT_BY_PROPERTIES))
            .of(() -> orderStateRepository.countOrderStatesBySearchInput(orderHistorySearchInput));
    }

    @Override
    public void saveOrderStateSnapshot(OrderStateEntity orderStateEntity) {
        LOGGER.trace("saveOrderStateSnapshot");
        recordLatencyIn(latencyTimer(Meters.OrderStateSnapshotQueryType.SAVE))
            .of(() -> orderStateRepository.saveOrderStateSnapshot(orderStateEntity));
    }

    @Override
    public void saveOrderState(OrderStateEntity orderStateEntity) {
        LOGGER.trace("saveOrderState");
        recordLatencyIn(latencyTimer(Meters.OrderStateQueryType.SAVE))
            .of(() -> orderStateRepository.saveOrderState(orderStateEntity));
    }

    @Override
    public void updateOrderStateSnapshot(OrderStateEntity orderStateEntity) {
        LOGGER.trace("updateOrderStateSnapshot");
        recordLatencyIn(latencyTimer(Meters.OrderStateSnapshotQueryType.UPDATE))
            .of(() -> orderStateRepository.updateOrderStateSnapshot(orderStateEntity));
    }

    @Override
    public void deleteAllOrderStateSnapshots() {
        recordLatencyIn(latencyTimer(Meters.OrderStateSnapshotQueryType.DELETE_ALL))
            .of(orderStateRepository::deleteAllOrderStateSnapshots);
    }

    @Override
    public void deleteAllOrderStates() {
        recordLatencyIn(latencyTimer(Meters.OrderStateQueryType.DELETE_ALL))
            .of(orderStateRepository::deleteAllOrderStates);
    }

    @Override
    public Optional<CancelReplaceRequestEntity> findCancelReplaceRequestByOrderId(String newOrderId, String origOrderId) {
        return recordLatencyIn(latencyTimer(Meters.CancelReplaceQueryType.FIND))
            .of(() -> orderStateRepository.findCancelReplaceRequestByOrderId(newOrderId, origOrderId));
    }

    @Override
    public void saveCancelReplaceRequest(CancelReplaceRequestEntity cancelReplaceRequestEntity) {
        recordLatencyIn(latencyTimer(Meters.CancelReplaceQueryType.SAVE))
            .of(() -> orderStateRepository.saveCancelReplaceRequest(cancelReplaceRequestEntity));
    }

    @Override
    public void deleteCancelReplaceRequest(CancelReplaceRequestEntity cancelReplaceRequestEntity) {
        recordLatencyIn(latencyTimer(Meters.CancelReplaceQueryType.DELETE))
            .of(() -> orderStateRepository.deleteCancelReplaceRequest(cancelReplaceRequestEntity));
    }

    private Timer latencyTimer(Meters.OrderStateQueryType orderStateQueryType) {
        try {
            return Meters.orderStateQueryLatencyTimer(meterRegistry, orderStateQueryType);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }

    private Timer latencyTimer(Meters.OrderStateSnapshotQueryType orderStateSnapshotQueryType) {
        try {
            return Meters.orderStateSnapshotQueryLatencyTimer(meterRegistry, orderStateSnapshotQueryType);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }

    private Timer latencyTimer(Meters.CancelReplaceQueryType cancelReplaceQueryType) {
        try {
            return Meters.cancelReplaceQueryLatencyTimer(meterRegistry, cancelReplaceQueryType);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}