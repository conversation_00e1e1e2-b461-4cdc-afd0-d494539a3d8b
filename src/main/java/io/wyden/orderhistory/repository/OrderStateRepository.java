package io.wyden.orderhistory.repository;

import io.wyden.orderhistory.model.CancelReplaceRequestEntity;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.OrderStateEntity;

import java.util.List;
import java.util.Optional;

@Deprecated
public interface OrderStateRepository {

    Optional<OrderStateEntity> findOrderStateSnapshotByOrderId(String orderId);

    List<OrderStateEntity> findOrderStateSnapshotsBySearchInput(OrderHistorySearchInput orderHistorySearchInput);

    List<OrderStateEntity> findOrderStatesBySearchInput(OrderHistorySearchInput orderHistorySearchInput);

    Integer countOrderStateSnapshotsBySearchInput(OrderHistorySearchInput orderHistorySearchInput);

    Integer countOrderStatesBySearchInput(OrderHistorySearchInput orderHistorySearchInput);

    void saveOrderStateSnapshot(OrderStateEntity entity);

    void saveOrderState(OrderStateEntity entity);

    void updateOrderStateSnapshot(OrderStateEntity state);

    void deleteAllOrderStateSnapshots();

    void deleteAllOrderStates();

    Optional<CancelReplaceRequestEntity> findCancelReplaceRequestByOrderId(String newOrderId, String origOrderId);

    void saveCancelReplaceRequest(CancelReplaceRequestEntity entity);

    void deleteCancelReplaceRequest(CancelReplaceRequestEntity cancelReplaceRequestEntity);
}
