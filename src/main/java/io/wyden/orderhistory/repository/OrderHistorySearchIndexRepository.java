package io.wyden.orderhistory.repository;

import io.wyden.orderhistory.model.OrderHistorySearchIndex;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.Optional;

@Repository
public interface OrderHistorySearchIndexRepository extends JpaRepository<OrderHistorySearchIndex, String>, JpaSpecificationExecutor<OrderHistorySearchIndex> {

    /**
     * Find search index by order ID
     */
    Optional<OrderHistorySearchIndex> findByOrderId(String orderId);

    Optional<OrderHistorySearchIndex> findByOrderIdOrderByLatestMessageTimestampAsc(String orderId);

    Optional<OrderHistorySearchIndex> findByOrderIdOrderByLatestMessageTimestampDesc(String orderId);

    /**
     * Upsert search index with conditional update based on timestamp
     * Only updates if the new timestamp is newer than existing one
     */
    @Transactional
    @Modifying
    @Query(value = """
        INSERT INTO order_history_search_index (
            order_id, latest_message_timestamp, latest_event_id, portfolio_id, portfolio_type,
            venue_accounts, instrument_id, cl_order_id, tif, result, reason, order_status,
            order_category, parent_order_id, root_order_id, ext_order_id, created_at, updated_at
        ) VALUES (
            :orderId, :latestMessageTimestamp, :latestEventId, :portfolioId, :portfolioType,
            :venueAccounts, :instrumentId, :clOrderId, :tif, :result, :reason, :orderStatus,
            :orderCategory, :parentOrderId, :rootOrderId, :extOrderId, :createdAt, :updatedAt
        )
        ON CONFLICT (order_id) DO UPDATE SET
            latest_message_timestamp = EXCLUDED.latest_message_timestamp,
            latest_event_id = EXCLUDED.latest_event_id,
            portfolio_id = EXCLUDED.portfolio_id,
            portfolio_type = EXCLUDED.portfolio_type,
            venue_accounts = EXCLUDED.venue_accounts,
            instrument_id = EXCLUDED.instrument_id,
            cl_order_id = EXCLUDED.cl_order_id,
            tif = EXCLUDED.tif,
            result = EXCLUDED.result,
            reason = EXCLUDED.reason,
            order_status = EXCLUDED.order_status,
            order_category = EXCLUDED.order_category,
            parent_order_id = EXCLUDED.parent_order_id,
            root_order_id = EXCLUDED.root_order_id,
            ext_order_id = EXCLUDED.ext_order_id,
            updated_at = EXCLUDED.updated_at
        WHERE EXCLUDED.latest_message_timestamp > order_history_search_index.latest_message_timestamp
        """, nativeQuery = true)
    void upsertSearchIndex(
        @Param("orderId") String orderId,
        @Param("latestMessageTimestamp") OffsetDateTime latestMessageTimestamp,
        @Param("latestEventId") Long latestEventId,
        @Param("portfolioId") String portfolioId,
        @Param("portfolioType") String portfolioType,
        @Param("venueAccounts") String[] venueAccounts,
        @Param("instrumentId") String instrumentId,
        @Param("clOrderId") String clOrderId,
        @Param("tif") String tif,
        @Param("result") String result,
        @Param("reason") String reason,
        @Param("orderStatus") String orderStatus,
        @Param("orderCategory") String orderCategory,
        @Param("parentOrderId") String parentOrderId,
        @Param("rootOrderId") String rootOrderId,
        @Param("extOrderId") String extOrderId,
        @Param("createdAt") OffsetDateTime createdAt,
        @Param("updatedAt") OffsetDateTime updatedAt
    );
}
