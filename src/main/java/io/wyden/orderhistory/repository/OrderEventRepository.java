package io.wyden.orderhistory.repository;

import io.wyden.orderhistory.model.OrderEventEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OrderEventRepository extends JpaRepository<OrderEventEntity, Long>, JpaSpecificationExecutor<OrderEventEntity> {

    /**
     * Find all events for a specific order, ordered by message timestamp
     */
    List<OrderEventEntity> findByOrderIdOrderByMessageTimestampAsc(String orderId);

    /**
     * Find the latest event for a specific order
     */
    @Query("SELECT e FROM OrderEventEntity e WHERE e.orderId = :orderId ORDER BY e.messageTimestamp DESC LIMIT 1")
    Optional<OrderEventEntity> findLatestEventByOrderId(@Param("orderId") String orderId);

    /**
     * Find latest events for multiple order IDs
     */
    @Query(
        value = """
        SELECT * FROM (
            SELECT DISTINCT ON (e.order_id) *
            FROM order_events e
            WHERE e.order_id IN :orderIds
            ORDER BY e.order_id, e.message_timestamp DESC
        ) AS latest_events
        """,
        nativeQuery = true
    )
    Slice<OrderEventEntity> findLatestEventsByOrderIds(@Param("orderIds") List<String> orderIds, Pageable pageable);

    @Query("""
        SELECT e FROM OrderEventEntity e
        WHERE e.orderId IN :orderIds
        AND e.messageTimestamp = (SELECT MAX(e2.messageTimestamp) FROM OrderEventEntity e2 WHERE e2.orderId = e.orderId)
        """)
    Slice<OrderEventEntity> findLatestEventsByOrderIds2(@Param("orderIds") List<String> orderIds, Pageable pageable);
}