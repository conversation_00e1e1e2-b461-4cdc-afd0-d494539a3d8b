package io.wyden.orderhistory.infrastructure.rabbit;

import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitHealthCheckConfiguration {

    @Bean
    public AbstractHealthIndicator rabbitHealthIndicator(RabbitIntegrator rabbitIntegrator) {
        return new AbstractHealthIndicator() {
            @Override
            protected void doHealthCheck(Health.Builder builder) {
                builder.up().withDetail("version", getVersion());

                try {
                    rabbitIntegrator.checkHealth();
                } catch (Throwable e) {
                    builder.down().withException(e);
                }
            }

            private String getVersion() {
                return rabbitIntegrator.getServerProperties().get("version").toString();
            }
        };
    }
}
