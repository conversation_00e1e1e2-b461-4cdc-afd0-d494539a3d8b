package io.wyden.orderhistory.infrastructure.rabbit;

import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.context.Context;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.destination.TradingMessageParser;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.orderhistory.service.inbound.ClientRequestHandler;
import io.wyden.orderhistory.service.inbound.ClientResponseHandler;
import io.wyden.orderhistory.service.inbound.OemsRequestHandler;
import io.wyden.orderhistory.service.inbound.OemsResponseHandler;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Map;
import javax.annotation.Nullable;

@Deprecated
@Component
@ConditionalOnProperty(name = "message.consumer.use.v1", havingValue = "true", matchIfMissing = true)
public class TradingMessageConsumer implements MessageConsumer<Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradingMessageConsumer.class);

    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<Message> tradingIngressExchange;
    private final ClientRequestHandler clientRequestHandler;
    private final ClientResponseHandler clientResponseHandler;
    private final OemsRequestHandler oemsRequestHandler;
    private final OemsResponseHandler oemsResponseHandler;
    private final Tracing otlTracing;
    private final String queueName;
    private final String consumerName;
    private String consumerTag;

    TradingMessageConsumer(RabbitIntegrator rabbitIntegrator,
                           RabbitExchange<Message> tradingIngressExchange,
                           ClientRequestHandler clientRequestHandler,
                           ClientResponseHandler clientResponseHandler,
                           OemsRequestHandler oemsRequestHandler,
                           OemsResponseHandler oemsResponseHandler,
                           Tracing otlTracing,
                           @Value("${rabbitmq.trading-order-history-queue}") String queueName,
                           @Value("${spring.application.name}") String consumerName) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.tradingIngressExchange = tradingIngressExchange;
        this.clientRequestHandler = clientRequestHandler;
        this.clientResponseHandler = clientResponseHandler;
        this.oemsRequestHandler = oemsRequestHandler;
        this.oemsResponseHandler = oemsResponseHandler;
        this.otlTracing = otlTracing;
        this.queueName = queueName;
        this.consumerName = consumerName;
        declareQueue();
    }

    @Override
    public ConsumptionResult consume(Message message, AMQP.BasicProperties properties) {
        Context parent = otlTracing.loadContext(RabbitHeadersPropagator.create(properties.getHeaders()), RabbitHeadersPropagator.getter());
        try (var ignored = otlTracing.createBaggage(parent)) {
            try (var ignored2 = otlTracing.createSpan("request.consume", SpanKind.CONSUMER, parent)) {
                return consumeInner(message, properties);
            }
        }
    }

    private ConsumptionResult consumeInner(@Nullable Message data, AMQP.BasicProperties properties) {
        LOGGER.debug("Received new Trading message. Properties: {}", properties);

        try {
            if (data == null) {
                LOGGER.error("Message parsing failed");
                Span.current().setStatus(StatusCode.ERROR, "Message parsing failed");
                return ConsumptionResult.failureNonRecoverable();
            } else if (data instanceof ClientRequest clientRequest) {
                return clientRequestHandler.consume(clientRequest, properties);
            } else if (data instanceof ClientResponse clientResponse) {
                return clientResponseHandler.consume(clientResponse, properties);
            } else if (data instanceof OemsRequest oemsRequest) {
                return oemsRequestHandler.consume(oemsRequest, properties);
            } else if (data instanceof OemsResponse oemsResponse) {
                return oemsResponseHandler.consume(oemsResponse, properties);
            } else {
                LOGGER.error("Message type not supported: {}", data.getClass().getSimpleName());
                Span.current().setStatus(StatusCode.ERROR, "Message type not supported");
                return ConsumptionResult.failureNonRecoverable();
            }
        } catch (Exception e) {
            LOGGER.error("Failed to process message", e);
            Span.current().setStatus(StatusCode.ERROR);
            Span.current().recordException(e);
            return ConsumptionResult.failureNeedsRequeue();
        }
    }

    private void declareQueue() {
        LOGGER.info("Declaring queue {} for trading messages (ClientRequest, ClientResponse, OemsRequest, OemsResponse).", queueName);
        RabbitQueue<Message> queue = new RabbitQueueBuilder<>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .setSingleActiveConsumer(true)
            .declare();

        bindQueue(queue, tradingIngressExchange, Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), ClientRequest.class.getSimpleName()
        ));
        bindQueue(queue, tradingIngressExchange, Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), ClientResponse.class.getSimpleName()
        ));
        bindQueue(queue, tradingIngressExchange, Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName()
        ));
        bindQueue(queue, tradingIngressExchange, Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName()
        ));

        consumerTag = queue.attachConsumer(TradingMessageParser.parser(), this);
    }

    private <T extends Message> void bindQueue(RabbitQueue<Message> queue, RabbitExchange<Message> exchange, Map<String, Object> headers) {
        LOGGER.info("Binding exchange {} and queue {} with headers {}", exchange.getName(), queue.getName(), headers);
        queue.bindWithHeaders(exchange, MatchingCondition.ALL, headers);
    }

    public String getConsumerTag() {
        return consumerTag;
    }
}
