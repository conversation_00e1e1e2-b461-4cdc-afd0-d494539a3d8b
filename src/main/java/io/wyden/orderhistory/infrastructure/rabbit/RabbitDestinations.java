package io.wyden.orderhistory.infrastructure.rabbit;

import com.google.protobuf.Message;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.published.reporting.OrderState;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class RabbitDestinations {

    @Bean
    public RabbitExchange<Message> tradingIngressExchange(RabbitIntegrator rabbitIntegrator) {
        return OemsExchange.Trading.declareIngressExchange(rabbitIntegrator);
    }

    @Bean
    public RabbitExchange<Message> tradingRouterExchange(RabbitIntegrator rabbitIntegrator) {
        return OemsExchange.Trading.declareRouterExchange(rabbitIntegrator);
    }

    @Bean
    public RabbitExchange<Message> tradingDLX(RabbitIntegrator rabbitIntegrator) {
        return OemsExchange.Trading.declareDLX(rabbitIntegrator);
    }

    @Bean
    public RabbitExchange<OrderState> orderStateExchange(RabbitIntegrator rabbitIntegrator) {
        return OemsExchange.OrderHistory.declareOrderStatesExchange(rabbitIntegrator);
    }
}
