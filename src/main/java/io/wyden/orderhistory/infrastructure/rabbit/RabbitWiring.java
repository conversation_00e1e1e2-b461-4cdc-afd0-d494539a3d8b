package io.wyden.orderhistory.infrastructure.rabbit;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
public class RabbitWiring {

    @Bean
    public RabbitIntegrator rabbitIntegrator(@Value("${rabbitmq.username}") String userName,
                                             @Value("${rabbitmq.password}") String password,
                                             @Value("${rabbitmq.virtualHost}") String virtualHost,
                                             @Value("${rabbitmq.host}") String host,
                                             @Value("${rabbitmq.port}") int port,
                                             @Value("${rabbitmq.tls}") String tls,
                                             ExecutorService consumptionExecutor,
                                             ExecutorService publishingExecutor) {
        return new RabbitIntegrator(userName, password, virtualHost, host, port, tls, consumptionExecutor, publishingExecutor);
    }

    @Bean("consumptionExecutor")
    public ExecutorService consumptionExecutor(@Value("${rabbitmq.consumer.threads:1}") int threads) {
        return Executors.newFixedThreadPool(threads, new ThreadFactoryBuilder().setNameFormat("m-consumer-%d").build());
    }

    @Bean("publishingExecutor")
    public ExecutorService publishingExecutor(@Value("${rabbitmq.publisher.threads:1}") int threads) {
        return Executors.newFixedThreadPool(threads, new ThreadFactoryBuilder().setNameFormat("m-producer-%d").build());
    }
}
