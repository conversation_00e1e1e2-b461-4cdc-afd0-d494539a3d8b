package io.wyden.orderhistory.infrastructure.rabbit;

import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.context.Context;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.destination.TradingMessageParser;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.orderhistory.repository.OrderEventRepository;
import io.wyden.orderhistory.service.AsyncOrderStateProcessor;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.Map;
import java.util.Objects;

import static io.wyden.orderhistory.service.utils.ProtobufUtils.toMessageType;

@Component
@ConditionalOnProperty(name = "message.consumer.use.v2", havingValue = "true")
public class TradingResponseMessageConsumer implements MessageConsumer<Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradingResponseMessageConsumer.class);

    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<Message> tradingIngressExchange;
    private final OrderEventRepository orderEventRepository;
    private final AsyncOrderStateProcessor asyncProcessor;
    private final Tracing otlTracing;
    private final String queueName;
    private final String consumerName;
    private String consumerTag;

    public TradingResponseMessageConsumer(
            RabbitIntegrator rabbitIntegrator,
            RabbitExchange<Message> tradingIngressExchange,
            OrderEventRepository orderEventRepository,
            AsyncOrderStateProcessor asyncProcessor,
            Tracing otlTracing,
            @Value("${rabbitmq.trading-order-history-queue-responses:order-history-responses}") String queueName,
            @Value("${order.history.consumer.name:order-history-responses-consumer}") String consumerName) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.tradingIngressExchange = tradingIngressExchange;
        this.orderEventRepository = orderEventRepository;
        this.asyncProcessor = asyncProcessor;
        this.otlTracing = otlTracing;
        this.queueName = queueName;
        this.consumerName = consumerName;
        declareQueue();
    }

    @Override
    public ConsumptionResult consume(Message message, AMQP.BasicProperties properties) {
        Context parent = otlTracing.loadContext(RabbitHeadersPropagator.create(properties.getHeaders()), RabbitHeadersPropagator.getter());
        try (var ignored = otlTracing.createBaggage(parent)) {
            try (var ignored2 = otlTracing.createSpan("responses.consume", SpanKind.CONSUMER, parent)) {
                LOGGER.trace("Received new Trading message: {}", message);
                return consumeInner(message, properties);
            }
        }
    }

    private ConsumptionResult consumeInner(Message data, AMQP.BasicProperties properties) {
        LOGGER.debug("Received new Trading message. Properties: {}", properties);
        try {
            if (Objects.isNull(data)) {
                LOGGER.error("Message parsing failed");
                Span.current().setStatus(StatusCode.ERROR, "Message parsing failed");
                return ConsumptionResult.failureNonRecoverable();
            }

            if (isResponseWithRequest(data)) {
                OrderEventEntity orderEvent = storeEvent(data);
                asyncProcessor.scheduleProcessing(orderEvent);
                LOGGER.debug("Stored event {}", orderEvent);
            } else {
                LOGGER.debug("Skipping message type: {} (not a Response with Request)", data.getClass().getSimpleName());
            }
            return ConsumptionResult.consumed();
        } catch (Exception ex) {
            LOGGER.error("Failed to process message, dropping", ex);
            Span.current().setStatus(StatusCode.ERROR);
            Span.current().recordException(ex);
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    private boolean isResponseWithRequest(Message message) {
        if (message instanceof OemsResponse oemsResponse) {
            return oemsResponse.hasRequest();
        } else if (message instanceof ClientResponse clientResponse) {
            return clientResponse.hasRequest();
        }
        return false;
    }

    private OrderEventEntity storeEvent(Message message) {
        OrderEventEntity event = OrderEventEntity.builder()
            .orderId(extractOrderId(message))
            .messageType(toMessageType(message.getClass().getSimpleName()))
            .messageTimestamp(extractMessageTimestamp(message))
            .protoBlob(message.toByteArray())
            .build();

        return orderEventRepository.save(event);
    }

    private String extractOrderId(Message message) {
        if (message instanceof OemsResponse oemsResponse) {
            return oemsResponse.getOrderId();
        } else if (message instanceof ClientResponse clientResponse) {
            return clientResponse.getOrderId();
        }
        throw new IllegalArgumentException("Unsupported message type for order ID extraction: " + message.getClass());
    }

    private OffsetDateTime extractMessageTimestamp(Message message) {
        if (message instanceof OemsResponse oemsResponse) {
            Metadata metadata = oemsResponse.getMetadata();
            String createdAt = metadata.getCreatedAt();
            Instant instant = DateUtils.isoUtcTimeToInstant(createdAt);

            if (Objects.nonNull(instant)) {
                return OffsetDateTime.ofInstant(instant, DateUtils.UTC);
            }
        } else if (message instanceof ClientResponse clientResponse) {
            Metadata metadata = clientResponse.getMetadata();
            String createdAt = metadata.getCreatedAt();
            Instant instant = DateUtils.isoUtcTimeToInstant(createdAt);

            if (Objects.nonNull(instant)) {
                return OffsetDateTime.ofInstant(instant, DateUtils.UTC);
            }
        }

        LOGGER.warn("No reliable timestamp found in message, using current time");
        return OffsetDateTime.now();
    }

    private void declareQueue() {
        LOGGER.info("Declaring queue {} for trading messages (OemsResponse, ClientResponse).", queueName);
        RabbitQueue<Message> queue = new RabbitQueueBuilder<>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .setSingleActiveConsumer(true)
            .declare();

        // Bind only Response messages
        bindQueue(queue, tradingIngressExchange, Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), ClientResponse.class.getSimpleName()
        ));
        bindQueue(queue, tradingIngressExchange, Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName()
        ));

        consumerTag = queue.attachConsumer(TradingMessageParser.parser(), this);
    }

    private <T extends Message> void bindQueue(RabbitQueue<Message> queue, RabbitExchange<Message> exchange, Map<String, Object> headers) {
        LOGGER.info("Binding exchange {} and queue {} with headers {}", exchange.getName(), queue.getName(), headers);
        queue.bindWithHeaders(exchange, MatchingCondition.ALL, headers);
    }

    public String getConsumerTag() {
        return consumerTag;
    }
}
