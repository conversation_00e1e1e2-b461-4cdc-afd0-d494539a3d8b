package io.wyden.orderhistory.infrastructure.telemetry;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.TracingUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TelemetryConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(TelemetryConfiguration.class);

    private static final String SERVICE = "order-history";
    private static final String SCOPE = "io.wyden.orderhistory";
    private static final String VERSION = TracingUtil.getAppVersion(TelemetryConfiguration.class);

    private final MeterRegistry meterRegistry;
    private final String tracingEndpoint;
    private Tracing tracing;

    public TelemetryConfiguration(MeterRegistry meterRegistry, @Value("${tracing.collector.endpoint:disabled}") String tracingEndpoint) {
        this.tracingEndpoint = tracingEndpoint;
        this.meterRegistry = meterRegistry;
    }

    @PostConstruct
    void setup() {
        tracing = TracingUtil.createTracing(this.tracingEndpoint, SERVICE, SCOPE, VERSION);
        Meters.configure(meterRegistry);
    }

    @PreDestroy
    void destroy() {
        try {
            tracing.close();
        } catch (Exception ex) {
            LOGGER.error("Exception when closing tracing service", ex);
        }
    }

    @Bean
    public Tracing otlTracing() {
        return this.tracing;
    }

    @Bean
    public Telemetry telemetry() {
        return new Telemetry(tracing, meterRegistry);
    }
}
