package io.wyden.orderhistory.infrastructure.telemetry;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.wyden.published.client.ClientRequestType;
import io.wyden.published.client.ClientResponseType;
import io.wyden.published.oems.OemsRequest.OemsRequestType;
import io.wyden.published.oems.OemsResponse.OemsResponseType;

import java.time.Duration;

import static io.wyden.cloudutils.telemetry.metrics.MetricsConfigUtils.createMeterFilter;
import static io.wyden.cloudutils.telemetry.metrics.MetricsConfigUtils.createTimer;
import static io.wyden.cloudutils.telemetry.metrics.MetricsConfigUtils.registerMeterFilter;

@Deprecated
public final class Meters {

    public static final String TRADING_CLIENT_REQUEST_INCOMING_LATENCY = "wyden.history.trading.client.request.incoming.latency";
    public static final String TRADING_CLIENT_RESPONSE_INCOMING_LATENCY = "wyden.history.trading.client.response.incoming.latency";
    public static final String TRADING_OEMS_REQUEST_INCOMING_LATENCY = "wyden.history.trading.oems.request.incoming.latency";
    public static final String TRADING_OEMS_RESPONSE_INCOMING_LATENCY = "wyden.history.trading.oems.response.incoming.latency";

    public static final String HISTORY_STATE_QUERY_LATENCY = "wyden.history.state.query.latency";
    public static final String HISTORY_SNAPSHOT_QUERY_LATENCY = "wyden.history.snapshot.query.latency";
    public static final String HISTORY_CANCEL_REPLACE_QUERY_LATENCY = "wyden.history.cancel-replace.query.latency";

    public static final String MESSAGE_TYPE_TAG = "messageType";
    public static final String QUERY_TYPE_TAG = "queryType";

    public enum OrderStateQueryType {
        SAVE,
        UPDATE,
        COUNT_BY_PROPERTIES,
        FIND_BY_ID,
        FIND_BY_PROPERTIES,
        DELETE_ALL
    }

    public enum OrderStateSnapshotQueryType {
        SAVE,
        UPDATE,
        COUNT_BY_PROPERTIES,
        FIND_BY_ID,
        FIND_BY_PROPERTIES,
        DELETE_ALL
    }

    public enum CancelReplaceQueryType {
        SAVE,
        FIND,
        DELETE
    }

    private Meters() {
        // Empty
    }

    public static void configure(MeterRegistry meterRegistry) {
        registerMeterFilter(meterRegistry, createMeterFilter(TRADING_CLIENT_REQUEST_INCOMING_LATENCY, Duration.ofMillis(0), Duration.ofMillis(100)));
        registerMeterFilter(meterRegistry, createMeterFilter(TRADING_CLIENT_RESPONSE_INCOMING_LATENCY, Duration.ofMillis(0), Duration.ofMillis(50)));
        registerMeterFilter(meterRegistry, createMeterFilter(TRADING_OEMS_REQUEST_INCOMING_LATENCY, Duration.ofMillis(0), Duration.ofMillis(100)));
        registerMeterFilter(meterRegistry, createMeterFilter(TRADING_OEMS_RESPONSE_INCOMING_LATENCY, Duration.ofMillis(0), Duration.ofMillis(50)));

        registerMeterFilter(meterRegistry, createMeterFilter(HISTORY_STATE_QUERY_LATENCY, Duration.ofMillis(0), Duration.ofMillis(20)));
        registerMeterFilter(meterRegistry, createMeterFilter(HISTORY_SNAPSHOT_QUERY_LATENCY, Duration.ofMillis(0), Duration.ofMillis(20)));
        registerMeterFilter(meterRegistry, createMeterFilter(HISTORY_CANCEL_REPLACE_QUERY_LATENCY, Duration.ofMillis(0), Duration.ofMillis(20)));
    }

    public static Timer tradingClientRequestIncomingLatencyTimer(MeterRegistry meterRegistry, ClientRequestType requestType) {
        return createTimer(meterRegistry, TRADING_CLIENT_REQUEST_INCOMING_LATENCY, MESSAGE_TYPE_TAG, requestType.name());
    }

    public static Timer tradingClientResponseIncomingLatencyTimer(MeterRegistry meterRegistry, ClientResponseType responseType) {
        return createTimer(meterRegistry, TRADING_CLIENT_RESPONSE_INCOMING_LATENCY, MESSAGE_TYPE_TAG, responseType.name());
    }

    public static Timer tradingOemsRequestIncomingLatencyTimer(MeterRegistry meterRegistry, OemsRequestType requestType) {
        return createTimer(meterRegistry, TRADING_OEMS_REQUEST_INCOMING_LATENCY, MESSAGE_TYPE_TAG, requestType.name());
    }

    public static Timer tradingOemsResponseIncomingLatencyTimer(MeterRegistry meterRegistry, OemsResponseType responseType) {
        return createTimer(meterRegistry, TRADING_OEMS_RESPONSE_INCOMING_LATENCY, MESSAGE_TYPE_TAG, responseType.name());
    }

    public static Timer orderStateQueryLatencyTimer(MeterRegistry meterRegistry, OrderStateQueryType orderStateQueryType) {
        return createTimer(meterRegistry, HISTORY_STATE_QUERY_LATENCY, QUERY_TYPE_TAG, orderStateQueryType.name());
    }

    public static Timer orderStateSnapshotQueryLatencyTimer(MeterRegistry meterRegistry, OrderStateSnapshotQueryType orderStateSnapshotQueryType) {
        return createTimer(meterRegistry, HISTORY_SNAPSHOT_QUERY_LATENCY, QUERY_TYPE_TAG, orderStateSnapshotQueryType.name());
    }

    public static Timer cancelReplaceQueryLatencyTimer(MeterRegistry meterRegistry, CancelReplaceQueryType cancelReplaceQueryType) {
        return createTimer(meterRegistry, HISTORY_CANCEL_REPLACE_QUERY_LATENCY, QUERY_TYPE_TAG, cancelReplaceQueryType.name());
    }
}
