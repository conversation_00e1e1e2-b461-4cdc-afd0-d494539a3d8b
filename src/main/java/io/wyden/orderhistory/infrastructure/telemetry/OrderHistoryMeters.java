package io.wyden.orderhistory.infrastructure.telemetry;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;

import static io.wyden.cloudutils.telemetry.metrics.MetricsConfigUtils.createMeterFilter;
import static io.wyden.cloudutils.telemetry.metrics.MetricsConfigUtils.createTimer;
import static io.wyden.cloudutils.telemetry.metrics.MetricsConfigUtils.registerMeterFilter;

@Component
public class OrderHistoryMeters {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderHistoryMeters.class);

    // Query operation metrics
    public static final String SEARCH_INDEX_QUERY_LATENCY = "wyden.history.search-index.query.latency";
    public static final String ORDER_EVENT_QUERY_LATENCY = "wyden.history.order-event.query.latency";
    public static final String ORDER_STATE_PROCESSING_LATENCY = "wyden.history.order-state.processing.latency";

    // Counters
    public static final String ERROR_COUNTER = "wyden.history.error.counter";

    // Common tags
    public static final String QUERY_TYPE_TAG = "queryType";
    public static final String OPERATION_TAG = "operation";

    private final MeterRegistry meterRegistry;

    public OrderHistoryMeters(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        configureMeterFilters();
    }

    public enum SearchIndexQueryType {
        FIND_ALL_WITH_SPECIFICATION,
        BUILD_SPECIFICATION,
        BUILD_PAGEABLE
    }

    public enum OrderEventQueryType {
        FIND_LATEST_BY_ORDER_IDS,
        FIND_ALL_BY_ORDER_IDS_ORDER_BY_TIMESTAMP,
        FIND_EVENTS_BY_ORDER_IDS_WITH_LIMIT,
        FIND_ALL_BY_ORDER_IDS_GROUPED
    }

    public enum ProcessingOperation {
        BUILD_ORDER_STATE_FROM_EVENT,
        DESERIALIZE_PROTO,
        BUILD_ORDER_STATE_FROM_RESPONSE,
        APPLY_EVENT_LEVEL_PAGINATION,
        CREATE_CURSOR_CONNECTION
    }

    private void configureMeterFilters() {
        registerMeterFilter(meterRegistry, createMeterFilter(SEARCH_INDEX_QUERY_LATENCY, Duration.ofMillis(0), Duration.ofMillis(200)));
        registerMeterFilter(meterRegistry, createMeterFilter(ORDER_EVENT_QUERY_LATENCY, Duration.ofMillis(0), Duration.ofMillis(500)));
        registerMeterFilter(meterRegistry, createMeterFilter(ORDER_STATE_PROCESSING_LATENCY, Duration.ofMillis(0), Duration.ofMillis(100)));
    }

    // Search index query timers
    public Timer searchIndexQueryLatencyTimer(SearchIndexQueryType queryType) {
        return safeCreateTimer(SEARCH_INDEX_QUERY_LATENCY, QUERY_TYPE_TAG, queryType.name());
    }

    // Order event query timers
    public Timer orderEventQueryLatencyTimer(OrderEventQueryType queryType) {
        return safeCreateTimer(ORDER_EVENT_QUERY_LATENCY, QUERY_TYPE_TAG, queryType.name());
    }

    public Timer orderEventQueryLatencyTimer(OrderEventQueryType queryType, int orderCount) {
        return safeCreateTimer(ORDER_EVENT_QUERY_LATENCY, QUERY_TYPE_TAG, queryType.name(), "orderCount", String.valueOf(orderCount));
    }

    // Processing operation timers
    public Timer orderStateProcessingLatencyTimer(ProcessingOperation operation) {
        return safeCreateTimer(ORDER_STATE_PROCESSING_LATENCY, OPERATION_TAG, operation.name());
    }

    // Service method timers
    public Timer serviceMethodTimer(String methodName) {
        return safeCreateTimer("wyden.history.service.method.latency", "method", methodName);
    }

    public Timer serviceMethodTimer(String methodName, int resultCount) {
        return safeCreateTimer("wyden.history.service.method.latency", "method", methodName, "resultCount", String.valueOf(resultCount));
    }

    public void incrementMethodErrorCounter(String methodName) {
        incrementCounter(ERROR_COUNTER, "method", methodName);
    }

    private Timer safeCreateTimer(String name, String... tags) {
        try {
            return createTimer(meterRegistry, name, tags);
        } catch (Exception e) {
            LOGGER.warn("Unable to create timer '{}' with tags: {}", name, String.join(",", tags), e);
            return new EmptyTimer();
        }
    }

    private void incrementCounter(String counterName, String... tags) {
        try {
            meterRegistry.counter(counterName, tags).increment();
        } catch (Exception ex) {
            LOGGER.warn("Counter increment failed", ex);
        }
    }
}