package io.wyden.orderhistory.infrastructure.telemetry;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;

@Component
@Aspect
public class MetricsAspect {

    private final OrderHistoryMeters metrics;

    public MetricsAspect(OrderHistoryMeters metrics) {
        this.metrics = metrics;
    }

    @Around("@annotation(recordLatency)")
    public Object recordLatency(ProceedingJoinPoint joinPoint, RecordLatency recordLatency) {
        String methodName = recordLatency.value();
        return recordLatencyIn(metrics.serviceMethodTimer(methodName)).of(() -> {
            try{
                return joinPoint.proceed();
            } catch (Throwable e) {
                metrics.incrementMethodErrorCounter(methodName);
                throw new RuntimeException(e);
            }
        });
    }
}
