package io.wyden.orderhistory.infrastructure.hazelcast;

import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.client.ReferenceDataProvider;
import io.wyden.referencedata.domain.PortfolioMapConfig;

import com.hazelcast.client.HazelcastClient;
import com.hazelcast.client.config.ClientConfig;
import com.hazelcast.client.config.ClientNetworkConfig;
import com.hazelcast.core.HazelcastInstance;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

import static io.wyden.cloudutils.hazelcast.NearCaches.defaultEvictionConfig;
import static io.wyden.cloudutils.hazelcast.NearCaches.defaultNearCacheConfig;

@Configuration
public class HazelcastConfig {

    @Bean
    public ClientConfig createClientConfig(@Value("${hz.addressList}") String addressList,
                                           @Value("${hz.outboundPortDefinition}") String outboundPortDefinition,
                                           List<HazelcastMapConfig> hazelcastMaps,
                                           PortfolioMapConfig portfolioMapConfig) {
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setInstanceName("order_history");
        clientConfig.getConnectionStrategyConfig().getConnectionRetryConfig().setMaxBackoffMillis(5000);

        ClientNetworkConfig networkConfig = clientConfig.getNetworkConfig();
        Arrays.stream(addressList.split(",")).forEach(networkConfig::addAddress);
        networkConfig.setSmartRouting(true);

        if (!outboundPortDefinition.isBlank()) {
            networkConfig.addOutboundPortDefinition(outboundPortDefinition);
        }

        networkConfig.setRedoOperation(true);
        networkConfig.setConnectionTimeout(5000);

        hazelcastMaps.forEach(m -> m.applyConfig(clientConfig));

        portfolioMapConfig.addNearCache(clientConfig, defaultNearCacheConfig(defaultEvictionConfig()));

        return clientConfig;
    }

    @Bean("hazelcast")
    public HazelcastInstance createHazelcastInstance(ClientConfig clientConfig, Telemetry telemetry, List<HazelcastMapConfig> hazelcastMaps) {
        HazelcastInstance hz = HazelcastClient.newHazelcastClient(clientConfig);
        hazelcastMaps.forEach(m -> m.setupMemberInstance(hz));
        hazelcastMaps.forEach(m -> m.setupMetrics(hz, telemetry.getMeterRegistry()));
        return hz;
    }

    @Bean
    ReferenceDataProvider referenceDataProvider() {
        return new ReferenceDataProvider();
    }

    @Bean
    public PortfolioMapConfig portfolioMapConfig() {
        return new PortfolioMapConfig();
    }

    @Bean
    public PortfoliosCacheFacade portfoliosCacheFacade(HazelcastInstance hazelcast,
                                                       ReferenceDataProvider referenceDataProvider,
                                                       Tracing otlTracing) {

        return referenceDataProvider.getPortfoliosCacheFacade(hazelcast, otlTracing);
    }

}
