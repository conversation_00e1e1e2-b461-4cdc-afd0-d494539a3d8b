package io.wyden.oems.storage.web;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.accessgateway.domain.permission.PermissionGroupListMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionGroupMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionUserListMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionUserMapConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

import static io.wyden.oems.storage.config.accessgateway.PermissionsJDBCQueries.E2ECleanupQueries.DELETE_FROM_CLIENTS_PERMISSION;
import static io.wyden.oems.storage.config.accessgateway.PermissionsJDBCQueries.E2ECleanupQueries.DELETE_FROM_GROUPS_PERMISSION;
import static io.wyden.oems.storage.config.accessgateway.PermissionsJDBCQueries.E2ECleanupQueries.DELETE_FROM_WYDEN_CLIENTS;
import static io.wyden.oems.storage.config.accessgateway.PermissionsJDBCQueries.E2ECleanupQueries.DELETE_FROM_WYDEN_GROUPS;
import static io.wyden.oems.storage.config.accessgateway.PermissionsJDBCQueries.E2ECleanupQueries.DELETE_FROM_WYDEN_PERMISSIONS;

@RestController
public class E2ETestsCleaningController {

    private static final Logger LOGGER = LoggerFactory.getLogger(E2ETestsCleaningController.class);

    private final JdbcTemplate jdbc;
    private final IMap<String, String> userPermissions;
    private final IMap<String, String> groupPermissions;
    private final IMap<String, Set<String>> userPermissionList;
    private final IMap<String, Set<String>> groupPermissionList;

    public E2ETestsCleaningController(JdbcTemplate jdbc, @Autowired HazelcastInstance hz) {
        this.jdbc = jdbc;
        this.userPermissions = PermissionUserMapConfig.getMap(hz);
        this.groupPermissions = PermissionGroupMapConfig.getMap(hz);
        this.userPermissionList = PermissionUserListMapConfig.getMap(hz);
        this.groupPermissionList = PermissionGroupListMapConfig.getMap(hz);
    }

    @DeleteMapping("/db/cleanup")
    void deleteUserPermissions() {
        LOGGER.debug("Removing all permissions for users starting with e2e");

        int clientPermissionDeletedRows = executeQuery(DELETE_FROM_CLIENTS_PERMISSION);
        LOGGER.debug("Removed {} rows from clients_permission", clientPermissionDeletedRows);

        int groupsPermissionDeletedRows = executeQuery(DELETE_FROM_GROUPS_PERMISSION);
        LOGGER.debug("Removed {} rows from groups_permissions", groupsPermissionDeletedRows);

        int wydenClientsDeletedRows = executeQuery(DELETE_FROM_WYDEN_CLIENTS);
        LOGGER.debug("Removed {} rows from wyden_client", wydenClientsDeletedRows);

        int wydenGroupDeletedRows = executeQuery(DELETE_FROM_WYDEN_GROUPS);
        LOGGER.debug("Removed {} rows from wyden_group", wydenGroupDeletedRows);

        int wydenPermissionDeletedRows = executeQuery(DELETE_FROM_WYDEN_PERMISSIONS);
        LOGGER.debug("Removed {} rows from wyden_permission", wydenPermissionDeletedRows);

        LOGGER.debug("Evicting hazelcast permissions");
        userPermissions.evictAll();
        groupPermissions.evictAll();
        userPermissionList.evictAll();
        groupPermissionList.evictAll();
    }

    private int executeQuery(String query) {
        try {
            return jdbc.update(query);
        } catch (Exception e) {
            LOGGER.warn("Problem while executing query: {}", query, e);
            return 0;
        }
    }
}
