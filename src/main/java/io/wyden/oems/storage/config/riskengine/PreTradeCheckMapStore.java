package io.wyden.oems.storage.config.riskengine;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.oems.storage.config.ProtobufMapStore;
import io.wyden.oems.storage.config.StringRowMapper;
import io.wyden.published.risk.PreTradeCheck;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;


@Component
public class PreTradeCheckMapStore extends ProtobufMapStore<String, PreTradeCheck> {

    private static final String LOAD_ALL_QUERY = "select id from risk_pre_trade_checks";
    private static final String LOAD_QUERY = "select payload from risk_pre_trade_checks where id=?";
    private static final String PSQL_STORE_QUERY = "insert into risk_pre_trade_checks (id, payload) values (?, ?) on conflict (id) do update set payload=?";
    private static final String ORACLE_STORE_QUERY = "merge into risk_pre_trade_checks using dual on (id=?) when matched then update set payload=? when not matched then insert (id, payload) values (?, ?)";
    private static final String DELETE_QUERY = "delete from risk_pre_trade_checks where id=?";

    private static final String ID = "id";

    private final String dbEngine;

    protected PreTradeCheckMapStore(JdbcTemplate jdbc, MeterRegistry meterRegistry, @Value("${db.engine}") String dbEngine) {
        super(jdbc, PreTradeCheck.parser(), meterRegistry);
        this.dbEngine = dbEngine;
    }

    @Override
    protected void storeRecord(String key, PreTradeCheck value) {
        byte[] payload = value.toByteArray();
        if ("oracle".equalsIgnoreCase(dbEngine)) {
            jdbc.update(ORACLE_STORE_QUERY, key, payload, key, payload);
        } else {
            jdbc.update(PSQL_STORE_QUERY, key, payload, payload);
        }
    }

    @Override
    protected String loadQuery() {
        return LOAD_QUERY;
    }

    @Override
    public Iterable<String> loadAllKeys() {
        return jdbc.query(LOAD_ALL_QUERY, new StringRowMapper(ID));
    }

    @Override
    protected void deleteRecord(String key) {
        jdbc.update(DELETE_QUERY, key);
    }
}
