package io.wyden.oems.storage.config.ordergateway.clientorderstate;

import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.ordergateway.domain.map.ClientOrderStateMapConfig;
import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.oems.storage.config.HazelcastMapConfigBean;
import io.wyden.oems.storage.config.StoredMapConfigBean;
import io.wyden.oems.storage.web.StringProtobufMapWebAccess;
import io.wyden.oems.storage.web.WebAccess;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static io.wyden.oems.storage.config.MapStores.defaultNoCoalescingStoreConfig;

@Configuration
public class ClientOrderStateMapConfiguration {

    @Bean
    HazelcastMapConfigBean clientOrderStateMapConfig(ClientOrderStateMapStore mapStore,
                                                     @Value("${hz.clientorders.writedelay}") int writeDelay,
                                                     @Value("${hz.clientorders.mapstore}") boolean mapStoreEnabled)
    {
        HazelcastMapConfig hazelcastMapConfig = new ClientOrderStateMapConfig();
        MapStoreConfig mapStoreConfig = defaultNoCoalescingStoreConfig(mapStore, writeDelay, mapStoreEnabled);
        return new StoredMapConfigBean(hazelcastMapConfig, mapStoreConfig);
    }

    @Bean
    WebAccess clientOrderStateMapWebAccess(HazelcastInstance hazelcast, ClientOrderStateMapStore mapStore) {
        HazelcastMapConfig hazelcastMapConfig = new ClientOrderStateMapConfig();
        return new StringProtobufMapWebAccess<>(hazelcast, hazelcastMapConfig, ClientOrderState.getDefaultInstance(), mapStore);
    }
}
