package io.wyden.oems.storage.config.restapiserver.orderstate;

import com.hazelcast.core.HazelcastInstance;
import io.wyden.apiserver.domain.OrderStateMapConfig;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.storage.config.EphemeralMapConfigBean;
import io.wyden.oems.storage.config.HazelcastMapConfigBean;
import io.wyden.oems.storage.web.StringProtobufMapWebAccess;
import io.wyden.oems.storage.web.WebAccess;
import io.wyden.published.reporting.OrderState;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderStateMapConfiguration {

    @Bean
    HazelcastMapConfigBean orderStateMapConfig() {
        HazelcastMapConfig hazelcastMapConfig = new OrderStateMapConfig();
        return new EphemeralMapConfigBean(hazelcastMapConfig);
    }

    @Bean
    WebAccess orderStateMapWebAccess(HazelcastInstance hazelcast) {
        HazelcastMapConfig hazelcastMapConfig = new OrderStateMapConfig();
        return new StringProtobufMapWebAccess<>(hazelcast, hazelcastMapConfig, OrderState.getDefaultInstance(), null);
    }
}
