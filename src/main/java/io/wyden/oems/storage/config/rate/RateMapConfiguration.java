package io.wyden.oems.storage.config.rate;

import com.hazelcast.core.HazelcastInstance;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.storage.config.EphemeralMapConfigBean;
import io.wyden.oems.storage.config.HazelcastMapConfigBean;
import io.wyden.oems.storage.web.RateKeyRateWebAccess;
import io.wyden.oems.storage.web.WebAccess;
import io.wyden.published.rate.Rate;
import io.wyden.rate.domain.map.RateMapConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RateMapConfiguration {

    @Bean
    HazelcastMapConfigBean rateMapConfig() {
        HazelcastMapConfig hazelcastMapConfig = new RateMapConfig();
        return new EphemeralMapConfigBean(hazelcastMapConfig);
    }

    @Bean
    WebAccess rateMapWebAccess(HazelcastInstance hazelcast) {
        HazelcastMapConfig hazelcastMapConfig = new RateMapConfig();
        return new RateKeyRateWebAccess(hazelcast, hazelcastMapConfig, Rate.getDefaultInstance(), null);
    }
}
