package io.wyden.oems.storage.config;

import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.TextFormat;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.MapLoaderLifecycleSupport;
import com.hazelcast.map.MapStore;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import org.apache.commons.lang3.RegExUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

public abstract class BaseMapStore<K, V> implements MapStore<K, V>, MapLoaderLifecycleSupport {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseMapStore.class);

    protected final JdbcTemplate jdbc;
    protected final MeterRegistry meterRegistry;

    protected String mapName;
    protected Timer loadTimer;
    protected Timer storeTimer;
    protected Timer deleteTimer;

    protected BaseMapStore(JdbcTemplate jdbc, MeterRegistry meterRegistry) {
        this.jdbc = jdbc;
        this.meterRegistry = meterRegistry;
    }

    @Override
    public void init(HazelcastInstance hazelcastInstance, Properties properties, String mapName) {
        this.mapName = RegExUtils.removePattern(mapName, "_.*$");
        this.loadTimer = meterRegistry.timer("mapstore.load", Tags.of("map", this.mapName));
        this.storeTimer = meterRegistry.timer("mapstore.store", Tags.of("map", this.mapName));
        this.deleteTimer = meterRegistry.timer("mapstore.delete", Tags.of("map", this.mapName));
    }

    @Override
    public void destroy() {
        // no-op
    }

    public String getMapName() {
        return mapName;
    }

    protected abstract void storeRecord(K key, V value);

    @Override
    public void store(K key, V value) {
        long startTime = System.currentTimeMillis();
        try {
            LOGGER.debug("Storing record {} for map {} for key={}", value.getClass().getSimpleName(), mapName, debugString(key));
            storeRecord(key, value);
        } finally {
            updateMetrics(storeTimer, startTime);
        }
    }

    @Override
    public void storeAll(Map<K, V> map) {
        map.forEach(this::store);
    }

    protected void deleteRecord(K key) {
    }

    @Override
    public void delete(K key) {
        long startTime = System.currentTimeMillis();
        try {
            LOGGER.debug("Deleting record for map {} for key={}", mapName, debugString(key));
            deleteRecord(key);
        } finally {
            updateMetrics(deleteTimer, startTime);
        }
    }

    @Override
    public void deleteAll(Collection<K> keys) {
        keys.forEach(this::delete);
    }

    protected abstract V loadRecord(K key);

    @Override
    public V load(K key) {
        long startTime = System.currentTimeMillis();
        try {
            LOGGER.debug("Loading record for map {} for key={}", mapName, debugString(key));
            return loadRecord(key);
        } finally {
            updateMetrics(loadTimer, startTime);
        }
    }

    @Override
    public Map<K, V> loadAll(Collection<K> keys) {
        Map<K, V> result = new HashMap<>();
        for (K key : keys) {
            try {
                V value = load(key);
                if (value != null) {
                    result.put(key, value);
                } else {
                    LOGGER.warn("Loading record for map {} failed for key {}, null value returned", mapName, debugString(key));
                }
            } catch (Exception ex) {
                LOGGER.warn("Loading record for map {} failed for key {}", mapName, debugString(key), ex);
            }
        }
        return result;
    }

    @Override
    public Iterable<K> loadAllKeys() {
        return List.of();
    }

    protected void updateMetrics(Timer timer, long startTime) {
        try {
            timer.record(System.currentTimeMillis() - startTime, TimeUnit.MILLISECONDS);
        } catch (Throwable th) {
            LOGGER.warn("Exception when updating metrics", th);
        }
    }

    protected String debugString(Object object) {
        if (object == null) {
            return "null";
        } else if (object instanceof MessageOrBuilder message) {
            return TextFormat.shortDebugString(message);
        } else {
            return object.toString();
        }
    }
}
