package io.wyden.smartrecommendationengine.service.audit;

import io.wyden.audit.client.AuditEventsClient;
import io.wyden.published.audit.AuditEventPayload;
import io.wyden.published.audit.OrderBookPayload;
import io.wyden.published.audit.PayloadType;
import io.wyden.published.marketdata.MarketDataEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AuditService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuditService.class);
    private static final String SMART_RECOMMENDATION_ENGINE = "smart-recommendation-engine";

    private final AuditEventsClient auditEventsClient;

    public AuditService(AuditEventsClient auditEventsClient) {
        this.auditEventsClient = auditEventsClient;
    }

    public void sendAuditEventWithOrderBooks(List<MarketDataEvent> marketDataEvents, String orderId, String requestId) {
        AuditEventPayload auditEventPayload = AuditEventPayload.newBuilder()
            .setType(PayloadType.ORDER_BOOK)
            .setOrderBookPayload(OrderBookPayload.newBuilder()
                .addAllOrderBookEvent(marketDataEvents)
                .setOrderId(orderId)
                .setRequestId(requestId)
                .build())
            .build();

        sendAuditEvent(auditEventPayload);
    }

    private void sendAuditEvent(AuditEventPayload auditEventPayload) {
        LOGGER.info("Sending new audit event: {}", auditEventPayload);
        auditEventsClient.emitAuditEvent(SMART_RECOMMENDATION_ENGINE, auditEventPayload);
    }
}
