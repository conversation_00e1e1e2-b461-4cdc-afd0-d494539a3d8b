package io.wyden.booking.reporting.interfaces.rabbitmq;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.wyden.booking.reporting.domain.transaction.ExecType;
import io.wyden.booking.reporting.domain.transaction.Transaction;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.TransactionToProtoMapper;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.booking.TransactionSnapshot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static java.util.Collections.emptyMap;

@Component
public class TransactionCreatedEventEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionCreatedEventEmitter.class);

    private final RabbitExchange<TransactionSnapshot> transactionCreatedExchange;
    private final boolean enabled;
    private final MeterRegistry meterRegistry;

    public TransactionCreatedEventEmitter(RabbitExchange<TransactionSnapshot> transactionCreatedExchange,
                                          @Value("${booking-engine.publisher.transaction-created.enabled}") boolean enabled,
                                          Telemetry telemetry) {
        this.transactionCreatedExchange = transactionCreatedExchange;
        this.enabled = enabled;
        this.meterRegistry = telemetry.getMeterRegistry();

        LOGGER.info("TransactionCreatedEventEmitter is {}.", enabled ? "enabled" : "disabled");
    }

    public void emit(Transaction transaction) {
        if (!enabled) {
            return;
        }

        if (isRejectedOrCanceled(transaction)) {
            LOGGER.trace("Transaction was rejected or cancelled. It will not be emitted as transaction created: \n{}", transaction);
            return;
        }

        TransactionSnapshot transactionSnapshot = TransactionToProtoMapper.map(transaction);

        LOGGER.info("Emitting transaction created event to {}: \n{}", transactionCreatedExchange.getName(), transaction);

        transactionCreatedExchange.tryPublishWithHeaders(transactionSnapshot, emptyMap());
        updateMetrics();
    }

    private static boolean isRejectedOrCanceled(Transaction transaction) {
        return ExecType.REJECTED == transaction.getExecType()
               || ExecType.CANCELED == transaction.getExecType()
               || ExecType.EXPIRED == transaction.getExecType();
    }

    private void updateMetrics() {
        String name = "wyden.booking-reporting.event-outgoing";
        try {
            Tags tags = Tags.of(
                "eventType", "TransactionSnapshot"
            );
            meterRegistry.counter(name, tags).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
