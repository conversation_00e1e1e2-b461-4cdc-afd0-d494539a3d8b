package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.application.referencedata.AccountSnapshot;
import io.wyden.booking.reporting.application.referencedata.PortfolioSnapshot;
import io.wyden.booking.reporting.domain.balance.Balance;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.published.booking.BalanceSnapshot;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.referencedata.WalletType;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public final class BalanceFromProtoMapper {

    private BalanceFromProtoMapper() {
    }

    public static Balance map(BalanceSnapshot proto, long sequenceNumber, PortfolioSnapshot portfolioSnapshot, AccountSnapshot accountSnapshot) {
        Balance.Builder builder = Balance.builder()
            .symbol(proto.getSymbol())
            .accountId(proto.getAccountId())
            .portfolioId(proto.getPortfolioId())
            .currency(proto.getCurrency())
            .quantity(bd(proto.getQuantity()))
            .pendingQuantity(bd(proto.getPendingQuantity()))
            .availableForTradingQuantity(bd(proto.getAvailableForTradingQuantity()))
            .availableForWithdrawalQuantity(bd(proto.getAvailableForWithdrawalQuantity()))
            .settledQuantity(bd(proto.getSettledQuantity()))
            .unsettledQuantity(bd(proto.getUnsettledQuantity()))
            .lastAppliedLedgerEntryId(proto.getLastAppliedLedgerEntryId())
            .sequenceNumber(sequenceNumber);

        if (isNotBlank(proto.getPortfolioId())) {
            builder.portfolioId(proto.getPortfolioId())
                .portfolioType(portfolioSnapshot.portfolioType());
        } else if (isNotBlank(proto.getAccountId())) {
            builder.accountId(proto.getAccountId())
                .accountType(accountSnapshot.accountType())
                .accountWalletType(accountSnapshot.walletType());
        } else {
            throw new IllegalArgumentException("Portfolio id and account id cannot both be empty. Portfolio id: %s, account id: %s (BalanceSnapshot: %s)"
                .formatted(proto.getPortfolioId(), proto.getAccountId(), proto));
        }

        return builder.build();
    }

    public static RequestModel.PortfolioType map(PortfolioType portfolioType) {
        return switch (portfolioType) {
            case VOSTRO -> RequestModel.PortfolioType.VOSTRO;
            case NOSTRO -> RequestModel.PortfolioType.NOSTRO;
            default -> null;
        };
    }

    public static RequestModel.AccountType map(AccountType accountType) {
        return switch (accountType) {
            case WALLET -> RequestModel.AccountType.WALLET;
            case EXCHANGE -> RequestModel.AccountType.EXCHANGE;
            case CUSTODY -> RequestModel.AccountType.CUSTODY;
            case ACCOUNT_TYPE_CLOB -> RequestModel.AccountType.CLOB;
            default -> null;
        };
    }

    public static RequestModel.WalletType map(WalletType walletType) {
        return switch (walletType) {
            case WALLET_VOSTRO -> RequestModel.WalletType.VOSTRO;
            case WALLET_NOSTRO -> RequestModel.WalletType.NOSTRO;
            default -> null;
        };
    }
}
