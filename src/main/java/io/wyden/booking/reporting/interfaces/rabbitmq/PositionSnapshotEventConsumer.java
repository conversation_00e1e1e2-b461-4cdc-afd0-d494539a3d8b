package io.wyden.booking.reporting.interfaces.rabbitmq;

import com.rabbitmq.client.AMQP;
import io.wyden.booking.reporting.application.position.PositionCommandService;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.published.booking.PositionSnapshot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class PositionSnapshotEventConsumer implements MessageConsumer<PositionSnapshot> {

    private static final Logger LOGGER = LoggerFactory.getLogger(PositionSnapshotEventConsumer.class);
    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<PositionSnapshot> positionPnlCalculatedExchange;
    private final RabbitQueue<PositionSnapshot> queue;

    private final PositionCommandService positionCommandService;

    public PositionSnapshotEventConsumer(RabbitIntegrator rabbitIntegrator,
                                         RabbitExchange<PositionSnapshot> positionPnlCalculatedExchange,
                                         @Value("${rabbitmq.booking-reporting-queue.position-snapshot.name}") String queueName,
                                         PositionCommandService positionCommandService) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.positionPnlCalculatedExchange = positionPnlCalculatedExchange;
        this.queue = declareQueue(queueName);

        // removing old queue
        rabbitIntegrator.tryDeleteQueue("booking.booking-reporting-queue.POSITION-SNAPSHOT");

        this.positionCommandService = positionCommandService;
    }

    private RabbitQueue<PositionSnapshot> declareQueue(String queueName) {
        RabbitQueue<PositionSnapshot> queue = new RabbitQueueBuilder<PositionSnapshot>(rabbitIntegrator)
            .setQueueName(queueName)
            .declare();

        queue.attachConsumer(PositionSnapshot.parser(), this);

        Map<String, Object> headerMatchers = Map.of();
        queue.bindWithHeaders(positionPnlCalculatedExchange, MatchingCondition.ALL, headerMatchers);
        LOGGER.info("Binding exchange {} and queue {} with headers {}", positionPnlCalculatedExchange, queue, headerMatchers);
        return queue;
    }

    @Override
    public ConsumptionResult consume(PositionSnapshot positionSnapshot, AMQP.BasicProperties properties) {
        try {
            LOGGER.info("Received PositionSnapshot: \n{}", positionSnapshot);
            positionCommandService.handlePositionSnapshot(positionSnapshot);
        } catch (Exception e) {
            LOGGER.error("Failed to consume position snapshot event \n{}", positionSnapshot, e);
            return ConsumptionResult.failureNonRecoverable();
        }

        return ConsumptionResult.consumed();
    }
}
