package io.wyden.booking.reporting.interfaces.rest;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum PositionType {

    CASH_POSITION(Values.CASH_POSITION),
    ASSET_POSITION(Values.ASSET_POSITION);

    private final String positionType;

    PositionType(String positionType) {
        // force equality between name of enum instance, and value of constant
        if (!this.name().equals(positionType)) {
            throw new IllegalArgumentException("Position type has to match enum value");
        }
        this.positionType = positionType;
    }

    public static List<String> stringValues() {
        return Arrays.stream(values())
            .map(value -> value.positionType)
            .collect(Collectors.toList());
    }

    public static class Values {
        public static final String CASH_POSITION = "CASH_POSITION";
        public static final String ASSET_POSITION = "ASSET_POSITION";
    }
}
