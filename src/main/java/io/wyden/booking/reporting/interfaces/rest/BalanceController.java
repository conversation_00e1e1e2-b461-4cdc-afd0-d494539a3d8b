package io.wyden.booking.reporting.interfaces.rest;

import io.wyden.booking.reporting.application.balance.BalanceQueryService;
import io.wyden.booking.reporting.domain.balance.Balance;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.BalanceToProtoMapper;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.PositionFromProtoMapper;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.rest.pagination.PaginationToProtoMapper;
import io.wyden.published.booking.PositionSearch;
import io.wyden.published.common.CursorConnection;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.http.MediaType.APPLICATION_PROTOBUF_VALUE;

@RestController
public class BalanceController {

    private static final Logger LOGGER = getLogger(BalanceController.class);

    private final BalanceQueryService balanceQueryService;

    public BalanceController(BalanceQueryService balanceQueryService) {
        this.balanceQueryService = balanceQueryService;
    }

    @PostMapping(value = "/balances/search", params = "version=proto", produces = APPLICATION_PROTOBUF_VALUE)
    public CursorConnection searchProto(@RequestBody PositionSearch request) {
        LOGGER.info("Lookup balances requested for: {}", request);

        RequestModel.PositionSearch balanceSearch = PositionFromProtoMapper.map(request);
        PaginationModel.CursorConnection<Balance> connection = balanceQueryService.search(balanceSearch);

        LOGGER.info("Lookup finished with {} balances: {}", connection.getAllNodes().size(), connection);
        return PaginationToProtoMapper.map(connection, BalanceToProtoMapper::mapToCursorNode);
    }
}
