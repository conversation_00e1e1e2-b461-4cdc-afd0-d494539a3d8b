package io.wyden.booking.reporting.interfaces.rabbitmq;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.PositionToProtoMapper;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.booking.PositionSnapshot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
public class PositionSnapshotEventEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(PositionSnapshotEventEmitter.class);
    private final RabbitExchange<PositionSnapshot> positionChangedExchange;
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;
    private final boolean enabled;

    public PositionSnapshotEventEmitter(RabbitExchange<PositionSnapshot> positionChangedExchange,
                                        Telemetry telemetry,
                                        @Value("${booking-engine.publisher.position-snapshot.enabled}") boolean enabled) {
        this.positionChangedExchange = positionChangedExchange;
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
        this.enabled = enabled;

        LOGGER.info("PositionSnapshotEventEmitter is {}.", enabled ? "enabled" : "disabled");
    }

    public void emit(Position position) {
        if (!enabled) {
            return;
        }

        LOGGER.debug("Calculating position value: {}", position);
        PositionSnapshot positionSnapshot = PositionToProtoMapper.map(position);
        try (var ignored = otlTracing.createSpan("booking.command.postprocess.position.emitpositionchangedhandling", SpanKind.PRODUCER)) {
            emitInternally(position, positionSnapshot);
        }
    }

    public Position emitInternally(Position position, PositionSnapshot positionChanged) {
        Map<String, String> headers = new HashMap<>();

        // Add symbol as currency header if available
        if (position.getSymbol() != null) {
            headers.put(OemsHeader.CURRENCY.getHeaderName(), position.getSymbol());
        }

        // Add portfolio reference if available
        if (position.getPortfolioId() != null) {
            headers.put(OemsHeader.PORTFOLIO_REFERENCE.getHeaderName(), position.getPortfolioId());
        }

        // Add account reference if available
        if (position.getAccountId() != null) {
            headers.put(OemsHeader.VENUE_ACCOUNT_REFERENCE.getHeaderName(), position.getAccountId());
        }

        LOGGER.info("Emitting position update to: {}, headers: {}\n{}", positionChangedExchange.getName(), headers, positionChanged);

        updateMetrics("wyden.booking-reporting.event-outgoing", positionChanged);
        positionChangedExchange.tryPublishWithHeaders(positionChanged, headers);

        return position;
    }

    @SuppressWarnings("SameParameterValue")
    private void updateMetrics(String name, PositionSnapshot positionSnapshot) {
        try {
            String reference = isNotBlank(positionSnapshot.getPortfolio()) ? positionSnapshot.getPortfolio() : positionSnapshot.getAccount();
            Tags tags = Tags.of(
                "eventType", "PositionSnapshot",
                "reference", reference,
                "symbol", positionSnapshot.getSymbol()
            );
            this.meterRegistry.counter(name, tags).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
