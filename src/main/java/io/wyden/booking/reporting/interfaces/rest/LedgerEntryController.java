package io.wyden.booking.reporting.interfaces.rest;

import io.wyden.booking.reporting.application.LedgerEntryService;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.LedgerEntryFromProtoMapper;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.LedgerEntryToProtoMapper;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.rest.pagination.PaginationToProtoMapper;
import io.wyden.published.booking.LedgerEntrySearch;
import io.wyden.published.common.CursorConnection;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
public class LedgerEntryController {

    private static final Logger LOGGER = getLogger(LedgerEntryController.class);

    private final LedgerEntryService ledgerEntryService;

    public LedgerEntryController(LedgerEntryService ledgerEntryService) {
        this.ledgerEntryService = ledgerEntryService;
    }

    @PostMapping(value = "/ledger-entries/search", params = "version=proto")
    public CursorConnection search(@RequestBody LedgerEntrySearch request) {
        LOGGER.info("Lookup ledger entries requested for: \n{}", request);

        RequestModel.LedgerEntrySearch ledgerEntrySearch = LedgerEntryFromProtoMapper.map(request);
        PaginationModel.CursorConnection<LedgerEntry> connection = ledgerEntryService.search(ledgerEntrySearch);

        LOGGER.info("Lookup finished with {} ledger entries: {}", connection.getAllNodes().size(), connection);
        return PaginationToProtoMapper.map(connection, LedgerEntryToProtoMapper::mapToCursorNode);
    }
}
