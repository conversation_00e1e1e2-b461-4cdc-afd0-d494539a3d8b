package io.wyden.booking.reporting.interfaces.rest;

import io.wyden.booking.reporting.application.position.PositionQueryService;
import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.PositionFromProtoMapper;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.PositionToProtoMapper;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.rest.pagination.PaginationToProtoMapper;
import io.wyden.published.booking.PositionSearch;
import io.wyden.published.common.CursorConnection;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.http.MediaType.APPLICATION_PROTOBUF_VALUE;

@RestController
public class PositionController {

    private static final Logger LOGGER = getLogger(PositionController.class);

    private final PositionQueryService positionQueryService;

    public PositionController(PositionQueryService positionQueryService) {
        this.positionQueryService = positionQueryService;
    }

    @PostMapping(value = "/positions/search", params = "version=proto", produces = APPLICATION_PROTOBUF_VALUE)
    public CursorConnection search(@RequestBody PositionSearch request) {
        LOGGER.info("Lookup positions requested for: {}", request);

        RequestModel.PositionSearch positionSearch = PositionFromProtoMapper.map(request);
        PaginationModel.CursorConnection<Position> connection = positionQueryService.search(positionSearch);

        LOGGER.info("Lookup finished with {} positions: {}", connection.getAllNodes().size(), connection);
        return PaginationToProtoMapper.map(connection, PositionToProtoMapper::mapToCursorNode);
    }

    @PostMapping(value = "/internal/positions/search", params = "version=proto", produces = APPLICATION_PROTOBUF_VALUE)
    public CursorConnection internalSearch(@RequestBody PositionSearch request) {
        LOGGER.info("Internal lookup positions requested for: {}", request);

        RequestModel.PositionSearch positionSearch = PositionFromProtoMapper.map(request);
        PaginationModel.CursorConnection<Position> connection = positionQueryService.searchInternal(positionSearch);

        LOGGER.info("Lookup finished with {} positions: {}", connection.getAllNodes().size(), connection);
        return PaginationToProtoMapper.map(connection, PositionToProtoMapper::mapToCursorNode);
    }

    @GetMapping("/position-types")
    public List<String> positionTypes() {
        return PositionType.stringValues();
    }
}
