package io.wyden.booking.reporting.interfaces.rest;

import io.wyden.booking.reporting.application.ReservationService;
import io.wyden.booking.reporting.domain.reservation.Reservation;
import io.wyden.booking.reporting.domain.reservation.ReservationBalance;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.ReservationFromProtoMapper;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.ReservationToProtoMapper;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.rest.pagination.PaginationToProtoMapper;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.OemsRequestWithVolatilityBuffer;
import io.wyden.published.booking.ReservationBalanceList;
import io.wyden.published.booking.ReservationSearch;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsRequest;
import org.slf4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.UUID;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
public class ReservationQueryController {

    private static final Logger LOGGER = getLogger(ReservationQueryController.class);

    private final ReservationService reservationService;

    public ReservationQueryController(ReservationService reservationService) {
        this.reservationService = reservationService;
    }

    @PostMapping(value = "/reservations/search", params = "version=proto")
    public CursorConnection searchProtoReservations(@RequestBody ReservationSearch request) {
        LOGGER.info("Lookup reservations requested for: \n{}", request);

        RequestModel.ReservationSearch reservationSearch = ReservationFromProtoMapper.map(request);
        PaginationModel.CursorConnection<Reservation> connection = reservationService.search(reservationSearch);

        LOGGER.info("Lookup finished with {} reservations: {}", connection.getAllNodes().size(), connection);
        return PaginationToProtoMapper.map(connection, ReservationToProtoMapper::mapToCursorNode);
    }

    @GetMapping("/reservations/{reservationRef}")
    public ReservationSnapshot getReservation(@PathVariable String reservationRef) {
        LOGGER.info("Get reservation by reservationRef: {}", reservationRef);
        return reservationService.findByReservationRef(reservationRef)
            .map(ReservationToProtoMapper::map)
            .orElseThrow(() -> new ResponseStatusException(
                HttpStatus.NOT_FOUND,
                "Reservation with reservationRef " + reservationRef + " not found"));
    }

    @GetMapping("/reservations/{reservationRef}/balances")
    public ReservationBalanceList getReservationBalances(@PathVariable String reservationRef) {
        LOGGER.info("Get reservation balances by reservationRef: {}", reservationRef);

        Collection<ReservationBalance> reservationBalances = reservationService.collectByReservationRef(reservationRef);
        return ReservationToProtoMapper.map(reservationBalances);
    }

    public static OemsRequestWithVolatilityBuffer asCommand(OemsRequest request, BigDecimal volatilityBuffer) {
        return OemsRequestWithVolatilityBuffer.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setRequestId(UUID.randomUUID().toString())
                .build())
            .setOemsRequest(request)
            .setVolatilityBuffer(volatilityBuffer.toPlainString())
            .build();
    }
}
