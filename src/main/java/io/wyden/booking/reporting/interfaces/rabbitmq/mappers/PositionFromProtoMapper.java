package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.application.referencedata.AccountSnapshot;
import io.wyden.booking.reporting.application.referencedata.PortfolioSnapshot;
import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.published.booking.PositionSearch;
import io.wyden.published.booking.PositionSnapshot;

import java.util.List;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public final class PositionFromProtoMapper {

    private PositionFromProtoMapper() {
    }

    public static Position map(PositionSnapshot positionSnapshot, PortfolioSnapshot portfolioSnapshot, AccountSnapshot accountSnapshot) {
         Position.Builder builder = Position.builder()
            .sequenceNumber(0L) // TODO SPL For now, we don't have sequence number in PositionSnapshot proto
            .symbol(positionSnapshot.getSymbol())
            .currency(positionSnapshot.getCurrency())
            .quantity(bd(positionSnapshot.getQuantity()))
            .notionalQuantity(bd(positionSnapshot.getNotionalQuantity()))
            .netCost(bd(positionSnapshot.getNetCost()))
            .netCostSc(bd(positionSnapshot.getNetCostSc()))
            .grossCost(bd(positionSnapshot.getGrossCost()))
            .grossCostSc(bd(positionSnapshot.getGrossCostSc()))
            .netRealizedPnl(bd(positionSnapshot.getNetRealizedPnl()))
            .netRealizedPnlSc(bd(positionSnapshot.getNetRealizedPnlSc()))
            .grossRealizedPnl(bd(positionSnapshot.getGrossRealizedPnl()))
            .grossRealizedPnlSc(bd(positionSnapshot.getGrossRealizedPnlSc()))
            .netAveragePrice(bd(positionSnapshot.getNetAveragePrice()))
            .grossAveragePrice(bd(positionSnapshot.getGrossAveragePrice()))
            .marketValue(bd(positionSnapshot.getMarketValue()))
            .marketValueSc(bd(positionSnapshot.getMarketValueSc()))
            .netUnrealizedPnl(bd(positionSnapshot.getNetUnrealizedPnl()))
            .netUnrealizedPnlSc(bd(positionSnapshot.getNetUnrealizedPnlSc()))
            .grossUnrealizedPnl(bd(positionSnapshot.getGrossUnrealizedPnl()))
            .grossUnrealizedPnlSc(bd(positionSnapshot.getGrossUnrealizedPnlSc()));

        if (isNotBlank(positionSnapshot.getPortfolio())) {
            builder.portfolioId(positionSnapshot.getPortfolio())
                .portfolioCurrency(portfolioSnapshot.portfolioCurrency())
                .portfolioType(portfolioSnapshot.portfolioType());
        } else if (isNotBlank(positionSnapshot.getAccount())) {
            builder.accountId(positionSnapshot.getAccount())
                .accountCurrency(accountSnapshot.accountCurrency())
                .accountType(accountSnapshot.accountType())
                .accountWalletType(accountSnapshot.walletType());
        } else {
            throw new IllegalArgumentException("Portfolio id and account id cannot both be empty. Portfolio id: %s, account id: %s (PositionSnapshot: %s)"
                .formatted(positionSnapshot.getPortfolio(), positionSnapshot.getAccount(), positionSnapshot));
        }

        if (isNotBlank(positionSnapshot.getLastAppliedLedgerEntryId())) {
            builder.lastAppliedLedgerEntryId(Long.parseLong(positionSnapshot.getLastAppliedLedgerEntryId()));
        }

        return builder.build();
    }

    public static RequestModel.PositionSearch map(final PositionSearch positionSearch) {
        return new RequestModel.PositionSearch(
            positionSearch.getSymbolList(),
            positionSearch.getCurrencyList(),
            positionSearch.getAccountIdList(),
            positionSearch.getPortfolioIdList(),
            null,
            null,
            null,
            List.of(),
            positionSearch.getOrderId(),
            positionSearch.getClientId(),
            positionSearch.getFirst(),
            positionSearch.getAfter(),
            TransactionFromProtoMapper.map(positionSearch.getSortingOrder())
        );
    }
}
