package io.wyden.booking.reporting.interfaces.rest;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;

public record SimpleSearchRequest(
    Collection<String> accountIds,
    Collection<String> portfolioIds,
    RequestModel.PortfolioType portfolioType,
    RequestModel.AccountType accountType,
    RequestModel.WalletType walletType,
    String clientId
) {

    public record AuthorizedSearchRequest(
        Collection<String> requestedAccountIds,
        Collection<String> requestedPortfolioIds,
        RequestModel.PortfolioType portfolioType,
        RequestModel.AccountType accountType,
        RequestModel.WalletType walletType,
        Collection<String> authorizedAccountIds,
        Collection<String> authorizedPortfolioIds,
        Collection<String> requestedAuthorizedAccountIds,
        Collection<String> requestedAuthorizedPortfolioIds,
        boolean allAccountsAuthorized,
        boolean allPortfoliosAuthorized,
        String clientId) {

        // Compact constructor with default values
        public AuthorizedSearchRequest {
            if (requestedAccountIds == null) requestedAccountIds = Collections.emptyList();
            if (requestedPortfolioIds == null) requestedPortfolioIds = Collections.emptyList();
            if (authorizedAccountIds == null) authorizedAccountIds = Collections.emptyList();
            if (authorizedPortfolioIds == null) authorizedPortfolioIds = Collections.emptyList();
            if (requestedAuthorizedAccountIds == null) requestedAuthorizedAccountIds = Collections.emptyList();
            if (requestedAuthorizedPortfolioIds == null) requestedAuthorizedPortfolioIds = Collections.emptyList();
            if (clientId == null) clientId = "";
        }

        public boolean isEmptyRequest() {
            return requestedAccountIds.isEmpty() && requestedPortfolioIds.isEmpty();
        }

        public boolean isRequestPortfoliosOnly() {
            return !requestedPortfolioIds.isEmpty() && requestedAccountIds.isEmpty();
        }

        public boolean isRequestAccountsOnly() {
            return requestedPortfolioIds.isEmpty() && !requestedAccountIds.isEmpty();
        }

        /**
         * Creates a new builder for AuthorizedSearchRequest
         *
         * @return A new builder instance
         */
        public static Builder builder() {
            return new Builder();
        }

        /**
         * Creates a builder instance pre-populated with this object's current data
         *
         * @return builder with current object's data
         */
        public Builder toBuilder() {
            return new Builder()
                .requestedAccountIds(requestedAccountIds)
                .requestedPortfolioIds(requestedPortfolioIds)
                .portfolioType(portfolioType)
                .accountType(accountType)
                .walletType(walletType)
                .authorizedAccountIds(authorizedAccountIds)
                .authorizedPortfolioIds(authorizedPortfolioIds)
                .requestedAuthorizedAccountIds(requestedAuthorizedAccountIds)
                .requestedAuthorizedPortfolioIds(requestedAuthorizedPortfolioIds)
                .allAccountsAuthorized(allAccountsAuthorized)
                .allPortfoliosAuthorized(allPortfoliosAuthorized)
                .clientId(clientId);
        }

        @Override
        public @NotNull String toString() {
            return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("requestedAccountIds", requestedAccountIds)
                .append("requestedPortfolioIds", requestedPortfolioIds)
                .append("portfolioType", portfolioType)
                .append("accountType", accountType)
                .append("walletType", walletType)
                .append("authorizedAccountIds", authorizedAccountIds)
                .append("authorizedPortfolioIds", authorizedPortfolioIds)
                .append("requestedAuthorizedAccountIds", requestedAuthorizedAccountIds)
                .append("requestedAuthorizedPortfolioIds", requestedAuthorizedPortfolioIds)
                .append("allAccountsAuthorized", allAccountsAuthorized)
                .append("allPortfoliosAuthorized", allPortfoliosAuthorized)
                .append("clientId", clientId)
                .toString();
        }

        /**
         * Builder for AuthorizedSearchRequest
         */
        public static class Builder {
            private Collection<String> requestedAccountIds = Collections.emptyList();
            private Collection<String> requestedPortfolioIds = Collections.emptyList();
            private RequestModel.PortfolioType portfolioType;
            private RequestModel.AccountType accountType;
            private RequestModel.WalletType walletType;
            private Collection<String> authorizedAccountIds = Collections.emptyList();
            private Collection<String> authorizedPortfolioIds = Collections.emptyList();
            private Collection<String> requestedAuthorizedAccountIds = Collections.emptyList();
            private Collection<String> requestedAuthorizedPortfolioIds = Collections.emptyList();
            private boolean allAccountsAuthorized = false;
            private boolean allPortfoliosAuthorized = false;
            private String clientId = "";

            /**
             * Creates a new builder instance from a SimpleSearchRequest
             *
             * @param request the SimpleSearchRequest to copy data from
             * @return a new builder instance with data from the request
             */
            public static Builder fromRequest(SimpleSearchRequest request) {
                return new Builder()
                    .requestedAccountIds(request.accountIds())
                    .requestedPortfolioIds(request.portfolioIds())
                    .portfolioType(request.portfolioType())
                    .accountType(request.accountType())
                    .walletType(request.walletType())
                    .clientId(request.clientId());
            }

            /**
             * Set the account IDs to filter by
             */
            public Builder requestedAccountIds(Collection<String> accountIds) {
                this.requestedAccountIds = accountIds != null ? new ArrayList<>(accountIds) : Collections.emptyList();
                return this;
            }

            /**
             * Set the portfolio IDs to filter by
             */
            public Builder requestedPortfolioIds(Collection<String> portfolioIds) {
                this.requestedPortfolioIds = portfolioIds != null ? new ArrayList<>(portfolioIds) : Collections.emptyList();
                return this;
            }

            /**
             * Set the portfolio type filter
             */
            public Builder portfolioType(RequestModel.PortfolioType portfolioType) {
                this.portfolioType = portfolioType;
                return this;
            }

            /**
             * Set the account type filter
             */
            public Builder accountType(RequestModel.AccountType accountType) {
                this.accountType = accountType;
                return this;
            }

            /**
             * Set the wallet type filter
             */
            public Builder walletType(RequestModel.WalletType walletType) {
                this.walletType = walletType;
                return this;
            }

            /**
             * Set the authorized account IDs
             */
            public Builder authorizedAccountIds(Collection<String> authorizedAccountIds) {
                this.authorizedAccountIds = authorizedAccountIds != null ? new ArrayList<>(authorizedAccountIds) : Collections.emptyList();
                return this;
            }

            /**
             * Set the authorized portfolio IDs
             */
            public Builder authorizedPortfolioIds(Collection<String> authorizedPortfolioIds) {
                this.authorizedPortfolioIds = authorizedPortfolioIds != null ? new ArrayList<>(authorizedPortfolioIds) : Collections.emptyList();
                return this;
            }

            /**
             * Set the requested authorized account IDs
             */
            public Builder requestedAuthorizedAccountIds(Collection<String> requestedAuthorizedAccountIds) {
                this.requestedAuthorizedAccountIds = requestedAuthorizedAccountIds != null ? new ArrayList<>(requestedAuthorizedAccountIds) : Collections.emptyList();
                return this;
            }

            /**
             * Set the requested authorized portfolio IDs
             */
            public Builder requestedAuthorizedPortfolioIds(Collection<String> requestedAuthorizedPortfolioIds) {
                this.requestedAuthorizedPortfolioIds = requestedAuthorizedPortfolioIds != null ? new ArrayList<>(requestedAuthorizedPortfolioIds) : Collections.emptyList();
                return this;
            }

            /**
             * Set whether all accounts are authorized
             */
            public Builder allAccountsAuthorized(boolean allAccountsAuthorized) {
                this.allAccountsAuthorized = allAccountsAuthorized;
                return this;
            }

            /**
             * Set whether all portfolios are authorized
             */
            public Builder allPortfoliosAuthorized(boolean allPortfoliosAuthorized) {
                this.allPortfoliosAuthorized = allPortfoliosAuthorized;
                return this;
            }

            /**
             * Set the client ID
             */
            public Builder clientId(String clientId) {
                this.clientId = clientId != null ? clientId : "";
                return this;
            }

            /**
             * Builds a new AuthorizedSearchRequest instance with the configured values
             *
             * @return new AuthorizedSearchRequest instance
             */
            public AuthorizedSearchRequest build() {
                return new AuthorizedSearchRequest(
                    requestedAccountIds,
                    requestedPortfolioIds,
                    portfolioType,
                    accountType,
                    walletType,
                    authorizedAccountIds,
                    authorizedPortfolioIds,
                    requestedAuthorizedAccountIds,
                    requestedAuthorizedPortfolioIds,
                    allAccountsAuthorized,
                    allPortfoliosAuthorized,
                    clientId
                );
            }
        }
    }
}
