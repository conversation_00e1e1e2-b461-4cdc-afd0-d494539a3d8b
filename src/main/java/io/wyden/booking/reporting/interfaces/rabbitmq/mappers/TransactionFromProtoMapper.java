package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.application.referencedata.ReferenceDataSnapshot;
import io.wyden.booking.reporting.domain.transaction.ExecType;
import io.wyden.booking.reporting.domain.transaction.SettlementType;
import io.wyden.booking.reporting.domain.transaction.Transaction;
import io.wyden.booking.reporting.domain.transaction.TransactionFee;
import io.wyden.booking.reporting.domain.transaction.TransactionFeeType;
import io.wyden.booking.reporting.domain.transaction.TransactionType;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.AccountCashTransferSnapshot;
import io.wyden.published.booking.ClientAssetTradeSnapshot;
import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.DepositSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeSnapshot;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.PortfolioAssetTransferSnapshot;
import io.wyden.published.booking.PortfolioCashTransferSnapshot;
import io.wyden.published.booking.StreetAssetTradeSnapshot;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSearch;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.WithdrawalSnapshot;
import io.wyden.published.common.SortingOrder;
import io.wyden.published.common.TernaryBool;
import io.wyden.published.oems.OemsExecType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static io.wyden.booking.reporting.domain.transaction.TransactionType.ACCOUNT_CASH_TRANSFER;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.CLIENT_ASSET_TRADE;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.CLIENT_CASH_TRADE;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.DEPOSIT;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.FEE;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.PORTFOLIO_ASSET_TRANSFER;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.PORTFOLIO_CASH_TRANSFER;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.SETTLEMENT;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.STREET_ASSET_TRADE;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.STREET_CASH_TRADE;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.WITHDRAWAL;
import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static io.wyden.cloudutils.tools.DateUtils.epochMillisToZonedDateTime;
import static io.wyden.cloudutils.tools.DateUtils.isoUtcTimeToZonedDateTime;
import static org.slf4j.LoggerFactory.getLogger;

public class TransactionFromProtoMapper {

    private static final Logger LOGGER = getLogger(TransactionFromProtoMapper.class);

    private TransactionFromProtoMapper() {
    }

    public static Optional<Transaction> map(TransactionSnapshot transactionSnapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        if (transactionSnapshot == null) {
            return Optional.empty();
        }

        Transaction transaction = switch (transactionSnapshot.getTransactionCase()) {
            case CLIENT_CASH_TRADE -> map(transactionSnapshot.getClientCashTrade(), sequenceNumber, referenceDataSnapshot);
            case STREET_CASH_TRADE -> map(transactionSnapshot.getStreetCashTrade(), sequenceNumber, referenceDataSnapshot);
            case CLIENT_ASSET_TRADE -> map(transactionSnapshot.getClientAssetTrade(), sequenceNumber, referenceDataSnapshot);
            case STREET_ASSET_TRADE -> map(transactionSnapshot.getStreetAssetTrade(), sequenceNumber, referenceDataSnapshot);
            case DEPOSIT -> map(transactionSnapshot.getDeposit(), sequenceNumber, referenceDataSnapshot);
            case WITHDRAWAL -> map(transactionSnapshot.getWithdrawal(), sequenceNumber, referenceDataSnapshot);
            case ACCOUNT_CASH_TRANSFER -> map(transactionSnapshot.getAccountCashTransfer(), sequenceNumber, referenceDataSnapshot);
            case PORTFOLIO_CASH_TRANSFER -> map(transactionSnapshot.getPortfolioCashTransfer(), sequenceNumber, referenceDataSnapshot);
            case PORTFOLIO_ASSET_TRANSFER -> map(transactionSnapshot.getPortfolioAssetTransfer(), sequenceNumber, referenceDataSnapshot);
            case FEE -> map(transactionSnapshot.getFee(), sequenceNumber, referenceDataSnapshot);
            case SETTLEMENT -> {
                LOGGER.warn("Ignoring settlement snapshot since from old booking-engine: {}", transactionSnapshot);
                yield null;
            }
            case TRANSACTION_NOT_SET -> {
                LOGGER.warn("Ignoring unset transaction snapshot: {}", transactionSnapshot);
                yield null;
            }
        };

        return Optional.ofNullable(transaction);
    }

    private static Transaction map(@NotNull ClientCashTradeSnapshot snapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        Transaction.TransactionBuilder builder = Transaction.builder()
            .uuid(snapshot.getUuid())
            .reservationRef(snapshot.getReservationRef())
            .sequenceNumber(sequenceNumber)
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(snapshot.getDateTime()))
            .quantity(bd(snapshot.getQuantity()))
            .leavesQuantity(bd(snapshot.getLeavesQuantity()))
            .price(bd(snapshot.getPrice()))
            .currency(snapshot.getCurrency())
            .baseCurrency(snapshot.getBaseCurrency())
            .portfolioId(snapshot.getPortfolio())
            .counterPortfolioId(snapshot.getCounterPortfolio())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .counterPortfolioType(referenceDataSnapshot.counterPortfolioType())
            .executionId(snapshot.getExecutionId())
            .venueExecutionId(snapshot.getVenueExecutionId())
            .intOrderId(snapshot.getIntOrderId())
            .extOrderId(snapshot.getExtOrderId())
            .orderId(snapshot.getOrderId())
            .parentOrderId(snapshot.getParentOrderId())
            .rootOrderId(snapshot.getRootOrderId())
            .underlyingExecutionId(snapshot.getUnderlyingExecutionId())
            .rootExecutionId(snapshot.getRootExecutionId())
            .clientRootOrderId(snapshot.getClientRootOrderId())
            .addFees(mapFees(snapshot.getTransactionFeeList()))
            .description(snapshot.getDescription())
            .isLive(snapshot.getIsLive())
            .settlementId(snapshot.getSettlementId())
            .settlementType(SettlementType.DEFERRED_SETTLEMENT)
            .transactionType(CLIENT_CASH_TRADE)
            .execType(map(snapshot.getExecType()));

        if (snapshot.getSettled()) {
            builder.isSettled(true);
            builder.settlementDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(snapshot.getSettledDateTime()), ZonedDateTime.now()));
        }

        return builder.build();
    }

    private static Transaction map(@NotNull StreetCashTradeSnapshot snapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        Transaction.TransactionBuilder builder = Transaction.builder()
            .uuid(snapshot.getUuid())
            .reservationRef(snapshot.getReservationRef())
            .sequenceNumber(sequenceNumber)
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(snapshot.getDateTime()))
            .quantity(bd(snapshot.getQuantity()))
            .leavesQuantity(bd(snapshot.getLeavesQuantity()))
            .price(bd(snapshot.getPrice()))
            .currency(snapshot.getCurrency())
            .baseCurrency(snapshot.getBaseCurrency())
            .portfolioId(snapshot.getPortfolio())
            .accountId(snapshot.getVenueAccount())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .accountType(referenceDataSnapshot.accountType())
            .accountWalletType(referenceDataSnapshot.walletType())
            .executionId(snapshot.getExecutionId())
            .venueExecutionId(snapshot.getVenueExecutionId())
            .intOrderId(snapshot.getIntOrderId())
            .extOrderId(snapshot.getExtOrderId())
            .orderId(snapshot.getOrderId())
            .parentOrderId(snapshot.getParentOrderId())
            .rootOrderId(snapshot.getRootOrderId())
            .underlyingExecutionId(snapshot.getUnderlyingExecutionId())
            .rootExecutionId(snapshot.getRootExecutionId())
            .clientRootOrderId(snapshot.getClientRootOrderId())
            .addFees(mapFees(snapshot.getTransactionFeeList()))
            .description(snapshot.getDescription())
            .isLive(snapshot.getIsLive())
            .settlementId(snapshot.getSettlementId())
            .settlementType(SettlementType.DEFERRED_SETTLEMENT)
            .transactionType(STREET_CASH_TRADE)
            .execType(map(snapshot.getExecType()));

        if (snapshot.getSettled()) {
            builder.isSettled(true);
            builder.settlementDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(snapshot.getSettledDateTime()), ZonedDateTime.now()));
        }

        return builder.build();
    }

    private static Transaction map(@NotNull ClientAssetTradeSnapshot snapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        Transaction.TransactionBuilder builder = Transaction.builder()
            .uuid(snapshot.getUuid())
            .reservationRef(snapshot.getReservationRef())
            .sequenceNumber(sequenceNumber)
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(snapshot.getDateTime()))
            .quantity(bd(snapshot.getQuantity()))
            .leavesQuantity(bd(snapshot.getLeavesQuantity()))
            .price(bd(snapshot.getPrice()))
            .currency(snapshot.getCurrency())
            .asset(snapshot.getInstrument())
            .portfolioId(snapshot.getPortfolio())
            .counterPortfolioId(snapshot.getCounterPortfolio())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .counterPortfolioType(referenceDataSnapshot.counterPortfolioType())
            .executionId(snapshot.getExecutionId())
            .venueExecutionId(snapshot.getVenueExecutionId())
            .intOrderId(snapshot.getIntOrderId())
            .extOrderId(snapshot.getExtOrderId())
            .orderId(snapshot.getOrderId())
            .addFees(mapFees(snapshot.getTransactionFeeList()))
            .description(snapshot.getDescription())
            .isLive(snapshot.getIsLive())
            .settlementType(SettlementType.DEFERRED_SETTLEMENT)
            .transactionType(CLIENT_ASSET_TRADE)
            .execType(map(snapshot.getExecType()));

        if (snapshot.getSettled()) {
            builder.isSettled(true);
            builder.settlementDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(snapshot.getSettledDateTime()), ZonedDateTime.now()));
        }

        return builder.build();
    }

    private static Transaction map(@NotNull StreetAssetTradeSnapshot snapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        Transaction.TransactionBuilder builder = Transaction.builder()
            .uuid(snapshot.getUuid())
            .reservationRef(snapshot.getReservationRef())
            .sequenceNumber(sequenceNumber)
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(snapshot.getDateTime()))
            .quantity(bd(snapshot.getQuantity()))
            .leavesQuantity(bd(snapshot.getLeavesQuantity()))
            .price(bd(snapshot.getPrice()))
            .currency(snapshot.getCurrency())
            .asset(snapshot.getInstrument())
            .portfolioId(snapshot.getPortfolio())
            .accountId(snapshot.getVenueAccount())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .accountType(referenceDataSnapshot.accountType())
            .accountWalletType(referenceDataSnapshot.walletType())
            .executionId(snapshot.getExecutionId())
            .venueExecutionId(snapshot.getVenueExecutionId())
            .intOrderId(snapshot.getIntOrderId())
            .extOrderId(snapshot.getExtOrderId())
            .orderId(snapshot.getOrderId())
            .addFees(mapFees(snapshot.getTransactionFeeList()))
            .description(snapshot.getDescription())
            .isLive(snapshot.getIsLive())
            .settlementType(SettlementType.DEFERRED_SETTLEMENT)
            .transactionType(STREET_ASSET_TRADE)
            .execType(map(snapshot.getExecType()));

        if (snapshot.getSettled()) {
            builder.isSettled(true);
            builder.settlementDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(snapshot.getSettledDateTime()), ZonedDateTime.now()));
        }

        return builder.build();
    }

    private static Transaction map(@NotNull DepositSnapshot snapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        Transaction.TransactionBuilder builder = Transaction.builder()
            .uuid(snapshot.getUuid())
            .reservationRef(snapshot.getReservationRef())
            .sequenceNumber(sequenceNumber)
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(snapshot.getDateTime()))
            .quantity(bd(snapshot.getQuantity()))
            .currency(snapshot.getCurrency())
            .portfolioId(snapshot.getPortfolio())
            .accountId(snapshot.getAccount())
            .feePortfolioId(snapshot.getFeePortfolioId())
            .feeAccountId(snapshot.getFeeAccountId())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .accountType(referenceDataSnapshot.accountType())
            .accountWalletType(referenceDataSnapshot.walletType())
            .feePortfolioType(referenceDataSnapshot.feePortfolioType())
            .feeAccountType(referenceDataSnapshot.feeAccountType())
            .feeAccountWalletType(referenceDataSnapshot.feeWalletType())
            .executionId(snapshot.getExecutionId())
            .venueExecutionId(snapshot.getVenueExecutionId())
            .addFees(mapFees(snapshot.getTransactionFeeList()))
            .description(snapshot.getDescription())
            .isLive(snapshot.getIsLive())
            .settlementType(SettlementType.INSTANT_SETTLEMENT)
            .transactionType(DEPOSIT);

        if (snapshot.getSettled()) {
            builder.isSettled(true);
            builder.settlementDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(snapshot.getSettledDateTime()), ZonedDateTime.now()));
        }

        return builder.build();
    }

    private static Transaction map(@NotNull WithdrawalSnapshot snapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        Transaction.TransactionBuilder builder = Transaction.builder()
            .uuid(snapshot.getUuid())
            .reservationRef(snapshot.getReservationRef())
            .sequenceNumber(sequenceNumber)
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(snapshot.getDateTime()))
            .quantity(bd(snapshot.getQuantity()))
            .currency(snapshot.getCurrency())
            .portfolioId(snapshot.getPortfolio())
            .accountId(snapshot.getAccount())
            .feePortfolioId(snapshot.getFeePortfolioId())
            .feeAccountId(snapshot.getFeeAccountId())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .accountType(referenceDataSnapshot.accountType())
            .accountWalletType(referenceDataSnapshot.walletType())
            .feePortfolioType(referenceDataSnapshot.feePortfolioType())
            .feeAccountType(referenceDataSnapshot.feeAccountType())
            .feeAccountWalletType(referenceDataSnapshot.feeWalletType())
            .executionId(snapshot.getExecutionId())
            .venueExecutionId(snapshot.getVenueExecutionId())
            .addFees(mapFees(snapshot.getTransactionFeeList()))
            .description(snapshot.getDescription())
            .isLive(snapshot.getIsLive())
            .settlementType(SettlementType.INSTANT_SETTLEMENT)
            .transactionType(WITHDRAWAL);

        if (snapshot.getSettled()) {
            builder.isSettled(true);
            builder.settlementDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(snapshot.getSettledDateTime()), ZonedDateTime.now()));
        }

        return builder.build();
    }

    private static Transaction map(@NotNull AccountCashTransferSnapshot snapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        Transaction.TransactionBuilder builder = Transaction.builder()
            .uuid(snapshot.getUuid())
            .reservationRef(snapshot.getReservationRef())
            .sequenceNumber(sequenceNumber)
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(snapshot.getDateTime()))
            .quantity(bd(snapshot.getQuantity()))
            .currency(snapshot.getCurrency())
            .sourceAccountId(snapshot.getFromAccountId())
            .targetAccountId(snapshot.getToAccountId())
            .feePortfolioId(snapshot.getFeePortfolioId())
            .feeAccountId(snapshot.getFeeAccountId())
            .sourceAccountType(referenceDataSnapshot.sourceAccountType())
            .sourceAccountWalletType(referenceDataSnapshot.sourceWalletType())
            .targetAccountType(referenceDataSnapshot.targetAccountType())
            .targetAccountWalletType(referenceDataSnapshot.targetWalletType())
            .feePortfolioType(referenceDataSnapshot.feePortfolioType())
            .feeAccountType(referenceDataSnapshot.feeAccountType())
            .feeAccountWalletType(referenceDataSnapshot.feeWalletType())
            .executionId(snapshot.getExecutionId())
            .venueExecutionId(snapshot.getVenueExecutionId())
            .addFees(mapFees(snapshot.getTransactionFeeList()))
            .description(snapshot.getDescription())
            .isLive(snapshot.getIsLive())
            .settlementType(SettlementType.INSTANT_SETTLEMENT)
            .transactionType(ACCOUNT_CASH_TRANSFER);

        if (snapshot.getSettled()) {
            builder.isSettled(true);
            builder.settlementDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(snapshot.getSettledDateTime()), ZonedDateTime.now()));
        }

        return builder.build();
    }

    private static Transaction map(@NotNull PortfolioCashTransferSnapshot snapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        Transaction.TransactionBuilder builder = Transaction.builder()
            .uuid(snapshot.getUuid())
            .reservationRef(snapshot.getReservationRef())
            .sequenceNumber(sequenceNumber)
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(snapshot.getDateTime()))
            .quantity(bd(snapshot.getQuantity()))
            .currency(snapshot.getCurrency())
            .sourcePortfolioId(snapshot.getFromPortfolioId())
            .targetPortfolioId(snapshot.getToPortfolioId())
            .feePortfolioId(snapshot.getFeePortfolioId())
            .sourcePortfolioType(referenceDataSnapshot.portfolioType())
            .targetPortfolioType(referenceDataSnapshot.portfolioType())
            .feePortfolioType(referenceDataSnapshot.feePortfolioType())
            .executionId(snapshot.getExecutionId())
            .venueExecutionId(snapshot.getVenueExecutionId())
            .addFees(mapFees(snapshot.getTransactionFeeList()))
            .description(snapshot.getDescription())
            .isLive(snapshot.getIsLive())
            .settlementType(SettlementType.INSTANT_SETTLEMENT)
            .transactionType(PORTFOLIO_CASH_TRANSFER);

        if (snapshot.getSettled()) {
            builder.isSettled(true);
            builder.settlementDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(snapshot.getSettledDateTime()), ZonedDateTime.now()));
        }

        return builder.build();
    }

    private static Transaction map(@NotNull PortfolioAssetTransferSnapshot snapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        Transaction.TransactionBuilder builder = Transaction.builder()
            .uuid(snapshot.getUuid())
            .reservationRef(snapshot.getReservationRef())
            .sequenceNumber(sequenceNumber)
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(snapshot.getDateTime()))
            .quantity(bd(snapshot.getQuantity()))
            .asset(snapshot.getInstrument())
            .sourcePortfolioId(snapshot.getFromPortfolioId())
            .targetPortfolioId(snapshot.getToPortfolioId())
            .feePortfolioId(snapshot.getFeePortfolioId())
            .sourcePortfolioType(referenceDataSnapshot.portfolioType())
            .targetPortfolioType(referenceDataSnapshot.portfolioType())
            .feePortfolioType(referenceDataSnapshot.feePortfolioType())
            .executionId(snapshot.getExecutionId())
            .venueExecutionId(snapshot.getVenueExecutionId())
            .addFees(mapFees(snapshot.getTransactionFeeList()))
            .description(snapshot.getDescription())
            .isLive(snapshot.getIsLive())
            .settlementType(SettlementType.INSTANT_SETTLEMENT)
            .transactionType(PORTFOLIO_ASSET_TRANSFER);

        if (snapshot.getSettled()) {
            builder.isSettled(true);
            builder.settlementDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(snapshot.getSettledDateTime()), ZonedDateTime.now()));
        }

        return builder.build();
    }

    private static Transaction map(@NotNull FeeSnapshot snapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        Transaction.TransactionBuilder builder = Transaction.builder()
            .uuid(snapshot.getUuid())
            .reservationRef(snapshot.getReservationRef())
            .sequenceNumber(sequenceNumber)
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(snapshot.getDateTime()))
            .quantity(bd(snapshot.getQuantity()))
            .currency(snapshot.getCurrency())
            .portfolioId(snapshot.getPortfolioId())
            .accountId(snapshot.getAccountId())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .accountType(referenceDataSnapshot.accountType())
            .accountWalletType(referenceDataSnapshot.walletType())
            .executionId(snapshot.getExecutionId())
            .venueExecutionId(snapshot.getVenueExecutionId())
            .addFees(mapFees(snapshot.getTransactionFeeList()))
            .description(snapshot.getDescription())
            .isLive(snapshot.getIsLive())
            .settlementType(SettlementType.DEFERRED_SETTLEMENT)
            .transactionType(FEE);

        if (snapshot.getSettled()) {
            builder.isSettled(true);
            builder.settlementDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(snapshot.getSettledDateTime()), ZonedDateTime.now()));
        }

        return builder.build();
    }

    public static RequestModel.TransactionSearch map(TransactionSearch transactionSearch) {
        ZonedDateTime dateFrom = epochMillisToZonedDateTime(transactionSearch.getFrom());
        ZonedDateTime dateTo = epochMillisToZonedDateTime(transactionSearch.getTo());
        ZonedDateTime settlementFrom = epochMillisToZonedDateTime(transactionSearch.getSettlementFrom());
        ZonedDateTime settlementTo = epochMillisToZonedDateTime(transactionSearch.getSettlementTo());

        return new RequestModel.TransactionSearch(
            transactionSearch.getSymbolList(),
            transactionSearch.getAccountIdList(),
            transactionSearch.getPortfolioIdList(),
            null,
            null,
            null,
            transactionSearch.getCurrencyList(),
            mapTransactionTypes(transactionSearch.getTransactionTypeList()),
            transactionSearch.getUuidList(),
            transactionSearch.getOrderId(),
            transactionSearch.getParentOrderId(),
            transactionSearch.getRootOrderId(),
            transactionSearch.getExecutionId(),
            transactionSearch.getVenueExecutionId(),
            transactionSearch.getUnderlyingExecutionId(),
            transactionSearch.getRootExecutionId(),
            transactionSearch.getReservationRef(),
            transactionSearch.getClientId(),
            dateFrom,
            dateTo,
            settlementFrom,
            settlementTo,
            map(transactionSearch.getSettled()),
            transactionSearch.getFirst(),
            transactionSearch.getAfter(),
            map(transactionSearch.getSortingOrder())
        );
    }

    private static ExecType map(OemsExecType execType) {
        return switch (execType) {
            case EXEC_TYPE_UNSPECIFIED, UNRECOGNIZED -> null;
            case NEW -> ExecType.NEW;
            case PARTIAL_FILL -> ExecType.PARTIAL_FILL;
            case FILL -> ExecType.FILL;
            case CANCELED -> ExecType.CANCELED;
            case PENDING_CANCEL -> ExecType.PENDING_CANCEL;
            case REJECTED -> ExecType.REJECTED;
            case PENDING_NEW -> ExecType.PENDING_NEW;
            case EXPIRED -> ExecType.EXPIRED;
            case CALCULATED -> ExecType.CALCULATED;
            case RESTATED -> ExecType.RESTATED;
        };
    }

    public static Collection<TransactionFee> mapFees(Collection<Fee> fees) {
        if (fees == null) {
            return Collections.emptyList();
        }

        return fees.stream()
            .map(fee -> new TransactionFee(
                bd(fee.getAmount()),
                fee.getCurrency(),
                null,
                map(fee.getFeeType())))
            .toList();
    }

    public static TransactionFeeType map(FeeType feeType) {
        if (feeType == null) {
            return TransactionFeeType.FEE_TYPE_UNSPECIFIED;
        }

        return switch (feeType) {
            case EXCHANGE_FEE -> TransactionFeeType.EXCHANGE_FEE;
            case TRANSACTION_FEE -> TransactionFeeType.TRANSACTION_FEE;
            case FIXED_FEE -> TransactionFeeType.FIXED_FEE;
            default -> TransactionFeeType.FEE_TYPE_UNSPECIFIED;
        };
    }

    public static Collection<TransactionType> mapTransactionTypes(List<io.wyden.published.booking.TransactionType> transactionTypes) {
        if (CollectionUtils.isEmpty(transactionTypes)) {
            return List.of();
        }

        return transactionTypes.stream()
            .map(transactionType -> switch (transactionType) {
                case TRANSACTION_TYPE_UNSPECIFIED, UNRECOGNIZED -> null;
                case TRANSACTION_TYPE_CLIENT_CASH_TRADE -> CLIENT_CASH_TRADE;
                case TRANSACTION_TYPE_STREET_CASH_TRADE -> STREET_CASH_TRADE;
                case TRANSACTION_TYPE_CLIENT_ASSET_TRADE -> CLIENT_ASSET_TRADE;
                case TRANSACTION_TYPE_STREET_ASSET_TRADE -> STREET_ASSET_TRADE;
                case TRANSACTION_TYPE_PORTFOLIO_CASH_TRANSFER -> PORTFOLIO_CASH_TRANSFER;
                case TRANSACTION_TYPE_ACCOUNT_CASH_TRANSFER -> ACCOUNT_CASH_TRANSFER;
                case TRANSACTION_TYPE_DEPOSIT -> DEPOSIT;
                case TRANSACTION_TYPE_WITHDRAWAL -> WITHDRAWAL;
                case TRANSACTION_TYPE_SETTLEMENT -> SETTLEMENT;
                default -> null;
            })
            .filter(Objects::nonNull)
            .toList();
    }

    public static RequestModel.SortingOrder map(SortingOrder sortingOrder) {
        if (sortingOrder == null) {
            return null;
        }

        return switch (sortingOrder) {
            case SORTING_ORDER_ASC -> RequestModel.SortingOrder.ASC;
            case SORTING_ORDER_DESC -> RequestModel.SortingOrder.DESC;
            default -> null;
        };
    }

    private static Boolean map(TernaryBool bool) {
        if (bool == null) {
            return null;
        }

        return switch (bool) {
            case TERNARY_BOOL_UNSPECIFIED -> null;
            case TRUE -> true;
            case FALSE -> false;
            default -> null;
        };
    }
}
