package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.reporting.domain.transaction.TransactionFee;
import io.wyden.booking.reporting.domain.transaction.TransactionFeeType;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.LedgerEntrySnapshot;
import io.wyden.published.booking.LedgerEntryType;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.Metadata;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;
import java.util.UUID;

import static io.wyden.cloudutils.tools.ProtobufUtils.toProtoString;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public final class LedgerEntryToProtoMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(LedgerEntryToProtoMapper.class);

    private LedgerEntryToProtoMapper() {
    }

    public static LedgerEntrySnapshot map(LedgerEntry ledgerEntry) {
        String createdAt = DateUtils.toIsoUtcTime(ledgerEntry.getCreatedAt());
        String updatedAt = DateUtils.toIsoUtcTime(ledgerEntry.getUpdatedAt());

        Metadata meta = Metadata.newBuilder()
            .setCreatedAt(createdAt)
            .setUpdatedAt(updatedAt)
            .setRequesterId("booking-reporting")
            .setResponseId(UUID.randomUUID().toString())
            .build();

        LedgerEntrySnapshot.Builder builder = LedgerEntrySnapshot.newBuilder()
            .setMetadata(meta);

        if (ledgerEntry.getId() != null) {
            builder.setId(mapToId(ledgerEntry.getId()));
        }

        if (isNotBlank(ledgerEntry.getReservationRef())) {
            builder.setReservationRef(ledgerEntry.getReservationRef());
        }

        if (ledgerEntry.getQuantity() != null) {
            builder.setQuantity(toProtoString(ledgerEntry.getQuantity()));
        } else {
            // handle inconsistent ledger entry data
            builder.setQuantity("0");
        }

        // handle inconsistent ledger entry data
        builder.setFee("0");

        if (ledgerEntry.getPrice() != null) {
            builder.setPrice(toProtoString(ledgerEntry.getPrice()));
        } else {
            // handle inconsistent ledger entry data
            builder.setPrice("0");
        }

        if (CollectionUtils.isNotEmpty(ledgerEntry.getFees())) {
            ledgerEntry.getFees().stream()
                .map(LedgerEntryToProtoMapper::map)
                .forEach(builder::addFees);
        }

        if (ledgerEntry.getBalanceBefore() != null) {
            builder.setBalanceBefore(toProtoString(ledgerEntry.getBalanceBefore()));
        }

        if (ledgerEntry.getBalanceAfter() != null) {
            builder.setBalanceAfter(toProtoString(ledgerEntry.getBalanceAfter()));
        }

        if (ledgerEntry.getLedgerEntryType() != null) {
            builder.setType(map(ledgerEntry.getLedgerEntryType()));
        }

        if (isNotBlank(ledgerEntry.getTransactionId())) {
            builder.setTransactionId(ledgerEntry.getTransactionId());
        }

        if (ledgerEntry.getSymbol() != null) {
            builder.setSymbol(ledgerEntry.getSymbol())
                .setCurrency(ledgerEntry.getSymbol());
        }

        if (isNotBlank(ledgerEntry.getPortfolioId())) {
            builder.setPortfolio(ledgerEntry.getPortfolioId());
        }

        if (isNotBlank(ledgerEntry.getAccountId())) {
            builder.setAccount(ledgerEntry.getAccountId());
        }

        if (ledgerEntry.getSequenceNumber() != null) {
            builder.setSequenceNumber(ledgerEntry.getSequenceNumber());
        }

        return builder.build();
    }

    public static LedgerEntryType map(io.wyden.booking.reporting.domain.ledgerentry.LedgerEntryType ledgerEntryType) {
        if (ledgerEntryType == null) {
            return LedgerEntryType.UNSPECIFIED;
        }

        return switch (ledgerEntryType) {
            case ASSET_TRADE_BUY -> LedgerEntryType.ASSET_TRADE_BUY;
            case ASSET_TRADE_SELL -> LedgerEntryType.ASSET_TRADE_SELL;
            case CASH_TRADE_CREDIT -> LedgerEntryType.CASH_TRADE_CREDIT;
            case CASH_TRADE_DEBIT -> LedgerEntryType.CASH_TRADE_DEBIT;
            case ASSET_TRADE_PROCEEDS -> LedgerEntryType.ASSET_TRADE_PROCEEDS;
            case DEPOSIT -> LedgerEntryType.DEPOSIT;
            case WITHDRAWAL -> LedgerEntryType.WITHDRAWAL;
            case TRANSFER -> LedgerEntryType.TRANSFER;
            case FEE -> LedgerEntryType.FEE;
            case TRADING_FEE -> LedgerEntryType.TRADING_FEE;
            case DEPOSIT_FEE -> LedgerEntryType.DEPOSIT_FEE;
            case WITHDRAWAL_FEE -> LedgerEntryType.WITHDRAWAL_FEE;
            case TRANSFER_FEE -> LedgerEntryType.TRANSFER_FEE;
            case RESERVATION -> LedgerEntryType.RESERVATION;
            case WITHDRAWAL_RESERVATION -> LedgerEntryType.WITHDRAWAL_RESERVATION;
            case RESERVATION_RELEASE -> LedgerEntryType.RESERVATION_RELEASE;
            case RESERVATION_RELEASE_REMAINING -> LedgerEntryType.RESERVATION_RELEASE_REMAINING;
            case UNSPECIFIED -> LedgerEntryType.UNSPECIFIED;
        };
    }

    public static Fee map(TransactionFee fee) {
        if (fee == null) {
            return null;
        }

        return Fee.newBuilder()
            .setAmount(toProtoString(fee.getAmount()))
            .setCurrency(fee.getCurrency())
            .setFeeType(map(fee.getFeeType()))
            .build();
    }

    private static FeeType map(TransactionFeeType feeType) {
        if (feeType == null) {
            return FeeType.FEE_TYPE_UNSPECIFIED;
        }

        return switch (feeType) {
            case EXCHANGE_FEE -> FeeType.EXCHANGE_FEE;
            case TRANSACTION_FEE -> FeeType.TRANSACTION_FEE;
            case FIXED_FEE -> FeeType.FIXED_FEE;
            default -> FeeType.FEE_TYPE_UNSPECIFIED;
        };
    }

    public static String mapToId(Long id) {
        if (id == null) {
            return null;
        }

        return String.valueOf(id);
    }

    public static Optional<CursorNode> mapToCursorNode(LedgerEntry ledgerEntry) {
        if (ledgerEntry == null) {
            return Optional.empty();
        }

        LedgerEntrySnapshot ledgerEntrySnapshot = map(ledgerEntry);

        CursorNode cursorNode = CursorNode.newBuilder()
            .setLedgerEntry(ledgerEntrySnapshot)
            .build();

        return Optional.of(cursorNode);
    }
}
