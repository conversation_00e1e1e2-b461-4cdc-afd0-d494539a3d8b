package io.wyden.booking.reporting.interfaces.rest;

import io.wyden.booking.reporting.application.TransactionService;
import io.wyden.booking.reporting.domain.transaction.Transaction;
import io.wyden.booking.reporting.domain.transaction.TransactionType;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.TransactionFromProtoMapper;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.TransactionToProtoMapper;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.rest.pagination.PaginationToProtoMapper;
import io.wyden.published.booking.TransactionSearch;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.common.CursorConnection;
import org.slf4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
public class TransactionQueryController {

    private static final Logger LOGGER = getLogger(TransactionQueryController.class);

    private final TransactionService transactionService;

    public TransactionQueryController(TransactionService transactionService) {
        this.transactionService = transactionService;
    }

    @PostMapping(value = "/transactions/search", params = "version=proto")
    public CursorConnection searchProto(@RequestBody TransactionSearch request) {
        LOGGER.info("Lookup transactions requested for: \n{}", request);

        RequestModel.TransactionSearch transactionSearch = TransactionFromProtoMapper.map(request);
        PaginationModel.CursorConnection<Transaction> connection = transactionService.search(transactionSearch);

        LOGGER.info("Lookup finished with {} transactions: {}", connection.getAllNodes().size(), connection);
        return PaginationToProtoMapper.map(connection, TransactionToProtoMapper::mapToCursorNode);
    }

    @GetMapping("/transactions/{transactionUuid}")
    public TransactionSnapshot search(@PathVariable String transactionUuid) {
        LOGGER.info("Search transaction by id for: {}", transactionUuid);

        // Transactions can be queried using UUID, we do not want to expose database ids to REST users
        return transactionService.findByUuid(transactionUuid)
            .map(TransactionToProtoMapper::map)
            .orElseThrow(() -> new ResponseStatusException(
                HttpStatus.NOT_FOUND,
                "Transaction with id " + transactionUuid + " not found"));
    }

    @GetMapping("/transaction-types")
    public List<String> transactionTypes() {
        return TransactionType.stringValues();
    }
}
