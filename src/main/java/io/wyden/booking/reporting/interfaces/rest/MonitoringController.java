package io.wyden.booking.reporting.interfaces.rest;

import io.wyden.booking.reporting.domain.rate.SystemCurrencyProvider;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
public class MonitoringController {

    private static final Logger LOGGER = getLogger(MonitoringController.class);

    private final SystemCurrencyProvider systemCurrencyProvider;

    public MonitoringController(SystemCurrencyProvider systemCurrencyProvider) {
        this.systemCurrencyProvider = systemCurrencyProvider;
    }

    @GetMapping("/system-currency")
    public String getSystemCurrency() {
        return SystemCurrencyProvider.getSystemCurrency();
    }

    @PutMapping("/system-currency")
    public void updateSystemCurrency(@RequestBody String systemCurrency) {
        LOGGER.warn("Updating system currency to: {}. All position metrics might be inaccurate!", systemCurrency);
        systemCurrencyProvider.setSystemCurrency(systemCurrency);
    }
}
