package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.domain.balance.Balance;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.BalanceSnapshot;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.Metadata;

import java.util.Optional;
import java.util.UUID;

import static io.wyden.cloudutils.tools.ProtobufUtils.toProtoString;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public final class BalanceToProtoMapper {

    private BalanceToProtoMapper() {
    }

    public static BalanceSnapshot map(Balance entity) {
        String createdAt = DateUtils.toIsoUtcTime(entity.getCreatedAt());
        String updatedAt = DateUtils.toIsoUtcTime(entity.getUpdatedAt());

        Metadata meta = Metadata.newBuilder()
            .setCreatedAt(createdAt)
            .setUpdatedAt(updatedAt)
            .setRequesterId("booking-reporting")
            .setResponseId(UUID.randomUUID().toString())
            .build();

        BalanceSnapshot.Builder builder = BalanceSnapshot.newBuilder()
            .setMetadata(meta);

        if (isNotBlank(entity.getSymbol())) {
            builder.setSymbol(entity.getSymbol());
        }

        if (isNotBlank(entity.getAccountId())) {
            builder.setAccountId(entity.getAccountId());
        }

        if (isNotBlank(entity.getPortfolioId())) {
            builder.setPortfolioId(entity.getPortfolioId());
        }

        if (isNotBlank(entity.getCurrency())) {
            builder.setCurrency(entity.getCurrency());
        }

        if (entity.getQuantity() != null) {
            builder.setQuantity(toProtoString(entity.getQuantity()));
        }

        if (entity.getPendingQuantity() != null) {
            builder.setPendingQuantity(toProtoString(entity.getPendingQuantity()));
        }

        if (entity.getAvailableForTradingQuantity() != null) {
            builder.setAvailableForTradingQuantity(toProtoString(entity.getAvailableForTradingQuantity()));
        }

        if (entity.getAvailableForWithdrawalQuantity() != null) {
            builder.setAvailableForWithdrawalQuantity(toProtoString(entity.getAvailableForWithdrawalQuantity()));
        }

        if (entity.getSettledQuantity() != null) {
            builder.setSettledQuantity(toProtoString(entity.getSettledQuantity()));
        }

        if (entity.getUnsettledQuantity() != null) {
            builder.setUnsettledQuantity(toProtoString(entity.getUnsettledQuantity()));
        }

        if (entity.getLastAppliedLedgerEntryId() != null) {
            builder.setLastAppliedLedgerEntryId(entity.getLastAppliedLedgerEntryId());
        }

        return builder.build();
    }

    public static Optional<CursorNode> mapToCursorNode(Balance balance) {
        if (balance == null) {
            return Optional.empty();
        }

        BalanceSnapshot balanceSnapshot = map(balance);

        CursorNode cursorNode = CursorNode.newBuilder()
            .setBalance(balanceSnapshot)
            .build();

        return Optional.of(cursorNode);
    }
}
