package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.PositionSnapshot;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.Metadata;

import java.util.Optional;
import java.util.UUID;

import static io.wyden.cloudutils.tools.ProtobufUtils.toProtoString;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public final class PositionToProtoMapper {

    private PositionToProtoMapper() {
    }

    public static PositionSnapshot map(Position position) {
        String createdAt = DateUtils.toIsoUtcTime(position.getCreatedAt());
        String updatedAt = DateUtils.toIsoUtcTime(position.getUpdatedAt());

        Metadata meta = Metadata.newBuilder()
            .setCreatedAt(createdAt)
            .setUpdatedAt(updatedAt)
            .setRequesterId("booking-reporting")
            .setResponseId(UUID.randomUUID().toString())
            .build();

        PositionSnapshot.Builder builder = PositionSnapshot.newBuilder()
            .setMetadata(meta);

        if (isNotBlank(position.getSymbol())) {
            builder.setSymbol(position.getSymbol());
        }

        if (isNotBlank(position.getCurrency())) {
            builder.setCurrency(position.getCurrency());
        }

        // Set booking currency - use portfolio currency if available, otherwise account currency
        String bookingCurrency = position.getReferenceCurrency();
        if (isNotBlank(bookingCurrency)) {
            builder.setBookingCurrency(bookingCurrency);
        }

        if (position.getQuantity() != null) {
            builder.setQuantity(toProtoString(position.getQuantity()));
        }

        if (position.getNotionalQuantity() != null) {
            builder.setNotionalQuantity(toProtoString(position.getNotionalQuantity()));
        }

        if (position.getNetCost() != null) {
            builder.setNetCost(toProtoString(position.getNetCost()));
        }

        if (position.getNetCostSc() != null) {
            builder.setNetCostSc(toProtoString(position.getNetCostSc()));
        }

        if (position.getGrossCost() != null) {
            builder.setGrossCost(toProtoString(position.getGrossCost()));
        }

        if (position.getGrossCostSc() != null) {
            builder.setGrossCostSc(toProtoString(position.getGrossCostSc()));
        }

        if (position.getNetRealizedPnl() != null) {
            builder.setNetRealizedPnl(toProtoString(position.getNetRealizedPnl()));
        }

        if (position.getNetRealizedPnlSc() != null) {
            builder.setNetRealizedPnlSc(toProtoString(position.getNetRealizedPnlSc()));
        }

        if (position.getGrossRealizedPnl() != null) {
            builder.setGrossRealizedPnl(toProtoString(position.getGrossRealizedPnl()));
        }

        if (position.getGrossRealizedPnlSc() != null) {
            builder.setGrossRealizedPnlSc(toProtoString(position.getGrossRealizedPnlSc()));
        }

        if (position.getNetAveragePrice() != null) {
            builder.setNetAveragePrice(toProtoString(position.getNetAveragePrice()));
        }

        if (position.getGrossAveragePrice() != null) {
            builder.setGrossAveragePrice(toProtoString(position.getGrossAveragePrice()));
        }

        if (position.getMarketValue() != null) {
            builder.setMarketValue(toProtoString(position.getMarketValue()));
        }

        if (position.getMarketValueSc() != null) {
            builder.setMarketValueSc(toProtoString(position.getMarketValueSc()));
        }

        if (position.getNetUnrealizedPnl() != null) {
            builder.setNetUnrealizedPnl(toProtoString(position.getNetUnrealizedPnl()));
        }

        if (position.getNetUnrealizedPnlSc() != null) {
            builder.setNetUnrealizedPnlSc(toProtoString(position.getNetUnrealizedPnlSc()));
        }

        if (position.getGrossUnrealizedPnl() != null) {
            builder.setGrossUnrealizedPnl(toProtoString(position.getGrossUnrealizedPnl()));
        }

        if (position.getGrossUnrealizedPnlSc() != null) {
            builder.setGrossUnrealizedPnlSc(toProtoString(position.getGrossUnrealizedPnlSc()));
        }

        if (isNotBlank(position.getPortfolioId())) {
            builder.setPortfolio(position.getPortfolioId());
        }

        if (isNotBlank(position.getAccountId())) {
            builder.setAccount(position.getAccountId());
        }

        if (position.getLastAppliedLedgerEntryId() != null) {
            builder.setLastAppliedLedgerEntryId(position.getLastAppliedLedgerEntryId().toString());
        }

        return builder.build();
    }

    public static Optional<CursorNode> mapToCursorNode(Position position) {
        if (position == null) {
            return Optional.empty();
        }

        PositionSnapshot positionSnapshot = map(position);

        CursorNode cursorNode = CursorNode.newBuilder()
            .setPosition(positionSnapshot)
            .build();

        return Optional.of(cursorNode);
    }
}
