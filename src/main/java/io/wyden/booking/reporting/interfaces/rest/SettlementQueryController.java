package io.wyden.booking.reporting.interfaces.rest;

import io.wyden.published.booking.settlement.SettlementSearch;
import io.wyden.published.common.CursorConnection;
import org.apache.commons.lang3.NotImplementedException;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
public class SettlementQueryController {

    private static final Logger LOGGER = getLogger(SettlementQueryController.class);

    // TODO-MD Can we route REST-API to settlement-engine directly and maintain backward compatibility?

    @PostMapping("/settlements/search")
    public CursorConnection search(@RequestBody SettlementSearch request) {
        LOGGER.info("Retrieving settlement for request: {}", request);
        throw new NotImplementedException();
    }
}
