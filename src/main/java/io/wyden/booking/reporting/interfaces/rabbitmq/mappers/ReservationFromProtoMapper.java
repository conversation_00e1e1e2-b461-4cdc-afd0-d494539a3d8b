package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.application.referencedata.ReferenceDataSnapshot;
import io.wyden.booking.reporting.domain.reservation.Reservation;
import io.wyden.booking.reporting.domain.transaction.TransactionType;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.AccountCashTransferReservationSnapshot;
import io.wyden.published.booking.ClientAssetTradeReservationSnapshot;
import io.wyden.published.booking.ClientCashTradeReservationSnapshot;
import io.wyden.published.booking.DepositReservationSnapshot;
import io.wyden.published.booking.FeeReservationSnapshot;
import io.wyden.published.booking.PortfolioAssetTransferReservationSnapshot;
import io.wyden.published.booking.PortfolioCashTransferReservationSnapshot;
import io.wyden.published.booking.ReservationSearch;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.booking.StreetAssetTradeReservationSnapshot;
import io.wyden.published.booking.StreetCashTradeReservationSnapshot;
import io.wyden.published.booking.WithdrawalReservationSnapshot;
import org.slf4j.Logger;

import java.time.ZonedDateTime;
import java.util.Optional;
import java.util.UUID;

import static io.wyden.booking.reporting.interfaces.rabbitmq.mappers.TransactionFromProtoMapper.mapTransactionTypes;
import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static io.wyden.cloudutils.tools.DateUtils.epochMillisToZonedDateTime;
import static org.slf4j.LoggerFactory.getLogger;

public final class ReservationFromProtoMapper {

    private static final Logger LOGGER = getLogger(ReservationFromProtoMapper.class);

    private ReservationFromProtoMapper() {
    }

    public static Optional<Reservation> map(ReservationSnapshot reservationSnapshot, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        String uuid = reservationSnapshot.getUuid().isBlank() ? UUID.randomUUID().toString() : reservationSnapshot.getUuid();

        Reservation reservation = switch (reservationSnapshot.getReservationCase()) {
            case CLIENT_CASH_TRADE_RESERVATION -> map(reservationSnapshot.getClientCashTradeReservation(), uuid, sequenceNumber, referenceDataSnapshot);
            case STREET_CASH_TRADE_RESERVATION -> map(reservationSnapshot.getStreetCashTradeReservation(), uuid, sequenceNumber, referenceDataSnapshot);
            case CLIENT_ASSET_TRADE_RESERVATION -> map(reservationSnapshot.getClientAssetTradeReservation(), uuid, sequenceNumber, referenceDataSnapshot);
            case STREET_ASSET_TRADE_RESERVATION -> map(reservationSnapshot.getStreetAssetTradeReservation(), uuid, sequenceNumber, referenceDataSnapshot);
            case DEPOSIT_RESERVATION -> map(reservationSnapshot.getDepositReservation(), uuid, sequenceNumber, referenceDataSnapshot);
            case WITHDRAWAL_RESERVATION -> map(reservationSnapshot.getWithdrawalReservation(), uuid, sequenceNumber, referenceDataSnapshot);
            case PORTFOLIO_CASH_TRANSFER_RESERVATION -> map(reservationSnapshot.getPortfolioCashTransferReservation(), uuid, sequenceNumber, referenceDataSnapshot);
            case PORTFOLIO_ASSET_TRANSFER_RESERVATION -> map(reservationSnapshot.getPortfolioAssetTransferReservation(), uuid, sequenceNumber, referenceDataSnapshot);
            case ACCOUNT_CASH_TRANSFER_RESERVATION -> map(reservationSnapshot.getAccountCashTransferReservation(), uuid, sequenceNumber, referenceDataSnapshot);
            case FEE_RESERVATION -> map(reservationSnapshot.getFeeReservation(), uuid, sequenceNumber, referenceDataSnapshot);
            case RESERVATION_NOT_SET -> {
                LOGGER.warn("Ignoring unset reservation snapshot: {}", reservationSnapshot);
                yield null;
            }
        };

        return Optional.ofNullable(reservation);
    }

    public static Reservation map(ClientCashTradeReservationSnapshot request, String uuid, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        return Reservation.builder()
            .uuid(uuid)
            .sequenceNumber(sequenceNumber)
            .reservationRef(request.getReservationRef())
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()))
            .quantity(bd(request.getQuantity()))
            .price(bd(request.getPrice()))
            .stopPrice(bd(request.getStopPrice()))
            .currency(request.getCurrency())
            .baseCurrency(request.getBaseCurrency())
            .portfolioId(request.getPortfolioId())
            .counterPortfolioId(request.getCounterPortfolioId())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .counterPortfolioType(referenceDataSnapshot.counterPortfolioType())
            .addFees(TransactionFromProtoMapper.mapFees(request.getReservationFeeList()))
            .transactionType(TransactionType.CLIENT_CASH_TRADE)
            .build();
    }

    public static Reservation map(StreetCashTradeReservationSnapshot request, String uuid, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        return Reservation.builder()
            .uuid(uuid)
            .sequenceNumber(sequenceNumber)
            .reservationRef(request.getReservationRef())
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()))
            .quantity(bd(request.getQuantity()))
            .price(bd(request.getPrice()))
            .stopPrice(bd(request.getStopPrice()))
            .currency(request.getCurrency())
            .baseCurrency(request.getBaseCurrency())
            .portfolioId(request.getPortfolioId())
            .accountId(request.getAccountId())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .accountType(referenceDataSnapshot.accountType())
            .accountWalletType(referenceDataSnapshot.walletType())
            .addFees(TransactionFromProtoMapper.mapFees(request.getReservationFeeList()))
            .transactionType(TransactionType.STREET_CASH_TRADE)
            .build();
    }

    public static Reservation map(ClientAssetTradeReservationSnapshot request, String uuid, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        return Reservation.builder()
            .uuid(uuid)
            .sequenceNumber(sequenceNumber)
            .reservationRef(request.getReservationRef())
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()))
            .quantity(bd(request.getQuantity()))
            .price(bd(request.getPrice()))
            .stopPrice(bd(request.getStopPrice()))
            .currency(request.getCurrency())
            .asset(request.getInstrument())
            .portfolioId(request.getPortfolioId())
            .counterPortfolioId(request.getCounterPortfolioId())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .counterPortfolioType(referenceDataSnapshot.counterPortfolioType())
            .addFees(TransactionFromProtoMapper.mapFees(request.getReservationFeeList()))
            .transactionType(TransactionType.CLIENT_ASSET_TRADE)
            .build();
    }

    public static Reservation map(StreetAssetTradeReservationSnapshot request, String uuid, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        return Reservation.builder()
            .uuid(uuid)
            .sequenceNumber(sequenceNumber)
            .reservationRef(request.getReservationRef())
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()))
            .quantity(bd(request.getQuantity()))
            .price(bd(request.getPrice()))
            .stopPrice(bd(request.getStopPrice()))
            .currency(request.getCurrency())
            .asset(request.getInstrument())
            .portfolioId(request.getPortfolioId())
            .accountId(request.getAccountId())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .accountType(referenceDataSnapshot.accountType())
            .accountWalletType(referenceDataSnapshot.walletType())
            .addFees(TransactionFromProtoMapper.mapFees(request.getReservationFeeList()))
            .transactionType(TransactionType.STREET_ASSET_TRADE)
            .build();
    }

    public static Reservation map(DepositReservationSnapshot request, String uuid, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        return Reservation.builder()
            .uuid(uuid)
            .sequenceNumber(sequenceNumber)
            .reservationRef(request.getReservationRef())
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()))
            .currency(request.getCurrency())
            .quantity(bd(request.getQuantity()))
            .portfolioId(request.getPortfolioId())
            .accountId(request.getAccountId())
            .feePortfolioId(request.getFeePortfolioId())
            .feeAccountId(request.getFeeAccountId())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .accountType(referenceDataSnapshot.accountType())
            .accountWalletType(referenceDataSnapshot.walletType())
            .feePortfolioType(referenceDataSnapshot.feePortfolioType())
            .feeAccountType(referenceDataSnapshot.feeAccountType())
            .feeAccountWalletType(referenceDataSnapshot.feeWalletType())
            .addFees(TransactionFromProtoMapper.mapFees(request.getReservationFeeList()))
            .transactionType(TransactionType.DEPOSIT)
            .build();
    }

    public static Reservation map(WithdrawalReservationSnapshot request, String uuid, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        return Reservation.builder()
            .uuid(uuid)
            .sequenceNumber(sequenceNumber)
            .reservationRef(request.getReservationRef())
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()))
            .currency(request.getCurrency())
            .quantity(bd(request.getQuantity()))
            .portfolioId(request.getPortfolioId())
            .accountId(request.getAccountId())
            .feePortfolioId(request.getFeePortfolioId())
            .feeAccountId(request.getFeeAccountId())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .accountType(referenceDataSnapshot.accountType())
            .accountWalletType(referenceDataSnapshot.walletType())
            .feePortfolioType(referenceDataSnapshot.feePortfolioType())
            .feeAccountType(referenceDataSnapshot.feeAccountType())
            .feeAccountWalletType(referenceDataSnapshot.feeWalletType())
            .addFees(TransactionFromProtoMapper.mapFees(request.getReservationFeeList()))
            .transactionType(TransactionType.WITHDRAWAL)
            .build();
    }

    public static Reservation map(PortfolioCashTransferReservationSnapshot request, String uuid, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        return Reservation.builder()
            .uuid(uuid)
            .sequenceNumber(sequenceNumber)
            .reservationRef(request.getReservationRef())
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()))
            .quantity(bd(request.getQuantity()))
            .currency(request.getCurrency())
            .sourcePortfolioId(request.getFromPortfolioId())
            .targetPortfolioId(request.getToPortfolioId())
            .feePortfolioId(request.getFeePortfolioId())
            .sourcePortfolioType(referenceDataSnapshot.portfolioType())
            .targetPortfolioType(referenceDataSnapshot.portfolioType())
            .feePortfolioType(referenceDataSnapshot.feePortfolioType())
            .addFees(TransactionFromProtoMapper.mapFees(request.getReservationFeeList()))
            .transactionType(TransactionType.PORTFOLIO_CASH_TRANSFER)
            .build();
    }

    public static Reservation map(PortfolioAssetTransferReservationSnapshot request, String uuid, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        return Reservation.builder()
            .uuid(uuid)
            .sequenceNumber(sequenceNumber)
            .reservationRef(request.getReservationRef())
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()))
            .quantity(bd(request.getQuantity()))
            .asset(request.getInstrument())
            .sourcePortfolioId(request.getFromPortfolioId())
            .targetPortfolioId(request.getToPortfolioId())
            .feePortfolioId(request.getFeePortfolioId())
            .sourcePortfolioType(referenceDataSnapshot.portfolioType())
            .targetPortfolioType(referenceDataSnapshot.portfolioType())
            .feePortfolioType(referenceDataSnapshot.feePortfolioType())
            .addFees(TransactionFromProtoMapper.mapFees(request.getReservationFeeList()))
            .transactionType(TransactionType.PORTFOLIO_ASSET_TRANSFER)
            .build();
    }

    public static Reservation map(AccountCashTransferReservationSnapshot request, String uuid, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        return Reservation.builder()
            .uuid(uuid)
            .sequenceNumber(sequenceNumber)
            .reservationRef(request.getReservationRef())
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()))
            .quantity(bd(request.getQuantity()))
            .currency(request.getCurrency())
            .sourceAccountId(request.getFromAccountId())
            .targetAccountId(request.getToAccountId())
            .feePortfolioId(request.getFeePortfolioId())
            .feeAccountId(request.getFeeAccountId())
            .sourceAccountType(referenceDataSnapshot.sourceAccountType())
            .sourceAccountWalletType(referenceDataSnapshot.sourceWalletType())
            .targetAccountType(referenceDataSnapshot.targetAccountType())
            .targetAccountWalletType(referenceDataSnapshot.targetWalletType())
            .feePortfolioType(referenceDataSnapshot.feePortfolioType())
            .feeAccountType(referenceDataSnapshot.feeAccountType())
            .feeAccountWalletType(referenceDataSnapshot.feeWalletType())
            .addFees(TransactionFromProtoMapper.mapFees(request.getReservationFeeList()))
            .transactionType(TransactionType.ACCOUNT_CASH_TRANSFER)
            .build();
    }

    public static Reservation map(FeeReservationSnapshot request, String uuid, long sequenceNumber, ReferenceDataSnapshot referenceDataSnapshot) {
        return Reservation.builder()
            .uuid(uuid)
            .sequenceNumber(sequenceNumber)
            .reservationRef(request.getReservationRef())
            .dateTime(DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()))
            .currency(request.getCurrency())
            .quantity(bd(request.getQuantity()))
            .portfolioId(request.getPortfolioId())
            .accountId(request.getAccountId())
            .portfolioType(referenceDataSnapshot.portfolioType())
            .accountType(referenceDataSnapshot.accountType())
            .accountWalletType(referenceDataSnapshot.walletType())
            .transactionType(TransactionType.FEE)
            .build();
    }

    public static RequestModel.ReservationSearch map(ReservationSearch reservationSearch) {
        ZonedDateTime dateFrom = epochMillisToZonedDateTime(reservationSearch.getFrom());
        ZonedDateTime dateTo = epochMillisToZonedDateTime(reservationSearch.getTo());

        return new RequestModel.ReservationSearch(
            reservationSearch.getSymbolList(),
            reservationSearch.getAccountIdList(),
            reservationSearch.getPortfolioIdList(),
            null,
            null,
            null,
            null,
            reservationSearch.getCurrencyList(),
            mapTransactionTypes(reservationSearch.getTransactionTypeList()),
            reservationSearch.getReservationRef(),
            reservationSearch.getClientId(),
            dateFrom,
            dateTo,
            reservationSearch.getFirst(),
            reservationSearch.getAfter(),
            TransactionFromProtoMapper.map(reservationSearch.getSortingOrder())
        );
    }
}
