package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.domain.transaction.ExecType;
import io.wyden.booking.reporting.domain.transaction.Transaction;
import io.wyden.booking.reporting.domain.transaction.TransactionFee;
import io.wyden.booking.reporting.domain.transaction.TransactionFeeType;
import io.wyden.booking.reporting.domain.transaction.TransactionType;
import io.wyden.published.booking.AccountCashTransferSnapshot;
import io.wyden.published.booking.ClientAssetTradeSnapshot;
import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.DepositSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.PortfolioAssetTransferSnapshot;
import io.wyden.published.booking.PortfolioCashTransferSnapshot;
import io.wyden.published.booking.StreetAssetTradeSnapshot;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.WithdrawalSnapshot;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsExecType;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Optional;
import java.util.UUID;

import static io.wyden.cloudutils.tools.DateUtils.toIsoUtcTime;
import static io.wyden.cloudutils.tools.ProtobufUtils.toProtoString;
import static org.apache.commons.lang3.ObjectUtils.firstNonNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.slf4j.LoggerFactory.getLogger;

public final class TransactionToProtoMapper {

    private static final Logger LOGGER = getLogger(TransactionToProtoMapper.class);

    private TransactionToProtoMapper() {
    }

    public static TransactionSnapshot map(Transaction transaction) {

        String createdAt = firstNonNull(
            toIsoUtcTime(transaction.getCreatedAt()),
            toIsoUtcTime());

        // transactions are immutable (review after settlements)
        String updatedAt = createdAt;

        Metadata metadata = Metadata.newBuilder()
            .setCreatedAt(createdAt)
            .setUpdatedAt(updatedAt)
            .setRequesterId("booking-reporting")
            .setResponseId(UUID.randomUUID().toString())
            .build();


        if (transaction.getTransactionType() == TransactionType.CLIENT_CASH_TRADE) {
            ClientCashTradeSnapshot.Builder snapshot = ClientCashTradeSnapshot.newBuilder()
                .setMetadata(metadata)
                .setExecType(map(transaction.getExecType()))
                .setIsLive(transaction.isLive());

            ZonedDateTime dateTime = transaction.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(toIsoUtcTime(dateTime));
            }

            String uuid = transaction.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transaction.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transaction.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transaction.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transaction.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transaction.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal leavesQuantity = transaction.getLeavesQuantity();
            if (leavesQuantity != null) {
                snapshot.setLeavesQuantity(toProtoString(leavesQuantity));
            }

            BigDecimal price = transaction.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            String currency = transaction.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String intOrderId = transaction.getIntOrderId();
            if (intOrderId != null) {
                snapshot.setIntOrderId(intOrderId);
            }

            String extOrderId = transaction.getExtOrderId();
            if (extOrderId != null) {
                snapshot.setExtOrderId(extOrderId);
            }

            String orderId = transaction.getOrderId();
            if (orderId != null) {
                snapshot.setOrderId(orderId);
            }

            String parentOrderId = transaction.getParentOrderId();
            if (parentOrderId != null) {
                snapshot.setParentOrderId(parentOrderId);
            }

            String rootOrderId = transaction.getRootOrderId();
            if (rootOrderId != null) {
                snapshot.setRootOrderId(rootOrderId);
            }

            String clientRootOrderId = transaction.getClientRootOrderId();
            if (clientRootOrderId != null) {
                snapshot.setClientRootOrderId(clientRootOrderId);
            }

            String baseCurrency = transaction.getBaseCurrency();
            if (baseCurrency != null) {
                snapshot.setBaseCurrency(baseCurrency);
            }

            String portfolioId = transaction.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String counterPortfolioId = transaction.getCounterPortfolioId();
            if (isNotBlank(counterPortfolioId)) {
                snapshot.setCounterPortfolio(counterPortfolioId);
            }

            Collection<Fee> fees = map(transaction.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            String underlyingExecutionId = transaction.getUnderlyingExecutionId();
            if (underlyingExecutionId != null) {
                snapshot.setUnderlyingExecutionId(underlyingExecutionId);
            }

            String rootExecutionId = transaction.getRootExecutionId();
            if (rootExecutionId != null) {
                snapshot.setRootExecutionId(rootExecutionId);
            }

            snapshot.setSettled(transaction.isSettled());

            ZonedDateTime settledDateTime = transaction.getSettlementDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(toIsoUtcTime(settledDateTime));
            }

            String settlementId = transaction.getSettlementId();
            if (isNotBlank(settlementId)) {
                snapshot.setSettlementId(settlementId);
            }

            return TransactionSnapshot.newBuilder()
                .setClientCashTrade(snapshot)
                .build();
        }

        if (transaction.getTransactionType() == TransactionType.STREET_CASH_TRADE) {
            StreetCashTradeSnapshot.Builder snapshot = StreetCashTradeSnapshot.newBuilder()
                .setMetadata(metadata)
                .setExecType(map(transaction.getExecType()))
                .setIsLive(transaction.isLive());

            ZonedDateTime dateTime = transaction.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(toIsoUtcTime(dateTime));
            }

            String uuid = transaction.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transaction.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transaction.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transaction.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transaction.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transaction.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal leavesQuantity = transaction.getLeavesQuantity();
            if (leavesQuantity != null) {
                snapshot.setLeavesQuantity(toProtoString(leavesQuantity));
            }

            BigDecimal price = transaction.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            String currency = transaction.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String orderId = transaction.getOrderId();
            if (orderId != null) {
                snapshot.setOrderId(orderId);
            }

            String parentOrderId = transaction.getParentOrderId();
            if (parentOrderId != null) {
                snapshot.setParentOrderId(parentOrderId);
            }

            String rootOrderId = transaction.getRootOrderId();
            if (rootOrderId != null) {
                snapshot.setRootOrderId(rootOrderId);
            }

            String clientRootOrderId = transaction.getClientRootOrderId();
            if (clientRootOrderId != null) {
                snapshot.setClientRootOrderId(clientRootOrderId);
            }

            String intOrderId = transaction.getIntOrderId();
            if (intOrderId != null) {
                snapshot.setIntOrderId(intOrderId);
            }

            String extOrderId = transaction.getExtOrderId();
            if (extOrderId != null) {
                snapshot.setExtOrderId(extOrderId);
            }

            String baseCurrency = transaction.getBaseCurrency();
            if (baseCurrency != null) {
                snapshot.setBaseCurrency(baseCurrency);
            }

            String portfolioId = transaction.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String accountId = transaction.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setVenueAccount(accountId);
            }

            String underlyingExecutionId = transaction.getUnderlyingExecutionId();
            if (underlyingExecutionId != null) {
                snapshot.setUnderlyingExecutionId(underlyingExecutionId);
            }

            String rootExecutionId = transaction.getRootExecutionId();
            if (rootExecutionId != null) {
                snapshot.setRootExecutionId(rootExecutionId);
            }

            Collection<Fee> fees = map(transaction.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(transaction.isSettled());

            ZonedDateTime settledDateTime = transaction.getSettlementDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(toIsoUtcTime(settledDateTime));
            }

            String settlementId = transaction.getSettlementId();
            if (isNotBlank(settlementId)) {
                snapshot.setSettlementId(settlementId);
            }

            return TransactionSnapshot.newBuilder()
                .setStreetCashTrade(snapshot)
                .build();
        }

        if (transaction.getTransactionType() == TransactionType.CLIENT_ASSET_TRADE) {
            ClientAssetTradeSnapshot.Builder snapshot = ClientAssetTradeSnapshot.newBuilder()
                .setMetadata(metadata)
                .setExecType(map(transaction.getExecType()))
                .setIsLive(transaction.isLive());

            ZonedDateTime dateTime = transaction.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(toIsoUtcTime(dateTime));
            }

            String uuid = transaction.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transaction.getReservationRef();
            if (isNotBlank(reservationRef)) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transaction.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transaction.getVenueExecutionId();
            if (isNotBlank(venueExecutionId)) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transaction.getDescription();
            if (isNotBlank(description)) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transaction.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal price = transaction.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            String currency = transaction.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String intOrderId = transaction.getIntOrderId();
            if (intOrderId != null) {
                snapshot.setIntOrderId(intOrderId);
            }

            String extOrderId = transaction.getExtOrderId();
            if (extOrderId != null) {
                snapshot.setExtOrderId(extOrderId);
            }

            String orderId = transaction.getOrderId();
            if (orderId != null) {
                snapshot.setOrderId(orderId);
            }

            String asset = transaction.getAsset();
            if (asset != null) {
                snapshot.setInstrument(asset);
            }

            String portfolioId = transaction.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String counterPortfolioId = transaction.getCounterPortfolioId();
            if (isNotBlank(counterPortfolioId)) {
                snapshot.setCounterPortfolio(counterPortfolioId);
            }

            Collection<Fee> fees = map(transaction.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(transaction.isSettled());

            ZonedDateTime settledDateTime = transaction.getSettlementDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(toIsoUtcTime(settledDateTime));
            }

            return TransactionSnapshot.newBuilder()
                .setClientAssetTrade(snapshot)
                .build();
        }

        if (transaction.getTransactionType() == TransactionType.STREET_ASSET_TRADE) {
            StreetAssetTradeSnapshot.Builder snapshot = StreetAssetTradeSnapshot.newBuilder()
                .setMetadata(metadata)
                .setExecType(map(transaction.getExecType()))
                .setIsLive(transaction.isLive());

            ZonedDateTime dateTime = transaction.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(toIsoUtcTime(dateTime));
            }

            String uuid = transaction.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transaction.getReservationRef();
            if (isNotBlank(reservationRef)) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transaction.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transaction.getVenueExecutionId();
            if (isNotBlank(venueExecutionId)) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transaction.getDescription();
            if (isNotBlank(description)) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transaction.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal price = transaction.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            String currency = transaction.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String intOrderId = transaction.getIntOrderId();
            if (intOrderId != null) {
                snapshot.setIntOrderId(intOrderId);
            }

            String extOrderId = transaction.getExtOrderId();
            if (extOrderId != null) {
                snapshot.setExtOrderId(extOrderId);
            }

            String orderId = transaction.getOrderId();
            if (orderId != null) {
                snapshot.setOrderId(orderId);
            }

            String asset = transaction.getAsset();
            if (asset != null) {
                snapshot.setInstrument(asset);
            }

            String portfolioId = transaction.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String accountId = transaction.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setVenueAccount(accountId);
            }

            Collection<Fee> fees = map(transaction.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(transaction.isSettled());

            ZonedDateTime settledDateTime = transaction.getSettlementDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(toIsoUtcTime(settledDateTime));
            }

            return TransactionSnapshot.newBuilder()
                .setStreetAssetTrade(snapshot)
                .build();
        }

        if (transaction.getTransactionType() == TransactionType.DEPOSIT) {
            DepositSnapshot.Builder snapshot = DepositSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(transaction.isLive());

            ZonedDateTime dateTime = transaction.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(toIsoUtcTime(dateTime));
            }

            String uuid = transaction.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transaction.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transaction.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transaction.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transaction.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transaction.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = transaction.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String portfolioId = transaction.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String accountId = transaction.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setAccount(accountId);
            }

            String feePortfolioId = transaction.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            String feeAccountId = transaction.getFeeAccountId();
            if (isNotBlank(feeAccountId)) {
                snapshot.setFeeAccountId(feeAccountId);
            }

            Collection<Fee> fees = map(transaction.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(transaction.isSettled());

            ZonedDateTime settledDateTime = transaction.getSettlementDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(toIsoUtcTime(settledDateTime));
            }

            return TransactionSnapshot.newBuilder()
                .setDeposit(snapshot)
                .build();
        }

        if (transaction.getTransactionType() == TransactionType.WITHDRAWAL) {
            WithdrawalSnapshot.Builder snapshot = WithdrawalSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(transaction.isLive());

            ZonedDateTime dateTime = transaction.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(toIsoUtcTime(dateTime));
            }

            String uuid = transaction.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transaction.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transaction.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transaction.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transaction.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transaction.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = transaction.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String portfolioId = transaction.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String accountId = transaction.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setAccount(accountId);
            }

            String feePortfolioId = transaction.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            String feeAccountId = transaction.getFeeAccountId();
            if (isNotBlank(feeAccountId)) {
                snapshot.setFeeAccountId(feeAccountId);
            }

            Collection<Fee> fees = map(transaction.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(transaction.isSettled());

            ZonedDateTime settledDateTime = transaction.getSettlementDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(toIsoUtcTime(settledDateTime));
            }

            return TransactionSnapshot.newBuilder()
                .setWithdrawal(snapshot)
                .build();
        }

        if (transaction.getTransactionType() == TransactionType.PORTFOLIO_CASH_TRANSFER) {
            PortfolioCashTransferSnapshot.Builder snapshot = PortfolioCashTransferSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(transaction.isLive());

            ZonedDateTime dateTime = transaction.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(toIsoUtcTime(dateTime));
            }

            String uuid = transaction.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transaction.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transaction.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transaction.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transaction.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transaction.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = transaction.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String fromPortfolioId = transaction.getSourcePortfolioId();
            if (isNotBlank(fromPortfolioId)) {
                snapshot.setFromPortfolioId(fromPortfolioId);
            }

            String toPortfolioId = transaction.getTargetPortfolioId();
            if (isNotBlank(toPortfolioId)) {
                snapshot.setToPortfolioId(toPortfolioId);
            }

            Collection<Fee> fees = map(transaction.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(transaction.isSettled());

            ZonedDateTime settledDateTime = transaction.getSettlementDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(toIsoUtcTime(settledDateTime));
            }

            String feePortfolioId = transaction.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            return TransactionSnapshot.newBuilder()
                .setPortfolioCashTransfer(snapshot)
                .build();
        }

        if (transaction.getTransactionType() == TransactionType.ACCOUNT_CASH_TRANSFER) {
            AccountCashTransferSnapshot.Builder snapshot = AccountCashTransferSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(transaction.isLive());

            ZonedDateTime dateTime = transaction.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(toIsoUtcTime(dateTime));
            }

            String uuid = transaction.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transaction.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transaction.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transaction.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transaction.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transaction.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = transaction.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String fromAccountId = transaction.getSourceAccountId();
            if (isNotBlank(fromAccountId)) {
                snapshot.setFromAccountId(fromAccountId);
            }

            String toAccountId = transaction.getTargetAccountId();
            if (isNotBlank(toAccountId)) {
                snapshot.setToAccountId(toAccountId);
            }

            String feeAccountId = transaction.getFeeAccountId();
            if (isNotBlank(feeAccountId)) {
                snapshot.setFeeAccountId(feeAccountId);
            }

            String feePortfolioId = transaction.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            Collection<Fee> fees = map(transaction.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(transaction.isSettled());

            ZonedDateTime settledDateTime = transaction.getSettlementDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(toIsoUtcTime(settledDateTime));
            }

            return TransactionSnapshot.newBuilder()
                .setAccountCashTransfer(snapshot)
                .build();
        }

        if (transaction.getTransactionType() == TransactionType.PORTFOLIO_ASSET_TRANSFER) {
            PortfolioAssetTransferSnapshot.Builder snapshot = PortfolioAssetTransferSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(transaction.isLive());

            ZonedDateTime dateTime = transaction.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(toIsoUtcTime(dateTime));
            }

            String uuid = transaction.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transaction.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transaction.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transaction.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transaction.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transaction.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String asset = transaction.getAsset();
            if (asset != null) {
                snapshot.setInstrument(asset);
            }

            String fromPortfolioId = transaction.getSourcePortfolioId();
            if (isNotBlank(fromPortfolioId)) {
                snapshot.setFromPortfolioId(fromPortfolioId);
            }

            String toPortfolioId = transaction.getTargetPortfolioId();
            if (isNotBlank(toPortfolioId)) {
                snapshot.setToPortfolioId(toPortfolioId);
            }

            Collection<Fee> fees = map(transaction.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(transaction.isSettled());

            ZonedDateTime settledDateTime = transaction.getSettlementDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(toIsoUtcTime(settledDateTime));
            }

            String feePortfolioId = transaction.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            return TransactionSnapshot.newBuilder()
                .setPortfolioAssetTransfer(snapshot)
                .build();
        }

        LOGGER.warn("Unknown transaction type. Cannot convert to proto, returning empty proto: {}", transaction);

        return TransactionSnapshot.newBuilder()
            .build();
    }

    private static OemsExecType map(ExecType execType) {
        if (execType == null) {
            return OemsExecType.EXEC_TYPE_UNSPECIFIED;
        }

        return switch (execType) {
            case PENDING_NEW -> OemsExecType.PENDING_NEW;
            case NEW -> OemsExecType.NEW;
            case PARTIAL_FILL -> OemsExecType.PARTIAL_FILL;
            case FILL -> OemsExecType.FILL;
            case PENDING_CANCEL -> OemsExecType.PENDING_CANCEL;
            case CANCELED -> OemsExecType.CANCELED;
            case REJECTED -> OemsExecType.REJECTED;
            case EXPIRED -> OemsExecType.EXPIRED;
            case CALCULATED -> OemsExecType.CALCULATED;
            case RESTATED -> OemsExecType.RESTATED;
        };
    }

    public static Collection<Fee> map(Collection<TransactionFee> fees) {
        if (fees == null) {
            return null;
        }

        return fees.stream()
            .map(fee -> Fee.newBuilder()
                .setAmount(toProtoString(fee.getAmount()))
                .setCurrency(fee.getCurrency())
                .setFeeType(map(fee.getFeeType()))
                .build())
            .toList();
    }

    public static FeeType map(TransactionFeeType feeType) {
        if (feeType == null) {
            return null;
        }

        return switch (feeType) {
            case EXCHANGE_FEE -> FeeType.EXCHANGE_FEE;
            case FIXED_FEE -> FeeType.FIXED_FEE;
            case TRANSACTION_FEE -> FeeType.TRANSACTION_FEE;
            default -> FeeType.FEE_TYPE_UNSPECIFIED;
        };
    }

    public static Optional<CursorNode> mapToCursorNode(Transaction transaction) {
        if (transaction == null) {
            return Optional.empty();
        }

        TransactionSnapshot transactionSnapshot = map(transaction);

        CursorNode cursorNode = CursorNode.newBuilder()
            .setTransaction(transactionSnapshot)
            .build();

        return Optional.of(cursorNode);
    }
}
