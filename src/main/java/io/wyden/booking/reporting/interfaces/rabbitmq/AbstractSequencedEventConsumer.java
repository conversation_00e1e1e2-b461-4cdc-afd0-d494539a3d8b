package io.wyden.booking.reporting.interfaces.rabbitmq;

import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.wyden.booking.reporting.domain.tracking.EventTrackingRepository;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.InfrastructureException;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * Abstract base class for event consumers that provides common functionality
 * for gap recovery, sequence tracking, and RabbitMQ setup.
 */
public abstract class AbstractSequencedEventConsumer<T> implements MessageConsumer<T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractSequencedEventConsumer.class);
    
    protected final RabbitIntegrator rabbitIntegrator;
    protected final EventTrackingRepository trackingRepository;
    protected final boolean shouldBreakOnError;
    protected long lastProcessedSeqNum = 0;

    protected AbstractSequencedEventConsumer(RabbitIntegrator rabbitIntegrator,
                                             EventTrackingRepository trackingRepository,
                                             boolean shouldBreakOnError) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.trackingRepository = trackingRepository;
        this.shouldBreakOnError = shouldBreakOnError;
        
        // Load the last processed sequence from the database
        this.lastProcessedSeqNum = trackingRepository.findLastProcessedSequence(getEventType()).orElse(0L);
        LOGGER.info("Loaded last processed sequence number for {}: {}", getEventType(), lastProcessedSeqNum);
    }

    protected <E extends Message> RabbitQueue<E> declareQueue(String queueName, RabbitExchange<E> exchange) {
        RabbitQueue<E> queue = new RabbitQueueBuilder<E>(rabbitIntegrator)
            .setQueueName(queueName)
            .declare();

        Map<String, Object> headerMatchers = Map.of();
        queue.bindWithHeaders(exchange, MatchingCondition.ALL, headerMatchers);
        LOGGER.info("Binding exchange {} and queue {} with headers {}", exchange, queue, headerMatchers);
        return queue;
    }

    @Override
    public ConsumptionResult consume(T event, AMQP.BasicProperties properties) {
        LOGGER.info("Received {}: \n{}", getEventType(), event);

        long sequenceNumber = getSequenceNumber(event);

        boolean shouldSkip = !ensureNoMissingEvents(sequenceNumber, lastProcessedSeqNum);
        if (shouldSkip) {
            return ConsumptionResult.consumed();
        }

        LOGGER.info("{} - Processing realtime {} event.", sequenceNumber, getEventType());

        try {
            if (processEvent(event)) {
                updateLastProcessedSequence(sequenceNumber);
                return ConsumptionResult.consumed();
            } else {
                if (shouldBreakOnError) {
                    LOGGER.error("{} - Failed to process {} event - aborting with requeue", sequenceNumber, getEventType());
                    return ConsumptionResult.failureNeedsRequeue();
                }

                LOGGER.warn("Failed to process {} event {}, skipping", getEventType(), sequenceNumber);
                updateLastProcessedSequence(sequenceNumber);
                return ConsumptionResult.failureNonRecoverable();
            }
        } catch (Exception e) {
            LOGGER.error("Failed to consume {} event {}", getEventType(), event, e);
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    protected void performStartupRecovery() {
        LOGGER.info("Starting startup recovery for {} events", getEventType());
        List<T> recoveredEvents = recoverAllMissingEvents(lastProcessedSeqNum);
        processRecoveredEvents(recoveredEvents);

        LOGGER.info("Startup recovery complete. Recovered {} {} events", recoveredEvents.size(), getEventType());
    }

    protected boolean ensureNoMissingEvents(long inboundSequenceNumber, long lastProcessedSequence) {
        long expectedSequence = lastProcessedSequence + 1;
        if (inboundSequenceNumber != expectedSequence) {
            if (inboundSequenceNumber < expectedSequence) {
                LOGGER.warn("Received already processed sequence number: {}, current: {}. Skipping processing", inboundSequenceNumber, lastProcessedSequence);
                return false;
            }

            LOGGER.warn("Sequence gap detected. Expected: {}, Received: {}", expectedSequence, inboundSequenceNumber);
            List<T> gapRecovered = recoverGap(expectedSequence, inboundSequenceNumber);
            LOGGER.info("Recovered {} events before realtime event", gapRecovered.size());
            processRecoveredEvents(gapRecovered);
        }

        return true;
    }

    protected void processRecoveredEvents(List<T> recoveredEvents) {
        if (!recoveredEvents.isEmpty()) {
            LOGGER.info("Processing {} recovered {} events", recoveredEvents.size(), getEventType());
            int processedCount = 0;
            int failedCount = 0;

            for (T event : recoveredEvents) {
                if (processEvent(event)) {
                    updateLastProcessedSequence(getSequenceNumber(event));
                    processedCount++;
                } else {
                    failedCount++;
                    if (shouldBreakOnError) {
                        LOGGER.error("Failed to process recovered {} event: {}", getEventType(), event);
                        throw new InfrastructureException("Failed to process %s event %s. Aborting".formatted(getEventType(), event));
                    } else {
                        LOGGER.warn("Failed to process recovered {} event, skipping: {}", getEventType(), event);
                    }
                }
            }

            LOGGER.info("Successfully processed {}/{} recovered {} events, {} failed and skipped", 
                processedCount, recoveredEvents.size(), getEventType(), failedCount);
        } else {
            LOGGER.info("No {} events to recover", getEventType());
        }
    }

    protected void updateLastProcessedSequence(long sequenceNumber) {
        lastProcessedSeqNum = sequenceNumber;
        try {
            int updated = trackingRepository.updateLastProcessedSequence(getEventType(), sequenceNumber);
            if (updated == 0) {
                LOGGER.warn("Failed to update sequence tracking - no rows updated for sequence: {}", sequenceNumber);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to persist sequence number {} to database", sequenceNumber, e);
            // Don't throw exception here as we don't want to fail message processing due to tracking issues
        }
    }

    public void resetSequenceNumber(long sequenceNumber) {
        updateLastProcessedSequence(sequenceNumber);
    }

    // Abstract methods that subclasses must implement
    protected abstract String getEventType();
    protected abstract long getSequenceNumber(T event);
    protected abstract boolean processEvent(T event);
    protected abstract List<T> recoverAllMissingEvents(long lastProcessedSequence);
    protected abstract List<T> recoverGap(long expectedSequence, long receivedSequence);
}