package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.domain.reservation.Reservation;
import io.wyden.booking.reporting.domain.reservation.ReservationBalance;
import io.wyden.booking.reporting.domain.transaction.TransactionType;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.AccountCashTransferReservationSnapshot;
import io.wyden.published.booking.ClientAssetTradeReservationSnapshot;
import io.wyden.published.booking.ClientCashTradeReservationSnapshot;
import io.wyden.published.booking.DepositReservationSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeReservationSnapshot;
import io.wyden.published.booking.PortfolioAssetTransferReservationSnapshot;
import io.wyden.published.booking.PortfolioCashTransferReservationSnapshot;
import io.wyden.published.booking.ReservationBalanceList;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.booking.StreetAssetTradeReservationSnapshot;
import io.wyden.published.booking.StreetCashTradeReservationSnapshot;
import io.wyden.published.booking.WithdrawalReservationSnapshot;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.Metadata;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Optional;
import java.util.UUID;

import static io.wyden.cloudutils.tools.ProtobufUtils.toProtoString;
import static io.wyden.published.booking.TransactionType.TRANSACTION_TYPE_ACCOUNT_CASH_TRANSFER;
import static io.wyden.published.booking.TransactionType.TRANSACTION_TYPE_CLIENT_ASSET_TRADE;
import static io.wyden.published.booking.TransactionType.TRANSACTION_TYPE_CLIENT_CASH_TRADE;
import static io.wyden.published.booking.TransactionType.TRANSACTION_TYPE_DEPOSIT;
import static io.wyden.published.booking.TransactionType.TRANSACTION_TYPE_FEE;
import static io.wyden.published.booking.TransactionType.TRANSACTION_TYPE_PORTFOLIO_ASSET_TRANSFER;
import static io.wyden.published.booking.TransactionType.TRANSACTION_TYPE_PORTFOLIO_CASH_TRANSFER;
import static io.wyden.published.booking.TransactionType.TRANSACTION_TYPE_STREET_ASSET_TRADE;
import static io.wyden.published.booking.TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE;
import static io.wyden.published.booking.TransactionType.TRANSACTION_TYPE_WITHDRAWAL;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.slf4j.LoggerFactory.getLogger;

public final class ReservationToProtoMapper {

    private static final Logger LOGGER = getLogger(ReservationToProtoMapper.class);

    private ReservationToProtoMapper() {
    }

    public static ReservationSnapshot map(Reservation reservation) {
        String createdAt = DateUtils.toIsoUtcTime(reservation.getCreatedAt());

        Metadata metadata = Metadata.newBuilder()
            .setCreatedAt(createdAt)
            .setRequesterId("booking-reporting")
            .setResponseId(UUID.randomUUID().toString())
            .build();

        if (reservation.getTransactionType() == TransactionType.CLIENT_CASH_TRADE) {
            ClientCashTradeReservationSnapshot.Builder snapshot = ClientCashTradeReservationSnapshot.newBuilder()
                .setTransactionType(TRANSACTION_TYPE_CLIENT_CASH_TRADE)
                .setMetadata(metadata);

            String reservationRef = reservation.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            ZonedDateTime dateTime = reservation.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String currency = reservation.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String baseCurrency = reservation.getBaseCurrency();
            if (baseCurrency != null) {
                snapshot.setBaseCurrency(baseCurrency);
            }

            BigDecimal quantity = reservation.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal price = reservation.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            BigDecimal stopPrice = reservation.getStopPrice();
            if (stopPrice != null) {
                snapshot.setStopPrice(toProtoString(stopPrice));
            }

            String portfolioId = reservation.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolioId(portfolioId);
            }

            String counterPortfolioId = reservation.getCounterPortfolioId();
            if (isNotBlank(counterPortfolioId)) {
                snapshot.setCounterPortfolioId(counterPortfolioId);
            }

            Collection<Fee> fees = TransactionToProtoMapper.map(reservation.getFees());
            if (fees != null) {
                snapshot.addAllReservationFee(fees);
            }

            return ReservationSnapshot.newBuilder()
                .setClientCashTradeReservation(snapshot)
                .build();
        }

        if (reservation.getTransactionType() == TransactionType.STREET_CASH_TRADE) {
            StreetCashTradeReservationSnapshot.Builder snapshot = StreetCashTradeReservationSnapshot.newBuilder()
                .setTransactionType(TRANSACTION_TYPE_STREET_CASH_TRADE)
                .setMetadata(metadata);

            String reservationRef = reservation.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            ZonedDateTime dateTime = reservation.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String currency = reservation.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String baseCurrency = reservation.getBaseCurrency();
            if (baseCurrency != null) {
                snapshot.setBaseCurrency(baseCurrency);
            }

            BigDecimal quantity = reservation.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal price = reservation.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            BigDecimal stopPrice = reservation.getStopPrice();
            if (stopPrice != null) {
                snapshot.setStopPrice(toProtoString(stopPrice));
            }

            String portfolioId = reservation.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolioId(portfolioId);
            }

            String accountId = reservation.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setAccountId(accountId);
            }

            Collection<Fee> fees = TransactionToProtoMapper.map(reservation.getFees());
            if (fees != null) {
                snapshot.addAllReservationFee(fees);
            }

            return ReservationSnapshot.newBuilder()
                .setStreetCashTradeReservation(snapshot)
                .build();
        }

        if (reservation.getTransactionType() == TransactionType.CLIENT_ASSET_TRADE) {
            ClientAssetTradeReservationSnapshot.Builder snapshot = ClientAssetTradeReservationSnapshot.newBuilder()
                .setTransactionType(TRANSACTION_TYPE_CLIENT_ASSET_TRADE)
                .setMetadata(metadata);

            String reservationRef = reservation.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            ZonedDateTime dateTime = reservation.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String currency = reservation.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String asset = reservation.getAsset();
            if (isNotBlank(asset)) {
                snapshot.setInstrument(asset);
            }

            BigDecimal quantity = reservation.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal price = reservation.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            BigDecimal stopPrice = reservation.getStopPrice();
            if (stopPrice != null) {
                snapshot.setStopPrice(toProtoString(stopPrice));
            }

            String portfolioId = reservation.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolioId(portfolioId);
            }

            String counterPortfolioId = reservation.getCounterPortfolioId();
            if (isNotBlank(counterPortfolioId)) {
                snapshot.setCounterPortfolioId(counterPortfolioId);
            }

            Collection<Fee> fees = TransactionToProtoMapper.map(reservation.getFees());
            if (fees != null) {
                snapshot.addAllReservationFee(fees);
            }

            return ReservationSnapshot.newBuilder()
                .setClientAssetTradeReservation(snapshot)
                .build();
        }

        if (reservation.getTransactionType() == TransactionType.STREET_ASSET_TRADE) {
            StreetAssetTradeReservationSnapshot.Builder snapshot = StreetAssetTradeReservationSnapshot.newBuilder()
                .setTransactionType(TRANSACTION_TYPE_STREET_ASSET_TRADE)
                .setMetadata(metadata);

            String reservationRef = reservation.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            ZonedDateTime dateTime = reservation.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String currency = reservation.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String asset = reservation.getAsset();
            if (isNotBlank(asset)) {
                snapshot.setInstrument(asset);
            }

            BigDecimal quantity = reservation.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal price = reservation.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            BigDecimal stopPrice = reservation.getStopPrice();
            if (stopPrice != null) {
                snapshot.setStopPrice(toProtoString(stopPrice));
            }

            String portfolioId = reservation.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolioId(portfolioId);
            }

            String accountId = reservation.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setAccountId(accountId);
            }

            Collection<Fee> fees = TransactionToProtoMapper.map(reservation.getFees());
            if (fees != null) {
                snapshot.addAllReservationFee(fees);
            }

            return ReservationSnapshot.newBuilder()
                .setStreetAssetTradeReservation(snapshot)
                .build();
        }

        if (reservation.getTransactionType() == TransactionType.DEPOSIT) {
            DepositReservationSnapshot.Builder snapshot = DepositReservationSnapshot.newBuilder()
                .setTransactionType(TRANSACTION_TYPE_DEPOSIT)
                .setMetadata(metadata);

            ZonedDateTime dateTime = reservation.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String reservationRef = reservation.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            BigDecimal quantity = reservation.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = reservation.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String portfolioId = reservation.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolioId(portfolioId);
            }

            String accountId = reservation.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setAccountId(accountId);
            }

            String feePortfolioId = reservation.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            String feeAccountId = reservation.getFeeAccountId();
            if (isNotBlank(feeAccountId)) {
                snapshot.setFeeAccountId(feeAccountId);
            }

            Collection<Fee> fees = TransactionToProtoMapper.map(reservation.getFees());
            if (fees != null) {
                snapshot.addAllReservationFee(fees);
            }

            return ReservationSnapshot.newBuilder()
                .setDepositReservation(snapshot)
                .build();
        }

        if (reservation.getTransactionType() == TransactionType.WITHDRAWAL) {
            WithdrawalReservationSnapshot.Builder snapshot = WithdrawalReservationSnapshot.newBuilder()
                .setTransactionType(TRANSACTION_TYPE_WITHDRAWAL)
                .setMetadata(metadata);

            ZonedDateTime dateTime = reservation.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String reservationRef = reservation.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            BigDecimal quantity = reservation.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = reservation.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String portfolioId = reservation.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolioId(portfolioId);
            }

            String accountId = reservation.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setAccountId(accountId);
            }

            String feePortfolioId = reservation.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            String feeAccountId = reservation.getFeeAccountId();
            if (isNotBlank(feeAccountId)) {
                snapshot.setFeeAccountId(feeAccountId);
            }

            Collection<Fee> fees = TransactionToProtoMapper.map(reservation.getFees());
            if (fees != null) {
                snapshot.addAllReservationFee(fees);
            }

            return ReservationSnapshot.newBuilder()
                .setWithdrawalReservation(snapshot)
                .build();
        }

        if (reservation.getTransactionType() == TransactionType.PORTFOLIO_CASH_TRANSFER) {
            PortfolioCashTransferReservationSnapshot.Builder snapshot = PortfolioCashTransferReservationSnapshot.newBuilder()
                .setTransactionType(TRANSACTION_TYPE_PORTFOLIO_CASH_TRANSFER)
                .setMetadata(metadata);

            ZonedDateTime dateTime = reservation.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String reservationRef = reservation.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            BigDecimal quantity = reservation.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = reservation.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String fromPortfolioId = reservation.getSourcePortfolioId();
            if (isNotBlank(fromPortfolioId)) {
                snapshot.setFromPortfolioId(fromPortfolioId);
            }

            String toPortfolioId = reservation.getTargetPortfolioId();
            if (isNotBlank(toPortfolioId)) {
                snapshot.setToPortfolioId(toPortfolioId);
            }

            Collection<Fee> fees = TransactionToProtoMapper.map(reservation.getFees());
            if (fees != null) {
                snapshot.addAllReservationFee(fees);
            }

            String feePortfolioId = reservation.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            return ReservationSnapshot.newBuilder()
                .setPortfolioCashTransferReservation(snapshot)
                .build();
        }

        if (reservation.getTransactionType() == TransactionType.ACCOUNT_CASH_TRANSFER) {
            AccountCashTransferReservationSnapshot.Builder snapshot = AccountCashTransferReservationSnapshot.newBuilder()
                .setTransactionType(TRANSACTION_TYPE_ACCOUNT_CASH_TRANSFER)
                .setMetadata(metadata);

            ZonedDateTime dateTime = reservation.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String reservationRef = reservation.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            BigDecimal quantity = reservation.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = reservation.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String fromAccountId = reservation.getSourceAccountId();
            if (isNotBlank(fromAccountId)) {
                snapshot.setFromAccountId(fromAccountId);
            }

            String toAccountId = reservation.getTargetAccountId();
            if (isNotBlank(toAccountId)) {
                snapshot.setToAccountId(toAccountId);
            }

            String feeAccountId = reservation.getFeeAccountId();
            if (isNotBlank(feeAccountId)) {
                snapshot.setFeeAccountId(feeAccountId);
            }

            String feePortfolioId = reservation.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            Collection<Fee> fees = TransactionToProtoMapper.map(reservation.getFees());
            if (fees != null) {
                snapshot.addAllReservationFee(fees);
            }

            return ReservationSnapshot.newBuilder()
                .setAccountCashTransferReservation(snapshot)
                .build();
        }

        if (reservation.getTransactionType() == TransactionType.PORTFOLIO_ASSET_TRANSFER) {
            PortfolioAssetTransferReservationSnapshot.Builder snapshot = PortfolioAssetTransferReservationSnapshot.newBuilder()
                .setTransactionType(TRANSACTION_TYPE_PORTFOLIO_ASSET_TRANSFER)
                .setMetadata(metadata);

            ZonedDateTime dateTime = reservation.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String reservationRef = reservation.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            BigDecimal quantity = reservation.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String asset = reservation.getAsset();
            if (asset != null) {
                snapshot.setInstrument(asset);
            }

            String fromPortfolioId = reservation.getSourcePortfolioId();
            if (isNotBlank(fromPortfolioId)) {
                snapshot.setFromPortfolioId(fromPortfolioId);
            }

            String toPortfolioId = reservation.getTargetPortfolioId();
            if (isNotBlank(toPortfolioId)) {
                snapshot.setToPortfolioId(toPortfolioId);
            }

            Collection<Fee> fees = TransactionToProtoMapper.map(reservation.getFees());
            if (fees != null) {
                snapshot.addAllReservationFee(fees);
            }

            String feePortfolioId = reservation.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            return ReservationSnapshot.newBuilder()
                .setPortfolioAssetTransferReservation(snapshot)
                .build();
        }

        if (reservation.getTransactionType() == TransactionType.FEE) {
            FeeReservationSnapshot.Builder snapshot = FeeReservationSnapshot.newBuilder()
                .setTransactionType(TRANSACTION_TYPE_FEE)
                .setMetadata(metadata);

            ZonedDateTime dateTime = reservation.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String reservationRef = reservation.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            BigDecimal quantity = reservation.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = reservation.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String portfolioId = reservation.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolioId(portfolioId);
            }

            String accountId = reservation.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setAccountId(accountId);
            }

            return ReservationSnapshot.newBuilder()
                .setFeeReservation(snapshot)
                .build();
        }

        LOGGER.warn("Unknown transaction type. Cannot convert to proto, returning empty proto: {}", reservation);

        return ReservationSnapshot.newBuilder()
            .build();
    }

    public static ReservationBalanceList map(Collection<ReservationBalance> reservationBalances) {
        ReservationBalanceList.Builder list = ReservationBalanceList.newBuilder();

        for (ReservationBalance reservationBalance : reservationBalances) {
            io.wyden.published.booking.ReservationBalance.Builder builder = io.wyden.published.booking.ReservationBalance.newBuilder();

            if (isNotBlank(reservationBalance.portfolioId())) {
                builder.setPortfolioId(reservationBalance.portfolioId());
            }

            if (isNotBlank(reservationBalance.accountId())) {
                builder.setAccountId(reservationBalance.accountId());
            }

            if (isNotBlank(reservationBalance.symbol())) {
                builder.setCurrency(reservationBalance.symbol());
            }

            if (reservationBalance.quantity() != null) {
                builder.setQuantity(toProtoString(reservationBalance.quantity()));
            }

            list.addReservationBalance(builder.build());
        }
        return list.build();
    }

    public static Optional<CursorNode> mapToCursorNode(Reservation reservation) {
        if (reservation == null) {
            return Optional.empty();
        }

        ReservationSnapshot reservationSnapshot = map(reservation);

        CursorNode cursorNode = CursorNode.newBuilder()
            .setReservation(reservationSnapshot)
            .build();

        return Optional.of(cursorNode);
    }
}
