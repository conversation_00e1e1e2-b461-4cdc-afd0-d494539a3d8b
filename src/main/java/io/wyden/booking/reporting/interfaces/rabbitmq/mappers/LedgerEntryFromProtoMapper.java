package io.wyden.booking.reporting.interfaces.rabbitmq.mappers;

import io.wyden.booking.reporting.application.referencedata.AccountSnapshot;
import io.wyden.booking.reporting.application.referencedata.PortfolioSnapshot;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntryType;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.LedgerEntrySearch;
import io.wyden.published.booking.LedgerEntrySnapshot;
import io.wyden.published.common.Metadata;
import org.apache.commons.collections4.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static io.wyden.booking.reporting.interfaces.rabbitmq.mappers.TransactionFromProtoMapper.mapFees;
import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public final class LedgerEntryFromProtoMapper {

    private LedgerEntryFromProtoMapper() {
    }

    public static Optional<LedgerEntry> map(LedgerEntrySnapshot entry, PortfolioSnapshot portfolioSnapshot, AccountSnapshot accountSnapshot) {
        if (entry == null) {
            return Optional.empty();
        }

        LedgerEntry.Builder builder = LedgerEntry.builder()
            .quantity(bd(entry.getQuantity()))
            .price(bd(entry.getPrice()))
            .sequenceNumber(entry.getSequenceNumber())
            .dateTime(dt(entry.getMetadata()))
            .reservationRef(entry.getReservationRef())
            .transactionId(entry.getTransactionId())
            .portfolioId(entry.getPortfolio())
            .accountId(entry.getAccount())
            .settled(mapBool(entry.getSettled()))
            .symbol(entry.getSymbol())
            .balanceBefore(bd(entry.getBalanceBefore()))
            .balanceAfter(bd(entry.getBalanceAfter()))
            .ledgerEntryType(map(entry.getType()))
            .addFees(mapFees(entry.getFeesList()));

        if (isNotBlank(entry.getPortfolio())) {
            builder.portfolioId(entry.getPortfolio())
                .portfolioType(portfolioSnapshot.portfolioType());
        } else if (isNotBlank(entry.getAccount())) {
            builder.accountId(entry.getAccount())
                .accountType(accountSnapshot.accountType())
                .accountWalletType(accountSnapshot.walletType());
        } else {
            throw new IllegalArgumentException("Portfolio id and account id cannot both be empty. Portfolio id: %s, account id: %s (PositionSnapshot: %s)"
                .formatted(entry.getPortfolio(), entry.getAccount(), entry));
        }

        return Optional.of(builder.build());
    }

    public static RequestModel.LedgerEntrySearch map(LedgerEntrySearch ledgerEntrySearch) {
        if (ledgerEntrySearch == null) {
            return null;
        }

        return new RequestModel.LedgerEntrySearch(
            ledgerEntrySearch.getSymbolList(),
            ledgerEntrySearch.getCurrencyList(),
            ledgerEntrySearch.getAccountIdList(),
            ledgerEntrySearch.getPortfolioIdList(),
            null,
            null,
            null,
            List.of(),
            map(ledgerEntrySearch.getLedgerEntryTypeList()),
            ledgerEntrySearch.getTransactionId(),
            ledgerEntrySearch.getOrderId(),
            ledgerEntrySearch.getClientId(),
            dt(ledgerEntrySearch.getFrom()),
            dt(ledgerEntrySearch.getTo()),
            ledgerEntrySearch.getFirst(),
            ledgerEntrySearch.getAfter(),
            TransactionFromProtoMapper.map(ledgerEntrySearch.getSortingOrder())
        );
    }

    private static LedgerEntryType map(io.wyden.published.booking.LedgerEntryType ledgerEntryType) {
        if (ledgerEntryType == null) {
            return LedgerEntryType.UNSPECIFIED;
        }

        return switch (ledgerEntryType) {
            case UNSPECIFIED -> LedgerEntryType.UNSPECIFIED;
            case ASSET_TRADE_BUY -> LedgerEntryType.ASSET_TRADE_BUY;
            case ASSET_TRADE_SELL -> LedgerEntryType.ASSET_TRADE_SELL;
            case CASH_TRADE_CREDIT -> LedgerEntryType.CASH_TRADE_CREDIT;
            case CASH_TRADE_DEBIT -> LedgerEntryType.CASH_TRADE_DEBIT;
            case ASSET_TRADE_PROCEEDS -> LedgerEntryType.ASSET_TRADE_PROCEEDS;
            case DEPOSIT -> LedgerEntryType.DEPOSIT;
            case WITHDRAWAL -> LedgerEntryType.WITHDRAWAL;
            case TRANSFER -> LedgerEntryType.TRANSFER;
            case FEE -> LedgerEntryType.FEE;
            case TRADING_FEE -> LedgerEntryType.TRADING_FEE;
            case DEPOSIT_FEE -> LedgerEntryType.DEPOSIT_FEE;
            case WITHDRAWAL_FEE -> LedgerEntryType.WITHDRAWAL_FEE;
            case TRANSFER_FEE -> LedgerEntryType.TRANSFER_FEE;
            case RESERVATION -> LedgerEntryType.RESERVATION;
            case RESERVATION_RELEASE -> LedgerEntryType.RESERVATION_RELEASE;
            case RESERVATION_RELEASE_REMAINING -> LedgerEntryType.RESERVATION_RELEASE_REMAINING;
            case WITHDRAWAL_RESERVATION -> LedgerEntryType.WITHDRAWAL_RESERVATION;
            default -> LedgerEntryType.UNSPECIFIED;
        };
    }

    private static Collection<LedgerEntryType> map(Collection<io.wyden.published.booking.LedgerEntryType> ledgerEntryTypes) {
        if (CollectionUtils.isEmpty(ledgerEntryTypes)) {
            return List.of();
        }

        return ledgerEntryTypes.stream()
            .map(LedgerEntryFromProtoMapper::map)
            .toList();
    }

    private static boolean mapBool(Boolean bool) {
        return bool != null && bool;
    }

    public static ZonedDateTime dt(Metadata metadata) {
        if (metadata == null) {
            return null;
        }

        String updatedAt = metadata.getUpdatedAt();
        return DateUtils.isoUtcTimeToZonedDateTime(updatedAt);
    }

    private static ZonedDateTime dt(String epochMillis) {
        if (isBlank(epochMillis)) {
            return null;
        }

        return DateUtils.epochMillisToZonedDateTime(epochMillis);
    }
}
