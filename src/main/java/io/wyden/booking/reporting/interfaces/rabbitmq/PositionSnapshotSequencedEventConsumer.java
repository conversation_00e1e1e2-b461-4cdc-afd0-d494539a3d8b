package io.wyden.booking.reporting.interfaces.rabbitmq;

import io.wyden.booking.reporting.application.position.PositionCommandService;
import io.wyden.booking.reporting.application.recovery.PositionSnapshotGapRecoveryService;
import io.wyden.booking.reporting.domain.tracking.EventTrackingRepository;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.published.booking.PositionSnapshot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PositionSnapshotSequencedEventConsumer extends AbstractSequencedEventConsumer<PositionSnapshot> {

    private static final Logger LOGGER = LoggerFactory.getLogger(PositionSnapshotSequencedEventConsumer.class);
    private static final String EVENT_TYPE = "position_snapshot";
    
    private final RabbitQueue<PositionSnapshot> positionSnapshotRabbitQueue;
    private final PositionCommandService positionCommandService;
    private final PositionSnapshotGapRecoveryService gapRecoveryService;

    public PositionSnapshotSequencedEventConsumer(RabbitIntegrator rabbitIntegrator,
                                                  RabbitExchange<PositionSnapshot> positionPnlCalculatedExchange,
                                                  @Value("${rabbitmq.booking-reporting-queue.position-snapshot.name}") String queueName,
                                                  PositionCommandService positionCommandService,
                                                  PositionSnapshotGapRecoveryService gapRecoveryService,
                                                  EventTrackingRepository trackingRepository,
                                                  @Value("${position.snapshot.recovery.onstartup:false}") boolean isRecoveryRequired,
                                                  @Value("${position.snapshot.onerror.break:false}") boolean shouldBreakOnError) {
        super(rabbitIntegrator, trackingRepository, shouldBreakOnError);
        
        this.positionSnapshotRabbitQueue = declareQueue(queueName, positionPnlCalculatedExchange);
        this.positionCommandService = positionCommandService;
        this.gapRecoveryService = gapRecoveryService;

        // removing old queue
        rabbitIntegrator.tryDeleteQueue("booking.booking-reporting-queue.POSITION-SNAPSHOT");

        if (isRecoveryRequired) {
            performStartupRecovery();
            attachConsumer();
        } else {
            LOGGER.info("Startup recovery not required for PositionSnapshot events");
            attachConsumer();
        }
    }

    private void attachConsumer() {
        LOGGER.info("Attaching consumer to PositionSnapshot queue");
        positionSnapshotRabbitQueue.attachConsumer(PositionSnapshot.parser(), this);
    }

    @Override
    protected String getEventType() {
        return EVENT_TYPE;
    }

    @Override
    protected long getSequenceNumber(PositionSnapshot event) {
        return event.getSequenceNumber();
    }

    @Override
    protected boolean processEvent(PositionSnapshot positionSnapshot) {
        try {
            positionCommandService.handlePositionSnapshot(positionSnapshot);
            return true;
        } catch (Exception e) {
            LOGGER.error("Failed to process PositionSnapshot event {}", positionSnapshot, e);
            return false;
        }
    }

    @Override
    protected List<PositionSnapshot> recoverAllMissingEvents(long lastProcessedSequence) {
        return gapRecoveryService.recoverAllMissingEvents(lastProcessedSequence);
    }

    @Override
    protected List<PositionSnapshot> recoverGap(long expectedSequence, long receivedSequence) {
        return gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
    }

    @Override
    protected boolean ensureNoMissingEvents(long inboundSequenceNumber, long lastProcessedSequence) {
        long expectedSequence = lastProcessedSequence + 1;

        if (inboundSequenceNumber < lastProcessedSequence) {
            LOGGER.warn("Received already processed sequence number: {}, current: {}. Skipping processing", inboundSequenceNumber, lastProcessedSequence);
            return false;
        }

        if (inboundSequenceNumber == expectedSequence || inboundSequenceNumber == lastProcessedSequence) {
            // Same sequence number or expected next sequence - continue processing
            return true;
        }

        // inboundSequenceNumber > expectedSequence:
        LOGGER.warn("Sequence gap detected. Expected: {}, Received: {}", expectedSequence, inboundSequenceNumber);
        List<PositionSnapshot> gapRecovered = recoverGap(expectedSequence, inboundSequenceNumber);
        LOGGER.info("Recovered {} events before realtime event", gapRecovered.size());
        processRecoveredEvents(gapRecovered);

        return true;
    }

    @Override
    protected void updateLastProcessedSequence(long sequenceNumber) {
        // Only update if the sequence number is actually newer
        if (sequenceNumber > lastProcessedSeqNum) {
            LOGGER.debug("Updating sequence number from {} to {}", lastProcessedSeqNum, sequenceNumber);
            super.updateLastProcessedSequence(sequenceNumber);
        } else {
            LOGGER.debug("Keeping sequence number at {} (incoming: {})", lastProcessedSeqNum, sequenceNumber);
        }
    }
}
