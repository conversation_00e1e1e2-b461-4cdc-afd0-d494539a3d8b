package io.wyden.booking.reporting.interfaces.rest;

import io.wyden.booking.reporting.domain.transaction.TransactionType;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;

public record TransactionIdentifiersSearch(
    Collection<String> symbol,
    Collection<String> accountId,
    Collection<String> portfolio,
    RequestModel.PortfolioType portfolioType,
    RequestModel.AccountType accountType,
    RequestModel.WalletType walletType,
    Collection<TransactionType> transactionType,
    String clientId
) {

    public TransactionIdentifiersSearch {
        if (symbol == null) symbol = Collections.emptyList();
        if (accountId == null) accountId = Collections.emptyList();
        if (portfolio == null) portfolio = Collections.emptyList();
        if (transactionType == null) transactionType = Collections.emptyList();
        if (clientId == null) clientId = "";
    }

    public static SimpleTransactionSearchBuilder builder() {
        return new SimpleTransactionSearchBuilder();
    }

    public SimpleTransactionSearchBuilder toBuilder() {
        return new SimpleTransactionSearchBuilder()
            .symbol(this.symbol)
            .accountId(this.accountId)
            .portfolio(this.portfolio)
            .portfolioType(this.portfolioType)
            .accountType(this.accountType)
            .walletType(this.walletType)
            .transactionType(this.transactionType)
            .clientId(this.clientId);
    }

    // Convenience methods
    public TransactionIdentifiersSearch withSymbol(String... symbols) {
        return withSymbol(Arrays.asList(symbols));
    }

    public TransactionIdentifiersSearch withSymbol(Collection<String> symbols) {
        return new TransactionIdentifiersSearch(symbols, accountId, portfolio, portfolioType, accountType, walletType, transactionType, clientId);
    }

    public TransactionIdentifiersSearch withAccountId(String... accounts) {
        return withAccountId(Arrays.asList(accounts));
    }

    public TransactionIdentifiersSearch withAccountId(Collection<String> accounts) {
        return new TransactionIdentifiersSearch(symbol, accounts, portfolio, portfolioType, accountType, walletType, transactionType, clientId);
    }

    public TransactionIdentifiersSearch withPortfolio(String... portfolios) {
        return withPortfolio(Arrays.asList(portfolios));
    }

    public TransactionIdentifiersSearch withPortfolio(Collection<String> portfolios) {
        return new TransactionIdentifiersSearch(symbol, accountId, portfolios, portfolioType, accountType, walletType, transactionType, clientId);
    }

    public TransactionIdentifiersSearch withPortfolioType(RequestModel.PortfolioType portfolioType) {
        return new TransactionIdentifiersSearch(symbol, accountId, portfolio, portfolioType, accountType, walletType, transactionType, clientId);
    }

    public TransactionIdentifiersSearch withAccountType(RequestModel.AccountType accountType) {
        return new TransactionIdentifiersSearch(symbol, accountId, portfolio, portfolioType, accountType, walletType, transactionType, clientId);
    }

    public TransactionIdentifiersSearch withWalletType(RequestModel.WalletType walletType) {
        return new TransactionIdentifiersSearch(symbol, accountId, portfolio, portfolioType, accountType, walletType, transactionType, clientId);
    }

    public TransactionIdentifiersSearch withTransactionType(TransactionType... types) {
        return withTransactionType(Arrays.asList(types));
    }

    public TransactionIdentifiersSearch withTransactionType(Collection<TransactionType> types) {
        return new TransactionIdentifiersSearch(symbol, accountId, portfolio, portfolioType, accountType, walletType, types, clientId);
    }

    public TransactionIdentifiersSearch withClientId(String clientId) {
        return new TransactionIdentifiersSearch(symbol, accountId, portfolio, portfolioType, accountType, walletType, transactionType, clientId);
    }

    // LedgerEntrySearchBuilder class
    public static class SimpleTransactionSearchBuilder {
        private Collection<String> symbol = Collections.emptyList();
        private Collection<String> accountId = Collections.emptyList();
        private Collection<String> portfolio = Collections.emptyList();
        private RequestModel.PortfolioType portfolioType;
        private RequestModel.AccountType accountType;
        private RequestModel.WalletType walletType;
        private Collection<TransactionType> transactionType = Collections.emptyList();
        private String clientId = "";

        SimpleTransactionSearchBuilder() {
        }

        public SimpleTransactionSearchBuilder symbol(String... symbols) {
            return symbol(Arrays.asList(symbols));
        }

        public SimpleTransactionSearchBuilder symbol(Collection<String> symbol) {
            this.symbol = symbol;
            return this;
        }

        public SimpleTransactionSearchBuilder accountId(String... accounts) {
            return accountId(Arrays.asList(accounts));
        }

        public SimpleTransactionSearchBuilder accountId(Collection<String> accountId) {
            this.accountId = accountId;
            return this;
        }

        public SimpleTransactionSearchBuilder portfolio(String... portfolios) {
            return portfolio(Arrays.asList(portfolios));
        }

        public SimpleTransactionSearchBuilder portfolio(Collection<String> portfolio) {
            this.portfolio = portfolio;
            return this;
        }

        public SimpleTransactionSearchBuilder portfolioType(RequestModel.PortfolioType portfolioType) {
            this.portfolioType = portfolioType;
            return this;
        }

        public SimpleTransactionSearchBuilder accountType(RequestModel.AccountType accountType) {
            this.accountType = accountType;
            return this;
        }

        public SimpleTransactionSearchBuilder walletType(RequestModel.WalletType walletType) {
            this.walletType = walletType;
            return this;
        }

        public SimpleTransactionSearchBuilder transactionType(TransactionType... types) {
            return transactionType(Arrays.asList(types));
        }

        public SimpleTransactionSearchBuilder transactionType(Collection<TransactionType> transactionType) {
            this.transactionType = transactionType;
            return this;
        }

        public SimpleTransactionSearchBuilder clientId(String clientId) {
            this.clientId = clientId;
            return this;
        }

        public TransactionIdentifiersSearch build() {
            return new TransactionIdentifiersSearch(symbol, accountId, portfolio, portfolioType, accountType, walletType, transactionType, clientId);
        }
    }
}