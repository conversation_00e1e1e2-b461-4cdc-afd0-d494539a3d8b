package io.wyden.booking.reporting.interfaces.rest;

import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntryType;
import io.wyden.booking.reporting.domain.transaction.TransactionType;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.jetbrains.annotations.NotNull;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static java.util.List.of;

public class RequestModel {

    public enum SortingOrder {
        ASC,
        DESC
    }

    public enum PortfolioType {
        VOSTRO,
        NOSTRO,
        ALL,
        NONE
    }

    public enum AccountType {
        WALLET,
        EXCHANGE,
        CUSTODY,
        CLOB,
        ALL,
        NONE
    }

    public enum WalletType {
        VOSTRO,
        NOSTRO,
        ALL,
        NONE
    }

    public interface SearchRequest {
        String clientId();

        Collection<String> symbol();

        Collection<String> currency();

        Collection<String> accountId();

        Collection<String> portfolio();

        PortfolioType portfolioType();

        AccountType accountType();

        WalletType walletType();

        Integer first();

        String after();

        SortingOrder sortingOrder();

        SearchRequest withPortfolio(Collection<String> portfolio);

        SearchRequest withPortfolioType(PortfolioType portfolioType);

        SearchRequest withAccount(Collection<String> account);

        SearchRequest withAccountType(AccountType accountType);

        SearchRequest withWalletType(WalletType walletType);

        SearchRequest withReference(Collection<String> reference);
    }

    public record AuthorizedPositionSearch(PositionSearch searchRequest, SimpleSearchRequest.AuthorizedSearchRequest authorizedSearchRequest) {
    }

    public record PositionSearch(
        Collection<String> symbol,
        Collection<String> currency,
        Collection<String> accountId,
        Collection<String> portfolio,
        PortfolioType portfolioType,
        AccountType accountType,
        WalletType walletType,
        // portfolio OR account
        Collection<String> reference,
        String orderId,
        String clientId,
        Integer first,
        String after,
        SortingOrder sortingOrder
    ) implements SearchRequest {
        public PositionSearch {
            if (symbol == null) symbol = of();
            if (currency == null) currency = of();
            if (accountId == null) accountId = of();
            if (portfolio == null) portfolio = of();
            if (reference == null) reference = of();
            if (sortingOrder == null) sortingOrder = SortingOrder.ASC;
        }

        public SimpleSearchRequest toSearchRequest() {
            return new SimpleSearchRequest(
                accountId,
                portfolio,
                portfolioType,
                accountType,
                walletType,
                clientId
            );
        }

        public static PositionSearch symbolAccountAndPortfolio(Collection<String> symbol, Collection<String> account, Collection<String> portfolio) {
            return new PositionSearch(of(), symbol, account, portfolio, null, null, null, null, null, null, null, null, null);
        }

        public static PositionSearch all() {
            return symbolAccountAndPortfolio(of(), of(), of());
        }

        public PositionSearch withPortfolio(Collection<String> portfolio) {
            return new PositionSearch(symbol, currency, accountId, portfolio, portfolioType, accountType, walletType, reference, orderId, clientId, first, after, sortingOrder);
        }

        public PositionSearch withPortfolioType(PortfolioType portfolioType) {
            return new PositionSearch(symbol, currency, accountId, portfolio, portfolioType, accountType, walletType, reference, orderId, clientId, first, after, sortingOrder);
        }

        public PositionSearch withAccount(Collection<String> venueAccount) {
            return new PositionSearch(symbol, currency, venueAccount, portfolio, portfolioType, accountType, walletType, reference, orderId, clientId, first, after, sortingOrder);
        }

        @Override
        public PositionSearch withAccountType(AccountType accountType) {
            return new PositionSearch(symbol, currency, accountId, portfolio, portfolioType, accountType, walletType, reference, orderId, clientId, first, after, sortingOrder);
        }

        @Override
        public PositionSearch withWalletType(WalletType walletType) {
            return new PositionSearch(symbol, currency, accountId, portfolio, portfolioType, accountType, walletType, reference, orderId, clientId, first, after, sortingOrder);
        }

        public PositionSearch withReference(Collection<String> reference) {
            return new PositionSearch(symbol, currency, accountId, portfolio, portfolioType, accountType, walletType, reference, orderId, clientId, first, after, sortingOrder);
        }

        public PositionSearch withClientId(String clientId) {
            return new PositionSearch(symbol, currency, accountId, portfolio, portfolioType, accountType, walletType, reference, orderId, clientId, first, after, sortingOrder);
        }

        /**
         * Creates a new builder for PositionSearch
         *
         * @return A new builder instance
         */
        public static PositionSearchBuilder builder() {
            return new PositionSearchBuilder();
        }

        /**
         * Creates a builder instance pre-populated with this object's current data
         *
         * @return builder with current object's data
         */
        public PositionSearchBuilder toBuilder() {
            return new PositionSearchBuilder()
                .symbol(symbol)
                .currency(currency)
                .accountId(accountId)
                .portfolio(portfolio)
                .portfolioType(portfolioType)
                .accountType(accountType)
                .walletType(walletType)
                .reference(reference)
                .clientId(clientId)
                .first(first)
                .after(after)
                .sortingOrder(sortingOrder)
                .orderId(orderId);
        }

        /**
         * PositionSearchBuilder for PositionSearch
         */
        public static class PositionSearchBuilder {
            private Collection<String> symbol = of();
            private Collection<String> currency = of();
            private Collection<String> accountId = of();
            private Collection<String> portfolio = of();
            private PortfolioType portfolioType;
            private AccountType accountType;
            private WalletType walletType;
            private Collection<String> reference = of();
            private String clientId;
            private Integer first;
            private String after;
            private SortingOrder sortingOrder = SortingOrder.ASC;
            private String orderId;

            /**
             * Set the list of asset symbols to filter by
             */
            public PositionSearchBuilder symbol(String... symbol) {
                this.symbol = symbol != null ? of(symbol) : of();
                return this;
            }

            /**
             * Set the list of asset symbols to filter by
             */
            public PositionSearchBuilder symbol(Collection<String> symbol) {
                this.symbol = symbol != null ? symbol : of();
                return this;
            }

            /**
             * Add a single symbol to the existing symbol collection
             */
            public PositionSearchBuilder addSymbol(String symbol) {
                if (this.symbol == null || this.symbol.isEmpty()) {
                    this.symbol = of(symbol);
                } else {
                    List<String> symbols = new ArrayList<>(this.symbol);
                    symbols.add(symbol);
                    this.symbol = symbols;
                }
                return this;
            }

            /**
             * Set the list of currencies to filter by
             */
            public PositionSearchBuilder currency(String... currency) {
                this.currency = currency != null ? of(currency) : of();
                return this;
            }

            /**
             * Set the list of currencies to filter by
             */
            public PositionSearchBuilder currency(Collection<String> currency) {
                this.currency = currency != null ? currency : of();
                return this;
            }

            /**
             * Add a single currency to the existing currency collection
             */
            public PositionSearchBuilder addCurrency(String currency) {
                if (this.currency == null || this.currency.isEmpty()) {
                    this.currency = of(currency);
                } else {
                    List<String> currencies = new ArrayList<>(this.currency);
                    currencies.add(currency);
                    this.currency = currencies;
                }
                return this;
            }

            /**
             * Set the list of account IDs to filter by
             */
            public PositionSearchBuilder accountId(Collection<String> accountId) {
                this.accountId = accountId != null ? accountId : of();
                return this;
            }

            /**
             * Set the list of account IDs to filter by
             */
            public PositionSearchBuilder accountId(String... accountId) {
                this.accountId = accountId != null ? of(accountId) : of();
                return this;
            }

            /**
             * Add a single account ID to the existing account collection
             */
            public PositionSearchBuilder addAccountId(String accountId) {
                if (this.accountId == null || this.accountId.isEmpty()) {
                    this.accountId = of(accountId);
                } else {
                    List<String> accounts = new ArrayList<>(this.accountId);
                    accounts.add(accountId);
                    this.accountId = accounts;
                }
                return this;
            }

            /**
             * Set the list of portfolio IDs to filter by
             */
            public PositionSearchBuilder portfolio(Collection<String> portfolio) {
                this.portfolio = portfolio != null ? portfolio : of();
                return this;
            }

            /**
             * Set the list of portfolio IDs to filter by
             */
            public PositionSearchBuilder portfolio(String... portfolio) {
                this.portfolio = portfolio != null ? of(portfolio) : of();
                return this;
            }

            /**
             * Add a single portfolio ID to the existing portfolio collection
             */
            public PositionSearchBuilder addPortfolioId(String portfolioId) {
                if (this.portfolio == null || this.portfolio.isEmpty()) {
                    this.portfolio = of(portfolioId);
                } else {
                    List<String> portfolios = new ArrayList<>(this.portfolio);
                    portfolios.add(portfolioId);
                    this.portfolio = portfolios;
                }
                return this;
            }

            /**
             * Set the portfolio type filter
             */
            public PositionSearchBuilder portfolioType(PortfolioType portfolioType) {
                this.portfolioType = portfolioType;
                return this;
            }

            /**
             * Set the account type filter
             */
            public PositionSearchBuilder accountType(AccountType accountType) {
                this.accountType = accountType;
                return this;
            }

            /**
             * Set the wallet type filter
             */
            public PositionSearchBuilder walletType(WalletType walletType) {
                this.walletType = walletType;
                return this;
            }

            /**
             * Set the list of reference IDs to filter by
             */
            public PositionSearchBuilder reference(Collection<String> reference) {
                this.reference = reference != null ? reference : of();
                return this;
            }

            /**
             * Set the list of reference IDs to filter by
             */
            public PositionSearchBuilder reference(String... reference) {
                this.reference = reference != null ? of(reference) : of();
                return this;
            }

            /**
             * Set the client ID to filter by
             */
            public PositionSearchBuilder clientId(String clientId) {
                this.clientId = clientId;
                return this;
            }

            /**
             * Set the page size
             */
            public PositionSearchBuilder first(Integer first) {
                this.first = first;
                return this;
            }

            /**
             * Set the pagination cursor
             */
            public PositionSearchBuilder after(String after) {
                this.after = after;
                return this;
            }

            /**
             * Set the sorting order
             */
            public PositionSearchBuilder sortingOrder(SortingOrder sortingOrder) {
                this.sortingOrder = sortingOrder != null ? sortingOrder : SortingOrder.DESC;
                return this;
            }

            /**
             * Set the order ID
             */
            public PositionSearchBuilder orderId(String orderId) {
                this.orderId = orderId;
                return this;
            }

            /**
             * Builds a new PositionSearch instance with the configured values
             *
             * @return new PositionSearch instance
             */
            public PositionSearch build() {
                return new PositionSearch(
                    symbol,
                    currency,
                    accountId,
                    portfolio,
                    portfolioType,
                    accountType,
                    walletType,
                    reference,
                    orderId,
                    clientId,
                    first,
                    after,
                    sortingOrder
                );
            }
        }
    }

    public record AuthorizedLedgerEntrySearch(LedgerEntrySearch searchRequest, SimpleSearchRequest.AuthorizedSearchRequest authorizedSearchRequest) {
    }

    public record LedgerEntrySearch(
        Collection<String> symbol,
        Collection<String> currency,
        Collection<String> accountId,
        Collection<String> portfolio,
        PortfolioType portfolioType,
        AccountType accountType,
        WalletType walletType,
        // portfolio OR account
        Collection<String> reference,
        Collection<LedgerEntryType> ledgerEntryType,
        String transactionId,
        String orderId,
        String clientId,
        ZonedDateTime from,
        ZonedDateTime to,
        Integer first,
        String after,
        SortingOrder sortingOrder
    ) implements SearchRequest {
        public LedgerEntrySearch {
            if (symbol == null) symbol = of();
            if (currency == null) currency = of();
            if (accountId == null) accountId = of();
            if (portfolio == null) portfolio = of();
            if (reference == null) reference = of();
            if (ledgerEntryType == null) ledgerEntryType = of();
            if (sortingOrder == null) sortingOrder = SortingOrder.DESC;
        }

        public SimpleSearchRequest toSearchRequest() {
            return new SimpleSearchRequest(
                accountId,
                portfolio,
                portfolioType,
                accountType,
                walletType,
                clientId
            );
        }

        public static LedgerEntrySearch all() {
            return LedgerEntrySearch.builder()
                .build();
        }

        public LedgerEntrySearch withClientId(String clientId) {
            return toBuilder()
                .clientId(clientId)
                .build();
        }

        public LedgerEntrySearch withPortfolio(Collection<String> portfolio) {
            return toBuilder()
                .portfolio(portfolio)
                .build();
        }

        public LedgerEntrySearch withAccount(Collection<String> accountId) {
            return toBuilder()
                .accountId(accountId)
                .build();
        }

        @Override
        public LedgerEntrySearch withAccountType(AccountType accountType) {
            return toBuilder()
                .accountType(accountType)
                .build();
        }

        @Override
        public LedgerEntrySearch withWalletType(WalletType walletType) {
            return toBuilder()
                .walletType(walletType)
                .build();
        }

        public LedgerEntrySearch withPortfolioType(PortfolioType portfolioType) {
            return toBuilder()
                .portfolioType(portfolioType)
                .build();
        }

        public LedgerEntrySearch withReference(Collection<String> reference) {
            return toBuilder()
                .accountId(reference)
                .portfolio(reference)
                .build();
        }

        /**
         * Creates a new builder for LedgerEntrySearch
         *
         * @return A new builder instance
         */
        public static LedgerEntrySearchBuilder builder() {
            return new LedgerEntrySearchBuilder();
        }

        /**
         * Creates a builder instance pre-populated with this object's current data
         *
         * @return builder with current object's data
         */
        public LedgerEntrySearchBuilder toBuilder() {
            return new LedgerEntrySearchBuilder()
                .symbol(symbol)
                .currency(currency)
                .accountId(accountId)
                .portfolio(portfolio)
                .portfolioType(portfolioType)
                .accountType(accountType)
                .walletType(walletType)
                .reference(reference)
                .ledgerEntryType(ledgerEntryType)
                .transactionId(transactionId)
                .orderId(orderId)
                .clientId(clientId)
                .from(from)
                .to(to)
                .first(first)
                .after(after)
                .sortingOrder(sortingOrder);
        }

        /**
         * LedgerEntrySearchBuilder for LedgerEntrySearch
         */
        public static class LedgerEntrySearchBuilder {
            private Collection<String> symbol = of();
            private Collection<String> currency = of();
            private Collection<String> accountId = of();
            private Collection<String> portfolio = of();
            private PortfolioType portfolioType;
            private AccountType accountType;
            private WalletType walletType;
            private Collection<String> reference = of();
            private Collection<LedgerEntryType> ledgerEntryType = of();
            private String transactionId;
            private String orderId;
            private String clientId;
            private ZonedDateTime from;
            private ZonedDateTime to;
            private Integer first;
            private String after;
            private SortingOrder sortingOrder = SortingOrder.DESC;

            /**
             * Set the list of asset symbols to filter by
             */
            public LedgerEntrySearchBuilder symbol(String... symbol) {
                this.symbol = symbol != null ? of(symbol) : of();
                return this;
            }

            /**
             * Set the list of asset symbols to filter by
             */
            public LedgerEntrySearchBuilder symbol(Collection<String> symbol) {
                this.symbol = symbol != null ? symbol : of();
                return this;
            }

            /**
             * Set the list of currencies to filter by
             */
            public LedgerEntrySearchBuilder currency(String... currency) {
                this.currency = currency != null ? of(currency) : of();
                return this;
            }

            /**
             * Set the list of currencies to filter by
             */
            public LedgerEntrySearchBuilder currency(Collection<String> currency) {
                this.currency = currency != null ? currency : of();
                return this;
            }

            /**
             * Set the list of account IDs to filter by
             */
            public LedgerEntrySearchBuilder accountId(Collection<String> accountId) {
                this.accountId = accountId != null ? accountId : of();
                return this;
            }

            /**
             * Set the list of account IDs to filter by
             */
            public LedgerEntrySearchBuilder accountId(String... accountId) {
                this.accountId = accountId != null ? of(accountId) : of();
                return this;
            }

            /**
             * Set the list of portfolio IDs to filter by
             */
            public LedgerEntrySearchBuilder portfolio(Collection<String> portfolio) {
                this.portfolio = portfolio != null ? portfolio : of();
                return this;
            }

            /**
             * Set the list of portfolio IDs to filter by
             */
            public LedgerEntrySearchBuilder portfolio(String... portfolio) {
                this.portfolio = portfolio != null ? of(portfolio) : of();
                return this;
            }

            /**
             * Set the portfolio type filter
             */
            public LedgerEntrySearchBuilder portfolioType(PortfolioType portfolioType) {
                this.portfolioType = portfolioType;
                return this;
            }

            /**
             * Set the account type filter
             */
            public LedgerEntrySearchBuilder accountType(AccountType accountType) {
                this.accountType = accountType;
                return this;
            }

            /**
             * Set the wallet type filter
             */
            public LedgerEntrySearchBuilder walletType(WalletType walletType) {
                this.walletType = walletType;
                return this;
            }

            /**
             * Set the list of reference IDs to filter by
             */
            public LedgerEntrySearchBuilder reference(Collection<String> reference) {
                this.reference = reference != null ? reference : of();
                return this;
            }

            /**
             * Set the list of ledger entry types to filter by
             */
            public LedgerEntrySearchBuilder ledgerEntryType(LedgerEntryType... ledgerEntryType) {
                this.ledgerEntryType = ledgerEntryType != null ? of(ledgerEntryType) : of();
                return this;
            }

            /**
             * Set the list of ledger entry types to filter by
             */
            public LedgerEntrySearchBuilder ledgerEntryType(Collection<LedgerEntryType> ledgerEntryType) {
                this.ledgerEntryType = ledgerEntryType != null ? ledgerEntryType : of();
                return this;
            }

            /**
             * Set the transaction ID to filter by
             */
            public LedgerEntrySearchBuilder transactionId(String transactionId) {
                this.transactionId = transactionId;
                return this;
            }

            /**
             * Set the order ID to filter by
             */
            public LedgerEntrySearchBuilder orderId(String orderId) {
                this.orderId = orderId;
                return this;
            }

            /**
             * Set the client ID to filter by
             */
            public LedgerEntrySearchBuilder clientId(String clientId) {
                this.clientId = clientId;
                return this;
            }

            /**
             * Set the from date filter
             */
            public LedgerEntrySearchBuilder from(ZonedDateTime from) {
                this.from = from;
                return this;
            }

            /**
             * Set the to date filter
             */
            public LedgerEntrySearchBuilder to(ZonedDateTime to) {
                this.to = to;
                return this;
            }

            public LedgerEntrySearchBuilder dateRange(ZonedDateTime from, ZonedDateTime to) {
                this.from = from;
                this.to = to;
                return this;
            }

            /**
             * Set the page size
             */
            public LedgerEntrySearchBuilder first(Integer first) {
                this.first = first;
                return this;
            }

            /**
             * Set the pagination cursor
             */
            public LedgerEntrySearchBuilder after(String after) {
                this.after = after;
                return this;
            }

            /**
             * Set the sorting order
             */
            public LedgerEntrySearchBuilder sortingOrder(SortingOrder sortingOrder) {
                this.sortingOrder = sortingOrder != null ? sortingOrder : SortingOrder.DESC;
                return this;
            }

            /**
             * Builds a new LedgerEntrySearch instance with the configured values
             *
             * @return new LedgerEntrySearch instance
             */
            public LedgerEntrySearch build() {
                return new LedgerEntrySearch(
                    symbol,
                    currency,
                    accountId,
                    portfolio,
                    portfolioType,
                    accountType,
                    walletType,
                    reference,
                    ledgerEntryType,
                    transactionId,
                    orderId,
                    clientId,
                    from,
                    to,
                    first,
                    after,
                    sortingOrder
                );
            }
        }
    }

    public record AuthorizedTransactionSearch(TransactionSearch searchRequest, SimpleSearchRequest.AuthorizedSearchRequest authorizedSearchRequest) {
    }

    public record TransactionSearch(
        Collection<String> symbol,
        Collection<String> accountId,
        Collection<String> portfolio,
        PortfolioType portfolioType,
        AccountType accountType,
        WalletType walletType,
        Collection<String> currency,
        Collection<TransactionType> transactionType,
        Collection<String> uuids,
        String orderId,
        String parentOrderId,
        String rootOrderId,
        String executionId,
        String venueExecutionId,
        String underlyingExecutionId,
        String rootExecutionId,
        String reservationRef,
        String clientId,
        ZonedDateTime from,
        ZonedDateTime to,
        ZonedDateTime settlementFrom,
        ZonedDateTime settlementTo,
        Boolean settled,
        Integer first,
        String after,
        SortingOrder sortingOrder
    ) implements SearchRequest {

        // Define constants for default values
        private static final int DEFAULT_PAGE_SIZE = 100;

        // Compact constructor with default values
        public TransactionSearch {
            // Default empty collections
            if (symbol == null) symbol = Collections.emptyList();
            if (accountId == null) accountId = Collections.emptyList();
            if (portfolio == null) portfolio = Collections.emptyList();
            if (currency == null) currency = Collections.emptyList();
            if (transactionType == null) transactionType = Collections.emptyList();
            if (uuids == null) uuids = Collections.emptyList();

            // Default empty strings for string fields
            if (orderId == null) orderId = "";
            if (parentOrderId == null) parentOrderId = "";
            if (rootOrderId == null) rootOrderId = "";
            if (executionId == null) executionId = "";
            if (venueExecutionId == null) venueExecutionId = "";
            if (underlyingExecutionId == null) underlyingExecutionId = "";
            if (rootExecutionId == null) rootExecutionId = "";
            if (reservationRef == null) reservationRef = "";
            if (clientId == null) clientId = "";
            if (after == null) after = "";

            // Default value for pagination
            if (first == null) first = DEFAULT_PAGE_SIZE;

            // Default sorting order
            if (sortingOrder == null) sortingOrder = SortingOrder.DESC;
        }

        public SimpleSearchRequest toSearchRequest() {
            return new SimpleSearchRequest(
                accountId,
                portfolio,
                portfolioType,
                accountType,
                walletType,
                clientId
            );
        }

        public static TransactionSearchBuilder builder() {
            return new TransactionSearchBuilder();
        }

        public TransactionSearchBuilder toBuilder() {
            return new TransactionSearchBuilder()
                .symbol(symbol)
                .accountId(accountId)
                .portfolio(portfolio)
                .portfolioType(portfolioType)
                .accountType(accountType)
                .walletType(walletType)
                .currency(currency)
                .transactionType(transactionType)
                .uuids(uuids)
                .orderId(orderId)
                .parentOrderId(parentOrderId)
                .rootOrderId(rootOrderId)
                .executionId(executionId)
                .venueExecutionId(venueExecutionId)
                .underlyingExecutionId(underlyingExecutionId)
                .rootExecutionId(rootExecutionId)
                .reservationRef(reservationRef)
                .clientId(clientId)
                .from(from)
                .to(to)
                .settlementFrom(settlementFrom)
                .settlementTo(settlementTo)
                .settled(settled)
                .first(first)
                .after(after)
                .sortingOrder(sortingOrder);
        }

        public TransactionSearch withAccount(String... venueAccount) {
            return toBuilder()
                .accountId(venueAccount)
                .build();
        }

        public TransactionSearch withAccount(Collection<String> venueAccount) {
            return toBuilder()
                .accountId(venueAccount)
                .build();
        }

        @Override
        public TransactionSearch withAccountType(AccountType accountType) {
            return toBuilder()
                .accountType(accountType)
                .build();
        }

        @Override
        public TransactionSearch withWalletType(WalletType walletType) {
            return toBuilder()
                .walletType(walletType)
                .build();
        }

        @Override
        public TransactionSearch withReference(Collection<String> ignored) {
            return this;
        }

        public TransactionSearch withPortfolio(String... portfolio) {
            return toBuilder()
                .portfolio(portfolio)
                .build();
        }

        public TransactionSearch withPortfolio(Collection<String> portfolio) {
            return toBuilder()
                .portfolio(portfolio)
                .build();
        }

        public TransactionSearch withClientId(String clientId) {
            return toBuilder()
                .clientId(clientId)
                .build();
        }

        public TransactionSearch withPortfolioType(PortfolioType portfolioType) {
            return toBuilder()
                .portfolioType(portfolioType)
                .build();
        }
    }

    public static class TransactionSearchBuilder {
        private Collection<String> symbol = Collections.emptyList();
        private Collection<String> accountId = Collections.emptyList();
        private Collection<String> portfolio = Collections.emptyList();
        private PortfolioType portfolioType;
        private AccountType accountType;
        private WalletType walletType;
        private Collection<String> currency = Collections.emptyList();
        private Collection<TransactionType> transactionType = Collections.emptyList();
        private Collection<String> uuids = Collections.emptyList();
        private String orderId = "";
        private String parentOrderId = "";
        private String rootOrderId = "";
        private String executionId = "";
        private String venueExecutionId = "";
        private String underlyingExecutionId = "";
        private String rootExecutionId = "";
        private String reservationRef = "";
        private String clientId = "";
        private ZonedDateTime from;
        private ZonedDateTime to;
        private ZonedDateTime settlementFrom;
        private ZonedDateTime settlementTo;
        private Boolean settled;
        private Integer first;
        private String after = "";
        private SortingOrder sortingOrder = SortingOrder.DESC;

        public TransactionSearchBuilder symbol(String... symbol) {
            this.symbol = Arrays.asList(symbol);
            return this;
        }

        public TransactionSearchBuilder symbol(Collection<String> symbol) {
            this.symbol = symbol;
            return this;
        }

        public TransactionSearchBuilder accountId(String... accountId) {
            this.accountId = Arrays.asList(accountId);
            return this;
        }

        public TransactionSearchBuilder accountId(Collection<String> accountId) {
            this.accountId = accountId;
            return this;
        }

        public TransactionSearchBuilder portfolio(String... portfolio) {
            this.portfolio = Arrays.asList(portfolio);
            return this;
        }

        public TransactionSearchBuilder portfolio(Collection<String> portfolio) {
            this.portfolio = portfolio;
            return this;
        }

        public TransactionSearchBuilder portfolioType(PortfolioType portfolioType) {
            this.portfolioType = portfolioType;
            return this;
        }

        public TransactionSearchBuilder accountType(AccountType accountType) {
            this.accountType = accountType;
            return this;
        }

        public TransactionSearchBuilder walletType(WalletType walletType) {
            this.walletType = walletType;
            return this;
        }

        public TransactionSearchBuilder currency(String... currency) {
            this.currency = Arrays.asList(currency);
            return this;
        }

        public TransactionSearchBuilder currency(Collection<String> currency) {
            this.currency = currency;
            return this;
        }

        public TransactionSearchBuilder transactionType(TransactionType... transactionType) {
            this.transactionType = Arrays.asList(transactionType);
            return this;
        }

        public TransactionSearchBuilder transactionType(Collection<TransactionType> transactionType) {
            this.transactionType = transactionType;
            return this;
        }

        public TransactionSearchBuilder uuids(String... uuids) {
            this.uuids = Arrays.asList(uuids);
            return this;
        }

        public TransactionSearchBuilder uuids(Collection<String> uuids) {
            this.uuids = uuids;
            return this;
        }

        public TransactionSearchBuilder orderId(String orderId) {
            this.orderId = orderId;
            return this;
        }

        public TransactionSearchBuilder parentOrderId(String parentOrderId) {
            this.parentOrderId = parentOrderId;
            return this;
        }

        public TransactionSearchBuilder rootOrderId(String rootOrderId) {
            this.rootOrderId = rootOrderId;
            return this;
        }

        public TransactionSearchBuilder executionId(String executionId) {
            this.executionId = executionId;
            return this;
        }

        public TransactionSearchBuilder venueExecutionId(String venueExecutionId) {
            this.venueExecutionId = venueExecutionId;
            return this;
        }

        public TransactionSearchBuilder underlyingExecutionId(String underlyingExecutionId) {
            this.underlyingExecutionId = underlyingExecutionId;
            return this;
        }

        public TransactionSearchBuilder rootExecutionId(String rootExecutionId) {
            this.rootExecutionId = rootExecutionId;
            return this;
        }

        public TransactionSearchBuilder reservationRef(String reservationRef) {
            this.reservationRef = reservationRef;
            return this;
        }

        public TransactionSearchBuilder clientId(String clientId) {
            this.clientId = clientId;
            return this;
        }

        public TransactionSearchBuilder from(ZonedDateTime from) {
            this.from = from;
            return this;
        }

        public TransactionSearchBuilder to(ZonedDateTime to) {
            this.to = to;
            return this;
        }

        public TransactionSearchBuilder dateRange(ZonedDateTime from, ZonedDateTime to) {
            this.from = from;
            this.to = to;
            return this;
        }

        public TransactionSearchBuilder settlementFrom(ZonedDateTime settlementFrom) {
            this.settlementFrom = settlementFrom;
            return this;
        }

        public TransactionSearchBuilder settlementTo(ZonedDateTime settlementTo) {
            this.settlementTo = settlementTo;
            return this;
        }

        public TransactionSearchBuilder settlementDateRange(ZonedDateTime settlementFrom, ZonedDateTime settlementTo) {
            this.settlementFrom = settlementFrom;
            this.settlementTo = settlementTo;
            return this;
        }

        public TransactionSearchBuilder settled(Boolean settled) {
            this.settled = settled;
            return this;
        }

        public TransactionSearchBuilder first(Integer first) {
            this.first = first;
            return this;
        }

        public TransactionSearchBuilder after(String after) {
            this.after = after;
            return this;
        }

        public TransactionSearchBuilder sortingOrder(SortingOrder sortingOrder) {
            this.sortingOrder = sortingOrder;
            return this;
        }

        public TransactionSearch build() {
            return new TransactionSearch(
                symbol,
                accountId,
                portfolio,
                portfolioType,
                accountType,
                walletType,
                currency,
                transactionType,
                uuids,
                orderId,
                parentOrderId,
                rootOrderId,
                executionId,
                venueExecutionId,
                underlyingExecutionId,
                rootExecutionId,
                reservationRef,
                clientId,
                from,
                to,
                settlementFrom,
                settlementTo,
                settled,
                first,
                after,
                sortingOrder
            );
        }

        public static TransactionSearchBuilder builder() {
            return new TransactionSearchBuilder();
        }
    }

    public record AuthorizedReservationSearch(ReservationSearch searchRequest, SimpleSearchRequest.AuthorizedSearchRequest authorizedSearchRequest) {
    }

    public record ReservationSearch(
        Collection<String> symbol,
        Collection<String> accountId,
        Collection<String> portfolio,
        PortfolioType portfolioType,
        AccountType accountType,
        WalletType walletType,
        // portfolio OR account
        Collection<String> reference,
        Collection<String> currency,
        Collection<TransactionType> transactionType,
        String reservationRef,
        String clientId,
        ZonedDateTime from,
        ZonedDateTime to,
        Integer first,
        String after,
        SortingOrder sortingOrder
    ) implements SearchRequest {

        public ReservationSearch {
            if (symbol == null) symbol = of();
            if (accountId == null) accountId = of();
            if (portfolio == null) portfolio = of();
            if (sortingOrder == null) sortingOrder = SortingOrder.DESC;
        }

        public SimpleSearchRequest toSearchRequest() {
            return new SimpleSearchRequest(
                accountId,
                portfolio,
                portfolioType,
                accountType,
                walletType,
                clientId
            );
        }

        public ReservationSearch withPortfolio(Collection<String> portfolios) {
            return this.toBuilder()
                .portfolio(portfolios)
                .build();
        }

        public ReservationSearch withPortfolioType(PortfolioType portfolioType) {
            return this.toBuilder()
                .portfolioType(portfolioType)
                .build();
        }

        public ReservationSearch withAccount(Collection<String> accounts) {
            return this.toBuilder()
                .accountId(accounts)
                .build();
        }

        @Override
        public ReservationSearch withAccountType(AccountType accountType) {
            return this.toBuilder()
                .accountType(accountType)
                .build();
        }

        @Override
        public ReservationSearch withWalletType(WalletType walletType) {
            return this.toBuilder()
                .walletType(walletType)
                .build();
        }

        public ReservationSearch withReference(Collection<String> references) {
            return new ReservationSearch(symbol, accountId, portfolio, portfolioType, accountType, walletType, references, currency, transactionType, reservationRef, clientId, from, to, first, after, sortingOrder);
        }

        /**
         * Creates a new builder instance
         */
        public static ReservationSearchBuilder builder() {
            return new ReservationSearchBuilder();
        }

        /**
         * Creates a builder pre-filled with current search data
         */
        public ReservationSearchBuilder toBuilder() {
            return new ReservationSearchBuilder()
                .symbol(this.symbol)
                .accountId(this.accountId)
                .portfolio(this.portfolio)
                .portfolioType(this.portfolioType)
                .accountType(this.accountType)
                .walletType(this.walletType)
                .reference(this.reference)
                .currency(this.currency)
                .transactionType(this.transactionType)
                .reservationRef(this.reservationRef)
                .clientId(this.clientId)
                .from(this.from)
                .to(this.to)
                .first(this.first)
                .after(this.after)
                .sortingOrder(this.sortingOrder);
        }

        @Override
        public @NotNull String toString() {
            return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("symbol", symbol)
                .append("accountId", accountId)
                .append("portfolio", portfolio)
                .append("portfolioType", portfolioType)
                .append("accountType", accountType)
                .append("walletType", walletType)
                .append("reference", reference)
                .append("currency", currency)
                .append("transactionType", transactionType)
                .append("reservationRef", reservationRef)
                .append("clientId", clientId)
                .append("from", from)
                .append("to", to)
                .append("first", first)
                .append("after", after)
                .append("sortingOrder", sortingOrder)
                .toString();
        }
    }

    public static class ReservationSearchBuilder {
        private Collection<String> symbol;
        private Collection<String> accountId;
        private Collection<String> portfolio;
        private PortfolioType portfolioType;
        private AccountType accountType;
        private WalletType walletType;
        private Collection<String> reference;
        private Collection<String> currency;
        private Collection<TransactionType> transactionType;
        private String reservationRef;
        private String clientId;
        private ZonedDateTime from;
        private ZonedDateTime to;
        private Integer first;
        private String after;
        private SortingOrder sortingOrder;

        public ReservationSearchBuilder() {
        }

        public ReservationSearchBuilder symbol(Collection<String> symbol) {
            this.symbol = symbol;
            return this;
        }

        public ReservationSearchBuilder symbol(String... symbols) {
            this.symbol = List.of(symbols);
            return this;
        }

        public ReservationSearchBuilder accountId(Collection<String> accountId) {
            this.accountId = accountId;
            return this;
        }

        public ReservationSearchBuilder accountId(String... accountIds) {
            this.accountId = List.of(accountIds);
            return this;
        }

        public ReservationSearchBuilder portfolio(Collection<String> portfolio) {
            this.portfolio = portfolio;
            return this;
        }

        public ReservationSearchBuilder portfolio(String... portfolios) {
            this.portfolio = List.of(portfolios);
            return this;
        }

        public ReservationSearchBuilder portfolioType(PortfolioType portfolioType) {
            this.portfolioType = portfolioType;
            return this;
        }

        public ReservationSearchBuilder accountType(AccountType accountType) {
            this.accountType = accountType;
            return this;
        }

        public ReservationSearchBuilder walletType(WalletType walletType) {
            this.walletType = walletType;
            return this;
        }

        public ReservationSearchBuilder reference(Collection<String> reference) {
            this.reference = reference;
            return this;
        }

        public ReservationSearchBuilder reference(String... references) {
            this.reference = List.of(references);
            return this;
        }

        public ReservationSearchBuilder currency(Collection<String> currency) {
            this.currency = currency;
            return this;
        }

        public ReservationSearchBuilder currency(String... currencies) {
            this.currency = List.of(currencies);
            return this;
        }

        public ReservationSearchBuilder transactionType(Collection<TransactionType> transactionType) {
            this.transactionType = transactionType;
            return this;
        }

        public ReservationSearchBuilder transactionType(TransactionType... transactionTypes) {
            this.transactionType = List.of(transactionTypes);
            return this;
        }

        public ReservationSearchBuilder reservationRef(String reservationRef) {
            this.reservationRef = reservationRef;
            return this;
        }

        public ReservationSearchBuilder clientId(String clientId) {
            this.clientId = clientId;
            return this;
        }

        public ReservationSearchBuilder from(ZonedDateTime from) {
            this.from = from;
            return this;
        }

        public ReservationSearchBuilder to(ZonedDateTime to) {
            this.to = to;
            return this;
        }

        public ReservationSearchBuilder dateRange(ZonedDateTime from, ZonedDateTime to) {
            this.from = from;
            this.to = to;
            return this;
        }

        public ReservationSearchBuilder first(Integer first) {
            this.first = first;
            return this;
        }

        public ReservationSearchBuilder after(String after) {
            this.after = after;
            return this;
        }

        public ReservationSearchBuilder sortingOrder(SortingOrder sortingOrder) {
            this.sortingOrder = sortingOrder;
            return this;
        }

        public ReservationSearch build() {
            return new ReservationSearch(
                symbol,
                accountId,
                portfolio,
                portfolioType,
                accountType,
                walletType,
                reference,
                currency,
                transactionType,
                reservationRef,
                clientId,
                from,
                to,
                first,
                after,
                sortingOrder
            );
        }
    }

    public record SettlementSearch(
        String transactionExecutionId,
        String clientSettlementId,
        String clientId,
        String from,
        String to,
        SortingOrder sortingOrder,
        Integer first,
        String after
    ) {

        public SettlementSearch {
            if (sortingOrder == null) sortingOrder = SortingOrder.DESC;
        }

        public static SettlementSearch all() {
            return new SettlementSearch("", "", "", "", "", null, null, "");
        }

        public static SettlementSearch limit(int first) {
            return new SettlementSearch("", "", "", "", "", SortingOrder.ASC, first, "");
        }

        public SettlementSearch withExecutionId(String transactionExecutionId) {
            return new SettlementSearch(transactionExecutionId, clientSettlementId, clientId, from, to, sortingOrder, first, after);
        }

        public SettlementSearch withinRange(String from, String to) {
            return new SettlementSearch(transactionExecutionId, clientSettlementId, clientId, from, to, sortingOrder, first, after);
        }

        public SettlementSearch withClientSettlementId(String clientSettlementId) {
            return new SettlementSearch(transactionExecutionId, clientSettlementId, clientId, from, to, sortingOrder, first, after);
        }

        public SettlementSearch after(String after) {
            return new SettlementSearch(transactionExecutionId, clientSettlementId, clientId, from, to, sortingOrder, first, after);
        }
    }
}
