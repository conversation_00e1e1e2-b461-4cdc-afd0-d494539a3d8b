package io.wyden.booking.reporting.interfaces.rabbitmq;

import io.wyden.booking.reporting.application.LedgerEntryService;
import io.wyden.booking.reporting.application.ReservationService;
import io.wyden.booking.reporting.application.TransactionService;
import io.wyden.booking.reporting.application.balance.BalanceCommandService;
import io.wyden.booking.reporting.application.recovery.BookingCompletedGapRecoveryService;
import io.wyden.booking.reporting.domain.tracking.EventTrackingRepository;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.published.booking.BookingCompleted;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BookingCompletedSequencedEventConsumer extends AbstractSequencedEventConsumer<BookingCompleted> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BookingCompletedSequencedEventConsumer.class);
    private static final String EVENT_TYPE = "booking_completed";
    
    private final RabbitQueue<BookingCompleted> bookingCompletedRabbitQueue;
    private final ReservationService reservationService;
    private final TransactionService transactionService;
    private final LedgerEntryService ledgerEntryService;
    private final BalanceCommandService balanceCommandService;
    private final BookingCompletedGapRecoveryService gapRecoveryService;

    public BookingCompletedSequencedEventConsumer(RabbitIntegrator rabbitIntegrator,
                                                  RabbitExchange<BookingCompleted> exchange,
                                                  @Value("${rabbitmq.booking-reporting-queue.booking-completed.name}") String queueName,
                                                  ReservationService reservationService,
                                                  TransactionService transactionService,
                                                  LedgerEntryService ledgerEntryService,
                                                  BalanceCommandService balanceCommandService,
                                                  BookingCompletedGapRecoveryService gapRecoveryService,
                                                  EventTrackingRepository trackingRepository,
                                                  @Value("${booking.completed.recovery.onstartup:false}") boolean isRecoveryRequired,
                                                  @Value("${booking.completed.onerror.break:false}") boolean shouldBreakOnError) {
        super(rabbitIntegrator, trackingRepository, shouldBreakOnError);
        
        this.bookingCompletedRabbitQueue = declareQueue(queueName, exchange);
        this.reservationService = reservationService;
        this.transactionService = transactionService;
        this.ledgerEntryService = ledgerEntryService;
        this.balanceCommandService = balanceCommandService;
        this.gapRecoveryService = gapRecoveryService;

        if (isRecoveryRequired) {
            performStartupRecovery();
            attachConsumer();
        } else {
            LOGGER.info("Startup recovery not required for BookingCompleted events");
            attachConsumer();
        }
    }

    private void attachConsumer() {
        LOGGER.info("Attaching consumer to BookingCompleted queue");
        bookingCompletedRabbitQueue.attachConsumer(BookingCompleted.parser(), this);
    }

    @Override
    protected String getEventType() {
        return EVENT_TYPE;
    }

    @Override
    protected long getSequenceNumber(BookingCompleted event) {
        return event.getSequenceNumber();
    }

    @Override
    protected boolean processEvent(BookingCompleted bookingCompleted) {
        try {
            if (bookingCompleted.hasReservationSnapshot()) {
                reservationService.accept(bookingCompleted, bookingCompleted.getReservationSnapshot());
            }

            if (bookingCompleted.hasTransactionCreated()) {
                transactionService.accept(bookingCompleted, bookingCompleted.getTransactionCreated());
            }

            if (!bookingCompleted.getLedgerEntryCreatedList().isEmpty()) {
                ledgerEntryService.accept(bookingCompleted, bookingCompleted.getLedgerEntryCreatedList());
            }

            if (!bookingCompleted.getBalanceSnapshotList().isEmpty()) {
                balanceCommandService.accept(bookingCompleted, bookingCompleted.getBalanceSnapshotList());
            }

            return true;
        } catch (Exception e) {
            LOGGER.error("Failed to process BookingCompleted event {}", bookingCompleted, e);
            return false;
        }
    }

    @Override
    protected List<BookingCompleted> recoverAllMissingEvents(long lastProcessedSequence) {
        return gapRecoveryService.recoverAllMissingEvents(lastProcessedSequence);
    }

    @Override
    protected List<BookingCompleted> recoverGap(long expectedSequence, long receivedSequence) {
        return gapRecoveryService.recoverGap(expectedSequence, receivedSequence);
    }
}
