package io.wyden.booking.reporting.utils;

import com.p6spy.engine.spy.appender.MessageFormattingStrategy;
import org.hibernate.engine.jdbc.internal.FormatStyle;

public class P6SpyMessageFormatter implements MessageFormattingStrategy {

    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category,
                                String prepared, String sql, String url) {
        if (sql == null || sql.trim().isEmpty()) {
            return "";
        }

        // Remove synthetic parameter details added by Spring JPQL for cleaner logs
        String cleanedSql = sql.replaceAll("__\\$synthetic\\$__\\d+", "?");

        // Format SQL to be nicely indented
        String formattedSql = FormatStyle.BASIC.getFormatter().format(cleanedSql);

        // Get stack trace (excluding P6Spy internal classes)
        StringBuilder callStack = new StringBuilder();
        StackTraceElement[] stackTrace = new Exception().getStackTrace();
        for (int i = 0; i < stackTrace.length && i < 150; i++) {
            String stackElement = stackTrace[i].toString();
            if (!stackElement.contains("com.p6spy")
                && !stackElement.contains("java.lang.reflect")
                && stackElement.contains("io.wyden")) {
                callStack.append("\n\tat ").append(stackElement);
            }
        }

        return new StringBuilder()
            .append("\n\n━━━━━━━━━━━━━━━━━━━━━━━━━ SQL QUERY ━━━━━━━━━━━━━━━━━━━━━━━━━")
            .append("\nExecution Time: ").append(elapsed).append(" ms")
            .append("\nConnection ID: ").append(connectionId)
            .append("\nQuery:")
            .append("\n").append(formattedSql)
            .append("\n\nStack Trace:").append(callStack)
            .append("\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            .append("\n")
            .toString();
    }
}
