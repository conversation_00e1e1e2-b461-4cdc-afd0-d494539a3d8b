package io.wyden.booking.reporting.infrastructure;

import io.wyden.booking.reporting.domain.rate.StablecoinProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.trimToEmpty;

@Configuration
public class StablecoinConfig {

    @Bean
    public StablecoinProvider stablecoinProvider(@Value("${booking-reporting.stablecoins}") String stablecoins) {
        // Map (stablecoin -> fiat)
        Map<String, String> map = new HashMap<>();
        String[] lines = stablecoins.split(";");
        for (String line : lines) {
            String[] values = line.split(",");
            String stablecoin = trimToEmpty(values[0]);
            String currency = trimToEmpty(values[1]);
            // enable conversion BTC/USDT -> BTC/USD
            map.put(stablecoin, currency);
        }
        return new StablecoinProvider(map);
    }
}
