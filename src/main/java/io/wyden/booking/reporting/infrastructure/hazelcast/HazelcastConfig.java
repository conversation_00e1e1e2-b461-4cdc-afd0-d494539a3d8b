package io.wyden.booking.reporting.infrastructure.hazelcast;

import com.hazelcast.client.HazelcastClient;
import com.hazelcast.client.config.ClientConfig;
import com.hazelcast.client.config.ClientNetworkConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.accessgateway.domain.license.LicenseMapConfig;
import io.wyden.accessgateway.domain.license.LicenseState;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.rate.Rate;
import io.wyden.published.rate.RateKey;
import io.wyden.rate.client.RatesCacheFacade;
import io.wyden.rate.domain.map.RateMapConfig;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.client.ReferenceDataProvider;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.referencedata.domain.PortfolioMapConfig;
import io.wyden.referencedata.domain.VenueAccountMapConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Configuration
public class HazelcastConfig {

    @Bean
    public LicenseMapConfig licenseMapConfig() {
        return new LicenseMapConfig();
    }

    @Bean
    Map<String, LicenseState> licenseMap(HazelcastInstance hazelcast) {
        return LicenseMapConfig.getMap(hazelcast);
    }

    @Bean
    ReferenceDataProvider referenceDataProvider() {
        return new ReferenceDataProvider();
    }

    @Bean
    public VenueAccountMapConfig venueAccountMapConfig() {
        return new VenueAccountMapConfig();
    }

    @Bean
    public VenueAccountCacheFacade venueAccountCacheFacade(HazelcastInstance hazelcast,
                                                           Tracing otlTracing) {
        return ReferenceDataProvider.getVenueAccountCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public PortfolioMapConfig portfolioMapConfig() {
        return new PortfolioMapConfig();
    }

    @Bean
    public PortfoliosCacheFacade portfoliosCacheFacade(HazelcastInstance hazelcast,
                                                       ReferenceDataProvider referenceDataProvider,
                                                       Tracing otlTracing) {
        return referenceDataProvider.getPortfoliosCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public RateMapConfig rateMapConfig() {
        return new RateMapConfig();
    }

    @Bean
    public RatesCacheFacade ratesCacheFacade(HazelcastInstance hazelcast, Tracing tracing) {
        IMap<RateKey, Rate> map = RateMapConfig.getMap(hazelcast);
        return new RatesCacheFacade(map, tracing);
    }

    @Bean
    public ClientConfig createClientConfig(@Value("${hz.addressList}") String addressList,
                                           @Value("${hz.outboundPortDefinition}") String outboundPortDefinition,
                                           List<HazelcastMapConfig> hazelcastMaps) {
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setInstanceName("booking_reporting");
        clientConfig.getConnectionStrategyConfig().getConnectionRetryConfig().setMaxBackoffMillis(5000);

        ClientNetworkConfig networkConfig = clientConfig.getNetworkConfig();
        Arrays.stream(addressList.split(",")).forEach(networkConfig::addAddress);
        networkConfig.setSmartRouting(true);

        if (!outboundPortDefinition.isBlank()) {
            networkConfig.addOutboundPortDefinition(outboundPortDefinition);
        }

        networkConfig.setRedoOperation(true);
        networkConfig.setConnectionTimeout(5000);

        hazelcastMaps.forEach(m -> m.applyConfig(clientConfig));

        return clientConfig;
    }

    @Bean
    public HazelcastInstance hazelcast(ClientConfig clientConfig, Telemetry telemetry, List<HazelcastMapConfig> hazelcastMaps) {
        HazelcastInstance hz = HazelcastClient.newHazelcastClient(clientConfig);
        hazelcastMaps.forEach(m -> m.setupClientInstance(hz));
        hazelcastMaps.forEach(m -> m.setupMetrics(hz, telemetry.getMeterRegistry()));
        return hz;
    }
}
