package io.wyden.booking.reporting.infrastructure.rabbit;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.PositionSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitDestinationsConfig {

    @Bean
    public RabbitExchange<PositionSnapshot> positionChangedExchange(RabbitIntegrator rabbitIntegrator) {
        return OemsExchange.Booking.declarePositionUpdatedExchange(rabbitIntegrator);
    }

    @Bean
    public RabbitExchange<PositionSnapshot> positionPnlCalculatedExchange(RabbitIntegrator rabbitIntegrator) {
        return OemsExchange.Booking.declarePositionPnlCalculatedExchange(rabbitIntegrator);
    }

    @Bean
    public RabbitExchange<TransactionSnapshot> transactionCreatedExchange(RabbitIntegrator rabbitIntegrator) {
        return OemsExchange.Booking.declareTransactionCreatedExchange(rabbitIntegrator);
    }

    @Bean
    public RabbitExchange<BookingCompleted> bookingCompletedRabbitExchange(RabbitIntegrator rabbitIntegrator) {
        return OemsExchange.Booking.declareBookingCompletedExchange(rabbitIntegrator);
    }
}
