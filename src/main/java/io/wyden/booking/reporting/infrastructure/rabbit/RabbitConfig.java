package io.wyden.booking.reporting.infrastructure.rabbit;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;

@Configuration
public class RabbitConfig {

    @Bean
    public RabbitIntegrator rabbitIntegrator(@Value("${rabbitmq.username}") String userName,
                                             @Value("${rabbitmq.password}") String password,
                                             @Value("${rabbitmq.virtualHost}") String virtualHost,
                                             @Value("${rabbitmq.host}") String host,
                                             @Value("${rabbitmq.port}") int port,
                                             @Value("${rabbitmq.tls}") String tls,
                                             MeterRegistry meterRegistry,
                                             ExecutorService consumptionExecutor,
                                             ExecutorService publishingExecutor) {
        return new RabbitIntegrator(userName, password, virtualHost, host, port, tls, meterRegistry, consumptionExecutor, publishingExecutor);
    }

    @Bean("consumptionExecutor")
    public ExecutorService consumptionExecutor() {
        return Executors.newSingleThreadScheduledExecutor(new ThreadFactoryBuilder().setNameFormat("c-consumer-%d").build());
    }

    @Bean("publishingExecutor")
    public ExecutorService publishingExecutor(@Value("${rabbitmq.producer-count}") int producerCount) {
        return Executors.newFixedThreadPool(producerCount, new ThreadFactoryBuilder().setNameFormat("m-producer-%d").build());
    }

    @Bean("backgroundTaskExecutor")
    public ScheduledExecutorService backgroundTaskExecutor() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("background-task-%d")
            .build();

        return Executors.newSingleThreadScheduledExecutor(namedThreadFactory);
    }
}
