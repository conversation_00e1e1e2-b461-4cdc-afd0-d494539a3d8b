package io.wyden.booking.reporting.application.balance;

import io.wyden.booking.reporting.application.referencedata.AccountSnapshot;
import io.wyden.booking.reporting.application.referencedata.PortfolioProvider;
import io.wyden.booking.reporting.application.referencedata.PortfolioSnapshot;
import io.wyden.booking.reporting.application.referencedata.VenueAccountProvider;
import io.wyden.booking.reporting.domain.balance.Balance;
import io.wyden.booking.reporting.domain.balance.BalanceRepository;
import io.wyden.published.booking.BalanceSnapshot;
import io.wyden.published.booking.BookingCompleted;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static io.wyden.booking.reporting.interfaces.rabbitmq.mappers.BalanceFromProtoMapper.map;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
public class BalanceCommandService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BalanceCommandService.class);

    private final BalanceRepository balanceRepository;
    private final VenueAccountProvider venueAccountProvider;
    private final PortfolioProvider portfolioProvider;

    public BalanceCommandService(BalanceRepository balanceRepository,
                                 VenueAccountProvider venueAccountProvider,
                                 PortfolioProvider portfolioProvider) {
        this.balanceRepository = balanceRepository;
        this.venueAccountProvider = venueAccountProvider;
        this.portfolioProvider = portfolioProvider;
    }

    @Transactional
    public void accept(BookingCompleted bookingCompleted, List<BalanceSnapshot> balanceSnapshotList) {
        long sequenceNumber = bookingCompleted.getSequenceNumber();

        for (BalanceSnapshot snapshot : balanceSnapshotList) {
            Optional<Balance> existing = balanceRepository
                .findBySymbolAndAccountIdAndPortfolioId(snapshot.getSymbol(), snapshot.getAccountId(), snapshot.getPortfolioId());

            if (existing.isPresent() && existing.get().getSequenceNumber() >= sequenceNumber) {
                LOGGER.warn("Balance snapshot with sequence number already exists. Existing sequenceNumer: {}, received sequence number: {}. Not applying older snapshot.", existing.get().getSequenceNumber(), sequenceNumber);
                continue;
            }

            String portfolioId = snapshot.getPortfolioId();
            String accountId = snapshot.getAccountId();

            Optional<PortfolioSnapshot> portfolioSnapshot = portfolioProvider.getPortfolioSnapshot(portfolioId);
            Optional<AccountSnapshot> accountSnapshot = venueAccountProvider.getAccountSnapshot(accountId);

            if (portfolioSnapshot.isEmpty() && accountSnapshot.isEmpty()) {
                throw new IllegalArgumentException("Both portfolio and account data cannot be found for: \n" + snapshot);
            }

            if (isNotBlank(portfolioId) && portfolioSnapshot.isEmpty()) {
                LOGGER.warn("Portfolio data not found for portfolioId: {}", portfolioId);
                return;
            }

            if (isNotBlank(accountId) && accountSnapshot.isEmpty()) {
                LOGGER.warn("Account data not found for accountId: {}", accountId);
                return;
            }

            Balance toSave = map(snapshot, sequenceNumber, portfolioSnapshot.orElse(null), accountSnapshot.orElse(null));
            existing.ifPresent(e -> {
                toSave.setId(e.getId());
                toSave.setCreatedAt(e.getCreatedAt());
                toSave.setCreatedBy(e.getCreatedBy());
                toSave.setVersion(e.getVersion());
            });
            Balance saved = balanceRepository.save(toSave);

            LOGGER.info("Saved new balance snapshot with sequence number: {}", saved.getSequenceNumber());
        }
    }
}
