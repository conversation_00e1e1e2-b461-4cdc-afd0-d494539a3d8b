package io.wyden.booking.reporting.application.permissions;

import io.wyden.accessgateway.client.apikey.ApiKeyRestClient;
import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.accessgateway.client.permission.PermissionCache;
import io.wyden.accessgateway.client.permission.PermissionChecker;
import io.wyden.accessgateway.client.permission.WydenRole;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class AccessGatewayClient implements AccessGatewayFacade {

    private final ApiKeyRestClient apiKeyRestClient;
    private final PermissionChecker permissionChecker;
    private final PermissionCache permissionCache;
    private final int queryLimit;

    public AccessGatewayClient(ApiKeyRestClient apiKeyRestClient,
                                PermissionChecker permissionChecker,
                                PermissionCache permissionCache,
                                @Value("${booking-reporting.query-limits.portfolio:100}") int queryLimit) {
        this.apiKeyRestClient = apiKeyRestClient;
        this.permissionChecker = permissionChecker;
        this.permissionCache = permissionCache;
        this.queryLimit = queryLimit;
    }

    public User getUserAttributes(String username) {
        return User.user(apiKeyRestClient.getUserAttributes(username));
    }

    public boolean hasPermission(Set<WydenRole> roles, Set<String> groups, String username, Permission permission) {
        return permissionChecker.hasPermission(roles, groups, username, permission);
    }

    public Set<String> getPermittedResourceIds(Set<String> groups, String username, String resource, String scope) {
        return permissionCache.getPermittedResourceIds(groups, username, resource, scope, queryLimit);
    }
}
