package io.wyden.booking.reporting.application.position;

import io.wyden.booking.reporting.application.permissions.TransactionSearchRequestAuthorizer;
import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.booking.reporting.domain.position.PositionRepository;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

import static io.wyden.booking.reporting.application.TransactionService.DEFAULT_PAGE_SIZE;
import static io.wyden.booking.reporting.application.TransactionService.mapDirection;
import static io.wyden.booking.reporting.application.TransactionService.wrapToModel;
import static org.slf4j.LoggerFactory.getLogger;

@Component
public class PositionQueryService {

    private static final Logger LOGGER = getLogger(PositionQueryService.class);

    private final PositionRepository positionRepository;
    private final TransactionSearchRequestAuthorizer searchRequestAuthorizer;
    private final PositionCommandService positionCommandService;

    public PositionQueryService(PositionRepository positionRepository,
                                TransactionSearchRequestAuthorizer searchRequestAuthorizer, PositionCommandService positionCommandService) {
        this.positionRepository = positionRepository;
        this.searchRequestAuthorizer = searchRequestAuthorizer;
        this.positionCommandService = positionCommandService;
    }

    public PaginationModel.CursorConnection<Position> search(RequestModel.PositionSearch search) {
        LOGGER.info("Searching for positions for: {}", search);

        Optional<SimpleSearchRequest.AuthorizedSearchRequest> authorizedSearch = searchRequestAuthorizer.authorize(search.toSearchRequest());
        if (authorizedSearch.isEmpty()) {
            LOGGER.warn("User {} has no permissions, processing will be skipped: {}", search.clientId(), search);
            return PaginationModel.emptyCursorConnection();
        }

        RequestModel.AuthorizedPositionSearch authorizedPositionSearch = new RequestModel.AuthorizedPositionSearch(search, authorizedSearch.get());

        return searchAuthorizedPositions(authorizedPositionSearch);
    }

    public PaginationModel.CursorConnection<Position> searchInternal(RequestModel.PositionSearch search) {
        LOGGER.info("Internal searching for positions for: {}", search);

        SimpleSearchRequest.AuthorizedSearchRequest authorizedSearch = SimpleSearchRequest.AuthorizedSearchRequest.builder()
            .allAccountsAuthorized(true)
            .allPortfoliosAuthorized(true)
            .build();

        RequestModel.AuthorizedPositionSearch authorizedPositionSearch = new RequestModel.AuthorizedPositionSearch(search, authorizedSearch);

        return searchAuthorizedPositions(authorizedPositionSearch);
    }

    private PaginationModel.CursorConnection<Position> searchAuthorizedPositions(RequestModel.AuthorizedPositionSearch authorizedPositionSearch) {
        Sort.Direction direction = mapDirection(authorizedPositionSearch.searchRequest().sortingOrder());

        Integer pageSize = Objects.requireNonNullElse(authorizedPositionSearch.searchRequest().first(), DEFAULT_PAGE_SIZE);

        Pageable pageable = PageRequest.of(
            0,
            pageSize,
            Sort.by(direction, "id")
        );

        Page<Position> positions = positionRepository.findByProperties(authorizedPositionSearch, pageable);
        LOGGER.info("Found {} positions for search request: {}", positions.getNumberOfElements(), authorizedPositionSearch);

        Page<Position> updatedPositions = positions.map(position -> {
            Position recalculatedPosition = positionCommandService.recalculatePosition(position);
            LOGGER.debug("Recalculated position: {}", recalculatedPosition);
            return recalculatedPosition;
        });

        return wrapToModel(updatedPositions, position -> String.valueOf(position.getId()));
    }
}
