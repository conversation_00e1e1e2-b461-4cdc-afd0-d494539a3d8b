package io.wyden.booking.reporting.application.referencedata;

import io.wyden.published.booking.AccountCashTransferReservationSnapshot;
import io.wyden.published.booking.AccountCashTransferSnapshot;
import io.wyden.published.booking.ClientAssetTradeReservationSnapshot;
import io.wyden.published.booking.ClientAssetTradeSnapshot;
import io.wyden.published.booking.ClientCashTradeReservationSnapshot;
import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.DepositReservationSnapshot;
import io.wyden.published.booking.DepositSnapshot;
import io.wyden.published.booking.FeeReservationSnapshot;
import io.wyden.published.booking.FeeSnapshot;
import io.wyden.published.booking.PortfolioAssetTransferReservationSnapshot;
import io.wyden.published.booking.PortfolioAssetTransferSnapshot;
import io.wyden.published.booking.PortfolioCashTransferReservationSnapshot;
import io.wyden.published.booking.PortfolioCashTransferSnapshot;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.booking.StreetAssetTradeReservationSnapshot;
import io.wyden.published.booking.StreetAssetTradeSnapshot;
import io.wyden.published.booking.StreetCashTradeReservationSnapshot;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.WithdrawalReservationSnapshot;
import io.wyden.published.booking.WithdrawalSnapshot;
import org.springframework.stereotype.Component;

@Component
public class ReferenceDataResolver {

    private final PortfolioProvider portfolioProvider;
    private final VenueAccountProvider venueAccountProvider;

    public ReferenceDataResolver(PortfolioProvider portfolioProvider, VenueAccountProvider venueAccountProvider) {
        this.portfolioProvider = portfolioProvider;
        this.venueAccountProvider = venueAccountProvider;
    }

    public ReferenceDataSnapshot resolve(TransactionSnapshot transactionSnapshot) {
        ReferenceDataSnapshot.Builder builder = ReferenceDataSnapshot.builder();

        if (transactionSnapshot.hasClientCashTrade()) {
            ClientCashTradeSnapshot snapshot = transactionSnapshot.getClientCashTrade();
            setPortfolioData(builder, snapshot.getPortfolio());
            setCounterPortfolioData(builder, snapshot.getCounterPortfolio());
        }

        if (transactionSnapshot.hasStreetCashTrade()) {
            StreetCashTradeSnapshot snapshot = transactionSnapshot.getStreetCashTrade();
            setPortfolioData(builder, snapshot.getPortfolio());
            setAccountData(builder, snapshot.getVenueAccount());
        }

        if (transactionSnapshot.hasClientAssetTrade()) {
            ClientAssetTradeSnapshot snapshot = transactionSnapshot.getClientAssetTrade();
            setPortfolioData(builder, snapshot.getPortfolio());
            setCounterPortfolioData(builder, snapshot.getCounterPortfolio());
        }

        if (transactionSnapshot.hasStreetAssetTrade()) {
            StreetAssetTradeSnapshot snapshot = transactionSnapshot.getStreetAssetTrade();
            setPortfolioData(builder, snapshot.getPortfolio());
            setAccountData(builder, snapshot.getVenueAccount());
        }

        if (transactionSnapshot.hasDeposit()) {
            DepositSnapshot snapshot = transactionSnapshot.getDeposit();
            setPortfolioData(builder, snapshot.getPortfolio());
            setAccountData(builder, snapshot.getAccount());
            setFeePortfolioData(builder, snapshot.getFeePortfolioId());
            setFeeAccountData(builder, snapshot.getFeeAccountId());
        }

        if (transactionSnapshot.hasWithdrawal()) {
            WithdrawalSnapshot snapshot = transactionSnapshot.getWithdrawal();
            setPortfolioData(builder, snapshot.getPortfolio());
            setAccountData(builder, snapshot.getAccount());
            setFeePortfolioData(builder, snapshot.getFeePortfolioId());
            setFeeAccountData(builder, snapshot.getFeeAccountId());
        }

        if (transactionSnapshot.hasPortfolioCashTransfer()) {
            PortfolioCashTransferSnapshot snapshot = transactionSnapshot.getPortfolioCashTransfer();
            setSourcePortfolioData(builder, snapshot.getFromPortfolioId());
            setTargetPortfolioData(builder, snapshot.getToPortfolioId());
            setFeePortfolioData(builder, snapshot.getFeePortfolioId());
        }

        if (transactionSnapshot.hasPortfolioAssetTransfer()) {
            PortfolioAssetTransferSnapshot snapshot = transactionSnapshot.getPortfolioAssetTransfer();
            setSourcePortfolioData(builder, snapshot.getFromPortfolioId());
            setTargetPortfolioData(builder, snapshot.getToPortfolioId());
            setFeePortfolioData(builder, snapshot.getFeePortfolioId());
        }

        if (transactionSnapshot.hasAccountCashTransfer()) {
            AccountCashTransferSnapshot snapshot = transactionSnapshot.getAccountCashTransfer();
            setSourceAccountData(builder, snapshot.getFromAccountId());
            setTargetAccountData(builder, snapshot.getToAccountId());
            setFeePortfolioData(builder, snapshot.getFeePortfolioId());
            setFeeAccountData(builder, snapshot.getFeeAccountId());
        }

        if (transactionSnapshot.hasFee()) {
            FeeSnapshot snapshot = transactionSnapshot.getFee();
            setPortfolioData(builder, snapshot.getPortfolioId());
            setAccountData(builder, snapshot.getAccountId());
        }

        return builder.build();
    }

    public ReferenceDataSnapshot resolve(ReservationSnapshot reservationSnapshot) {
        ReferenceDataSnapshot.Builder builder = ReferenceDataSnapshot.builder();

        if (reservationSnapshot.hasClientCashTradeReservation()) {
            ClientCashTradeReservationSnapshot snapshot = reservationSnapshot.getClientCashTradeReservation();
            setPortfolioData(builder, snapshot.getPortfolioId());
            setCounterPortfolioData(builder, snapshot.getCounterPortfolioId());
        }

        if (reservationSnapshot.hasStreetCashTradeReservation()) {
            StreetCashTradeReservationSnapshot snapshot = reservationSnapshot.getStreetCashTradeReservation();
            setPortfolioData(builder, snapshot.getPortfolioId());
            setAccountData(builder, snapshot.getAccountId());
        }

        if (reservationSnapshot.hasClientAssetTradeReservation()) {
            ClientAssetTradeReservationSnapshot snapshot = reservationSnapshot.getClientAssetTradeReservation();
            setPortfolioData(builder, snapshot.getPortfolioId());
            setCounterPortfolioData(builder, snapshot.getCounterPortfolioId());
        }

        if (reservationSnapshot.hasStreetAssetTradeReservation()) {
            StreetAssetTradeReservationSnapshot snapshot = reservationSnapshot.getStreetAssetTradeReservation();
            setPortfolioData(builder, snapshot.getPortfolioId());
            setAccountData(builder, snapshot.getAccountId());
        }

        if (reservationSnapshot.hasDepositReservation()) {
            DepositReservationSnapshot snapshot = reservationSnapshot.getDepositReservation();
            setPortfolioData(builder, snapshot.getPortfolioId());
            setAccountData(builder, snapshot.getAccountId());
            setFeePortfolioData(builder, snapshot.getFeePortfolioId());
            setFeeAccountData(builder, snapshot.getFeeAccountId());
        }

        if (reservationSnapshot.hasWithdrawalReservation()) {
            WithdrawalReservationSnapshot snapshot = reservationSnapshot.getWithdrawalReservation();
            setPortfolioData(builder, snapshot.getPortfolioId());
            setAccountData(builder, snapshot.getAccountId());
            setFeePortfolioData(builder, snapshot.getFeePortfolioId());
            setFeeAccountData(builder, snapshot.getFeeAccountId());
        }

        if (reservationSnapshot.hasPortfolioCashTransferReservation()) {
            PortfolioCashTransferReservationSnapshot snapshot = reservationSnapshot.getPortfolioCashTransferReservation();
            setSourcePortfolioData(builder, snapshot.getFromPortfolioId());
            setTargetPortfolioData(builder, snapshot.getToPortfolioId());
            setFeePortfolioData(builder, snapshot.getFeePortfolioId());
        }

        if (reservationSnapshot.hasPortfolioAssetTransferReservation()) {
            PortfolioAssetTransferReservationSnapshot snapshot = reservationSnapshot.getPortfolioAssetTransferReservation();
            setSourcePortfolioData(builder, snapshot.getFromPortfolioId());
            setTargetPortfolioData(builder, snapshot.getToPortfolioId());
            setFeePortfolioData(builder, snapshot.getFeePortfolioId());
        }

        if (reservationSnapshot.hasAccountCashTransferReservation()) {
            AccountCashTransferReservationSnapshot snapshot = reservationSnapshot.getAccountCashTransferReservation();
            setSourceAccountData(builder, snapshot.getFromAccountId());
            setTargetAccountData(builder, snapshot.getToAccountId());
            setFeePortfolioData(builder, snapshot.getFeePortfolioId());
            setFeeAccountData(builder, snapshot.getFeeAccountId());
        }

        if (reservationSnapshot.hasFeeReservation()) {
            FeeReservationSnapshot snapshot = reservationSnapshot.getFeeReservation();
            setPortfolioData(builder, snapshot.getPortfolioId());
            setAccountData(builder, snapshot.getAccountId());
        }

        return builder.build();
    }

    private void setPortfolioData(ReferenceDataSnapshot.Builder builder, String portfolioId) {
        portfolioProvider.getPortfolioSnapshot(portfolioId)
            .ifPresent(s -> builder
                .portfolioId(s.portfolioId())
                .portfolioCurrency(s.portfolioCurrency())
                .portfolioType(s.portfolioType()));
    }

    private void setCounterPortfolioData(ReferenceDataSnapshot.Builder builder, String portfolioId) {
        portfolioProvider.getPortfolioSnapshot(portfolioId)
            .ifPresent(s -> builder
                .counterPortfolioId(s.portfolioId())
                .counterPortfolioCurrency(s.portfolioCurrency())
                .counterPortfolioType(s.portfolioType()));
    }

    private void setFeePortfolioData(ReferenceDataSnapshot.Builder builder, String portfolioId) {
        portfolioProvider.getPortfolioSnapshot(portfolioId)
            .ifPresent(s -> builder
                .feePortfolioId(s.portfolioId())
                .feePortfolioCurrency(s.portfolioCurrency())
                .feePortfolioType(s.portfolioType()));
    }

    private void setSourcePortfolioData(ReferenceDataSnapshot.Builder builder, String portfolioId) {
        portfolioProvider.getPortfolioSnapshot(portfolioId)
            .ifPresent(s -> builder
                .sourcePortfolioId(s.portfolioId())
                .sourcePortfolioCurrency(s.portfolioCurrency())
                .sourcePortfolioType(s.portfolioType()));
    }

    private void setTargetPortfolioData(ReferenceDataSnapshot.Builder builder, String portfolioId) {
        portfolioProvider.getPortfolioSnapshot(portfolioId)
            .ifPresent(s -> builder
                .targetPortfolioId(s.portfolioId())
                .targetPortfolioCurrency(s.portfolioCurrency())
                .targetPortfolioType(s.portfolioType()));
    }

    private void setAccountData(ReferenceDataSnapshot.Builder builder, String accountId) {
        venueAccountProvider.getAccountSnapshot(accountId)
            .ifPresent(s -> builder
                .accountId(s.accountId())
                .accountCurrency(s.accountCurrency())
                .accountType(s.accountType()));
    }

    private void setFeeAccountData(ReferenceDataSnapshot.Builder builder, String accountId) {
        venueAccountProvider.getAccountSnapshot(accountId)
            .ifPresent(s -> builder
                .feeAccountId(s.accountId())
                .feeAccountCurrency(s.accountCurrency())
                .feeAccountType(s.accountType()));
    }

    private void setSourceAccountData(ReferenceDataSnapshot.Builder builder, String accountId) {
        venueAccountProvider.getAccountSnapshot(accountId)
            .ifPresent(s -> builder
                .sourceAccountId(s.accountId())
                .sourceAccountCurrency(s.accountCurrency())
                .sourceAccountType(s.accountType()));
    }

    private void setTargetAccountData(ReferenceDataSnapshot.Builder builder, String accountId) {
        venueAccountProvider.getAccountSnapshot(accountId)
            .ifPresent(s -> builder
                .targetAccountId(s.accountId())
                .targetAccountCurrency(s.accountCurrency())
                .targetAccountType(s.accountType()));
    }
}