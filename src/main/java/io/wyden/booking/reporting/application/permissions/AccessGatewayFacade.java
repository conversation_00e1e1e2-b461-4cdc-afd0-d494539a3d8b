package io.wyden.booking.reporting.application.permissions;

import io.wyden.accessgateway.client.apikey.dto.AuthResponseDto;
import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.accessgateway.client.permission.WydenRole;

import java.util.Set;

public interface AccessGatewayFacade {
    User getUserAttributes(String username);
    boolean hasPermission(Set<WydenRole> roles, Set<String> groups, String username, Permission permission);
    Set<String> getPermittedResourceIds(Set<String> groups, String username, String resource, String scope);

    record User(String clientId, Set<String> groups, Set<WydenRole> roles) {
        public static User user(AuthResponseDto authResponseDto) {
            return new User(authResponseDto.username(), authResponseDto.groups(), authResponseDto.roles());
        }
    }

}
