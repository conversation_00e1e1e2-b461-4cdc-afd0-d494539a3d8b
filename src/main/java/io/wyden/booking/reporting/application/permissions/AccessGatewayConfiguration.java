package io.wyden.booking.reporting.application.permissions;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.accessgateway.client.apikey.ApiKeyRestClient;
import io.wyden.accessgateway.client.permission.PermissionCache;
import io.wyden.accessgateway.client.permission.PermissionChecker;
import io.wyden.accessgateway.client.rest.RestTemplateFactory;
import io.wyden.accessgateway.domain.permission.PermissionGroupListMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionGroupMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionUserListMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionUserMapConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.util.Set;

@Configuration
public class AccessGatewayConfiguration {

    @Bean
    public RestTemplate restTemplate() {
        return RestTemplateFactory.create();
    }

    @Bean
    public ApiKeyRestClient apiKeyRestClient(RestTemplate restTemplate,
                                             @Value("${access.gateway.host}") String accessGatewayHost) {
        return new ApiKeyRestClient(restTemplate, accessGatewayHost);
    }

    @Bean
    public PermissionChecker permissionChecker(HazelcastInstance hazelcast) {
        return new PermissionChecker(hazelcast);
    }

    @Bean
    public PermissionCache permissionCache(IMap<String, Set<String>> userPermissionListCache, IMap<String, Set<String>> groupPermissionListCache) {
        return new PermissionCache(userPermissionListCache, groupPermissionListCache);
    }

    @Bean
    public IMap<String, String> userPermission(HazelcastInstance hazelcast) {
        return PermissionUserMapConfig.getMap(hazelcast);
    }

    @Bean
    public IMap<String, String> groupPermission(HazelcastInstance hazelcast) {
        return PermissionGroupMapConfig.getMap(hazelcast);
    }

    @Bean
    public PermissionUserMapConfig permissionUserMapConfig() {
        return new PermissionUserMapConfig();
    }

    @Bean
    public PermissionGroupMapConfig permissionGroupMapConfig() {
        return new PermissionGroupMapConfig();
    }

    @Bean
    public IMap<String, Set<String>> userPermissionListCache(HazelcastInstance hazelcast) {
        return PermissionUserListMapConfig.getMap(hazelcast);
    }

    @Bean
    public IMap<String, Set<String>> groupPermissionListCache(HazelcastInstance hazelcast) {
        return PermissionGroupListMapConfig.getMap(hazelcast);
    }

    @Bean
    public PermissionUserListMapConfig permissionUserListMapConfig() {
        return new PermissionUserListMapConfig();
    }

    @Bean
    public PermissionGroupListMapConfig permissionGroupListMapConfig() {
        return new PermissionGroupListMapConfig();
    }
}
