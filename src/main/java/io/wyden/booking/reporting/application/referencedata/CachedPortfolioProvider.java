package io.wyden.booking.reporting.application.referencedata;

import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.BalanceFromProtoMapper;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static io.wyden.booking.reporting.interfaces.rabbitmq.mappers.BalanceFromProtoMapper.map;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Component
public class CachedPortfolioProvider implements PortfolioProvider {

    private final PortfoliosCacheFacade portfoliosCacheFacade;

    public CachedPortfolioProvider(PortfoliosCacheFacade portfoliosCacheFacade) {
        this.portfoliosCacheFacade = portfoliosCacheFacade;
    }

    @Override
    public Portfolio getPortfolio(String portfolioId) {
        return this.portfoliosCacheFacade.find(portfolioId)
            .orElseThrow(() -> new IllegalStateException("Could not find portfolio for: " + portfolioId));
    }

    @Override
    public String getPortfolioCurrency(String portfolioId) {
        if (isBlank(portfolioId)) {
            return EMPTY;
        }

        return portfoliosCacheFacade.find(portfolioId)
            .map(Portfolio::getPortfolioCurrency)
            .orElseThrow(() -> new IllegalStateException("Could not find portfolio currency for: " + portfolioId));
    }

    public Optional<PortfolioSnapshot> getPortfolioSnapshot(String portfolioId) {
        if (isBlank(portfolioId)) {
            return Optional.empty();
        }

        return this.portfoliosCacheFacade.find(portfolioId)
            .map(portfolio -> new PortfolioSnapshot(portfolio.getId(), portfolio.getPortfolioCurrency(), map(portfolio.getPortfolioType())));
    }

    @Override
    public RequestModel.PortfolioType getPortfolioType(String portfolioId) {
        if (isBlank(portfolioId)) {
            return null;
        }

        return portfoliosCacheFacade.find(portfolioId)
            .map(Portfolio::getPortfolioType)
            .map(BalanceFromProtoMapper::map)
            .orElse(null);
    }
}
