package io.wyden.booking.reporting.application.recovery;

import io.wyden.published.booking.BookingCompleted;

import java.util.List;

public class BookingCompletedRecoveryResult {
    private final List<BookingCompleted> bookingCompletedEvents;
    private final boolean hasMoreEvents;

    public BookingCompletedRecoveryResult(List<BookingCompleted> bookingCompletedEvents, boolean hasMoreEvents) {
        this.bookingCompletedEvents = bookingCompletedEvents;
        this.hasMoreEvents = hasMoreEvents;
    }

    public List<BookingCompleted> getBookingCompletedEvents() {
        return bookingCompletedEvents;
    }

    public boolean hasMoreEvents() {
        return hasMoreEvents;
    }
}