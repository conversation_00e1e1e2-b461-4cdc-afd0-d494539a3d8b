package io.wyden.booking.reporting.application;

import io.wyden.booking.reporting.domain.rate.Rate;
import io.wyden.booking.reporting.domain.rate.SystemCurrencyProvider;
import io.wyden.rate.client.RatesCacheFacade;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

import static org.slf4j.LoggerFactory.getLogger;

@Service
public class RateService {

    private static final Logger LOGGER = getLogger(RateService.class);

    private final RatesCacheFacade ratesCacheFacade;

    public RateService(RatesCacheFacade ratesCacheFacade) {
        this.ratesCacheFacade = ratesCacheFacade;
    }

    public Optional<Rate> findRate(String baseCurrency, String quoteCurrency) {
        Objects.requireNonNull(baseCurrency, "baseCurrency must not be null");
        Objects.requireNonNull(quoteCurrency, "quoteCurrency must not be null");

        return ratesCacheFacade.find(baseCurrency, quoteCurrency)
            .map(Rate::from);
    }

    public Optional<Rate> findRate(String baseCurrency) {
        return findRate(baseCurrency, SystemCurrencyProvider.getSystemCurrency());
    }
}
