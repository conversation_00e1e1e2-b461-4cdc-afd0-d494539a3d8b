package io.wyden.booking.reporting.application.recovery;

import io.wyden.published.booking.BookingCompleted;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service class responsible for recovering sequence gaps in BookingCompleted events.
 */
@Service
public class BookingCompletedGapRecoveryService {

    private final BookingCompletedRecoveryClient recoveryClient;

    public BookingCompletedGapRecoveryService(BookingCompletedRecoveryClient recoveryClient) {
        this.recoveryClient = recoveryClient;
    }

    /**
     * Recovers a sequence gap between the expected and received event sequences.
     */
    public List<BookingCompleted> recoverGap(long expectedSequence, long receivedSequence) {
        return GapRecoveryOperations.recoverGap(
            expectedSequence,
            receivedSequence,
            "BookingCompleted",
            recoveryClient::fetchBookingCompletedEventsAfter,
            BookingCompletedRecoveryResult::getBookingCompletedEvents,
            BookingCompletedRecoveryResult::hasMoreEvents,
            BookingCompleted::getSequenceNumber
        );
    }

    /**
     * Recovers all events from the last processed sequence up to the current state.
     * This is used during system startup to ensure no events are missed.
     */
    public List<BookingCompleted> recoverAllMissingEvents(long lastProcessedSequence) {
        return GapRecoveryOperations.recoverAllMissingEvents(
            lastProcessedSequence,
            "BookingCompleted",
            recoveryClient::fetchBookingCompletedEventsAfter,
            BookingCompletedRecoveryResult::getBookingCompletedEvents,
            BookingCompletedRecoveryResult::hasMoreEvents,
            BookingCompleted::getSequenceNumber
        );
    }
}