package io.wyden.booking.reporting.application.permissions;

import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.booking.reporting.application.referencedata.PortfolioProvider;
import io.wyden.booking.reporting.application.referencedata.VenueAccountProvider;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.WalletType;

import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Optional;

@Component
public class SearchRequestAuthorizer {
    private final AccessGatewayService accessGatewayService;
    private final PortfolioProvider portfolioProvider;
    private final VenueAccountProvider venueAccountProvider;

    public SearchRequestAuthorizer(AccessGatewayService accessGatewayService,
                                   PortfolioProvider portfolioProvider,
                                   VenueAccountProvider venueAccountProvider) {
        this.accessGatewayService = accessGatewayService;
        this.portfolioProvider = portfolioProvider;
        this.venueAccountProvider = venueAccountProvider;
    }

    public @Nullable RequestModel.SearchRequest authorize(RequestModel.SearchRequest request) {
        return authorize(request, true);
    }

    @SuppressWarnings("java:S3776")
    public @Nullable RequestModel.SearchRequest authorize(RequestModel.SearchRequest request, boolean accountAndPortfoliosDisjointly) {
        AccessGatewayFacade.User user = accessGatewayService.getUserAttributes(request.clientId());

        boolean hasVenueAccountStaticPermission = accessGatewayService.hasVenueAccountStaticPermission(user);
        boolean hasWalletStaticPermission = accessGatewayService.hasStaticPermission(user, Permission.WALLET_READ);
        boolean hasWalletNostroStaticPermission = accessGatewayService.hasStaticPermission(user, Permission.WALLET_NOSTRO_READ);
        boolean hasWalletVostroStaticPermission = accessGatewayService.hasStaticPermission(user, Permission.WALLET_VOSTRO_READ);

        boolean hasPortfolioStaticPermission = accessGatewayService.hasPortfolioStaticPermission(user);
        boolean hasPortfolioNostroStaticPermission = accessGatewayService.hasStaticPermission(user, Permission.PORTFOLIO_NOSTRO_READ);
        boolean hasPortfolioVostroStaticPermission = accessGatewayService.hasStaticPermission(user, Permission.PORTFOLIO_VOSTRO_READ);

        LazyResourceHolder<String> authorizedVenueAccounts = new LazyResourceHolder<>(() -> accessGatewayService.selectAuthorizedVenueAccounts(user, request.accountId()));
        LazyResourceHolder<String> authorizedPortfolios = new LazyResourceHolder<>(() -> accessGatewayService.selectAuthorizedPortfolios(user, request.portfolio()));

        if (!hasVenueAccountStaticPermission
            && !hasPortfolioStaticPermission
            && !hasPortfolioNostroStaticPermission
            && !hasPortfolioVostroStaticPermission
            && !hasWalletStaticPermission
            && !hasWalletVostroStaticPermission
            && !hasWalletNostroStaticPermission
            && authorizedVenueAccounts.get().isEmpty()
            && authorizedPortfolios.get().isEmpty()) {
            return null;
        }

        if (hasPortfolioNostroStaticPermission) {
            authorizedPortfolios.add(() -> filterPortfolioByType(request.portfolio(), RequestModel.PortfolioType.NOSTRO));
        }

        if (hasPortfolioVostroStaticPermission) {
            authorizedPortfolios.add(() -> filterPortfolioByType(request.portfolio(), RequestModel.PortfolioType.VOSTRO));
        }

        if (hasWalletStaticPermission || hasWalletNostroStaticPermission) {
            authorizedVenueAccounts.add(() -> filterAccessibleWalletsByType(request.accountId(), RequestModel.WalletType.NOSTRO));
        }

        if (hasWalletStaticPermission || hasWalletVostroStaticPermission) {
            authorizedVenueAccounts.add(() -> filterAccessibleWalletsByType(request.accountId(), RequestModel.WalletType.VOSTRO));
        }

        if (hasPortfolioVostroStaticPermission && hasPortfolioNostroStaticPermission) {
            hasPortfolioStaticPermission = true;
            hasPortfolioNostroStaticPermission = false;
            hasPortfolioVostroStaticPermission = false;
        }

        if (hasWalletVostroStaticPermission && hasWalletNostroStaticPermission) {
            hasWalletStaticPermission = true;
            hasWalletVostroStaticPermission = false;
            hasWalletNostroStaticPermission = false;
        }

        RequestModel.SearchRequest authorizedRequest = applyPortfolioAndWalletType(request,
            hasPortfolioStaticPermission, hasPortfolioNostroStaticPermission, hasPortfolioVostroStaticPermission, authorizedPortfolios,
            hasVenueAccountStaticPermission, hasWalletStaticPermission, hasWalletNostroStaticPermission, hasWalletVostroStaticPermission, authorizedVenueAccounts);

        if (request.portfolio().isEmpty() && request.accountId().isEmpty()) {
            if (hasVenueAccountStaticPermission && hasPortfolioStaticPermission) {
                return request.withAccountType(RequestModel.AccountType.ALL).withPortfolioType(RequestModel.PortfolioType.ALL);
            } else if (hasVenueAccountStaticPermission || hasWalletStaticPermission || hasWalletNostroStaticPermission || hasWalletVostroStaticPermission) {
                return authorizedRequest.withPortfolio(authorizedPortfolios.get());
            } else if (hasPortfolioStaticPermission || hasPortfolioNostroStaticPermission || hasPortfolioVostroStaticPermission) {
                return authorizedRequest.withAccount(authorizedVenueAccounts.get());
            } else {
                return authorizedRequest
                    .withAccount(authorizedVenueAccounts.get())
                    .withPortfolio(authorizedPortfolios.get());
            }
        }

        // portfolio AND account
        if (!request.portfolio().isEmpty() && !request.accountId().isEmpty()) {
            boolean canAccessAnyPortfolio = hasPortfolioStaticPermission || !authorizedPortfolios.get().isEmpty();
            boolean canAccessAnyAccount = hasVenueAccountStaticPermission || !authorizedVenueAccounts.get().isEmpty();

            if (!canAccessAnyPortfolio && !canAccessAnyAccount) {
                return null;
            }

            RequestModel.SearchRequest result = authorizedRequest;

            if (canAccessAnyAccount) {
                result = result.withAccount(authorizedVenueAccounts.get());
            } else {
                result = result.withAccount(null);
                result = result.withAccountType(RequestModel.AccountType.NONE);
            }

            if (canAccessAnyPortfolio) {
                result = result.withPortfolio(authorizedPortfolios.get());
            } else {
                result = result.withPortfolio(null);
                result = result.withPortfolioType(RequestModel.PortfolioType.NONE);
            }

            return result;
        }

        //portfolio only
        if (!request.portfolio().isEmpty()) {
            if (!hasPortfolioStaticPermission && authorizedPortfolios.get().isEmpty()) {
                return null;
            }
            return accountAndPortfoliosDisjointly
                ? authorizedRequest.withPortfolio(authorizedPortfolios.get()).withAccountType(RequestModel.AccountType.NONE).withPortfolioType(null)
                : authorizedRequest.withPortfolio(authorizedPortfolios.get());
        }

        //account only
        if (!hasVenueAccountStaticPermission && authorizedVenueAccounts.get().isEmpty()) {
            return null;
        }
        return accountAndPortfoliosDisjointly
            ? authorizedRequest.withAccount(authorizedVenueAccounts.get()).withPortfolioType(RequestModel.PortfolioType.NONE).withAccountType(null)
            : authorizedRequest.withAccount(authorizedVenueAccounts.get());
    }

    @SuppressWarnings("java:S107")
    private RequestModel.SearchRequest applyPortfolioAndWalletType(RequestModel.SearchRequest request, boolean hasPortfolioStaticPermission, boolean hasPortfolioNostroStaticPermission, boolean hasPortfolioVostroStaticPermission, LazyResourceHolder<String> authorizedPortfolios,
                                                                   boolean hasVenueAccountStaticPermission, boolean hasWalletStaticPermission, boolean hasWalletNostroStaticPermission, boolean hasWalletVostroStaticPermission, LazyResourceHolder<String> authorizedVenueAccounts) {
        RequestModel.SearchRequest authorizedRequest = applyPortfolioType(request, hasPortfolioStaticPermission, hasPortfolioNostroStaticPermission, hasPortfolioVostroStaticPermission,
            authorizedPortfolios);
        authorizedRequest = applyWalletType(authorizedRequest, hasVenueAccountStaticPermission, hasWalletStaticPermission, hasWalletNostroStaticPermission, hasWalletVostroStaticPermission,
            authorizedVenueAccounts);
        return authorizedRequest;
    }

    private RequestModel.SearchRequest applyPortfolioType(RequestModel.SearchRequest searchRequest,
                                                          boolean hasPortfolioStaticPermission,
                                                          boolean hasStaticNostroPermission,
                                                          boolean hasStaticVostroPermission,
                                                          LazyResourceHolder<String> authorizedPortfolios) {
        if (hasPortfolioStaticPermission || hasStaticNostroPermission && hasStaticVostroPermission) {
            return searchRequest.withPortfolioType(RequestModel.PortfolioType.ALL);
        } else if (hasStaticNostroPermission) {
            return searchRequest.withPortfolioType(RequestModel.PortfolioType.NOSTRO);
        } else if (hasStaticVostroPermission) {
            return searchRequest.withPortfolioType(RequestModel.PortfolioType.VOSTRO);
        } else {
            if (authorizedPortfolios.get().isEmpty()) {
                return searchRequest.withPortfolioType(RequestModel.PortfolioType.NONE);
            } else {
                return searchRequest;
            }
        }
    }

    private RequestModel.SearchRequest applyWalletType(RequestModel.SearchRequest searchRequest,
                                                       boolean hasStaticAccountReadPermission,
                                                       boolean hasStaticWalletPermission,
                                                       boolean hasStaticWalletNostroPermission,
                                                       boolean hasStaticWalletVostroPermission,
                                                       LazyResourceHolder<String> authorizedVenueAccounts) {
        if (hasStaticAccountReadPermission) {
            return searchRequest.withAccountType(RequestModel.AccountType.ALL);
        } else {
            if (hasStaticWalletPermission || (hasStaticWalletNostroPermission && hasStaticWalletVostroPermission)) {
                return searchRequest.withAccountType(RequestModel.AccountType.WALLET)
                    .withWalletType(RequestModel.WalletType.ALL);
            } else if (hasStaticWalletNostroPermission) {
                return searchRequest.withAccountType(RequestModel.AccountType.WALLET)
                    .withWalletType(RequestModel.WalletType.NOSTRO);
            } else if (hasStaticWalletVostroPermission) {
                return searchRequest.withAccountType(RequestModel.AccountType.WALLET)
                    .withWalletType(RequestModel.WalletType.VOSTRO);
            } else {
                if (authorizedVenueAccounts.get().isEmpty()) {
                    return searchRequest.withAccountType(RequestModel.AccountType.NONE).withWalletType(RequestModel.WalletType.NONE);
                } else {
                    return searchRequest;
                }
            }
        }
    }

    private Collection<String> filterPortfolioByType(Collection<String> portfolioIds, RequestModel.PortfolioType
        type) {
        return portfolioIds.stream()
            .map(portfolioProvider::getPortfolio)
            .filter(portfolio -> portfolio.getPortfolioType().name().equals(type.name()))
            .map(Portfolio::getId)
            .toList();
    }

    private Collection<String> filterAccessibleWalletsByType
        (Collection<String> walletIds, RequestModel.WalletType type) {
        return walletIds.stream()
            .map(venueAccountProvider::getVenueAccount)
            .filter(Optional::isPresent)
            .filter(va -> isWalletAccessibleByType(va.get(), type))
            .map(Optional::get)
            .map(VenueAccount::getId)
            .toList();
    }

    private boolean isWalletAccessibleByType(VenueAccount venueAccount, RequestModel.WalletType type) {
        if (AccountType.WALLET.equals(venueAccount.getAccountType())) {

            if (RequestModel.WalletType.VOSTRO == type) {
                return venueAccount.getWalletType().equals(WalletType.WALLET_TYPE_UNSPECIFIED) || venueAccount.getWalletType().equals(WalletType.WALLET_VOSTRO);
            }

            if (RequestModel.WalletType.NOSTRO == type) {
                return venueAccount.getWalletType().equals(WalletType.WALLET_TYPE_UNSPECIFIED) || venueAccount.getWalletType().equals(WalletType.WALLET_NOSTRO);
            }

        }
        return false;
    }
}
