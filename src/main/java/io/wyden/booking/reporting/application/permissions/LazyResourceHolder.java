package io.wyden.booking.reporting.application.permissions;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.function.Supplier;

public class LazyResourceHolder<T>{

    private final Collection<Supplier<Collection<T>>> suppliers = new ArrayList<>();
    private final Collection<LazyResourceHolder<T>> holders = new ArrayList<>();

    private final Collection<T> items = new HashSet<>();

    private boolean initialized = false;

    public LazyResourceHolder(Supplier<Collection<T>> supplier) {
        this.suppliers.add(supplier);
    }

    public Collection<T> get() {
        if (!initialized) {
            suppliers.forEach(holder -> items.addAll(holder.get()));
            holders.forEach(holder -> items.addAll(holder.get()));
            initialized = true;
        }
        return items;
    }

    public void add(Supplier<Collection<T>> supplier) {
        this.suppliers.add(supplier);
    }

    public void add(LazyResourceHolder<T> supplier) {
        this.holders.add(supplier);
    }

    public static <T> LazyResourceHolder<T> empty() {
        return new LazyResourceHolder<>(ArrayList::new);
    }
}


