package io.wyden.booking.reporting.application.referencedata;

import io.wyden.booking.reporting.interfaces.rest.RequestModel;

public record ReferenceDataSnapshot(
    String portfolioId,
    String portfolioCurrency,
    RequestModel.PortfolioType portfolioType,
    String counterPortfolioId,
    String counterPortfolioCurrency,
    RequestModel.PortfolioType counterPortfolioType,
    String feePortfolioId,
    String feePortfolioCurrency,
    RequestModel.PortfolioType feePortfolioType,
    String sourcePortfolioId,
    String sourcePortfolioCurrency,
    RequestModel.PortfolioType sourcePortfolioType,
    String targetPortfolioId,
    String targetPortfolioCurrency,
    RequestModel.PortfolioType targetPortfolioType,
    String accountId,
    String accountCurrency,
    RequestModel.AccountType accountType,
    RequestModel.WalletType walletType,
    String feeAccountId,
    String feeAccountCurrency,
    RequestModel.AccountType feeAccountType,
    RequestModel.WalletType feeWalletType,
    String sourceAccountId,
    String sourceAccountCurrency,
    RequestModel.AccountType sourceAccountType,
    RequestModel.WalletType sourceWalletType,
    String targetAccountId,
    String targetAccountCurrency,
    RequestModel.AccountType targetAccountType,
    RequestModel.WalletType targetWalletType
) {

    public static class Builder {
        private String portfolioId;
        private String portfolioCurrency;
        private RequestModel.PortfolioType portfolioType;
        private String counterPortfolioId;
        private String counterPortfolioCurrency;
        private RequestModel.PortfolioType counterPortfolioType;
        private String feePortfolioId;
        private String feePortfolioCurrency;
        private RequestModel.PortfolioType feePortfolioType;
        private String sourcePortfolioId;
        private String sourcePortfolioCurrency;
        private RequestModel.PortfolioType sourcePortfolioType;
        private String targetPortfolioId;
        private String targetPortfolioCurrency;
        private RequestModel.PortfolioType targetPortfolioType;
        private String accountId;
        private String accountCurrency;
        private RequestModel.AccountType accountType;
        private RequestModel.WalletType walletType;
        private String feeAccountId;
        private String feeAccountCurrency;
        private RequestModel.AccountType feeAccountType;
        private RequestModel.WalletType feeWalletType;
        private String sourceAccountId;
        private String sourceAccountCurrency;
        private RequestModel.AccountType sourceAccountType;
        private RequestModel.WalletType sourceWalletType;
        private String targetAccountId;
        private String targetAccountCurrency;
        private RequestModel.AccountType targetAccountType;
        private RequestModel.WalletType targetWalletType;

        public Builder portfolioId(String portfolioId) {
            this.portfolioId = portfolioId;
            return this;
        }

        public Builder portfolioCurrency(String portfolioCurrency) {
            this.portfolioCurrency = portfolioCurrency;
            return this;
        }

        public Builder portfolioType(RequestModel.PortfolioType portfolioType) {
            this.portfolioType = portfolioType;
            return this;
        }

        public Builder counterPortfolioId(String counterPortfolioId) {
            this.counterPortfolioId = counterPortfolioId;
            return this;
        }

        public Builder counterPortfolioCurrency(String counterPortfolioCurrency) {
            this.counterPortfolioCurrency = counterPortfolioCurrency;
            return this;
        }

        public Builder counterPortfolioType(RequestModel.PortfolioType counterPortfolioType) {
            this.counterPortfolioType = counterPortfolioType;
            return this;
        }

        public Builder feePortfolioId(String feePortfolioId) {
            this.feePortfolioId = feePortfolioId;
            return this;
        }

        public Builder feePortfolioCurrency(String feePortfolioCurrency) {
            this.feePortfolioCurrency = feePortfolioCurrency;
            return this;
        }

        public Builder feePortfolioType(RequestModel.PortfolioType feePortfolioType) {
            this.feePortfolioType = feePortfolioType;
            return this;
        }

        public Builder sourcePortfolioId(String sourcePortfolioId) {
            this.sourcePortfolioId = sourcePortfolioId;
            return this;
        }

        public Builder sourcePortfolioCurrency(String sourcePortfolioCurrency) {
            this.sourcePortfolioCurrency = sourcePortfolioCurrency;
            return this;
        }

        public Builder sourcePortfolioType(RequestModel.PortfolioType sourcePortfolioType) {
            this.sourcePortfolioType = sourcePortfolioType;
            return this;
        }

        public Builder targetPortfolioId(String targetPortfolioId) {
            this.targetPortfolioId = targetPortfolioId;
            return this;
        }

        public Builder targetPortfolioCurrency(String targetPortfolioCurrency) {
            this.targetPortfolioCurrency = targetPortfolioCurrency;
            return this;
        }

        public Builder targetPortfolioType(RequestModel.PortfolioType targetPortfolioType) {
            this.targetPortfolioType = targetPortfolioType;
            return this;
        }

        public Builder accountId(String accountId) {
            this.accountId = accountId;
            return this;
        }

        public Builder accountCurrency(String accountCurrency) {
            this.accountCurrency = accountCurrency;
            return this;
        }

        public Builder accountType(RequestModel.AccountType accountType) {
            this.accountType = accountType;
            return this;
        }

        public Builder walletType(RequestModel.WalletType walletType) {
            this.walletType = walletType;
            return this;
        }

        public Builder feeAccountId(String feeAccountId) {
            this.feeAccountId = feeAccountId;
            return this;
        }

        public Builder feeAccountCurrency(String feeAccountCurrency) {
            this.feeAccountCurrency = feeAccountCurrency;
            return this;
        }

        public Builder feeAccountType(RequestModel.AccountType feeAccountType) {
            this.feeAccountType = feeAccountType;
            return this;
        }

        public Builder feeWalletType(RequestModel.WalletType feeWalletType) {
            this.feeWalletType = feeWalletType;
            return this;
        }

        public Builder sourceAccountId(String sourceAccountId) {
            this.sourceAccountId = sourceAccountId;
            return this;
        }

        public Builder sourceAccountCurrency(String sourceAccountCurrency) {
            this.sourceAccountCurrency = sourceAccountCurrency;
            return this;
        }

        public Builder sourceAccountType(RequestModel.AccountType sourceAccountType) {
            this.sourceAccountType = sourceAccountType;
            return this;
        }

        public Builder sourceWalletType(RequestModel.WalletType sourceWalletType) {
            this.sourceWalletType = sourceWalletType;
            return this;
        }

        public Builder targetAccountId(String targetAccountId) {
            this.targetAccountId = targetAccountId;
            return this;
        }

        public Builder targetAccountCurrency(String targetAccountCurrency) {
            this.targetAccountCurrency = targetAccountCurrency;
            return this;
        }

        public Builder targetAccountType(RequestModel.AccountType targetAccountType) {
            this.targetAccountType = targetAccountType;
            return this;
        }

        public Builder targetWalletType(RequestModel.WalletType targetWalletType) {
            this.targetWalletType = targetWalletType;
            return this;
        }

        public ReferenceDataSnapshot build() {
            return new ReferenceDataSnapshot(
                portfolioId,
                portfolioCurrency,
                portfolioType,
                counterPortfolioId,
                counterPortfolioCurrency,
                counterPortfolioType,
                feePortfolioId,
                feePortfolioCurrency,
                feePortfolioType,
                sourcePortfolioId,
                sourcePortfolioCurrency,
                sourcePortfolioType,
                targetPortfolioId,
                targetPortfolioCurrency,
                targetPortfolioType,
                accountId,
                accountCurrency,
                accountType,
                walletType,
                feeAccountId,
                feeAccountCurrency,
                feeAccountType,
                feeWalletType,
                sourceAccountId,
                sourceAccountCurrency,
                sourceAccountType,
                sourceWalletType,
                targetAccountId,
                targetAccountCurrency,
                targetAccountType,
                targetWalletType
            );
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}