package io.wyden.booking.reporting.application.recovery;

import io.wyden.cloudutils.rabbitmq.InfrastructureException;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

@Component
public class BookingCompletedRecoveryClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(BookingCompletedRecoveryClient.class);
    private static final int DEFAULT_BATCH_SIZE = 100;

    private final RestTemplate restClient;
    private final String bookingSnapshotterUrl;
    private final int batchSize;

    public BookingCompletedRecoveryClient(
        RestTemplate restClient,
        @Value("${booking.snapshotter.host:http://booking-snapshotter:8043}") String bookingSnapshotterUrl,
        @Value("${booking.completed.recovery.batch-size:100}") Integer configuredBatchSize) {
        this.restClient = restClient;
        this.bookingSnapshotterUrl = bookingSnapshotterUrl;
        this.batchSize = configuredBatchSize != null ? configuredBatchSize : DEFAULT_BATCH_SIZE;
    }

    /**
     * Fetches BookingCompleted events after the specified sequence number.
     *
     * @param afterSeqNumber The sequence number to fetch events after
     * @return A result containing the fetched events and whether there are more available
     */
    public BookingCompletedRecoveryResult fetchBookingCompletedEventsAfter(long afterSeqNumber) {
        LOGGER.info("Fetching BookingCompleted events after sequence: {}, batch size: {}", afterSeqNumber, batchSize);

        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(bookingSnapshotterUrl)
                .path("/booking-completed/search")
                .queryParam("first", batchSize)
                .queryParam("after", afterSeqNumber)
                .build()
                .toUri();

            ResponseEntity<CursorConnection> responseEntity = restClient.getForEntity(uri, CursorConnection.class);
            CursorConnection response = responseEntity.getBody();

            if (response == null) {
                LOGGER.error("Received null response from booking-snapshotter service");
                return new BookingCompletedRecoveryResult(List.of(), false);
            }

            List<BookingCompleted> events = new ArrayList<>();
            for (CursorEdge edge : response.getEdgesList()) {
                if (edge.getNode().hasBookingCompleted()) {
                    events.add(edge.getNode().getBookingCompleted());
                }
            }

            boolean hasMore = response.getPageInfo().getHasNextPage();
            LOGGER.info("Fetched {} BookingCompleted events, hasMore: {}", events.size(), hasMore);

            return new BookingCompletedRecoveryResult(events, hasMore);
        } catch (InfrastructureException e) {
            throw new BookingCompletedRecoveryException("Failed to fetch BookingCompleted events after " + afterSeqNumber, e);
        }
    }

    public static class BookingCompletedRecoveryException extends RuntimeException {
        public BookingCompletedRecoveryException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}