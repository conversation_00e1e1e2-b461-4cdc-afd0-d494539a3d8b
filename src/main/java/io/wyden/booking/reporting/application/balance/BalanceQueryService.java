package io.wyden.booking.reporting.application.balance;

import io.wyden.booking.reporting.application.permissions.TransactionSearchRequestAuthorizer;
import io.wyden.booking.reporting.domain.balance.Balance;
import io.wyden.booking.reporting.domain.balance.BalanceRepository;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

import static io.wyden.booking.reporting.application.TransactionService.DEFAULT_PAGE_SIZE;
import static io.wyden.booking.reporting.application.TransactionService.mapDirection;
import static io.wyden.booking.reporting.application.TransactionService.wrapToModel;
import static org.slf4j.LoggerFactory.getLogger;

@Component
public class BalanceQueryService {

    private static final Logger LOGGER = getLogger(BalanceQueryService.class);

    private final BalanceRepository balanceRepository;
    private final TransactionSearchRequestAuthorizer searchRequestAuthorizer;

    public BalanceQueryService(BalanceRepository balanceRepository,
                               TransactionSearchRequestAuthorizer searchRequestAuthorizer) {
        this.balanceRepository = balanceRepository;
        this.searchRequestAuthorizer = searchRequestAuthorizer;
    }

    public PaginationModel.CursorConnection<Balance> search(RequestModel.PositionSearch search) {
        LOGGER.info("Searching for balances for: {}", search);

        Optional<SimpleSearchRequest.AuthorizedSearchRequest> authorizedSearch = searchRequestAuthorizer.authorize(search.toSearchRequest());
        if (authorizedSearch.isEmpty()) {
            LOGGER.warn("User {} has no permissions, processing will be skipped: {}", search.clientId(), search);
            return PaginationModel.emptyCursorConnection();
        }

        RequestModel.AuthorizedPositionSearch authorizedPositionSearch = new RequestModel.AuthorizedPositionSearch(search, authorizedSearch.get());

        Sort.Direction direction = mapDirection(search.sortingOrder());

        Integer pageSize = Objects.requireNonNullElse(search.first(), DEFAULT_PAGE_SIZE);

        Pageable pageable = PageRequest.of(
            0,
            pageSize,
            Sort.by(direction, "id")
        );

        Page<Balance> balances = balanceRepository.findByProperties(authorizedPositionSearch, pageable);
        LOGGER.info("Found {} balances for search request: {}", balances.getNumberOfElements(), authorizedPositionSearch);

        return wrapToModel(balances, reservation -> String.valueOf(reservation.getId()));
    }
}
