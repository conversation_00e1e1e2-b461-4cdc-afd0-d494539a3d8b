package io.wyden.booking.reporting.application.recovery;

import io.wyden.published.booking.PositionSnapshot;

import java.util.List;

public class PositionSnapshotRecoveryResult {
    private final List<PositionSnapshot> positionSnapshotEvents;
    private final boolean hasMoreEvents;

    public PositionSnapshotRecoveryResult(List<PositionSnapshot> positionSnapshotEvents, boolean hasMoreEvents) {
        this.positionSnapshotEvents = positionSnapshotEvents;
        this.hasMoreEvents = hasMoreEvents;
    }

    public List<PositionSnapshot> getPositionSnapshotEvents() {
        return positionSnapshotEvents;
    }

    public boolean hasMoreEvents() {
        return hasMoreEvents;
    }
}