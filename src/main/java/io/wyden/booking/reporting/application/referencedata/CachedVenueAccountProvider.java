package io.wyden.booking.reporting.application.referencedata;

import io.wyden.booking.reporting.domain.rate.SystemCurrencyProvider;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static io.wyden.booking.reporting.interfaces.rabbitmq.mappers.BalanceFromProtoMapper.map;

@Component
public class CachedVenueAccountProvider implements VenueAccountProvider {

    private final VenueAccountCacheFacade venueAccountCacheFacade;

    public CachedVenueAccountProvider(VenueAccountCacheFacade venueAccountCacheFacade) {
        this.venueAccountCacheFacade = venueAccountCacheFacade;
    }

    @Override
    public Optional<VenueAccount> getVenueAccount(String accountId) {
        return venueAccountCacheFacade.find(accountId);
    }

    @Override
    public Optional<AccountSnapshot> getAccountSnapshot(String accountId) {
        return this.venueAccountCacheFacade.find(accountId)
            .map(account -> new AccountSnapshot(
                account.getId(),
                SystemCurrencyProvider.getSystemCurrency(),
                map(account.getAccountType()),
                map(account.getWalletType())));
    }
}
