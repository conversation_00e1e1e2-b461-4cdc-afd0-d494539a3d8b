package io.wyden.booking.reporting.application;

import io.wyden.booking.reporting.application.permissions.TransactionSearchRequestAuthorizer;
import io.wyden.booking.reporting.application.referencedata.AccountSnapshot;
import io.wyden.booking.reporting.application.referencedata.PortfolioProvider;
import io.wyden.booking.reporting.application.referencedata.PortfolioSnapshot;
import io.wyden.booking.reporting.application.referencedata.VenueAccountProvider;
import io.wyden.booking.reporting.domain.ledgerentry.EntryReference;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntryRepository;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.LedgerEntryFromProtoMapper;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.LedgerEntrySnapshot;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static io.wyden.booking.reporting.application.TransactionService.DEFAULT_PAGE_SIZE;
import static io.wyden.booking.reporting.application.TransactionService.mapDirection;
import static io.wyden.booking.reporting.application.TransactionService.wrapToModel;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.slf4j.LoggerFactory.getLogger;

@Component
public class LedgerEntryService {

    private static final Logger LOGGER = getLogger(LedgerEntryService.class);

    private final LedgerEntryRepository ledgerEntryRepository;
    private final TransactionSearchRequestAuthorizer searchRequestAuthorizer;
    private final VenueAccountProvider venueAccountProvider;
    private final PortfolioProvider portfolioProvider;

    public LedgerEntryService(LedgerEntryRepository ledgerEntryRepository,
                              TransactionSearchRequestAuthorizer searchRequestAuthorizer,
                              VenueAccountProvider venueAccountProvider,
                              PortfolioProvider portfolioProvider) {
        this.ledgerEntryRepository = ledgerEntryRepository;
        this.searchRequestAuthorizer = searchRequestAuthorizer;
        this.venueAccountProvider = venueAccountProvider;
        this.portfolioProvider = portfolioProvider;
    }

    public void accept(BookingCompleted bookingCompleted, List<LedgerEntrySnapshot> ledgerEntrySnapshotList) {
        List<LedgerEntry> ledgerEntries = ledgerEntrySnapshotList.stream()
            .flatMap(ledgerEntry -> mapToLedgerEntryStream(ledgerEntry).stream())
            .toList();

        LOGGER.info("Ledger entries {} accepted: {}", ledgerEntries.size(), ledgerEntries);

        ledgerEntryRepository.saveAll(ledgerEntries);
    }

    private Optional<LedgerEntry> mapToLedgerEntryStream(LedgerEntrySnapshot ledgerEntry) {
        String portfolioId = ledgerEntry.getPortfolio();
        String accountId = ledgerEntry.getAccount();

        Optional<PortfolioSnapshot> portfolioSnapshot = portfolioProvider.getPortfolioSnapshot(portfolioId);
        Optional<AccountSnapshot> accountSnapshot = venueAccountProvider.getAccountSnapshot(accountId);

        if (portfolioSnapshot.isEmpty() && accountSnapshot.isEmpty()) {
            throw new IllegalArgumentException("Both portfolio and account data cannot be found for: " + ledgerEntry);
        }

        if (isNotBlank(portfolioId) && portfolioSnapshot.isEmpty()) {
            LOGGER.warn("Portfolio data not found for portfolioId: {}", portfolioId);
            return Optional.empty();
        }

        if (isNotBlank(accountId) && accountSnapshot.isEmpty()) {
            LOGGER.warn("Account data not found for accountId: {}", accountId);
            return Optional.empty();
        }

        return LedgerEntryFromProtoMapper.map(ledgerEntry, portfolioSnapshot.orElse(null), accountSnapshot.orElse(null));
    }

    public PaginationModel.CursorConnection<LedgerEntry> search(RequestModel.LedgerEntrySearch search) {
        LOGGER.info("Searching for ledger entries for: {}", search);

        Optional<SimpleSearchRequest.AuthorizedSearchRequest> authorizedSearch = searchRequestAuthorizer.authorize(search.toSearchRequest());
        if (authorizedSearch.isEmpty()) {
            LOGGER.warn("User {} has no permissions, processing will be skipped: {}", search.clientId(), search);
            return PaginationModel.emptyCursorConnection();
        }

        RequestModel.AuthorizedLedgerEntrySearch authorizedLedgerEntrySearch = new RequestModel.AuthorizedLedgerEntrySearch(search, authorizedSearch.get());

        LOGGER.info("Using authorized search: {}", authorizedLedgerEntrySearch);

        Sort.Direction direction = mapDirection(search.sortingOrder());

        Integer pageSize = Objects.requireNonNullElse(search.first(), DEFAULT_PAGE_SIZE);

        Pageable pageable = PageRequest.of(
            0,
            pageSize,
            Sort.by(direction, "dateTime", "id")
        );

        Page<LedgerEntry> ledgerEntries = ledgerEntryRepository.findByProperties(authorizedLedgerEntrySearch, pageable);
        LOGGER.info("Found {} ledgerEntries for search request: {}", ledgerEntries.getNumberOfElements(), authorizedLedgerEntrySearch);

        return wrapToModel(ledgerEntries, reservation -> String.valueOf(reservation.getId()));
    }

    public Map<EntryReference, List<LedgerEntry>> groupByReservationRef(String reservationRef) {
        return ledgerEntryRepository.findByReservationRef(reservationRef).stream()
            .collect(Collectors.groupingBy(le -> new EntryReference(le.getPortfolioId(), le.getAccountId(), le.getSymbol())));
    }
}
