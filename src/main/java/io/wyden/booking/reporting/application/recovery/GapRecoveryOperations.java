package io.wyden.booking.reporting.application.recovery;

import io.wyden.cloudutils.rabbitmq.InfrastructureException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.function.LongFunction;
import java.util.function.Predicate;
import java.util.function.ToLongFunction;

/**
 * Operations class containing common gap recovery logic that can be shared
 * across different event types. Keeps the logic simple and readable.
 */
public final class GapRecoveryOperations {

    private static final Logger LOGGER = LoggerFactory.getLogger(GapRecoveryOperations.class);
    
    private GapRecoveryOperations() {
        // Utility class - prevent instantiation
    }

    /**
     * Generic gap recovery logic that can be used by any event type
     *
     * @param expectedSequence The starting point of the gap (inclusive)
     * @param receivedSequence The sequence number of the first non-missing event (exclusive)
     * @param eventTypeName Name of the event type for logging
     * @param fetchFunction Function that fetches events after a given sequence number
     * @param eventsExtractor Function that extracts events list from result
     * @param hasMoreExtractor Function that extracts hasMore flag from result
     * @param sequenceExtractor Function that extracts sequence number from an event
     * @param <T> The event type
     * @param <R> The result type returned by fetchFunction
     * @return List of recovered events
     */
    public static <T, R> List<T> recoverGap(
            long expectedSequence, 
            long receivedSequence,
            String eventTypeName,
            LongFunction<R> fetchFunction,
            Function<R, List<T>> eventsExtractor,
            Predicate<R> hasMoreExtractor,
            ToLongFunction<T> sequenceExtractor) {
        
        if (eventTypeName == null || fetchFunction == null || eventsExtractor == null || 
            hasMoreExtractor == null || sequenceExtractor == null) {
            throw new IllegalArgumentException("All parameters must not be null");
        }
        
        long toSeqNum = receivedSequence - 1;
        LOGGER.info("Starting {} gap recovery from sequence {} to {}", eventTypeName, expectedSequence, toSeqNum);

        if (expectedSequence > receivedSequence) {
            LOGGER.warn("Expected sequence {} is greater than received sequence {}. No gap to recover.", expectedSequence, receivedSequence);
            return new ArrayList<>();
        }

        List<T> recoveredEvents = new ArrayList<>();

        try {
            long currentSeq = expectedSequence - 1;
            boolean hasMoreEvents = true;

            while (hasMoreEvents && currentSeq < toSeqNum) {
                R result = fetchFunction.apply(currentSeq);
                List<T> events = eventsExtractor.apply(result);

                if (events.isEmpty()) {
                    LOGGER.warn("No {} events returned in recovery batch after sequence {}", eventTypeName, currentSeq);
                    return recoveredEvents;
                }

                for (T event : events) {
                    currentSeq = sequenceExtractor.applyAsLong(event);
                    if (currentSeq <= toSeqNum) {
                        recoveredEvents.add(event);
                    }
                }

                hasMoreEvents = hasMoreExtractor.test(result);
            }

            LOGGER.info("{} gap recovery completed successfully from {} to {}", eventTypeName, expectedSequence, toSeqNum);
            return recoveredEvents;
        } catch (Exception e) {
            LOGGER.error("Error during {} gap recovery", eventTypeName, e);
            return recoveredEvents;
        }
    }

    /**
     * Generic startup recovery logic that can be used by any event type
     *
     * @param lastProcessedSequence The last sequence number that was successfully processed
     * @param eventTypeName Name of the event type for logging
     * @param fetchFunction Function that fetches events after a given sequence number
     * @param eventsExtractor Function that extracts events list from result
     * @param hasMoreExtractor Function that extracts hasMore flag from result
     * @param sequenceExtractor Function that extracts sequence number from an event
     * @param <T> The event type
     * @param <R> The result type returned by fetchFunction
     * @return List of recovered events
     */
    public static <T, R> List<T> recoverAllMissingEvents(
            long lastProcessedSequence,
            String eventTypeName,
            LongFunction<R> fetchFunction,
            Function<R, List<T>> eventsExtractor,
            Predicate<R> hasMoreExtractor,
            ToLongFunction<T> sequenceExtractor) {
        
        if (eventTypeName == null || fetchFunction == null || eventsExtractor == null || 
            hasMoreExtractor == null || sequenceExtractor == null) {
            throw new IllegalArgumentException("All parameters must not be null");
        }
        
        LOGGER.info("Starting recovery of all missing {} events after sequence {}", eventTypeName, lastProcessedSequence);

        List<T> recoveredEvents = new ArrayList<>();
        try {
            long currentSeq = lastProcessedSequence;
            boolean hasMoreEvents = true;

            while (hasMoreEvents) {
                R result = fetchFunction.apply(currentSeq);
                List<T> events = eventsExtractor.apply(result);

                if (events.isEmpty()) {
                    LOGGER.warn("No {} events returned in recovery batch after sequence {}", eventTypeName, currentSeq);
                    return recoveredEvents;
                }

                for (T event : events) {
                    long seqNumber = sequenceExtractor.applyAsLong(event);
                    recoveredEvents.add(event);
                    currentSeq = seqNumber;
                }

                hasMoreEvents = hasMoreExtractor.test(result);
            }

            LOGGER.info("Startup recovery completed successfully, processed {} {} events", recoveredEvents.size(), eventTypeName);
            return recoveredEvents;
        } catch (Exception e) {
            throw new InfrastructureException("Failed to recover missing " + eventTypeName + " events during startup", e);
        }
    }
}