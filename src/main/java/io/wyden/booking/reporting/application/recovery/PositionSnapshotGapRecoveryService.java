package io.wyden.booking.reporting.application.recovery;

import io.wyden.published.booking.PositionSnapshot;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service class responsible for recovering sequence gaps in PositionSnapshot events.
 */
@Service
public class PositionSnapshotGapRecoveryService {

    private final PositionSnapshotRecoveryClient recoveryClient;

    public PositionSnapshotGapRecoveryService(PositionSnapshotRecoveryClient recoveryClient) {
        this.recoveryClient = recoveryClient;
    }

    /**
     * Recovers a sequence gap between the expected and received event sequences.
     */
    public List<PositionSnapshot> recoverGap(long expectedSequence, long receivedSequence) {
        return GapRecoveryOperations.recoverGap(
            expectedSequence,
            receivedSequence,
            "PositionSnapshot",
            recoveryClient::fetchPositionSnapshotEventsAfter,
            PositionSnapshotRecoveryResult::getPositionSnapshotEvents,
            PositionSnapshotRecoveryResult::hasMoreEvents,
            PositionSnapshot::getSequenceNumber
        );
    }

    /**
     * Recovers all events from the last processed sequence up to the current state.
     * This is used during system startup to ensure no events are missed.
     */
    public List<PositionSnapshot> recoverAllMissingEvents(long lastProcessedSequence) {
        return GapRecoveryOperations.recoverAllMissingEvents(
            lastProcessedSequence,
            "PositionSnapshot",
            recoveryClient::fetchPositionSnapshotEventsAfter,
            PositionSnapshotRecoveryResult::getPositionSnapshotEvents,
            PositionSnapshotRecoveryResult::hasMoreEvents,
            PositionSnapshot::getSequenceNumber
        );
    }
}