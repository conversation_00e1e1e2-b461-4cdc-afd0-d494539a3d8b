package io.wyden.booking.reporting.application;

import io.wyden.booking.reporting.application.permissions.TransactionSearchRequestAuthorizer;
import io.wyden.booking.reporting.application.referencedata.ReferenceDataResolver;
import io.wyden.booking.reporting.application.referencedata.ReferenceDataSnapshot;
import io.wyden.booking.reporting.domain.transaction.Transaction;
import io.wyden.booking.reporting.domain.transaction.TransactionRepository;
import io.wyden.booking.reporting.interfaces.rabbitmq.TransactionCreatedEventEmitter;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.TransactionFromProtoMapper;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.rest.pagination.PaginationWrapper;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.TransactionSnapshot;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.slf4j.LoggerFactory.getLogger;

@Component
public class TransactionService {

    private static final Logger LOGGER = getLogger(TransactionService.class);
    public static final int DEFAULT_PAGE_SIZE = 100;

    private final ReferenceDataResolver referenceDataResolver;
    private final TransactionRepository transactionRepository;
    private final TransactionSearchRequestAuthorizer transactionSearchRequestAuthorizer;
    private final TransactionCreatedEventEmitter transactionCreatedEventEmitter;

    public TransactionService(ReferenceDataResolver referenceDataResolver,
                              TransactionRepository transactionRepository,
                              TransactionSearchRequestAuthorizer transactionSearchRequestAuthorizer,
                              TransactionCreatedEventEmitter transactionCreatedEventEmitter) {
        this.referenceDataResolver = referenceDataResolver;
        this.transactionRepository = transactionRepository;
        this.transactionSearchRequestAuthorizer = transactionSearchRequestAuthorizer;
        this.transactionCreatedEventEmitter = transactionCreatedEventEmitter;
    }

    public void accept(BookingCompleted bookingCompleted, TransactionSnapshot transactionSnapshot) {
        ReferenceDataSnapshot referenceDataSnapshot = referenceDataResolver.resolve(transactionSnapshot);

        TransactionFromProtoMapper.map(transactionSnapshot, bookingCompleted.getSequenceNumber(), referenceDataSnapshot)
            .ifPresentOrElse(
                transaction -> {
                    LOGGER.info("Transaction accepted: {}", transaction);
                    transactionRepository.save(transaction);
                    transactionCreatedEventEmitter.emit(transaction);
                },
                () -> LOGGER.error("Transaction processing failed on mapping step: {}", transactionSnapshot)
            );
    }

    public Optional<Transaction> findByUuid(String transactionId) {
        return transactionRepository.findByUuid(transactionId);
    }

    public PaginationModel.CursorConnection<Transaction> search(RequestModel.TransactionSearch transactionSearch) {
        LOGGER.info("Searching transactions for: {}", transactionSearch);

        Optional<SimpleSearchRequest.AuthorizedSearchRequest> authorizedSearch = transactionSearchRequestAuthorizer.authorize(transactionSearch.toSearchRequest());

        if (authorizedSearch.isEmpty()) {
            LOGGER.warn("User {} has no permissions, processing will be skipped: {}", transactionSearch.clientId(), transactionSearch);
            return empty();
        }

        RequestModel.AuthorizedTransactionSearch search = new RequestModel.AuthorizedTransactionSearch(transactionSearch, authorizedSearch.get());

        LOGGER.info("Using authorized search: {}", search);

        Sort.Direction direction = mapDirection(transactionSearch.sortingOrder());

        Integer pageSize = Objects.requireNonNullElse(transactionSearch.first(), DEFAULT_PAGE_SIZE);

        Pageable pageable = PageRequest.of(
            0,
            pageSize,
            Sort.by(direction, "dateTime", "id")
        );

        Page<Transaction> transactions = transactionRepository.findByProperties(search, pageable);
        LOGGER.info("Found {} transactions for search request: {}", transactions.getNumberOfElements(), search);

        return wrapToModel(transactions, transaction -> String.valueOf(transaction.getId()));
    }

    public static Sort.@NotNull Direction mapDirection(RequestModel.SortingOrder sortingOrder) {
        if (sortingOrder == RequestModel.SortingOrder.DESC) {
            return Sort.Direction.DESC;
        }

        if (sortingOrder == RequestModel.SortingOrder.ASC) {
            return Sort.Direction.ASC;
        }

        return Sort.Direction.ASC;
    }

    /**
     * Wraps a Spring Data Page into a CursorConnection model
     */
    public static <T extends Serializable> PaginationModel.CursorConnection<T> wrapToModel(Page<T> page,
                                                                                           Function<T, String> cursorExtractor) {

        if (page == null || page.isEmpty()) {
            return PaginationModel.emptyCursorConnection();
        }

        List<PaginationModel.CursorEdge<T>> cursorEdges = page.stream()
            .map(item -> new PaginationModel.CursorEdge<>(item, cursorExtractor.apply(item)))
            .toList();

        String endCursor = PaginationWrapper.getLast(cursorEdges)
            .map(PaginationModel.CursorEdge::cursor)
            .orElse(EMPTY);

        int pageSize = page.getNumberOfElements();
        boolean hasNextPage = page.hasNext();
        Long totalSize = page.getTotalElements();

        PaginationModel.PageInfo pageInfo = new PaginationModel.PageInfo(
            hasNextPage,
            endCursor,
            pageSize,
            totalSize);

        return new PaginationModel.CursorConnection<>(cursorEdges, pageInfo);
    }

    public static <T extends Serializable> PaginationModel.CursorConnection<T> empty() {
        return new PaginationModel.CursorConnection<>(
            List.of(),
            new PaginationModel.PageInfo(false, EMPTY, 0, 0L)
        );
    }
}
