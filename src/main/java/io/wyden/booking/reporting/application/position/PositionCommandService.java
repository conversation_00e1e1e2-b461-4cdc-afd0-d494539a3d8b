package io.wyden.booking.reporting.application.position;

import io.wyden.booking.reporting.application.RateService;
import io.wyden.booking.reporting.application.referencedata.AccountSnapshot;
import io.wyden.booking.reporting.application.referencedata.PortfolioProvider;
import io.wyden.booking.reporting.application.referencedata.PortfolioSnapshot;
import io.wyden.booking.reporting.application.referencedata.VenueAccountProvider;
import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.booking.reporting.domain.position.PositionRepository;
import io.wyden.booking.reporting.domain.rate.Rate;
import io.wyden.booking.reporting.domain.rate.RateValidator;
import io.wyden.booking.reporting.domain.rate.SystemCurrencyProvider;
import io.wyden.booking.reporting.interfaces.rabbitmq.PositionSnapshotEventEmitter;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.PositionFromProtoMapper;
import io.wyden.published.booking.PositionSnapshot;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.slf4j.LoggerFactory.getLogger;

@Service
public class PositionCommandService {

    private static final Logger LOGGER = getLogger(PositionCommandService.class);

    private final PositionRepository positionRepository;
    private final RateService rateService;
    private final RateValidator rateValidator;
    private final VenueAccountProvider venueAccountProvider;
    private final PortfolioProvider portfolioProvider;
    private final PositionSnapshotEventEmitter positionSnapshotEventEmitter;

    public PositionCommandService(PositionRepository positionRepository,
                                  RateService rateService,
                                  RateValidator rateValidator,
                                  VenueAccountProvider venueAccountProvider,
                                  PortfolioProvider portfolioProvider,
                                  PositionSnapshotEventEmitter positionSnapshotEventEmitter) {
        this.positionRepository = positionRepository;
        this.rateService = rateService;
        this.rateValidator = rateValidator;
        this.venueAccountProvider = venueAccountProvider;
        this.portfolioProvider = portfolioProvider;
        this.positionSnapshotEventEmitter = positionSnapshotEventEmitter;
    }

    @Transactional
    public Position recalculatePosition(Position position) {
        LOGGER.debug("Recalculating position: {}", position);

        String positionCurrency = position.getCurrency();
        String referenceCurrency = position.getReferenceCurrency();
        String systemCurrency = SystemCurrencyProvider.getSystemCurrency();

        try {
            // Example: BTC Position for EUR portfolio
            // rate1: BTC/EUR
            Optional<Rate> instrumentToReferenceCurrencyRate = rateService.findRate(positionCurrency, referenceCurrency);
            // rate2: EUR/USD
            Optional<Rate> referenceToSystemCurrencyRate = rateService.findRate(referenceCurrency, systemCurrency);

            if (instrumentToReferenceCurrencyRate.isEmpty()) {
                LOGGER.warn("Failed to find rate for instrument: {}/{}", positionCurrency, referenceCurrency);
                return position;
            }

            if (referenceToSystemCurrencyRate.isEmpty()) {
                LOGGER.warn("Failed to find rate for reference: {}/{}", referenceCurrency, systemCurrency);
                return position;
            }

            LOGGER.debug("Found rates for position recalculation: {} and {}", instrumentToReferenceCurrencyRate, referenceToSystemCurrencyRate);

            Position updatedPosition = position.recalculatedUsingRates(rateValidator, instrumentToReferenceCurrencyRate.get(), referenceToSystemCurrencyRate.get());
            Position savedPosition = positionRepository.save(updatedPosition);
            positionSnapshotEventEmitter.emit(savedPosition);
            return savedPosition;

        } catch (Exception e) {
            LOGGER.warn("Failed to calculate position metrics for position: {}", position, e);
        }

        return position;
    }

    @Transactional
    public void handlePositionSnapshot(PositionSnapshot positionSnapshot) {
        LOGGER.debug("Processing PositionSnapshot: symbol={}, account={}, portfolio={}",
            positionSnapshot.getSymbol(), positionSnapshot.getAccount(), positionSnapshot.getPortfolio());

        String symbol = positionSnapshot.getSymbol();
        String accountId = positionSnapshot.getAccount();
        String portfolioId =  positionSnapshot.getPortfolio();

        Optional<Position> existing = positionRepository.findBySymbolAndAccountIdOrPortfolioId(symbol, accountId, portfolioId);

        // TODO SPL: Only update if the new snapshot has a higher or equal sequence number
        // Note: For now, we don't have sequence number in PositionSnapshot, so we always update
        // This can be enhanced when sequence number is added to the PositionSnapshot proto
        // if (existing.isPresent() && existing.get().getSequenceNumber() >= sequenceNumber) {
        //     LOGGER.warn("Position snapshot with sequence number already exists. Existing sequenceNumer: {}, received sequence number: {}. Not applying older snapshot.", existing.get().getSequenceNumber(), sequenceNumber);
        //     continue;
        // }

        Optional<PortfolioSnapshot> portfolioSnapshot = portfolioProvider.getPortfolioSnapshot(portfolioId);
        Optional<AccountSnapshot> accountSnapshot = venueAccountProvider.getAccountSnapshot(accountId);

        if (portfolioSnapshot.isEmpty() && accountSnapshot.isEmpty()) {
            throw new IllegalArgumentException("Both portfolio and account data cannot be found for: " + positionSnapshot);
        }

        if (isNotBlank(portfolioId) && portfolioSnapshot.isEmpty()) {
            LOGGER.warn("Portfolio data not found for portfolioId: {}", portfolioId);
            return;
        }

        if (isNotBlank(accountId) && accountSnapshot.isEmpty()) {
            LOGGER.warn("Account data not found for accountId: {}", accountId);
            return;
        }

        Position toSave = PositionFromProtoMapper.map(positionSnapshot, portfolioSnapshot.orElse(null), accountSnapshot.orElse(null));

        existing.ifPresent(e -> {
            toSave.setId(e.getId());
            toSave.setCreatedAt(e.getCreatedAt());
            toSave.setCreatedBy(e.getCreatedBy());
            toSave.setVersion(e.getVersion());
        });

        Position saved = positionRepository.save(toSave);

        LOGGER.info("Saved new position snapshot with sequence number: {}", saved.getSequenceNumber());
    }
}
