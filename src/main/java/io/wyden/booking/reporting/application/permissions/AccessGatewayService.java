package io.wyden.booking.reporting.application.permissions;

import io.wyden.accessgateway.client.permission.Permission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collection;
import java.util.Optional;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Component
public class AccessGatewayService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccessGatewayService.class);

    private final AccessGatewayFacade accessGatewayFacade;

    public AccessGatewayService(AccessGatewayFacade accessGatewayFacade) {
        this.accessGatewayFacade = accessGatewayFacade;
    }

    public AccessGatewayFacade.User getUserAttributes(String username) {
        return accessGatewayFacade.getUserAttributes(username);
    }

    public Collection<String> selectAuthorizedVenueAccounts(AccessGatewayFacade.User user, Collection<String> requested) {
        return selectAuthorizedResources(user, Permission.VENUE_ACCOUNT_READ, requested);
    }

    public Collection<String> selectAuthorizedPortfolios(AccessGatewayFacade.User user, Collection<String> requested) {
        return selectAuthorizedResources(user, Permission.PORTFOLIO_READ, requested);
    }

    public boolean hasVenueAccountStaticPermission(AccessGatewayFacade.User user) {
        return hasStaticPermission(user, Permission.VENUE_ACCOUNT_READ);
    }

    public boolean hasPortfolioStaticPermission(AccessGatewayFacade.User user) {
        return hasStaticPermission(user, Permission.PORTFOLIO_READ);
    }

    public boolean hasStaticPermission(AccessGatewayFacade.User user, Permission permission) {
        return accessGatewayFacade.hasPermission(user.roles(), user.groups(), user.clientId(), permission);
    }

    private Collection<String> selectAuthorizedResources(AccessGatewayFacade.User user, Permission permission, Collection<String> requestedResources) {
        if (user == null || isBlank(user.clientId())) {
            LOGGER.debug("Cannot find authorized {}. ClientId is missing. Assuming all requested resources are selected: {}", permission, requestedResources);
            return requestedResources;
        }

        boolean hasFullStaticPermission = hasStaticPermission(user, permission);
        boolean hasVostroPlusNostroStaticPermission = getCorespondentNostroPermission(permission).map(p -> hasStaticPermission(user, p)).orElse(false)
            && getCorespondentVostroPermission(permission).map(p -> hasStaticPermission(user, p)).orElse(false);

        if (hasFullStaticPermission || hasVostroPlusNostroStaticPermission) {
            LOGGER.debug("Static permission found {} for {}. Assuming all requested resources are selected: {}", permission, user.clientId(), requestedResources);
            return requestedResources;
        }

        LOGGER.debug("Find authorized {} for user {} with request {}", permission, user.clientId(), requestedResources);

        Collection<String> authorizedResources = findResourceIdByResourceType(user, permission);
        Collection<String> selectedResources = selectAuthorized(requestedResources, authorizedResources);
        LOGGER.debug("Found authorized {} resources for user {} with request {}: {}", permission, user.clientId(), requestedResources, selectedResources);

        return selectedResources;
    }

    public Collection<String> findResourceIdByResourceType(AccessGatewayFacade.User user, Permission permission) {
        return accessGatewayFacade.getPermittedResourceIds(user.groups(), user.clientId(), permission.getResource(), permission.getScope());
    }

    private Collection<String> selectAuthorized(Collection<String> requested, Collection<String> authorized) {
        if (requested.isEmpty()) {
            // all resources that are authorized are selected
            return authorized;
        }

        // only resources on 'authorized' list are selected
        return requested.stream()
            .filter(authorized::contains)
            .toList();
    }

    public Collection<String> authorizeVenueAccounts(AccessGatewayFacade.User user, Collection<String> requested) {
        return authorizeResources(user, Permission.VENUE_ACCOUNT_READ, requested);
    }

    public Collection<String> authorizePortfolios(AccessGatewayFacade.User user, Collection<String> requested) {
        return authorizeResources(user, Permission.PORTFOLIO_READ, requested);
    }

    private Collection<String> authorizeResources(AccessGatewayFacade.User user, Permission permission, Collection<String> requestedResources) {
        if (user == null || isBlank(user.clientId())) {
            LOGGER.debug("Cannot find authorized {}. ClientId is missing. Assuming all requested resources are selected: {}", permission, requestedResources);
            return requestedResources;
        }
        if (hasStaticPermission(user, permission)) {
            LOGGER.debug("Static permission found {} for {}. Assuming all requested resources are selected: {}", permission, user.clientId(), requestedResources);
            return requestedResources;
        }

        LOGGER.debug("Find authorized {} for user {} with request {}", permission, user.clientId(), requestedResources);

        Collection<String> authorizedResources = findResourceIdByResourceType(user, permission);
        Collection<String> selectedResources = authorize(requestedResources, authorizedResources);
        LOGGER.debug("Found authorized {} resources for user {} with request {}: {}", permission, user.clientId(), requestedResources, selectedResources);

        return selectedResources;
    }

    private Collection<String> authorize(Collection<String> requested, Collection<String> authorized) {
        // only resources on 'authorized' list are selected
        return requested.stream()
            .filter(authorized::contains)
            .toList();
    }

    private Optional<Permission> getCorespondentVostroPermission(Permission permission) {
        if (!permission.getResource().equals(Permission.Resources.PORTFOLIO)) {
            return Optional.empty();
        }

        return Arrays.stream(Permission.values())
            .filter(p -> p.getResource().equals(Permission.Resources.PORTFOLIO_VOSTRO))
            .filter(p -> p.getScope().equals(permission.getScope()))
            .findFirst();
    }

    private Optional<Permission> getCorespondentNostroPermission(Permission permission) {
        if (!permission.getResource().equals(Permission.Resources.PORTFOLIO)) {
            return Optional.empty();
        }

        return Arrays.stream(Permission.values())
            .filter(p -> p.getResource().equals(Permission.Resources.PORTFOLIO_NOSTRO))
            .filter(p -> p.getScope().equals(permission.getScope()))
            .findFirst();
    }
}
