package io.wyden.booking.reporting.application;

import io.wyden.booking.reporting.application.permissions.TransactionSearchRequestAuthorizer;
import io.wyden.booking.reporting.application.referencedata.ReferenceDataResolver;
import io.wyden.booking.reporting.application.referencedata.ReferenceDataSnapshot;
import io.wyden.booking.reporting.domain.ledgerentry.EntryReference;
import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.reporting.domain.reservation.Reservation;
import io.wyden.booking.reporting.domain.reservation.ReservationBalance;
import io.wyden.booking.reporting.domain.reservation.ReservationRepository;
import io.wyden.booking.reporting.interfaces.rabbitmq.mappers.ReservationFromProtoMapper;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.ReservationSnapshot;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static io.wyden.booking.reporting.application.TransactionService.DEFAULT_PAGE_SIZE;
import static io.wyden.booking.reporting.application.TransactionService.empty;
import static io.wyden.booking.reporting.application.TransactionService.mapDirection;
import static io.wyden.booking.reporting.application.TransactionService.wrapToModel;
import static org.slf4j.LoggerFactory.getLogger;

@Component
public class ReservationService {

    private static final Logger LOGGER = getLogger(ReservationService.class);

    private final ReservationRepository reservationRepository;
    private final TransactionSearchRequestAuthorizer transactionSearchRequestAuthorizer;
    private final ReferenceDataResolver referenceDataResolver;
    private final LedgerEntryService ledgerEntryService;

    public ReservationService(ReservationRepository reservationRepository,
                              TransactionSearchRequestAuthorizer transactionSearchRequestAuthorizer,
                              ReferenceDataResolver referenceDataResolver, LedgerEntryService ledgerEntryService) {
        this.reservationRepository = reservationRepository;
        this.transactionSearchRequestAuthorizer = transactionSearchRequestAuthorizer;
        this.referenceDataResolver = referenceDataResolver;
        this.ledgerEntryService = ledgerEntryService;
    }

    public void accept(BookingCompleted bookingCompleted, ReservationSnapshot reservationSnapshot) {
        ReferenceDataSnapshot referenceDataSnapshot = referenceDataResolver.resolve(reservationSnapshot);

        ReservationFromProtoMapper.map(reservationSnapshot, bookingCompleted.getSequenceNumber(), referenceDataSnapshot)
            .ifPresent(reservation -> {
                LOGGER.info("Reservation accepted: {}", reservation);
                reservationRepository.save(reservation);
            });
    }

    public Optional<Reservation> findByReservationRef(String reservationRef) {
        return reservationRepository.findByReservationRef(reservationRef);
    }

    public Collection<ReservationBalance> collectByReservationRef(String reservationRef) {
        // find all ledger entries for reservationRef
        // group them by portfolio (or account) + instrument
        Map<EntryReference, List<LedgerEntry>> ledgerEntries = ledgerEntryService.groupByReservationRef(reservationRef);

        return ledgerEntries.entrySet().stream()
            .map(entry -> {
                BigDecimal outstandingReservationValue = entry.getValue().stream()
                    .filter(le -> le.getLedgerEntryType().isReservationOrRelease())
                    .map(LedgerEntry::getQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                String portfolioId = entry.getKey().portfolioId();
                String accountId = entry.getKey().accountId();
                String symbol = entry.getKey().symbol();

                return new ReservationBalance(outstandingReservationValue, portfolioId, accountId, symbol);
            })
            .toList();
    }

    public PaginationModel.CursorConnection<Reservation> search(RequestModel.ReservationSearch reservationSearch) {
        LOGGER.info("Searching for reservations for: {}", reservationSearch);

        Optional<SimpleSearchRequest.AuthorizedSearchRequest> authorizedSearch = transactionSearchRequestAuthorizer.authorize(reservationSearch.toSearchRequest());

        if (authorizedSearch.isEmpty()) {
            LOGGER.warn("User {} has no permissions, processing will be skipped: {}", reservationSearch.clientId(), reservationSearch);
            return empty();
        }

        RequestModel.AuthorizedReservationSearch search = new RequestModel.AuthorizedReservationSearch(reservationSearch, authorizedSearch.get());

        LOGGER.info("Using authorized search: {}", search);

        Sort.Direction direction = mapDirection(reservationSearch.sortingOrder());

        Integer pageSize = Objects.requireNonNullElse(reservationSearch.first(), DEFAULT_PAGE_SIZE);

        Pageable pageable = PageRequest.of(
            0,
            pageSize,
            Sort.by(direction, "dateTime", "id")
        );

        Page<Reservation> reservations = reservationRepository.findByProperties(search, pageable);
        LOGGER.info("Found {} reservations for search request: {}", reservations.getNumberOfElements(), search);

        return wrapToModel(reservations, reservation -> String.valueOf(reservation.getId()));
    }
}
