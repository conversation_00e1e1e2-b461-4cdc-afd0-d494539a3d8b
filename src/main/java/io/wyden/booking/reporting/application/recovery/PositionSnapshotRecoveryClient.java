package io.wyden.booking.reporting.application.recovery;

import io.wyden.published.booking.PositionSnapshot;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

@Component
public class PositionSnapshotRecoveryClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(PositionSnapshotRecoveryClient.class);
    private static final int DEFAULT_BATCH_SIZE = 100;

    private final RestTemplate restClient;
    private final String bookingPnlUrl;
    private final int batchSize;

    public PositionSnapshotRecoveryClient(
        RestTemplate restClient,
        @Value("${booking.pnl.host:http://booking-pnl:8080}") String bookingPnlUrl,
        @Value("${position.snapshot.recovery.batch-size:100}") Integer configuredBatchSize) {
        this.restClient = restClient;
        this.bookingPnlUrl = bookingPnlUrl;
        this.batchSize = configuredBatchSize != null ? configuredBatchSize : DEFAULT_BATCH_SIZE;
    }

    /**
     * Fetches PositionSnapshot events after the specified sequence number.
     *
     * @param afterSeqNumber The sequence number to fetch events after
     * @return A result containing the fetched events and whether there are more available
     */
    public PositionSnapshotRecoveryResult fetchPositionSnapshotEventsAfter(long afterSeqNumber) {
        LOGGER.info("Fetching PositionSnapshot events after sequence: {}, batch size: {}", afterSeqNumber, batchSize);

        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(bookingPnlUrl)
                .path("/position-snapshot/search")
                .queryParam("first", batchSize)
                .queryParam("after", afterSeqNumber)
                .build()
                .toUri();

            ResponseEntity<CursorConnection> responseEntity = restClient.getForEntity(uri, CursorConnection.class);
            CursorConnection response = responseEntity.getBody();

            if (response == null) {
                LOGGER.error("Received null response from booking-pnl service");
                return new PositionSnapshotRecoveryResult(List.of(), false);
            }

            List<PositionSnapshot> events = new ArrayList<>();
            for (CursorEdge edge : response.getEdgesList()) {
                if (edge.getNode().hasPosition()) {
                    events.add(edge.getNode().getPosition());
                }
            }

            boolean hasMore = response.getPageInfo().getHasNextPage();
            LOGGER.info("Fetched {} PositionSnapshot events, hasMore: {}", events.size(), hasMore);

            return new PositionSnapshotRecoveryResult(events, hasMore);
        } catch (Exception e) {
            throw new PositionSnapshotRecoveryException("Failed to fetch PositionSnapshot events after " + afterSeqNumber, e);
        }
    }

    public static class PositionSnapshotRecoveryException extends RuntimeException {
        public PositionSnapshotRecoveryException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}