package io.wyden.booking.reporting.application.permissions;

import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.booking.reporting.application.referencedata.PortfolioProvider;
import io.wyden.booking.reporting.application.referencedata.VenueAccountProvider;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.WalletType;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Optional;

@Component
public class TransactionSearchRequestAuthorizer {

    private final AccessGatewayService accessGatewayService;
    private final PortfolioProvider portfolioProvider;
    private final VenueAccountProvider venueAccountProvider;

    public TransactionSearchRequestAuthorizer(AccessGatewayService accessGatewayService, PortfolioProvider portfolioProvider, VenueAccountProvider venueAccountProvider) {
        this.accessGatewayService = accessGatewayService;
        this.portfolioProvider = portfolioProvider;
        this.venueAccountProvider = venueAccountProvider;
    }

    @SuppressWarnings({"java:S2589", "java:S6541", "java:S3776"})
    public Optional<SimpleSearchRequest.AuthorizedSearchRequest> authorize(SimpleSearchRequest search) {

        AccessGatewayFacade.User user = accessGatewayService.getUserAttributes(search.clientId());

        boolean allAccountsAuthorized = accessGatewayService.hasVenueAccountStaticPermission(user);
        boolean hasWalletStaticPermission = accessGatewayService.hasStaticPermission(user, Permission.WALLET_READ);
        boolean hasWalletNostroStaticPermission = accessGatewayService.hasStaticPermission(user, Permission.WALLET_NOSTRO_READ);
        boolean hasWalletVostroStaticPermission = accessGatewayService.hasStaticPermission(user, Permission.WALLET_VOSTRO_READ);

        boolean allPortfoliosAuthorized = accessGatewayService.hasPortfolioStaticPermission(user);
        boolean hasPortfolioNostroStaticPermission = accessGatewayService.hasStaticPermission(user, Permission.PORTFOLIO_NOSTRO_READ);
        boolean hasPortfolioVostroStaticPermission = accessGatewayService.hasStaticPermission(user, Permission.PORTFOLIO_VOSTRO_READ);

        LazyResourceHolder<String> authorizedPortfolios = new LazyResourceHolder<>(() -> accessGatewayService.findResourceIdByResourceType(user, Permission.PORTFOLIO_READ));
        LazyResourceHolder<String> requestedAuthorizedPortfolios = allPortfoliosAuthorized || (hasPortfolioNostroStaticPermission && hasPortfolioVostroStaticPermission)
            ? new LazyResourceHolder<>(search::portfolioIds)
            : new LazyResourceHolder<>(() -> accessGatewayService.authorizePortfolios(user, search.portfolioIds()));

        LazyResourceHolder<String> authorizedVenueAccounts = new LazyResourceHolder<>(() -> accessGatewayService.findResourceIdByResourceType(user, Permission.VENUE_ACCOUNT_READ));
        LazyResourceHolder<String> requestedAuthorizedVenueAccounts = allAccountsAuthorized
            ? new LazyResourceHolder<>(search::accountIds)
            : new LazyResourceHolder<>(() -> accessGatewayService.authorizeVenueAccounts(user, search.accountIds()));

        if (!allAccountsAuthorized
            && !allPortfoliosAuthorized
            && !hasPortfolioNostroStaticPermission
            && !hasPortfolioVostroStaticPermission
            && !hasWalletStaticPermission
            && !hasWalletVostroStaticPermission
            && !hasWalletNostroStaticPermission
            && authorizedVenueAccounts.get().isEmpty()
            && authorizedPortfolios.get().isEmpty()) {
            return Optional.empty();
        }

        if (hasPortfolioNostroStaticPermission) {
            LazyResourceHolder<String> portfolioNostro = new LazyResourceHolder<>(() -> filterPortfolioByType(search.portfolioIds(), RequestModel.PortfolioType.NOSTRO));
            authorizedPortfolios.add(portfolioNostro);
            requestedAuthorizedPortfolios.add(portfolioNostro);
        }

        if (hasPortfolioVostroStaticPermission) {
            LazyResourceHolder<String> portfolioVostro = new LazyResourceHolder<>(() -> filterPortfolioByType(search.portfolioIds(), RequestModel.PortfolioType.VOSTRO));
            authorizedPortfolios.add(portfolioVostro);
            requestedAuthorizedPortfolios.add(portfolioVostro);
        }

        if (hasWalletStaticPermission || hasWalletNostroStaticPermission) {
            LazyResourceHolder<String> walletNostro = new LazyResourceHolder<>(() -> filterWalletsByType(search.accountIds(), RequestModel.WalletType.NOSTRO));
            authorizedVenueAccounts.add(walletNostro);
            requestedAuthorizedVenueAccounts.add(walletNostro);
        }

        if (hasWalletStaticPermission || hasWalletVostroStaticPermission) {
            LazyResourceHolder<String> walletVostro = new LazyResourceHolder<>(() -> filterWalletsByType(search.accountIds(), RequestModel.WalletType.VOSTRO));
            authorizedVenueAccounts.add(walletVostro);
            requestedAuthorizedVenueAccounts.add(walletVostro);
        }

        if (hasPortfolioVostroStaticPermission && hasPortfolioNostroStaticPermission) {
            allPortfoliosAuthorized = true;
            hasPortfolioNostroStaticPermission = false;
            hasPortfolioVostroStaticPermission = false;
        }

        if (hasWalletVostroStaticPermission && hasWalletNostroStaticPermission) {
            hasWalletStaticPermission = true;
            hasWalletVostroStaticPermission = false;
            hasWalletNostroStaticPermission = false;
        }

        SimpleSearchRequest.AuthorizedSearchRequest.Builder authorizedSearch = SimpleSearchRequest.AuthorizedSearchRequest.Builder
            .fromRequest(search)
            .requestedAuthorizedPortfolioIds(requestedAuthorizedPortfolios.get())
            .authorizedAccountIds(authorizedVenueAccounts.get())
            .requestedAuthorizedAccountIds(requestedAuthorizedVenueAccounts.get())
            .allPortfoliosAuthorized(allPortfoliosAuthorized)
            .allAccountsAuthorized(allAccountsAuthorized);

        if (allPortfoliosAuthorized) {
            authorizedSearch.portfolioType(RequestModel.PortfolioType.ALL);
        } else if (hasPortfolioNostroStaticPermission) {
            authorizedSearch
                .portfolioType(RequestModel.PortfolioType.NOSTRO)
                .authorizedPortfolioIds(authorizedPortfolios.get());
        } else if (hasPortfolioVostroStaticPermission) {
            authorizedSearch
                .portfolioType(RequestModel.PortfolioType.VOSTRO)
                .authorizedPortfolioIds(authorizedPortfolios.get());
        } else {
            authorizedSearch.authorizedPortfolioIds(authorizedPortfolios.get());
            authorizedSearch.portfolioType(RequestModel.PortfolioType.NONE);
        }

        if (allAccountsAuthorized) {
            authorizedSearch
                .accountType(RequestModel.AccountType.ALL);
        } else if (hasWalletStaticPermission) {
            authorizedSearch
                .accountType(RequestModel.AccountType.WALLET)
                .walletType(RequestModel.WalletType.ALL);
        } else if (hasWalletNostroStaticPermission) {
            authorizedSearch
                .accountType(RequestModel.AccountType.WALLET)
                .walletType(RequestModel.WalletType.NOSTRO);
        } else if (hasWalletVostroStaticPermission) {
            authorizedSearch
                .accountType(RequestModel.AccountType.WALLET)
                .walletType(RequestModel.WalletType.VOSTRO);
        } else if (authorizedVenueAccounts.get().isEmpty()) {
            authorizedSearch
                .accountType(RequestModel.AccountType.NONE)
                .walletType(RequestModel.WalletType.NONE);
        }

        return Optional.of(authorizedSearch.build());
    }

    private Collection<String> filterPortfolioByType(Collection<String> portfolioIds, RequestModel.PortfolioType type) {
        return portfolioIds.stream()
            .map(portfolioProvider::getPortfolio)
            .filter(portfolio -> portfolio.getPortfolioType().name().equals(type.name()))
            .map(Portfolio::getId)
            .toList();
    }

    private Collection<String> filterWalletsByType(Collection<String> walletIds, RequestModel.WalletType type) {
        return walletIds.stream()
            .map(venueAccountProvider::getVenueAccount)
            .filter(Optional::isPresent)
            .filter(va -> type.equals(mapWalletType(va.get().getWalletType())))
            .map(Optional::get)
            .map(VenueAccount::getId)
            .toList();
    }

    private RequestModel.WalletType mapWalletType(WalletType walletType) {
        return switch (walletType) {
            case WALLET_VOSTRO -> RequestModel.WalletType.VOSTRO;
            case WALLET_NOSTRO -> RequestModel.WalletType.NOSTRO;
            default -> null;
        };
    }
}
