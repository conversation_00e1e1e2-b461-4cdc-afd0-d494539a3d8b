package io.wyden.booking.reporting.application.referencedata;

import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.published.referencedata.Portfolio;

import java.util.Optional;

public interface PortfolioProvider {

    String getPortfolioCurrency(String portfolioId);

    Portfolio getPortfolio(String portfolioId);

    Optional<PortfolioSnapshot> getPortfolioSnapshot(String portfolioId);

    RequestModel.PortfolioType getPortfolioType(String portfolioId);
}
