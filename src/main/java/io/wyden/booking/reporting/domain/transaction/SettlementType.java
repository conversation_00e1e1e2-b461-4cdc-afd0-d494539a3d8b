package io.wyden.booking.reporting.domain.transaction;

public enum SettlementType {

    /**
     * INSTANT_SETTLEMENT: Such transactions are attributed towards settled quantity/balance immediately.
     * This is typically the case for transfers and withdrawals.
     */
    INSTANT_SETTLEMENT,

    /**
     * DEFERRED_SETTLEMENT: Such transactions are attributed towards unsettled quantity/balance initially.
     * This is typically the case for cash trades.
     */
    DEFERRED_SETTLEMENT
}
