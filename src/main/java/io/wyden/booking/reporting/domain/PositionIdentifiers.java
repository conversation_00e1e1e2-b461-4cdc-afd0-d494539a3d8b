package io.wyden.booking.reporting.domain;

import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Version;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.sql.Timestamp;

@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class PositionIdentifiers implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;

    @CreatedDate
    protected Timestamp createdAt;

    @LastModifiedDate
    protected Timestamp updatedAt;

    @Version
    protected long version;

    protected String portfolioId;

    protected String accountId;

    @Enumerated(EnumType.STRING)
    protected RequestModel.PortfolioType portfolioType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.AccountType accountType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.WalletType accountWalletType;

    public Long getId() {
        return id;
    }

    public PositionIdentifiers setId(Long id) {
        this.id = id;
        return this;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public PositionIdentifiers setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    public Timestamp getUpdatedAt() {
        return updatedAt;
    }

    public PositionIdentifiers setUpdatedAt(Timestamp updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    public long getVersion() {
        return version;
    }

    public PositionIdentifiers setVersion(long version) {
        this.version = version;
        return this;
    }

    public String getPortfolioId() {
        return portfolioId;
    }

    public String getAccountId() {
        return accountId;
    }

    public RequestModel.PortfolioType getPortfolioType() {
        return portfolioType;
    }

    public RequestModel.AccountType getAccountType() {
        return accountType;
    }

    public RequestModel.WalletType getAccountWalletType() {
        return accountWalletType;
    }
}
