package io.wyden.booking.reporting.domain.transaction;

import com.vladmihalcea.hibernate.type.json.JsonType;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class TransactionIdentifiers implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;

    @CreatedDate
    protected Timestamp createdAt;

    /**
     * unique identifier of the reservation
     */
    protected String uuid;

    /**
     * reference identifier of the requested reservation
     */
    protected String reservationRef;

    /**
     * sequence number of corresponding Wal event
     */
    protected Long sequenceNumber;

    /**
     * date of the transaction
     */
    protected ZonedDateTime dateTime;

    /**
     * the (client) portfolio into which the trade is executed
     */
    protected String portfolioId;

    /**
     * the (bank) portfolio against which the trade is executed
     */
    protected String counterPortfolioId;

    /**
     * the portfolio from which the transfer is initiated
     */
    protected String sourcePortfolioId;

    /**
     * the portfolio into which the transfer arrives
     */
    protected String targetPortfolioId;

    /**
     * the portfolio transfer fees will be charged to
     */
    protected String feePortfolioId;

    /**
     * the venue account into which the trade is executed
     */
    protected String accountId;

    /**
     * the custody account from which the transfer is initiated
     */
    protected String sourceAccountId;

    /**
     * the custody account into which the transfer arrives
     */
    protected String targetAccountId;

    /**
     * the account (from/to) transfer fees will be charged to
     */
    protected String feeAccountId;

    @Enumerated(EnumType.STRING)
    protected TransactionType transactionType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.PortfolioType portfolioType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.PortfolioType counterPortfolioType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.PortfolioType sourcePortfolioType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.PortfolioType targetPortfolioType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.PortfolioType feePortfolioType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.AccountType accountType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.AccountType sourceAccountType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.AccountType targetAccountType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.AccountType feeAccountType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.WalletType accountWalletType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.WalletType sourceAccountWalletType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.WalletType targetAccountWalletType;

    @Enumerated(EnumType.STRING)
    protected RequestModel.WalletType feeAccountWalletType;

    /**
     * Predicted fees for reservation (unidirectional many-to-many)
     */
    @Column(columnDefinition = "jsonb")
    @Type(JsonType.class)
    protected List<TransactionFee> fees = new ArrayList<>();

    public Long getId() {
        return id;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public String getUuid() {
        return uuid;
    }

    public String getReservationRef() {
        return reservationRef;
    }

    public Long getSequenceNumber() {
        return sequenceNumber;
    }

    public ZonedDateTime getDateTime() {
        return dateTime;
    }

    public String getPortfolioId() {
        return portfolioId;
    }

    public String getCounterPortfolioId() {
        return counterPortfolioId;
    }

    public String getSourcePortfolioId() {
        return sourcePortfolioId;
    }

    public String getTargetPortfolioId() {
        return targetPortfolioId;
    }

    public String getFeePortfolioId() {
        return feePortfolioId;
    }

    public String getAccountId() {
        return accountId;
    }

    public String getSourceAccountId() {
        return sourceAccountId;
    }

    public String getTargetAccountId() {
        return targetAccountId;
    }

    public String getFeeAccountId() {
        return feeAccountId;
    }

    public TransactionType getTransactionType() {
        return transactionType;
    }

    public RequestModel.PortfolioType getPortfolioType() {
        return portfolioType;
    }

    public RequestModel.PortfolioType getCounterPortfolioType() {
        return counterPortfolioType;
    }

    public RequestModel.PortfolioType getSourcePortfolioType() {
        return sourcePortfolioType;
    }

    public RequestModel.PortfolioType getTargetPortfolioType() {
        return targetPortfolioType;
    }

    public RequestModel.PortfolioType getFeePortfolioType() {
        return feePortfolioType;
    }

    public RequestModel.AccountType getAccountType() {
        return accountType;
    }

    public RequestModel.AccountType getSourceAccountType() {
        return sourceAccountType;
    }

    public RequestModel.AccountType getTargetAccountType() {
        return targetAccountType;
    }

    public RequestModel.AccountType getFeeAccountType() {
        return feeAccountType;
    }

    public RequestModel.WalletType getAccountWalletType() {
        return accountWalletType;
    }

    public RequestModel.WalletType getSourceAccountWalletType() {
        return sourceAccountWalletType;
    }

    public RequestModel.WalletType getTargetAccountWalletType() {
        return targetAccountWalletType;
    }

    public RequestModel.WalletType getFeeAccountWalletType() {
        return feeAccountWalletType;
    }

    public List<TransactionFee> getFees() {
        return fees;
    }

}
