package io.wyden.booking.reporting.domain.reservation;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Optional;

@Repository
public interface ReservationRepository extends JpaRepository<Reservation, Long>, ReservationRepositoryCustom {

    Collection<Reservation> findBySequenceNumber(long sequenceNumber);

    Optional<Reservation> findByReservationRef(String reservationRef);
}
