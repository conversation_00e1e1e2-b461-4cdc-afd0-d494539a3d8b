package io.wyden.booking.reporting.domain.reservation;

import io.wyden.booking.reporting.domain.transaction.TransactionFee;
import io.wyden.booking.reporting.domain.transaction.TransactionIdentifiers;
import io.wyden.booking.reporting.domain.transaction.TransactionType;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import jakarta.persistence.Entity;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Objects;

@Entity
public class Reservation extends TransactionIdentifiers {

    /**
     * the quantity that is requested in trade
     */
    protected BigDecimal quantity;

    /**
     * the limit price for limit and stop limit orders
     */
    protected BigDecimal price;

    /**
     * the stop price for stop and stop limit orders
     */
    protected BigDecimal stopPrice;

    /**
     * the transaction / quote currency
     */
    protected String currency;

    /**
     * the currency that represents the quantity of the trade, for a BTC/USD trade, BTC is the baseCurrency
     */
    protected String baseCurrency;

    /**
     * The Instrument traded
     */
    protected String asset;

    public Reservation() {
        // JPA
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * Creates a builder instance pre-populated with this object's current data
     *
     * @return builder with current object's data
     */
    public Builder toBuilder() {
        Builder builder = builder()
            .id(this.id)
            .createdAt(this.createdAt)
            .uuid(this.uuid)
            .reservationRef(this.reservationRef)
            .sequenceNumber(this.sequenceNumber)
            .dateTime(this.dateTime)
            .quantity(this.quantity)
            .price(this.price)
            .stopPrice(this.stopPrice)
            .currency(this.currency)
            .baseCurrency(this.baseCurrency)
            .asset(this.asset)
            .portfolioId(this.portfolioId)
            .counterPortfolioId(this.counterPortfolioId)
            .sourcePortfolioId(this.sourcePortfolioId)
            .targetPortfolioId(this.targetPortfolioId)
            .feePortfolioId(this.feePortfolioId)
            .accountId(this.accountId)
            .sourceAccountId(this.sourceAccountId)
            .targetAccountId(this.targetAccountId)
            .feeAccountId(this.feeAccountId)
            .transactionType(this.transactionType)
            .portfolioType(this.portfolioType)
            .counterPortfolioType(this.counterPortfolioType)
            .sourcePortfolioType(this.sourcePortfolioType)
            .targetPortfolioType(this.targetPortfolioType)
            .feePortfolioType(this.feePortfolioType)
            .accountType(this.accountType)
            .sourceAccountType(this.sourceAccountType)
            .targetAccountType(this.targetAccountType)
            .feeAccountType(this.feeAccountType)
            .accountWalletType(this.accountWalletType)
            .sourceAccountWalletType(this.sourceAccountWalletType)
            .targetAccountWalletType(this.targetAccountWalletType)
            .feeAccountWalletType(this.feeAccountWalletType);

        // Add all fees
        if (this.fees != null && !this.fees.isEmpty()) {
            builder.addFees(this.fees);
        }

        return builder;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public BigDecimal getStopPrice() {
        return stopPrice;
    }

    public String getCurrency() {
        return currency;
    }

    public String getBaseCurrency() {
        return baseCurrency;
    }

    public String getAsset() {
        return asset;
    }

    @Override
    public String toString() {
        return "Reservation{" +
               "id=" + id +
               ", createdAt=" + createdAt +
               ", uuid='" + uuid + '\'' +
               ", reservationRef='" + reservationRef + '\'' +
               ", sequenceNumber=" + sequenceNumber +
               ", dateTime=" + dateTime +
               ", quantity=" + quantity +
               ", price=" + price +
               ", stopPrice=" + stopPrice +
               ", currency='" + currency + '\'' +
               ", baseCurrency='" + baseCurrency + '\'' +
               ", asset='" + asset + '\'' +
               ", portfolioId='" + portfolioId + '\'' +
               ", counterPortfolioId='" + counterPortfolioId + '\'' +
               ", sourcePortfolioId='" + sourcePortfolioId + '\'' +
               ", targetPortfolioId='" + targetPortfolioId + '\'' +
               ", feePortfolioId='" + feePortfolioId + '\'' +
               ", accountId='" + accountId + '\'' +
               ", sourceAccountId='" + sourceAccountId + '\'' +
               ", targetAccountId='" + targetAccountId + '\'' +
               ", feeAccountId='" + feeAccountId + '\'' +
               ", transactionType=" + transactionType +
               ", portfolioType=" + portfolioType +
               ", counterPortfolioType=" + counterPortfolioType +
               ", sourcePortfolioType=" + sourcePortfolioType +
               ", targetPortfolioType=" + targetPortfolioType +
               ", feePortfolioType=" + feePortfolioType +
               ", accountType=" + accountType +
               ", sourceAccountType=" + sourceAccountType +
               ", targetAccountType=" + targetAccountType +
               ", feeAccountType=" + feeAccountType +
               ", accountWalletType=" + accountWalletType +
               ", sourceAccountWalletType=" + sourceAccountWalletType +
               ", targetAccountWalletType=" + targetAccountWalletType +
               ", feeAccountWalletType=" + feeAccountWalletType +
               ", fees=" + fees +
               '}';
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof Reservation that)) return false;
        return Objects.equals(id, that.id)
               && Objects.equals(createdAt, that.createdAt)
               && Objects.equals(uuid, that.uuid)
               && Objects.equals(reservationRef, that.reservationRef)
               && Objects.equals(sequenceNumber, that.sequenceNumber)
               && Objects.equals(dateTime, that.dateTime)
               && Objects.equals(quantity, that.quantity)
               && Objects.equals(price, that.price)
               && Objects.equals(stopPrice, that.stopPrice)
               && Objects.equals(currency, that.currency)
               && Objects.equals(baseCurrency, that.baseCurrency)
               && Objects.equals(asset, that.asset)
               && Objects.equals(portfolioId, that.portfolioId)
               && Objects.equals(counterPortfolioId, that.counterPortfolioId)
               && Objects.equals(sourcePortfolioId, that.sourcePortfolioId)
               && Objects.equals(targetPortfolioId, that.targetPortfolioId)
               && Objects.equals(feePortfolioId, that.feePortfolioId)
               && Objects.equals(accountId, that.accountId)
               && Objects.equals(sourceAccountId, that.sourceAccountId)
               && Objects.equals(targetAccountId, that.targetAccountId)
               && Objects.equals(feeAccountId, that.feeAccountId)
               && transactionType == that.transactionType
               && portfolioType == that.portfolioType
               && counterPortfolioType == that.counterPortfolioType
               && sourcePortfolioType == that.sourcePortfolioType
               && targetPortfolioType == that.targetPortfolioType
               && feePortfolioType == that.feePortfolioType
               && accountType == that.accountType
               && sourceAccountType == that.sourceAccountType
               && targetAccountType == that.targetAccountType
               && feeAccountType == that.feeAccountType
               && accountWalletType == that.accountWalletType
               && sourceAccountWalletType == that.sourceAccountWalletType
               && targetAccountWalletType == that.targetAccountWalletType
               && feeAccountWalletType == that.feeAccountWalletType
               && Objects.equals(fees, that.fees);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            id,
            createdAt,
            uuid,
            reservationRef,
            sequenceNumber,
            dateTime,
            quantity,
            price,
            stopPrice,
            currency,
            baseCurrency,
            asset,
            portfolioId,
            counterPortfolioId,
            sourcePortfolioId,
            targetPortfolioId,
            feePortfolioId,
            accountId,
            sourceAccountId,
            targetAccountId,
            feeAccountId,
            transactionType,
            portfolioType,
            counterPortfolioType,
            sourcePortfolioType,
            targetPortfolioType,
            feePortfolioType,
            accountType,
            sourceAccountType,
            targetAccountType,
            feeAccountType,
            accountWalletType,
            sourceAccountWalletType,
            targetAccountWalletType,
            feeAccountWalletType,
            fees);
    }

    /**
     * LedgerEntrySearchBuilder for creating Reservation instances with a fluent API
     */
    public static class Builder {

        private final Reservation reservation;

        private Builder() {
            this.reservation = new Reservation();
        }

        public Builder id(Long id) {
            reservation.id = id;
            return this;
        }

        public Builder createdAt(Timestamp createdAt) {
            reservation.createdAt = createdAt;
            return this;
        }

        public Builder uuid(String uuid) {
            reservation.uuid = uuid;
            return this;
        }

        public Builder reservationRef(String reservationRef) {
            reservation.reservationRef = reservationRef;
            return this;
        }

        public Builder sequenceNumber(Long sequenceNumber) {
            reservation.sequenceNumber = sequenceNumber;
            return this;
        }

        public Builder dateTime(ZonedDateTime dateTime) {
            reservation.dateTime = dateTime;
            return this;
        }

        public Builder quantity(BigDecimal quantity) {
            reservation.quantity = quantity;
            return this;
        }

        public Builder price(BigDecimal price) {
            reservation.price = price;
            return this;
        }

        public Builder stopPrice(BigDecimal stopPrice) {
            reservation.stopPrice = stopPrice;
            return this;
        }

        public Builder currency(String currency) {
            reservation.currency = currency;
            return this;
        }

        public Builder baseCurrency(String baseCurrency) {
            reservation.baseCurrency = baseCurrency;
            return this;
        }

        public Builder asset(String asset) {
            reservation.asset = asset;
            return this;
        }

        public Builder portfolioId(String portfolioId) {
            reservation.portfolioId = portfolioId;
            return this;
        }

        public Builder counterPortfolioId(String counterPortfolioId) {
            reservation.counterPortfolioId = counterPortfolioId;
            return this;
        }

        public Builder sourcePortfolioId(String sourcePortfolioId) {
            reservation.sourcePortfolioId = sourcePortfolioId;
            return this;
        }

        public Builder targetPortfolioId(String targetPortfolioId) {
            reservation.targetPortfolioId = targetPortfolioId;
            return this;
        }

        public Builder feePortfolioId(String feePortfolioId) {
            reservation.feePortfolioId = feePortfolioId;
            return this;
        }

        public Builder accountId(String accountId) {
            reservation.accountId = accountId;
            return this;
        }

        public Builder sourceAccountId(String sourceAccountId) {
            reservation.sourceAccountId = sourceAccountId;
            return this;
        }

        public Builder targetAccountId(String targetAccountId) {
            reservation.targetAccountId = targetAccountId;
            return this;
        }

        public Builder feeAccountId(String feeAccountId) {
            reservation.feeAccountId = feeAccountId;
            return this;
        }

        public Builder transactionType(TransactionType transactionType) {
            reservation.transactionType = transactionType;
            return this;
        }

        public Builder portfolioType(RequestModel.PortfolioType portfolioType) {
            reservation.portfolioType = portfolioType;
            return this;
        }

        public Builder counterPortfolioType(RequestModel.PortfolioType counterPortfolioType) {
            reservation.counterPortfolioType = counterPortfolioType;
            return this;
        }

        public Builder sourcePortfolioType(RequestModel.PortfolioType sourcePortfolioType) {
            reservation.sourcePortfolioType = sourcePortfolioType;
            return this;
        }

        public Builder targetPortfolioType(RequestModel.PortfolioType targetPortfolioType) {
            reservation.targetPortfolioType = targetPortfolioType;
            return this;
        }

        public Builder feePortfolioType(RequestModel.PortfolioType feePortfolioType) {
            reservation.feePortfolioType = feePortfolioType;
            return this;
        }

        public Builder accountType(RequestModel.AccountType accountType) {
            reservation.accountType = accountType;
            return this;
        }

        public Builder sourceAccountType(RequestModel.AccountType sourceAccountType) {
            reservation.sourceAccountType = sourceAccountType;
            return this;
        }

        public Builder targetAccountType(RequestModel.AccountType targetAccountType) {
            reservation.targetAccountType = targetAccountType;
            return this;
        }

        public Builder feeAccountType(RequestModel.AccountType feeAccountType) {
            reservation.feeAccountType = feeAccountType;
            return this;
        }

        public Builder accountWalletType(RequestModel.WalletType accountWalletType) {
            reservation.accountWalletType = accountWalletType;
            return this;
        }

        public Builder sourceAccountWalletType(RequestModel.WalletType sourceAccountWalletType) {
            reservation.sourceAccountWalletType = sourceAccountWalletType;
            return this;
        }

        public Builder targetAccountWalletType(RequestModel.WalletType targetAccountWalletType) {
            reservation.targetAccountWalletType = targetAccountWalletType;
            return this;
        }

        public Builder feeAccountWalletType(RequestModel.WalletType feeAccountWalletType) {
            reservation.feeAccountWalletType = feeAccountWalletType;
            return this;
        }

        public Builder addFees(Collection<TransactionFee> fees) {
            reservation.fees = fees != null ? new ArrayList<>(fees) : new ArrayList<>();
            return this;
        }

        public Builder addFee(TransactionFee fee) {
            if (reservation.fees == null) {
                reservation.fees = new ArrayList<>();
            }
            reservation.fees.add(fee);
            return this;
        }

        public Builder clearFees() {
            reservation.fees = new ArrayList<>();
            return this;
        }

        /**
         * Builds a new Reservation instance with the configured values
         *
         * @return new Reservation instance
         */
        public Reservation build() {
            return reservation;
        }
    }
}
