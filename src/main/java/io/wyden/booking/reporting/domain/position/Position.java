package io.wyden.booking.reporting.domain.position;

import io.wyden.booking.reporting.domain.PositionIdentifiers;
import io.wyden.booking.reporting.domain.rate.Rate;
import io.wyden.booking.reporting.domain.rate.RateValidator;
import io.wyden.booking.reporting.domain.rate.SystemCurrencyProvider;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Objects;

import static io.wyden.booking.reporting.utils.HostUtils.resolveHostname;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isNullOrZero;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isZero;
import static io.wyden.cloudutils.tools.MathUtils.divide;
import static java.math.BigDecimal.ZERO;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Entity
public class Position extends PositionIdentifiers {

    /**
     * Symbol of the Position asset
     */
    private String symbol;

    /**
     * For CashPositions the position currency is the same as instrument's symbol: BTC -> BTC, USD -> USD
     * <p>
     * For AssetPositions the position currency is the base currency of security: BTC/USD -> BTC, ETH/BTC -> ETH, AAPL -> AAPL
     * <p>
     * Position currency defines what the currency of quantity is
     */
    private String currency;

    /**
     * For portfolio positions, it is equal to portfolio currency.
     * Otherwise, empty.
     */
    private String portfolioCurrency;

    /**
     * For account positions, it is equal to account (system) currency.
     * Otherwise, empty
     */
    private String accountCurrency;

    private boolean inverseContract = false;

    private BigDecimal contractSize = BigDecimal.ONE;

    /**
     * The current quantity (positive or negative) of the position.
     * Also represents the total amount per currency (or asset) of all Ledger Entries excluding Reservations.
     * The total quantity will typically change by either transferring funds in or out of the portfolio/account,
     * and it will change when a profit or loss was realized from trading, as well as due to fees being charged.
     * For Cash Positions, the total quantity is generally available for withdrawal.
     */
    private BigDecimal quantity;

    private BigDecimal notionalQuantity;

    /**
     * the cost paid to open the position (after fees)
     */
    private BigDecimal netCost;

    private BigDecimal netCostSc;

    /**
     * the cost paid to open the position (before fees)
     */
    private BigDecimal grossCost;

    private BigDecimal grossCostSc;

    /**
     * the realized profit and loss made so far from this position  (after fees)
     */
    private BigDecimal netRealizedPnl;

    private BigDecimal netRealizedPnlSc;

    /**
     * the realized profit and loss made so far from this position  (before fees)
     */
    private BigDecimal grossRealizedPnl;

    private BigDecimal grossRealizedPnlSc;

    private BigDecimal netAveragePrice;

    private BigDecimal grossAveragePrice;

    private BigDecimal marketValue;

    private BigDecimal marketValueSc;

    private BigDecimal netUnrealizedPnl;

    private BigDecimal netUnrealizedPnlSc;

    private BigDecimal grossUnrealizedPnl;

    private BigDecimal grossUnrealizedPnlSc;

    /**
     * Tracks the most recent {@link io.wyden.booking.reporting.domain.ledgerentry.LedgerEntry} applied to this Position, updated after each processing.
     */
    private Long lastAppliedLedgerEntryId;

    private long sequenceNumber;

    private String createdBy;

    private String updatedBy;

    @PrePersist
    public void prePersist() {
        this.createdBy = this.updatedBy = resolveHostname();
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedBy = resolveHostname();
    }

    /**
     * Calculates the value of an instrument based on the given quantity and price.
     * The calculation method varies depending on whether the instrument is an inverse contract.
     *
     * @param quantity the quantity of the instrument
     * @param price    the price of the instrument
     * @return the calculated value of the instrument
     */
    public BigDecimal getValue(BigDecimal quantity, BigDecimal price) {
        if (!isInverseContract()) {
            return quantity.multiply(getContractSize()).multiply(price);
        } else {
            BigDecimal value = quantity.multiply(getContractSize());
            return divide(value, price);
        }
    }

    /**
     * Calculates the price of an instrument based on the provided quantity and value.
     * The calculation considers whether the instrument is an inverse contract.
     *
     * @param quantity the quantity of the instrument
     * @param value    the value of the instrument
     * @return the calculated price of the instrument; returns zero if the quantity or value is invalid
     */
    public BigDecimal getPrice(BigDecimal quantity, BigDecimal value) {
        BigDecimal notionalQuantity = quantity.multiply(getContractSize());

        if (!isInverseContract()) {
            if (isNullOrZero(quantity)) {
                return ZERO;
            }
            return divide(value, notionalQuantity);
        }

        if (isZero(value)) {
            return ZERO;
        }

        return divide(notionalQuantity, value);
    }

    /**
     * The current marketValue of this position
     */
    private BigDecimal calculateMarketValue(Rate instrumentCurrencyRate) {
        if (getQuantity() == null) {
            return ZERO;
        }

        return getQuantity()
            .multiply(contractSize)
            .multiply(instrumentCurrencyRate.value());
    }

    private BigDecimal calculateMarketValueSc(Rate instrumentCurrencyRate, Rate positionCurrencyRate) {
        return calculateMarketValue(instrumentCurrencyRate)
            .multiply(positionCurrencyRate.value());
    }

    /**
     * The average price paid to open this position (after fees)
     */
    private BigDecimal calculateNetAveragePrice() {
        return getPrice(getQuantity(), netCost);
    }

    /**
     * The average price paid to open this position (before fees)
     */
    private BigDecimal calculateGrossAveragePrice() {
        return getPrice(getQuantity(), grossCost);
    }

    /**
     * The unrealized profit and lost of this position (after fees).
     */
    private BigDecimal calculateNetUnrealizedPnl(Rate instrumentCurrencyRate) {
        BigDecimal marketValue = calculateMarketValue(instrumentCurrencyRate);
        BigDecimal netCost = getNetCost();

        if (inverseContract) {
            return netCost.subtract(marketValue);
        }

        return marketValue.subtract(netCost);
    }

    /**
     * The unrealized profit and lost in system currency (after fees).
     * <p>
     * For example, ETH/BTC position (asset=ETH, quote=BTC) need the following rates:
     * asset-rate ETH/USD = 1.530
     * quote-rate BTC/USD = 21.500
     * asset-to-quote-rate ETH/BTC = 0.07
     */
    private BigDecimal calculateNetUnrealizedPnlSc(Rate instrumentCurrencyRate, Rate positionCurrencyRate) {
        return calculateNetUnrealizedPnl(instrumentCurrencyRate)
            .multiply(positionCurrencyRate.value());
    }

    /**
     * The unrealized profit and lost of this position (before fees).
     */
    private BigDecimal calculateGrossUnrealizedPnl(Rate instrumentCurrencyRate) {
        BigDecimal marketValue = calculateMarketValue(instrumentCurrencyRate);
        BigDecimal grossCost = getGrossCost();

        if (inverseContract) {
            return grossCost.subtract(marketValue);
        }

        return marketValue.subtract(grossCost);
    }

    /**
     * The unrealized profit and lost in system currency (before fees)
     */
    private BigDecimal calculateGrossUnrealizedPnlSc(Rate instrumentCurrencyRate, Rate positionCurrencyRate) {
        return calculateGrossUnrealizedPnl(instrumentCurrencyRate)
            .multiply(positionCurrencyRate.value());
    }

    /**
     * The realized profit and lost in system currency (after fees)
     */
    private BigDecimal calculateNetRealizedPnlSc(Rate positionCurrencyRate) {
        return getNetRealizedPnl()
            .multiply(positionCurrencyRate.value());
    }

    /**
     * The realized profit and lost in system currency (before fees)
     */
    private BigDecimal calculateGrossRealizedPnlSc(Rate positionCurrencyRate) {
        return getGrossRealizedPnl()
            .multiply(positionCurrencyRate.value());
    }

    /**
     * The cost in system currency (after fees)
     */
    private BigDecimal calculateNetCostSc(Rate positionCurrencyRate) {
        // contract size is skipped - it should already be used for cost calculation
        return getNetCost().multiply(positionCurrencyRate.value());
    }

    /**
     * The cost in system currency (before fees)
     */
    private BigDecimal calculateGrossCostSc(Rate positionCurrencyRate) {
        return getValue(getGrossCost(), positionCurrencyRate.value());
    }

    public Position recalculatedUsingRates(RateValidator rateValidator, Rate instrumentToReferenceCurrencyRate, Rate referenceToSystemCurrencyRate) {
        validateInstrumentCurrencyToReferenceCurrencyRate(rateValidator, instrumentToReferenceCurrencyRate);
        validateReferenceCurrencyToSystemCurrencyRate(rateValidator, referenceToSystemCurrencyRate);

        BigDecimal netAveragePrice = calculateNetAveragePrice();
        BigDecimal grossAveragePrice = calculateGrossAveragePrice();

        BigDecimal marketValue = calculateMarketValue(instrumentToReferenceCurrencyRate);
        BigDecimal marketValueSc = calculateMarketValueSc(instrumentToReferenceCurrencyRate, referenceToSystemCurrencyRate);

        BigDecimal netUnrealizedPnl = calculateNetUnrealizedPnl(instrumentToReferenceCurrencyRate);
        BigDecimal netUnrealizedPnlSc = calculateNetUnrealizedPnlSc(instrumentToReferenceCurrencyRate, referenceToSystemCurrencyRate);

        BigDecimal grossUnrealizedPnl = calculateGrossUnrealizedPnl(instrumentToReferenceCurrencyRate);
        BigDecimal grossUnrealizedPnlSc = calculateGrossUnrealizedPnlSc(instrumentToReferenceCurrencyRate, referenceToSystemCurrencyRate);

        BigDecimal netRealizedPnl = getNetRealizedPnl();
        BigDecimal netRealizedPnlSc = calculateNetRealizedPnlSc(referenceToSystemCurrencyRate);

        BigDecimal grossRealizedPnl = getGrossRealizedPnl();
        BigDecimal grossRealizedPnlSc = calculateGrossRealizedPnlSc(referenceToSystemCurrencyRate);

        BigDecimal netCost = getNetCost();
        BigDecimal netCostSc = calculateNetCostSc(referenceToSystemCurrencyRate);

        BigDecimal grossCost = getGrossCost();
        BigDecimal grossCostSc = calculateGrossCostSc(referenceToSystemCurrencyRate);

        return toBuilder()
            .netAveragePrice(netAveragePrice)
            .grossAveragePrice(grossAveragePrice)
            .marketValue(marketValue)
            .marketValueSc(marketValueSc)
            .netCost(netCost)
            .netCostSc(netCostSc)
            .grossCost(grossCost)
            .grossCostSc(grossCostSc)
            .netRealizedPnl(netRealizedPnl)
            .netRealizedPnlSc(netRealizedPnlSc)
            .grossRealizedPnl(grossRealizedPnl)
            .grossRealizedPnlSc(grossRealizedPnlSc)
            .netUnrealizedPnl(netUnrealizedPnl)
            .netUnrealizedPnlSc(netUnrealizedPnlSc)
            .grossUnrealizedPnl(grossUnrealizedPnl)
            .grossUnrealizedPnlSc(grossUnrealizedPnlSc)
            .build();
    }

    private void validateInstrumentCurrencyToReferenceCurrencyRate(RateValidator validator, Rate rate) {
        String instrument = getCurrency();
        String referenceCurrency = isNotBlank(getPortfolioId()) ? getPortfolioCurrency() : getAccountCurrency();

        validator.validateRate(rate, instrument, referenceCurrency);
    }

    private void validateReferenceCurrencyToSystemCurrencyRate(RateValidator validator, Rate rate) {
        String referenceCurrency = isNotBlank(getPortfolioId()) ? getPortfolioCurrency() : getAccountCurrency();
        String systemCurrency = SystemCurrencyProvider.getSystemCurrency();

        validator.validateRate(rate, referenceCurrency, systemCurrency);
    }

    public String getSymbol() {
        return symbol;
    }

    public String getCurrency() {
        return currency;
    }

    public String getPortfolioCurrency() {
        return portfolioCurrency;
    }

    public String getAccountCurrency() {
        return accountCurrency;
    }

    public String getReferenceCurrency() {
        return isNotBlank(getPortfolioId()) ? getPortfolioCurrency() : getAccountCurrency();
    }

    public BigDecimal getContractSize() {
        return contractSize;
    }

    public boolean isInverseContract() {
        return inverseContract;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public BigDecimal getNotionalQuantity() {
        return notionalQuantity;
    }

    public BigDecimal getNetCost() {
        return netCost;
    }

    public BigDecimal getNetCostSc() {
        return netCostSc;
    }

    public BigDecimal getGrossCost() {
        return grossCost;
    }

    public BigDecimal getGrossCostSc() {
        return grossCostSc;
    }

    public BigDecimal getNetRealizedPnl() {
        return netRealizedPnl;
    }

    public BigDecimal getNetRealizedPnlSc() {
        return netRealizedPnlSc;
    }

    public BigDecimal getGrossRealizedPnl() {
        return grossRealizedPnl;
    }

    public BigDecimal getGrossRealizedPnlSc() {
        return grossRealizedPnlSc;
    }

    public BigDecimal getNetAveragePrice() {
        return netAveragePrice;
    }

    public BigDecimal getGrossAveragePrice() {
        return grossAveragePrice;
    }

    public BigDecimal getMarketValue() {
        return marketValue;
    }

    public BigDecimal getMarketValueSc() {
        return marketValueSc;
    }

    public BigDecimal getNetUnrealizedPnl() {
        return netUnrealizedPnl;
    }

    public BigDecimal getNetUnrealizedPnlSc() {
        return netUnrealizedPnlSc;
    }

    public BigDecimal getGrossUnrealizedPnl() {
        return grossUnrealizedPnl;
    }

    public BigDecimal getGrossUnrealizedPnlSc() {
        return grossUnrealizedPnlSc;
    }

    public Long getLastAppliedLedgerEntryId() {
        return lastAppliedLedgerEntryId;
    }

    public long getSequenceNumber() {
        return sequenceNumber;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof Position position)) return false;
        return sequenceNumber == position.sequenceNumber
               && Objects.equals(symbol, position.symbol)
               && Objects.equals(currency, position.currency)
               && Objects.equals(portfolioCurrency, position.portfolioCurrency)
               && Objects.equals(accountCurrency, position.accountCurrency)
               && Objects.equals(contractSize, position.contractSize)
               && inverseContract == position.inverseContract
               && Objects.equals(quantity, position.quantity)
               && Objects.equals(notionalQuantity, position.notionalQuantity)
               && Objects.equals(netCost, position.netCost)
               && Objects.equals(netCostSc, position.netCostSc)
               && Objects.equals(grossCost, position.grossCost)
               && Objects.equals(grossCostSc, position.grossCostSc)
               && Objects.equals(netRealizedPnl, position.netRealizedPnl)
               && Objects.equals(netRealizedPnlSc, position.netRealizedPnlSc)
               && Objects.equals(grossRealizedPnl, position.grossRealizedPnl)
               && Objects.equals(grossRealizedPnlSc, position.grossRealizedPnlSc)
               && Objects.equals(netAveragePrice, position.netAveragePrice)
               && Objects.equals(grossAveragePrice, position.grossAveragePrice)
               && Objects.equals(marketValue, position.marketValue)
               && Objects.equals(marketValueSc, position.marketValueSc)
               && Objects.equals(netUnrealizedPnl, position.netUnrealizedPnl)
               && Objects.equals(netUnrealizedPnlSc, position.netUnrealizedPnlSc)
               && Objects.equals(grossUnrealizedPnl, position.grossUnrealizedPnl)
               && Objects.equals(grossUnrealizedPnlSc, position.grossUnrealizedPnlSc)
               && Objects.equals(lastAppliedLedgerEntryId, position.lastAppliedLedgerEntryId)
               && Objects.equals(createdBy, position.createdBy)
               && Objects.equals(updatedBy, position.updatedBy);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            symbol,
            currency,
            portfolioCurrency,
            accountCurrency,
            contractSize,
            inverseContract,
            quantity,
            notionalQuantity,
            netCost,
            netCostSc,
            grossCost,
            grossCostSc,
            netRealizedPnl,
            netRealizedPnlSc,
            grossRealizedPnl,
            grossRealizedPnlSc,
            netAveragePrice,
            grossAveragePrice,
            marketValue,
            marketValueSc,
            netUnrealizedPnl,
            netUnrealizedPnlSc,
            grossUnrealizedPnl,
            grossUnrealizedPnlSc,
            lastAppliedLedgerEntryId,
            sequenceNumber,
            createdBy,
            updatedBy);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", id)
            .append("createdAt", createdAt)
            .append("updatedAt", updatedAt)
            .append("version", version)
            .append("symbol", symbol)
            .append("currency", currency)
            .append("portfolioId", portfolioId)
            .append("accountId", accountId)
            .append("portfolioType", portfolioType)
            .append("accountType", accountType)
            .append("accountWalletType", accountWalletType)
            .append("portfolioCurrency", portfolioCurrency)
            .append("accountCurrency", accountCurrency)
            .append("inverseContract", inverseContract)
            .append("contractSize", contractSize)
            .append("quantity", quantity)
            .append("notionalQuantity", notionalQuantity)
            .append("netCost", netCost)
            .append("netCostSc", netCostSc)
            .append("grossCost", grossCost)
            .append("grossCostSc", grossCostSc)
            .append("netRealizedPnl", netRealizedPnl)
            .append("netRealizedPnlSc", netRealizedPnlSc)
            .append("grossRealizedPnl", grossRealizedPnl)
            .append("grossRealizedPnlSc", grossRealizedPnlSc)
            .append("netAveragePrice", netAveragePrice)
            .append("grossAveragePrice", grossAveragePrice)
            .append("marketValue", marketValue)
            .append("marketValueSc", marketValueSc)
            .append("netUnrealizedPnl", netUnrealizedPnl)
            .append("netUnrealizedPnlSc", netUnrealizedPnlSc)
            .append("grossUnrealizedPnl", grossUnrealizedPnl)
            .append("grossUnrealizedPnlSc", grossUnrealizedPnlSc)
            .append("lastAppliedLedgerEntryId", lastAppliedLedgerEntryId)
            .append("sequenceNumber", sequenceNumber)
            .append("createdBy", createdBy)
            .append("updatedBy", updatedBy)
            .toString();
    }

    public static Builder builder() {
        return new Builder();
    }

    public Builder toBuilder() {
        return new Builder(this);
    }

    public static class Builder {
        private final Position position;

        private Builder() {
            this.position = new Position();
        }

        private Builder(Position position) {
            this.position = new Position();
            this.position.id = position.id;
            this.position.createdAt = position.createdAt;
            this.position.updatedAt = position.updatedAt;
            this.position.version = position.version;
            this.position.portfolioId = position.portfolioId;
            this.position.accountId = position.accountId;
            this.position.portfolioType = position.portfolioType;
            this.position.accountType = position.accountType;
            this.position.accountWalletType = position.accountWalletType;
            this.position.symbol = position.symbol;
            this.position.currency = position.currency;
            this.position.portfolioCurrency = position.portfolioCurrency;
            this.position.accountCurrency = position.accountCurrency;
            this.position.contractSize = position.contractSize;
            this.position.inverseContract = position.inverseContract;
            this.position.quantity = position.quantity;
            this.position.notionalQuantity = position.notionalQuantity;
            this.position.netCost = position.netCost;
            this.position.netCostSc = position.netCostSc;
            this.position.grossCost = position.grossCost;
            this.position.grossCostSc = position.grossCostSc;
            this.position.netRealizedPnl = position.netRealizedPnl;
            this.position.netRealizedPnlSc = position.netRealizedPnlSc;
            this.position.grossRealizedPnl = position.grossRealizedPnl;
            this.position.grossRealizedPnlSc = position.grossRealizedPnlSc;
            this.position.netAveragePrice = position.netAveragePrice;
            this.position.grossAveragePrice = position.grossAveragePrice;
            this.position.marketValue = position.marketValue;
            this.position.marketValueSc = position.marketValueSc;
            this.position.netUnrealizedPnl = position.netUnrealizedPnl;
            this.position.netUnrealizedPnlSc = position.netUnrealizedPnlSc;
            this.position.grossUnrealizedPnl = position.grossUnrealizedPnl;
            this.position.grossUnrealizedPnlSc = position.grossUnrealizedPnlSc;
            this.position.lastAppliedLedgerEntryId = position.lastAppliedLedgerEntryId;
            this.position.sequenceNumber = position.sequenceNumber;
            this.position.createdBy = position.createdBy;
            this.position.updatedBy = position.updatedBy;
        }

        public Builder id(Long id) {
            position.id = id;
            return this;
        }

        public Builder createdAt(Timestamp createdAt) {
            position.createdAt = createdAt;
            return this;
        }

        public Builder updatedAt(Timestamp updatedAt) {
            position.updatedAt = updatedAt;
            return this;
        }

        public Builder version(Long version) {
            position.version = version;
            return this;
        }

        public Builder portfolioId(String portfolioId) {
            position.portfolioId = portfolioId;
            return this;
        }

        public Builder accountId(String accountId) {
            position.accountId = accountId;
            return this;
        }

        public Builder portfolioType(RequestModel.PortfolioType portfolioType) {
            position.portfolioType = portfolioType;
            return this;
        }

        public Builder accountType(RequestModel.AccountType accountType) {
            position.accountType = accountType;
            return this;
        }

        public Builder accountWalletType(RequestModel.WalletType accountWalletType) {
            position.accountWalletType = accountWalletType;
            return this;
        }

        public Builder symbol(String symbol) {
            position.symbol = symbol;
            return this;
        }

        public Builder currency(String currency) {
            position.currency = currency;
            return this;
        }

        public Builder portfolioCurrency(String portfolioCurrency) {
            position.portfolioCurrency = portfolioCurrency;
            return this;
        }

        public Builder accountCurrency(String accountCurrency) {
            position.accountCurrency = accountCurrency;
            return this;
        }

        public Builder contractSize(BigDecimal contractSize) {
            position.contractSize = contractSize;
            return this;
        }

        public Builder inverseContract(boolean inverseContract) {
            position.inverseContract = inverseContract;
            return this;
        }

        public Builder quantity(BigDecimal quantity) {
            position.quantity = quantity;
            return this;
        }

        public Builder notionalQuantity(BigDecimal notionalQuantity) {
            position.notionalQuantity = notionalQuantity;
            return this;
        }

        public Builder netCost(BigDecimal netCost) {
            position.netCost = netCost;
            return this;
        }

        public Builder netCostSc(BigDecimal netCostSc) {
            position.netCostSc = netCostSc;
            return this;
        }

        public Builder grossCost(BigDecimal grossCost) {
            position.grossCost = grossCost;
            return this;
        }

        public Builder grossCostSc(BigDecimal grossCostSc) {
            position.grossCostSc = grossCostSc;
            return this;
        }

        public Builder netRealizedPnl(BigDecimal netRealizedPnl) {
            position.netRealizedPnl = netRealizedPnl;
            return this;
        }

        public Builder netRealizedPnlSc(BigDecimal netRealizedPnlSc) {
            position.netRealizedPnlSc = netRealizedPnlSc;
            return this;
        }

        public Builder grossRealizedPnl(BigDecimal grossRealizedPnl) {
            position.grossRealizedPnl = grossRealizedPnl;
            return this;
        }

        public Builder grossRealizedPnlSc(BigDecimal grossRealizedPnlSc) {
            position.grossRealizedPnlSc = grossRealizedPnlSc;
            return this;
        }

        public Builder netAveragePrice(BigDecimal netAveragePrice) {
            position.netAveragePrice = netAveragePrice;
            return this;
        }

        public Builder grossAveragePrice(BigDecimal grossAveragePrice) {
            position.grossAveragePrice = grossAveragePrice;
            return this;
        }

        public Builder marketValue(BigDecimal marketValue) {
            position.marketValue = marketValue;
            return this;
        }

        public Builder marketValueSc(BigDecimal marketValueSc) {
            position.marketValueSc = marketValueSc;
            return this;
        }

        public Builder netUnrealizedPnl(BigDecimal netUnrealizedPnl) {
            position.netUnrealizedPnl = netUnrealizedPnl;
            return this;
        }

        public Builder netUnrealizedPnlSc(BigDecimal netUnrealizedPnlSc) {
            position.netUnrealizedPnlSc = netUnrealizedPnlSc;
            return this;
        }

        public Builder grossUnrealizedPnl(BigDecimal grossUnrealizedPnl) {
            position.grossUnrealizedPnl = grossUnrealizedPnl;
            return this;
        }

        public Builder grossUnrealizedPnlSc(BigDecimal grossUnrealizedPnlSc) {
            position.grossUnrealizedPnlSc = grossUnrealizedPnlSc;
            return this;
        }

        public Builder lastAppliedLedgerEntryId(Long lastAppliedLedgerEntryId) {
            position.lastAppliedLedgerEntryId = lastAppliedLedgerEntryId;
            return this;
        }

        public Builder sequenceNumber(long sequenceNumber) {
            position.sequenceNumber = sequenceNumber;
            return this;
        }

        public Builder createdBy(String createdBy) {
            position.createdBy = createdBy;
            return this;
        }

        public Builder updatedBy(String updatedBy) {
            position.updatedBy = updatedBy;
            return this;
        }

        public Position build() {
            if (isBlank(position.portfolioCurrency) && isBlank(position.accountCurrency)) {
                throw new IllegalArgumentException("portfolioCurrency and accountCurrency cannot both be blank for position: " + position);
            }

            return position;
        }
    }

    public static String toString(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return "null";
        }

        if (bigDecimal.compareTo(ZERO) == 0) {
            return "-";
        }

        return bigDecimal.stripTrailingZeros().toPlainString();
    }
}