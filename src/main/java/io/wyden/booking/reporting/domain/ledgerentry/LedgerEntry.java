package io.wyden.booking.reporting.domain.ledgerentry;

import com.vladmihalcea.hibernate.type.json.JsonType;
import io.wyden.booking.reporting.domain.PositionIdentifiers;
import io.wyden.booking.reporting.domain.transaction.TransactionFee;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import org.hibernate.annotations.Type;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Entity
public class LedgerEntry extends PositionIdentifiers {

    /**
     * date of the transaction
     */
    protected ZonedDateTime dateTime;

    private BigDecimal quantity;

    private BigDecimal price;

    /**
     * Sequence number of the WalEvent that created this ledger entry
     */
    private Long sequenceNumber;

    /**
     * reference identifier of the related reservation
     */
    private String reservationRef;

    /**
     * Transaction that created this entry (internal id)
     */
    private String transactionId;

    /**
     * Mutable parameter indicating whether LE is settled
     */
    private boolean isSettled;

    private String symbol;

    private BigDecimal balanceBefore;

    private BigDecimal balanceAfter;

    @Enumerated(EnumType.STRING)
    private LedgerEntryType ledgerEntryType;

    /**
     * Transaction fees for this ledger entry (unidirectional many-to-many)
     */
    @Column(columnDefinition = "jsonb")
    @Type(JsonType.class)
    protected List<TransactionFee> fees = new ArrayList<>();

    protected LedgerEntry() {
        // JPA
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * Creates a builder instance pre-populated with this object's current data
     *
     * @return builder with current object's data
     */
    public Builder toBuilder() {
        Builder builder = builder()
            .id(this.id)
            .createdAt(this.createdAt)
            .updatedAt(this.updatedAt)
            .version(this.version)
            .dateTime(this.dateTime)
            .quantity(this.quantity)
            .price(this.price)
            .sequenceNumber(this.sequenceNumber)
            .reservationRef(this.reservationRef)
            .transactionId(this.transactionId)
            .portfolioId(this.portfolioId)
            .accountId(this.accountId)
            .settled(this.isSettled)
            .symbol(this.symbol)
            .balanceBefore(this.balanceBefore)
            .balanceAfter(this.balanceAfter)
            .ledgerEntryType(this.ledgerEntryType)
            .portfolioType(this.portfolioType)
            .accountType(this.accountType)
            .accountWalletType(this.accountWalletType);

        // Add all fees
        if (this.fees != null && !this.fees.isEmpty()) {
            builder.addFees(this.fees);
        }

        return builder;
    }

    public Long getId() {
        return id;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public Timestamp getUpdatedAt() {
        return updatedAt;
    }

    public long getVersion() {
        return version;
    }

    public ZonedDateTime getDateTime() {
        return dateTime;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public Long getSequenceNumber() {
        return sequenceNumber;
    }

    public String getReservationRef() {
        return reservationRef;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public String getPortfolioId() {
        return portfolioId;
    }

    public String getAccountId() {
        return accountId;
    }

    public boolean isIsSettled() {
        return isSettled;
    }

    public String getSymbol() {
        return symbol;
    }

    public BigDecimal getBalanceBefore() {
        return balanceBefore;
    }

    public BigDecimal getBalanceAfter() {
        return balanceAfter;
    }

    public LedgerEntryType getLedgerEntryType() {
        return ledgerEntryType;
    }

    public List<TransactionFee> getFees() {
        return fees;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof LedgerEntry that)) return false;
        return version == that.version
               && isSettled == that.isSettled
               && Objects.equals(id, that.id)
               && Objects.equals(createdAt, that.createdAt)
               && Objects.equals(updatedAt, that.updatedAt)
               && Objects.equals(dateTime, that.dateTime)
               && Objects.equals(quantity, that.quantity)
               && Objects.equals(price, that.price)
               && Objects.equals(sequenceNumber, that.sequenceNumber)
               && Objects.equals(reservationRef, that.reservationRef)
               && Objects.equals(transactionId, that.transactionId)
               && Objects.equals(portfolioId, that.portfolioId)
               && Objects.equals(accountId, that.accountId)
               && Objects.equals(symbol, that.symbol)
               && Objects.equals(balanceBefore, that.balanceBefore)
               && Objects.equals(balanceAfter, that.balanceAfter)
               && ledgerEntryType == that.ledgerEntryType
               && portfolioType == that.portfolioType
               && accountType == that.accountType
               && accountWalletType == that.accountWalletType
               && Objects.equals(fees, that.fees);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            id,
            createdAt,
            updatedAt,
            version,
            dateTime,
            quantity,
            price,
            sequenceNumber,
            reservationRef,
            transactionId,
            portfolioId,
            accountId,
            isSettled,
            symbol,
            balanceBefore,
            balanceAfter,
            ledgerEntryType,
            portfolioType,
            accountType,
            accountWalletType,
            fees);
    }

    @Override
    public String toString() {
        return "LedgerEntry{" +
               "id=" + id +
               ", createdAt=" + createdAt +
               ", updatedAt=" + updatedAt +
               ", version=" + version +
               ", dateTime=" + dateTime +
               ", quantity=" + quantity +
               ", price=" + price +
               ", sequenceNumber=" + sequenceNumber +
               ", reservationRef='" + reservationRef + '\'' +
               ", transactionId='" + transactionId + '\'' +
               ", portfolioId='" + portfolioId + '\'' +
               ", accountId='" + accountId + '\'' +
               ", isSettled=" + isSettled +
               ", symbol='" + symbol + '\'' +
               ", balanceBefore=" + balanceBefore +
               ", balanceAfter=" + balanceAfter +
               ", ledgerEntryType=" + ledgerEntryType +
               ", portfolioType=" + portfolioType +
               ", accountType=" + accountType +
               ", accountWalletType=" + accountWalletType +
               ", fees=" + fees +
               '}';
    }

    /**
     * LedgerEntrySearchBuilder for creating LedgerEntry instances with a fluent API
     */
    public static class Builder {

        private final LedgerEntry ledgerEntry;

        private Builder() {
            this.ledgerEntry = new LedgerEntry();
        }

        public Builder id(Long id) {
            ledgerEntry.id = id;
            return this;
        }

        public Builder createdAt(Timestamp createdAt) {
            ledgerEntry.createdAt = createdAt;
            return this;
        }

        public Builder updatedAt(Timestamp updatedAt) {
            ledgerEntry.updatedAt = updatedAt;
            return this;
        }

        public Builder dateTime(ZonedDateTime dateTime) {
            ledgerEntry.dateTime = dateTime;
            return this;
        }

        public Builder version(long version) {
            ledgerEntry.version = version;
            return this;
        }

        public Builder quantity(BigDecimal quantity) {
            ledgerEntry.quantity = quantity;
            return this;
        }

        public Builder price(BigDecimal price) {
            ledgerEntry.price = price;
            return this;
        }

        public Builder sequenceNumber(Long sequenceNumber) {
            ledgerEntry.sequenceNumber = sequenceNumber;
            return this;
        }

        public Builder reservationRef(String reservationRef) {
            ledgerEntry.reservationRef = reservationRef;
            return this;
        }

        public Builder transactionId(String transactionId) {
            ledgerEntry.transactionId = transactionId;
            return this;
        }

        public Builder portfolioId(String portfolioId) {
            ledgerEntry.portfolioId = portfolioId;
            return this;
        }

        public Builder accountId(String accountId) {
            ledgerEntry.accountId = accountId;
            return this;
        }

        public Builder settled(boolean settled) {
            ledgerEntry.isSettled = settled;
            return this;
        }

        public Builder symbol(String symbol) {
            ledgerEntry.symbol = symbol;
            return this;
        }

        public Builder balanceBefore(BigDecimal balanceBefore) {
            ledgerEntry.balanceBefore = balanceBefore;
            return this;
        }

        public Builder balanceAfter(BigDecimal balanceAfter) {
            ledgerEntry.balanceAfter = balanceAfter;
            return this;
        }

        public Builder ledgerEntryType(LedgerEntryType ledgerEntryType) {
            ledgerEntry.ledgerEntryType = ledgerEntryType;
            return this;
        }

        public Builder portfolioType(RequestModel.PortfolioType portfolioType) {
            ledgerEntry.portfolioType = portfolioType;
            return this;
        }

        public Builder accountType(RequestModel.AccountType accountType) {
            ledgerEntry.accountType = accountType;
            return this;
        }

        public Builder accountWalletType(RequestModel.WalletType accountWalletType) {
            ledgerEntry.accountWalletType = accountWalletType;
            return this;
        }

        public Builder addFees(Collection<TransactionFee> fees) {
            ledgerEntry.fees = fees != null ? new ArrayList<>(fees) : new ArrayList<>();
            return this;
        }

        public Builder addFee(TransactionFee fee) {
            if (ledgerEntry.fees == null) {
                ledgerEntry.fees = new ArrayList<>();
            }
            ledgerEntry.fees.add(fee);
            return this;
        }

        public Builder clearFees() {
            ledgerEntry.fees = new ArrayList<>();
            return this;
        }

        /**
         * Builds a new LedgerEntry instance with the configured values
         *
         * @return new LedgerEntry instance
         */
        public LedgerEntry build() {
            return ledgerEntry;
        }
    }
}