package io.wyden.booking.reporting.domain.transaction;

import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

import static io.wyden.booking.reporting.domain.transaction.TransactionType.ACCOUNT_CASH_TRANSFER;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.CLIENT_ASSET_TRADE;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.CLIENT_CASH_TRADE;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.DEPOSIT;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.FEE;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.PORTFOLIO_ASSET_TRANSFER;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.PORTFOLIO_CASH_TRANSFER;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.STREET_ASSET_TRADE;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.STREET_CASH_TRADE;
import static io.wyden.booking.reporting.domain.transaction.TransactionType.WITHDRAWAL;

public final class TransactionPredicates {

    private TransactionPredicates() {
    }

    public static Predicate getPortfolioAndAccountPredicate(SimpleSearchRequest.AuthorizedSearchRequest search, CriteriaBuilder cb, Root<? extends TransactionIdentifiers> root) {
        Predicate isPortfolioAuthorized = isPortfolioAuthorized(search, cb, root.get("portfolioId"), root.get("portfolioType"));
        Predicate isPortfolioAuthorizedAndSelected = isPortfolioAuthorizedAndSelected(search, root.get("portfolioId"));

        Predicate isCounterPortfolioAuthorized = isPortfolioAuthorized(search, cb, root.get("counterPortfolioId"), root.get("counterPortfolioType"));
        Predicate isCounterPortfolioAuthorizedAndSelected = isPortfolioAuthorizedAndSelected(search, root.get("counterPortfolioId"));

        Predicate isSourcePortfolioAuthorized = isPortfolioAuthorized(search, cb, root.get("sourcePortfolioId"), root.get("sourcePortfolioType"));
        Predicate isSourcePortfolioAuthorizedAndSelected = isPortfolioAuthorizedAndSelected(search, root.get("sourcePortfolioId"));

        Predicate isTargetPortfolioAuthorized = isPortfolioAuthorized(search, cb, root.get("targetPortfolioId"), root.get("targetPortfolioType"));
        Predicate isTargetPortfolioAuthorizedAndSelected = isPortfolioAuthorizedAndSelected(search, root.get("targetPortfolioId"));

        Predicate isAccountAuthorized = isAccountAuthorized(search, cb, root.get("accountId"), root.get("accountType"), root.get("accountWalletType"));
        Predicate isAccountAuthorizedAndSelected = isAccountAuthorizedAndSelected(search, root.get("accountId"));

        Predicate isSourceAccountAuthorized = isAccountAuthorized(search, cb, root.get("sourceAccountId"), root.get("sourceAccountType"), root.get("sourceAccountWalletType"));
        Predicate isSourceAccountAuthorizedAndSelected = isAccountAuthorizedAndSelected(search, root.get("sourceAccountId"));

        Predicate isTargetAccountAuthorized = isAccountAuthorized(search, cb, root.get("targetAccountId"), root.get("targetAccountType"), root.get("targetAccountWalletType"));
        Predicate isTargetAccountAuthorizedAndSelected = isAccountAuthorizedAndSelected(search, root.get("targetAccountId"));

        Predicate isClientSideTransaction = root.get("transactionType").in(CLIENT_CASH_TRADE, CLIENT_ASSET_TRADE);
        Predicate isStreetSideTransaction = root.get("transactionType").in(STREET_CASH_TRADE, STREET_ASSET_TRADE, DEPOSIT, WITHDRAWAL, FEE);
        Predicate isPortfolioTransfer = root.get("transactionType").in(PORTFOLIO_CASH_TRANSFER, PORTFOLIO_ASSET_TRANSFER);
        Predicate isAccountTransfer = root.get("transactionType").in(ACCOUNT_CASH_TRANSFER);

        // empty request -> return all authorized transactions
        if (search.isEmptyRequest()) {
            // client trades
            Predicate isClientSideAuthorized = cb.and(
                isClientSideTransaction,
                cb.or(
                    isPortfolioAuthorized,
                    isCounterPortfolioAuthorized
                )
            );

            // street trades, payments (deposit, withdrawal), fees
            Predicate isStreetSideAuthorized = cb.and(
                isStreetSideTransaction,
                isPortfolioAuthorized,
                isAccountAuthorized
            );

            Predicate isPortfolioTransferAuthorized = cb.and(
                isPortfolioTransfer,
                cb.or(
                    isSourcePortfolioAuthorized,
                    isTargetPortfolioAuthorized
                )
            );

            Predicate isAccountTransferAuthorized = cb.and(
                isAccountTransfer,
                isSourceAccountAuthorized,
                isTargetAccountAuthorized
            );

            return cb.or(
                isClientSideAuthorized,
                isStreetSideAuthorized,
                isPortfolioTransferAuthorized,
                isAccountTransferAuthorized
//                settlementPredicate
            );
        }

        // request portfolios only -> return transactions where user has access either to portfolioId OR counterPortfolioId
        if (search.isRequestPortfoliosOnly()) {
            Predicate isClientSideAuthorized = cb.and(
                isClientSideTransaction,
                cb.or(
                    isPortfolioAuthorizedAndSelected,
                    isCounterPortfolioAuthorizedAndSelected
                ));

            // street trades, payments (deposit, withdrawal)
            Predicate isStreetSideAuthorized = cb.and(
                isStreetSideTransaction,
                isPortfolioAuthorizedAndSelected,
                isAccountAuthorized
            );

            Predicate isPortfolioTransferAuthorized = cb.and(
                isPortfolioTransfer,
                cb.or(
                    isSourcePortfolioAuthorizedAndSelected,
                    isTargetPortfolioAuthorizedAndSelected
                )
            );

            return cb.or(
                isClientSideAuthorized,
                isStreetSideAuthorized,
                isPortfolioTransferAuthorized
            );
        }

        // request accounts only -> return transactions where user has access both to portfolioId AND accountId
        if (search.isRequestAccountsOnly()) {

            // street trades, payments (deposit, withdrawal)
            Predicate isStreetSideAuthorized = cb.and(
                isStreetSideTransaction,
                isPortfolioAuthorized,
                isAccountAuthorizedAndSelected
            );

            Predicate isAccountTransferAuthorized = cb.and(
                isAccountTransfer,
                cb.or(
                    cb.and(
                        isSourceAccountAuthorizedAndSelected,
                        isTargetAccountAuthorized
                    ),
                    cb.and(
                        isSourceAccountAuthorized,
                        isTargetAccountAuthorizedAndSelected
                    )
                )
            );

            return cb.or(
                isStreetSideAuthorized,
                isAccountTransferAuthorized
            );
        }

        // request portfolios and accounts -> return transactions where user has access both to portfolioId AND accountId
        // street trades, payments (deposit, withdrawal)
        return cb.and(
            isPortfolioAuthorizedAndSelected,
            isAccountAuthorizedAndSelected);
    }

    public static Predicate isPortfolioAuthorizedAndSelected(SimpleSearchRequest.AuthorizedSearchRequest search, Path<Object> portfolioId) {
        return portfolioId.in(search.requestedAuthorizedPortfolioIds());
    }

    public static Predicate isPortfolioAuthorized(SimpleSearchRequest.AuthorizedSearchRequest search, CriteriaBuilder cb, Path<Object> portfolioId, Path<Object> portfolioType) {
        return cb.or(
            isPortfolioAuthorizedById(search, portfolioId),
            isPortfolioAuthorizedByType(search, cb, portfolioType)
        );
    }

    private static Predicate isPortfolioAuthorizedById(SimpleSearchRequest.AuthorizedSearchRequest search, Path<Object> portfolioId) {
        // static read access to portfolio
        if (search.allPortfoliosAuthorized()) {
            return portfolioId.isNotNull();
        }

        return portfolioId.in(search.authorizedPortfolioIds());
    }

    private static Predicate isPortfolioAuthorizedByType(SimpleSearchRequest.AuthorizedSearchRequest search, CriteriaBuilder cb, Path<Object> portfolioType) {
        if (search.portfolioType() == null) {
            return cb.disjunction();
        }

        if (RequestModel.PortfolioType.NONE.equals(search.portfolioType())) {
            return cb.disjunction();
        }

        if (RequestModel.PortfolioType.ALL.equals(search.portfolioType())) {
            return cb.conjunction();
        }

        return cb.or(
            portfolioType.isNull(),
            cb.equal(portfolioType, search.portfolioType())
        );
    }

    public static Predicate isAccountAuthorizedAndSelected(SimpleSearchRequest.AuthorizedSearchRequest search, Path<Object> accountId) {
        return accountId.in(search.requestedAuthorizedAccountIds());
    }

    public static Predicate isAccountAuthorized(SimpleSearchRequest.AuthorizedSearchRequest search, CriteriaBuilder cb, Path<Object> accountId, Path<Object> accountType, Path<Object> walletType) {
        return cb.or(
            isAccountAuthorizedById(search, accountId),
            isAccountAuthorizedByType(search, cb, accountType, walletType)
        );
    }

    private static Predicate isAccountAuthorizedById(SimpleSearchRequest.AuthorizedSearchRequest search, Path<Object> accountId) {
        // static read access to venue.account
        if (search.allAccountsAuthorized()) {
            return accountId.isNotNull();
        }

        return accountId.in(search.authorizedAccountIds());
    }

    private static Predicate isAccountAuthorizedByType(SimpleSearchRequest.AuthorizedSearchRequest search, CriteriaBuilder cb, Path<Object> accountType, Path<Object> walletType) {
        if (search.accountType() == null) {
            return cb.disjunction();
        }

        if (RequestModel.AccountType.NONE.equals(search.accountType())) {
            return cb.disjunction();
        }

        if (RequestModel.AccountType.ALL.equals(search.accountType())) {
            return cb.conjunction();
        }

        Predicate accountTypeEmptyPredicate = accountType.isNull();
        Predicate accountTypePredicate = accountType.in(search.accountType());

        if (RequestModel.WalletType.NOSTRO.equals(search.walletType()) || RequestModel.WalletType.VOSTRO.equals(search.walletType())) {
            Predicate walletTypePredicate = walletType.in(search.walletType());
            Predicate emptywalletTypePredicate = walletType.isNull();

            return cb.and(
                cb.or(accountTypeEmptyPredicate, accountTypePredicate),
                cb.or(emptywalletTypePredicate, walletTypePredicate));
        }

        return cb.or(accountTypeEmptyPredicate, accountTypePredicate);
    }
}
