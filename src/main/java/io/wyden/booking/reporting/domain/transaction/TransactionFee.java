package io.wyden.booking.reporting.domain.transaction;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;

import java.math.BigDecimal;

public class TransactionFee {

    /**
     * amount of fee charged
     */
    private BigDecimal amount;

    /**
     * currency fee is charged in
     */
    private String currency;

    /**
     * arbitrary description of the fee
     */
    private String description;

    @Enumerated(EnumType.STRING)
    private TransactionFeeType feeType;

    public TransactionFee(BigDecimal amount,
                          String currency,
                          String description,
                          TransactionFeeType feeType) {
        this.amount = amount;
        this.currency = currency;
        this.description = description;
        this.feeType = feeType;
    }

    public TransactionFee() {
        // JPA
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public String getCurrency() {
        return currency;
    }

    public String getDescription() {
        return description;
    }

    public TransactionFeeType getFeeType() {
        return feeType;
    }

    @Override
    public String toString() {
        return "TransactionFee{" +
            "amount=" + amount +
            ", currency=" + currency +
            ", description='" + description + '\'' +
            ", feeType=" + feeType +
            '}';
    }
}
