package io.wyden.booking.reporting.domain.balance;

import io.wyden.booking.reporting.domain.PositionIdentifiers;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;

import java.math.BigDecimal;
import java.util.Objects;

import static io.wyden.booking.reporting.utils.HostUtils.resolveHostname;

@Entity
public class Balance extends PositionIdentifiers {

    private String symbol;

    private BigDecimal quantity;

    private BigDecimal pendingQuantity;

    private BigDecimal availableForTradingQuantity;

    private BigDecimal availableForWithdrawalQuantity;

    private BigDecimal settledQuantity;

    private BigDecimal unsettledQuantity;

    private String currency;

    private String lastAppliedLedgerEntryId;

    private long sequenceNumber;

    private String createdBy;

    private String updatedBy;

    @PrePersist
    public void prePersist() {
        this.createdBy = this.updatedBy = resolveHostname();
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedBy = resolveHostname();
    }

    public String getPortfolioId() {
        return portfolioId;
    }

    public void setPortfolioId(String portfolioId) {
        this.portfolioId = portfolioId;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPendingQuantity() {
        return pendingQuantity;
    }

    public void setPendingQuantity(BigDecimal pendingQuantity) {
        this.pendingQuantity = pendingQuantity;
    }

    public BigDecimal getAvailableForTradingQuantity() {
        return availableForTradingQuantity;
    }

    public void setAvailableForTradingQuantity(BigDecimal availableForTradingQuantity) {
        this.availableForTradingQuantity = availableForTradingQuantity;
    }

    public BigDecimal getAvailableForWithdrawalQuantity() {
        return availableForWithdrawalQuantity;
    }

    public void setAvailableForWithdrawalQuantity(BigDecimal availableForWithdrawalQuantity) {
        this.availableForWithdrawalQuantity = availableForWithdrawalQuantity;
    }

    public BigDecimal getSettledQuantity() {
        return settledQuantity;
    }

    public void setSettledQuantity(BigDecimal settledQuantity) {
        this.settledQuantity = settledQuantity;
    }

    public BigDecimal getUnsettledQuantity() {
        return unsettledQuantity;
    }

    public void setUnsettledQuantity(BigDecimal unsettledQuantity) {
        this.unsettledQuantity = unsettledQuantity;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getLastAppliedLedgerEntryId() {
        return lastAppliedLedgerEntryId;
    }

    public void setLastAppliedLedgerEntryId(String lastAppliedLedgerEntryId) {
        this.lastAppliedLedgerEntryId = lastAppliedLedgerEntryId;
    }

    public long getSequenceNumber() {
        return sequenceNumber;
    }

    public void setSequenceNumber(long sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public static Builder builder() {
        return new Builder();
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof Balance that)) return false;
        return sequenceNumber == that.sequenceNumber
               && Objects.equals(id, that.id)
               && Objects.equals(quantity, that.quantity)
               && Objects.equals(pendingQuantity, that.pendingQuantity)
               && Objects.equals(availableForTradingQuantity, that.availableForTradingQuantity)
               && Objects.equals(availableForWithdrawalQuantity, that.availableForWithdrawalQuantity)
               && Objects.equals(settledQuantity, that.settledQuantity)
               && Objects.equals(unsettledQuantity, that.unsettledQuantity)
               && Objects.equals(currency, that.currency)
               && Objects.equals(lastAppliedLedgerEntryId, that.lastAppliedLedgerEntryId)
               && Objects.equals(createdAt, that.createdAt)
               && Objects.equals(updatedAt, that.updatedAt)
               && Objects.equals(createdBy, that.createdBy)
               && Objects.equals(updatedBy, that.updatedBy);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            id,
            quantity,
            pendingQuantity,
            availableForTradingQuantity,
            availableForWithdrawalQuantity,
            settledQuantity,
            unsettledQuantity,
            currency,
            lastAppliedLedgerEntryId,
            sequenceNumber,
            createdAt,
            updatedAt,
            createdBy,
            updatedBy);
    }

    public static class Builder {
        private final Balance balance;

        public Builder() {
            this.balance = new Balance();
        }

        public Builder id(Long id) {
            balance.id = id;
            return this;
        }

        public Builder portfolioId(String portfolioId) {
            balance.portfolioId = portfolioId;
            return this;
        }

        public Builder accountId(String accountId) {
            balance.accountId = accountId;
            return this;
        }

        public Builder portfolioType(RequestModel.PortfolioType portfolioType) {
            balance.portfolioType = portfolioType;
            return this;
        }

        public Builder accountType(RequestModel.AccountType accountType) {
            balance.accountType = accountType;
            return this;
        }

        public Builder accountWalletType(RequestModel.WalletType accountWalletType) {
            balance.accountWalletType = accountWalletType;
            return this;
        }

        public Builder symbol(String symbol) {
            balance.symbol = symbol;
            return this;
        }

        public Builder quantity(BigDecimal quantity) {
            balance.quantity = quantity;
            return this;
        }

        public Builder pendingQuantity(BigDecimal pendingQuantity) {
            balance.pendingQuantity = pendingQuantity;
            return this;
        }

        public Builder availableForTradingQuantity(BigDecimal availableForTradingQuantity) {
            balance.availableForTradingQuantity = availableForTradingQuantity;
            return this;
        }

        public Builder availableForWithdrawalQuantity(BigDecimal availableForWithdrawalQuantity) {
            balance.availableForWithdrawalQuantity = availableForWithdrawalQuantity;
            return this;
        }

        public Builder settledQuantity(BigDecimal settledQuantity) {
            balance.settledQuantity = settledQuantity;
            return this;
        }

        public Builder unsettledQuantity(BigDecimal unsettledQuantity) {
            balance.unsettledQuantity = unsettledQuantity;
            return this;
        }

        public Builder currency(String currency) {
            balance.currency = currency;
            return this;
        }

        public Builder lastAppliedLedgerEntryId(String lastAppliedLedgerEntryId) {
            balance.lastAppliedLedgerEntryId = lastAppliedLedgerEntryId;
            return this;
        }

        public Builder sequenceNumber(long sequenceNumber) {
            balance.sequenceNumber = sequenceNumber;
            return this;
        }

        public Balance build() {
            return balance;
        }
    }
}