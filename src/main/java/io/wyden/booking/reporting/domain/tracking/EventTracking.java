package io.wyden.booking.reporting.domain.tracking;

import jakarta.persistence.*;

import java.time.ZonedDateTime;

@Entity
@Table(name = "event_tracking")
public class EventTracking {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "event_type", nullable = false, unique = true, length = 50)
    private String eventType;

    @Column(name = "last_processed_sequence", nullable = false)
    private Long lastProcessedSequence = 0L;

    @Column(name = "updated_at", nullable = false)
    private ZonedDateTime updatedAt = ZonedDateTime.now();

    // Constructors
    public EventTracking() {}

    public EventTracking(String eventType, Long lastProcessedSequence) {
        this.eventType = eventType;
        this.lastProcessedSequence = lastProcessedSequence;
        this.updatedAt = ZonedDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public Long getLastProcessedSequence() {
        return lastProcessedSequence;
    }

    public void setLastProcessedSequence(Long lastProcessedSequence) {
        this.lastProcessedSequence = lastProcessedSequence;
        this.updatedAt = ZonedDateTime.now();
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = ZonedDateTime.now();
    }

    public ZonedDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}