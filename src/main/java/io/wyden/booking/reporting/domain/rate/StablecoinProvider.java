package io.wyden.booking.reporting.domain.rate;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public class StablecoinProvider {

    /**
     * Map (stablecoin -> fiat)
     */
    private final Map<String, String> stablecoinMapping;

    public StablecoinProvider(Map<String, String> stablecoinMapping) {
        this.stablecoinMapping = stablecoinMapping;
    }

    public boolean areDifferent(String currency, String compare) {
        return !currencyMatch(currency, compare);
    }

    public boolean currencyMatch(String currency, String stablecoin) {
        if (currency.equals(stablecoin)) {
            return true;
        }

        String fiat = stablecoinMapping.get(stablecoin);
        if (fiat == null) {
            return false;
        }

        return Objects.equals(currency, fiat);
    }

    /**
     * Check if currency is stablecoin, like USDT
     */
    public boolean isStablecoin(String stablecoin) {
        return stablecoinMapping.containsKey(stablecoin);
    }

    /**
     * Find matching fiat for stablecoin, for example: USDT -> USD
     */
    public Optional<String> getFiat(String stablecoin) {
        return Optional.ofNullable(stablecoinMapping.get(stablecoin));
    }

    /**
     * Make sure currency is fiat. Either convert it to fiat or return immediately (if not stablecoin)
     */
    public String toFiat(String currency) {
        return stablecoinMapping.getOrDefault(currency, currency);
    }
}
