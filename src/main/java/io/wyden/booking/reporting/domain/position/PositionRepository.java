package io.wyden.booking.reporting.domain.position;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PositionRepository extends JpaRepository<Position, Long>, PositionRepositoryCustom {

    @Query("SELECT p FROM Position p WHERE p.symbol = :symbol AND " +
           "((:accountId IS NULL OR :accountId = '') OR p.accountId = :accountId) AND " +
           "((:portfolioId IS NULL OR :portfolioId = '') OR p.portfolioId = :portfolioId) AND " +
           "(p.accountId IS NOT NULL OR p.portfolioId IS NOT NULL)")
    Optional<Position> findBySymbolAndAccountIdOrPortfolioId(@Param("symbol") String symbol, 
                                                            @Param("accountId") String accountId, 
                                                            @Param("portfolioId") String portfolioId);
}