package io.wyden.booking.reporting.domain.transaction;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum TransactionType {

    CLIENT_CASH_TRADE(Values.CLIENT_CASH_TRADE),
    STREET_CASH_TRADE(Values.STREET_CASH_TRADE),
    CLIENT_ASSET_TRADE(Values.CLIENT_ASSET_TRADE),
    STREET_ASSET_TRADE(Values.STREET_ASSET_TRADE),
    DEPOSIT(Values.DEPOSIT),
    WITHDRAWAL(Values.WITHDRAWAL),
    POR<PERSON><PERSON><PERSON>_CASH_TRANSFER(Values.PORTFOLIO_CASH_TRANSFER),
    ACCOUNT_CASH_TRANSFER(Values.ACCOUNT_CASH_TRANSFER),
    PORTFOLIO_ASSET_TRANSFER(Values.PORTFOLIO_ASSET_TRANSFER),
    FEE(Values.FEE),
    SETTLEMENT(Values.SETTLEMENT);

    private final String transactionType;

    TransactionType(String transactionType) {
        // force equality between name of enum instance, and value of constant
        if (!this.name().equals(transactionType)) {
            throw new IllegalArgumentException("Transaction type has to match enum value");
        }
        this.transactionType = transactionType;
    }

    public static List<String> stringValues() {
        return Arrays.stream(values())
            .map(value -> value.transactionType)
            .collect(Collectors.toList());
    }

    // To use enum values as type discriminator (JPA @DiscriminatorColumn), it has to be a String
    public static class Values {
        public static final String CLIENT_CASH_TRADE = "CLIENT_CASH_TRADE";
        public static final String STREET_CASH_TRADE = "STREET_CASH_TRADE";
        public static final String CLIENT_ASSET_TRADE = "CLIENT_ASSET_TRADE";
        public static final String STREET_ASSET_TRADE = "STREET_ASSET_TRADE";
        public static final String DEPOSIT = "DEPOSIT";
        public static final String WITHDRAWAL = "WITHDRAWAL";
        public static final String FEE = "FEE";
        public static final String ASSET_TRANSFER = "ASSET_TRANSFER";
        public static final String PORTFOLIO_CASH_TRANSFER = "PORTFOLIO_CASH_TRANSFER";
        public static final String PORTFOLIO_ASSET_TRANSFER = "PORTFOLIO_ASSET_TRANSFER";
        public static final String ACCOUNT_CASH_TRANSFER = "ACCOUNT_CASH_TRANSFER";
        public static final String SETTLEMENT = "SETTLEMENT";
    }
}
