package io.wyden.booking.reporting.domain.transaction;

import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.query.QueryUtils;

import java.util.ArrayList;
import java.util.List;

import static io.wyden.booking.reporting.domain.transaction.TransactionPredicates.getPortfolioAndAccountPredicate;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class TransactionRepositoryCustomImpl implements TransactionRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<Transaction> findByProperties(RequestModel.AuthorizedTransactionSearch search, Pageable pageable) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Transaction> query = cb.createQuery(Transaction.class);
        Root<Transaction> root = query.from(Transaction.class);

        List<Predicate> predicates = buildPredicates(cb, root, search);

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // Add sorting
        if (pageable.getSort().isSorted()) {
            query.orderBy(QueryUtils.toOrders(pageable.getSort(), root, cb));
        }

        TypedQuery<Transaction> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<Transaction> content = typedQuery.getResultList();
        long total = getTotalCount(search);

        return new PageImpl<>(content, pageable, total);
    }

    private long getTotalCount(RequestModel.AuthorizedTransactionSearch search) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Transaction> root = countQuery.from(Transaction.class);

        List<Predicate> predicates = buildPredicates(cb, root, search);

        if (!predicates.isEmpty()) {
            countQuery.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        countQuery.select(cb.count(root));
        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private List<Predicate> buildPredicates(CriteriaBuilder cb, Root<Transaction> root, RequestModel.AuthorizedTransactionSearch authorizedTransactionSearch) {
        RequestModel.TransactionSearch search = authorizedTransactionSearch.searchRequest();
        SimpleSearchRequest.AuthorizedSearchRequest authorizedSearch = authorizedTransactionSearch.authorizedSearchRequest();

        List<Predicate> predicates = new ArrayList<>();

        // UUID filter
        if (search.uuids() != null && !search.uuids().isEmpty()) {
            predicates.add(root.get("uuid").in(search.uuids()));
        }

        // Reservation reference filter
        if (search.reservationRef() != null && !search.reservationRef().isBlank()) {
            predicates.add(cb.equal(root.get("reservationRef"), search.reservationRef()));
        }

        if (search.orderId() != null && !search.orderId().isBlank()) {
            predicates.add(cb.equal(root.get("orderId"), search.orderId()));
        }

        if (search.parentOrderId() != null && !search.parentOrderId().isBlank()) {
            predicates.add(cb.equal(root.get("parentOrderId"), search.parentOrderId()));
        }

        if (search.rootOrderId() != null && !search.rootOrderId().isBlank()) {
            predicates.add(cb.equal(root.get("rootOrderId"), search.rootOrderId()));
        }

        if (search.executionId() != null && !search.executionId().isBlank()) {
            predicates.add(cb.equal(root.get("executionId"), search.executionId()));
        }

        if (search.venueExecutionId() != null && !search.venueExecutionId().isBlank()) {
            predicates.add(cb.equal(root.get("venueExecutionId"), search.venueExecutionId()));
        }

        if (search.underlyingExecutionId() != null && !search.underlyingExecutionId().isBlank()) {
            predicates.add(cb.equal(root.get("underlyingExecutionId"), search.underlyingExecutionId()));
        }

        if (search.rootExecutionId() != null && !search.rootExecutionId().isBlank()) {
            predicates.add(cb.equal(root.get("rootExecutionId"), search.rootExecutionId()));
        }

        // Date range filters
        if (search.from() != null) {
            predicates.add(cb.greaterThanOrEqualTo(root.get("dateTime"), search.from()));
        }

        if (search.to() != null) {
            predicates.add(cb.lessThan(root.get("dateTime"), search.to()));
        }

        // Settlement date filters
        if (search.settlementFrom() != null) {
            predicates.add(cb.greaterThanOrEqualTo(root.get("settlementDateTime"), search.settlementFrom()));
        }

        if (search.settlementTo() != null) {
            predicates.add(cb.lessThan(root.get("settlementDateTime"), search.settlementTo()));
        }

        // Settlement status filter
        if (search.settled() != null) {
            predicates.add(cb.equal(root.get("isSettled"), search.settled()));
        }

        // Exclude problematic execution types
        predicates.add(cb.or(
            cb.isNull(root.get("execType")),
            cb.not(root.get("execType").in(ExecType.CANCELED, ExecType.REJECTED, ExecType.EXPIRED))
        ));

        // Symbol filter (asset, currency, baseCurrency)
        if (search.symbol() != null && !search.symbol().isEmpty()) {
            predicates.add(cb.or(
                root.get("asset").in(search.symbol()),
                root.get("currency").in(search.symbol()),
                root.get("baseCurrency").in(search.symbol())
            ));
        }

        // Currency filter (asset, currency, baseCurrency)
        if (search.currency() != null && !search.currency().isEmpty()) {
            predicates.add(cb.or(
                root.get("asset").in(search.currency()),
                root.get("currency").in(search.currency()),
                root.get("baseCurrency").in(search.currency())
            ));
        }

        // Transaction type filter
        if (search.transactionType() != null && !search.transactionType().isEmpty()) {
            predicates.add(root.get("transactionType").in(search.transactionType()));
        }

        if (isNotBlank(search.after())) {
            long cursor = Long.parseLong(search.after());
            Predicate laterPredicate = cb.greaterThan(root.get("id"), cursor);
            Predicate earlierPredicate = cb.lessThan(root.get("id"), cursor);
            Predicate afterPredicate = search.sortingOrder() == RequestModel.SortingOrder.ASC ? laterPredicate : earlierPredicate;
            predicates.add(afterPredicate);
        }

        predicates.add(getPortfolioAndAccountPredicate(authorizedSearch, cb, root));

        return predicates;
    }
}
