package io.wyden.booking.reporting.domain.ledgerentry;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;

public interface LedgerEntryRepository extends JpaRepository<LedgerEntry, Long>, LedgerEntryRepositoryCustom {

    Collection<LedgerEntry> findBySequenceNumber(long sequenceNumber);

    Collection<LedgerEntry> findByReservationRef(String reservationRef);
}
