package io.wyden.booking.reporting.domain.balance;

import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.query.QueryUtils;

import java.util.ArrayList;
import java.util.List;

import static io.wyden.booking.reporting.domain.ledgerentry.LedgerEntryRepositoryCustomImpl.getPortfolioAndAccountPredicate;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class BalanceRepositoryCustomImpl implements BalanceRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<Balance> findByProperties(RequestModel.AuthorizedPositionSearch search, Pageable pageable) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Balance> query = cb.createQuery(Balance.class);
        Root<Balance> root = query.from(Balance.class);

        List<Predicate> predicates = buildPredicates(cb, root, search);

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // Add sorting
        if (pageable.getSort().isSorted()) {
            query.orderBy(QueryUtils.toOrders(pageable.getSort(), root, cb));
        }

        TypedQuery<Balance> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<Balance> content = typedQuery.getResultList();
        long total = getTotalCount(search);

        return new PageImpl<>(content, pageable, total);
    }

    private long getTotalCount(RequestModel.AuthorizedPositionSearch search) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Balance> root = countQuery.from(Balance.class);

        List<Predicate> predicates = buildPredicates(cb, root, search);

        if (!predicates.isEmpty()) {
            countQuery.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        countQuery.select(cb.count(root));
        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private List<Predicate> buildPredicates(CriteriaBuilder cb, Root<Balance> root, RequestModel.AuthorizedPositionSearch ledgerEntrySearch) {
        RequestModel.PositionSearch search = ledgerEntrySearch.searchRequest();
        SimpleSearchRequest.AuthorizedSearchRequest authorizedSearch = ledgerEntrySearch.authorizedSearchRequest();

        List<Predicate> predicates = new ArrayList<>();

        // Symbol filter (asset, currency, baseCurrency)
        if (search.symbol() != null && !search.symbol().isEmpty()) {
            predicates.add(root.get("symbol").in(search.symbol()));
        }

        // Currency filter (asset, currency, baseCurrency)
        if (search.currency() != null && !search.currency().isEmpty()) {
            predicates.add(root.get("currency").in(search.currency()));
        }

        if (isNotBlank(search.after())) {
            long cursor = Long.parseLong(search.after());
            Predicate laterPredicate = cb.greaterThan(root.get("id"), cursor);
            Predicate earlierPredicate = cb.lessThan(root.get("id"), cursor);
            Predicate afterPredicate = search.sortingOrder() == RequestModel.SortingOrder.ASC ? laterPredicate : earlierPredicate;
            predicates.add(afterPredicate);
        }

        predicates.add(getPortfolioAndAccountPredicate(authorizedSearch, cb, root));

        return predicates;
    }
}
