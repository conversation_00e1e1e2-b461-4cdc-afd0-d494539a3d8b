package io.wyden.booking.reporting.domain.position;

import io.wyden.booking.reporting.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.query.QueryUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static io.wyden.booking.reporting.domain.ledgerentry.LedgerEntryRepositoryCustomImpl.getPortfolioAndAccountPredicate;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Repository
public class PositionRepositoryCustomImpl implements PositionRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<Position> findByProperties(RequestModel.AuthorizedPositionSearch search, Pageable pageable) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Position> query = cb.createQuery(Position.class);
        Root<Position> root = query.from(Position.class);

        List<Predicate> predicates = buildPredicates(cb, root, search);

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // Add sorting
        if (pageable.getSort().isSorted()) {
            query.orderBy(QueryUtils.toOrders(pageable.getSort(), root, cb));
        }

        TypedQuery<Position> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<Position> content = typedQuery.getResultList();
        long total = getTotalCount(search);

        return new PageImpl<>(content, pageable, total);
    }

    private long getTotalCount(RequestModel.AuthorizedPositionSearch search) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Position> root = countQuery.from(Position.class);

        List<Predicate> predicates = buildPredicates(cb, root, search);

        if (!predicates.isEmpty()) {
            countQuery.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        countQuery.select(cb.count(root));
        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private List<Predicate> buildPredicates(CriteriaBuilder cb, Root<Position> root, RequestModel.AuthorizedPositionSearch positionSearch) {
        RequestModel.PositionSearch search = positionSearch.searchRequest();
        SimpleSearchRequest.AuthorizedSearchRequest authorizedSearch = positionSearch.authorizedSearchRequest();

        List<Predicate> predicates = new ArrayList<>();

        // Symbol filter (asset, currency, baseCurrency)
        if (search.symbol() != null && !search.symbol().isEmpty()) {
            predicates.add(root.get("symbol").in(search.symbol()));
        }

        // Currency
        if (search.currency() != null && !search.currency().isEmpty()) {
            predicates.add(root.get("currency").in(search.currency()));
        }

        // OrderId predicate using subqueries
        if (isNotBlank(search.orderId())) {
            predicates.add(createOrderIdPredicate(cb, root, search));
        }

        if (isNotBlank(search.after())) {
            long cursor = Long.parseLong(search.after());
            Predicate laterPredicate = cb.greaterThan(root.get("id"), cursor);
            Predicate earlierPredicate = cb.lessThan(root.get("id"), cursor);
            Predicate afterPredicate = search.sortingOrder() == RequestModel.SortingOrder.ASC ? laterPredicate : earlierPredicate;
            predicates.add(afterPredicate);
        }

        predicates.add(getPortfolioAndAccountPredicate(authorizedSearch, cb, root));

        return predicates;
    }

    private Predicate createOrderIdPredicate(CriteriaBuilder cb, Root<Position> root, RequestModel.PositionSearch search) {
         // Subquery to find ledger entries with the requested orderId
        Subquery<String> ledgerSubquery = cb.createQuery().subquery(String.class);
        Root<LedgerEntry> ledgerRoot = ledgerSubquery.from(LedgerEntry.class);
        
        // Select symbol from ledger entries where reservationRef matches orderId
        ledgerSubquery.select(ledgerRoot.get("symbol"))
                      .where(cb.equal(ledgerRoot.get("reservationRef"), search.orderId()));
        
        // Subquery to find portfolio/account combinations from ledger entries
        Subquery<String> portfolioSubquery = cb.createQuery().subquery(String.class);
        Root<LedgerEntry> portfolioLedgerRoot = portfolioSubquery.from(LedgerEntry.class);
        portfolioSubquery.select(portfolioLedgerRoot.get("portfolioId"))
                         .where(cb.equal(portfolioLedgerRoot.get("reservationRef"), search.orderId()));
        
        Subquery<String> accountSubquery = cb.createQuery().subquery(String.class);
        Root<LedgerEntry> accountLedgerRoot = accountSubquery.from(LedgerEntry.class);
        accountSubquery.select(accountLedgerRoot.get("accountId"))
                       .where(cb.equal(accountLedgerRoot.get("reservationRef"), search.orderId()));
        
        // The main predicate: positions with matching symbol AND (portfolioId OR accountId)
        Predicate symbolPredicate = root.get("symbol").in(ledgerSubquery);
        Predicate portfolioPredicate = root.get("portfolioId").in(portfolioSubquery);
        Predicate accountPredicate = root.get("accountId").in(accountSubquery);
        
        return cb.and(
            symbolPredicate,
            cb.or(portfolioPredicate, accountPredicate)
        );
    }
}
