package io.wyden.booking.reporting.domain.rate;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static org.slf4j.LoggerFactory.getLogger;

@Component
public class SystemCurrencyProvider {

    private static final Logger LOGGER = getLogger(SystemCurrencyProvider.class);

    public static final String DEFAULT_SYSTEM_CURRENCY = "USD";

    private static String systemCurrency = DEFAULT_SYSTEM_CURRENCY;

    public static String getSystemCurrency() {
        return systemCurrency;
    }

    @Value("${booking-reporting.default-currency}")
    public void setSystemCurrency(String systemCurrency) {

        if (alreadySet()) {
            // system currency has already been configured - further customization is not allowed
            throw new IllegalStateException("System currency has already been set to: " + getSystemCurrency());
        }

        LOGGER.info("Setting system currency to: {}", systemCurrency);

        // dirty hack for backward compatibility to allow for static field access without spring beans
        SystemCurrencyProvider.systemCurrency = systemCurrency;
    }

    private static boolean alreadySet() {
        return !getSystemCurrency().equalsIgnoreCase(DEFAULT_SYSTEM_CURRENCY);
    }
}
