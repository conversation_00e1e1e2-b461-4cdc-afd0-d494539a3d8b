package io.wyden.booking.reporting.domain.transaction;

import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Objects;

@Entity
public class Transaction extends TransactionIdentifiers {

    /**
     * the quantity that was executed
     */
    private BigDecimal quantity;

    /**
     * the quantity that is still pending
     */
    private BigDecimal leavesQuantity;

    /**
     * the unit price at which the trade was executed
     */
    private BigDecimal price;

    /**
     * the transaction / quote currency
     */
    private String currency;

    /**
     * the currency that represents the quantity of the trade, for a BTC/USD trade, BTC is the baseCurrency
     */
    private String baseCurrency;

    /**
     * The Instrument traded
     */
    private String asset;

    /**
     * internal ID of the execution
     */
    private String executionId;

    /**
     * external / custody / venue ID of the execution
     */
    private String venueExecutionId;

    /**
     * Internal order identifier generated by the connector, formatted according to external venue requirements.
     * Used for order tracking within the venue-specific connector.
     */
    private String intOrderId;

    /**
     * External order identifier assigned by the trading venue/exchange.
     * Represents how the order is identified in the external venue's system.
     */
    private String extOrderId;

    /**
     * Internal OEMS order identifier.
     * Primary identifier for the order within Wyden's OEMS system.
     */
    private String orderId;

    /**
     * Direct parent order identifier.
     * References the immediate parent order in a chain of orders. For example:
     * - For SOR child orders, references the SOR parent order
     * - For agency street-side orders, references the agency client-side order
     */
    private String parentOrderId;

    /**
     * The execution ID of the parent/underlying trade that triggered this trade.
     * Used to establish a hierarchical relationship between related trades, particularly
     * useful in scenarios where one trade leads to or triggers another trade.
     */
    private String rootOrderId;

    /**
     * The execution ID of the parent/underlying trade that triggered this trade.
     * Used to establish a hierarchical relationship between related trades, particularly
     * useful in scenarios where one trade leads to or triggers another trade.
     */
    private String underlyingExecutionId;

    /**
     * The execution ID of the original/root trade in the execution chain.
     * This ID represents the very first trade that initiated a series of related trades,
     * allowing tracking of the complete execution hierarchy back to its origin.
     * Unlike underlyingExecutionId which points to the immediate parent, this points to the top-most ancestor.
     */
    private String rootExecutionId;

    /**
     * References the original order ID from the client space (FIX API/REST API/GQL API).
     * Only present for orders that originated from client interactions.
     * This ID is propagated through the entire order chain, allowing tracking back to
     * the initial client request regardless of how many internal orders were created.
     * Unlike rootOrderId which tracks the OEMS space hierarchy, this tracks the client space origin.
     */
    private String clientRootOrderId;

    /**
     * arbitrary description of the transaction, i.e. description received from custodians or venues
     */
    private String description;

    private boolean isLive;

    private boolean isSettled;

    private ZonedDateTime settlementDateTime;

    private String settlementId;

    @Enumerated(EnumType.STRING)
    private SettlementType settlementType;

    @Enumerated(EnumType.STRING)
    private ExecType execType;

    protected Transaction() {
        // JPA
    }

    public String getExecutionId() {
        return executionId;
    }

    public String getVenueExecutionId() {
        return venueExecutionId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public BigDecimal getLeavesQuantity() {
        return leavesQuantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public String getCurrency() {
        return currency;
    }

    public String getBaseCurrency() {
        return baseCurrency;
    }

    public String getAsset() {
        return asset;
    }

    public String getIntOrderId() {
        return intOrderId;
    }

    public String getExtOrderId() {
        return extOrderId;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getParentOrderId() {
        return parentOrderId;
    }

    public String getRootOrderId() {
        return rootOrderId;
    }

    public String getUnderlyingExecutionId() {
        return underlyingExecutionId;
    }

    public String getRootExecutionId() {
        return rootExecutionId;
    }

    public String getClientRootOrderId() {
        return clientRootOrderId;
    }

    public String getDescription() {
        return description;
    }

    public boolean isLive() {
        return isLive;
    }

    public boolean isSettled() {
        return isSettled;
    }

    public ZonedDateTime getSettlementDateTime() {
        return settlementDateTime;
    }

    public String getSettlementId() {
        return settlementId;
    }

    public SettlementType getSettlementType() {
        return settlementType;
    }

    public ExecType getExecType() {
        return execType;
    }

    public void settleIfPossible() {
        boolean isSettledByDefault = getSettlementType() == SettlementType.INSTANT_SETTLEMENT;

        if (isSettledByDefault) {
            isSettled = true;
            settlementDateTime = ZonedDateTime.now();
        }
    }

    @Override
    public String toString() {
        return "Transaction{" +
               "id=" + id +
               ", createdAt=" + createdAt +
               ", uuid='" + uuid + '\'' +
               ", reservationRef='" + reservationRef + '\'' +
               ", sequenceNumber=" + sequenceNumber +
               ", dateTime=" + dateTime +
               ", quantity=" + quantity +
               ", leavesQuantity=" + leavesQuantity +
               ", price=" + price +
               ", currency='" + currency + '\'' +
               ", baseCurrency='" + baseCurrency + '\'' +
               ", asset='" + asset + '\'' +
               ", portfolioId='" + portfolioId + '\'' +
               ", counterPortfolioId='" + counterPortfolioId + '\'' +
               ", accountId='" + accountId + '\'' +
               ", sourcePortfolioId='" + sourcePortfolioId + '\'' +
               ", targetPortfolioId='" + targetPortfolioId + '\'' +
               ", sourceAccountId='" + sourceAccountId + '\'' +
               ", targetAccountId='" + targetAccountId + '\'' +
               ", feePortfolioId='" + feePortfolioId + '\'' +
               ", feeAccountId='" + feeAccountId + '\'' +
               ", executionId='" + executionId + '\'' +
               ", venueExecutionId='" + venueExecutionId + '\'' +
               ", intOrderId='" + intOrderId + '\'' +
               ", extOrderId='" + extOrderId + '\'' +
               ", orderId='" + orderId + '\'' +
               ", parentOrderId='" + parentOrderId + '\'' +
               ", rootOrderId='" + rootOrderId + '\'' +
               ", underlyingExecutionId='" + underlyingExecutionId + '\'' +
               ", rootExecutionId='" + rootExecutionId + '\'' +
               ", clientRootOrderId='" + clientRootOrderId + '\'' +
               ", description='" + description + '\'' +
               ", isLive=" + isLive +
               ", isSettled=" + isSettled +
               ", settlementDateTime=" + settlementDateTime +
               ", settlementId='" + settlementId + '\'' +
               ", settlementType=" + settlementType +
               ", transactionType=" + transactionType +
               ", execType=" + execType +
               ", portfolioType=" + portfolioType +
               ", counterPortfolioType=" + counterPortfolioType +
               ", sourcePortfolioType=" + sourcePortfolioType +
               ", targetPortfolioType=" + targetPortfolioType +
               ", feePortfolioType=" + feePortfolioType +
               ", accountType=" + accountType +
               ", sourceAccountType=" + sourceAccountType +
               ", targetAccountType=" + targetAccountType +
               ", feeAccountType=" + feeAccountType +
               ", accountWalletType=" + accountWalletType +
               ", sourceAccountWalletType=" + sourceAccountWalletType +
               ", targetAccountWalletType=" + targetAccountWalletType +
               ", feeAccountWalletType=" + feeAccountWalletType +
               ", fees=" + fees +
               '}';
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof Transaction that)) return false;
        return sequenceNumber == that.sequenceNumber
               && isLive == that.isLive
               && isSettled == that.isSettled
               && Objects.equals(id, that.id)
               && Objects.equals(createdAt, that.createdAt)
               && Objects.equals(uuid, that.uuid)
               && Objects.equals(reservationRef, that.reservationRef)
               && Objects.equals(dateTime, that.dateTime)
               && Objects.equals(quantity, that.quantity)
               && Objects.equals(leavesQuantity, that.leavesQuantity)
               && Objects.equals(price, that.price)
               && Objects.equals(currency, that.currency)
               && Objects.equals(baseCurrency, that.baseCurrency)
               && Objects.equals(asset, that.asset)
               && Objects.equals(portfolioId, that.portfolioId)
               && Objects.equals(counterPortfolioId, that.counterPortfolioId)
               && Objects.equals(accountId, that.accountId)
               && Objects.equals(sourcePortfolioId, that.sourcePortfolioId)
               && Objects.equals(targetPortfolioId, that.targetPortfolioId)
               && Objects.equals(sourceAccountId, that.sourceAccountId)
               && Objects.equals(targetAccountId, that.targetAccountId)
               && Objects.equals(feePortfolioId, that.feePortfolioId)
               && Objects.equals(feeAccountId, that.feeAccountId)
               && Objects.equals(executionId, that.executionId)
               && Objects.equals(venueExecutionId, that.venueExecutionId)
               && Objects.equals(intOrderId, that.intOrderId)
               && Objects.equals(extOrderId, that.extOrderId)
               && Objects.equals(orderId, that.orderId)
               && Objects.equals(parentOrderId, that.parentOrderId)
               && Objects.equals(rootOrderId, that.rootOrderId)
               && Objects.equals(underlyingExecutionId, that.underlyingExecutionId)
               && Objects.equals(rootExecutionId, that.rootExecutionId)
               && Objects.equals(clientRootOrderId, that.clientRootOrderId)
               && Objects.equals(description, that.description)
               && Objects.equals(settlementDateTime, that.settlementDateTime)
               && Objects.equals(settlementId, that.settlementId)
               && settlementType == that.settlementType
               && transactionType == that.transactionType
               && execType == that.execType
               && portfolioType == that.portfolioType
               && counterPortfolioType == that.counterPortfolioType
               && sourcePortfolioType == that.sourcePortfolioType
               && targetPortfolioType == that.targetPortfolioType
               && feePortfolioType == that.feePortfolioType
               && accountType == that.accountType
               && sourceAccountType == that.sourceAccountType
               && targetAccountType == that.targetAccountType
               && feeAccountType == that.feeAccountType
               && accountWalletType == that.accountWalletType
               && sourceAccountWalletType == that.sourceAccountWalletType
               && targetAccountWalletType == that.targetAccountWalletType
               && feeAccountWalletType == that.feeAccountWalletType
               && Objects.equals(fees, that.fees);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            id,
            createdAt,
            uuid,
            reservationRef,
            sequenceNumber,
            dateTime,
            quantity,
            leavesQuantity,
            price,
            currency,
            baseCurrency,
            asset,
            portfolioId,
            counterPortfolioId,
            accountId,
            sourcePortfolioId,
            targetPortfolioId,
            sourceAccountId,
            targetAccountId,
            feePortfolioId,
            feeAccountId,
            executionId,
            venueExecutionId,
            intOrderId,
            extOrderId,
            orderId,
            parentOrderId,
            rootOrderId,
            underlyingExecutionId,
            rootExecutionId,
            clientRootOrderId,
            description,
            isLive,
            isSettled,
            settlementDateTime,
            settlementId,
            settlementType,
            transactionType,
            execType,
            portfolioType,
            counterPortfolioType,
            sourcePortfolioType,
            targetPortfolioType,
            feePortfolioType,
            accountType,
            sourceAccountType,
            targetAccountType,
            feeAccountType,
            accountWalletType,
            sourceAccountWalletType,
            targetAccountWalletType,
            feeAccountWalletType,
            fees);
    }

    public static TransactionBuilder builder() {
        return new TransactionBuilder();
    }

    /**
     * Creates a builder pre-filled with this transaction's data.
     * This method allows for creating a modified copy of the current transaction.
     *
     * @return a new TransactionBuilder initialized with this transaction's values
     */
    public TransactionBuilder toBuilder() {
        TransactionBuilder builder = builder()
            .uuid(this.uuid)
            .reservationRef(this.reservationRef)
            .sequenceNumber(this.sequenceNumber)
            .dateTime(this.dateTime)
            .executionId(this.executionId)
            .venueExecutionId(this.venueExecutionId)
            .quantity(this.quantity)
            .leavesQuantity(this.leavesQuantity)
            .price(this.price)
            .currency(this.currency)
            .baseCurrency(this.baseCurrency)
            .asset(this.asset)
            .portfolioId(this.portfolioId)
            .accountId(this.accountId)
            .counterPortfolioId(this.counterPortfolioId)
            .sourcePortfolioId(this.sourcePortfolioId)
            .targetPortfolioId(this.targetPortfolioId)
            .sourceAccountId(this.sourceAccountId)
            .targetAccountId(this.targetAccountId)
            .feePortfolioId(this.feePortfolioId)
            .feeAccountId(this.feeAccountId)
            .intOrderId(this.intOrderId)
            .extOrderId(this.extOrderId)
            .orderId(this.orderId)
            .parentOrderId(this.parentOrderId)
            .rootOrderId(this.rootOrderId)
            .underlyingExecutionId(this.underlyingExecutionId)
            .rootExecutionId(this.rootExecutionId)
            .clientRootOrderId(this.clientRootOrderId)
            .description(this.description)
            .isLive(this.isLive)
            .isSettled(this.isSettled)
            .settlementDateTime(this.settlementDateTime)
            .settlementId(this.settlementId)
            .settlementType(this.settlementType)
            .transactionType(this.transactionType)
            .portfolioType(this.portfolioType)
            .counterPortfolioType(this.counterPortfolioType)
            .sourcePortfolioType(this.sourcePortfolioType)
            .targetPortfolioType(this.targetPortfolioType)
            .feePortfolioType(this.feePortfolioType)
            .accountType(this.accountType)
            .sourceAccountType(this.sourceAccountType)
            .targetAccountType(this.targetAccountType)
            .feeAccountType(this.feeAccountType)
            .accountWalletType(this.accountWalletType)
            .sourceAccountWalletType(this.sourceAccountWalletType)
            .targetAccountWalletType(this.targetAccountWalletType)
            .feeAccountWalletType(this.feeAccountWalletType)
            .execType(this.execType);

        // Add all fees
        if (this.fees != null && !this.fees.isEmpty()) {
            builder.addFees(this.fees);
        }

        return builder;
    }

    public static class TransactionBuilder {
        private final Transaction transaction;

        private TransactionBuilder() {
            transaction = new Transaction();
        }

        public TransactionBuilder uuid(String uuid) {
            transaction.uuid = uuid;
            return this;
        }

        public TransactionBuilder reservationRef(String reservationRef) {
            transaction.reservationRef = reservationRef;
            return this;
        }

        public TransactionBuilder sequenceNumber(long sequenceNumber) {
            transaction.sequenceNumber = sequenceNumber;
            return this;
        }

        public TransactionBuilder dateTime(ZonedDateTime dateTime) {
            transaction.dateTime = dateTime;
            return this;
        }

        public TransactionBuilder executionId(String executionId) {
            transaction.executionId = executionId;
            return this;
        }

        public TransactionBuilder venueExecutionId(String venueExecutionId) {
            transaction.venueExecutionId = venueExecutionId;
            return this;
        }

        public TransactionBuilder quantity(BigDecimal quantity) {
            transaction.quantity = quantity;
            return this;
        }

        public TransactionBuilder leavesQuantity(BigDecimal leavesQuantity) {
            transaction.leavesQuantity = leavesQuantity;
            return this;
        }

        public TransactionBuilder price(BigDecimal price) {
            transaction.price = price;
            return this;
        }

        public TransactionBuilder currency(String currency) {
            transaction.currency = currency;
            return this;
        }

        public TransactionBuilder baseCurrency(String baseCurrency) {
            transaction.baseCurrency = baseCurrency;
            return this;
        }

        public TransactionBuilder asset(String asset) {
            transaction.asset = asset;
            return this;
        }

        public TransactionBuilder portfolioId(String portfolioId) {
            transaction.portfolioId = portfolioId;
            return this;
        }

        public TransactionBuilder accountId(String accountId) {
            transaction.accountId = accountId;
            return this;
        }

        public TransactionBuilder counterPortfolioId(String counterPortfolioId) {
            transaction.counterPortfolioId = counterPortfolioId;
            return this;
        }

        public TransactionBuilder sourcePortfolioId(String sourcePortfolioId) {
            transaction.sourcePortfolioId = sourcePortfolioId;
            return this;
        }

        public TransactionBuilder targetPortfolioId(String targetPortfolioId) {
            transaction.targetPortfolioId = targetPortfolioId;
            return this;
        }

        public TransactionBuilder sourceAccountId(String sourceAccountId) {
            transaction.sourceAccountId = sourceAccountId;
            return this;
        }

        public TransactionBuilder targetAccountId(String targetAccountId) {
            transaction.targetAccountId = targetAccountId;
            return this;
        }

        public TransactionBuilder feePortfolioId(String feePortfolioId) {
            transaction.feePortfolioId = feePortfolioId;
            return this;
        }

        public TransactionBuilder feeAccountId(String feeAccountId) {
            transaction.feeAccountId = feeAccountId;
            return this;
        }

        public TransactionBuilder intOrderId(String intOrderId) {
            transaction.intOrderId = intOrderId;
            return this;
        }

        public TransactionBuilder extOrderId(String extOrderId) {
            transaction.extOrderId = extOrderId;
            return this;
        }

        public TransactionBuilder orderId(String orderId) {
            transaction.orderId = orderId;
            return this;
        }

        public TransactionBuilder parentOrderId(String parentOrderId) {
            transaction.parentOrderId = parentOrderId;
            return this;
        }

        public TransactionBuilder rootOrderId(String rootOrderId) {
            transaction.rootOrderId = rootOrderId;
            return this;
        }

        public TransactionBuilder underlyingExecutionId(String underlyingExecutionId) {
            transaction.underlyingExecutionId = underlyingExecutionId;
            return this;
        }

        public TransactionBuilder rootExecutionId(String rootExecutionId) {
            transaction.rootExecutionId = rootExecutionId;
            return this;
        }

        public TransactionBuilder clientRootOrderId(String clientRootOrderId) {
            transaction.clientRootOrderId = clientRootOrderId;
            return this;
        }

        public TransactionBuilder description(String description) {
            transaction.description = description;
            return this;
        }

        public TransactionBuilder isLive(boolean isLive) {
            transaction.isLive = isLive;
            return this;
        }

        public TransactionBuilder isSettled(boolean isSettled) {
            transaction.isSettled = isSettled;
            return this;
        }

        public TransactionBuilder settlementDateTime(ZonedDateTime settlementDateTime) {
            transaction.settlementDateTime = settlementDateTime;
            return this;
        }

        public TransactionBuilder settlementId(String settlementId) {
            transaction.settlementId = settlementId;
            return this;
        }

        public TransactionBuilder settlementType(SettlementType settlementType) {
            transaction.settlementType = settlementType;
            return this;
        }

        public TransactionBuilder transactionType(TransactionType transactionType) {
            transaction.transactionType = transactionType;
            return this;
        }

        public TransactionBuilder execType(ExecType execType) {
            transaction.execType = execType;
            return this;
        }

        public TransactionBuilder portfolioType(RequestModel.PortfolioType portfolioType) {
            transaction.portfolioType = portfolioType;
            return this;
        }

        public TransactionBuilder counterPortfolioType(RequestModel.PortfolioType counterPortfolioType) {
            transaction.counterPortfolioType = counterPortfolioType;
            return this;
        }

        public TransactionBuilder sourcePortfolioType(RequestModel.PortfolioType sourcePortfolioType) {
            transaction.sourcePortfolioType = sourcePortfolioType;
            return this;
        }

        public TransactionBuilder targetPortfolioType(RequestModel.PortfolioType targetPortfolioType) {
            transaction.targetPortfolioType = targetPortfolioType;
            return this;
        }

        public TransactionBuilder feePortfolioType(RequestModel.PortfolioType feePortfolioType) {
            transaction.feePortfolioType = feePortfolioType;
            return this;
        }

        public TransactionBuilder accountType(RequestModel.AccountType accountType) {
            transaction.accountType = accountType;
            return this;
        }

        public TransactionBuilder sourceAccountType(RequestModel.AccountType sourceAccountType) {
            transaction.sourceAccountType = sourceAccountType;
            return this;
        }

        public TransactionBuilder targetAccountType(RequestModel.AccountType targetAccountType) {
            transaction.targetAccountType = targetAccountType;
            return this;
        }

        public TransactionBuilder feeAccountType(RequestModel.AccountType feeAccountType) {
            transaction.feeAccountType = feeAccountType;
            return this;
        }

        public TransactionBuilder accountWalletType(RequestModel.WalletType accountWalletType) {
            transaction.accountWalletType = accountWalletType;
            return this;
        }

        public TransactionBuilder sourceAccountWalletType(RequestModel.WalletType sourceAccountWalletType) {
            transaction.sourceAccountWalletType = sourceAccountWalletType;
            return this;
        }

        public TransactionBuilder targetAccountWalletType(RequestModel.WalletType targetAccountWalletType) {
            transaction.targetAccountWalletType = targetAccountWalletType;
            return this;
        }

        public TransactionBuilder feeAccountWalletType(RequestModel.WalletType feeAccountWalletType) {
            transaction.feeAccountWalletType = feeAccountWalletType;
            return this;
        }

        public TransactionBuilder addFee(TransactionFee fee) {
            transaction.fees.add(fee);
            return this;
        }

        public TransactionBuilder addFees(Collection<TransactionFee> fees) {
            transaction.fees.addAll(fees);
            return this;
        }

        public Transaction build() {
            transaction.settleIfPossible();
            return transaction;
        }
    }
}
