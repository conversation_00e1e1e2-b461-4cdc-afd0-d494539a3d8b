package io.wyden.booking.reporting.domain.reservation;

import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.query.QueryUtils;

import java.util.ArrayList;
import java.util.List;

import static io.wyden.booking.reporting.domain.transaction.TransactionPredicates.getPortfolioAndAccountPredicate;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class ReservationRepositoryCustomImpl implements ReservationRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<Reservation> findByProperties(RequestModel.AuthorizedReservationSearch search, Pageable pageable) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Reservation> query = cb.createQuery(Reservation.class);
        Root<Reservation> root = query.from(Reservation.class);

        List<Predicate> predicates = buildPredicates(cb, root, search);

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // Add sorting
        if (pageable.getSort().isSorted()) {
            query.orderBy(QueryUtils.toOrders(pageable.getSort(), root, cb));
        }

        TypedQuery<Reservation> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<Reservation> content = typedQuery.getResultList();
        long total = getTotalCount(search);

        return new PageImpl<>(content, pageable, total);
    }

    private long getTotalCount(RequestModel.AuthorizedReservationSearch search) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Reservation> root = countQuery.from(Reservation.class);

        List<Predicate> predicates = buildPredicates(cb, root, search);

        if (!predicates.isEmpty()) {
            countQuery.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        countQuery.select(cb.count(root));
        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private List<Predicate> buildPredicates(CriteriaBuilder cb, Root<Reservation> root, RequestModel.AuthorizedReservationSearch reservationSearch) {
        RequestModel.ReservationSearch search = reservationSearch.searchRequest();
        SimpleSearchRequest.AuthorizedSearchRequest authorizedSearch = reservationSearch.authorizedSearchRequest();

        List<Predicate> predicates = new ArrayList<>();

        // Reservation reference filter
        if (search.reservationRef() != null && !search.reservationRef().isBlank()) {
            predicates.add(cb.equal(root.get("reservationRef"), search.reservationRef()));
        }

        // Date range filters
        if (search.from() != null) {
            predicates.add(cb.greaterThanOrEqualTo(root.get("dateTime"), search.from()));
        }

        if (search.to() != null) {
            predicates.add(cb.lessThan(root.get("dateTime"), search.to()));
        }

        // Symbol filter (asset, currency, baseCurrency)
        if (search.symbol() != null && !search.symbol().isEmpty()) {
            predicates.add(cb.or(
                root.get("asset").in(search.symbol()),
                root.get("currency").in(search.symbol()),
                root.get("baseCurrency").in(search.symbol())
            ));
        }

        // Currency filter (asset, currency, baseCurrency)
        if (search.currency() != null && !search.currency().isEmpty()) {
            predicates.add(cb.or(
                root.get("asset").in(search.currency()),
                root.get("currency").in(search.currency()),
                root.get("baseCurrency").in(search.currency())
            ));
        }

        // Transaction type filter
        if (search.transactionType() != null && !search.transactionType().isEmpty()) {
            predicates.add(root.get("transactionType").in(search.transactionType()));
        }

        if (isNotBlank(search.after())) {
            long cursor = Long.parseLong(search.after());
            Predicate laterPredicate = cb.greaterThan(root.get("id"), cursor);
            Predicate earlierPredicate = cb.lessThan(root.get("id"), cursor);
            Predicate afterPredicate = search.sortingOrder() == RequestModel.SortingOrder.ASC ? laterPredicate : earlierPredicate;
            predicates.add(afterPredicate);
        }

        predicates.add(getPortfolioAndAccountPredicate(authorizedSearch, cb, root));

        return predicates;
    }
}
