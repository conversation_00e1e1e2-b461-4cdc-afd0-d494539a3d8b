package io.wyden.booking.reporting.domain.ledgerentry;

import io.wyden.booking.reporting.domain.PositionIdentifiers;
import io.wyden.booking.reporting.interfaces.rest.RequestModel;
import io.wyden.booking.reporting.interfaces.rest.SimpleSearchRequest;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.query.QueryUtils;

import java.util.ArrayList;
import java.util.List;

import static io.wyden.booking.reporting.domain.transaction.TransactionPredicates.isAccountAuthorized;
import static io.wyden.booking.reporting.domain.transaction.TransactionPredicates.isAccountAuthorizedAndSelected;
import static io.wyden.booking.reporting.domain.transaction.TransactionPredicates.isPortfolioAuthorized;
import static io.wyden.booking.reporting.domain.transaction.TransactionPredicates.isPortfolioAuthorizedAndSelected;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class LedgerEntryRepositoryCustomImpl implements LedgerEntryRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<LedgerEntry> findByProperties(RequestModel.AuthorizedLedgerEntrySearch search, Pageable pageable) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<LedgerEntry> query = cb.createQuery(LedgerEntry.class);
        Root<LedgerEntry> root = query.from(LedgerEntry.class);

        List<Predicate> predicates = buildPredicates(cb, root, search);

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // Add sorting
        if (pageable.getSort().isSorted()) {
            query.orderBy(QueryUtils.toOrders(pageable.getSort(), root, cb));
        }

        TypedQuery<LedgerEntry> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<LedgerEntry> content = typedQuery.getResultList();
        long total = getTotalCount(search);

        return new PageImpl<>(content, pageable, total);
    }

    private long getTotalCount(RequestModel.AuthorizedLedgerEntrySearch search) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<LedgerEntry> root = countQuery.from(LedgerEntry.class);

        List<Predicate> predicates = buildPredicates(cb, root, search);

        if (!predicates.isEmpty()) {
            countQuery.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        countQuery.select(cb.count(root));
        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private List<Predicate> buildPredicates(CriteriaBuilder cb, Root<LedgerEntry> root, RequestModel.AuthorizedLedgerEntrySearch ledgerEntrySearch) {
        RequestModel.LedgerEntrySearch search = ledgerEntrySearch.searchRequest();
        SimpleSearchRequest.AuthorizedSearchRequest authorizedSearch = ledgerEntrySearch.authorizedSearchRequest();

        List<Predicate> predicates = new ArrayList<>();

        // Symbol filter (asset, currency, baseCurrency)
        if (search.symbol() != null && !search.symbol().isEmpty()) {
            predicates.add(root.get("symbol").in(search.symbol()));
        }

        // Currency filter (asset, currency, baseCurrency)
        if (search.currency() != null && !search.currency().isEmpty()) {
            predicates.add(root.get("symbol").in(search.currency()));
        }

        // Ledger entry type filter
        if (search.ledgerEntryType() != null && !search.ledgerEntryType().isEmpty()) {
            predicates.add(root.get("ledgerEntryType").in(search.ledgerEntryType()));
        }

        if (isNotBlank(search.transactionId())) {
            predicates.add(cb.equal(root.get("transactionId"), search.transactionId()));
        }

        if (isNotBlank(search.orderId())) {
            predicates.add(cb.equal(root.get("reservationRef"), search.orderId()));
        }

        // Date range filters
        if (search.from() != null) {
            predicates.add(cb.greaterThanOrEqualTo(root.get("dateTime"), search.from()));
        }

        if (search.to() != null) {
            predicates.add(cb.lessThan(root.get("dateTime"), search.to()));
        }

        if (isNotBlank(search.after())) {
            long cursor = Long.parseLong(search.after());
            Predicate laterPredicate = cb.greaterThan(root.get("id"), cursor);
            Predicate earlierPredicate = cb.lessThan(root.get("id"), cursor);
            Predicate afterPredicate = search.sortingOrder() == RequestModel.SortingOrder.ASC ? laterPredicate : earlierPredicate;
            predicates.add(afterPredicate);
        }

        predicates.add(getPortfolioAndAccountPredicate(authorizedSearch, cb, root));

        return predicates;
    }

    public static Predicate getPortfolioAndAccountPredicate(SimpleSearchRequest.AuthorizedSearchRequest search, CriteriaBuilder cb, Root<? extends PositionIdentifiers> root) {
        Predicate isPortfolioAuthorized = isPortfolioAuthorized(search, cb, root.get("portfolioId"), root.get("portfolioType"));
        Predicate isPortfolioAuthorizedAndSelected = isPortfolioAuthorizedAndSelected(search, root.get("portfolioId"));

        Predicate isAccountAuthorized = isAccountAuthorized(search, cb, root.get("accountId"), root.get("accountType"), root.get("accountWalletType"));
        Predicate isAccountAuthorizedAndSelected = isAccountAuthorizedAndSelected(search, root.get("accountId"));

        if (search.isEmptyRequest()) {
            return cb.or(isPortfolioAuthorized, isAccountAuthorized);
        }

        if (search.isRequestPortfoliosOnly()) {
            return isPortfolioAuthorizedAndSelected;
        }

        if (search.isRequestAccountsOnly()) {
            return isAccountAuthorizedAndSelected;
        }

        return cb.or(isPortfolioAuthorizedAndSelected, isAccountAuthorizedAndSelected);
    }
}
