package io.wyden.booking.reporting.domain.transaction;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Optional;

@Repository
public interface TransactionRepository extends JpaRepository<Transaction, Long>, PagingAndSortingRepository<Transaction, Long>, TransactionRepositoryCustom {

    Collection<Transaction> findBySequenceNumber(long sequenceNumber);

    Optional<Transaction> findByUuid(String uuid);
}
