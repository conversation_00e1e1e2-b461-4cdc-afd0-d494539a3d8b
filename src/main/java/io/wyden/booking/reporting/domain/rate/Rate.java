package io.wyden.booking.reporting.domain.rate;

import io.wyden.booking.reporting.domain.position.Position;
import io.wyden.cloudutils.tools.BigDecimalUtils;
import io.wyden.cloudutils.tools.DateUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

import static org.apache.commons.lang3.StringUtils.isBlank;

public record Rate(String instrument, String quoteCurrency, BigDecimal value, ZonedDateTime timestamp) {

    public static Rate defaultRate(String baseCurrency, String quoteCurrency) {
        return new Rate(baseCurrency, quoteCurrency, BigDecimal.ONE, ZonedDateTime.now());
    }

    public static Rate from(io.wyden.published.rate.Rate rate) {
        BigDecimal rateValue = bd(rate.getValue());
        ZonedDateTime rateTimestamp = DateUtils.isoUtcTimeToZonedDateTime(rate.getTimestamp());

        return new Rate(rate.getBaseCurrency(),
            rate.getQuoteCurrency(),
            rateValue,
            rateTimestamp);
    }

    public static BigDecimal bd(String value) {
        if (isBlank(value)) {
            return BigDecimal.ZERO;
        }

        return BigDecimalUtils.bd(value);
    }

    public @NotNull String toString() {
        return String.format("%s/%s: %s", instrument, quoteCurrency, Position.toString(value));
    }
}
