package io.wyden.booking.reporting.domain.tracking;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository
public interface EventTrackingRepository extends JpaRepository<EventTracking, Long> {

    @Query("SELECT t.lastProcessedSequence FROM EventTracking t WHERE t.eventType = :eventType")
    Optional<Long> findLastProcessedSequence(@Param("eventType") String eventType);

    @Modifying
    @Transactional
    @Query("UPDATE EventTracking t SET t.lastProcessedSequence = :sequenceNumber WHERE t.eventType = :eventType")
    int updateLastProcessedSequence(@Param("eventType") String eventType, @Param("sequenceNumber") Long sequenceNumber);

    @Query("SELECT t FROM EventTracking t WHERE t.eventType = :eventType")
    Optional<EventTracking> findByEventType(@Param("eventType") String eventType);
}