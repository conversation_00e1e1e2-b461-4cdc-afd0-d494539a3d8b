package io.wyden.booking.pnl.domain.model.reference;

import io.wyden.booking.pnl.domain.model.instrument.Currency;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.ManyToOne;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.sql.Timestamp;
import java.util.Objects;

@Entity
@EntityListeners(AuditingEntityListener.class)
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "reference_type")
@DiscriminatorValue("null")
public abstract class GenericReference {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;

    @CreatedDate
    private Timestamp createdAt;

    protected String name;
    protected String referenceId;

    @ManyToOne
    protected Currency currency;

    protected GenericReference(String name, String referenceId, Currency currency) {
        this.name = name;
        this.referenceId = referenceId;
        this.currency = currency;
    }

    protected GenericReference() {
        // JPA
    }

    /**
     * Factory method to create a PortfolioReference
     * 
     * @param portfolioName the name of the portfolio
     * @param currency the currency of the portfolio
     * @return a new PortfolioReference instance
     */
    public static PortfolioReference createPortfolioReference(String portfolioName, String portfolioId, Currency currency) {
        return new PortfolioReference(portfolioName, portfolioId, currency);
    }

    /**
     * Factory method to create an AccountReference
     * 
     * @param accountName the name of the account
     * @param currency the currency of the account
     * @return a new AccountReference instance
     */
    public static AccountReference createAccountReference(String accountName, String accountId, Currency currency) {
        return new AccountReference(accountName, accountId, currency);
    }

    public Long getId() {
        return id;
    }

    public GenericReference setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public Currency getCurrency() {
        return currency;
    }

    public GenericReference setCurrency(Currency currency) {
        this.currency = currency;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        GenericReference that = (GenericReference) o;
        return Objects.equals(id, that.id)
            && Objects.equals(name, that.name)
            && Objects.equals(referenceId, that.referenceId)
            && Objects.equals(currency, that.currency);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, referenceId, currency);
    }
}
