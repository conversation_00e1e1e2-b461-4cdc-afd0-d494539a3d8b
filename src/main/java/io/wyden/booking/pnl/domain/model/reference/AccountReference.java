package io.wyden.booking.pnl.domain.model.reference;

import io.wyden.booking.pnl.domain.model.instrument.Currency;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;

@Entity
@DiscriminatorValue("ACCOUNT")
public class AccountReference extends GenericReference {

    protected AccountReference(String accountName, String referenceId, Currency currency) {
        super(accountName, referenceId, currency);
    }

    protected AccountReference() {
        // JPA
    }

    @Override
    public String toString() {
        return "AccountReference{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", currency=" + currency +
            '}';
    }
}
