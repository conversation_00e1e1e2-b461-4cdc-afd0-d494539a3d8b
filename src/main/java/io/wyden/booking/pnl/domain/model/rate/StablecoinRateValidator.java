package io.wyden.booking.pnl.domain.model.rate;

import org.springframework.stereotype.Service;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class StablecoinRateValidator implements RateValidator {

    private final StablecoinProvider stablecoinProvider;

    public StablecoinRateValidator(StablecoinProvider stablecoinProvider) {
        this.stablecoinProvider = stablecoinProvider;
    }

    @Override
    public void validateRate(Rate rate, String instrument, String quoteCurrency) {
        boolean valid = true;

        if (isNotBlank(instrument) && !instrument.equalsIgnoreCase(rate.instrument())) {
            String fiatInstrument = stablecoinProvider.toFiat(instrument);
            String fiatRate = stablecoinProvider.toFiat(rate.instrument());
            valid = fiatInstrument.equalsIgnoreCase(fiatRate);
        }

        if (isNotBlank(quoteCurrency) && !quoteCurrency.equalsIgnoreCase(rate.quoteCurrency())) {
            String fiatQuoteCurrency = stablecoinProvider.toFiat(quoteCurrency);
            String fiatRate = stablecoinProvider.toFiat(rate.quoteCurrency());
            valid = fiatQuoteCurrency.equalsIgnoreCase(fiatRate);
        }

        if (!valid) {
            throw new IllegalArgumentException("Cannot use rate: %s/%s. Expecting rate for: %s/%s".formatted(rate.instrument(), rate.quoteCurrency(), instrument, quoteCurrency));
        }
    }
}
