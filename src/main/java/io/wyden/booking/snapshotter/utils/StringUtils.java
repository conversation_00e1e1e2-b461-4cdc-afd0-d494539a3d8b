package io.wyden.booking.snapshotter.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;
import java.util.Locale;

public final class StringUtils {

    private static final DecimalFormat formatter;
    private static final DecimalFormatSymbols symbols;

    static {
        formatter = (DecimalFormat) NumberFormat.getInstance(Locale.US);
        formatter.setMaximumFractionDigits(8);
        symbols = formatter.getDecimalFormatSymbols();
        symbols.setGroupingSeparator('_');
        formatter.setDecimalFormatSymbols(symbols);
    }

    private StringUtils() {
    }

    public static String toJBehaveString(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return "";
        }

        return formatter.format(bigDecimal.stripTrailingZeros());
    }

    public static String toPrettyString(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return "-";
        }

        return bigDecimal.stripTrailingZeros().toPlainString();
    }
}
