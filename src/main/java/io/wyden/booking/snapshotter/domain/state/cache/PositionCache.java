package io.wyden.booking.snapshotter.domain.state.cache;

import io.wyden.booking.snapshotter.domain.ledgerentry.SimpleReference;
import io.wyden.booking.snapshotter.domain.position.Position;
import io.wyden.cloudutils.telemetry.Telemetry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
public class PositionCache {

    private static final Logger LOGGER = LoggerFactory.getLogger(PositionCache.class);

    private final Map<SimpleReference, CachedEntry<Position>> cachedPositions;
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final Duration ttl;

    public PositionCache(Telemetry telemetry,
                         @Value("${cache.position.eviction.ttl.duration}") Duration duration) {
        this.ttl = duration == null ? Duration.ZERO : duration;
        LOGGER.info("Effective TTL: {}", ttl);

        this.cachedPositions = new ConcurrentHashMap<>();
        telemetry.getMeterRegistry().gaugeMapSize(
            "wyden.booking-snapshotter.cache.position",
            List.of(),
            cachedPositions);

        if (ttl.compareTo(Duration.ZERO) > 0) {
            startEvictionTask();
        }
    }

    private void startEvictionTask() {
        scheduler.scheduleAtFixedRate(this::evictExpiredEntries, ttl.toSeconds(), ttl.toSeconds(), TimeUnit.SECONDS);
    }

    private void evictExpiredEntries() {
        long now = System.currentTimeMillis();
        cachedPositions.entrySet().removeIf(entry -> entry.getValue().isExpired(now, ttl));
    }

    public Optional<Position> findPositionBySymbolAndPortfolioId(String symbol, String portfolioId) {
        if (portfolioId == null || symbol == null) {
            return Optional.empty();
        }

        CachedEntry<Position> cachedPosition = cachedPositions.get(new SimpleReference(symbol, portfolioId, null));
        return Optional.ofNullable(cachedPosition)
            // Always return a copy - else state changes will be propagated directly to the cached values.
            // We want state propagation to happen only explicitly in store() method
            // Why - some command processors will apply their changes only conditionally, if the result state fulfills some conditions (e.g., buying power check)
            .map(CachedEntry::value);
    }

    public Optional<Position> findPositionBySymbolAndAccountId(String symbol, String accountId) {
        if (accountId == null || symbol == null) {
            return Optional.empty();
        }

        CachedEntry<Position> cachedPosition = cachedPositions.get(new SimpleReference(symbol, null, accountId));
        return Optional.ofNullable(cachedPosition)
            // Always return a copy - else state changes will be propagated directly to the cached values.
            // We want state propagation to happen only explicitly in store() method
            // Why - some command processors will apply their changes only conditionally, if the result state fulfills some conditions (e.g., buying power check)
            .map(CachedEntry::value);
    }

    public void store(Position position) {
        SimpleReference simpleReference = new SimpleReference(position.getInstrument(), position.getPortfolioId(), position.getAccountId());
        cachedPositions.put(simpleReference, CachedEntry.of(position));
    }

    /**
     * Batch lookup for portfolio-based positions. Returns cached positions and collection of cache misses.
     * @param lookups Set of (symbol, portfolioId) pairs to lookup
     * @return BatchLookupResult containing found positions and missed lookups
     */
    public BatchLookupResult<PortfolioLookup> findPositionsBySymbolAndPortfolioIds(Set<PortfolioLookup> lookups) {
        Map<SimpleReference, Position> foundPositions = HashMap.newHashMap(lookups.size());
        Collection<PortfolioLookup> cacheMisses = new ArrayList<>();
        
        for (PortfolioLookup lookup : lookups) {
            if (isValidLookup(lookup.symbol(), lookup.portfolioId())) {
                processPortfolioLookup(lookup, foundPositions, cacheMisses);
            }
        }
        
        return new BatchLookupResult<>(foundPositions, cacheMisses);
    }
    
    /**
     * Batch lookup for account-based positions. Returns cached positions and collection of cache misses.
     * @param lookups Set of (symbol, accountId) pairs to lookup
     * @return BatchLookupResult containing found positions and missed lookups
     */
    public BatchLookupResult<AccountLookup> findPositionsBySymbolAndAccountIds(Set<AccountLookup> lookups) {
        Map<SimpleReference, Position> foundPositions = HashMap.newHashMap(lookups.size());
        Collection<AccountLookup> cacheMisses = new ArrayList<>();
        
        for (AccountLookup lookup : lookups) {
            if (isValidLookup(lookup.symbol(), lookup.accountId())) {
                processAccountLookup(lookup, foundPositions, cacheMisses);
            }
        }
        
        return new BatchLookupResult<>(foundPositions, cacheMisses);
    }
    
    private boolean isValidLookup(String symbol, String id) {
        return symbol != null && id != null;
    }
    
    private void processPortfolioLookup(
            PortfolioLookup lookup, 
            Map<SimpleReference, Position> foundPositions, 
            Collection<PortfolioLookup> cacheMisses) {
        SimpleReference key = new SimpleReference(lookup.symbol(), lookup.portfolioId(), null);
        CachedEntry<Position> cachedPosition = cachedPositions.get(key);
        
        if (cachedPosition != null) {
            foundPositions.put(key, cachedPosition.value());
        } else {
            cacheMisses.add(lookup);
        }
    }
    
    private void processAccountLookup(
            AccountLookup lookup, 
            Map<SimpleReference, Position> foundPositions, 
            Collection<AccountLookup> cacheMisses) {
        SimpleReference key = new SimpleReference(lookup.symbol(), null, lookup.accountId());
        CachedEntry<Position> cachedPosition = cachedPositions.get(key);
        
        if (cachedPosition != null) {
            foundPositions.put(key, cachedPosition.value());
        } else {
            cacheMisses.add(lookup);
        }
    }
    
    // Records for batch lookup parameters
    public record PortfolioLookup(String symbol, String portfolioId) {}
    public record AccountLookup(String symbol, String accountId) {}
    
    /**
     * Result of batch cache lookup containing both cache hits and misses
     */
    public record BatchLookupResult<T>(Map<SimpleReference, Position> foundPositions, Collection<T> cacheMisses) {}
}
