package io.wyden.booking.snapshotter.domain.position;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Lightweight rollback mechanism for Position state changes.
 * Captures only the minimal state needed to restore Position to its original state
 * if buying power check fails, avoiding expensive full object copying.
 */
public class PositionRollback {
    
    private final BigDecimal originalQuantity;
    private final BigDecimal originalSettledQuantity;
    private final BigDecimal originalUnsettledQuantity;
    private final BigDecimal originalAvailableForWithdrawalQuantity;
    private final Map<String, BigDecimal> originalReservedQuantityMap;
    
    private PositionRollback(BigDecimal originalQuantity,
                            BigDecimal originalSettledQuantity,
                            BigDecimal originalUnsettledQuantity,
                            BigDecimal originalAvailableForWithdrawalQuantity,
                            Map<String, BigDecimal> originalReservedQuantityMap) {
        this.originalQuantity = originalQuantity;
        this.originalSettledQuantity = originalSettledQuantity;
        this.originalUnsettledQuantity = originalUnsettledQuantity;
        this.originalAvailableForWithdrawalQuantity = originalAvailableForWithdrawalQuantity;
        this.originalReservedQuantityMap = originalReservedQuantityMap;
    }
    
    /**
     * Captures the current state of a Position for potential rollback.
     * Only stores the fields that can be mutated by Position.applyLedgerEntry().
     * 
     * @param position the Position to capture state from
     * @return PositionRollback instance containing the original state
     */
    public static PositionRollback capture(Position position) {
        return new PositionRollback(
            position.getQuantity(),
            position.getSettledQuantity(),
            position.getUnsettledQuantity(),
            position.getAvailableForWithdrawalQuantity(),
            new HashMap<>(position.getReservedQuantityMap()) // Shallow copy of Map structure
        );
    }
    
    /**
     * Restores the Position to its original state captured by this rollback.
     * This operation is atomic - either all fields are restored or none are.
     * 
     * @param position the Position to restore
     */
    public void restore(Position position) {
        position.setQuantity(originalQuantity);
        position.setSettledQuantity(originalSettledQuantity);
        position.setUnsettledQuantity(originalUnsettledQuantity);
        position.setAvailableForWithdrawalQuantity(originalAvailableForWithdrawalQuantity);
        
        // Clear and restore reserved quantity map
        position.getReservedQuantityMap().clear();
        position.getReservedQuantityMap().putAll(originalReservedQuantityMap);
    }
}