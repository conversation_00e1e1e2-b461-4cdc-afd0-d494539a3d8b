package io.wyden.booking.snapshotter.domain.bookingcompleted;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import org.springframework.data.annotation.CreatedDate;

import java.io.Serializable;
import java.time.Instant;
import java.util.Arrays;
import java.util.Objects;

@Entity
@Table(name = "booking_completed")
public class BookingCompletedEvent implements Serializable {

    public static final String MESSAGE_TYPE = "BOOKING_COMPLETED";

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @CreatedDate
    private Instant createdAt;

    @Column(name = "message_type", nullable = false)
    private String messageType;

    @Column(name = "sequence_number", nullable = false)
    private Long sequenceNumber;

    @Column(name = "payload", nullable = false, columnDefinition = "bytea")
    private byte[] payload;

    protected BookingCompletedEvent() {
        // JPA
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * Creates a builder instance pre-populated with this object's current data
     *
     * @return builder with current object's data
     */
    public Builder toBuilder() {
        return new Builder()
            .id(this.id)
            .createdAt(this.createdAt)
            .messageType(this.messageType)
            .sequenceNumber(this.sequenceNumber)
            .payload(this.payload);
    }

    // Getters
    public Long getId() {
        return id;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public String getMessageType() {
        return messageType;
    }

    public Long getSequenceNumber() {
        return sequenceNumber;
    }

    public byte[] getPayload() {
        return payload;
    }

    // Setters
    public void setId(Long id) {
        this.id = id;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public void setSequenceNumber(Long sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    public void setPayload(byte[] payload) {
        this.payload = payload;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BookingCompletedEvent that = (BookingCompletedEvent) o;
        return Objects.equals(id, that.id) &&
            Objects.equals(createdAt, that.createdAt) &&
            Objects.equals(messageType, that.messageType) &&
            Objects.equals(sequenceNumber, that.sequenceNumber) &&
            Objects.deepEquals(payload, that.payload);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, createdAt, messageType, sequenceNumber, Arrays.hashCode(payload));
    }

    @Override
    public String toString() {
        return "BookingCompletedEvent{" +
            "id=" + id +
            ", createdAt=" + createdAt +
            ", messageType='" + messageType + '\'' +
            ", sequenceNumber=" + sequenceNumber +
            ", payloadSize=" + (payload != null ? payload.length : 0) +
            '}';
    }

    /**
     * Builder for creating BookingCompletedEvent instances with a fluent API
     */
    public static class Builder {

        private final BookingCompletedEvent event;

        private Builder() {
            this.event = new BookingCompletedEvent();
            this.event.messageType = MESSAGE_TYPE;
        }

        public Builder id(Long id) {
            this.event.id = id;
            return this;
        }

        public Builder createdAt(Instant createdAt) {
            this.event.createdAt = createdAt;
            return this;
        }

        public Builder messageType(String messageType) {
            if (messageType != null && !MESSAGE_TYPE.equals(messageType)) {
                throw new IllegalArgumentException("Message type must be " + MESSAGE_TYPE);
            }
            this.event.messageType = messageType;
            return this;
        }

        public Builder sequenceNumber(Long sequenceNumber) {
            this.event.sequenceNumber = sequenceNumber;
            return this;
        }

        public Builder payload(byte[] payload) {
            this.event.payload = payload;
            return this;
        }

        /**
         * Builds a new BookingCompletedEvent instance with the configured values
         *
         * @return new BookingCompletedEvent instance
         */
        public BookingCompletedEvent build() {
            if (event.sequenceNumber == null) {
                throw new IllegalStateException("Sequence number is required");
            }
            if (event.payload == null) {
                throw new IllegalStateException("Payload is required");
            }
            return event;
        }
    }

    /**
     * Factory method for creating new BookingCompletedEvent instances
     */
    public static BookingCompletedEvent create(Long sequenceNumber, byte[] payload) {
        return builder()
            .sequenceNumber(sequenceNumber)
            .payload(payload)
            .build();
    }
}