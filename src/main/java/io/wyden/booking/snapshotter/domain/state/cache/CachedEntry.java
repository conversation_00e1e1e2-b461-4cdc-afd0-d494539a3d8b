package io.wyden.booking.snapshotter.domain.state.cache;

import java.time.Duration;
import java.util.Objects;

final class CachedEntry<T> {
    private final T value;
    private long timestamp;

    CachedEntry(T value, long timestamp) {
        this.value = value;
        this.timestamp = timestamp;
    }

    static <T> CachedEntry<T> of(T value) {
        return new CachedEntry<>(value, System.currentTimeMillis());
    }

    boolean isExpired(long now, Duration ttl) {
        return now - timestamp > ttl.toMillis();
    }

    public void refresh() {
        timestamp = System.currentTimeMillis();
    }

    public T value() {
        return value;
    }

    public long timestamp() {
        return timestamp;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj == null || obj.getClass() != this.getClass()) return false;
        var that = (CachedEntry) obj;
        return Objects.equals(this.value, that.value) &&
            this.timestamp == that.timestamp;
    }

    @Override
    public int hashCode() {
        return Objects.hash(value, timestamp);
    }

    @Override
    public String toString() {
        return "CachedEntry[" +
            "value=" + value + ", " +
            "timestamp=" + timestamp + ']';
    }

}
