package io.wyden.booking.snapshotter.domain.common;

import org.hibernate.exception.ConstraintViolationException;
import org.slf4j.Logger;
import org.springframework.dao.ConcurrencyFailureException;
import org.springframework.dao.DataAccessException;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import static org.slf4j.LoggerFactory.getLogger;

public interface JpaRepository<T extends AuditedEntity, ID> {

    Logger LOGGER = getLogger(JpaRepository.class);

    static <T extends AuditedEntity> T trySave(Function<T, Optional<T>> searchFn, Function<T, T> saveFn, T entity) {
        // 1. try search entity
        LOGGER.trace("Try to find entity before saving: ({})", entity);
        T managedEntity = searchFn.apply(entity)
            .orElseGet(() -> {
                try {
                    // 2a. if not found, save it and return saved entity
                    // 2b. it is possible that other thread (or other jvm) saves the entity in the meantime, causing data integrity exception
                    LOGGER.trace("Entity not found, try to save new: ({})", entity);
                    return saveFn.apply(entity);
                } catch (DataAccessException | ConstraintViolationException ex) {
                    // Unique Constraint Violation might be thrown here.
                    // This is to handle problematic insert-if-not-exists problem
                    // in concurrent (and multi jvm) environment.
                    // It looks ugly, but the only viable alternative would be to use
                    // vendor-specific SQL syntax like MERGE INTO...
                    // 3. rethrow the exception and have it retried by rabbit (other thread will finish first)
                    LOGGER.trace("Failed to save new. Try searching again: ({})", entity);
                    return searchFn.apply(entity)
                        .orElseThrow(() -> {
                            String detailedReason = ("Failed find and create new entity: " + entity.toString());
                            return new ConcurrencyFailureException(detailedReason, ex);
                        });
                }
            });

        LOGGER.trace("Entity found: {}", managedEntity);
        return managedEntity;
    }

    @Transactional
    List<T> findAll();

    Optional<T> findById(ID id);

    <S extends T> S save(S entity);

    long count();
}
