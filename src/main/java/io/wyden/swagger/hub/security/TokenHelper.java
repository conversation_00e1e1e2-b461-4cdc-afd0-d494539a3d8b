package io.wyden.swagger.hub.security;

import com.nimbusds.jose.shaded.gson.internal.LinkedTreeMap;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;

import java.util.List;

public class TokenHelper {

    private static String ROLE_NAME = "WYDEN_ADMIN";

    public static boolean hasWydenAdminRole(OAuth2AuthenticationToken token) {
        LinkedTreeMap realmAccess = token.getPrincipal().getAttribute("realm_access");
        List<String> list = (List<String>) realmAccess.get("roles");
        return list.contains(ROLE_NAME);
    }
}
