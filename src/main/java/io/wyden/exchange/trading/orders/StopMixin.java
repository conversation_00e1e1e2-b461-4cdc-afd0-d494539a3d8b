package io.wyden.exchange.trading.orders;

import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;

import java.math.BigDecimal;

import static io.wyden.exchange.trading.ExecutionService.getMarketPrice;
import static io.wyden.exchange.trading.ExecutionService.hasAskPrice;
import static io.wyden.exchange.trading.ExecutionService.hasBidPrice;
import static org.apache.commons.lang3.compare.ComparableUtils.is;

public interface StopMixin {

    BigDecimal getStopPrice();

    default boolean stopTrigger(Order order, MarketDataEventDTO event) {
        return order.getSide() == Order.OrderSide.BUY && hasAskPrice(event) && is(getStopPrice()).lessThanOrEqualTo(getMarketPrice(order, event))
            || order.getSide() == Order.OrderSide.SELL && hasBidPrice(event) && is(getStopPrice()).greaterThanOrEqualTo(getMarketPrice(order, event));
    }
}
