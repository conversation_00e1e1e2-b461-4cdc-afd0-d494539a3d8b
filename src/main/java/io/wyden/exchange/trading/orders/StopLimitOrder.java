package io.wyden.exchange.trading.orders;

import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;

import quickfix.FieldNotFound;
import quickfix.SessionID;
import quickfix.fix44.NewOrderSingle;

import java.math.BigDecimal;

public class StopLimitOrder extends LimitOrder implements StopMixin {

    private final BigDecimal stopPrice;
    private boolean executable = false;

    public StopLimitOrder(NewOrderSingle order, SessionID sessionId) throws FieldNotFound {
        super(order, sessionId);
        this.stopPrice = BigDecimal.valueOf(order.getStopPx().getValue());
    }

    @Override
    public boolean isAffected(MarketDataEventDTO event) {
        executable = executable || stopTrigger(this, event);
        return executable && super.isAffected(event);
    }

    @Override
    public BigDecimal getStopPrice() {
        return stopPrice;
    }

    @Override
    public String toString() {
        return "StopLimitOrder{" +
            "stopPrice=" + stopPrice +
            "} " + super.toString();
    }
}
