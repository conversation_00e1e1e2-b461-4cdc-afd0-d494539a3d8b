package io.wyden.exchange;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;

@SpringBootApplication
@EnableScheduling
public class ExchangeSimulatorApplication {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExchangeSimulatorApplication.class);

    public static void main(String[] args) {
        printMemorySettings();
        SpringApplication.run(ExchangeSimulatorApplication.class, args);
    }

    private static void printMemorySettings() {
        int mb = 1024 * 1024;
        MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
        long xmx = memoryMXBean.getHeapMemoryUsage().getMax() / mb;
        long xms = memoryMXBean.getHeapMemoryUsage().getInit() / mb;
        LOGGER.info("Initial Memory : {}mb", xms);
        LOGGER.info("Max Memory: {} mb", xmx);
    }
}
