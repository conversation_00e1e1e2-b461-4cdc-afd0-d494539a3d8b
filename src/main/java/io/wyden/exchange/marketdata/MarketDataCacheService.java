package io.wyden.exchange.marketdata;

import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Service
public class MarketDataCacheService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataCacheService.class);

    private final Map<BySessionIdAndTickerIdKey, MarketDataEventDTO> lastMarketDataEventByTickerId = new ConcurrentHashMap<>();

    private static final BiFunction<MarketDataEventDTO, MarketDataEventDTO, MarketDataEventDTO> MERGE_FUNCTION =
        (lastEvent, newEvent) -> newEvent.getDateTime().compareTo(lastEvent.getDateTime()) >= 0 ? newEvent : lastEvent;


    public void onEvent(String sessionId, final MarketDataEventDTO marketDataEvent) {
        if (marketDataEvent.getDateTime() != null) {
            LOGGER.debug("Merging market data event for session {} and ticker {} with new event {}", sessionId, marketDataEvent.getTickerId(), marketDataEvent);
            lastMarketDataEventByTickerId.merge(new BySessionIdAndTickerIdKey(sessionId, marketDataEvent.getTickerId()), marketDataEvent, MERGE_FUNCTION);
        }
    }

    public MarketDataEventDTO getCurrentMarketDataEvent(String tickerId) {
        return lastMarketDataEventByTickerId.entrySet().stream()
            .filter(e -> e.getKey().tickerId.equals(tickerId))
            .map(Map.Entry::getValue)
            .findFirst()
            .orElse(null);
    }

    public MarketDataEventDTO getCurrentMarketDataEvent(String sessionId, String tickerId) {
        return lastMarketDataEventByTickerId.get(new BySessionIdAndTickerIdKey(sessionId, tickerId));
    }

    public Set<BySessionIdAndTickerIdKey> getTickerIds() {
        return lastMarketDataEventByTickerId.keySet();
    }

    public void reset() {
        lastMarketDataEventByTickerId.clear();
    }

    public synchronized void reset(String sessionId) {
        Collection<BySessionIdAndTickerIdKey> toDelete = lastMarketDataEventByTickerId.keySet().stream()
            .filter(e -> e.sessionId.equals(sessionId))
            .collect(Collectors.toSet());

        toDelete.forEach(lastMarketDataEventByTickerId::remove);
    }

    public synchronized void reset(String sessionId, String tickerId) {
        Collection<BySessionIdAndTickerIdKey> toDelete = lastMarketDataEventByTickerId.keySet().stream()
            .filter(e -> e.sessionId.equals(sessionId))
            .filter(e -> e.tickerId.equals(tickerId))
            .collect(Collectors.toSet());

        toDelete.forEach(lastMarketDataEventByTickerId::remove);
    }

    public record BySessionIdAndTickerIdKey(String sessionId, String tickerId) {
    }

}
