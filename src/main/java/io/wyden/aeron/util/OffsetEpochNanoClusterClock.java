package io.wyden.aeron.util;

import org.agrona.concurrent.OffsetEpochNanoClock;

import java.util.concurrent.TimeUnit;

import io.aeron.cluster.service.ClusterClock;

public class OffsetEpochNanoClusterClock implements ClusterClock {

    private final OffsetEpochNanoClock clock = new OffsetEpochNanoClock();

    @Override
    public TimeUnit timeUnit() {
        return TimeUnit.NANOSECONDS;
    }

    @Override
    public long time() {
        return clock.nanoTime();
    }

    @Override
    public long timeMillis() {
        return System.currentTimeMillis();
    }

    @Override
    public long timeMicros() {
        return clock.nanoTime() / 1000;
    }

    @Override
    public long timeNanos() {
        return clock.nanoTime();
    }

}
