package io.wyden.aeron.util;

import java.util.Arrays;
import java.util.NoSuchElementException;

public class LongSortedSet {
    private final LongIterator ascendingIterator = new AscendingLongIterator();
    private final DescendingLongIterator descendingIterator = new DescendingLongIterator();
    private long[] elements;
    private int size;

    public LongSortedSet(int initialCapacity) {
        elements = new long[initialCapacity];
        size = 0;
    }

    public boolean add(long value) {
        int index = Arrays.binarySearch(elements, 0, size, value);
        if (index >= 0) {
            return false;
        }
        index = -index - 1;
        if (size == elements.length) {
            elements = Arrays.copyOf(elements, size * 2);
        }
        System.arraycopy(elements, index, elements, index + 1, size - index);
        elements[index] = value;
        size++;
        return true;
    }

    public boolean remove(long value) {
        int index = Arrays.binarySearch(elements, 0, size, value);
        if (index < 0) {
            return false;
        }
        System.arraycopy(elements, index + 1, elements, index, size - index - 1);
        size--;
        return true;
    }

    public boolean contains(long value) {
        return Arrays.binarySearch(elements, 0, size, value) >= 0;
    }

    public void clear() {
        size = 0;
    }

    public int size() {
        return size;
    }

    public LongIterator ascendingIterator() {
        ascendingIterator.reset();
        return ascendingIterator;
    }

    public LongIterator descendingIterator() {
        descendingIterator.reset();
        return descendingIterator;
    }

    public interface LongIterator {
        boolean hasNext();

        void reset();

        long next();
    }

    public class AscendingLongIterator implements LongIterator {
        private int currentIndex = 0;

        @Override
        public boolean hasNext() {
            return currentIndex < size;
        }

        @Override
        public void reset() {
            currentIndex = 0;
        }

        @Override
        public long next() {
            if (!hasNext()) {
                throw new NoSuchElementException();
            }
            return elements[currentIndex++];
        }
    }

    public class DescendingLongIterator implements LongIterator {
        private int currentIndex = size - 1;

        @Override
        public boolean hasNext() {
            return currentIndex >= 0;
        }

        @Override
        public void reset() {
            currentIndex = size - 1;
        }

        @Override
        public long next() {
            if (!hasNext()) {
                throw new NoSuchElementException();
            }
            return elements[currentIndex--];
        }
    }

}