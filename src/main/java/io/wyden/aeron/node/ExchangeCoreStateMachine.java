package io.wyden.aeron.node;

import exchange.core2.collections.objpool.ObjectsPool;
import exchange.core2.core.ExchangeApi;
import exchange.core2.core.ExchangeCore;
import exchange.core2.core.SimpleEventsProcessor;
import exchange.core2.core.common.CoreSymbolSpecification;
import exchange.core2.core.common.IOrder;
import exchange.core2.core.common.OrderAction;
import exchange.core2.core.common.OrderType;
import exchange.core2.core.common.SymbolType;
import exchange.core2.core.common.api.binary.BatchAddSymbolsCommand;
import exchange.core2.core.common.cmd.OrderCommandType;
import exchange.core2.core.common.config.ExchangeConfiguration;
import exchange.core2.core.common.config.PerformanceConfiguration;
import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.aeron.node.timer.CancelOrderTask;
import io.wyden.aeron.node.timer.OrderBookTask;
import io.wyden.aeron.report.AllOrdersReportQuery;
import io.wyden.aeron.report.AllOrdersReportResult;
import io.wyden.aeron.report.SingleUserOrdersReportQuery;
import io.wyden.aeron.report.SingleUserOrdersReportResult;
import io.wyden.aeron.report.SymbolSpecificationReportQuery;
import io.wyden.aeron.report.SymbolSpecificationReportResult;
import io.wyden.sbe.AddSymbolSpecificationRequestDecoder;
import io.wyden.sbe.AddSymbolSpecificationRequestDecoder.SymbolsDecoder;
import io.wyden.sbe.AllOrdersReportQueryDecoder;
import io.wyden.sbe.ApiCancelOrderDecoder;
import io.wyden.sbe.ApiMoveOrderDecoder;
import io.wyden.sbe.ApiOrderBookRequestDecoder;
import io.wyden.sbe.ApiPlaceBulkOrderDecoder;
import io.wyden.sbe.ApiPlaceOrderDecoder;
import io.wyden.sbe.ApiReduceOrderDecoder;
import io.wyden.sbe.CancelTimerDecoder;
import io.wyden.sbe.ScheduleOrderBookTimerDecoder;
import io.wyden.sbe.SingleUserOrdersReportQueryDecoder;
import io.wyden.sbe.SnapshotDataType;
import io.wyden.sbe.SnapshotHeaderDecoder;
import io.wyden.sbe.SnapshotHeaderEncoder;
import io.wyden.sbe.SymbolSpecificationRequestDecoder;
import io.wyden.sbe.TimerTaskType;
import net.openhft.chronicle.bytes.Bytes;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static io.wyden.aeron.TelemetryConfiguration.COUNT_OUTBOUND_TRADING_EVENT;

@Component
public class ExchangeCoreStateMachine extends AeronSnapshotable {

    private final ExchangeCoreEventsHandler eventsHandler;
    private final SimpleEventsProcessor eventsProcessor;
    private final MeterRegistry meterRegistry;
    private final boolean metricsEnabled;
    private final ExchangeCoreSerializationProcessor serializationProcessor;
    private final ObjectsPool objectsPool;
    private final AeronTimerManager timerManager;

    private ExchangeApi api;
    private BulkOrderProcessor bulkOrderProcessor;

    private long timestamp = 0;

    public ExchangeCoreStateMachine(
            @Qualifier("realtimeEventHandler") ExchangeCoreEventsHandler eventsHandler,
            @Qualifier("realtimeTimerManager") AeronTimerManager timerManager,
            ExchangeCoreSerializationProcessor serializationProcessor,
            ObjectsPool objectsPool,
            MeterRegistry meterRegistry,
            @Value("${metrics.enabled}") boolean metricsEnabled,
            @Value("${matching.engine.trades.batch.size}") int tradesBatchSize) {

        super(SnapshotDataType.MATCHING_ENGINE);

        this.eventsHandler = eventsHandler;
        this.timerManager = timerManager;
        this.serializationProcessor = serializationProcessor;
        this.objectsPool = objectsPool;
        this.meterRegistry = meterRegistry;
        this.metricsEnabled = metricsEnabled;
        this.eventsProcessor = new SimpleEventsProcessor(eventsHandler, tradesBatchSize);
        this.bulkOrderProcessor = new BulkOrderProcessor(eventsHandler, objectsPool);
    }

    public void start() {

        PerformanceConfiguration perfConf = PerformanceConfiguration.latencyPerformanceBuilder()
                .build();

        ExchangeConfiguration exchangeConf = ExchangeConfiguration.defaultBuilder()
                .performanceCfg(perfConf)
                .build();

        this.api = ExchangeCore.builder()
                .exchangeConfiguration(exchangeConf)
                .eventsProcessor(eventsProcessor)
                .serializationProcessor(serializationProcessor)
                .objectsPool(objectsPool)
                .build().getApi();
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public void onTimerEvent(long correlationId, long timestamp) {
        timerManager.onTimerEvent(this, correlationId, timestamp);
    }

    @Override
    public void loadSnapshotHeaderFields(SnapshotHeaderDecoder snapshot) {
        bulkOrderProcessor.setOrderId(snapshot.orderId());
        eventsProcessor.loadSnapshot(snapshot.tradeId(), snapshot.matchId());
    }

    @Override
    public void setSnapshotData(Bytes<ByteBuffer> data) {
        serializationProcessor.setData(data);
    }

    @Override
    public void persistSnapshotData() {
        api.persistState(timestamp, 0, false, 1);
    }

    @Override
    public void takeSnapshotHeader(final SnapshotHeaderEncoder snapshot) {
        snapshot.orderId(bulkOrderProcessor.getOrderId());
        snapshot.tradeId(eventsProcessor.getTradeId());
        snapshot.matchId(eventsProcessor.getMatchId());
    }

    @Override
    public Bytes<ByteBuffer> getSnapshotData() {
        return serializationProcessor.getData();
    }

    public void addSymbolSpecifications(final AddSymbolSpecificationRequestDecoder decoder) {

        List<CoreSymbolSpecification> symbolSpecifications = new ArrayList<>();
        for (SymbolsDecoder symbol : decoder.symbols()) {
            symbolSpecifications.add(CoreSymbolSpecification.builder()
                    .symbolId(symbol.symbolId())
                    .type(SymbolType.of(symbol.symbolType().value()))
                    .baseCurrency(symbol.baseCurrency())
                    .quoteCurrency(symbol.quoteCurrency())
                    .preventSelfMatch(true)
                    .build());
        }

        api.submitBinaryData(new BatchAddSymbolsCommand(symbolSpecifications));
        eventsHandler.addSymbolSpecificationResult(decoder.correlation());
    }

    public void symbolSpecifications(final SymbolSpecificationRequestDecoder decoder) {

        Optional<SymbolSpecificationReportResult> report = api.submitReportQuery(new SymbolSpecificationReportQuery());
        if (report.isPresent()) {
            eventsHandler.symbolSpecificationResult(report.get(), decoder.correlation());
        }
    }

    public void placeBulkOrder(final ApiPlaceBulkOrderDecoder decoder) {
        bulkOrderProcessor.placeBulkOrder(decoder, timestamp, api);
        updateMetrics(OrderCommandType.PLACE_ORDER, getOrderType(decoder.orderType()).name(), decoder.symbol(), true);
    }

    public void placeOrder(final ApiPlaceOrderDecoder decoder) {

        api.placeOrder(
            decoder.orderId(),
            getOrderAction(decoder.action()),
            getOrderType(decoder.orderType()),
            decoder.symbol(),
            decoder.uid(),
            decoder.price(),
            decoder.size(),
            decoder.amount(),
            0,
            decoder.orderUuid().mostSigBits(),
            decoder.orderUuid().leastSigBits(),
            timestamp,
            decoder.correlation(),
            false,
            decoder.sbeSchemaVersion());

        scheduleGTDTimer(decoder);
        updateMetrics(OrderCommandType.PLACE_ORDER, getOrderType(decoder.orderType()).name(), decoder.symbol(), false);
    }

    public void moveOrder(final ApiMoveOrderDecoder decoder) {

        api.moveOrder(
            decoder.orderId(),
            decoder.symbol(),
            decoder.uid(),
            decoder.newPrice(),
            0,
            timestamp,
            decoder.correlation(),
            false,
            decoder.sbeSchemaVersion());

        updateMetrics(OrderCommandType.MOVE_ORDER, "", decoder.symbol(), false);
    }

    public void reduceOrder(ApiReduceOrderDecoder decoder) {

        api.reduceOrder(
            decoder.orderId(),
            decoder.symbol(),
            decoder.uid(),
            decoder.reduceSize(),
            0,
            timestamp,
            decoder.correlation(),
            false,
            decoder.sbeSchemaVersion());

        updateMetrics(OrderCommandType.REDUCE_ORDER, "", decoder.symbol(), false);
    }

    public void cancelOrder(final ApiCancelOrderDecoder decoder) {

        cancelGTDTimer(decoder.orderId());

        api.cancelOrder(
            decoder.orderId(),
            decoder.symbol(),
            decoder.uid(),
            0,
            timestamp,
            decoder.correlation(),
            false,
            decoder.sbeSchemaVersion());

        updateMetrics(OrderCommandType.CANCEL_ORDER, "", decoder.symbol(), false);
    }

    public void cancelOrderOnExpiry(int symbol, long orderId, long timestamp, long correlation) {

        IOrder order = api.getOrderBooks().get(symbol).getOrderById(orderId);
        if (order != null) {

            cancelGTDTimer(orderId);

            api.cancelOrder(
                orderId,
                symbol,
                order.getUid(),
                FlagEncoder.setFlags(0, FlagEncoder.Flag.ORDER_EXPIRED),
                timestamp,
                correlation,
                false,
                1);

            updateMetrics(OrderCommandType.CANCEL_ORDER, "", symbol, false);
        }
    }

    public void requestOrderBook(final ApiOrderBookRequestDecoder decoder) {

        api.requestOrderBook(
            decoder.symbol(),
            decoder.depth(),
            timestamp,
            decoder.correlation(),
            decoder.sbeSchemaVersion());
    }


    public void requestOrderBook(final int symbol, final long depth, final long timestamp, final long correlation) {

        api.requestOrderBook(symbol, depth, timestamp, correlation, 1);
    }

    public void requestAllOrderBooks(long timestamp, long correlation) {

        api.getOrderBooks().forEach(orderBook -> api.requestOrderBook(
            orderBook.getSymbolSpec().symbolId,
            -1,
            timestamp,
            correlation,
            1));
    }

    public void requestSingleUserOrdersReport(SingleUserOrdersReportQueryDecoder query) {
        Optional<SingleUserOrdersReportResult> report = api.submitReportQuery(new SingleUserOrdersReportQuery(query.uid()));
        if (report.isPresent()) {
            eventsHandler.singleUserOrdersReport(report.get(), query.correlation());
        }
    }

    public void requestAllOrdersReport(AllOrdersReportQueryDecoder query) {
        Optional<AllOrdersReportResult> report = api.submitReportQuery(new AllOrdersReportQuery());
        if (report.isPresent()) {
            eventsHandler.allOrdersReport(report.get(), query.correlation());
        }
    }

    public void scheduleOrderBookTimer(ScheduleOrderBookTimerDecoder decoder) {
        timerManager.cancelTimer(TimerTaskType.ORDER_BOOK_FULL_REFRESH, 0);
        timerManager.scheduleTimer(((OrderBookTask) timerManager.getTimerTask(TimerTaskType.get(decoder.taskTypeRaw()))).init(decoder.cronExpression()));
    }

    public void cancelTimer(CancelTimerDecoder decoder) {
        timerManager.cancelTimer(TimerTaskType.get(decoder.taskTypeRaw()), decoder.id());
    }

    private void scheduleGTDTimer(final ApiPlaceOrderDecoder decoder) {
        if (decoder.orderType() == io.wyden.sbe.OrderType.GTD) {
            timerManager.scheduleTimer(((CancelOrderTask) timerManager.getTimerTask(TimerTaskType.CANCEL_ORDER)).init(decoder.symbol(), decoder.orderId(), decoder.expiry()));
        }
    }

    private void cancelGTDTimer(final long orderId) {
        timerManager.cancelTimer(TimerTaskType.CANCEL_ORDER, orderId);
    }

    private OrderType getOrderType(io.wyden.sbe.OrderType orderType) {
        if (orderType == io.wyden.sbe.OrderType.GTD) {
            return OrderType.GTC;
        } else {
            return OrderType.of((byte) orderType.value());
        }
    }

    private OrderAction getOrderAction(io.wyden.sbe.OrderAction orderAction) {
        return OrderAction.of((byte) orderAction.value());
    }

    private void updateMetrics(OrderCommandType command, String orderType, int symbol, boolean isBulk) {
        if (metricsEnabled) {
            try {
                this.meterRegistry.counter(COUNT_OUTBOUND_TRADING_EVENT,
                                "command", command.name(),
                                "symbol", String.valueOf(symbol),
                                "orderType", orderType,
                                "isBulk", String.valueOf(isBulk))
                        .increment();
            } catch (Exception ex) {
                // no op
            }
        }
    }
}
