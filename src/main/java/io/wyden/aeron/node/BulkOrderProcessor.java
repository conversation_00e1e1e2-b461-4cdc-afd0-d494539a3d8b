package io.wyden.aeron.node;

import io.wyden.aeron.client.enumeration.BulkOrderPhase;
import io.wyden.aeron.util.LongSortedSet;
import io.wyden.aeron.util.LongSortedSet.LongIterator;
import io.wyden.sbe.ApiPlaceBulkOrderDecoder;
import io.wyden.sbe.ApiPlaceBulkOrderDecoder.AsksDecoder;
import io.wyden.sbe.ApiPlaceBulkOrderDecoder.BidsDecoder;
import io.wyden.sbe.BulkOrderType;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

import exchange.core2.collections.art.LongAdaptiveRadixTreeMap;
import exchange.core2.collections.objpool.ObjectsPool;
import exchange.core2.core.ExchangeApi;
import exchange.core2.core.common.Order;
import exchange.core2.core.common.OrderAction;
import exchange.core2.core.common.OrderType;

@SuppressWarnings("java:S1450")
public class BulkOrderProcessor {

    private static final int NODE_ID = 1023;

    private final ExchangeCoreEventsHandler eventsHandler;
    private final ObjectsPool objectsPool;

    private final OrderIdGenerator idGenerator = new OrderIdGenerator(NODE_ID);

    private List<Order> newOrders = new ArrayList<>();

    private LongSortedSet bidPrices;
    private LongSortedSet askPrices;
    private LongAdaptiveRadixTreeMap<Order> currentBids;
    private LongAdaptiveRadixTreeMap<Order> currentAsks;
    private LongAdaptiveRadixTreeMap<Order> newBids;
    private LongAdaptiveRadixTreeMap<Order> newAsks;

    private long newCumSize = 0;
    private long targetSize = 0;
    private long targetCumSize = 0;

    private long price;
    private Order currentOrder;
    private Order newOrder;
    private long currentSize;
    private long newSize;

    private int version;

    public BulkOrderProcessor(ExchangeCoreEventsHandler eventsHandler, ObjectsPool objectsPool) {

        this.eventsHandler = eventsHandler;
        this.objectsPool = objectsPool;

        this.bidPrices = new LongSortedSet(100);
        this.askPrices = new LongSortedSet(100);
        this.currentBids = new LongAdaptiveRadixTreeMap<>(objectsPool);
        this.currentAsks = new LongAdaptiveRadixTreeMap<>(objectsPool);
        this.newBids = new LongAdaptiveRadixTreeMap<>(objectsPool);
        this.newAsks = new LongAdaptiveRadixTreeMap<>(objectsPool);
    }

    public void placeBulkOrder(final ApiPlaceBulkOrderDecoder decoder, long timestamp, final ExchangeApi api) {
        version = decoder.sbeSchemaVersion();
        List<Order> currentOrders = api.getOrderBooks().get(decoder.symbol()).findUserOrders(decoder.uid());

        newOrders.clear();

        for (BidsDecoder bid : decoder.bids()) {
            Order order = objectsPool.get(ObjectsPool.ORDER, (Supplier<Order>) Order::new);
            order.price = bid.price();
            order.size = bid.size();
            order.action = OrderAction.BID;

            newOrders.add(order);
        }

        for (AsksDecoder ask : decoder.asks()) {
            Order order = objectsPool.get(ObjectsPool.ORDER, (Supplier<Order>) Order::new);
            order.price = ask.price();
            order.size = ask.size();
            order.action = OrderAction.ASK;

            newOrders.add(order);
        }

        placeBulkOrder(api, decoder.symbol(), decoder.uid(), decoder.maxQtyDeviation(), decoder.bulkOrderType(), OrderType.of((byte) (decoder.orderTypeRaw())), timestamp, decoder.correlation(), currentOrders, newOrders);

        for (Order currentOrd : currentOrders) {
            objectsPool.put(ObjectsPool.ORDER, currentOrd);
        }

        for (Order newOrd : newOrders) {
            objectsPool.put(ObjectsPool.ORDER, newOrd);
        }
    }

    @SuppressWarnings("java:S107")
    private void placeBulkOrder(ExchangeApi api, int symbol, long uid, float maxQtyDeviation, BulkOrderType bulkOrderType, OrderType orderType, long timestamp, long correlation, List<Order> currentOrders, List<Order> newOrders) {

        bidPrices.clear();
        askPrices.clear();
        currentBids.clear();
        currentAsks.clear();
        newBids.clear();
        newAsks.clear();

        for (Order order : currentOrders) {
            if (order.getAction() == OrderAction.BID) {
                currentBids.put(order.getPrice(), order);
                bidPrices.add(order.getPrice());
            } else {
                currentAsks.put(order.getPrice(), order);
                askPrices.add(order.getPrice());
            }
        }

        for (Order order : newOrders) {
            if (order.getAction() == OrderAction.BID) {
                newBids.put(order.getPrice(), order);
                bidPrices.add(order.getPrice());
            } else {
                newAsks.put(order.getPrice(), order);
                askPrices.add(order.getPrice());
            }
        }

        processDelta(api, symbol, OrderAction.ASK, orderType, uid, maxQtyDeviation, timestamp, correlation, askPrices.ascendingIterator(), currentAsks, newAsks, BulkOrderPhase.CANCEL_REDUCE);
        processDelta(api, symbol, OrderAction.BID, orderType, uid, maxQtyDeviation, timestamp, correlation, bidPrices.descendingIterator(), currentBids, newBids, BulkOrderPhase.CANCEL_REDUCE);

        processDelta(api, symbol, OrderAction.ASK, orderType, uid, maxQtyDeviation, timestamp, correlation, askPrices.ascendingIterator(), currentAsks, newAsks, BulkOrderPhase.PLACE_NEW);
        processDelta(api, symbol, OrderAction.BID, orderType, uid, maxQtyDeviation, timestamp, correlation, bidPrices.descendingIterator(), currentBids, newBids, BulkOrderPhase.PLACE_NEW);

        eventsHandler.placeBulkOrderResult(symbol, uid, maxQtyDeviation, bulkOrderType, orderType, timestamp, correlation, newBids.getValues(), newAsks.getValues());
    }

    public void setOrderId(long orderId) {
        idGenerator.setId(orderId);
    }

    public long getOrderId() {
        return idGenerator.getId();
    }

    @SuppressWarnings({"java:S107", "java:S1301", "java:S3776"})
    private void processDelta(
            ExchangeApi api,
            int symbol,
            OrderAction action,
            OrderType orderType,
            long uid,
            float maxQtyDeviation,
            long timestamp,
            long correlation,
            LongIterator priceIterator,
            LongAdaptiveRadixTreeMap<Order> currentOrderMap,
            LongAdaptiveRadixTreeMap<Order> newOrderMap,
            BulkOrderPhase phase) {

        newCumSize = 0;
        targetSize = 0;
        targetCumSize = 0;

        while (priceIterator.hasNext()) {

            price = priceIterator.next();
            currentOrder = currentOrderMap.get(price);
            newOrder = newOrderMap.get(price);
            currentSize = currentOrder != null ? currentOrder.getSize() : 0;
            newSize = newOrder != null ? newOrder.getSize() : 0;
            newCumSize += newSize;

            if (Math.abs(targetCumSize + currentSize - newCumSize) > newCumSize * maxQtyDeviation) {
                targetSize = Math.max(newCumSize - targetCumSize, 0);
            } else {
                targetSize = currentSize;
            }
            targetCumSize += targetSize;

            switch (phase) {
                case CANCEL_REDUCE:
                    if (currentSize == 0 && targetSize != 0) {
                        // do nothing
                    } else if (targetSize == 0 && currentSize != 0) {
                        api.cancelOrder(currentOrder.getOrderId(), symbol, uid, 0, timestamp, correlation, true, version);
                    } else if (targetSize < currentSize) {
                        api.reduceOrder(currentOrder.getOrderId(), symbol, uid, currentOrder.getSize() - currentOrder.getFilled(), 0, timestamp, correlation, true, version);
                    } else if (targetSize > currentSize) {
                        api.cancelOrder(currentOrder.getOrderId(), symbol, uid, 0, timestamp, correlation, true, version);
                    }
                    break;
                case PLACE_NEW:
                    if (currentSize == 0 && targetSize != 0) {
                        api.placeOrder(idGenerator.nextOrderId(), action, orderType, symbol, uid, price, targetSize, 0, 0, 0, 0, timestamp, correlation, true, version);
                    } else if (targetSize == 0 && currentSize != 0) {
                        // do nothing
                    } else if (targetSize < currentSize) {
                        // do nothing
                    } else if (targetSize > currentSize) {
                        api.placeOrder(idGenerator.nextOrderId(), action, orderType, symbol, uid, price, targetSize, 0, 0, 0, 0, timestamp, correlation, true, version);
                    }
                    break;
            }
        }
    }
}
