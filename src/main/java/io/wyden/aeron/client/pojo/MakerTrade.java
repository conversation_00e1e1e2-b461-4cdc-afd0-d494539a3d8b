package io.wyden.aeron.client.pojo;

public class MakerTrade {
    private final long makerOrderId;
    private final long makerUid;
    private final boolean makerOrderCompleted;
    private final long price;
    private final long volume;

    public MakerTrade(long makerOrderId, long makerUid, boolean makerOrderCompleted, long price, long volume) {
        this.makerOrderId = makerOrderId;
        this.makerUid = makerUid;
        this.makerOrderCompleted = makerOrderCompleted;
        this.price = price;
        this.volume = volume;
    }

    public long getMakerOrderId() {
        return makerOrderId;
    }

    public long getMakerUid() {
        return makerUid;
    }

    public boolean isMakerOrderCompleted() {
        return makerOrderCompleted;
    }

    public long getPrice() {
        return price;
    }

    public long getVolume() {
        return volume;
    }
}
