package io.wyden.test.scenariorunner.integration;

import reactor.core.publisher.SignalType;

import java.time.Duration;

public class WebClientConstants {

    public static final int DEFAULT_FRAME_BUFFER_SIZE = 65536 * 10_000;

    public static final Duration DEFAULT_BLOCK_TIMEOUT = Duration.ofSeconds(30);

    public static SignalType[] DEFAULT_LOGGED_SIGNAL_TYPES =
        new SignalType[] {SignalType.ON_NEXT, SignalType.ON_ERROR};
    //    SignalType.values();
}
