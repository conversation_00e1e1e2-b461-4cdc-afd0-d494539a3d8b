package io.wyden.test.scenariorunner.integration.restmgmtclient;

import io.qameta.allure.Step;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.rest.pagination.PaginationModel.CursorConnection;
import io.wyden.rest.management.domain.TransactionModel.ClientCashTrade;
import io.wyden.rest.management.domain.TransactionModel.StreetCashTrade;
import io.wyden.rest.management.domain.TransactionModel.Transaction;
import io.wyden.rest.management.domain.TransactionModel.TransactionRequest;
import io.wyden.rest.management.domain.TransactionModel.TransactionSearch;
import io.wyden.test.scenariorunner.integration.service.accessgateway.KeySecret;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;

//TODO transactions should be retrieved in generic manner e.g. get all transactions returns all transactions, not depending on type
public class TransactionService extends RestManagementBaseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionService.class);

    private static final String TRANSACTIONS_ENDPOINT = "/api/v1/transactions";

    public TransactionService(WebClient webClient, KeySecret keySecret, String clientId) {
        super(webClient, keySecret, clientId);
    }

    @Step("Create transaction {0}")
    public <T extends Transaction> T createTransactionWithResult(T transactionRequest, Class<T> clazz) {
        LOGGER.info("RestManagement Actor ({}): Requesting to create a transaction : " + transactionRequest, clientId);
        HttpEntity<T> entity = new HttpEntity<>(transactionRequest);
        return request(HttpMethod.POST, TRANSACTIONS_ENDPOINT, entity, clazz).getBody();
    }

    @Step("Create transaction {0}")
    public <T extends TransactionRequest, S extends Transaction> S createTransactionWithResult(T transactionRequest, Class<S> transactionResponse) {
        LOGGER.info("RestManagement Actor ({}): Requesting to create a transaction : " + transactionRequest, clientId);
        HttpEntity<T> entity = new HttpEntity<>(transactionRequest);
        return request(HttpMethod.POST, TRANSACTIONS_ENDPOINT, entity, transactionResponse).getBody();
    }

    @Step("Get transactions by search {0}")
    public PaginationModel.CursorConnection<ClientCashTrade> getClientCashTransactions(TransactionSearch search) {
        LOGGER.info("RestManagement Actor ({}): Requesting transaction by search: " + search, clientId);
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromPath(TRANSACTIONS_ENDPOINT);
        addQueryParamsFromObject(uriBuilder, search);
        return request(
            HttpMethod.GET,
            uriBuilder.toUriString(),
            new HttpEntity<>(""),
            new ParameterizedTypeReference<CursorConnection<ClientCashTrade>>() {
            })
            .getBody();
    }

    @Step("Get transactions by search {0}")
    public PaginationModel.CursorConnection<StreetCashTrade> getStreetCashTransactions(TransactionSearch search) {
        LOGGER.info("RestManagement Actor ({}): Requesting transaction by search: " + search, clientId);
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromPath(TRANSACTIONS_ENDPOINT);
        addQueryParamsFromObject(uriBuilder, search);
        return request(
            HttpMethod.GET,
            uriBuilder.toUriString(),
            new HttpEntity<>(""),
            new ParameterizedTypeReference<PaginationModel.CursorConnection<StreetCashTrade>>() {
            }).getBody();
    }

    @Step("Get transaction by ID {0}")
    public <T extends Transaction> T getTransactionById(String id, Class<T> clazz) {
        LOGGER.info("RestManagement Actor ({}): Requesting transaction by ID: " + id, clientId);
        return request(HttpMethod.GET, TRANSACTIONS_ENDPOINT + "/{id}", null, clazz, id).getBody();
    }

    @Step("Search transactions by {0}")
    public CursorConnection<io.wyden.test.scenariorunner.model.restmgmt.Transaction> search(TransactionSearch search) {
        LOGGER.info("RestManagement Actor ({}): Requesting transaction by search: " + search, clientId);
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromPath(TRANSACTIONS_ENDPOINT);
        addQueryParamsFromObject(uriBuilder, search);
        return request(
            HttpMethod.GET,
            uriBuilder.toUriString(),
            new HttpEntity<>(""),
            new ParameterizedTypeReference<PaginationModel.CursorConnection<io.wyden.test.scenariorunner.model.restmgmt.Transaction>>() {
            }).getBody();
    }

    @Step("Get transaction by ID {0}")
    public io.wyden.test.scenariorunner.model.restmgmt.Transaction byId(String id) {
        LOGGER.info("RestManagement Actor ({}): Requesting transaction by ID: " + id, clientId);
        return request(HttpMethod.GET, TRANSACTIONS_ENDPOINT + "/{id}", null, io.wyden.test.scenariorunner.model.restmgmt.Transaction.class, id).getBody();
    }

}
