package io.wyden.test.scenariorunner.integration.restmgmtclient;

import io.qameta.allure.Step;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.rest.management.domain.ReservationModel;
import io.wyden.rest.management.domain.ReservationModel.Reservation;
import io.wyden.rest.management.domain.ReservationModel.ReservationRequest;
import io.wyden.test.scenariorunner.integration.service.accessgateway.KeySecret;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;

public class ReservationService extends RestManagementBaseService {

    public static final String RESERVATIONS_ENDPOINT = "/api/v1/reservations";
    private static final Logger LOGGER = LoggerFactory.getLogger(ReservationService.class);

    public ReservationService(WebClient webClient, KeySecret keySecret, String clientId) {
        super(webClient, keySecret, clientId);
    }

    @Step("Create reservation {0}")
    public <T extends ReservationRequest> void createReservation(T reservationRequest, Class<T> clazz) {
        LOGGER.info("RestManagement Actor ({}): Requesting to create a reservation : " + reservationRequest, clientId);
        HttpEntity<T> entity = new HttpEntity<>(reservationRequest);
        request(HttpMethod.POST, RESERVATIONS_ENDPOINT, entity, clazz);
    }

    @Step("Create reservation {0}")
    public <T extends ReservationRequest, S extends Reservation> S createReservationWithResult(T reservationRequest, Class<S> transactionResponse) {
        LOGGER.info("RestManagement Actor ({}): Requesting to create a reservation : " + reservationRequest, clientId);
        HttpEntity<T> entity = new HttpEntity<>(reservationRequest);
        return request(HttpMethod.POST, RESERVATIONS_ENDPOINT, entity, transactionResponse).getBody();
    }

    @Step("Get reservation by reservation ref {0}")
    public <T extends Reservation> T getReservationByRef(String reservationRef, Class<T> clazz) {
        LOGGER.info("RestManagement Actor ({}): Requesting reservation by reference: " + reservationRef, clientId);
        return request(HttpMethod.GET, RESERVATIONS_ENDPOINT + "/{reservationRef}", null, clazz, reservationRef).getBody();
    }

    @Step("Release reservation for {0}")
    public void releaseReservation(String reservationRef) {
        LOGGER.info("RestManagement Actor ({}): Requesting to release a reservation : " + reservationRef, clientId);
        request(HttpMethod.POST, RESERVATIONS_ENDPOINT + "/" + reservationRef + "/release", null, String.class);
    }

    @Step("Search reservations by {0}")
    public PaginationModel.CursorConnection<io.wyden.test.scenariorunner.model.restmgmt.Reservation> search(ReservationModel.ReservationSearch search) {
        LOGGER.info("RestManagement Actor ({}): Requesting reservations by search: " + search, clientId);
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromPath(RESERVATIONS_ENDPOINT);
        addQueryParamsFromObject(uriBuilder, search);
        return request(
            HttpMethod.GET,
            uriBuilder.toUriString(),
            new HttpEntity<>(""),
            new ParameterizedTypeReference<PaginationModel.CursorConnection<io.wyden.test.scenariorunner.model.restmgmt.Reservation>>() {
            }).getBody();
    }

}
