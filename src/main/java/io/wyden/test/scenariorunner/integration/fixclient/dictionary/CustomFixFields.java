package io.wyden.test.scenariorunner.integration.fixclient.dictionary;

public enum CustomFixFields {

    WYDEN_PORTFOLIO(10000),
    WYDEN_ACCOUNT(10004),
    SOR(10001),
    NO_SOR_ACCOUNT(10002),
    SOR_ACCOUNT(10003),
    BAR_SIZE(10009),
    WYDEN_ROOT_EXECUTION_ID(10015),
    WYDEN_UNDERLYING_ACCOUNT(10008),
    WYDEN_ORDER_CATEGORY(10012),
    MATCH_ID(10007),
    AVAILABLE_BALANCE_AMOUNT(10005),
    AVAILABLE_BALANCE_CURRENCY(10006),
    CALCULATED_CCY_LAST_QTY(1056),
    CALCULATED_CCY_REDUCED_QTY(10010)
    ;

    private final int fieldId;

    public int fieldId() {
        return fieldId;
    }

    CustomFixFields(int fieldId) {
        this.fieldId = fieldId;
    }

}
