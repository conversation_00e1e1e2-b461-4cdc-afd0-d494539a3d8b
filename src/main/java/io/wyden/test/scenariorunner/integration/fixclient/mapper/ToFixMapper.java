package io.wyden.test.scenariorunner.integration.fixclient.mapper;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.published.marketdata.MarketDataRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import quickfix.BooleanField;
import quickfix.Group;
import quickfix.StringField;
import quickfix.field.ClOrdID;
import quickfix.field.Currency;
import quickfix.field.ExpireTime;
import quickfix.field.MDEntryType;
import quickfix.field.MDReqID;
import quickfix.field.MDUpdateType;
import quickfix.field.MarketDepth;
import quickfix.field.OrdType;
import quickfix.field.OrderID;
import quickfix.field.OrderQty;
import quickfix.field.OrigClOrdID;
import quickfix.field.Price;
import quickfix.field.SecurityID;
import quickfix.field.SecurityListRequestType;
import quickfix.field.SecurityReqID;
import quickfix.field.SecurityType;
import quickfix.field.StopPx;
import quickfix.field.SubscriptionRequestType;
import quickfix.field.Symbol;
import quickfix.field.TimeInForce;
import quickfix.field.TransactTime;
import quickfix.fix44.MarketDataRequest.NoMDEntryTypes;
import quickfix.fix44.MarketDataRequest.NoRelatedSym;
import quickfix.fix44.OrderCancelReplaceRequest;
import quickfix.fix44.OrderCancelRequest;
import quickfix.fix44.OrderStatusRequest;
import quickfix.fix44.SecurityListRequest;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.UUID;

import static io.wyden.test.scenariorunner.integration.fixclient.dictionary.CustomFixFields.AVAILABLE_BALANCE_AMOUNT;
import static io.wyden.test.scenariorunner.integration.fixclient.dictionary.CustomFixFields.AVAILABLE_BALANCE_CURRENCY;
import static io.wyden.test.scenariorunner.integration.fixclient.dictionary.CustomFixFields.NO_SOR_ACCOUNT;
import static io.wyden.test.scenariorunner.integration.fixclient.dictionary.CustomFixFields.SOR;
import static io.wyden.test.scenariorunner.integration.fixclient.dictionary.CustomFixFields.SOR_ACCOUNT;
import static io.wyden.test.scenariorunner.integration.fixclient.dictionary.CustomFixFields.WYDEN_ACCOUNT;
import static io.wyden.test.scenariorunner.integration.fixclient.dictionary.CustomFixFields.WYDEN_PORTFOLIO;

public class ToFixMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(ToFixMapper.class);

    public static String createSecurityListRequest() {
        SecurityListRequest securityListRequest = new SecurityListRequest(
            new SecurityReqID(UUID.randomUUID().toString()),
            new SecurityListRequestType(SecurityListRequestType.ALL_SECURITIES)
        );
        return securityListRequest.toString();
    }

    public static String createCancel(ClientRequest cancel, ClientSide side) {
        String origClOrderId = cancel.getOrigClOrderId();
        String clOrderId = cancel.getClOrderId();

        OrderCancelRequest orderCancelRequest = new OrderCancelRequest(
            new OrigClOrdID(origClOrderId),
            new ClOrdID(clOrderId),
            new quickfix.field.Side(createSide(side)),
            new TransactTime(LocalDateTime.now()));

        // TODO - currently, FIX API Server is ignoring those since it only needs orderId
        //  But since FIX protocol requires it, we should be validating these fields against the ORder to be cancelled
        //  and reject Cancel request if these are not the same as in ORder in flight
//        orderCancelRequest.set(new Symbol("instrumentId"));
//        orderCancelRequest.set(new Account("portfolio"));

        return orderCancelRequest.toString();
    }

    public static String createOrderStatusRequest(ClientRequest oemsOrderStatusRequest, ClientSide side) {
        String clOrderId = oemsOrderStatusRequest.getClOrderId();
        String orderId = oemsOrderStatusRequest.getOrderId();
        OrderStatusRequest orderStatusRequest = new OrderStatusRequest();

        if (StringUtils.isNotBlank(clOrderId)) {
            orderStatusRequest.set(new ClOrdID(clOrderId));
        }
        if (StringUtils.isNotBlank(orderId)) {
            orderStatusRequest.set(new OrderID(orderId));
        }
        orderStatusRequest.set(new quickfix.field.Side(createSide(side)));
        orderStatusRequest.set(new SecurityID("BTCUSD@FOREX@Bitmex"));

        return orderStatusRequest.toString();
    }

    // NOTE: Modify together with createFixNewOrderSingle()
    public static String createCancelReplace(ClientRequest cancelReplace, ClientSide side) {
        String origClOrderId = cancelReplace.getOrigClOrderId();

        OrderCancelReplaceRequest orderCancelReplaceRequest = new OrderCancelReplaceRequest(
            new OrigClOrdID(origClOrderId),
            new ClOrdID(cancelReplace.getClOrderId()),
            new quickfix.field.Side(createSide(side)),
            new TransactTime(LocalDateTime.now()),
            new OrdType(toFixOrdType(cancelReplace.getOrderType()))
        );

//        orderCancelReplaceRequest.set(new SecurityExchange(cancelReplace.getTarget()));
        orderCancelReplaceRequest.setString(WYDEN_ACCOUNT.fieldId(), cancelReplace.getTarget());
        orderCancelReplaceRequest.set(new SecurityID(cancelReplace.getInstrumentId()));
        orderCancelReplaceRequest.set(new OrderQty(Double.parseDouble(cancelReplace.getQuantity())));
        orderCancelReplaceRequest.setField(new StringField(WYDEN_PORTFOLIO.fieldId(), cancelReplace.getPortfolioId())); //TODO replace it with custom class when AC-948 is done
//        newOrderSingle.set(new CashOrderQty()); // TODO this can be a feature later, we will need "smart" Order service for this (with Marekt data and Ref data access)

        // optional/conditional fields:

        String price = cancelReplace.getPrice();
        if (!price.isBlank()) {
            orderCancelReplaceRequest.set(new Price(Double.parseDouble(price)));
        }

        String stopPrice = cancelReplace.getStopPrice();
        if (!stopPrice.isBlank()) {
            orderCancelReplaceRequest.set(new StopPx(Double.parseDouble(stopPrice)));
        }

        TimeInForce tif = toFixTif(cancelReplace.getTif());
        orderCancelReplaceRequest.set(tif);

        String expireTime = cancelReplace.getExpireTime();
        if (!expireTime.isBlank()) {
            orderCancelReplaceRequest.set(new ExpireTime(DateUtils.fromFixUtcTime(expireTime).toLocalDateTime()));
        }

        return orderCancelReplaceRequest.toString();
    }

    // NOTE: Modify together with createCancelReplace()
    public static String createFixNewOrderSingle(ClientRequest wydenNewOrderSingle) {
        quickfix.fix44.NewOrderSingle newOrderSingle = new quickfix.fix44.NewOrderSingle(
            new ClOrdID(wydenNewOrderSingle.getClOrderId()),
            new quickfix.field.Side(createSide(wydenNewOrderSingle.getSide())),
            new TransactTime(LocalDateTime.now()), // todo document the meaning of this
            new OrdType(toFixOrdType(wydenNewOrderSingle.getOrderType()))
        );

        if (isSOR(wydenNewOrderSingle)) {
            newOrderSingle.setField(new BooleanField(SOR.fieldId(), true));
            Group sorAccounts = new Group(NO_SOR_ACCOUNT.fieldId(), SOR_ACCOUNT.fieldId());
            wydenNewOrderSingle.getVenueAccountsList().forEach(venueAccount -> {
                sorAccounts.setString(SOR_ACCOUNT.fieldId(), venueAccount);
                newOrderSingle.addGroup(sorAccounts);
            });
            newOrderSingle.set(new Symbol(wydenNewOrderSingle.getSymbol()));
            newOrderSingle.set(new SecurityType(wydenNewOrderSingle.getInstrumentType().name()));
        } else {
            newOrderSingle.set(new SecurityID(wydenNewOrderSingle.getInstrumentId()));
            if (wydenNewOrderSingle.getVenueAccountsCount() == 1) {
                newOrderSingle.setString(WYDEN_ACCOUNT.fieldId(), wydenNewOrderSingle.getVenueAccounts(0));
            }
        }

        newOrderSingle.setField(new StringField(WYDEN_PORTFOLIO.fieldId(), wydenNewOrderSingle.getPortfolioId())); //TODO replace it with custom class when AC-948 is done
        newOrderSingle.set(new OrderQty(Double.parseDouble(wydenNewOrderSingle.getQuantity())));
//        newOrderSingle.set(new CashOrderQty()); // TODO this can be a feature later, we will need "smart" Order service for this (with Marekt data and Ref data access)

        // optional/conditional fields:

        String price = wydenNewOrderSingle.getPrice();
        if (!price.isBlank()) {
            newOrderSingle.set(new Price(Double.parseDouble(price)));
        }

        String stopPrice = wydenNewOrderSingle.getStopPrice();
        if (!stopPrice.isBlank()) {
            newOrderSingle.set(new StopPx(Double.parseDouble(stopPrice)));
        }

        TimeInForce tif = toFixTif(wydenNewOrderSingle.getTif());
        newOrderSingle.set(tif);

        String expireTime = wydenNewOrderSingle.getExpireTime();
        if (!expireTime.isBlank()) {
            newOrderSingle.set(new ExpireTime(DateUtils.fromFixUtcTime(expireTime).toLocalDateTime()));
        }

        String currency = wydenNewOrderSingle.getCurrency();
        if (!currency.isBlank()) {
            newOrderSingle.set(new Currency(currency));
        }

        String availableBalanceAmount = wydenNewOrderSingle.getAvailableBalanceAmount();
        if (!availableBalanceAmount.isBlank()) {
            newOrderSingle.setField(new quickfix.DoubleField(AVAILABLE_BALANCE_AMOUNT.fieldId(), Double.parseDouble(availableBalanceAmount)));
        }

        String availableBalanceCurrency = wydenNewOrderSingle.getAvailableBalanceCurrency();
        if (!availableBalanceCurrency.isBlank()) {
            newOrderSingle.setField(new StringField(AVAILABLE_BALANCE_CURRENCY.fieldId(), availableBalanceCurrency));
        }

        return newOrderSingle.toString();
    }

    public static String createFixMarketDataRequest(MarketDataRequest marketDataRequest, char subscriptionRequestType, Set<Character> marketDataTypes) {
        quickfix.fix44.MarketDataRequest fixMessage = new quickfix.fix44.MarketDataRequest(
            new MDReqID(marketDataRequest.getInstrumentKey().getInstrumentId()),
            new SubscriptionRequestType(subscriptionRequestType),
            new MarketDepth(marketDataRequest.getMarketDepth()));

        fixMessage.set(new MDUpdateType(MDUpdateType.FULL_REFRESH));

        marketDataTypes.forEach(mdType -> {
            final NoMDEntryTypes noMDEntryTypes = new NoMDEntryTypes();
            noMDEntryTypes.set(new MDEntryType(mdType));
            fixMessage.addGroup(noMDEntryTypes);
        });

        final NoRelatedSym symbolGroup = new NoRelatedSym();
        symbolGroup.set(new SecurityID(marketDataRequest.getInstrumentKey().getInstrumentId())); // instrumentId root

        String venueAccount = marketDataRequest.getInstrumentKey().getVenueAccount();
        if (!venueAccount.isBlank()) {
            symbolGroup.setString(WYDEN_ACCOUNT.fieldId(), venueAccount);
        }
        String portfolioId = marketDataRequest.getPortfolioId();
        if (!portfolioId.isBlank()) {
            symbolGroup.setString(WYDEN_PORTFOLIO.fieldId(), portfolioId);
        }
        fixMessage.addGroup(symbolGroup);

        return fixMessage.toString();
    }

    private static TimeInForce toFixTif(ClientTIF tif) {
        return switch (tif) {
            case GTC -> new TimeInForce(TimeInForce.GOOD_TILL_CANCEL);
            case GTD -> new TimeInForce(TimeInForce.GOOD_TILL_DATE);
            case IOC -> new TimeInForce(TimeInForce.IMMEDIATE_OR_CANCEL);
            case FOK -> new TimeInForce(TimeInForce.FILL_OR_KILL);
            case DAY -> new TimeInForce(TimeInForce.DAY);
            default -> throw new IllegalArgumentException("Cannot map TIF to FIX: " + tif);
        };
    }

    private static char toFixOrdType(ClientOrderType orderType) {
        return switch (orderType) {
            case MARKET -> OrdType.MARKET;
            case LIMIT -> OrdType.LIMIT;
            case STOP -> OrdType.STOP_STOP_LOSS;
            case STOP_LIMIT -> OrdType.STOP_LIMIT;
            default -> throw new IllegalArgumentException("Cannot map orderType to FIX: " + orderType);
        };
    }

    private static char createSide(ClientSide side) {
        return switch (side) {
            // todo we may need something custom here to allow FIX clients to determin cross margin vs isolated trading type on target exvhange
            case BUY, REDUCE_SHORT -> quickfix.field.Side.BUY;
            case SELL -> quickfix.field.Side.SELL;
            case SELL_SHORT -> quickfix.field.Side.SELL_SHORT;
            default -> throw new IllegalArgumentException("Cannot map side to FIX: " + side);
        };
    }

    private static boolean isSOR(ClientRequest request) {
        return StringUtils.isNotEmpty(request.getSymbol()) && StringUtils.isEmpty(request.getInstrumentId());
    }

}
