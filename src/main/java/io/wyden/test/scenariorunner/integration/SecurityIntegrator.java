package io.wyden.test.scenariorunner.integration;

import io.qameta.allure.Step;
import io.wyden.test.scenariorunner.data.Permission;
import io.wyden.test.scenariorunner.data.user.Administrator;
import io.wyden.test.scenariorunner.data.user.Manager;
import io.wyden.test.scenariorunner.integration.keycloak.KeycloakClient;
import io.wyden.test.scenariorunner.integration.keycloak.KeycloakDataManager;
import io.wyden.test.scenariorunner.integration.service.accessgateway.AccessGatewayDataManager;
import io.wyden.test.scenariorunner.integration.service.accessgateway.Authority;
import io.wyden.test.scenariorunner.integration.service.accessgateway.KeySecret;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class SecurityIntegrator {
    private static final Logger LOGGER = LoggerFactory.getLogger(SecurityIntegrator.class);

    private final KeycloakDataManager keycloakDataManager;
    Map<String, AccessGatewayDataManager> accessGatewayDataManagers = new HashMap<>();

    public SecurityIntegrator() {
        keycloakDataManager = new KeycloakDataManager();
    }

    public KeycloakClient setupManager() {
        return setupManager(Manager.USER_NAME);
    }

    public KeycloakClient setupManager(String clientId) {
        return setupGroupMember(clientId, Manager.GROUP_NAME);
    }

    public KeycloakClient setupAdministrator(String clientId) {
        return setupGroupMember(clientId, Administrator.GROUP_NAME);
    }
    public KeycloakClient setupUser(String clientId) {
        return setupUserWithNoGroup(clientId);
    }

    public KeycloakClient setupE2eMember(String clientId) {
        return setupGroupMember(clientId, "E2E");
    }

    @Step("Setup user {0} of {1} group")
    public KeycloakClient setupGroupMember(String clientId, String groupName) {
        KeycloakClient keycloakClient = keycloakDataManager.createUserBelongingToGroup(clientId, groupName);
        AccessGatewayDataManager accessGatewayDataManager = new AccessGatewayDataManager(keycloakClient, clientId);
        accessGatewayDataManagers.putIfAbsent(clientId, accessGatewayDataManager);
        LOGGER.info("Created user {} of group {}", clientId, groupName);
        return keycloakClient;
    }

    @Step("Setup user {0} with no groups")
    public KeycloakClient setupUserWithNoGroup(String clientId) {
        KeycloakClient keycloakClient = keycloakDataManager.createUser(clientId);
        AccessGatewayDataManager accessGatewayDataManager = new AccessGatewayDataManager(keycloakClient, clientId);
        accessGatewayDataManagers.putIfAbsent(clientId, accessGatewayDataManager);
        LOGGER.info("Created user {} with no groups", clientId);
        return keycloakClient;
    }

    public boolean tokenManagerConfigured() {
        return !accessGatewayDataManagers.isEmpty();
    }

    @Step("Tear down all setup users and groups")
    public void tearDown() {
        if (!accessGatewayDataManagers.isEmpty()) {
            accessGatewayDataManagers.values().forEach(AccessGatewayDataManager::tearDown);
        }
        accessGatewayDataManagers.clear();

        keycloakDataManager.cleanTestUsersAndGroups();
        LOGGER.info("Tear downed all test users and groups");
    }

    @Step("Create key secret for user {0}")
    public KeySecret createKeySecret(String clientId) {
        LOGGER.info("Create key secret for user {}", clientId);
        return accessGatewayDataManagers.get(clientId).createKeySecret(clientId);
    }

    @Step("Share from ownerUser={0} to targetUser={1} READ+TRADE permissions for accounts {2}")
    public void grantVenueAccountTradingAccessRights(String ownerClientId, String targetClientId, String... venueAccounts) {
        if (!accessGatewayDataManagers.containsKey(ownerClientId)) {
            LOGGER.warn("accessGatewayDataManager not configured for this test, skipping...");
            return;
        }

        Set<Authority> authorities = new HashSet<>();
        for (String venueAccountName : venueAccounts) {
            authorities.add(Permission.VENUE_ACCOUNT_TRADE.withResourceId(venueAccountName));
            authorities.add(Permission.VENUE_ACCOUNT_READ.withResourceId(venueAccountName));
        }
        accessGatewayDataManagers.get(ownerClientId).createUserPermissions(targetClientId, authorities);
    }

    @Step("Share from ownerUser={0} to targetUser={1} READ+TRADE permissions for portfolios {2}")
    public void grantPortfolioTradingAccessRights(String ownerClientId, String targetClientId, String... portfolios) {
        if (!accessGatewayDataManagers.containsKey(ownerClientId)) {
            LOGGER.warn("accessGatewayDataManager not configured for this test, skipping...");
            return;
        }

        Set<Authority> authorities = new HashSet<>();
        for (String portfolio : portfolios) {
            authorities.add(Permission.PORTFOLIO_TRADE.withResourceId(portfolio));
            authorities.add(Permission.PORTFOLIO_READ.withResourceId(portfolio));
        }
        accessGatewayDataManagers.get(ownerClientId).createUserPermissions(targetClientId, authorities);
    }

    @Step("Reset user {0} permissions")
    public void resetUserPermissions(String clientId) {
        if (!accessGatewayDataManagers.containsKey(clientId)) {
            LOGGER.warn("accessGatewayDataManager not configured for this test, skipping...");
            return;
        }

        accessGatewayDataManagers.get(clientId).tearDown();
    }

    @Step("Grant from owner={0} to target={1} permissions {2}")
    public void grantPermissions(String ownerClientId, String targetClientId, Authority... permissionsToAdd) {
        if (!accessGatewayDataManagers.containsKey(targetClientId)) {
            LOGGER.warn("accessGatewayDataManager not configured for this test, skipping...");
            return;
        }
        AccessGatewayDataManager accessGatewayDataManager = accessGatewayDataManagers.get(targetClientId);
        List<Authority> existingUserPermissions = accessGatewayDataManager.getUserPermissions(targetClientId);
        Set<Authority> updatedPermissions = Stream.of(existingUserPermissions, Arrays.asList(permissionsToAdd))
            .flatMap(Collection::stream)
            .collect(Collectors.toSet());
        accessGatewayDataManagers.get(ownerClientId).createUserPermissions(targetClientId, updatedPermissions);
    }

    public void addManagedUser(String managerUsername) {
        this.accessGatewayDataManagers.get(managerUsername).addManagedUsername(managerUsername);
    }
}
