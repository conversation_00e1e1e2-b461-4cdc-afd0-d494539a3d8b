package io.wyden.test.scenariorunner.integration.gqlclient;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.qameta.allure.Step;
import io.wyden.apiserver.rest.audit.model.AuditEventDto;
import io.wyden.apiserver.rest.audit.model.PreTradeCheckAuditLogDto;
import io.wyden.apiserver.rest.audit.model.PreTradeCheckAuditLogSearchInput;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.published.audit.AuditEvent;
import io.wyden.published.audit.AuditEventPayload;
import io.wyden.published.audit.PayloadType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class AuditService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RiskService.class);

    private final GraphQLClient gqlClient;
    private final String clientId;

    public AuditService(GraphQLClient gqlClient, String clientId) {
        this.gqlClient = gqlClient;
        this.clientId = clientId;
    }

    @Step("Get pre trade check audit log page by {0}")
    public PaginationModel.CursorConnection<PreTradeCheckAuditLogDto> getPreTradeCheckAuditLogsCursor(PreTradeCheckAuditLogSearchInput searchInput) {
        LOGGER.info("GraphQL Actor ({}): Get pre-trade check audit logs", clientId);
        return gqlClient.auditLog().getPreTradeCheckAuditLogs(searchInput);
    }

    @Step("Get pre trade check audit logs by {0}")
    public Collection<PreTradeCheckAuditLogDto> getPreTradeCheckAuditLogs(PreTradeCheckAuditLogSearchInput searchInput) {
        return getPreTradeCheckAuditLogsCursor(searchInput).getAllNodes();
    }

    @Step("Get audit logs by type {0}")
    public List<AuditEvent> getAuditEvents(PayloadType payloadType) {
        return gqlClient.auditLog().getAuditEvents(payloadType).stream().map(this::parseAuditEventDto).collect(Collectors.toList());
    }

    private AuditEvent parseAuditEventDto(AuditEventDto auditEventDto) {
        try {
            AuditEventPayload.Builder auditEventPayloadBuilder = AuditEventPayload.newBuilder();
            JsonFormat.parser().ignoringUnknownFields().merge(auditEventDto.payload(), auditEventPayloadBuilder);
            return AuditEvent.newBuilder()
                .setUuid(String.valueOf(auditEventDto.uuid()))
                .setSource(auditEventDto.source())
                .setCreatedAt(String.valueOf(auditEventDto.createdAt()))
                .setPayload(auditEventPayloadBuilder.build())
                .build();
        } catch (InvalidProtocolBufferException e) {
            throw new RuntimeException(e);
        }
    }
}
