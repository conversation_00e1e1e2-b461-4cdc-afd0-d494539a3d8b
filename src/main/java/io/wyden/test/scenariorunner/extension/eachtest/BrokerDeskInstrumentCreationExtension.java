package io.wyden.test.scenariorunner.extension.eachtest;

import io.wyden.apiserver.rest.referencedata.instruments.model.dto.VenueTypeDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.create.CreateInstrumentDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.test.scenariorunner.data.refdata.InstrumentFactory;
import io.wyden.test.scenariorunner.data.refdata.PortfolioFactory;
import io.wyden.test.scenariorunner.extension.ExtensionUtils;
import io.wyden.test.scenariorunner.extension.common.InvalidExtensionConfigException;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.ParameterContext;
import org.junit.jupiter.api.extension.ParameterResolutionException;
import org.junit.jupiter.api.extension.ParameterResolver;

import java.util.Collection;
import java.util.List;
import java.util.NoSuchElementException;

import static io.wyden.test.scenariorunner.extension.common.ExtensionActorUtils.gqlActorKey;
import static io.wyden.test.scenariorunner.extension.common.ExtensionActorUtils.throwInvalidExtensionConfigExceptionIfNull;

/**
 * Produces a pair of "mirroring" instruments for BrokerDesk scenarios:
 * - one instrument for street-side
 * - one instrument for client-side
 * The instruments are produced ONCE, before the first test, or before EACH test, depending on the switch passed to its constructor.
 * <p>
 * If you want to make use of this extension, make sure you:
 * 1. Register the extension in your test class
 * 2. Initialize the extension
 * 3. Add parameter of type {@link InstrumentResponseDto} and name {@value STREET_INSTRUMENT_PARAMETER_NAME} to you test methods
 * 4. Add parameter of type {@link InstrumentResponseDto} and name {@value CLIENT_INSTRUMENT_PARAMETER_NAME} to you test methods
 * <p>
 * Once you do the above, the extension will create instruments for you and provide them to your test method.
 * <p>
 * Enjoy!
 */
public class BrokerDeskInstrumentCreationExtension implements BeforeEachCallback, ParameterResolver {

    public static final String STREET_INSTRUMENT_PARAMETER_NAME = "streetInstrument";
    public static final String CLIENT_INSTRUMENT_PARAMETER_NAME = "clientInstrument";
    private final String streetVenue;
    private final String clientVenue;
    private final boolean produceOnce;
    private final String clientAccountOwnerUsername;
    private final String streetAccountOwnerUsername;
    private InstrumentResponseDto clientInstrument;
    private InstrumentResponseDto streetInstrument;
    private boolean produced = false;
    private boolean isNewStreetInstrument = false;

    public BrokerDeskInstrumentCreationExtension(String clientAccountOwnerUsername, String streetAccountOwnerUsername, boolean produceOnce, String clientVenue, String streetVenue) {
        this.clientAccountOwnerUsername = clientAccountOwnerUsername;
        this.streetAccountOwnerUsername = streetAccountOwnerUsername;
        this.produceOnce = produceOnce;
        this.clientVenue = clientVenue;
        this.streetVenue = streetVenue;
    }

    /**
     * @param isNewStreetInstrument in case true, new street instrument will be created
     *                              will be supported in https://algotrader.atlassian.net/browse/AC-4292
     */
    public BrokerDeskInstrumentCreationExtension(String clientAccountOwnerUsername, String streetAccountOwnerUsername, boolean produceOnce, String clientVenue, String streetVenue, boolean isNewStreetInstrument) {
        this.clientAccountOwnerUsername = clientAccountOwnerUsername;
        this.streetAccountOwnerUsername = streetAccountOwnerUsername;
        this.produceOnce = produceOnce;
        this.clientVenue = clientVenue;
        this.streetVenue = streetVenue;
        this.isNewStreetInstrument = isNewStreetInstrument;
    }

    @Override
    public void beforeEach(ExtensionContext context) throws Exception {
        if (!produced) {
            GraphQLActor clientAccountOwner = ExtensionUtils.getGlobalStore(context).get(gqlActorKey(this.clientAccountOwnerUsername), GraphQLActor.class);

            throwInvalidExtensionConfigExceptionIfNull(clientAccountOwner);

            GraphQLActor streetAccountOwner = ExtensionUtils.getGlobalStore(context).get(gqlActorKey(this.streetAccountOwnerUsername), GraphQLActor.class);

            throwInvalidExtensionConfigExceptionIfNull(streetAccountOwner);

            CreateInstrumentDto newStreetInstrument = InstrumentFactory.createRandomForexInstrument(VenueTypeDto.STREET, streetVenue);
            streetInstrument = isNewStreetInstrument ? streetAccountOwner.instrument().createAndWait(newStreetInstrument) : getRandomStreetInstrument(streetAccountOwner, streetVenue);

            String baseCurrency = streetInstrument.forexSpotProperties().baseCurrency();
            String quoteCurrency = streetInstrument.baseInstrument().quoteCurrency();
            CreateInstrumentDto newClientInstrument = InstrumentFactory.createClientSideForexInstrument(clientVenue, baseCurrency, quoteCurrency);

            clientInstrument = clientAccountOwner.instrument().createAndWait(newClientInstrument);

            if (produceOnce) {
                produced = true;
            }
        }
    }

    public String getClientInstrumentId() {
        return clientInstrument.instrumentIdentifiers().instrumentId();
    }

    private InstrumentResponseDto getRandomStreetInstrument(GraphQLActor streetAccountOwner, String streetVenue) {
        Collection<InstrumentResponseDto> streetInstruments = streetAccountOwner.getInstruments(streetVenue);
        if (streetInstruments.isEmpty()) {
            throw new InvalidExtensionConfigException("There are no street instruments accessible for user: %s".formatted(streetAccountOwner.getClientId()));
        }
        return streetInstruments.stream()
            // currently only USD quoted street instruments used because of accounting tests that rely on portfolios in DEFAULT_BOOKING_CURRENCY=USD
            .filter(instrument -> instrument.baseInstrument().quoteCurrency().equals(PortfolioFactory.DEFAULT_BOOKING_CURRENCY))
            .findAny()
            .orElseThrow(() -> new NoSuchElementException("There are no street instruments with quote currency [%s] accessible for user: %s".formatted(PortfolioFactory.DEFAULT_BOOKING_CURRENCY, streetAccountOwner.getClientId())));
    }

    @Override
    public boolean supportsParameter(ParameterContext parameterContext, ExtensionContext extensionContext) throws ParameterResolutionException {
        return InstrumentResponseDto.class.equals(parameterContext.getParameter().getType()) &&
            List.of(STREET_INSTRUMENT_PARAMETER_NAME, CLIENT_INSTRUMENT_PARAMETER_NAME).contains(parameterContext.getParameter().getName());
    }

    @Override
    public Object resolveParameter(ParameterContext parameterContext, ExtensionContext extensionContext) throws ParameterResolutionException {
        if (parameterContext.getParameter().isNamePresent() && parameterContext.getParameter().getName().equals(STREET_INSTRUMENT_PARAMETER_NAME)) {
            return streetInstrument;
        } else if (parameterContext.getParameter().isNamePresent() && parameterContext.getParameter().getName().equals(CLIENT_INSTRUMENT_PARAMETER_NAME)) {
            return clientInstrument;
        } else {
            throw new ParameterResolutionException(String.format("Could not resolve parameter %s", parameterContext.getParameter().getName()));
        }
    }

}
