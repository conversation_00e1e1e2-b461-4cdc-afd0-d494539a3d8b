package io.wyden.test.scenariorunner.extension.alltest;

import io.wyden.test.scenariorunner.integration.SecurityIntegrator;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.integration.keycloak.KeycloakClient;
import org.junit.jupiter.api.extension.ExtensionContext;

public class GraphQLActorExtension extends AbstractClientActorExtension<GraphQLActor> {

    public GraphQLActorExtension(String username, String groupName, boolean requireLogon) {
        super(username, groupName, requireLogon);
    }

    /**
     * Create GraphQLActorExtension with random generated clientId
     * @param groupName group name
     * @param requireLogon require logon
     */
    public GraphQLActorExtension(String groupName, boolean requireLogon) {
        super(groupName, requireLogon);
    }

    /**
     * Create GraphQLActorExtension with logon
     * @param clientId clientId
     * @param groupName group name
     */
    public GraphQLActorExtension(String clientId, String groupName) {
        super(clientId, groupName);
    }

    /**
     * Create GraphQLActorExtension with group=administrators and logon
     * @param clientId username
     */
    public GraphQLActorExtension(String clientId) {
        super(clientId);
    }

    /**
     * Create GraphQLActorExtension with group=administrators and no logon
     */
    public GraphQLActorExtension() {
        super();
    }

    @Override
    protected GraphQLActor createClientActor(ExtensionContext context, SecurityIntegrator securityIntegrator, KeycloakClient keycloakClient) {
        return new GraphQLActor(keycloakClient);
    }

}
