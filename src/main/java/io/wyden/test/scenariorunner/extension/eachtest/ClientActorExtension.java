package io.wyden.test.scenariorunner.extension.eachtest;

import io.wyden.test.scenariorunner.extension.common.AnnotationBasedClientActorCreator;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.integration.SecurityIntegrator;
import io.wyden.test.scenariorunner.integration.keycloak.KeycloakClient;
import org.junit.jupiter.api.extension.ExtensionContext;

public class ClientActorExtension extends AbstractClientActorExtension<ClientActor> {

    public ClientActorExtension() {
        super();
    }

    public ClientActorExtension(String groupName) {
        super(groupName);
    }

    protected ClientActor createActor(ExtensionContext context, SecurityIntegrator securityIntegrator, KeycloakClient keycloakClient) {
        return new AnnotationBasedClientActorCreator().createActor(context, securityIntegrator, keycloakClient);
    }

}
