package io.wyden.test.scenariorunner.extension.common;

import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.annotation.Rest;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.integration.SecurityIntegrator;
import io.wyden.test.scenariorunner.integration.fixclient.FixTradingActor;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.integration.keycloak.KeycloakClient;
import io.wyden.test.scenariorunner.integration.restclient.RestActor;
import io.wyden.test.scenariorunner.integration.service.accessgateway.KeySecret;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.platform.commons.util.AnnotationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AnnotationBasedClientActorCreator {

    private static final Logger LOGGER = LoggerFactory.getLogger(AnnotationBasedClientActorCreator.class);

    public ClientActor createActor(ExtensionContext context, SecurityIntegrator securityIntegrator, KeycloakClient keycloakClient) {

        String clientId = keycloakClient.username();

        ClientActor clientActor = null;

        if (AnnotationUtils.isAnnotated(context.getTestClass(), GraphQL.class)) {
            clientActor = new GraphQLActor(keycloakClient);

        } else if (AnnotationUtils.isAnnotated(context.getTestClass(), Fix.class)) {
            KeySecret keySecret = securityIntegrator.createKeySecret(clientId);
            clientActor = new FixTradingActor(keySecret.key(), keySecret.secret(), clientId);

        } else if (AnnotationUtils.isAnnotated(context.getTestClass(), Rest.class)) {
            //TODO apply key secret to all RestActor tests when rest-api-server controllers will be ready
            if (context.getTestClass().get().getName().contains("MarketData")) {
                KeySecret keySecret = securityIntegrator.createKeySecret(clientId);
                clientActor = new RestActor(keycloakClient, keySecret);
            } else {
                clientActor = new RestActor(keycloakClient);
            }
        }
        if (clientActor == null) {
            LOGGER.error("Client actor was not initialized. Pls check if protocol (@Fix/@GraphQL/@Rest) is defined for test class");
        }
        return clientActor;
    }

}
