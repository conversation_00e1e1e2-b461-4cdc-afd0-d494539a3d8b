package io.wyden.test.scenariorunner.model.booking.trade;

import io.wyden.apiserver.rest.booking.BookingModel;
import io.wyden.test.scenariorunner.model.booking.Transaction;

import java.math.BigDecimal;
import java.util.Objects;

public abstract class Trade extends Transaction {

    protected BigDecimal quantity;
    protected BigDecimal price;
    protected String currency;
    protected BigDecimal fee;
    protected String feeCurrency;
    protected String intOrderId;
    protected String extOrderId;
    protected String orderId;
    protected BookingModel.RootExecution rootExecution;

    public BigDecimal getQuantity() {
        return quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public String getCurrency() {
        return currency;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public String getFeeCurrency() {
        return feeCurrency;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getIntOrderId() {
        return intOrderId;
    }

    public String getExtOrderId() {
        return extOrderId;
    }

    public BookingModel.RootExecution getRootExecution() {
        return rootExecution;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        Trade trade = (Trade) o;
        return Objects.equals(quantity, trade.quantity) && Objects.equals(price, trade.price)
            && Objects.equals(currency, trade.currency) && Objects.equals(fee, trade.fee)
            && Objects.equals(feeCurrency, trade.feeCurrency) && Objects.equals(intOrderId, trade.intOrderId)
            && Objects.equals(extOrderId, trade.extOrderId) && Objects.equals(orderId, trade.orderId)
            && Objects.equals(rootExecution, trade.rootExecution);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), quantity, price, currency, fee, feeCurrency, intOrderId, extOrderId, orderId, rootExecution);
    }

}
