package io.wyden.test.scenariorunner.model.booking.trade;

import java.util.Objects;

public class StreetCashTrade extends CashTrade {

    private String portfolioId;
    private String venueAccount;

    public String getPortfolioId() {
        return portfolioId;
    }

    public String getVenueAccount() {
        return venueAccount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        StreetCashTrade that = (StreetCashTrade) o;
        return Objects.equals(portfolioId, that.portfolioId) && Objects.equals(venueAccount, that.venueAccount);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), portfolioId, venueAccount);
    }

    @Override
    public String toString() {
        return "StreetCashTrade{" +
            "portfolioId='" + portfolioId + '\'' +
            ", venueAccount='" + venueAccount + '\'' +
            ", baseCurrency='" + baseCurrency + '\'' +
            ", underlyingExecutionId='" + underlyingExecutionId + '\'' +
            ", quantity=" + quantity +
            ", price=" + price +
            ", currency='" + currency + '\'' +
            ", fee=" + fee +
            ", feeCurrency='" + feeCurrency + '\'' +
            ", intOrderId='" + intOrderId + '\'' +
            ", extOrderId='" + extOrderId + '\'' +
            ", orderId='" + orderId + '\'' +
            ", uuid='" + uuid + '\'' +
            ", updatedAt=" + updatedAt +
            ", executionId='" + executionId + '\'' +
            ", venueExecutionId='" + venueExecutionId + '\'' +
            ", description='" + description + '\'' +
            ", dateTime=" + dateTime +
            ", settled='" + settled + '\'' +
            ", settledDateTime=" + settledDateTime +
            ", rootExecution=" + rootExecution +
            '}';
    }

}
