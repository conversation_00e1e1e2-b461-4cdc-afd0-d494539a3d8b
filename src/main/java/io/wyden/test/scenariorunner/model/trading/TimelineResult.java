package io.wyden.test.scenariorunner.model.trading;

import io.wyden.apiserver.rest.orderhistory.model.TimelineEventType;
import io.wyden.apiserver.rest.orderhistory.model.TimelineResponse;

import java.io.Serializable;

public record TimelineResult<T extends TimelineResponse> (TimelineEventType type, T data) implements Serializable {

    public TimelineEventType type() {
        return this.type;
    }

    public T data() {
        return this.data;
    }
}
