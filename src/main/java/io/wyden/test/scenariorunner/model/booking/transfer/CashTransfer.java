package io.wyden.test.scenariorunner.model.booking.transfer;

import java.util.Objects;

public abstract class CashTransfer extends Transfer {

    protected String currency;

    public String getCurrency() {
        return currency;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        CashTransfer that = (CashTransfer) o;
        return Objects.equals(currency, that.currency);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), currency);
    }

}
