package io.wyden.test.scenariorunner.data.trading;

import io.wyden.published.client.ClientResponse;

import java.math.BigDecimal;

public class ClientResponseFeeExtractor {

    public static BigDecimal getFeeAmountSum(ClientResponse clientResponse) {
        return clientResponse.getFeeDataList()
            .stream()
            .map(feeData -> new BigDecimal(feeData.getAmount()))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
