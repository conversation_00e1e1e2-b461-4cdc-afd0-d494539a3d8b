package io.wyden.test.scenariorunner.assertion.risk;

import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioResponseDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.TagDto;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckChannel;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckInputDto;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckLevel;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckPropertyDto;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckResponseDto;
import io.wyden.test.scenariorunner.assertion.AbstractSoftAssert;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;

import java.util.List;

public class PreTradeCheckSoftAssert extends AbstractSoftAssert<PreTradeCheckSoftAssert, PreTradeCheckResponseDto> {

    protected PreTradeCheckSoftAssert(PreTradeCheckResponseDto preTradeCheckResponseDto) {
        super(preTradeCheckResponseDto, PreTradeCheckSoftAssert.class);
    }

    public static PreTradeCheckSoftAssert assertThat(PreTradeCheckResponseDto actual) {
        return new PreTradeCheckSoftAssert(actual);
    }

    public PreTradeCheckSoftAssert isOriginTo(PreTradeCheckInputDto preTradeCheckInput) {
        idIs(preTradeCheckInput.id());
        typeIs(preTradeCheckInput.type());
        levelIs(preTradeCheckInput.level());
        channelsAreSameAs(preTradeCheckInput.channels());
        portfolioIdsAreSame(preTradeCheckInput.portfolios());
        portfolioTagsAreSame(preTradeCheckInput.portfolioTags());
        propertiesAreSame(preTradeCheckInput.configuration());
        return this;
    }

    public PreTradeCheckSoftAssert idIs(String id) {
        return isEqualTo("id", actual.id(), id);
    }

    public PreTradeCheckSoftAssert typeIs(String type) {
        return isEqualTo("type", actual.type(), type);
    }

    public PreTradeCheckSoftAssert levelIs(PreTradeCheckLevel level) {
        return isEqualTo("level", actual.level(), level);
    }

    public PreTradeCheckSoftAssert channelsAreSameAs(List<PreTradeCheckChannel> channels) {
        softAssert.assertThat(actual.channels())
            .as("channels are not same as %s".formatted(channels))
            .hasSameElementsAs(channels);
        return this;
    }

    public PreTradeCheckSoftAssert portfolioIdsAreSame(List<String> portfolioIds) {
        softAssert.assertThat(actual.portfolios())
            .extracting(PortfolioResponseDto::id)
            .as("portfolioIds are not same as %s".formatted(portfolioIds))
            .hasSameElementsAs(portfolioIds);
        return this;
    }

    public PreTradeCheckSoftAssert portfolioTagsAreSame(List<TagDto> portfolioTags) {
        softAssert.assertThat(actual.portfolioTags())
            .as("portfolioTags are not same as %s".formatted(portfolioTags))
            .hasSameElementsAs(portfolioTags);
        return this;
    }

    public PreTradeCheckSoftAssert propertiesAreSame(List<PreTradeCheckPropertyDto> properties) {
        softAssert.assertThat(actual.configuration())
            .as("properties are not same as %s".formatted(properties))
            .usingRecursiveFieldByFieldElementComparator(RecursiveComparisonConfiguration.builder()
                .withIgnoreCollectionOrder(true)
                .build())
            .hasSameElementsAs(actual.configuration());
        return this;
    }

}
