package io.wyden.test.scenariorunner.assertion.accounting;

import io.wyden.test.scenariorunner.model.booking.Transaction;
import io.wyden.test.scenariorunner.model.booking.trade.Trade;
import io.wyden.test.scenariorunner.util.NamedPredicate;

public class TransactionPredicates {

    public static NamedPredicate<Transaction> withOrderId(String orderId) {
        return new NamedPredicate<>("orderId=%s".formatted(orderId), t -> ((Trade)t).getOrderId().equals(orderId));
    }

}
