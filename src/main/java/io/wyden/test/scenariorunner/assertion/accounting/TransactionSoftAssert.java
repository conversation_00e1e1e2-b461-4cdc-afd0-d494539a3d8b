package io.wyden.test.scenariorunner.assertion.accounting;

import io.wyden.test.scenariorunner.assertion.AbstractSoftAssert;
import io.wyden.test.scenariorunner.model.booking.Transaction;
import org.assertj.core.api.Assertions;

import java.util.concurrent.TimeUnit;

@SuppressWarnings("unchecked")
public abstract class TransactionSoftAssert<SELF extends TransactionSoftAssert<SELF, ACTUAL>, ACTUAL extends Transaction> extends AbstractSoftAssert<TransactionSoftAssert<SELF, ACTUAL>, ACTUAL> {

    protected TransactionSoftAssert(ACTUAL transaction) {
        super(transaction, TransactionSoftAssert.class);
    }

    public SELF uuidIs(String uuid) {
        isEqualTo("uuid", actual.getUuid(), uuid);
        return (SELF) this;
    }

    public SELF uuidIsNotBlank() {
        isNotBlank("uuid", actual.getUuid());
        return (SELF) this;
    }

    public SELF updatedAtIs(long updatedAt) {
        isEqualTo("updatedAt", actual.getUpdatedAt(), updatedAt);
        return (SELF) this;
    }

    public SELF updatedAtIsLaterThen(long updatedAt) {
        Assertions.assertThat(actual.getUpdatedAt())
            .isGreaterThanOrEqualTo(updatedAt);
        return (SELF) this;
    }

    public SELF updatedAtIsMaxSecondsEarlierThenNow(int seconds) {
        long now = System.currentTimeMillis();
        softAssert.assertThat(actual.getUpdatedAt())
            .as("updatedAt is not max %s seconds earlier then now", seconds)
            .isBetween(now - TimeUnit.SECONDS.toMillis(seconds), now);
        return (SELF) this;
    }

    public SELF executionIdIs(String executionId) {
        isEqualTo("executionId", actual.getExecutionId(), executionId);
        return (SELF) this;
    }

    public SELF executionIdIsNotBlank() {
        isNotBlank("executionId", actual.getExecutionId());
        return (SELF) this;
    }

    public SELF descriptionIs(String description) {
        isEqualTo("description", actual.getDescription(), description);
        return (SELF) this;
    }

}
