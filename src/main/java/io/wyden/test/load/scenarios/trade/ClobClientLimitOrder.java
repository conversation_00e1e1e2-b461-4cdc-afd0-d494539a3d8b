package io.wyden.test.load.scenarios.trade;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import io.wyden.test.load.AlarmClock;
import io.wyden.test.load.ExecutionState;
import io.wyden.test.load.ExecutionStore;
import io.wyden.test.load.ScenarioRunner;
import io.wyden.test.load.actors.Actor;
import io.wyden.test.load.actors.Message;
import io.wyden.test.load.actors.cloud.ConfigurationService;
import io.wyden.test.load.actors.cloud.ExecutionReportMessage;
import io.wyden.test.load.actors.cloud.NewOrderMessage;
import io.wyden.test.load.scenarios.Scenario;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nullable;
import javax.annotation.ParametersAreNonnullByDefault;

import static io.wyden.test.load.actors.cloud.ConfigurationService.CLIENT_INSTRUMENT_ID;
import static io.wyden.test.load.actors.cloud.NewOrderMessage.OrderType.LIMIT;
import static io.wyden.test.load.actors.cloud.NewOrderMessage.Side.SELL;
import static io.wyden.test.load.actors.cloud.NewOrderMessage.TIF.GTC;

@ParametersAreNonnullByDefault
public class ClobClientLimitOrder extends Scenario {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClobClientLimitOrder.class);

    private final ExecutionStore executionStore;
    private final AlarmClock alarmClock;
    private final MeterRegistry meterRegistry;
    private final ConfigurationService configService;
    private final ScenarioRunner.OrderMetadata orderMetadata;

    private static final BigDecimal QUANTITY = BigDecimal.ONE;
    private static final BigDecimal PRICE = BigDecimal.valueOf(0.10009);

    public ClobClientLimitOrder(ScenarioRunner.ScenarioRunnerConfig config, ExecutionStore executionStore, AlarmClock alarmClock, MeterRegistry meterRegistry, ConfigurationService configService) {
        super(config.name());
        this.orderMetadata = config.orderMetadata();
        this.executionStore = executionStore;
        this.alarmClock = alarmClock;
        this.meterRegistry = meterRegistry;
        this.configService = configService;
    }

    @Override
    public ExecutionState request(Actor actor, ExecutionState state) {
        setupTimers(actor, state);
        meterRegistry.counter("wyden.load.scenario.start", getTags(actor, state)).increment();
        String portfolio = configService.getMakerPortfolio();
        if (portfolio == null) {
            LOGGER.error("Cannot find maker portfolio. ConfigurationService may not be setup. Aborting");
            return null;
        }
        String instrumentId = CLIENT_INSTRUMENT_ID;
        NewOrderMessage.Side side = SELL;
        NewOrderMessage.TIF tif = GTC;
        BigDecimal limitPrice = PRICE;
        BigDecimal quantity = QUANTITY;
        if (orderMetadata != null) {
            if (orderMetadata.instrumentId() != null) instrumentId = orderMetadata.instrumentId();
            if (orderMetadata.side() != null) side = orderMetadata.side();
            if (orderMetadata.tif() != null) tif = orderMetadata.tif();
            if (orderMetadata.limitPrice() != null) limitPrice = orderMetadata.limitPrice();
            if (orderMetadata.quantity() != null) quantity = orderMetadata.quantity();
        }
        NewOrderMessage message = NewOrderMessage.newBuilder(state.id())
            .setInstrumentId(instrumentId)
            .setQuantity(quantity)
            .setSide(side)
            .setTif(tif)
            .setOrderType(LIMIT)
            .setPrice(limitPrice)
            .setPortfolio(portfolio)
            .build();
        actor.sendOrder(message);
        meterRegistry.counter("wyden.load.scenario.message.outgoing", getTags(actor, state)).increment();
        alarmClock.set(state.id(), "timeout", Duration.ofSeconds(15));
        state.put("OrderStatus", "PENDING_NEW");
        state.put("StartedAt", Instant.now().toEpochMilli());
        executionStore.put(state.id(), state);
        // TODO: Start failed
        return state;
    }

    @Override
    @Nullable
    public ExecutionState onMessage(Actor actor, ExecutionState state, Message message) {
        setupTimers(actor, state);
        if (message instanceof ExecutionReportMessage executionReportMessage) {
            String newStatus = executionReportMessage.getOrderStatus();
            String oldStatus = (String) state.get("OrderStatus");
            long startedAt = (Long) state.get("StartedAt");
            meterRegistry.counter("wyden.load.scenario.message.incoming", getTags(actor, state)).increment();
            long returnedAt = Instant.now().toEpochMilli();
            if ("PENDING_NEW".equals(oldStatus)) {
                long createdAt = executionReportMessage.getCreatedAt() > 0 ? executionReportMessage.getCreatedAt() : startedAt;
                long venueTimestamp = executionReportMessage.getVenueTimestamp() > 0 ? executionReportMessage.getVenueTimestamp() : executionReportMessage.getTimestamp();
                meterRegistry.timer("wyden.load.latency.order", getTags(actor, state)).record(venueTimestamp - createdAt, TimeUnit.MILLISECONDS);
                meterRegistry.timer("wyden.load.latency.report", getTags(actor, state)).record(returnedAt - venueTimestamp, TimeUnit.MILLISECONDS);
                LOGGER.info("Order latency {}ms", venueTimestamp - createdAt);
                LOGGER.info("Report latency {}ms", returnedAt - venueTimestamp);
            }
            if ("NEW".equals(newStatus)) {
                long acknowledgeLatency = returnedAt - startedAt;
                meterRegistry.timer("wyden.load.latency.ack", getTags(actor, state)).record(acknowledgeLatency, TimeUnit.MILLISECONDS);
                LOGGER.info("Acknowledge latency {}ms", acknowledgeLatency);
                meterRegistry.counter("wyden.load.scenario.ack", getTags(actor, state)).increment();
            }
            if ("NEW".equals(newStatus) || "PARTIALLY_FILLED".equals(newStatus)) {
                state.put("OrderStatus", newStatus);
                LOGGER.debug("New OrderStatus={} for id={}", newStatus, state.id());
                return state;
            } else if ("FILLED".equals(newStatus)) {
                executionStore.remove(state.id());
                alarmClock.cancel(state.id(), "timeout");
                LOGGER.info("Scenario success for id={}", state.id());
                meterRegistry.counter("wyden.load.scenario.success", getTags(actor, state)).increment();
                return null;
            } else {
                executionStore.remove(state.id());
                alarmClock.cancel(state.id(), "timeout");
                LOGGER.warn("Unexpected OrderStatus={} for id={}", newStatus, state.id());
                meterRegistry.counter("wyden.load.scenario.failure", getTags(actor, state)).increment();
                return null;
            }
        } else {
            LOGGER.warn("Unknown message {}", message.getClass().getSimpleName());
            return state;
        }
    }

    public ExecutionState onTimeout(Actor actor, ExecutionState state, String name) {
        setupTimers(actor, state);
        if (!"timeout".equals(name)) {
            LOGGER.warn("Unknown timeout: {}", name);
            return state;
        } else {
            LOGGER.warn("Timeout for id={}", state.id());
            meterRegistry.counter("wyden.load.scenario.timeout", getTags(actor, state)).increment();
            executionStore.remove(state.id());
            return null;
        }
    }

    private void setupTimers(Actor actor, ExecutionState state) {
        // TODO: Counters should be in ScenarioRunner?
        meterRegistry.counter("wyden.load.scenario.message.incoming", getTags(actor, state));
        meterRegistry.counter("wyden.load.scenario.message.outgoing", getTags(actor, state));
        meterRegistry.timer("wyden.load.latency.order", getTags(actor, state));
        meterRegistry.timer("wyden.load.latency.report", getTags(actor, state));
        meterRegistry.timer("wyden.load.latency.ack", getTags(actor, state));
        meterRegistry.counter("wyden.load.scenario.start", getTags(actor, state));
        meterRegistry.counter("wyden.load.scenario.success", getTags(actor, state));
        meterRegistry.counter("wyden.load.scenario.failure", getTags(actor, state));
        meterRegistry.counter("wyden.load.scenario.timeout", getTags(actor, state));
        meterRegistry.counter("wyden.load.scenario.ack", getTags(actor, state));
    }

    private Iterable<Tag> getTags(Actor actor, ExecutionState executionState) {
        return Tags.of("actorId", actor.getActorId(), "actorName", actor.getActorName(),
            "runnerId", executionState.runner().getRunnerId(), "scenarioName", getScenarioName());
    }

}

