package io.wyden.test.licenseserver;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.security.spec.KeySpec;
import java.time.LocalDate;
import java.util.Base64;
import java.util.Deque;
import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;

@RestController
public class TradedVolumeController {
    private static final Logger LOGGER = LoggerFactory.getLogger(TradedVolumeController.class);

    private final Deque<JsonNode> lastRequests;

    public TradedVolumeController(@Value("${volume.requests.to.keep}") int volumeRequestsToKeep) {
        lastRequests = new LinkedBlockingDeque<>(volumeRequestsToKeep);
    }

    @PostMapping(value = "/volume", consumes = MediaType.TEXT_HTML_VALUE)
    public void createReport(@RequestParam String licenseId,
                             @RequestBody String encryptedRequest) {
        var request = decrypt(encryptedRequest, licenseId, JsonNode.class);
        LOGGER.info("Received {}", request);
        synchronized (lastRequests) {
            boolean full = !lastRequests.offer(request);
            if (full) {
                lastRequests.pollFirst();
                lastRequests.offer(request);
            }
        }
    }

    @GetMapping("/volume/requests")
    public List<JsonNode> getReports(@RequestParam(required = false) String licenseId) {
        List<JsonNode> volumeReports = lastRequests.stream()
            .filter(req -> licenseId == null || (req.has("licenseId") && licenseId.equals(req.get("licenseId").asText())))
            .toList();

        LOGGER.info("Reports for: {}: ", licenseId, volumeReports);
        return volumeReports;
    }

    @DeleteMapping("/volume/requests")
    public void clear() {
        LOGGER.info("Clear requested");
        lastRequests.clear();
    }

    @GetMapping("/volume/last")
    public String getLastReportedDate(@RequestParam(required = false) String licenseId) {
        String lastDate = lastRequests.stream()
            .filter(req -> licenseId == null || (req.has("licenseId") && licenseId.equals(req.get("licenseId").asText())))
            .flatMap(req -> req.findValuesAsText("date").stream())
            .filter(StringUtils::isNotBlank)
            .findFirst()
            .orElse(LocalDate.ofEpochDay(0).toString());

        LOGGER.info("Last reported date for: {}: ", licenseId, lastDate);
        return encrypt(lastDate, licenseId);
    }

    private <T> T decrypt(String encryptedRequest, String licenseId, Class<T> requestClass) {
        try {
            String key = "tQdCfEjv_5HfUk4V" + licenseId;
            SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
            KeySpec spec = new PBEKeySpec(key.toCharArray(), licenseId.getBytes(StandardCharsets.UTF_8), 65536, 256);
            SecretKey tmp = factory.generateSecret(spec);
            SecretKeySpec secretKey = new SecretKeySpec(tmp.getEncoded(), "AES");

            Cipher dcipher = Cipher.getInstance("AES");
            dcipher.init(Cipher.DECRYPT_MODE, secretKey);

            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedRequest.getBytes(StandardCharsets.UTF_8));
            byte[] decrypted = dcipher.doFinal(encryptedBytes);
            return new ObjectMapper().readValue(decrypted, requestClass);
        } catch (Exception e) {
            throw new RuntimeException("Decryption failed", e);
        }
    }

    private String encrypt(String data, String licenseId) {
        try {
            String key = "$89JaeP+-yf3h+uf" + licenseId;
            SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
            KeySpec spec = new PBEKeySpec(key.toCharArray(), licenseId.getBytes(StandardCharsets.UTF_8), 65536, 256);
            SecretKey tmp = factory.generateSecret(spec);
            SecretKeySpec secretKey = new SecretKeySpec(tmp.getEncoded(), "AES");

            Cipher ecipher = Cipher.getInstance("AES");
            ecipher.init(Cipher.ENCRYPT_MODE, secretKey);

            byte[] enc = ecipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(enc);
        } catch (Exception e) {
            LOGGER.error("Encryption failed ", e);
            return null;
        }
    }
}
