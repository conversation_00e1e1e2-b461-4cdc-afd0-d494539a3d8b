package io.wyden.test.fixactor.controller;

import io.wyden.test.fixactor.model.UserParamsDto;
import io.wyden.test.fixactor.service.session.SessionManager;
import io.wyden.test.fixactor.service.session.SessionState;
import io.wyden.test.fixactor.service.session.SessionStateStreamer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("session")
public class SessionManagerRestController {

    private final SessionManager sessionManager;
    private final SessionStateStreamer sessionStateStreamer;

    public SessionManagerRestController(@Autowired SessionManager sessionManager, SessionStateStreamer sessionStateStreamer) {
        this.sessionManager = sessionManager;
        this.sessionStateStreamer = sessionStateStreamer;
    }

    /**
     * Initializes FIX Session between the Actor and FIX API Server.
     * The session will be initialized per given clientId.
     * Only one session per clientId will be created.
     * @param clientId session will be initialized and maintained on behalf of given clientId
     * @return operation result: true if success (created on request or already created), false on failure
     */
    @PostMapping("/{clientId}")
    public String initializeSession(@PathVariable("clientId") String clientId, @RequestBody UserParamsDto userParams) {
        return sessionManager.createNewSession(clientId, userParams.getUsername(), userParams.getPassword());
    }

    /**
     * Closes FIX Session between the Actor and FIX API Server.
     * Only session of given clientId will be terminated
     * @param clientId clientId
     * @return true if closed, false if failure
     */
    @DeleteMapping("/{clientId}/{qualifier}")
    public boolean closeSession(@PathVariable("clientId") String clientId,
                                @PathVariable("qualifier") String qualifier) {
        return sessionManager.closeSession(clientId, qualifier);
    }

    @GetMapping("/{clientId}")
    public String getStatus(@PathVariable("clientId") String clientId) {
        SessionState status = sessionManager.getStatus(clientId);
        if (status != null) {
            return status.toString();
        } else {
            return null;
        }
    }

    @GetMapping("/stream/{clientId}/{qualifier}")
    public Flux<String> getSessionStateChanges(@PathVariable("clientId") String clientId,
                                               @PathVariable("qualifier") String qualifier) {
        return sessionStateStreamer.sessionStateChanges()
            .filter(change -> change.qualifier().equals(qualifier))
            .filter(change -> change.clientId().equals(clientId))
            .map(change -> change.sessionState().toString());
    }
}
