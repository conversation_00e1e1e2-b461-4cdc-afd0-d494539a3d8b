package io.wyden.connector;

import ch.algotrader.api.connector.application.Connector;

import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.connector.account.ConnectorWrapperFacade;
import io.wyden.connector.account.VenueAccountAware.ActivityResult;
import io.wyden.connector.infra.ConnectorBuilder;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;

import java.util.HashMap;
import java.util.Map;

@SpringBootApplication
@Import(value = {io.wyden.cloud.utils.spring.fluentd.FluentdAppender.class})
@PropertySource("classpath:common.properties")
public class WintermuteConnectorApplication {

    private static final Logger LOGGER = LoggerFactory.getLogger(WintermuteConnectorApplication.class);

    private final RabbitIntegrator rabbitIntegrator;
    private final Telemetry telemetry;
    private final ConnectorBuilder connectorBuilder;
    private final ApplicationContext applicationContext;
    private final String accountName;
    private final int heartbeatInterval;
    private final int mdReconnectInterval;
    private final Map<String, String> venueOverride = new HashMap<>();

    ConnectorWrapperFacade connectorWrapperFacade;

    WintermuteConnectorApplication(RabbitIntegrator rabbitIntegrator,
                                   Telemetry telemetry,
                                   ConnectorBuilder connectorBuilder,
                                   ApplicationContext applicationContext,
                                   @Value("${account.name}") String accountName,
                                   @Value("${mdm-heartbeat-interval:120}") int heartbeatInterval,
                                   @Value("${md-reconnect-interval:120}") int mdReconnectInterval,
                                   @Value("#{${venue-override:{T(java.util.Collections).emptyMap()}}}") Map<String, String> venueOverride
    ) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.telemetry = telemetry;
        this.connectorBuilder = connectorBuilder;
        this.applicationContext = applicationContext;
        this.accountName = accountName;
        this.heartbeatInterval = heartbeatInterval;
        this.mdReconnectInterval = mdReconnectInterval;
        this.venueOverride.putAll(venueOverride);
    }

    @PostConstruct
    void init() {
        Connector connector = connectorBuilder.createConnector(accountName);
        connectorWrapperFacade = new ConnectorWrapperFacade(rabbitIntegrator, telemetry, connector, applicationContext, heartbeatInterval, mdReconnectInterval, venueOverride);
        ActivityResult result = connectorWrapperFacade.registerAccount(accountName)
            .block();
        if (result == ActivityResult.SUCCESS) {
            LOGGER.info("Initialized {}", accountName);
        } else {
            LOGGER.info("Failed to initialize {}", accountName);
        }
    }

    @PreDestroy
    void destroy() {
        if (connectorWrapperFacade != null) {
            connectorWrapperFacade.unregisterAccount(accountName)
                .block();
        }
    }

    public static void main(String[] args) {
        SpringApplication.run(WintermuteConnectorApplication.class, args);
    }
}
