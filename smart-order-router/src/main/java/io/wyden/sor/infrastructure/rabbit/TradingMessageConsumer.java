package io.wyden.sor.infrastructure.rabbit;

import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.context.Context;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.destination.TradingMessageParser;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsTargetType;
import io.wyden.sor.service.fsm.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.Map;

import static io.wyden.published.oems.OemsRequest.OemsPTCStatus.APPROVED;
import static io.wyden.published.oems.OemsRequest.OemsPTCStatus.NOT_REQUIRED;
import static io.wyden.published.oems.OemsResponse.OemsResponseType.CANCEL_REJECT;
import static io.wyden.published.oems.OemsResponse.OemsResponseType.EXECUTION_REPORT;

@Component
public class TradingMessageConsumer implements MessageConsumer<Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradingMessageConsumer.class);
    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitDestinations rabbitDestinations;
    private final OrderService orderService;
    private final Tracing otlTracing;
    private final String queueName;

    private RabbitQueue<Message> queue;

    TradingMessageConsumer(RabbitIntegrator rabbitIntegrator,
                           RabbitDestinations rabbitDestinations,
                           OrderService orderService,
                           Tracing otlTracing,
                           @Value("${rabbitmq.trading-smart-order-router-queue}") String queueName) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.rabbitDestinations = rabbitDestinations;
        this.orderService = orderService;
        this.otlTracing = otlTracing;
        this.queueName = queueName;

        declareQueue();
    }
    @Override
    public ConsumptionResult consume(Message message, AMQP.BasicProperties properties) {
        Context parent = otlTracing.loadContext(RabbitHeadersPropagator.create(properties.getHeaders()), RabbitHeadersPropagator.getter());
        try (var ignored = otlTracing.createBaggage(parent)) {
            try (var ignored2 = otlTracing.createSpan("request.consume", SpanKind.CONSUMER, parent)) {
                return consumeInner(message, properties);
            }
        }
    }

    private ConsumptionResult consumeInner(@Nullable Message data, AMQP.BasicProperties properties) {
        LOGGER.debug("Received new Trading message. Properties: {}", properties);

        try {
            if (data == null) {
                LOGGER.error("Message parsing failed");
                Span.current().setStatus(StatusCode.ERROR, "Message parsing failed");
                return ConsumptionResult.failureNonRecoverable();
            } else if (data instanceof OemsRequest oemsRequest && isSorRequest(properties)) {
                orderService.onOemsRequest(oemsRequest);
                return ConsumptionResult.consumed();
            } else if (data instanceof OemsRequest oemsRequest && isSorChildRequestDL(properties)) {
                orderService.onChildOrderNotDelivered(oemsRequest);
                return ConsumptionResult.consumed();
            } else if (data instanceof OemsResponse oemsResponse && oemsResponse.getResponseType() == EXECUTION_REPORT && isSorResponse(properties)) {
                orderService.onOemsParentResponse(oemsResponse);
                return ConsumptionResult.consumed();
            }  else if (data instanceof OemsResponse oemsResponse && oemsResponse.getResponseType() == EXECUTION_REPORT && !isSorResponse(properties)) {
                orderService.onOemsChildResponse(oemsResponse);
                return ConsumptionResult.consumed();
            } else if (data instanceof OemsResponse oemsResponse && oemsResponse.getResponseType() == CANCEL_REJECT) {
                orderService.onCancelReject(oemsResponse);
                return ConsumptionResult.consumed();
            } else {
                LOGGER.error("Message type not supported: {}", data.getClass().getSimpleName());
                Span.current().setStatus(StatusCode.ERROR, "Message type not supported");
                return ConsumptionResult.failureNonRecoverable();
            }
        } catch (Exception ex) {
            LOGGER.error("Failed to process message, dropping", ex);
            Span.current().setStatus(StatusCode.ERROR);
            Span.current().recordException(ex);
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    private boolean isSorResponse(AMQP.BasicProperties properties) {
        return hasHeader(OemsHeader.SOURCE_TYPE, OemsTargetType.SOR.name(), properties);
    }

    private void declareQueue() {
        queue = new RabbitQueueBuilder<>(rabbitIntegrator)
            .setQueueName(queueName)
            .setSingleActiveConsumer(true)
            .declare();

        queue.attachConsumer(TradingMessageParser.parser(), this);

        bindQueueToSorRequests(rabbitDestinations.getTradingIngressExchange(), APPROVED);
        bindQueueToSorRequests(rabbitDestinations.getTradingIngressExchange(), NOT_REQUIRED);
        bindQueueToSorChildResponses(rabbitDestinations.getTradingUnroutedExchange());
        bindQueueToSorParentResponses(rabbitDestinations.getTradingUnroutedExchange());
        bindQueueToSorChildRequestsDL(rabbitDestinations.getTradingDLX());
    }

    private <T extends Message> void bindQueueToSorRequests(RabbitExchange<Message> exchange, OemsRequest.OemsPTCStatus ptcStatus) {
        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName(),
            OemsHeader.TARGET_TYPE.getHeaderName(), OemsTargetType.SOR.name(),
            OemsHeader.PTC.getHeaderName(), ptcStatus.name()
        );
        bindQueue(exchange, headers);
    }

    private boolean isSorRequest(AMQP.BasicProperties properties) {
        return hasHeader(OemsHeader.TARGET_TYPE, OemsTargetType.SOR.name(), properties);
    }

    private void bindQueueToSorChildResponses(RabbitExchange<Message> exchange) {
        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName(),
            OemsHeader.TARGET_TYPE.getHeaderName(), Metadata.ServiceType.SOR.name()
        );
        bindQueue(exchange, headers);
    }

    private void bindQueueToSorParentResponses(RabbitExchange<Message> exchange) {
        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName(),
            OemsHeader.SOURCE_TYPE.getHeaderName(), Metadata.ServiceType.SOR.name()
        );
        bindQueue(exchange, headers);
    }

    private void bindQueueToSorChildRequestsDL(RabbitExchange<Message> exchange) {
        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName(),
            OemsHeader.SOURCE_TYPE.getHeaderName(), OemsTargetType.SOR.name(),
            OemsHeader.TARGET_TYPE.getHeaderName(), OemsTargetType.EXTERNAL_VENUE_ACCOUNT.name()
        );
        bindQueue(exchange, headers);
    }

    private boolean isSorChildRequestDL(AMQP.BasicProperties properties) {
        return hasHeader(OemsHeader.SOURCE_TYPE, OemsTargetType.SOR.name(), properties)
            && hasHeader(OemsHeader.TARGET_TYPE, OemsTargetType.EXTERNAL_VENUE_ACCOUNT.name(), properties);
    }

    private void bindQueue(RabbitExchange<Message> exchange, Map<String, Object> headers) {
        LOGGER.info("Binding exchange {} and queue {} with headers {}", exchange.getName(), queue.getName(), headers);
        queue.bindWithHeaders(exchange, MatchingCondition.ALL, headers);
    }

    private static boolean hasHeader(OemsHeader header, String expectedValue, AMQP.BasicProperties properties) {
        Object value = properties.getHeaders().get(header.getHeaderName());
        return value != null && value.toString().equals(expectedValue);
    }
}
