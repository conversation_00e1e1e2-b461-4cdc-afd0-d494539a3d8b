package io.wyden.sor.service.tracking;

import com.hazelcast.core.HazelcastInstance;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.sor.domain.map.RecommendationSubscriptionMapConfig;
import io.wyden.sor.model.RecommendationSubscription;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class RecommendationSubscriptionCache extends RecommendationSubscriptionLookup {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecommendationSubscriptionCache.class);
    private final long completedOrderTtlSeconds;
    private final boolean canRemove;

    public RecommendationSubscriptionCache(HazelcastInstance hazelcast,
                                           Tracing otlTracing,
                                           @Value("${hz.completedOrderTtlSeconds:60}") long completedOrderTtlSeconds,
                                           @Value("${hz.remove:true}") boolean canRemove) {
        super(RecommendationSubscriptionMapConfig.getMap(hazelcast), otlTracing);
        this.completedOrderTtlSeconds = completedOrderTtlSeconds;
        this.canRemove = canRemove;
    }

    public void add(RecommendationSubscription recommendationSubscription) {
        try (var ignored = otlTracing.createSpan("recommendationsubscriptioncache.add", SpanKind.CLIENT)) {
            addInner(recommendationSubscription);
        }
    }

    private void addInner(RecommendationSubscription recommendationSubscription) {
        String recommendationSubscriptionId = recommendationSubscription.getRecommendationSubscriptionId();
        uniqueConstraintCheck(recommendationSubscriptionId);

        LOGGER.debug("Registering a new RecommendationSubscription in the cache TTL={}\n{}", completedOrderTtlSeconds, recommendationSubscription);
        recommendationSubscriptionMap.put(recommendationSubscriptionId, recommendationSubscription, completedOrderTtlSeconds, TimeUnit.SECONDS);
    }

    public void update(RecommendationSubscription recommendationSubscription) {
        try (var ignored = otlTracing.createSpan("recommendationsubscriptioncache.update", SpanKind.CLIENT)) {
            updateInner(recommendationSubscription);
        }
    }

    private void updateInner(RecommendationSubscription updatedState) {
        LOGGER.trace("Updating RecommendationSubscription in the cache TTL={}\n{}", completedOrderTtlSeconds, updatedState);
        recommendationSubscriptionMap.put(updatedState.getRecommendationSubscriptionId(), updatedState, completedOrderTtlSeconds, TimeUnit.SECONDS);
    }

    private void uniqueConstraintCheck(String recommendationSubscriptionId) {
        if (recommendationSubscriptionMap.containsKey(recommendationSubscriptionId)) {
            throw new DuplicateRecommendationSubscription(
                "Unique constraint fail - RecommendationSubscription with recommendationSubscriptionId %s already present!".formatted(recommendationSubscriptionId));
        }
    }

    public void updateBestExecutionRequestRefreshId(RecommendationSubscription recommendationSubscription, String bestExecutionRequestRefreshId) {
        RecommendationSubscription newRecommendationSubscription = recommendationSubscription.toBuilder()
            .setBestExecutionRequestRefreshId(bestExecutionRequestRefreshId)
            .build();
        update(newRecommendationSubscription);
    }

    public RecommendationSubscription updateSreHostId(RecommendationSubscription recommendationSubscription, String sreHostId) {
        RecommendationSubscription newRecommendationSubscription = recommendationSubscription.toBuilder()
            .setSreHostId(sreHostId)
            .build();
        update(newRecommendationSubscription);
        return newRecommendationSubscription;
    }

    public void remove(String recommendationSubscriptionId) {
        try (var ignored = otlTracing.createSpan("recommendationsubscriptioncache.remove", SpanKind.CLIENT)) {
            if (canRemove) {
                LOGGER.trace("Removing RecommendationSubscription from the cache: {}", recommendationSubscriptionId);
                recommendationSubscriptionMap.remove(recommendationSubscriptionId);
            } else {
                LOGGER.warn("Cannot remove RecommendationSubscription from the cache: {}", recommendationSubscriptionId);
            }
        }
    }

    public static class DuplicateRecommendationSubscription extends FailureNonRecoverableException {
        public DuplicateRecommendationSubscription(String message) {
            super(message);
        }
    }
}
