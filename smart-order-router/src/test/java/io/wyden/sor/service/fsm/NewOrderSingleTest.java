package io.wyden.sor.service.fsm;

import io.wyden.published.oems.OemsInstrumentType;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsSide;
import io.wyden.sor.service.tracking.FailureRequeueException;
import org.junit.jupiter.api.Test;

import static io.wyden.published.oems.OemsExecType.REJECTED;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_NEW;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_REJECTED;
import static io.wyden.published.oems.OemsResponse.OemsResponseType.EXECUTION_REPORT;
import static io.wyden.published.oems.OemsResponse.Result.OEMS_ORDER_REGISTRATION_ERROR_DUPLICATE;
import static io.wyden.published.oems.OemsResponse.Result.OEMS_ORDER_REGISTRATION_ERROR_VALIDATION;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;


class NewOrderSingleTest extends OrderServiceBase {

    @Test
    void orderWithDuplicatedOrderIdShouldBeRejected() {
        orderService.onOemsRequest(order);
        verify(oemsResponseEmitter, ONCE).emit(any(OemsResponse.class));
        reset(oemsResponseEmitter);

        OemsRequest order2 = defaultOrder().toBuilder().setOrderId(order.getOrderId()).build();
        orderService.onOemsRequest(order2);
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_NEW);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_DUPLICATE);
        assertThat(oemsResponse.getReason()).isEqualTo("Unique constraint fail - Order with orderId %s already present!".formatted(order.getOrderId()));
    }

    @Test
    void orderWithMissingSideShouldBeRequeued() {
        order = defaultOrder().toBuilder().setSide(OemsSide.SIDE_UNDETERMINED).build();
        assertThrows(FailureRequeueException.class, () -> orderService.onOemsRequest(order));
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, NEVER).emit(any(OemsResponse.class));
    }

    @Test
    void orderWithoutSymbolShouldBeRejected() {
        order = defaultOrder().toBuilder()
            .setSymbol(EMPTY)
            .build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Smart order " + oemsResponse.getOrderId() + ": symbol not set");
    }
    @Test
    void orderWithoutInstrumentTypeShouldBeRejected() {
        order = defaultOrder().toBuilder()
            .setInstrumentType(OemsInstrumentType.INSTRUMENT_TYPE_UNSPECIFIED)
            .build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Smart order " + oemsResponse.getOrderId() + ": instrument type not valid: INSTRUMENT_TYPE_UNSPECIFIED");
    }

    @Test
    void orderWithMissingOrderIdShouldBeRejected() {
        order = defaultOrder().toBuilder().setOrderId("").build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Field orderId is blank");
    }

    @Test
    void orderWithMissingClientIdShouldBeRejected() {
        order = defaultOrder().toBuilder().setClientId("").build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Smart order " + oemsResponse.getOrderId() + ": field clientId is blank");
    }

    @Test
    void orderWithMissingOrderTypeShouldBeRequeued() {
        order = defaultOrder().toBuilder().setOrderType(OemsOrderType.ORDER_TYPE_UNSPECIFIED).build();
        assertThrows(FailureRequeueException.class, () -> orderService.onOemsRequest(order));
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, NEVER).emit(any(OemsResponse.class));
    }

    @Test
    void orderWithMissingQuantityShouldBeRejected() {
        order = defaultOrder().toBuilder().setQuantity("").build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Smart order " + oemsResponse.getOrderId() + ": field quantity is blank");
    }

    @Test
    void orderWithNonNumberQuantityShouldBeRejected() {
        order = defaultOrder().toBuilder().setQuantity("abc").build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Smart order " + oemsResponse.getOrderId() + ": field quantity, number 'abc' parse error");
    }

    @Test
    void orderWithZeroQuantityShouldBeRejected() {
        order = defaultOrder().toBuilder().setQuantity("0").build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Smart order " + oemsResponse.getOrderId() + ": field quantity invalid value: 0");
    }

    @Test
    void orderWithNegativeQuantityShouldBeRejected() {
        order = defaultOrder().toBuilder().setQuantity("-10").build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Smart order " + oemsResponse.getOrderId() + ": field quantity invalid value: -10");
    }

    @Test
    void orderWithMissingPriceShouldBeRejected() {
        order = defaultOrder().toBuilder().setPrice("").build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Smart order " + oemsResponse.getOrderId() + ": field price is blank, orderType STOP_LIMIT");
    }

    @Test
    void orderWithNonNumberPriceShouldBeRejected() {
        order = defaultOrder().toBuilder().setPrice("cde").build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Smart order " + oemsResponse.getOrderId() + ": field limit price, number 'cde' parse error");
    }

    @Test
    void orderWithMissingStopPriceShouldBeRejected() {
        order = defaultOrder().toBuilder().setStopPrice("").build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Smart order " + oemsResponse.getOrderId() + ": field stop price is blank, orderType STOP_LIMIT");
    }

    @Test
    void orderWithNonNumberStopPriceShouldBeRejected() {
        order = defaultOrder().toBuilder().setStopPrice("cde").build();
        orderService.onOemsRequest(order);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getAllValues().get(0);
        assertThat(oemsResponse.getResponseType()).isEqualTo(EXECUTION_REPORT);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_VALIDATION);
        assertThat(oemsResponse.getReason()).isEqualTo("Smart order " + oemsResponse.getOrderId() + ": field stop price, number 'cde' parse error");
    }
}
