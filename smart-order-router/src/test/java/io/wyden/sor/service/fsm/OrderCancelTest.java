package io.wyden.sor.service.fsm;

import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.sor.service.tracking.FailureRequeueException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static io.wyden.published.oems.OemsOrderStatus.STATUS_CANCELED;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_FILLED;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PENDING_CANCEL;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_REJECTED;
import static io.wyden.published.oems.OemsResponse.Result.OEMS_CANCEL_ERROR;
import static io.wyden.published.oems.OemsResponse.Result.OEMS_CANCEL_ERROR_WRONG_STATE;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;

class OrderCancelTest extends OrderServiceBase {

    @BeforeEach
    void beforeEach() {
        orderService.onOemsRequest(order);
        reset(oemsResponseEmitter);
    }

    @Test
    void givenMissingOrderIdCancelIsRejected() {
        OemsRequest cancel = defaultCancel().toBuilder()
            .setOrderId(EMPTY)
            .build();
        orderService.onOemsRequest(cancel);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse response = oemsResponseCaptor.getAllValues().get(0);
        assertThat(response.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.CANCEL_REJECT);
        assertThat(response.getMetadata().getInResponseToRequestId()).isEqualTo(cancel.getMetadata().getRequestId());
        assertThat(response.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(response.getRequestResult()).isEqualTo(OEMS_CANCEL_ERROR);
        assertThat(response.getReason()).isEqualTo("Field orderId is blank");
    }

    @Test
    void givenUnknownOrderIdCancelIsRequeued() {
        OemsRequest cancel = defaultCancel().toBuilder()
            .setOrderId(UUID.randomUUID().toString())
            .build();
        assertThrows(FailureRequeueException.class, () -> orderService.onOemsRequest(cancel));
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, NEVER).emit(any());
    }

    @Test
    void givenMissingRequestIdCancelIsRejected() {
        OemsRequest cancel = defaultCancel(EMPTY);
        orderService.onOemsRequest(cancel);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse response = oemsResponseCaptor.getAllValues().get(0);
        assertThat(response.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.CANCEL_REJECT);
        assertThat(response.getMetadata().getRequestId()).isEqualTo(cancel.getMetadata().getRequestId());
        assertThat(response.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(response.getRequestResult()).isEqualTo(OEMS_CANCEL_ERROR);
        assertThat(response.getReason()).isEqualTo("Smart order " + response.getOrderId() + ": field requestId is blank");
    }

    @Test
    void givenOrderCancelledCancelIsRejected() {
        orderService.onOemsChildResponse(newExecutionReport());
        orderService.onOemsRequest(defaultCancel());
        orderService.onOemsChildResponse(cancelledExecutionReport("0", "0"));
        reset(venueRequestEmitter);
        reset(oemsResponseEmitter);

        OemsRequest cancel = defaultCancel();
        orderService.onOemsRequest(cancel);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse response = oemsResponseCaptor.getAllValues().get(0);
        assertThat(response.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.CANCEL_REJECT);
        assertThat(response.getMetadata().getInResponseToRequestId()).isEqualTo(cancel.getMetadata().getRequestId());
        assertThat(response.getOrderStatus()).isEqualTo(STATUS_CANCELED);
        assertThat(response.getRequestResult()).isEqualTo(OEMS_CANCEL_ERROR_WRONG_STATE);
        assertThat(response.getReason()).isEqualTo("Current status: STATUS_CANCELED");
    }

    @Test
    void givenOrderFilledCancelIsRejected() {
        orderService.onOemsChildResponse(newExecutionReport());
        orderService.onOemsChildResponse(filledExecutionReport("10.0", "10.0", "25.0", "25.0"));
        reset(oemsResponseEmitter);

        OemsRequest cancel = defaultCancel();
        orderService.onOemsRequest(cancel);
        verify(venueRequestEmitter, NEVER).emit(any(OemsRequest.class));
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse response = oemsResponseCaptor.getAllValues().get(0);
        assertThat(response.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.CANCEL_REJECT);
        assertThat(response.getMetadata().getInResponseToRequestId()).isEqualTo(cancel.getMetadata().getRequestId());
        assertThat(response.getOrderStatus()).isEqualTo(STATUS_FILLED);
        assertThat(response.getRequestResult()).isEqualTo(OEMS_CANCEL_ERROR_WRONG_STATE);
        assertThat(response.getReason()).isEqualTo("Current status: STATUS_FILLED");
    }

    @Test
    void givenOrderRejectedCancelIsExecuted() {
        orderService.onOemsChildResponse(rejectedExecutionReport(EMPTY));
        reset(oemsResponseEmitter);

        OemsRequest cancel = defaultCancel();
        orderService.onOemsRequest(cancel);
        verify(oemsResponseEmitter, TWICE).emit(oemsResponseCaptor.capture());
        {
            OemsResponse responsePending = oemsResponseCaptor.getAllValues().get(0);
            assertThat(responsePending.getOrderId()).isEqualTo(order.getOrderId());
            assertThat(responsePending.getMetadata().getRequestId()).isEqualTo(cancel.getMetadata().getRequestId());
            assertThat(responsePending.getExecType()).isEqualTo(OemsExecType.PENDING_CANCEL);
            assertThat(responsePending.getOrderStatus()).isEqualTo(STATUS_PENDING_CANCEL);
        }

        {
            OemsResponse response = oemsResponseCaptor.getAllValues().get(1);
            assertThat(response.getOrderId()).isEqualTo(order.getOrderId());
            assertThat(response.getMetadata().getRequestId()).isEqualTo(cancel.getMetadata().getRequestId());
            assertThat(response.getExecType()).isEqualTo(OemsExecType.CANCELED);
            assertThat(response.getOrderStatus()).isEqualTo(STATUS_CANCELED);
        }
    }

    @Test
    void givenOrderNewCancelIsExecuted() {
        OemsResponse executionReport = newExecutionReport();
        orderService.onOemsChildResponse(executionReport);
        reset(oemsResponseEmitter);

        OemsRequest cancel = defaultCancel();
        orderService.onOemsRequest(cancel);
        verify(oemsResponseEmitter, TWICE).emit(oemsResponseCaptor.capture());
        {
            OemsResponse responsePending = oemsResponseCaptor.getAllValues().get(0);
            assertThat(responsePending.getOrderId()).isEqualTo(order.getOrderId());
            assertThat(responsePending.getMetadata().getRequestId()).isEqualTo(cancel.getMetadata().getRequestId());
            assertThat(responsePending.getExecType()).isEqualTo(OemsExecType.PENDING_CANCEL);
            assertThat(responsePending.getOrderStatus()).isEqualTo(STATUS_PENDING_CANCEL);
            assertThat(responsePending.getOrderQty()).isEqualTo("10.0");
            assertThat(responsePending.getCumQty()).isEqualTo("0");
            assertThat(responsePending.getLastQty()).isEqualTo("0");
            assertThat(responsePending.getLeavesQty()).isEqualTo("10.0");
            assertThat(responsePending.getAvgPrice()).isEqualTo("0");
            assertThat(responsePending.getLastPrice()).isEqualTo("0");
        }

        {
            OemsResponse response = oemsResponseCaptor.getAllValues().get(1);
            assertThat(response.getOrderId()).isEqualTo(order.getOrderId());
            assertThat(response.getMetadata().getRequestId()).isEqualTo(cancel.getMetadata().getRequestId());
            assertThat(response.getExecType()).isEqualTo(OemsExecType.CANCELED);
            assertThat(response.getOrderStatus()).isEqualTo(STATUS_CANCELED);
            assertThat(response.getOrderQty()).isEqualTo("10.0");
            assertThat(response.getCumQty()).isEqualTo("0");
            assertThat(response.getLastQty()).isEqualTo("0");
            assertThat(response.getLeavesQty()).isEqualTo("10.0");
            assertThat(response.getAvgPrice()).isEqualTo("0");
            assertThat(response.getLastPrice()).isEqualTo("0");
        }
    }

    @Test
    void givenOrderPartiallyFilledCancelIsExecuted() {
        orderService.onOemsChildResponse(newExecutionReport());
        OemsResponse executionReport = partialFillExecutionReport("3.0", "3.0", "25.0", "25.0");
        orderService.onOemsChildResponse(executionReport);
        reset(oemsResponseEmitter);

        OemsRequest cancel = defaultCancel();
        orderService.onOemsRequest(cancel);
        verify(oemsResponseEmitter, TWICE).emit(oemsResponseCaptor.capture());
        {
            OemsResponse responsePending = oemsResponseCaptor.getAllValues().get(0);
            assertThat(responsePending.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.EXECUTION_REPORT);
            assertThat(responsePending.getOrderId()).isEqualTo(order.getOrderId());
            assertThat(responsePending.getMetadata().getRequestId()).isEqualTo(cancel.getMetadata().getRequestId());
            assertThat(responsePending.getExecType()).isEqualTo(OemsExecType.PENDING_CANCEL);
            assertThat(responsePending.getOrderStatus()).isEqualTo(STATUS_PENDING_CANCEL);
            assertThat(responsePending.getOrderQty()).isEqualTo("10.0");
            assertThat(responsePending.getCumQty()).isEqualTo("3.0");
            assertThat(responsePending.getLastQty()).isEqualTo("0");
            assertThat(responsePending.getLeavesQty()).isEqualTo("7.0");
            assertThat(responsePending.getAvgPrice()).isEqualTo("25.0");
            assertThat(responsePending.getLastPrice()).isEqualTo("0");
        }

        {
            OemsResponse response = oemsResponseCaptor.getAllValues().get(1);
            assertThat(response.getOrderId()).isEqualTo(order.getOrderId());
            assertThat(response.getMetadata().getRequestId()).isEqualTo(cancel.getMetadata().getRequestId());
            assertThat(response.getExecType()).isEqualTo(OemsExecType.CANCELED);
            assertThat(response.getOrderStatus()).isEqualTo(STATUS_CANCELED);
            assertThat(response.getOrderQty()).isEqualTo("10.0");
            assertThat(response.getCumQty()).isEqualTo("3.0");
            assertThat(response.getLastQty()).isEqualTo("0");
            assertThat(response.getLeavesQty()).isEqualTo("7.0");
            assertThat(response.getAvgPrice()).isEqualTo("25.0");
            assertThat(response.getLastPrice()).isEqualTo("0");
        }
    }
}
