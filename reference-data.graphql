extend type Query {
    venueList(types : [VenueType!]) : [VenueResponse]

    venueAccountList(input : VenueAccountSearchInput) : VenueAccountConnection
    venueAccount(id : String!) : VenueAccountResponse
    venueAccountPositions(id : String!) : VenueAccountPositionsResponse
    venueAccountNameCheck(name : String) : <PERSON><PERSON><PERSON>
}

extend type Mutation {
    venueCreate(name: String!, type : VenueType!): MutationSubmittedResponse

    venueAccountCreate(input : VenueAccountInput!) : MutationSubmittedResponse
    venueAccountUpdate(input : VenueAccountInput!) : MutationSubmittedResponse
    venueAccountAction(id : String!, type : VenueAccountActionType!) : MutationSubmittedResponse

    venueAccountPositionCreate(venueAccountId : String!, input : VenueAccountPositionInput!) : MutationSubmittedResponse
    venueAccountPositionUpdate(venueAccountId : String!, input : VenueAccountPositionInput!) : MutationSubmittedResponse
    venueAccountPositionDelete(venueAccountId : String!, currency : String!) : MutationSubmittedResponse

}

input VenueAccountPositionInput {
    currency: String!
    connectorId: String
    walletId: String
    metadata: [KeyValueInput!]
}

input VenueAccountInput {
    venueAccountId: String
    venueAccountName: String!
    connectorId: String
    venueAccountType: VenueAccountType!
    vostroNostro: VostroNostro!
    correlationObject: String!
    integrated: Boolean
}

input VenueAccountSearchInput {
    venueAccountId: String
    venueAccountName: String
    venueAccountType: VenueAccountType
    venue: String
    connectorId: String
    vostroNostro: VostroNostro
    sortingOrder: SortingOrder
    first: Int
    after: String
    archived: Boolean
}

type VenueAccountConnection {
    edges: [VenueAccountEdge!]
    pageInfo: PageInfo!
}

type VenueAccountEdge {
    node: VenueAccountResponse
    cursor: String
}

type VenueAccountResponse {
    venueAccountId: String
    venueAccountName: String
    connectorId: String!
    venue: String
    accountType: VenueAccountType
    vostroNostro: VostroNostro
    integrated: Boolean

    archivedAtDatetime: String  # Epoch Unix Timestamp
    createdAtDatetime: String # Epoch Unix Timestamp

    scopes: [Scope]
    dynamicScopes: [Scope]
}

type VenueAccountPositionsResponse {
    venueAccountId: String
    positions: [VenueAccountPosition]
}

type VenueAccountPosition {
    currency: String!
    connectorId: String
    connectorName: String
    walletId: String
    metadata: [KeyValue!]!
}

type VenueResponse {
    name: String,
    type: VenueType
}

enum VenueAccountActionType {
    ARCHIVE, UNARCHIVE
}

enum VenueAccountType {
    EXCHANGE,
    CUSTODY,
    CLOB
}

enum VostroNostro {
    VOSTRO,
    NOSTRO
}
