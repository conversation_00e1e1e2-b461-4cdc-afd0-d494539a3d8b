package io.wyden.apiserver.fix.marketdata;

import io.wyden.apiserver.fix.common.dictionary.WydenPortfolio;
import io.wyden.apiserver.fix.common.fix.FixSessionWrapper;
import io.wyden.apiserver.fix.common.security.AccessService;
import io.wyden.apiserver.fix.inbound.fix.MarketDataRequestHandler;
import io.wyden.cloud.utils.test.ExchangeObserver;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.published.audit.EventLogEvent;
import io.wyden.published.audit.EventLogStatus;
import io.wyden.published.common.Metadata;
import io.wyden.published.marketdata.BidAskQuote;
import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.MarketDataIdentifier;
import io.wyden.published.marketdata.MarketDataRequest;
import io.wyden.published.marketdata.Trade;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.ForexSpotProperties;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.VenueType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.DirtiesContext;
import quickfix.FieldNotFound;
import quickfix.SessionID;
import quickfix.field.MDEntryType;
import quickfix.field.MDReqID;
import quickfix.field.MDUpdateType;
import quickfix.field.MarketDepth;
import quickfix.field.SecurityID;
import quickfix.field.SubscriptionRequestType;
import quickfix.field.Symbol;
import quickfix.fix44.MarketDataSnapshotFullRefresh;
import quickfix.fix44.Message;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;
import static org.testcontainers.shaded.org.apache.commons.lang3.StringUtils.EMPTY;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
public class ClientSideSubscriptionTest extends MDIntegrationTestBase {


    // Internal format - ISO8601 in UTC time zone with microsecond precision. Choosen precision is driven by PostgreSQL precision.
    public static final DateTimeFormatter ISO_UTC_FORMATTER;
    public static final MarketDataSnapshotFullRefresh.NoMDEntries NO_MD_ENTRIES = new MarketDataSnapshotFullRefresh.NoMDEntries();
    public static final MDEntryType MD_ENTRY_TYPE = new MDEntryType();

    static {
        ISO_UTC_FORMATTER = new DateTimeFormatterBuilder()
            .parseCaseInsensitive()
            .appendInstant(6)
            .toFormatter();
    }

    private static final String INSTRUMENT_ID = "i1@FOREX@Test";
    private static final String INSTRUMENT_ID_2 = "i2@FOREX@Test";
    private static final String SYMBOL = "i1";
    private static final String PORTFOLIO_ID_1 = "p1";
    private static final String PORTFOLIO_ID_2 = "p2";
    private static final String CLIENT_ID_1 = "client1";
    private static final String CLIENT_ID_2 = "client2";
    private static final String STREAM_ID_1 = "1234";
    private static final String STREAM_ID_2 = "5678";
    private static final String STREAM_ID_1_1 = "1234xyz";
    private static final String STREAM_ID_2_1 = "5678abc";
    private static final SessionID SESSION_1 = new SessionID("FIX.4.4", "wydenInTest", CLIENT_ID_1);
    private static final SessionID SESSION_2 = new SessionID("FIX.4.4", "wydenInTest", CLIENT_ID_2);

    @Autowired
    MarketDataRequestHandler marketDataRequestHandler;

    @Autowired
    RabbitExchange<MarketDataRequest> marketDataRequestExchange;

    @Autowired
    RabbitExchange<EventLogEvent> eventLogExchange;

    @Autowired
    RabbitExchange<MarketDataEvent> mdEvents;

    @MockBean
    FixSessionWrapper fixSessionWrapper;

    @MockBean
    AccessService accessService;

    private ExchangeObserver<MarketDataRequest> configRequestObserver;
    private ScheduledFuture<?> scheduledFuture;
    private List<Message> session1Events;
    private List<Message> session2Events;
    List<MDReqID> requestsSent;

    @BeforeEach
    void setUp() {
        requestsSent = new ArrayList<>();
        session1Events = new CopyOnWriteArrayList<>();
        session2Events = new CopyOnWriteArrayList<>();

        configRequestObserver = ExchangeObserver
            .newBuilder(rabbitIntegrator, marketDataRequestExchange, (data, p) -> MarketDataRequest.parseFrom(data), "it-req-config")
            .withRoutingKey("broker-config-service")
            .build();
        configRequestObserver.attach();

        when(accessService.hasClientSideMarketDataAccess(any())).thenReturn(true);

        when(instrumentsRepository.find(anyString())).thenReturn(clientSideInstrument());
        doAnswer(invocationOnMock -> {
            SessionID session = invocationOnMock.getArgument(0, SessionID.class);
            Message msg = invocationOnMock.getArgument(1, Message.class);
            if (session.getTargetCompID().equals(CLIENT_ID_1)) {
                session1Events.add(msg);
            } else if (session.getTargetCompID().equals(CLIENT_ID_2)) {
                session2Events.add(msg);
            }
            return null;
        })
            .when(fixSessionWrapper).send(any(SessionID.class), any(Message.class));
    }

    @AfterEach
    void tearDown() {
        if (scheduledFuture != null) {
            scheduledFuture.cancel(true);
        }

        configRequestObserver.detach();

        requestsSent.forEach(r -> {
            quickfix.fix44.MarketDataRequest unsub = createUnsubMdReq(r.getValue());
            marketDataRequestHandler.onMessage(unsub, SESSION_1);
            marketDataRequestHandler.onMessage(unsub, SESSION_2);
        });
    }

    @Test
    void subscriptionRequestShouldTriggerConfigServiceRequest() {
        // when
        quickfix.fix44.MarketDataRequest mdRequestMessage = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessage, SESSION_1);

        // then
        MarketDataRequest marketDataRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID));
        assertThat(marketDataRequest.getInstrumentKey().getInstrumentId()).isEqualTo(INSTRUMENT_ID);
        assertThat(marketDataRequest.getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
    }

    @Test
    void subscriptionAckShouldTriggerProperQueueBind() {
        // given
        quickfix.fix44.MarketDataRequest mdRequestMessage = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessage, SESSION_1);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID));
        // and -- assume config service forwarded to pricing service
        pricingServiceIsProducing(STREAM_ID_1, INSTRUMENT_ID);

        // when
        ackedByPricingService(STREAM_ID_1, configServiceReceivedRequest.getMetadata().getRequestId());

        // then
        await().until(() -> !session1Events.isEmpty());
    }

    @Test
    void ackedSubscriptionShouldBeRenewedWithStreamId() {
        // given -- requested by client
        quickfix.fix44.MarketDataRequest mdRequestMessage = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessage, SESSION_1);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID));

        // when -- acked by pricing service, with streamId
        ackedByPricingService(STREAM_ID_1, configServiceReceivedRequest.getMetadata().getRequestId());

        // then -- config service should start receiving heartbeats with streamId
        configRequestObserver.getMessage().set(null);
        configRequestObserver.awaitMessage(mdr -> mdr.getStreamId().equals(STREAM_ID_1));
        configRequestObserver.awaitMessage(mdr -> mdr.getStreamId().equals(STREAM_ID_1));
        configRequestObserver.awaitMessage(mdr -> mdr.getStreamId().equals(STREAM_ID_1));
    }

    @Test
    void additionalSubscriberShouldReuseSubscription() {
        // given
        quickfix.fix44.MarketDataRequest mdRequestMessage = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessage, SESSION_1);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID));
        // and -- assume config service forwarded to pricing service
        pricingServiceIsProducing(STREAM_ID_1, INSTRUMENT_ID);
        ackedByPricingService(STREAM_ID_1, configServiceReceivedRequest.getMetadata().getRequestId());

        // and -- first client is receiving data
        await().until(() -> !session1Events.isEmpty());

        // when -- second client subscribes for the same thing
        quickfix.fix44.MarketDataRequest mdRequestMessageClient2 = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessageClient2, SESSION_2);

        // then -- second client should also receive data
        await().until(() -> !session2Events.isEmpty());

        // and -- data for both consumers should come from the same stream
        assertThat(session1Events)
            .extracting(m -> m.getField(new Symbol()))
            .allMatch(t -> t.equals(new Symbol(SYMBOL)));

        assertThat(session2Events)
            .extracting(m -> m.getField(new Symbol()))
            .allMatch(t -> t.equals(new Symbol(SYMBOL)));
    }

    @Test
    void subscriptionsOnTwoPortfoliosAreIsolated() {
        // given
        quickfix.fix44.MarketDataRequest mdRequestMessage = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessage, SESSION_1);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID));
        // and -- assume config service forwarded to pricing service
        pricingServiceIsProducing(STREAM_ID_1, INSTRUMENT_ID);
        ackedByPricingService(STREAM_ID_1, configServiceReceivedRequest.getMetadata().getRequestId());

        // and -- first client is receiving data
        await().until(() -> !session1Events.isEmpty());

        // when -- second client subscribes for the same thing
        quickfix.fix44.MarketDataRequest mdRequestMessageClient2 = createMdReq(PORTFOLIO_ID_2, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessageClient2, SESSION_2);

        // then - should be treated as distinct sub and forwarded to config
        MarketDataRequest configServiceReceivedRequest2 = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID));
        // and -- delivered to pricing service eventually and streamed with different streamId
        pricingServiceIsProducing(STREAM_ID_2, INSTRUMENT_ID);
        ackedByPricingService(STREAM_ID_2, configServiceReceivedRequest2.getMetadata().getRequestId());

        // then -- second client should also receive data
        await().until(() -> !session2Events.isEmpty());

        // and -- data for both consumers should come from different streams
        // STREAM 1 receives OHLC from mid-prices, STREAM 2 receives OHLC from trades
        assertThat(session1Events)
            .allSatisfy(m -> assertThat(m.getGroups(NO_MD_ENTRIES.getFieldTag()))
                .extracting(grp -> grp.getField(MD_ENTRY_TYPE).getValue())
                .containsExactlyInAnyOrder('w', 'x', 'y', 'z'));

        assertThat(session2Events)
            .allSatisfy(m -> assertThat(m.getGroups(NO_MD_ENTRIES.getFieldTag()))
                .extracting(grp -> grp.getField(MD_ENTRY_TYPE).getValue())
                .containsExactlyInAnyOrder('o', 'h', 'l', 'c'));
    }

    @Test
    void leavingSubscriberShouldNotDisposeSharedSubscription() throws FieldNotFound {
        // given
        quickfix.fix44.MarketDataRequest mdRequestMessage = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessage, SESSION_1);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID));
        // and -- assume config service forwarded to pricing service
        pricingServiceIsProducing(STREAM_ID_1, INSTRUMENT_ID);
        ackedByPricingService(STREAM_ID_1, configServiceReceivedRequest.getMetadata().getRequestId());

        // and -- second client subscribes for the same thing
        quickfix.fix44.MarketDataRequest mdRequestMessageClient2 = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessageClient2, SESSION_2);

        // and -- both clients received data
        await().until(() -> !session1Events.isEmpty());
        await().until(() -> !session2Events.isEmpty());

        // when -- client1 leaves
        quickfix.fix44.MarketDataRequest mdUnsubRequestMessage = createUnsubMdReq(mdRequestMessage.getMDReqID().getValue());
        marketDataRequestHandler.onMessage(mdUnsubRequestMessage, SESSION_1);

        // then -- client2 should keep receiving data
        session2Events.clear();
        await().until(() -> !session2Events.isEmpty());

        // and -- heart-beating should continue
        configRequestObserver.getMessage().set(null);
        configRequestObserver.awaitMessage(mdr -> mdr.getStreamId().equals(STREAM_ID_1));
    }

    @Test
    void lastLeavingSubscriberShouldDisposeSubscription() throws FieldNotFound {
        // given
        quickfix.fix44.MarketDataRequest mdRequestMessage = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessage, SESSION_1);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID));
        // and -- assume config service forwarded to pricing service
        pricingServiceIsProducing(STREAM_ID_1, INSTRUMENT_ID);
        ackedByPricingService(STREAM_ID_1, configServiceReceivedRequest.getMetadata().getRequestId());

        // and -- second client subscribes for the same thing
        // given
        quickfix.fix44.MarketDataRequest mdRequestMessage2 = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessage2, SESSION_2);

        // when -- both subscribers are leaving
        quickfix.fix44.MarketDataRequest unsub1 = createUnsubMdReq(mdRequestMessage.getMDReqID().getValue());
        quickfix.fix44.MarketDataRequest unsub2 = createUnsubMdReq(mdRequestMessage2.getMDReqID().getValue());
        marketDataRequestHandler.onMessage(unsub1, SESSION_1);
        marketDataRequestHandler.onMessage(unsub2, SESSION_2);

        // then -- heart-beating should stop
        configRequestObserver.getMessage().set(null);
        configRequestObserver.ensureNoMoreMessages();
    }

    @Test
    void configChangeForPortfolioIdShouldTriggerNewRequestsForAllSubs() {
        // given -- 2 requests made by client on the same Portfolio
        quickfix.fix44.MarketDataRequest mdRequestMessage = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID);
        marketDataRequestHandler.onMessage(mdRequestMessage, SESSION_1);
        quickfix.fix44.MarketDataRequest mdRequestMessageClient2 = createMdReq(PORTFOLIO_ID_1, INSTRUMENT_ID_2);
        marketDataRequestHandler.onMessage(mdRequestMessageClient2, SESSION_2);

        // and -- both requests delivered to config, to be forwarded to pricing
        await().until(() -> {
            boolean id1Received = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID));
            boolean id2Received = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID));
            return id1Received && id2Received;
        });
        String fixApiRequest1 = configRequestObserver.getMessages().stream()
            .filter(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID))
            .map(mdr -> mdr.getMetadata().getRequestId()).findFirst().get();
        String fixApiRequest2 = configRequestObserver.getMessages().stream()
            .filter(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID_2))
            .map(mdr -> mdr.getMetadata().getRequestId()).findFirst().get();

        // and -- both acked by pricing service, each with streamId
        ackedByPricingService(STREAM_ID_1, fixApiRequest1);
        ackedByPricingService(STREAM_ID_2, fixApiRequest2);

        // and -- pricing service is receiving heartbeats with both streamIds
        configRequestObserver.getMessage().set(null);
        await().until(() -> {
            boolean stream1Renewed = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getStreamId().equals(STREAM_ID_1));
            boolean stream2Renewed = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getStreamId().equals(STREAM_ID_2));
            return stream1Renewed && stream2Renewed;
        });

        // when -- config has changed for portfolioId
        configChanged(PORTFOLIO_ID_1, EMPTY, EMPTY);

        // then -- request heart-beating to config service with old streamId should stop
        configRequestObserver.getMessage().set(null);
        configRequestObserver.ensureNoMoreMessages(mdr -> !mdr.getStreamId().equals(STREAM_ID_1));
        configRequestObserver.ensureNoMoreMessages(mdr -> !mdr.getStreamId().equals(STREAM_ID_2));

        // and then -- config service should receive new requests for both instruments from FIX API Server
        // and -- both requests delivered to config, to be forwarded to pricing
        await().until(() -> {
            boolean id1Received = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID) && !mdr.getMetadata().getRequestId().equals(fixApiRequest1));
            boolean id2Received = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID) && !mdr.getMetadata().getRequestId().equals(fixApiRequest2));
            return id1Received && id2Received;
        });
        String fixApiRequest1_1 = configRequestObserver.getMessages().stream()
            .filter(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID) && !mdr.getMetadata().getRequestId().equals(fixApiRequest1))
            .map(mdr -> mdr.getMetadata().getRequestId()).findFirst().get();
        String fixApiRequest2_1 = configRequestObserver.getMessages().stream()
            .filter(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(INSTRUMENT_ID_2) && !mdr.getMetadata().getRequestId().equals(fixApiRequest2))
            .map(mdr -> mdr.getMetadata().getRequestId()).findFirst().get();

        // and -- both acked by pricing service, each with new streamIds
        ackedByPricingService(STREAM_ID_1_1, fixApiRequest1_1);
        ackedByPricingService(STREAM_ID_2_1, fixApiRequest2_1);

        // and -- pricing service is receiving heartbeats with new streamIds
        configRequestObserver.getMessage().set(null);
        await().until(() -> {
            boolean stream1_1Renewed = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getStreamId().equals(STREAM_ID_1_1));
            boolean stream2_2Renewed = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getStreamId().equals(STREAM_ID_2_1));
            return stream1_1Renewed && stream2_2Renewed;
        });

        // and -- server is propagating market data events with new stream ids to client
        pricingServiceIsProducing(STREAM_ID_1_1, INSTRUMENT_ID);
        pricingServiceIsProducing(STREAM_ID_2_1, INSTRUMENT_ID_2);
        // and -- both clients are receiving data
        session1Events.clear();
        session2Events.clear();
        await().until(() -> !session1Events.isEmpty());
        await().until(() -> !session2Events.isEmpty());
    }

    private void configChanged(String portfolioId, String portfolioGroup, String instrumentId) {
        eventLogExchange.publishWithHeaders(EventLogEvent.newBuilder()
            .setStatus(EventLogStatus.SUCCESS)
            .setBrokerConfigChanged(EventLogEvent.BrokerConfigChanged.newBuilder()
                .setPortfolioId(portfolioId)
                .setPortfolioGroup(portfolioGroup)
                .setInstrumentId(instrumentId)
                .addAffectedAreas(EventLogEvent.BrokerConfigType.PRICING)
                .build())
            .build(), Map.of(OemsHeader.CLIENT_ID.getHeaderName(), "rest-api-server"));
    }

    private quickfix.fix44.MarketDataRequest createMdReq(String portfolioId, String instrumentId) {
        MDReqID mDReqID = new MDReqID(UUID.randomUUID().toString());
        quickfix.fix44.MarketDataRequest marketDataRequest = new quickfix.fix44.MarketDataRequest(
            mDReqID,
            new SubscriptionRequestType(SubscriptionRequestType.SNAPSHOT_UPDATES),
            new MarketDepth(1));

        marketDataRequest.set(new MDUpdateType(MDUpdateType.FULL_REFRESH));

        quickfix.fix44.MarketDataRequest.NoMDEntryTypes bid = new quickfix.fix44.MarketDataRequest.NoMDEntryTypes();
        bid.set(new MDEntryType(MDEntryType.BID));
        marketDataRequest.addGroup(bid);
        quickfix.fix44.MarketDataRequest.NoMDEntryTypes offer = new quickfix.fix44.MarketDataRequest.NoMDEntryTypes();
        offer.set(new MDEntryType(MDEntryType.OFFER));
        marketDataRequest.addGroup(offer);
        quickfix.fix44.MarketDataRequest.NoMDEntryTypes trade = new quickfix.fix44.MarketDataRequest.NoMDEntryTypes();
        trade.set(new MDEntryType(MDEntryType.TRADE));
        marketDataRequest.addGroup(trade);

        quickfix.fix44.MarketDataRequest.NoRelatedSym noRelatedSym = new quickfix.fix44.MarketDataRequest.NoRelatedSym();
        noRelatedSym.set(new SecurityID(instrumentId));
        noRelatedSym.setField(new WydenPortfolio(portfolioId));
        marketDataRequest.addGroup(noRelatedSym);

        requestsSent.add(mDReqID);

        return marketDataRequest;
    }

    private static quickfix.fix44.MarketDataRequest createUnsubMdReq(String mdReqId) {
        return new quickfix.fix44.MarketDataRequest(
            new MDReqID(mdReqId),
            new SubscriptionRequestType(SubscriptionRequestType.DISABLE_PREVIOUS_SNAPSHOT_UPDATE_REQUEST),
            new MarketDepth(1));
    }

    private Instrument clientSideInstrument() {
        return Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setVenueType(VenueType.CLIENT)
                .setSymbol("BTCUSD@Bank")
                .setVenueName("Bank")
                .build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("BTC")
                .build())
            .build();
    }

    private void pricingServiceIsProducing(String streamId, String instrumentId) {
        scheduledFuture = Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(() -> {
            MarketDataEvent.Builder builder = MarketDataEvent.newBuilder()
                .setIdentifier(MarketDataIdentifier.newBuilder()
                    .setInstrumentId(instrumentId)
                    .setStreamId(streamId)
                    .setDateTime(ZonedDateTime.now().toInstant().atZone(UTC).format(ISO_UTC_FORMATTER))
                    .build());

            if (streamId.equals(STREAM_ID_1)) {
                builder
                    .setBidAskQuote(BidAskQuote.newBuilder()
                        .setBidPrice("1")
                        .setBidSize("1")
                        .setAskPrice("2")
                        .setAskSize("1")
                        .build());
            } else {
                builder
                    .setTrade(Trade.newBuilder()
                        .setLastPrice("1")
                        .setLastSize("1")
                        .build());
            }

            mdEvents.publishWithHeaders(builder.build(), Map.of(OemsHeader.MD_STREAM_ID.getHeaderName(), streamId));
        }, 0, 50, TimeUnit.MILLISECONDS);
    }

    private void ackedByPricingService(String streamId, String requestId) {
        eventLogExchange.publishWithHeaders(EventLogEvent.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setInResponseToRequestId(requestId)
                .setInResponseToRequesterId("fix-api-custom-ohlc")
                .build())
            .setEventType("marketDataRequest")
            .setStatus(EventLogStatus.SUCCESS)
            .setMarketDataSubscriptionCreated(EventLogEvent.MarketDataSubscriptionCreated.newBuilder()
                .setStreamId(streamId)
                .build())
            .build(), Map.of(OemsHeader.CLIENT_ID.getHeaderName(), "fix-api-custom-ohlc"));
    }
}
