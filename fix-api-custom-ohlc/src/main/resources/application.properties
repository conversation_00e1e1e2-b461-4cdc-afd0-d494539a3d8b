spring.application.name = fix-api-custom-ohlc
spring.profiles.active = prod

server.port = 8086

fix.acceptor.templateSession.senderCompID = ATFIXOHLC
fix.socketAccept.port = 9879

rabbitmq.username = fixapicustomohlc
rabbitmq.password = password
rabbitmq.virtualHost = /
rabbitmq.host = localhost
# default RabbitMQ port for non-TLS connections: 5672, default port for TLS connections: 5671
rabbitmq.port = 5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls =

marketdata.client.single-active-queue = fix-api-custom-ohlc-queue.market-data.single-active.EVENT
marketdata.client.single-active-queue.message.ttl = PT2H
marketdata.clientside.renewViaConfigService = true
event.log.queueTemplatePrefix = fix-api-custom-ohlc-queue.event-log.%s.EVENT

access.gateway.host = http://localhost:8089

# comma-separated list of hz member hosts
hz.addressList = localhost
hz.outboundPortDefinition =

spring.datasource.url=****************************************************
spring.datasource.username=fix_api_custom_ohlc
spring.datasource.password=password
spring.datasource.hikari.maximum-pool-size=3

spring.flyway.url=${spring.datasource.url}
spring.flyway.user=${spring.datasource.username}
spring.flyway.password=${spring.datasource.password}
spring.flyway.enabled=false
spring.flyway.locations=classpath:psql/migration/schema,classpath:psql/migration/data

tracing.collector.endpoint=http://localhost:4317
management.endpoints.web.exposure.include=health,prometheus,metrics,loggers
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.metrics.tags.wyden_service=fix-api-custom-ohlc

# default false. when set to true, then logged off sessions will be preserved and fed with makret data events
fix.acceptor.feedLoggedOffSessions = true

mdm-heartbeat-interval = 15

# A string representation of duration using ISO-8601 seconds based representation, such as PT8H6M12.345S
bar.size = PT1S
