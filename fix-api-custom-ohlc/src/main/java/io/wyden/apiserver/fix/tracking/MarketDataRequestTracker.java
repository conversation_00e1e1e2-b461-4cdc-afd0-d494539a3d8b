package io.wyden.apiserver.fix.tracking;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.wyden.apiserver.fix.common.telemetry.Meters;
import io.wyden.apiserver.fix.domain.MarketDataSubscriptionsMapConfig;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import quickfix.FieldNotFound;
import quickfix.SessionID;
import quickfix.fix44.MarketDataRequest;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static io.wyden.apiserver.fix.common.telemetry.Meters.storageQueryLatencyTimer;
import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;

@Service
public class MarketDataRequestTracker {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataRequestTracker.class);

    private final IMap<String, Pair<MarketDataRequest, SessionID>> requestMap;
    private final MeterRegistry meterRegistry;

    public MarketDataRequestTracker(HazelcastInstance hazelcastInstance, Telemetry telemetry) {
        this.requestMap = MarketDataSubscriptionsMapConfig.getMap(hazelcastInstance);
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    public void addIfAbsent(MarketDataRequest message, SessionID sessionId) throws Exception {
        recordLatencyIn(latencyTimer(Meters.QueryType.MD_TRACKER_ADD)).ofThrowing(() -> {
            String key = asKey(message, sessionId);
            requestMap.putIfAbsent(key, Pair.of(message, sessionId));
        });
    }

    public void delete(MarketDataRequest message, SessionID sessionId) throws Exception {
        recordLatencyIn(latencyTimer(Meters.QueryType.MD_TRACKER_DELETE)).ofThrowing(() -> {
            String key = asKey(message, sessionId);
            requestMap.delete(key);
        });
    }

    public void delete(String key) {
        recordLatencyIn(latencyTimer(Meters.QueryType.MD_TRACKER_DELETE)).of(() -> {
            requestMap.delete(key);
        });
    }

    private String asKey(MarketDataRequest message, SessionID sessionId) throws FieldNotFound {
        try {
            return message.getMDReqID().getValue() + sessionId.toString();
        } catch (FieldNotFound e) {
            LOGGER.error("Failed for extract key for request: {}, sessionId: {}", message, sessionId, e);
            throw e;
        }
    }

    public Collection<Pair<MarketDataRequest, SessionID>> findAll() {
        return recordLatencyIn(latencyTimer(Meters.QueryType.MD_TRACKER_FIND))
            .of(() -> requestMap.values());
    }

    public List<String> findAllKeys() {
        return recordLatencyIn(latencyTimer(Meters.QueryType.MD_TRACKER_FIND_KEYS))
            .of(() -> new ArrayList<>(requestMap.keySet()));
    }

    public int size() {
        return recordLatencyIn(latencyTimer(Meters.QueryType.MD_TRACKER_SIZE))
            .of(requestMap::size);
    }

    private Timer latencyTimer(Meters.QueryType queryType) {
        try {
            return storageQueryLatencyTimer(this.meterRegistry, queryType);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}
