package io.wyden.apiserver.fix.infrastructure.rabbit;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.telemetry.Telemetry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
public class RabbitWiring {

    @Bean
    public RabbitIntegrator rabbitIntegrator(@Value("${rabbitmq.username}") String userName,
                                             @Value("${rabbitmq.password}") String password,
                                             @Value("${rabbitmq.tls}") String tls,
                                             @Value("${rabbitmq.virtualHost}") String virtualHost,
                                             @Value("${rabbitmq.host}") String host,
                                             @Value("${rabbitmq.port}") int port,
                                             Telemetry telemetry,
                                             ExecutorService consumingExecutor,
                                             ExecutorService publishingExecutor) {
        MeterRegistry meterRegistry = telemetry.getMeterRegistry();
        return new RabbitIntegrator(userName, password, virtualHost, host, port, tls, meterRegistry, consumingExecutor, publishingExecutor);
    }

    @Bean
    public ExecutorService publishingExecutor() {
        return Executors.newSingleThreadExecutor();
    }

    @Bean
    public ExecutorService consumingExecutor() {
        return Executors.newSingleThreadExecutor();
    }
}
