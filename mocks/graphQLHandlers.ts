import { ENTITLEMENTS } from '@wyden/constants';
import {
  aBaseInstrumentResponse,
  aConnectorResponse,
  aCounterparty,
  aCreateInstrumentResult,
  aCurrencyResponse,
  aDetailedCapabilities,
  aForexSpotPropertiesResponse,
  aHedgeResult,
  aMarketQuote,
  aMatchResponse,
  aPageInfo,
  aPermission,
  aPortfolioConnection,
  aPortfolioEdge,
  aPortfolioGroupConfigurationFlat,
  aPortfolioResponse,
  aPositionConnection,
  aPositionEdge,
  aPositionResponse,
  aRateSubscriptionResponse,
  aSymbolSearchConnection,
  aSymbolSearchEdge,
  aSymbolSearchResponse,
  aTifsPerOrderType,
  aTimelineConnection,
  aTimelineEdge,
  aTimelineResult,
  aTradingCapabilities,
  aTradingConstraintsResponse,
  aVenue,
  aVenueAccount,
  aVenueAccountDesc,
  aVenueAccountsPerVenue,
  aVenueCapabilitiesResponse,
  aWalletAccountConnection,
  aWalletAccountEdge,
  aWalletAccountResponse,
  anInstrumentEdge,
  anInstrumentIdentifiersResponse,
  anInstrumentResponse,
  anInstrumentsConnection,
  anOrderRequestsPerOrderType,
  anOrderStateResponse,
} from '@wyden/services/graphql/generated/mocks';
import { graphql } from 'msw';
import {
  AssetClass,
  OrderCategory,
  OrderRequest,
  OrderStatus,
  OrderType,
  ParticipantReferenceType,
  PortfolioType,
  Resource,
  Scope,
  Side,
  Tif,
  TimelineEventType,
  VenueType,
} from '../src/wyden/services/graphql/generated/graphql';

const venueCapabilitiesWithCashOrders = {
  venueCapabilities: [
    aVenueCapabilitiesResponse({
      venue: 'bitmex',
      capabilities: aDetailedCapabilities({
        tradingCapabilities: aTradingCapabilities({
          supportedOrderTypes: [
            OrderType.Market,
            OrderType.MarketCash,
            OrderType.Limit,
            OrderType.Stop,
            OrderType.StopLimit,
          ],
          supportedTIFs: [
            aTifsPerOrderType({
              key: OrderType.Market,
              values: [Tif.Gtd, Tif.Day, Tif.Fok, Tif.Gtc, Tif.Ioc],
            }),
            aTifsPerOrderType({
              key: OrderType.MarketCash,
              values: [Tif.Gtd, Tif.Day, Tif.Gtc, Tif.Ioc],
            }),
            aTifsPerOrderType({
              key: OrderType.Limit,
              values: [Tif.Day, Tif.Fok, Tif.Gtc, Tif.Ioc],
            }),
            aTifsPerOrderType({
              key: OrderType.Stop,
              values: [Tif.Day, Tif.Fok, Tif.Gtc, Tif.Ioc],
            }),
            aTifsPerOrderType({
              key: OrderType.StopLimit,
              values: [Tif.Day, Tif.Fok, Tif.Gtc, Tif.Ioc],
            }),
          ],
          supportedOrderRequests: [
            anOrderRequestsPerOrderType({
              key: OrderType.StopLimit,
              values: [OrderRequest.Cancel, OrderRequest.Modify, OrderRequest.Submit],
            }),
            anOrderRequestsPerOrderType({
              key: OrderType.Market,
              values: [OrderRequest.Submit, OrderRequest.Cancel],
            }),
            anOrderRequestsPerOrderType({
              key: OrderType.Stop,
              values: [OrderRequest.Cancel, OrderRequest.Modify, OrderRequest.Submit],
            }),
            anOrderRequestsPerOrderType({
              key: OrderType.Limit,
              values: [OrderRequest.Cancel, OrderRequest.Modify, OrderRequest.Submit],
            }),
          ],
        }),
      }),
    }),
  ],
};

export const venueCapabilitiesWithoutCashOrders = {
  venueCapabilities: [
    aVenueCapabilitiesResponse({
      venue: 'bitmex',
      capabilities: aDetailedCapabilities({
        tradingCapabilities: aTradingCapabilities({
          supportedOrderTypes: [
            OrderType.Market,
            OrderType.Limit,
            OrderType.Stop,
            OrderType.StopLimit,
          ],
          supportedTIFs:
            venueCapabilitiesWithCashOrders.venueCapabilities[0].capabilities?.tradingCapabilities
              ?.supportedTIFs,
          supportedOrderRequests:
            venueCapabilitiesWithCashOrders.venueCapabilities[0].capabilities?.tradingCapabilities
              ?.supportedOrderRequests,
        }),
      }),
    }),
  ],
};

export const positionsQuery = {
  positions: aPositionConnection({
    edges: [
      aPositionEdge({
        node: aPositionResponse({
          __typename: 'PositionResponse',
          account: 'bank',
          currency: 'USD',
          grossAveragePrice: null,
          grossCost: 1500,
          grossCostSc: null,
          grossRealizedPnl: 1200,
          grossRealizedPnlSc: null,
          grossUnrealizedPnl: null,
          grossUnrealizedPnlSc: null,
          symbol: 'BTC',
          marketValue: null,
          marketValueSc: null,
          netAveragePrice: null,
          netCost: 1550,
          netCostSc: null,
          netRealizedPnl: 1200,
          netRealizedPnlSc: null,
          netUnrealizedPnl: null,
          netUnrealizedPnlSc: null,
          notionalQuantity: null,
          quantity: 10,
        }),
      }),
      aPositionEdge({
        node: aPositionResponse({
          __typename: 'PositionResponse',
          account: 'bank',
          currency: 'BTC',
          grossAveragePrice: null,
          grossCost: 95000,
          grossCostSc: null,
          grossRealizedPnl: 0,
          grossRealizedPnlSc: null,
          grossUnrealizedPnl: null,
          grossUnrealizedPnlSc: null,
          symbol: 'USD',
          marketValue: null,
          marketValueSc: null,
          netAveragePrice: null,
          netCost: 95000,
          netCostSc: null,
          netRealizedPnl: 0,
          netRealizedPnlSc: null,
          netUnrealizedPnl: null,
          netUnrealizedPnlSc: null,
          notionalQuantity: null,
          quantity: 100000,
        }),
      }),
    ],
    pageInfo: aPageInfo({
      hasNextPage: false,
    }),
  }),
};

export const positionsQueryWithCorrectFilters = {
  positions: aPositionConnection({
    edges: [
      aPositionEdge({
        node: aPositionResponse({
          __typename: 'PositionResponse',
          account: 'bank',
          currency: 'Correct filters',
          grossAveragePrice: null,
          grossCost: 1500,
          grossCostSc: null,
          grossRealizedPnl: 1200,
          grossRealizedPnlSc: null,
          grossUnrealizedPnl: null,
          grossUnrealizedPnlSc: null,
          symbol: 'BTC',
          marketValue: null,
          marketValueSc: null,
          netAveragePrice: null,
          netCost: 1550,
          netCostSc: null,
          netRealizedPnl: 1200,
          netRealizedPnlSc: null,
          netUnrealizedPnl: null,
          netUnrealizedPnlSc: null,
          notionalQuantity: null,
          quantity: 10,
        }),
      }),
    ],
    pageInfo: aPageInfo({
      hasNextPage: false,
    }),
  }),
};

const timeline = {
  timeline: aTimelineConnection({
    edges: [
      aTimelineEdge({
        node: aTimelineResult({
          type: TimelineEventType.OrderState,
          data: anOrderStateResponse({
            orderId: '083a440d-2af4-42e3-8aa7-061878a6bb77',
            clientId: 'joseph_the_banker',
            clOrderId: '',
            currency: 'BTC',
            expirationDateTime: null,
            origClOrderId: '',
            portfolioId: 'BANK_Portfolio',
            portfolioName: 'BANK Portfolio1',
            counterPortfolioId: '',
            counterPortfolioName: '',
            orderStatus: OrderStatus.PendingNew,
            orderQty: 0.01,
            limitPrice: null,
            stopPrice: null,
            tif: Tif.Gtc,
            filledQty: 0.0,
            remainingQty: 0.01,
            lastQty: 0.0,
            avgPrice: '0',
            lastPrice: '0',
            reason: '',
            lastRequestResult: '',
            side: Side.Buy,
            instrument: anInstrumentResponse({
              baseInstrument: aBaseInstrumentResponse({
                assetClass: AssetClass.Forex,
                symbol: 'BTCUSDT',
                description: '',
                quoteCurrency: 'USDT',
                inverseContract: false,
                venueType: VenueType.Street,
                venueName: 'Generic',
                __typename: 'BaseInstrumentResponse',
              }),
              forexSpotProperties: aForexSpotPropertiesResponse({
                baseCurrency: 'BTC',
                __typename: 'ForexSpotPropertiesResponse',
              }),
              tradingConstraints: aTradingConstraintsResponse({
                minQty: '',
                maxQty: '',
                qtyIncr: '1E-8',
                minPrice: '',
                maxPrice: '',
                priceIncr: '1E-7',
                minNotional: '',
                contractSize: '1',
                tradeable: true,
                __typename: 'TradingConstraintsResponse',
              }),
              instrumentIdentifiers: anInstrumentIdentifiersResponse({
                adapterTicker: 'KRAKEN_SPOT_BTC_USDT',
                instrumentId: 'BTCUSDT@FOREX@Generic',
                tradingViewId: '',
                venueTradingViewId: '',
                __typename: 'InstrumentIdentifiersResponse',
              }),
              archivedAt: null,
              createdAt: '2024-08-09T15:43:24.059254Z',
              __typename: 'InstrumentResponse',
            }),
            symbol: 'BTCUSDT',
            assetClass: AssetClass.Forex,
            venue: 'Generic',
            venueTimestamp: null,
            createdAt: '*************',
            updatedAt: '*************',
            orderCategory: OrderCategory.DirectMarketAccessOrder,
            parentOrderId: 'e69d7806-e049-4b7e-b85c-9f119610abbb',
            orderStateId: '15acc574-b758-4e41-8ffd-0ecc8f8a6098',
            venueAccountDescs: [
              aVenueAccountDesc({
                id: 'simulator',
                name: 'simulator',
                __typename: 'VenueAccountDesc',
              }),
            ],
            __typename: 'OrderStateResponse',
          }),
        }),
      }),
      aTimelineEdge({
        node: aTimelineResult({
          type: TimelineEventType.OrderState,
          data: anOrderStateResponse({
            orderId: '083a440d-2af4-42e3-8aa7-061878a6bb77',
            clientId: 'joseph_the_banker',
            clOrderId: '',
            currency: 'BTC',
            expirationDateTime: null,
            origClOrderId: '',
            portfolioId: 'BANK_Portfolio',
            portfolioName: 'BANK Portfolio1',
            counterPortfolioId: '',
            counterPortfolioName: '',
            orderStatus: OrderStatus.PendingNew,
            orderQty: 0.01,
            limitPrice: null,
            stopPrice: null,
            tif: Tif.Gtc,
            filledQty: 0.0,
            remainingQty: 0.01,
            lastQty: 0.0,
            avgPrice: '0',
            lastPrice: '0',
            reason: '',
            lastRequestResult: '',
            side: Side.Buy,
            instrument: anInstrumentResponse({
              baseInstrument: aBaseInstrumentResponse({
                assetClass: AssetClass.Forex,
                symbol: 'BTCUSDT',
                description: '',
                quoteCurrency: 'USDT',
                inverseContract: false,
                venueType: VenueType.Street,
                venueName: 'Generic',
                __typename: 'BaseInstrumentResponse',
              }),
              forexSpotProperties: aForexSpotPropertiesResponse({
                baseCurrency: 'BTC',
                __typename: 'ForexSpotPropertiesResponse',
              }),
              tradingConstraints: aTradingConstraintsResponse({
                minQty: '',
                maxQty: '',
                qtyIncr: '1E-8',
                minPrice: '',
                maxPrice: '',
                priceIncr: '1E-7',
                minNotional: '',
                contractSize: '1',
                tradeable: true,
                __typename: 'TradingConstraintsResponse',
              }),
              instrumentIdentifiers: anInstrumentIdentifiersResponse({
                adapterTicker: 'KRAKEN_SPOT_BTC_USDT',
                instrumentId: 'BTCUSDT@FOREX@Generic',
                tradingViewId: '',
                venueTradingViewId: '',
                __typename: 'InstrumentIdentifiersResponse',
              }),
              archivedAt: null,
              createdAt: '2024-08-09T15:43:24.059254Z',
              __typename: 'InstrumentResponse',
            }),
            symbol: 'BTCUSDT',
            assetClass: AssetClass.Forex,
            venue: 'Generic',
            venueTimestamp: null,
            createdAt: '*************',
            updatedAt: '*************',
            orderCategory: OrderCategory.SorChildOrder,
            parentOrderId: 'e69d7806-e049-4b7e-b85c-9f119610abbb',
            orderStateId: '15acc574-b758-4e41-8ffd-0ecc8f8a6098',
            venueAccountDescs: [
              aVenueAccountDesc({
                id: 'simulator',
                name: 'simulator',
                __typename: 'VenueAccountDesc',
              }),
            ],
            __typename: 'OrderStateResponse',
          }),
        }),
      }),
      aTimelineEdge({
        node: aTimelineResult({
          type: TimelineEventType.OrderState,
          data: anOrderStateResponse({
            orderId: '083a440d-2af4-42e3-8aa7-061878a6bb77',
            clientId: 'joseph_the_banker',
            clOrderId: '',
            currency: 'BTC',
            expirationDateTime: null,
            origClOrderId: '',
            portfolioId: 'BANK_Portfolio',
            portfolioName: 'BANK Portfolio1',
            counterPortfolioId: '',
            counterPortfolioName: '',
            orderStatus: OrderStatus.New,
            orderQty: 0.01,
            limitPrice: null,
            stopPrice: null,
            tif: Tif.Gtc,
            filledQty: 0.0,
            remainingQty: 0.01,
            lastQty: 0.0,
            avgPrice: '0',
            lastPrice: '0',
            reason: '',
            lastRequestResult: 'SUCCESS',
            side: Side.Buy,
            instrument: anInstrumentResponse({
              baseInstrument: aBaseInstrumentResponse({
                assetClass: AssetClass.Forex,
                symbol: 'BTCUSDT',
                description: '',
                quoteCurrency: 'USDT',
                inverseContract: false,
                venueType: VenueType.Street,
                venueName: 'Generic',
                __typename: 'BaseInstrumentResponse',
              }),
              forexSpotProperties: aForexSpotPropertiesResponse({
                baseCurrency: 'BTC',
                __typename: 'ForexSpotPropertiesResponse',
              }),
              tradingConstraints: aTradingConstraintsResponse({
                minQty: '',
                maxQty: '',
                qtyIncr: '1E-8',
                minPrice: '',
                maxPrice: '',
                priceIncr: '1E-7',
                minNotional: '',
                contractSize: '1',
                tradeable: true,
                __typename: 'TradingConstraintsResponse',
              }),
              instrumentIdentifiers: anInstrumentIdentifiersResponse({
                adapterTicker: 'KRAKEN_SPOT_BTC_USDT',
                instrumentId: 'BTCUSDT@FOREX@Generic',
                tradingViewId: '',
                venueTradingViewId: '',
                __typename: 'InstrumentIdentifiersResponse',
              }),
              archivedAt: null,
              createdAt: '2024-08-09T15:43:24.059254Z',
              __typename: 'InstrumentResponse',
            }),
            symbol: 'BTCUSDT',
            assetClass: AssetClass.Forex,
            venue: 'Generic',
            venueTimestamp: '*************',
            createdAt: '*************',
            updatedAt: '*************',
            orderCategory: OrderCategory.DirectMarketAccessOrder,
            parentOrderId: 'e69d7806-e049-4b7e-b85c-9f119610abbb',
            orderStateId: '9c754dba-5622-4fce-9e71-0baa88910191',
            venueAccountDescs: [
              aVenueAccountDesc({
                id: 'simulator',
                name: 'simulator',
                __typename: 'VenueAccountDesc',
              }),
            ],
            __typename: 'OrderStateResponse',
          }),
        }),
      }),
      aTimelineEdge({
        node: aTimelineResult({
          type: TimelineEventType.OrderState,
          data: anOrderStateResponse({
            orderId: '083a440d-2af4-42e3-8aa7-061878a6bb77',
            clientId: 'joseph_the_banker',
            clOrderId: '',
            currency: 'BTC',
            expirationDateTime: null,
            origClOrderId: '',
            portfolioId: 'BANK_Portfolio',
            portfolioName: 'BANK Portfolio1',
            counterPortfolioId: '',
            counterPortfolioName: '',
            orderStatus: OrderStatus.Filled,
            orderQty: 0.01,
            limitPrice: null,
            stopPrice: null,
            tif: Tif.Gtc,
            filledQty: 0.01,
            remainingQty: 0.0,
            lastQty: 0.01,
            avgPrice: '68330.0',
            lastPrice: '68330.0',
            reason: '',
            lastRequestResult: 'SUCCESS',
            side: Side.Buy,
            instrument: anInstrumentResponse({
              baseInstrument: aBaseInstrumentResponse({
                assetClass: AssetClass.Forex,
                symbol: 'BTCUSDT',
                description: '',
                quoteCurrency: 'USDT',
                inverseContract: false,
                venueType: VenueType.Street,
                venueName: 'Generic',
                __typename: 'BaseInstrumentResponse',
              }),
              forexSpotProperties: aForexSpotPropertiesResponse({
                baseCurrency: 'BTC',
                __typename: 'ForexSpotPropertiesResponse',
              }),
              tradingConstraints: aTradingConstraintsResponse({
                minQty: '',
                maxQty: '',
                qtyIncr: '1E-8',
                minPrice: '',
                maxPrice: '',
                priceIncr: '1E-7',
                minNotional: '',
                contractSize: '1',
                tradeable: true,
                __typename: 'TradingConstraintsResponse',
              }),
              instrumentIdentifiers: anInstrumentIdentifiersResponse({
                adapterTicker: 'KRAKEN_SPOT_BTC_USDT',
                instrumentId: 'BTCUSDT@FOREX@Generic',
                tradingViewId: '',
                venueTradingViewId: '',
                __typename: 'InstrumentIdentifiersResponse',
              }),
              archivedAt: null,
              createdAt: '2024-08-09T15:43:24.059254Z',
              __typename: 'InstrumentResponse',
            }),
            symbol: 'BTCUSDT',
            assetClass: AssetClass.Forex,
            venue: 'Generic',
            venueTimestamp: '*************',
            createdAt: '*************',
            updatedAt: '*************',
            orderCategory: OrderCategory.DirectMarketAccessOrder,
            parentOrderId: 'e69d7806-e049-4b7e-b85c-9f119610abbb',
            orderStateId: 'fe80213e-b25c-42da-bb72-bd8ba8d02944',
            venueAccountDescs: [
              aVenueAccountDesc({
                id: 'simulator',
                name: 'simulator',
                __typename: 'VenueAccountDesc',
              }),
            ],
            __typename: 'OrderStateResponse',
          }),
        }),
      }),
      aTimelineEdge({
        node: aTimelineResult({
          type: TimelineEventType.MatchResponse,
          data: aMatchResponse({
            timestamp: '*************',
            makers: [
              aCounterparty({
                portfolioId: 'Denis_Bank_Portfolio',
                portfolioName: null,
                venueAccount: 'simulator',
                venueAccountName: 'simulator',
                referenceType: ParticipantReferenceType.VenueAccount,
                orderId: '4c44868f-f17e-45ad-9367-fff501911112',
                price: 61840.7,
                volume: 0.001,
                __typename: 'Counterparty',
              }),
            ],
            taker: aCounterparty({
              portfolioId: 'RetailCLOB',
              portfolioName: 'RetailCLOB',
              venueAccount: null,
              venueAccountName: null,
              referenceType: ParticipantReferenceType.Portfolio,
              orderId: '8ff48f67-d7b1-4c5f-ae1d-196d47116266',
              price: 61840.7,
              volume: 0.001,
              __typename: 'Counterparty',
            }),
            primarySymbolQuotes: [
              aMarketQuote({
                instrument: anInstrumentResponse({
                  baseInstrument: aBaseInstrumentResponse({
                    symbol: 'BTCUSD',
                    venueName: 'Generic',
                    assetClass: AssetClass.Forex,
                    venueType: VenueType.Street,
                    quoteCurrency: 'USD',
                    __typename: 'BaseInstrumentResponse',
                  }),
                  tradingConstraints: {},
                  instrumentIdentifiers: anInstrumentIdentifiersResponse({
                    instrumentId: 'BTCUSD@FOREX@Generic',
                    __typename: 'InstrumentIdentifiersResponse',
                  }),
                  forexSpotProperties: aForexSpotPropertiesResponse({
                    baseCurrency: 'BTC',
                    __typename: 'ForexSpotPropertiesResponse',
                  }),
                  __typename: 'InstrumentResponse',
                }),
                marketAskPrice: 61840.7,
                marketBidPrice: 61840.6,
                marketAskSize: 0.83,
                marketBidSize: 4.38161965,
                markupAskPrice: null,
                markupBidPrice: null,
                timestamp: '2024-10-11T15:04:08.253965Z',
                __typename: 'MarketQuote',
              }),
            ],
            secondarySymbolQuotes: [],
            __typename: 'MatchResponse',
          }),
        }),
      }),
      aTimelineEdge({
        node: {
          type: TimelineEventType.HedgeResult,
          data: aHedgeResult({
            hedgeOrderId: '8dhw74hssj',
            hedgeOrderQuantity: 1,
            // price: 63953.22,
            // priceTc: 1791969.22,
            hedgeOrderAmount: 63953.22,
            hedgeOrderAmountInClobQuoteCurrency: 1791969.22,
            hedgingVenue: 'Kraken',
            hedgeOrderSide: Side.Buy,
            primarySymbolQuote: aMarketQuote({
              marketAskPrice: 63953.22,
              // marketAskSize: 2,
              marketBidPrice: 63954.44,
              // marketBidSize: 3,
              markupAskPrice: 63955.55,
              markupBidPrice: 63957.77,
              instrument: anInstrumentResponse({
                baseInstrument: aBaseInstrumentResponse({ quoteCurrency: 'USD' }),
                tradingConstraints: aTradingConstraintsResponse({ priceIncr: '0.0001' }),
              }),
            }),
            secondarySymbolQuote: aMarketQuote({
              marketAskPrice: 63953.22,
              // marketAskSize: 2,
              marketBidPrice: 63954.44,
              // marketBidSize: 3,
              markupAskPrice: 63955.55,
              markupBidPrice: 63957.77,
              instrument: anInstrumentResponse({
                baseInstrument: aBaseInstrumentResponse({ quoteCurrency: 'TRY' }),
              }),
            }),
            breakEvenFxRate: 28.15,
            // topOfBookPrice: 63912.31,
            estimatedPrice: 63943,
            executionPrice: 63953.22,
            hedgeOrderLimitPrice: 64882.23,
          }),
        },
      }),
    ],
    pageInfo: aPageInfo({}),
  }),
};

export const graphQLHandlers = [
  graphql.query('Watchlist', (req, res, ctx) => {
    return res(
      ctx.data({
        watchlist: [
          {
            __typename: 'WatchlistResponse',
            id: '1',
            instrumentId: 'SHIBUSD',
            venueAccount: 'Bitfinex',
          },
          {
            __typename: 'WatchlistResponse',
            id: '2',
            instrumentId: 'SHIBUSD',
            venueAccount: 'BitMEX',
          },
        ],
      }),
    );
  }),

  graphql.query('WalletAccountSearch', (req, res, ctx) => {
    return res(
      ctx.data({
        walletAccountSearch: aWalletAccountConnection({
          edges: [
            aWalletAccountEdge({
              node: aWalletAccountResponse({
                id: 'Bank',
                name: 'Bank',
                scopes: [Scope.Read, Scope.Manage],
                dynamicScopes: [],
              }),
            }),
            aWalletAccountEdge({
              node: aWalletAccountResponse({
                id: 'Bank2',
                name: 'Bank2',
                scopes: [Scope.Read, Scope.Create],
                dynamicScopes: [],
              }),
            }),
          ],
          pageInfo: aPageInfo({
            endCursor: 'Bank2',
            hasNextPage: false,
            __typename: 'PageInfo',
          }),
        }),
      }),
    );
  }),
  graphql.query('CurrencySearch', (req, res, ctx) => {
    return res(
      ctx.data({
        currencySearch: [
          aCurrencyResponse({
            symbol: 'USD',
            precision: 2,
          }),
          aCurrencyResponse({
            symbol: 'BTC',
            precision: 8,
          }),
          aCurrencyResponse({
            symbol: 'ETH',
            precision: 8,
          }),
          aCurrencyResponse({
            symbol: 'EUR',
            precision: 2,
          }),
        ],
      }),
    );
  }),
  graphql.query('Positions', (req, res, ctx) => {
    return res(ctx.status(200), ctx.data(positionsQuery));
  }),

  graphql.query('InstrumentSearch', (req, res, ctx) => {
    return res(
      ctx.data({
        instrumentSearch: anInstrumentsConnection({
          pageInfo: aPageInfo({
            hasNextPage: false,
            endCursor: '',
          }),
          edges: [
            anInstrumentEdge({
              node: anInstrumentResponse({
                baseInstrument: aBaseInstrumentResponse({
                  assetClass: AssetClass.Forex,
                  symbol: 'BMEXUSDT',
                  description: '',
                  quoteCurrency: 'USDT',
                  inverseContract: false,
                  venueType: VenueType.Client,
                  venueName: 'Bank',
                  __typename: 'BaseInstrumentResponse',
                }),
                forexSpotProperties: aForexSpotPropertiesResponse({
                  baseCurrency: 'BMEX',
                  __typename: 'ForexSpotPropertiesResponse',
                }),
                tradingConstraints: aTradingConstraintsResponse({
                  minQty: '0',
                  maxQty: '9999999',
                  qtyIncr: '',
                  minPrice: '',
                  maxPrice: '',
                  priceIncr: '',
                  minNotional: '',
                  contractSize: '',
                  tradeable: true,
                  __typename: 'TradingConstraintsResponse',
                }),
                instrumentIdentifiers: anInstrumentIdentifiersResponse({
                  adapterTicker: 'BMEXUSDT',
                  instrumentId: 'BMEXUSDT@FOREX@Bank',
                  tradingViewId: '',
                  venueTradingViewId: '',
                  __typename: 'InstrumentIdentifiersResponse',
                }),
                archivedAt: null,
                createdAt: '2024-04-17T06:56:32.475137Z',
                __typename: 'InstrumentResponse',
              }),
              cursor: 'BMEXUSDT@FOREX@Bank',
              __typename: 'InstrumentEdge',
            }),
            anInstrumentEdge({
              node: anInstrumentResponse({
                baseInstrument: aBaseInstrumentResponse({
                  assetClass: AssetClass.Forex,
                  description: 'ADAUSD@WydenMock',
                  symbol: 'ADAUSD',
                  quoteCurrency: 'USD',
                  inverseContract: true,
                  venueType: VenueType.Street,
                  venueName: 'WydenMock',
                  __typename: 'BaseInstrumentResponse',
                }),
                forexSpotProperties: aForexSpotPropertiesResponse({
                  baseCurrency: 'ADA',
                  __typename: 'ForexSpotPropertiesResponse',
                }),
                tradingConstraints: aTradingConstraintsResponse({
                  ...aTradingConstraintsResponse(),
                  priceIncr: '1',
                  qtyIncr: '1',
                  minQty: null,
                  maxQty: null,
                  tradeable: true,
                  __typename: 'TradingConstraintsResponse',
                }),
                instrumentIdentifiers: anInstrumentIdentifiersResponse({
                  ...anInstrumentIdentifiersResponse(),
                  adapterTicker: 'ADAUSD',
                  instrumentId: 'ADAUSD@FOREX@WydenMock',
                  __typename: 'InstrumentIdentifiersResponse',
                }),
                archivedAt: null,
                __typename: 'InstrumentResponse',
              }),
              cursor: '1',
            }),
            anInstrumentEdge({
              node: anInstrumentResponse({
                baseInstrument: aBaseInstrumentResponse({
                  assetClass: AssetClass.Forex,
                  description: null,
                  symbol: 'BTCUSD',
                  quoteCurrency: 'USD',
                  inverseContract: false,
                  venueType: VenueType.Street,
                  venueName: 'BitMEX',
                  __typename: 'BaseInstrumentResponse',
                }),
                forexSpotProperties: aForexSpotPropertiesResponse({
                  baseCurrency: 'BTC',
                  __typename: 'ForexSpotPropertiesResponse',
                }),
                tradingConstraints: aTradingConstraintsResponse({
                  ...aTradingConstraintsResponse(),
                  priceIncr: '1',
                  qtyIncr: '1',
                  tradeable: true,
                  minQty: '0',
                  maxQty: '9999999',
                  __typename: 'TradingConstraintsResponse',
                }),
                instrumentIdentifiers: anInstrumentIdentifiersResponse({
                  ...anInstrumentIdentifiersResponse(),
                  adapterTicker: 'btcusd',
                  instrumentId: 'BTCUSD@FOREX@BitMEX',
                  __typename: 'InstrumentIdentifiersResponse',
                }),
                archivedAt: null,
                __typename: 'InstrumentResponse',
              }),
              cursor: '2',
            }),
            anInstrumentEdge({
              node: anInstrumentResponse({
                baseInstrument: aBaseInstrumentResponse({
                  assetClass: AssetClass.Forex,
                  description: null,
                  symbol: 'BTCUSDT',
                  quoteCurrency: 'USDT',
                  inverseContract: false,
                  venueType: VenueType.Street,
                  venueName: 'BitMEX',
                  __typename: 'BaseInstrumentResponse',
                }),
                forexSpotProperties: aForexSpotPropertiesResponse({
                  baseCurrency: 'BTC',
                  __typename: 'ForexSpotPropertiesResponse',
                }),
                tradingConstraints: aTradingConstraintsResponse({
                  priceIncr: '1',
                  qtyIncr: '1',
                  tradeable: true,
                  minQty: '0',
                  maxQty: '9999999',
                  __typename: 'TradingConstraintsResponse',
                }),
                instrumentIdentifiers: anInstrumentIdentifiersResponse({
                  ...anInstrumentIdentifiersResponse(),
                  adapterTicker: 'BTCUSDT',
                  instrumentId: 'BTCUSDT@FOREX@BitMEX',
                  __typename: 'InstrumentIdentifiersResponse',
                }),
                archivedAt: null,
                __typename: 'InstrumentResponse',
              }),
              cursor: '3',
            }),
            anInstrumentEdge({
              node: anInstrumentResponse({
                baseInstrument: aBaseInstrumentResponse({
                  assetClass: AssetClass.Forex,
                  description: 'ETHUSD@WydenMock',
                  symbol: 'ETHUSD',
                  quoteCurrency: 'USD',
                  inverseContract: true,
                  venueType: VenueType.Street,
                  venueName: 'WydenMock',
                  __typename: 'BaseInstrumentResponse',
                }),
                forexSpotProperties: aForexSpotPropertiesResponse({
                  baseCurrency: 'ETH',
                  __typename: 'ForexSpotPropertiesResponse',
                }),
                tradingConstraints: aTradingConstraintsResponse({
                  tradeable: true,
                  minQty: null,
                  maxQty: null,
                  __typename: 'TradingConstraintsResponse',
                }),
                instrumentIdentifiers: anInstrumentIdentifiersResponse({
                  ...anInstrumentIdentifiersResponse(),
                  adapterTicker: 'ETHUSD',
                  instrumentId: 'ETHUSD@FOREX@WydenMock',
                  __typename: 'InstrumentIdentifiersResponse',
                }),
                archivedAt: null,
                __typename: 'InstrumentResponse',
              }),
              cursor: '4',
            }),
            anInstrumentEdge({
              node: anInstrumentResponse({
                baseInstrument: aBaseInstrumentResponse({
                  assetClass: AssetClass.Forex,
                  description: 'BTCUSD@WydenMock',
                  symbol: 'BTCUSD',
                  quoteCurrency: 'USD',
                  inverseContract: true,
                  venueType: VenueType.Street,
                  venueName: 'WydenMock',
                  __typename: 'BaseInstrumentResponse',
                }),
                forexSpotProperties: aForexSpotPropertiesResponse({
                  baseCurrency: 'BTC',
                  __typename: 'ForexSpotPropertiesResponse',
                }),
                tradingConstraints: aTradingConstraintsResponse({
                  ...aTradingConstraintsResponse(),
                  tradeable: true,
                  minQty: null,
                  maxQty: null,
                  __typename: 'TradingConstraintsResponse',
                }),
                instrumentIdentifiers: anInstrumentIdentifiersResponse({
                  ...anInstrumentIdentifiersResponse(),
                  adapterTicker: 'BTCUSD',
                  instrumentId: 'BTCUSD@FOREX@WydenMock',
                  __typename: 'InstrumentIdentifiersResponse',
                }),
                __typename: 'InstrumentResponse',
                archivedAt: null,
              }),
              cursor: '5',
            }),
          ],
        }),
      }),
    );
  }),

  graphql.query('SymbolSearch', (req, res, ctx) => {
    return res(
      ctx.data({
        symbolSearch: aSymbolSearchConnection({
          pageInfo: aPageInfo({
            hasNextPage: false,
            endCursor: '',
          }),
          edges: [
            aSymbolSearchEdge({
              node: aSymbolSearchResponse({
                symbol: 'BMEXUSDT',
                assetClass: AssetClass.Forex,
                venues: [],
              }),
              cursor: 'BMEXUSDT@FOREX@Bank',
            }),
            aSymbolSearchEdge({
              node: aSymbolSearchResponse({
                symbol: 'ADAUSD',
                assetClass: AssetClass.Forex,
                venues: [],
              }),
              cursor: 'ADAUSD@FOREX@WydenMock',
            }),
            aSymbolSearchEdge({
              node: aSymbolSearchResponse({
                symbol: 'BTCUSD',
                assetClass: AssetClass.Forex,
                venues: [],
              }),
              cursor: 'BTCUSD@FOREX@BitMEX',
            }),
            aSymbolSearchEdge({
              node: aSymbolSearchResponse({
                symbol: 'BTCUSDT',
                assetClass: AssetClass.Forex,
                venues: [],
              }),
              cursor: 'BTCUSDT@FOREX@BitMEX',
            }),
            aSymbolSearchEdge({
              node: aSymbolSearchResponse({
                symbol: 'ETHUSD',
                assetClass: AssetClass.Forex,
                venues: [],
              }),
              cursor: 'ETHUSD@FOREX@BitMEX',
            }),
          ],
        }),
      }),
    );
  }),

  graphql.query('VenueAccounts', (req, res, ctx) => {
    return res(
      ctx.data({
        venueAccounts: [
          aVenueAccountsPerVenue({
            venue: 'BitMEX',
            venueAccounts: [
              aVenueAccount({
                venueAccountName: 'Bitmex1',
                venueAccountId: 'Bitmex1',
                scopes: [Scope.Manage, Scope.Trade, Scope.Read],
                dynamicScopes: [],
                archivedAt: null,
                deactivatedAt: undefined,
              }),
              aVenueAccount({
                venueAccountName: 'Bitmex2',
                venueAccountId: 'Bitmex2',
                scopes: [],
                dynamicScopes: [],
                archivedAt: null,
                deactivatedAt: undefined,
              }),
              aVenueAccount({
                venueAccountName: 'BitMEX-testnet1',
                venueAccountId: 'BitMEX-testnet1',
                scopes: [Scope.Trade],
                dynamicScopes: [],
                archivedAt: null,
                deactivatedAt: undefined,
              }),
            ],
            __typename: 'VenueAccountsPerVenue',
          }),
          aVenueAccountsPerVenue({
            venue: 'SkrilLEX',
            venueAccounts: [
              aVenueAccount({
                venueAccountName: 'Wasabi',
                venueAccountId: 'Wasabi',
                deactivatedAt: undefined,
                archivedAt: null,
              }),
              aVenueAccount({
                venueAccountName: 'Otomaki',
                venueAccountId: 'Otomaki',
                archivedAt: null,
                deactivatedAt: undefined,
              }),
            ],
            __typename: 'VenueAccountsPerVenue',
          }),
          aVenueAccountsPerVenue({
            venue: 'WydenMock',
            venueAccounts: [
              aVenueAccount({
                venueAccountName: 'WydenMockAccount',
                venueAccountId: 'WydenMockAccount',
                scopes: [Scope.Trade],
                dynamicScopes: [],
                deactivatedAt: undefined,
                archivedAt: null,
              }),
            ],
            __typename: 'VenueAccountsPerVenue',
          }),
          aVenueAccountsPerVenue({
            venue: 'WydenMock',
            venueAccounts: [
              aVenueAccount({
                venueAccountName: 'WydenMockAccountName',
                venueAccountId: 'accountID',
                scopes: [Scope.Trade],
                dynamicScopes: [],
                deactivatedAt: undefined,
                archivedAt: null,
              }),
            ],
            __typename: 'VenueAccountsPerVenue',
          }),
        ],
      }),
    );
  }),

  graphql.query('ConnectorList', (req, res, ctx) => {
    return res(
      ctx.data({
        connectorList: [
          aConnectorResponse({
            connectorId: 'connector1',
            connectorName: 'Bitmex Connector',
            venue: 'BitMEX',
            deactivatedAtDateTime: null,
          }),
        ],
      }),
    );
  }),

  graphql.query('ConnectorTypeList', (req, res, ctx) => {
    return res(
      ctx.data({
        connectorTypeList: ['BitMEX', 'WydenMock'],
      }),
    );
  }),

  graphql.query('StaticPermissions', (req, res, ctx) => {
    return res(
      ctx.data({
        groupStaticPermissions: [
          aPermission({
            resource: Resource.VenueAccount,
            scope: Scope.Create,
            resourceId: null,
          }),
          aPermission({
            resource: Resource.Connector,
            scope: Scope.Create,
            resourceId: null,
          }),
          aPermission({
            resource: Resource.Portfolio,
            scope: Scope.Create,
            resourceId: null,
          }),
          aPermission({
            resource: Resource.BrokerConfig,
            scope: Scope.Manage,
            resourceId: null,
          }),
          aPermission({
            resource: Resource.BrokerConfig,
            scope: Scope.Read,
            resourceId: null,
          }),
          aPermission({
            resource: Resource.Risk,
            scope: Scope.Manage,
            resourceId: null,
          }),
          aPermission({
            resource: Resource.ApiKey,
            scope: Scope.Manage,
            resourceId: null,
          }),
          aPermission({
            resource: Resource.ApiKey,
            scope: Scope.Create,
            resourceId: null,
          }),
          aPermission({
            resource: Resource.Risk,
            scope: Scope.Read,
            resourceId: null,
          }),
        ],
        userStaticPermissions: [],
      }),
    );
  }),

  graphql.query('Instruments', (req, res, ctx) => {
    return res(
      ctx.data({
        instruments: [],
      }),
    );
  }),

  graphql.query('PortfolioSearch', (req, res, ctx) => {
    return res(
      ctx.data({
        portfolioSearch: aPortfolioConnection({
          edges: [
            aPortfolioEdge({
              node: aPortfolioResponse({
                id: 'portfolio_trader_1',
                name: 'portfolio_trader_1',
                scopes: [Scope.Manage],
                archivedAt: null,
              }),
            }),
            aPortfolioEdge({
              node: aPortfolioResponse({
                id: 'portfolio_trader_2',
                name: 'portfolio_trader_2',
                archivedAt: null,
              }),
            }),
            aPortfolioEdge({
              node: aPortfolioResponse({
                id: 'portfolio_trader_3',
                name: 'portfolio_trader_3',
                archivedAt: null,
              }),
            }),
            aPortfolioEdge({
              node: aPortfolioResponse({
                id: 'portfolio_trader_4',
                name: 'portfolio_trader_4',
                archivedAt: null,
              }),
            }),
            aPortfolioEdge({
              node: aPortfolioResponse({
                id: 'BANK_Portfolio',
                name: 'BANK_Portfolio',
                createdAt: '2024-04-01T05:03:45.379242Z',
                scopes: [Scope.Manage, Scope.Trade, Scope.Read],
                dynamicScopes: [],
                portfolioCurrency: 'USD',
                portfolioType: PortfolioType.Nostro,
                tags: [],
                archivedAt: null,
                __typename: 'PortfolioResponse',
              }),
              cursor: '2024-04-01T05:03:45.379242Z',
              __typename: 'PortfolioEdge',
            }),
          ],
        }),
      }),
    );
  }),
  graphql.mutation('CreateInstrument', (req, res, ctx) => {
    return res(ctx.data({ createInstrument: aCreateInstrumentResult() }));
  }),
  graphql.query('Venues', (req, res, ctx) => {
    return res(
      ctx.data({
        venues: [
          aVenue({ name: 'Test Venue', venueType: VenueType.Client }),
          aVenue({ name: 'BitMEX', venueType: VenueType.Street }),
          aVenue({ name: 'SkrilLEX', venueType: VenueType.Street }),
          aVenue({ name: 'Wyden Exchange', venueType: VenueType.Clob }),
        ],
      }),
    );
  }),
  graphql.query('OrderStates', (req, res, ctx) => {
    return res(
      ctx.data({
        currentOrders: [],
      }),
    );
  }),
  graphql.query('OrderStatesWithPredicates', (req, res, ctx) => {
    return res(ctx.data({ orderStatesWithPredicates: [] }));
  }),
  graphql.mutation('UpdateUserData', (req, res, ctx) => {
    const { request } = req.variables;
    return res(
      ctx.data({
        updateUserData: request,
      }),
    );
  }),
  graphql.mutation('CreateVenueAccount', (req, res, ctx) => {
    return res(ctx.data({ venueAccounts: aVenueAccountsPerVenue() }));
  }),
  graphql.mutation('ConnectorCreate', (req, res, ctx) => {
    return res(ctx.data({ connectorCreate: aConnectorResponse() }));
  }),
  graphql.query('PortfolioGroupConfigurationList', (req, res, ctx) => {
    return res(
      ctx.data({
        portfolioGroupConfigurationList: [
          aPortfolioGroupConfigurationFlat({
            id: 'group-1',
            name: 'group-1',
            portfolioType: PortfolioType.Nostro,
          }),
          aPortfolioGroupConfigurationFlat({
            id: 'group-2',
            name: 'group-2',
            portfolioType: PortfolioType.Nostro,
          }),
          aPortfolioGroupConfigurationFlat({
            id: 'group-3',
            name: 'group-3',
            portfolioType: PortfolioType.Vostro,
          }),
        ],
      }),
    );
  }),
  graphql.query('UserData', (req, res, ctx) => {
    const userData = {
      timestamp: Date.now(),
      widgets: [
        {
          id: 'test-widget',
          type: 'BASE',
        },
      ],
      workspaces: [
        {
          name: 'current-workspace',
          id: 'current-workspace',
          json: {},
          isTrading: false,
          portfolio: aPortfolioResponse({
            id: 'portfolio_trader_1',
            name: 'portfolio_trader_1',
            scopes: [Scope.Manage, Scope.Trade, Scope.Read],
          }),
        },
      ],
    };
    return res(
      ctx.data({
        userData: { data: JSON.stringify(userData) },
      }),
    );
  }),
  graphql.query('PortfolioTags', (req, res, ctx) => {
    return res(ctx.data({}));
  }),
  graphql.query('PortfolioGroupConfigurationIds', (req, res, ctx) => {
    return res(ctx.data({ portfolioGroupConfigurationIds: ['Crypto Group'] }));
  }),

  graphql.query('VenueCapabilities', (req, res, ctx) => {
    return res(ctx.data(venueCapabilitiesWithCashOrders));
  }),

  graphql.query('SystemCurrency', (req, res, ctx) => {
    return res(
      ctx.data({
        systemCurrency: 'USD',
      }),
    );
  }),

  graphql.query('RateSubscriptions', (req, res, ctx) => {
    return res(
      ctx.data({
        rateSubscriptions: [aRateSubscriptionResponse()],
      }),
    );
  }),

  graphql.query('Timeline', (req, res, ctx) => {
    return res(ctx.data(timeline));
  }),
  graphql.query('Entitlements', (req, res, ctx) => {
    return res(
      ctx.data({
        entitlements: [ENTITLEMENTS['Brokerage Engine'], ENTITLEMENTS['Wyden Exchange']],
      }),
    );
  }),
];
