syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.settlement;

import "io/wyden/published/common/metadata.proto";
import "io/wyden/published/settlement/settlement-leg-state-change.proto";
import "io/wyden/published/settlement/settlement-run-state-change.proto";

message SettlementStateChange {
  io.wyden.published.common.Metadata metadata = 1;
  oneof state_change {
      LegStateChange leg_state_change = 2;
      RunStateChange run_state_change = 3;
  }

}