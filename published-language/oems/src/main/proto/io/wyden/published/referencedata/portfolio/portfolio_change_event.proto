syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.referencedata;

import "io/wyden/published/referencedata/portfolio/portfolio.proto";
import "io/wyden/published/referencedata/portfolio/portfolio_modification_request.proto";

message PortfolioChangeEvent {
  string message_id = 1;
  PortfolioChangeEventType portfolio_change_event_type = 2;
  Portfolio portfolio = 3;
  string reason = 4;
  string owner_client_id = 5;

  // when portfolio is created, grant access to the portfolio to the following groups of users
  repeated Grant grants = 6;

  int64 matching_engine_uid = 7;
}

enum PortfolioChangeEventType {
  PORTFOLIO_CHANGE_EVENT_TYPE_UNSPECIFIED = 0;
  PORTFOLIO_CHANGE_EVENT_TYPE_CREATED = 1;
  PORTFOLIO_CHANGE_EVENT_TYPE_CREATION_FAILED = 2;
  PORTFOLIO_CHANGE_EVENT_TYPE_UPDATED = 3;
  PORTFOLIO_CHANGE_EVENT_TYPE_UPDATE_FAILED = 4;
  PORTFOLIO_CHANGE_EVENT_TYPE_DELETED = 5;
}