# 🏗️ High-Level Architecture

This document provides an overview of the project's technical architecture, tools, conventions, and structure.

---

## 🧱 Core Technologies

- **Build System:** Vite `4.4.9`
- **Frameworks & Languages:** React `18`, TypeScript `5.2`
- **Node Version:** `>=22.0.0`
- **NPM Version:** `>=10.7.0`
- **Entry Point:** `main.tsx`
- **Vite Config:** `vite.config.ts`

---

## 🎨 UI & Styling

- **Component Library:** [MUI](https://mui.com/)
  - Heavily customized based on Figma designs
  - Theming and CSS overrides are handled in `ThemeProvider.tsx`

- **Styling Approach:**
  - Styled-components style syntax using a custom `styled` function exported from `ui/styled.ts`
  - Centralized styling using ThemeProvider and emotion/styled setup

---

## 🗂️ Project Structure

The project is divided into three main parts:

1. **`ui/`**  
   Contains custom and overridden MUI components.

2. **`wyden/`**  
   Houses the core business logic of the application.
   - Structured into features: `wyden/features/*`
   - Flat structure within features for scalability
   - Each feature includes a `__tests__` folder containing Cypress component tests

3. **`nest-react/`**  
   A React wrapper around an internal window management library.

---

## 🔗 API Communication

- **GraphQL Versions:**
  - `graphql`: `16.8.0`
  - `@apollo/client`: `3.11.10`
  - `graphql-ws`: `5.14.0`

- **Apollo Configuration:**
  - Client setup and cache configuration in `apollo-client.ts`

- **Subscriptions:**
  - The app is subscription-heavy
  - Apollo cache is bypassed for subscriptions
  - Custom Apollo client intercepts subscription messages and dispatches them to relevant Zustand stores

---

## 🛠️ Code Generation

- **GraphQL Codegen:** `5.0.0`
  - Used for auto-generating hooks for queries, mutations, and subscriptions
  - All GraphQL operations are stored in a flat structure under `wyden/services/graphql/query`
  - The GraphQL schema is provided via a separate, versioned package: `@algotrader/schema-graphql`, maintained in a dedicated repository and updated with every schema change
  - Code generation is executed via the `npm run codegen` script

---

## 🌐 State Management

- **Zustand** is used as the global state manager
- **User Data Persistence:**
  - `userStore` keeps UI-related state (filters, layout, watchlists, etc.)
  - Data is validated with Zod and persisted to both localStorage and backend
  - Defined in `useUserData.tsx`

---

## 🔐 Authentication

- **Keycloak** is used as the identity and access management system

---

## ⚙️ Runtime Configuration

- Runtime variables such as URIs are dynamically loaded from a public `config.json` file
- This is handled by the `getConfig` function in `rest.ts`

---

## 📅 Date Handling

- **date-fns** is used for all date manipulation and formatting tasks

---

## 🧪 Testing

- **Cypress:** `13.2` with component testing enabled
  - Tests are colocated in `__tests__` folders inside each feature directory
- **Mocking:** [MSW (Mock Service Worker)](https://mswjs.io/) `1.3.1` is used for mocking API responses during testing

---

## 📊 Data Presentation

- **ag-grid-react:** `28.2.1`
  - A custom wrapper called `WydenGrid.tsx` is used to abstract and standardize grid usage across the app

---

## 📝 Forms & Validation

- **Form Handling:**
  - Using `@ts-react/form` `1.83` as a wrapper over `react-hook-form`
- **Validation:**
  - All schemas are defined using **Zod**
  - Zod is also used for runtime object validation (e.g., in userStore)

---

## 🌍 Internationalization (i18n)

- **Library:** `react-i18next` `13.2.2`
- **Translations:** Maintained in a single `translation.json` file (no module splitting)

---

## 🔔 Notifications

- **Library:** [`notistack`](https://github.com/iamhosseindhv/notistack)
- **Wrapper:** Custom logic lives in `useNotification.tsx`

---

## 💯 Big Number Handling

- **big.js** is used for precise mathematical operations on large or decimal numbers

---
