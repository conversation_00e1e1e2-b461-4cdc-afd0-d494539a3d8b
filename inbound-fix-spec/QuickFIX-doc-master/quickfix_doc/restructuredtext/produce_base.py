import os
from rstcloth import rstcloth
from . import produce_pages

def make_restructured_text(spec_name, base_file, data_dict_xml_path, output_path, msgs, comps, fields):
    """Make restructured text documents for Sphinx consumption"""
    data_per_file = dict()
    # Construct base document header
    d = rstcloth.RstCloth()
    d.title("Wyden Infinity Inbound FIX specification " + spec_name)
    d.newline()
    d.content("WARNING: This document and its contents are for the sole use of Wyden's employees, partners and customers. All")
    d.content("the information herein is confidential and cannot be reproduced without the express, written permission of Wyden AG.")
    d.newline()    
    d.h2("Introduction")
    d.newline()
    print("Current working directory: " + os.path.abspath(os.getcwd()))
    #Note: html should be indent with 4 spaces at the begining of each line
    intro_html = open("introduction.html", mode='r')
    intro_content = intro_html.read()
    d.directive(name='raw', arg='html', content=intro_content)
    d.newline()
    # Copy data dictionary as-is into the output path
    with open(data_dict_xml_path, mode='r') as data_dict_file:
        data_per_file[os.path.join(output_path, base_file)] = data_dict_file.read()
    d.h2("Data Dictionary Source")
    d.content("Wyden is using QuickFixJ based FIX engine with a FIX dictionary; it is provided as is for guidance purposes only. "
              "The client is free to use any implementation of FIX engine which conforms with this spec")
    d.newline()
    d.content(rstcloth.RstCloth.role("download", base_file))
    d.newline()
    d.content("If your FIX implementation produces more tags than our data dictionary accepts and you neither want want to change your message format, "
              "nor get rejections, you can extend the configuration of the FIX session (fix-acceptor.cfg) with the parameter AllowUnknownMsgFields=Y")
    d.newline()

    # Categorize all the messages
    msg_with_categories = dict()

    for msg_name in msgs:
        # Categorize the message
        msgtype = msgs[msg_name]['category']
        if not msgtype in msg_with_categories:
            msg_with_categories[msgtype] = list()
        msg_with_categories[msgtype].append(msg_name)
        # Generate the data

    d.newline()
    # Generate table of contents
    sorted_categories = sorted([cat for cat in msg_with_categories])
    for msg_category in sorted_categories:
        d.h2("Messages - "+msg_category.upper())

        for key in msg_with_categories[msg_category]:
            msg_name = str(key)
            msg_tag = str(msgs[key]['msgtype'])
            d.directive(name='raw', arg='html',
                                    content='<ul><li> <a href = "#' + _get_href(msg_name, msg_tag) + '"> '
                                            + msg_name + " (" + msg_tag + ")"
                                            + '</a></li></ul>')
            d.newline()

    d.newline()
    d.title("Message specifications")
    d.newline()
    d.content("Wyden inbound FIX is based on FIX 4.4 standard with some custom tags and values")
    d.newline()
    d.content("Specification of each message is provided below. Also there are some examples of FIX messages for different flows in Appendix A below")
    d.newline()

    for msg_category in sorted_categories:
        for msg_name in msg_with_categories[msg_category]:
            produce_pages.produce_message_body(msg_name, msgs[msg_name], fields, comps, d)

    d.title("Appendix A. FIX message examples")
    msg_examples_html = open("msg_examples.html", mode='r', encoding='utf-8').read()
    d.directive(name='raw', arg='html', content=msg_examples_html)
    d.newline()

################### all data generation is done, need jut to render it
    data_per_file[os.path.join(output_path, "index.rst")] = '\n'.join(d.data)
    # Generate conf.py for sphinx
    conf_py = list()
    conf_py.append("project = '" + "Inbound FIX" + "'")
    conf_py.append("author = '" + "Wyden" + "'")
    conf_py.append("source_suffix = '.rst'")
    conf_py.append("master_doc = 'index'")
    conf_py.append("language = None")
    conf_py.append("html_theme = 'bizstyle'")
    conf_py.append("exclude_patterns = ['Thumbs.db', '.DS_Store']")
    conf_py.append("pygments_style = None")
    data_per_file[os.path.join(output_path, "conf.py")] = '\n'.join(conf_py)
    return data_per_file

def _get_href(msg_name, msg_tag):
    return (msg_name.lower() + '-' + msg_tag.lower()).replace(' ', '-')