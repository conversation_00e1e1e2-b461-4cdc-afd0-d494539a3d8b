package io.wyden.cloud.utils.spring.health;

import org.springframework.boot.actuate.health.DefaultHealthContributorRegistry;
import org.springframework.boot.actuate.health.HealthContributor;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

// Inspired by: org.springframework.boot.actuate.autoconfigure.health.AutoConfiguredHealthContributorRegistry
// And: https://stackoverflow.com/a/72311217
class LoggingHealthContributorRegistry extends DefaultHealthContributorRegistry {

    private final Collection<String> groupNames;

    LoggingHealthContributorRegistry(Map<String, HealthContributor> contributors,
                                     Collection<String> groupNames) {
        super(contributors.entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey, LoggingHealthContributorRegistry::wrap)));
        this.groupNames = groupNames;
        contributors.keySet().forEach(this::assertDoesNotClashWithGroup);
    }

    @Override
    public void registerContributor(String name, HealthContributor contributor) {
        assertDoesNotClashWithGroup(name);
        super.registerContributor(name, wrap(name, contributor));
    }

    private static HealthContributor wrap(Map.Entry<String, HealthContributor> entry) {
        return wrap(entry.getKey(), entry.getValue());
    }

    private static HealthContributor wrap(String name, HealthContributor contributor) {
        if (contributor instanceof HealthIndicator indicator) {
            return new LoggingHealthIndicator(name, indicator);
        } else {
            return contributor;
        }
    }

    private void assertDoesNotClashWithGroup(String name) {
        Assert.state(!this.groupNames.contains(name),
            () -> "HealthContributor with name \"" + name + "\" clashes with group");
    }
}
