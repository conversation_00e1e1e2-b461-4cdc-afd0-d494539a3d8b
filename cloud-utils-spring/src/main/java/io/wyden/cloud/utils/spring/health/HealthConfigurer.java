package io.wyden.cloud.utils.spring.health;

import org.springframework.boot.actuate.health.HealthContributor;
import org.springframework.boot.actuate.health.HealthContributorRegistry;
import org.springframework.boot.actuate.health.HealthEndpointGroups;
import org.springframework.boot.actuate.health.ReactiveHealthContributor;
import org.springframework.boot.actuate.health.ReactiveHealthContributorRegistry;
import org.springframework.context.ApplicationContext;
import org.springframework.util.ClassUtils;

import java.util.LinkedHashMap;
import java.util.Map;


public final class HealthConfigurer {

    private HealthConfigurer() {
        // Empty
    }

    // From: org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration.healthContributorRegistry()
    public static HealthContributorRegistry healthContributorRegistry(ApplicationContext applicationContext,
                                                               Map<String, HealthContributor> contributors,
                                                               Map<String, ReactiveHealthContributor> reactiveHealthContributors,
                                                               HealthEndpointGroups groups) {
        if (ClassUtils.isPresent("reactor.core.publisher.Flux", applicationContext.getClassLoader())) {
            contributors.putAll(new AdaptedReactiveHealthContributors(reactiveHealthContributors).get());
        }
        return new LoggingHealthContributorRegistry(contributors, groups.getNames());
    }

    // From: org.springframework.boot.actuate.autoconfigure.health.ReactiveHealthEndpointConfiguration
    public static ReactiveHealthContributorRegistry reactiveHealthContributorRegistry(Map<String, HealthContributor> contributors,
                                                                               Map<String, ReactiveHealthContributor> reactiveHealthContributors,
                                                                               HealthEndpointGroups groups) {
        Map<String, ReactiveHealthContributor> allContributors = new LinkedHashMap<>(reactiveHealthContributors);
        contributors.forEach((name, contributor) -> allContributors.computeIfAbsent(name, (key) -> ReactiveHealthContributor.adapt(contributor)));
        return new LoggingReactiveHealthContributorRegistry(allContributors, groups.getNames());
    }
}
