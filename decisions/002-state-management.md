---
status: ACCEPTED
---

# Use Hazelcast as external distributed state storage

## Context and Problem Statement
Since we want (most) of the (critical) services to be stateless, we decided 
to offload the state to external distributed storage, i.e. Hazelcast.

## Considered Options
- Hazelcast
- Relational Database
- NoSQL

## Decision Outcome

At the initial stage, each service is connecting to the same HZ cluster.
Such setup achieves two things:
- Hazelcast cluster can be managed, monitored and scaled independently of the applications deployments
- We have to manage only a single HZ cluster instead of managing cluster-per-service, which greatly simplifies 
deployment requirements at this stage

However, such setups has some tradeoffs:
- each service has to publish its domain as a public lib, which then has to be declared as HZ storage dependency. 
This is OK for rarely changing domains but can become a problem when we start adding more (independent) services
