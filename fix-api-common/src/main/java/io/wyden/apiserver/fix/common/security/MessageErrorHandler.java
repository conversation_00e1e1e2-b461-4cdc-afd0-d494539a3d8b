package io.wyden.apiserver.fix.common.security;

import io.wyden.apiserver.fix.common.fix.FixMessageUtil;
import io.wyden.apiserver.fix.common.fix.FixSessionWrapper;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import quickfix.FieldNotFound;
import quickfix.SessionID;
import quickfix.field.CxlRejReason;
import quickfix.field.CxlRejResponseTo;
import quickfix.field.RefMsgType;
import quickfix.field.Text;
import quickfix.fix44.Message;
import quickfix.fix44.NewOrderSingle;
import quickfix.fix44.OrderCancelReplaceRequest;
import quickfix.fix44.Reject;

import static quickfix.field.MsgType.ORDER_CANCEL_REPLACE_REQUEST;

@Service
public class MessageErrorHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(MessageErrorHandler.class);

    public static final String ACCESS_DENIED = "Access Denied";
    private final FixSessionWrapper fixSessionWrapper;

    public MessageErrorHandler(FixSessionWrapper fixSessionWrapper) {
        this.fixSessionWrapper = fixSessionWrapper;
    }

    public void handleNoAccess(SessionID sessionId, Message message) {
        LOGGER.warn("User has no permission to perform this operation: %s".formatted(message));
        try {
            Message execReportReject;
            if (message instanceof NewOrderSingle newOrderSingle) {
                execReportReject = FixMessageUtil.createExecReportReject(newOrderSingle, ACCESS_DENIED);
            } else if (message instanceof OrderCancelReplaceRequest cancelRequest) {
                execReportReject = FixMessageUtil.createOrderCancelReject(new CxlRejResponseTo(CxlRejResponseTo.ORDER_CANCEL_REPLACE_REQUEST),
                    cancelRequest.getClOrdID().getValue(), ACCESS_DENIED, CxlRejReason.OTHER);
            } else {
                throw new RuntimeException("Unhandled message type: %s".formatted(message.getClass().getSimpleName()));
            }
            fixSessionWrapper.send(sessionId, execReportReject);
        } catch (FieldNotFound e) {
            if (message instanceof NewOrderSingle) {
                handleFieldNotFoundException(sessionId, (NewOrderSingle) message, e);
            } else {
                handleFieldNotFoundException(sessionId, (OrderCancelReplaceRequest) message, e);
            }
        }
    }

    public void handleFieldNotFoundException(SessionID sessionId, NewOrderSingle newOrderSingle, FieldNotFound e) {
        LOGGER.error("Session message parsing error ({}) - Field not found, producing early Reject", sessionId, e);
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(e);
        Reject reject = FixMessageUtil.createFieldNotFoundReject(newOrderSingle, e);
        fixSessionWrapper.send(sessionId, reject);
    }

    public void handleFieldNotFoundException(SessionID sessionId, OrderCancelReplaceRequest orderCancelReplaceRequest, FieldNotFound e) {
        LOGGER.error("Session message parsing error ({}) - Field not found", sessionId, e);
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(e);
        Reject reject = FixMessageUtil.createReject(orderCancelReplaceRequest);
        reject.set(new Text("Missing field: " + e.field));
        reject.set(new RefMsgType(ORDER_CANCEL_REPLACE_REQUEST));
        fixSessionWrapper.send(sessionId, reject);
    }

    public void handleIllegalArgumentException(SessionID sessionId, OrderCancelReplaceRequest orderCancelReplaceRequest, IllegalArgumentException e) {
        LOGGER.error("Session message handling error ({}) - Illegal argument", sessionId, e);
        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(e);
        Reject reject = FixMessageUtil.createReject(orderCancelReplaceRequest);
        reject.set(new Text("Illegal argument: " + e.getMessage()));
        reject.set(new RefMsgType(ORDER_CANCEL_REPLACE_REQUEST));
        fixSessionWrapper.send(sessionId, reject);
    }

    public void handleOrigClOrderIdNotFound(SessionID sessionId, OrderCancelReplaceRequest orderCancelReplaceRequest, String origClOrderId) {
        Reject reject = FixMessageUtil.createReject(orderCancelReplaceRequest);
        reject.set(new Text("OrigClOrderId unknown: " + origClOrderId));
        reject.set(new RefMsgType(ORDER_CANCEL_REPLACE_REQUEST));
        fixSessionWrapper.send(sessionId, reject);
    }
}
