package io.wyden.apiserver.fix.common.hazelcast;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.accessgateway.domain.permission.PermissionGroupMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionUserMapConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class PermissionCheckerConfig {

    @Bean
    public IMap<String, String> userPermission(HazelcastInstance hazelcast) {
        return PermissionUserMapConfig.getMap(hazelcast);
    }

    @Bean
    public IMap<String, String> groupPermission(HazelcastInstance hazelcast) {
        return PermissionGroupMapConfig.getMap(hazelcast);
    }

    @Bean
    public PermissionUserMapConfig userPermissionMapConfig() {
        return new PermissionUserMapConfig();
    }

    @Bean
    public PermissionGroupMapConfig groupPermissionMapConfig() {
        return new PermissionGroupMapConfig();
    }
}
