package io.wyden.apiserver.fix.common.dictionary;

import quickfix.FieldNotFound;
import quickfix.Group;

public class NoExcludedAccounts extends Group {
    public static final int FIELD = 10013;

    public NoExcludedAccounts() {
        super(FIELD, SORAccount.FIELD);
    }

    public ExcludedAccount get(ExcludedAccount value) throws FieldNotFound {
        this.getField(value);
        return value;
    }

    public ExcludedAccount getExcludedAccount() throws FieldNotFound {
        return this.get(new ExcludedAccount());
    }
}
