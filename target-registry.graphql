extend type Query {
    connectorList : [ConnectorResponse]
    connectorById(id : String!) : ConnectorResponse
    connectorDetails(id : String!) : ConnectorDetailsResponse
    connectorNameCheck(name : String!) : Boolean
    connectorTypeList : [String]
    connectorTemplateByVenue(venue : String!) : [ConnectorConfigField]
}

extend type Mutation {
    connectorCreate(input : ConnectorDetailsInput!) : MutationSubmittedResponse
    connectorUpdate(input: ConnectorDetailsInput!) : MutationSubmittedResponse
    connectorRemove(id : String!) : MutationSubmittedResponse
    connectorAction(id : String!, action : ConnectorActionType!) : MutationSubmittedResponse
    connectorSyncData(id : String!) : MutationSubmittedResponse
}

extend type Subscription {
    connectorStates: ConnectorStatesResponse
}

input ConnectorDetailsInput {
    connectorId: String
    connectorName: String
    connectorType: String
    venue: String
    deactivatedAtDateTime: String
    keyValues: [KeyValueInput]
}

type ConnectorResponse {
    connectorId: String
    connectorName: String
    connectorType: String
    venue: String
    deactivatedAtDateTime: String
}

type ConnectorDetailsResponse {
    connectorId: String
    connectorName: String
    connectorType: String
    venue: String
    deactivatedAtDateTime: String
    keyValues: [KeyValue]
}

type ConnectorStatesResponse {
    connectorId: String!
    capability: [CapabilityState!]
}

type CapabilityState {
    capability: Capability!
    healthStatus: HealthStatus!
    message: String
    timestamp: String
}

type ConnectorConfigField {
    type: String
    secret: Boolean
    enumValues: [String]
    propertyKey: String
    defaultValue: String
    required: Boolean
    multiline: Boolean
    description: String
}

enum ConnectorActionType {
    ACTIVATE,
    DEACTIVATE
}