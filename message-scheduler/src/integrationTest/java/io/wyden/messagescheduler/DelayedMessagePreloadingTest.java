package io.wyden.messagescheduler;

import com.rabbitmq.client.AMQP;
import io.wyden.cloud.utils.test.ExchangeObserver;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@Import(DelayedMessagePreloadingConfig.class)
public class DelayedMessagePreloadingTest extends IntegrationTestBase {
    @Test
    void shouldProcessPreloadedMessages() {
        await().atMost(15, TimeUnit.SECONDS).untilAsserted(() -> {
            assertThat(new ArrayList<>(positionSnapshotObserver.getMessagesWithProperties()))
                .hasSizeGreaterThan(5)
                .allSatisfy(m -> {
                    AMQP.BasicProperties properties = m.properties();
                    assertThat(properties.getHeaders()).containsKeys(testHeaders().keySet().toArray(new String[0]));
                })
                .extracting(ExchangeObserver.MessageWithProperties::message)
                .containsSubsequence(
                    testMessage("preloaded-message-1"),
                    testMessage("preloaded-message-2"),
                    testMessage("preloaded-message-3"),
                    testMessage("preloaded-message-4"),
                    testMessage("preloaded-message-5")
                )
                .contains(
                    testMessage("repeated-message")
                )
                .doesNotContain(
                    testMessage("yearly-message") // make sure that task loaded from the DB does not fire immediately
                );
        });
    }
}
