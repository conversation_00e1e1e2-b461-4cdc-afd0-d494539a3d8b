package io.wyden.risk.engine.pretradecheck;

import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import jakarta.validation.constraints.AssertTrue;
import java.util.Set;

public class OrderTypePreTradeCheck implements PreTradeCheck {

    private static final String REASON = "Order type not allowed";

    public record Properties(
        Set<OrderType> allowlist,
        Set<OrderType> blocklist
    ) {
        @AssertTrue(
            message = "One and only one of [allowlist, blocklist] must be set"
        )
        private boolean isAllowOrBlockDefined() {
            return (
                (allowlist != null && !allowlist.isEmpty()) ^
                (blocklist != null && !blocklist.isEmpty())
            );
        }
    }

    private final Properties properties;
    private final PreTradeCheckLevel level;

    public OrderTypePreTradeCheck(
        Properties properties,
        PreTradeCheckLevel level
    ) {
        this.properties = properties;
        this.level = level;
    }

    @Override
    public PreTradeCheckResult check(OemsRequest request) {
        OrderType orderType = OrderType.fromProto(request.getOrderType());

        if (properties.blocklist != null && !properties.blocklist.isEmpty()) {
            if (properties.blocklist.contains(orderType)) {
                return PreTradeCheckResult.failure(level, REASON);
            }
        }

        if (properties.allowlist != null && !properties.allowlist.isEmpty()) {
            if (!properties.allowlist.contains(orderType)) {
                return PreTradeCheckResult.failure(level, REASON);
            }
        }

        return PreTradeCheckResult.approved();
    }

    public enum OrderType {
        MARKET,
        LIMIT,
        STOP,
        STOP_LIMIT;

        public static OrderType fromProto(OemsOrderType oemsOrderType) {
            return switch (oemsOrderType) {
                case MARKET -> MARKET;
                case LIMIT -> LIMIT;
                case STOP -> STOP;
                case STOP_LIMIT -> STOP_LIMIT;
                case
                    ORDER_TYPE_UNSPECIFIED,
                    UNRECOGNIZED -> throw new IllegalArgumentException(
                    "Order type not specified"
                );
            };
        }
    }
}
