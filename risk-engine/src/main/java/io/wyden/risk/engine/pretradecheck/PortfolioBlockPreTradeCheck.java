package io.wyden.risk.engine.pretradecheck;

import io.wyden.published.oems.OemsRequest;

public class PortfolioBlockPreTradeCheck implements PreTradeCheck {
    private static final String REASON_BLOCK = "Portfolio is blocked for trading";

    public record PortfolioBlockPreTradeCheckProperties() {
    }

    public PortfolioBlockPreTradeCheck(PreTradeCheckLevel level) {
        this.level = level;
    }

    private final PreTradeCheckLevel level;

    @Override
    public PreTradeCheckResult check(OemsRequest request) {
        return PreTradeCheckResult.failure(level, REASON_BLOCK);
    }
}
