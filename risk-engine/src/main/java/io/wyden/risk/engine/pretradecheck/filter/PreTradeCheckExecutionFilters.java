package io.wyden.risk.engine.pretradecheck.filter;

import io.wyden.published.common.ApiType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.risk.engine.pretradecheck.PreTradeCheckRequestChannel;
import io.wyden.risk.engine.pretradecheck.wrapper.PreTradeCheckWrapper;
import io.wyden.risk.engine.referencedata.PortfolioTagsResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

public class PreTradeCheckExecutionFilters {
    private static final Logger LOGGER = LoggerFactory.getLogger(PreTradeCheckExecutionFilters.class);

    public static boolean shouldCheckPortfolio(PreTradeCheckWrapper ptc, OemsRequest request, PortfolioTagsResolver tagsResolver) {
        boolean portfoliosConfigured = ptc.portfolios() != null && !ptc.portfolios().isEmpty();
        boolean portfolioTagsConfigured = ptc.portfolioTags() != null && !ptc.portfolioTags().isEmpty();
        boolean checkAllPortfolios = !portfoliosConfigured && !portfolioTagsConfigured;
        if (checkAllPortfolios) {
            LOGGER.debug("ptc={} will check orderId={}, it's configured to run on all portfolios", ptc, request.getOrderId());
            return true;
        }

        boolean checkPortfolioById = portfoliosConfigured && ptc.portfolios().contains(request.getPortfolioId());
        if (checkPortfolioById) {
            LOGGER.debug("ptc={} will check orderId={}, portfolio {} is among configured portfolios", ptc, request.getOrderId(), request.getPortfolioId());
            return true;
        }

        if (!portfolioTagsConfigured) {
            LOGGER.debug("ptc={} won't check orderId={}, portfolio {} is not among configured portfolios", ptc, request.getOrderId(), request.getPortfolioId());
            return false;
        }

        Map<String, String> orderPortfolioTags = tagsResolver.resolveTags(request.getPortfolioId());
        Optional<Map.Entry<String, String>> matchingTag = findAnyMatchingEntry(orderPortfolioTags, ptc.portfolioTags());
        if (matchingTag.isPresent()) {
            LOGGER.debug("ptc={} will check orderId={}, at least one of configured portfolio tags is among portfolio {} tags", ptc, request.getOrderId(), request.getPortfolioId());
            return true;
        } else {
            LOGGER.debug("ptc={} won't check orderId={}, portfolio {} tags do not match configured portfolio tags", ptc, request.getOrderId(), request.getPortfolioId());
            return false;
        }
    }

    public static boolean shouldCheckRequestChannel(PreTradeCheckWrapper ptc, OemsRequest request) {
        ApiType apiType = request.getMetadata().getRequestApiType();
        PreTradeCheckRequestChannel requestChannel = switch (apiType) {
            case GRAPHQL -> PreTradeCheckRequestChannel.UI;
            case FIX, REST, API_TYPE_UNSPECIFIED, UNRECOGNIZED -> PreTradeCheckRequestChannel.API;
        };

        if (ptc.requestChannels().contains(requestChannel)) {
            LOGGER.debug("ptc={} will check orderId={}, request channel {} is among configured channels", ptc, request.getOrderId(), requestChannel);
            return true;
        } else {
            LOGGER.debug("ptc={} won't check orderId={}, request channel {} is not among configured channels (configured={})", ptc, request.getOrderId(), requestChannel, ptc.requestChannels());
            return false;
        }
    }

    private static Optional<Map.Entry<String, String>> findAnyMatchingEntry(Map<String, String> map1, Map<String, String> map2) {
        // Determine the smaller map to iterate over
        Map<String, String> smallerMap = map1.size() < map2.size() ? map1 : map2;
        Map<String, String> largerMap = smallerMap == map1 ? map2 : map1;

        Set<Map.Entry<String, String>> smallerMapEntries = smallerMap.entrySet();
        for (Map.Entry<String, String> entry : smallerMapEntries) {
            if (largerMap.entrySet().contains(entry)) {
                return Optional.of(entry);
            }
        }
        return Optional.empty();
    }
}
