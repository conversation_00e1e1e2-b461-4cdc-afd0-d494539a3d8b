package io.wyden.risk.engine.pretradecheck.factory;

import static io.wyden.risk.engine.model.PropertiesSchema.Builder.SimplePropertyType.DECIMAL;
import static io.wyden.risk.engine.model.PropertiesSchema.Builder.SimplePropertyType.STRING;

import io.wyden.published.risk.PreTradeCheckPropertyFormat;
import io.wyden.risk.engine.model.PropertiesSchema;
import io.wyden.risk.engine.pretradecheck.MaxPositionPreTradeCheck;
import io.wyden.risk.engine.pretradecheck.MaxPositionPreTradeCheck.Properties;
import io.wyden.risk.engine.pretradecheck.PreTradeCheckLevel;
import io.wyden.risk.engine.referencedata.InstrumentsRepository;
import io.wyden.risk.engine.service.outbound.booking.BookingEngineIntegrator;
import org.springframework.stereotype.Component;

@Component
public class MaxPositionPreTradeCheckFactory
    implements PreTradeCheckFactory<MaxPositionPreTradeCheck, Properties> {

    private final BookingEngineIntegrator bookingEngineIntegrator;
    private final InstrumentsRepository instrumentsRepository;

    public MaxPositionPreTradeCheckFactory(
        BookingEngineIntegrator bookingEngineIntegrator,
        InstrumentsRepository instrumentsRepository
    ) {
        this.bookingEngineIntegrator = bookingEngineIntegrator;
        this.instrumentsRepository = instrumentsRepository;
    }

    @Override
    public String type() {
        return "MaxPosition";
    }

    @Override
    public Class<Properties> propertiesClass() {
        return Properties.class;
    }

    @Override
    public PropertiesSchema propertiesSchema() {
        return PropertiesSchema.builder()
            .required("symbol", STRING, PreTradeCheckPropertyFormat.CURRENCY)
            .optional("maxQuantityWarn", DECIMAL)
            .optional("maxQuantityBlock", DECIMAL)
            .optional("maxValueWarn", DECIMAL)
            .optional("maxValueBlock", DECIMAL)
            .optional("maxValueCurrency", STRING)
            .build();
    }

    @Override
    public MaxPositionPreTradeCheck create(
        Properties properties,
        PreTradeCheckLevel level
    ) {
        return new MaxPositionPreTradeCheck(
            properties,
            level,
            bookingEngineIntegrator,
            instrumentsRepository
        );
    }
}
