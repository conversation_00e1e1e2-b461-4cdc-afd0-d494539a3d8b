package io.wyden.risk.engine.pretradecheck.factory;

import static io.wyden.risk.engine.model.PropertiesSchema.Builder.SimplePropertyType.DECIMAL;
import static io.wyden.risk.engine.model.PropertiesSchema.Builder.SimplePropertyType.STRING_ARRAY;

import io.wyden.published.risk.PreTradeCheckPropertyFormat;
import io.wyden.risk.engine.model.PropertiesSchema;
import io.wyden.risk.engine.pretradecheck.MaxOrderSizePreTradeCheck;
import io.wyden.risk.engine.pretradecheck.MaxOrderSizePreTradeCheck.MaxOrderSizePreTradeCheckProperties;
import io.wyden.risk.engine.pretradecheck.PreTradeCheckLevel;
import io.wyden.risk.engine.referencedata.InstrumentsRepository;
import org.springframework.stereotype.Component;

@Component
public class MaxOrderSizePreTradeCheckFactory
    implements
        PreTradeCheckFactory<
            MaxOrderSizePreTradeCheck,
            MaxOrderSizePreTradeCheckProperties
        > {

    private final InstrumentsRepository instrumentsRepository;

    public MaxOrderSizePreTradeCheckFactory(
        InstrumentsRepository instrumentsRepository
    ) {
        this.instrumentsRepository = instrumentsRepository;
    }

    @Override
    public String type() {
        return "MaxOrderSize";
    }

    @Override
    public Class<MaxOrderSizePreTradeCheckProperties> propertiesClass() {
        return MaxOrderSizePreTradeCheckProperties.class;
    }

    @Override
    public PropertiesSchema propertiesSchema() {
        return PropertiesSchema.builder()
            .optional("maxQuantityBlock", DECIMAL)
            .optional("maxQuantityWarn", DECIMAL)
            .optional(
                "symbols",
                STRING_ARRAY,
                PreTradeCheckPropertyFormat.CURRENCY
            )
            .build();
    }

    @Override
    public MaxOrderSizePreTradeCheck create(
        MaxOrderSizePreTradeCheckProperties properties,
        PreTradeCheckLevel level
    ) {
        return new MaxOrderSizePreTradeCheck(
            properties,
            level,
            instrumentsRepository
        );
    }
}
