package io.wyden.cloudutils.rabbitmq.queue;

import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.DeliverCallback;
import io.wyden.cloudutils.rabbitmq.InfrastructureException;
import io.wyden.cloudutils.rabbitmq.MessageParser;
import io.wyden.cloudutils.rabbitmq.MetaDataHeader;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.RabbitStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import javax.annotation.Nullable;

import static io.wyden.cloudutils.rabbitmq.RawRabbitExchange.getCurrentDate;

public class RabbitStreamOverAmqp<T extends Message> implements RabbitStream<T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RabbitStreamOverAmqp.class);
    public static final String QUEUE_TYPE = "x-queue-type";
    public static final String STREAM_QUEUE_TYPE = "stream";
    public static final String X_MAX_LENGTH_BYTES = "x-max-length-bytes";
    public static final String X_QUEUE_LEADER_LOCATOR = "x-queue-leader-locator";
    public static final String X_STREAM_FILTER_SIZE_BYTES = "x-stream-filter-size-bytes";
    public static final String X_STREAM_FILTER_VALUE = "x-stream-filter-value"; // filter value on publisher side
    public static final String X_STREAM_OFFSET = "x-stream-offset";
    public static final String X_STREAM_FILTER = "x-stream-filter"; // filter on client side

    private final RabbitIntegrator rabbitIntegrator;
    private final AMQP.Queue.DeclareOk declared;

    public RabbitStreamOverAmqp(RabbitIntegrator rabbitIntegrator, String name) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.declared = declare(name);
    }

    private AMQP.Queue.DeclareOk declare(String name) {
        try {
            AMQP.Queue.DeclareOk stream = rabbitIntegrator.getDeclarationAndPublishChannel().queueDeclare(
                name,
                true,            // durable
                false, false, // not exclusive, not auto-delete
                Map.of(
                    QUEUE_TYPE, STREAM_QUEUE_TYPE,
                    // ~50MB retention:
                    X_MAX_LENGTH_BYTES, 50_000_000,
                    // reflects the defaults in native rabbitmq-stream-java-client:
                    X_QUEUE_LEADER_LOCATOR,	"balanced",
                    // size of the server-side filter (must be between 16 and 255 bytes)
                    X_STREAM_FILTER_SIZE_BYTES, 16
                )
            );
            LOGGER.info("Declared Stream over AMQP: {}", stream.getQueue());
            return stream;
        } catch (IOException e) {
            throw new InfrastructureException("Failed to declare Rabbit Stream over AMQP", e);
        }
    }

    @Override
    public CompletableFuture<Void> publish(T message) {
        return publish(message, new HashMap<>());
    }

    @Override
    public CompletableFuture<Void> publish(T message, String filter) {
        HashMap<String, Object> rabbitHeaders = new HashMap<>();
        rabbitHeaders.put(X_STREAM_FILTER_VALUE, filter);
        return publish(message, rabbitHeaders);
    }

    private CompletableFuture<Void> publish(T message, Map<String, Object> rabbitHeaders) {
        String contentType = message.getClass().getName();

        rabbitHeaders.put(MetaDataHeader.PROTOBUF_TYPE.getHeaderName(), contentType);

        AMQP.BasicProperties props = new AMQP.BasicProperties.Builder()
            .deliveryMode(2) // persistent
            .timestamp(getCurrentDate())
            .headers(rabbitHeaders)
            .contentType(contentType)
            .build();

        return CompletableFuture.runAsync(() -> {
            try {
                rabbitIntegrator.getDeclarationAndPublishChannel().basicPublish(
                    "",
                    declared.getQueue(),
                    props,
                    message.toByteArray()
                );
            } catch (IOException e) {
                throw new InfrastructureException(e);
            }
        }, rabbitIntegrator.getProducingExecutorService());
    }

    @Override
    public void attachConsumer(MessageParser<T> parser, MessageConsumer<T> consumer) {
        // Explicitly use "next" offset to ensure real-time consumption
        // This prevents any consumer group behavior that might cause historical replay
        Map<String, Object> arguments = Map.of(
            X_STREAM_OFFSET, SpecialOffset.NEXT.toString()
        );
        attachConsumer(parser, consumer, arguments);
    }

    @Override
    public void attachConsumer(MessageParser<T> parser, MessageConsumer<T> consumer, long offset) {
        Map<String, Object> arguments = Map.of(
            X_STREAM_OFFSET, offset
        );

        attachConsumer(parser, consumer, arguments);
    }

    @Override
    public void attachConsumer(MessageParser<T> parser, MessageConsumer<T> consumer, SpecialOffset specialOffset) {
        Map<String, Object> arguments = Map.of(
            X_STREAM_OFFSET, specialOffset.toString()
        );

        attachConsumer(parser, consumer, arguments);
    }

    @Override
    public void attachConsumer(MessageParser<T> parser, MessageConsumer<T> consumer, SpecialOffset specialOffset, String filter) {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put(X_STREAM_OFFSET, specialOffset.toString());
        arguments.put(X_STREAM_FILTER, filter);

        attachConsumer(parser, consumer, arguments);
    }

    private void attachConsumer(MessageParser<T> parser, MessageConsumer<T> consumer, Map<String, Object> arguments) {
        Channel channel = rabbitIntegrator.getStreamConsumptionChannel();
        try {
            Object filterAttribute = arguments.get(X_STREAM_FILTER);
            String filter = filterAttribute instanceof String filterString ? filterString : null;
            String consumerTag = channel.basicConsume(declared.getQueue(),
                false,
                arguments,
                asDeliverCallback(parser, consumer, filter),
                (ct, sig) -> LOGGER.info("Consumer shutdown: {}", ct, sig)
            );
            LOGGER.info("Stream consumer attached with tag: {} for stream: {}", consumerTag, declared.getQueue());
        } catch (IOException e) {
            throw new InfrastructureException("Failed to attach consumer for stream: %s".formatted(declared.getQueue()), e);
        }
    }

    private DeliverCallback asDeliverCallback(MessageParser<T> parser, MessageConsumer<T> consumer, @Nullable String filter) {
        return (consumerTag, message) -> {
            Channel channel = rabbitIntegrator.getStreamConsumptionChannel();
            rabbitIntegrator.getStreamConsumingExecutorService().submit(() -> {
                AMQP.BasicProperties properties = message.getProperties();
                try {
                    // Client-side filtering is mandatory for streams because of bloom-filtering on server side.
                    // See: https://www.rabbitmq.com/docs/streams#filtering
                    if (filter == null || filter.equals(properties.getHeaders().getOrDefault(X_STREAM_FILTER_VALUE, "").toString())) {
                        var parsedMessage = parser.parse(message.getBody(), properties.getType());
                        consumer.consume(parsedMessage, properties);
                    }
                    channel.basicAck(message.getEnvelope().getDeliveryTag(), false);
                } catch (Exception e) {
                    LOGGER.error("Nack-ing (no requeue) - failed to consume stream message. Message properties: {}", properties, e);
                    try {
                        channel.basicNack(message.getEnvelope().getDeliveryTag(), false, false);
                    } catch (IOException ex) {
                        LOGGER.error("Failed nack-ing (no requeue) - failed to consume stream.", ex);
                    }
                }
            });
        };
    }

    @Override
    public String toString() {
        return "Rabbit Stream (AMQP declared):" + declared.getQueue();
    }
}
