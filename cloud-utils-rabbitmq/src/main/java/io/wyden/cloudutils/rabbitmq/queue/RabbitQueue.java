package io.wyden.cloudutils.rabbitmq.queue;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.DeliverCallback;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.InfrastructureException;
import io.wyden.cloudutils.rabbitmq.MessageParser;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

import static io.wyden.cloudutils.rabbitmq.queue.RawRabbitQueue.NO_MULTIPLE;
import static io.wyden.cloudutils.rabbitmq.queue.RawRabbitQueue.NO_REQUEUE;
import static io.wyden.cloudutils.rabbitmq.queue.RawRabbitQueue.REQUEUE;

public class RabbitQueue<T extends Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RabbitQueue.class);

    private final RawRabbitQueue queue;
    private Predicate<AMQP.BasicProperties> filter;

    public RabbitQueue(RawRabbitQueue queue) {
        this.queue = queue;
    }

    public static <T extends Message> RabbitQueue<T> simpleQueue(RabbitIntegrator rabbitIntegrator, String queueName) {
        return rabbitIntegrator.<T>createQueue()
                .setQueueName(queueName)
                .setSingleActiveConsumer(false)
                .declare();
    }

    void declare() {
        queue.declare();
    }

    public void bindWithRoutingKey(RabbitExchange<T> exchange, String routingKey) {
        queue.bindWithRoutingKey(exchange, routingKey);
    }

    public void unbindWithRoutingKey(RabbitExchange<T> exchange, String routingKey) {
        queue.unbindWithRoutingKey(exchange, routingKey);
    }

    public <T1 extends Message> void bindWithHeaders(RabbitExchange<T1> exchange, MatchingCondition matchingCondition, Map<String, Object> headerMatchers) {
        queue.bindWithHeaders(exchange, matchingCondition, headerMatchers);
    }

    public void unbindWithHeaders(RabbitExchange<T> exchange, MatchingCondition matchingCondition, Map<String, Object> headerMatchers) {
        queue.unbindWithHeaders(exchange, matchingCondition, headerMatchers);
    }

    public String attachDefaultConsumer(Function<Channel, DefaultConsumer> consumerProvider, boolean autoAck) {
        return queue.attachDefaultConsumer(consumerProvider, autoAck);
    }

    /**
     * @return consumerTag - can be used for cancellation
     */
    public String attachConsumer(Parser<T> parser, MessageConsumer<T> consumer) {
        return queue.attach(asDeliverCallback(parser, consumer), false);
    }

    public String attachConsumer(MessageParser<T> parser, MessageConsumer<T> consumer) {
        return queue.attach(asDeliverCallback(parser, consumer), false);
    }

    /**
     * @return consumerTag - can be used for cancellation
     */
    public String attachConsumerAutoAck(MessageParser<T> parser, AutoAckMessageConsumer<T> consumer) {
        return queue.attach(asDeliverCallbackWithoutAck(parser, consumer), true);
    }

    public void cancelConsumer(String consumerTag) {
        queue.cancelConsumer(consumerTag);
    }

    private DeliverCallback asDeliverCallbackWithoutAck(MessageParser<T> parser, AutoAckMessageConsumer<T> messageConsumer) {
        return (consumerTag, message) -> queue.consumingExecutorService.submit(() -> {
            try {
                AMQP.BasicProperties properties = message.getProperties();

                if (filter != null && !filter.test(properties)) {
                    LOGGER.trace("Received a new message from destination: {} but skipping due to properties ({}) doesn't match filter ({})", getName(), properties, filter.toString());
                    return;
                }
                LOGGER.trace("Received a new message from destination: {}", getName());
                byte[] body = message.getBody();
                String type = properties.getType();
                T translated = parser.parse(body, type);
                messageConsumer.consume(translated, properties);
            } catch (Exception ex) {
                LOGGER.error("Exception when processing message", ex);
            } catch (Throwable th) {
                LOGGER.error("Error", th);
                throw th;
            }
        });
    }

    private DeliverCallback asDeliverCallback(MessageParser<T> parser,
                                              MessageConsumer<T> messageConsumer) {
        return (consumerTag, message) -> queue.consumingExecutorService.submit(() -> {
            long deliveryTag = message.getEnvelope().getDeliveryTag();
            AMQP.BasicProperties properties = message.getProperties();

            if (filter != null && !filter.test(properties)) {
                LOGGER.trace("Received a new message from destination: {} but skipping due to properties ({}) doesn't match filter ({})", getName(), properties, filter.toString());
                queue.basicAck(deliveryTag, NO_MULTIPLE);
                return;
            }

            LOGGER.trace("Received a new message from destination: {}", getName());
            T translated;
            try {
                translated = parser.parse(message.getBody(), properties.getType());
            } catch (Exception e) {
                LOGGER.error("NACK-ing with requeue: Couldn't translate received message from queue: '{}'. Reason: {}",
                    getName(), e.getMessage(), e);
                queue.basicNack(deliveryTag, NO_MULTIPLE, REQUEUE);
                return;
            } catch (Throwable th) {
                LOGGER.error("Error", th);
                throw th;
            }

            try {
                ConsumptionResult consumptionResult = messageConsumer.consume(translated, properties);
                if (consumptionResult.complete()) {
                    queue.basicAck(deliveryTag, NO_MULTIPLE);
                } else if (consumptionResult.requeue()) {
                    LOGGER.error("NACK-ing with re-queue: Couldn't process received message from queue: '{}'. Message: {}: {}",
                        getName(), translated.getClass().getSimpleName(), translated);
                    queue.basicNack(deliveryTag, NO_MULTIPLE, REQUEUE);
                } else {
                    LOGGER.error("NACK-ing without re-queue (unrecoverable error). Queue: '{}'. Message: {}: '{}'", getName(),
                        translated.getClass().getSimpleName(), translated);
                    queue.basicNack(deliveryTag, NO_MULTIPLE, NO_REQUEUE);
                }
            } catch (Exception e) {
                LOGGER.error("NACK-ing with requeue: Couldn't process received message from queue: '{}'. Reason: {}",
                    getName(), e.getMessage(), e);
                queue.basicNack(deliveryTag, NO_MULTIPLE, REQUEUE);
            } catch (Throwable th) {
                LOGGER.error("Error", th);
                throw th;
            }
        });
    }

    private DeliverCallback asDeliverCallback(Parser<T> parser, MessageConsumer<T> messageConsumer) {
        return asDeliverCallback(toObjectParser(parser), messageConsumer);
    }

    /**
     * Drain all messages from the Queue in a blocking way.
     * Will use a temporary channel for the operation.
     */
    public List<T> blockingDrain(Parser<T> parser) {
        List<T> output = new ArrayList<>();
        Consumer<byte[]> consumer = bytes -> {
            try {
                T item = parser.parseFrom(bytes);
                output.add(item);
            } catch (InvalidProtocolBufferException e) {
                throw new InfrastructureException(e);
            }
        };

        queue.blockingDrain(consumer);

        return output;
    }

    public String getName() {
        return queue.getQueueName();
    }

    public void setFilter(Predicate<AMQP.BasicProperties> filter) {
        this.filter = filter;
    }

    private static <T extends Message> MessageParser<T> toObjectParser(Parser<T> parser) {
        return (body, type) -> {
            LOGGER.trace("Parsing rabbit message: {} bytes", body.length);
            return parser.parseFrom(body);
        };
    }
}
