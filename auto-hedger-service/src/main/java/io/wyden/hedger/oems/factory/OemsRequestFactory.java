package io.wyden.hedger.oems.factory;

import io.wyden.cloudutils.rabbitmq.destination.OemsTarget;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsInstrumentType;
import io.wyden.published.oems.OemsOrderCategory;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.oems.OemsTIF;

import java.time.ZonedDateTime;
import java.util.UUID;

public class OemsRequestFactory {

    private static final String CLIENT_ID = "auto-hedger";

    public static OemsRequest createOrder(String instrumentId,
                                          String venueAccount,
                                          String portfolioId,
                                          String quantity,
                                          OemsSide side,
                                          OemsTIF tif,
                                          String baseCurrency,
                                          String quoteCurrency) {
        OemsRequest.Builder childOrder = OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderCategory(OemsOrderCategory.AUTO_HEDGING_ORDER)
            .setOrderId(UUID.randomUUID().toString())
            .setInstrumentId(instrumentId)
            .setVenueAccount(venueAccount)
            .setClientId(CLIENT_ID)
            .setPortfolioId(portfolioId)
            .setInstrumentType(OemsInstrumentType.FOREX)
            .setOrderType(OemsOrderType.MARKET)
            .setSide(side)
            .setQuantity(quantity)
            .setTif(tif)
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setSourceType(Metadata.ServiceType.AUTO_HEDGER)
                .setSource(OemsTarget.AUTO_HEDGER.getTarget())
                .setTargetType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT)
                .setTarget(venueAccount)
                .build());

        return childOrder.build();
    }
}
