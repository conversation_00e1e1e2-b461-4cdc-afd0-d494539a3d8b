spring.application.name = custody-service
spring.profiles.active = prod

server.port = 8055

# Hazelcast setup:

# comma-separated list of hz member hosts
hz.addressList = localhost
hz.outboundPortDefinition=

rabbitmq.username = custody-service
rabbitmq.password = password
rabbitmq.virtualHost = /
rabbitmq.host = localhost
# default RabbitMQ port for non-TLS connections: 5672, default port for TLS connections: 5671
rabbitmq.port = 5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls =

tracing.collector.endpoint=http://localhost:4317
management.endpoints.web.exposure.include=health,prometheus,metrics,loggers
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.metrics.tags.wyden_service=custody-service
management.endpoint.health.group.liveness.include=livenessState,rabbit,clusterRunning,diskSpace,hazelcast
management.endpoint.health.group.readiness.include=readinessState

schema.graphql.version=1.1.5
spring.graphql.websocket.path=/graphql/ws
spring.graphql.schema.printer.enabled=true
spring.graphql.graphiql.enabled=true
spring.graphql.graphiql.path=/graphiql
spring.graphql.schema.locations=classpath:graphql/${schema.graphql.version}
spring.graphql.schema.file-extensions=.graphql