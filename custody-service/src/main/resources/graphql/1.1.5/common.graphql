### ENUM ###
enum AssetClass {
  FOREX
}

enum VenueType {
  """
  External Venue
  Can be one of: Exchange, Broker, Market-maker, Liquidity provider, market data provider
  """
  STREET,

  """
  Internal broker or market maker
  """
  CLIENT,

  """
  Central Limit Order Book
  """
  CLOB
}

enum DayOfTheWeek {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY
}

enum MutationType {
  CREATED
  UPDATED
  DELETED
}
### / ENUM ###



### INSTRUMENT ###
"""
Describes an Instrument in Wyden system
"""
type InstrumentResponse {
  baseInstrument: BaseInstrumentResponse!
  instrumentIdentifiers: InstrumentIdentifiersResponse!
  tradingConstraints: TradingConstraintsResponse!
  """
  Not-null for baseInstrument.assetClass=FOREX
  """
  forexSpotProperties: ForexSpotPropertiesResponse
  """
  Not-null if Instrument has been archived.
  Archived Instruments cannot be traded, but it is still possible to view trade history on them and hold an open position on it.
  """
  archivedAt: String

  """
  Date the instrument was created in our system
  """
  createdAt: String

  """
  Date of a last update of the instrument in our system
  """
  updatedAt: String
}

type BaseInstrumentResponse {
  """
  Unique name of the (internal or external) venue when this Instrument can be traded
  """
  venueName: String!
  venueType: VenueType!
  assetClass: AssetClass!
  description: String
  quoteCurrency: String!
  inverseContract: Boolean
  symbol: String
}

type InstrumentIdentifiersResponse {
  """
  Instrument identifier that will be used when communicating with the external venue directly
  """
  adapterTicker: String

  """
  Instrument identifier that will be used when communicating with TradingView
  """
  tradingViewId: String

  """
  Venue identifier for this instrument that will be used when communicating with TradingView
  """
  venueTradingViewId: String

  """
  Wyden-system instrument identifier, unique in scope of the Wyden system across all instruments.
  Immutable, cannot be changed once set.
  """
  instrumentId: String!
}

type TradingConstraintsResponse {
  """
  Minimum quantity allowed for trading
  Optional, no constraint if not set
  """
  minQty: String

  """
  Maximum quantity allowed for trading
  Optional, no constraint if not set
  """
  maxQty: String

  """
  Minimum quote quantity allowed for trading
  Optional, no constraint if not set
  """
  minQuoteQty: String

  """
  Maximum quote quantity allowed for trading
  Optional, no constraint if not set
  """
  maxQuoteQty: String

  """
  Minimum quantity increment
  """
  qtyIncr: String

  """
  Minimum quote quantity increment
  """
  quoteQtyIncr: String

  """
  Minimum trading price - constraints Limit and StopLimit Orders
  Optional, no constraint if not set
  """
  minPrice: String

  """
  Maximum trading price - constraints Limit and StopLimit Orders
  Optional, no constraint if not set
  """
  maxPrice: String

  """
  Minimum price increment
  - constraints Limit and StopLimit Orders
  - defines tickSize in OrderBook
  """
  priceIncr: String

  """
  Minimum notional value that should be used for trading
  Typically it can be calculated by multiplying ContractSize * NotionalPrice (Note: For FOREX Spot, the contract size is always 1 = baseCurrency quantity)
  For more details, see: https://www.investopedia.com/terms/n/notionalvalue.asp
  """
  minNotional: String

  """
  Contract size
  """
  contractSize: String

  """
  Defines whether this Instrument can be traded or not
  """
  tradeable: Boolean
}

type ForexSpotPropertiesResponse {
  baseCurrency: String!
}
### / INSTRUMENT ###



### TRANSACTION ###
type RootExecution {
  orderId: String!
  executionId: String!
}

interface Trade {
  dateTime: String!
  uuid: String!
  updatedAt: Float!
  executionId: String!
  venueExecutionId: String
  fee: Float!
  feeCurrency: String!
  description: String!
  quantity: Float!
  price: Float!
  currency: String!
  intOrderId: String!
  extOrderId: String!
  settled: String!
  settledDateTime: Float
}

type ClientCashTrade implements Trade {
  dateTime: String!
  uuid: String!
  updatedAt: Float!
  executionId: String!
  venueExecutionId: String
  fee: Float!
  feeCurrency: String!
  description: String!
  quantity: Float!
  price: Float!
  currency: String!
  intOrderId: String!
  extOrderId: String!
  settled: String!
  settledDateTime: Float
  orderId: String!
  portfolioId: String!
  portfolioName: String!

  baseCurrency: String!
  counterPortfolioId: String!
  counterPortfolioName: String!

  rootExecution: RootExecution
  rootOrderId: String
  underlyingExecutionId: String
  parentOrderId: String
}

type StreetCashTrade implements Trade {
  dateTime: String!
  uuid: String!
  updatedAt: Float!
  executionId: String!
  venueExecutionId: String
  fee: Float!
  feeCurrency: String!
  description: String!
  quantity: Float!
  price: Float!
  currency: String!
  intOrderId: String!
  extOrderId: String!
  settled: String!
  settledDateTime: Float
  orderId: String!
  portfolioId: String!
  portfolioName: String!

  baseCurrency: String!
  venueAccount: String!
  venueAccountName: String!

  rootExecution: RootExecution
  rootOrderId: String
  underlyingExecutionId: String
  parentOrderId: String
}

type ClientAssetTrade implements Trade {
  dateTime: String!
  uuid: String!
  updatedAt: Float!
  executionId: String!
  venueExecutionId: String
  fee: Float!
  feeCurrency: String!
  description: String!
  quantity: Float!
  price: Float!
  currency: String!
  intOrderId: String!
  extOrderId: String!
  settled: String!
  settledDateTime: Float
  orderId: String!
  portfolioId: String!
  portfolioName: String!

  instrument: InstrumentResponse
  counterPortfolioId: String!
  counterPortfolioName: String!

  rootExecution: RootExecution
}

type StreetAssetTrade implements Trade {
  dateTime: String!
  uuid: String!
  updatedAt: Float!
  executionId: String!
  venueExecutionId: String
  fee: Float!
  feeCurrency: String!
  description: String!
  quantity: Float!
  price: Float!
  currency: String!
  intOrderId: String!
  extOrderId: String!
  settled: String!
  settledDateTime: Float
  orderId: String!
  portfolioId: String!
  portfolioName: String!

  instrument: InstrumentResponse
  venueAccount: String!
  venueAccountName: String!

  rootOrderId: String
}


type Withdrawal {
  dateTime: String!
  uuid: String!
  updatedAt: Float!
  executionId: String!
  venueExecutionId: String
  description: String!
  quantity: Float!
  currency: String!
  portfolioId: String!
  portfolioName: String!
  account: String!
  accountName: String!
  settled: String!
  settledDateTime: Float
  feeAccountId: String
  feeAccountName: String
  feePortfolioId: String
  feePortfolioName: String
}

type Deposit {
  dateTime: String!
  uuid: String!
  updatedAt: Float!
  executionId: String!
  venueExecutionId: String
  description: String!
  quantity: Float!
  currency: String!
  portfolioId: String!
  portfolioName: String!
  account: String!
  accountName: String!
  settled: String!
  settledDateTime: Float
  feeAccountId : String
  feeAccountName: String
  feePortfolioId: String
  feePortfolioName: String
}

type AccountCashTransfer {
  dateTime: String!
  uuid: String!
  updatedAt: Float!
  executionId: String!
  venueExecutionId: String
  description: String!
  quantity: Float!
  currency: String!
  sourceAccountId: String!
  sourceAccountName: String!
  targetAccountId: String!
  targetAccountName: String!
  settled: String!
  settledDateTime: Float
  feeAccountId : String
  feeAccountName: String
  feePortfolioId: String
  feePortfolioName: String
}

type PortfolioCashTransfer {
  dateTime: String!
  uuid: String!
  updatedAt: Float!
  executionId: String!
  venueExecutionId: String
  description: String!
  quantity: Float!
  currency: String!
  sourcePortfolioId: String!
  sourcePortfolioName: String!
  targetPortfolioId: String!
  targetPortfolioName: String!
  settled: String!
  settledDateTime: Float
  feePortfolioId: String
  feePortfolioName: String
}

type Settlement {
  dateTime: String!
  uuid: String!
  updatedAt: Float!
  description: String!
  settledTransactionIds: [String]!
}

type Fee {
  dateTime: String!
  uuid: String!
  updatedAt: Float!
  executionId: String!
  venueExecutionId: String
  description: String!
  quantity: Float!
  currency: String!
  portfolioId: String!
  portfolioName: String!
  account: String!
  accountName: String!
  settled: String!
  settledDateTime: Float
  orderId: String
  parentOrderId: String
  underlyingExecutionId: String
  rootExecution: RootExecution
}

type TransactionConnection {
  edges: [TransactionEdge!]!
  pageInfo: PageInfo!
}

type TransactionEdge {
  node: TransactionResponse!
  cursor: String!
}

union TransactionResponse = ClientCashTrade | StreetCashTrade | ClientAssetTrade | StreetAssetTrade | Withdrawal | Deposit | AccountCashTransfer | PortfolioCashTransfer | Settlement | Fee
### / TRANSACTION ###

type PageInfo {
  hasNextPage: Boolean!

  """
  Can be null if page is empty, otherwise non-null
  """
  endCursor: String
}

enum SortingOrder {
  ASC,
  DESC
}

type MutationSubmittedResponse {
  status: String!
}

type KeyValue {
  key: String!
  value: String!
}

input KeyValueInput {
  key: String!
  value: String!
}

enum Scope {
  READ
  TRADE
  MANAGE
  CREATE
}