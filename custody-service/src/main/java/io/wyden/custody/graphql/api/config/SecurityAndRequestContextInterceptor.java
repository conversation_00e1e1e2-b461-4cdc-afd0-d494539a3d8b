package io.wyden.custody.graphql.api.config;

import org.jetbrains.annotations.NotNull;
import org.springframework.graphql.server.WebGraphQlInterceptor;
import org.springframework.graphql.server.WebGraphQlRequest;
import org.springframework.graphql.server.WebGraphQlResponse;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
public class SecurityAndRequestContextInterceptor implements WebGraphQlInterceptor {

    public static final String GQL_REQUEST_CONTEXT_KEY = "gqlRequest";
    public static final String GQL_REQUEST_AUTHENTICATION_KEY = "gqlAuth";

    @Override
    public @NotNull Mono<WebGraphQlResponse> intercept(@NotNull WebGraphQlRequest request, @NotNull Chain chain) {

        return ReactiveSecurityContextHolder.getContext()
            .map(SecurityContext::getAuthentication)
            .flatMap(auth -> {
                request.configureExecutionInput((execInput, builder) ->
                    builder.graphQLContext(ctxBuilder -> ctxBuilder
                        .of(GQL_REQUEST_AUTHENTICATION_KEY, auth)
                        .of(GQL_REQUEST_CONTEXT_KEY, request)
                    ).build()
                );
                return chain.next(request);
            });
    }
}
