package io.wyden.custody.graphql.api.exception;

import graphql.GraphQLError;
import graphql.GraphqlErrorBuilder;
import graphql.schema.DataFetchingEnvironment;
import org.springframework.graphql.execution.ErrorType;

public class UnknownGqlExceptionHandler extends AbstractGqlExceptionHandler {

    protected UnknownGqlExceptionHandler() {
        super(null);
    }

    @Override
    public GraphQLError handle(Throwable ex, DataFetchingEnvironment env) {
        String message = String.format("Caught unhandled exception in GQL Controller - propagating to user as INTERNAL_ERROR. Root: %s", ex.getMessage());
        LOGGER.error(message, ex);

        return GraphqlErrorBuilder.newError()
            .message(message)
            .errorType(ErrorType.INTERNAL_ERROR)
            .build();
    }
}
