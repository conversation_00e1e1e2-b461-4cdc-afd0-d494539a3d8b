package io.wyden.custody.graphql.api.exception;

import graphql.ErrorClassification;
import graphql.GraphQLError;
import graphql.GraphqlErrorBuilder;
import graphql.schema.DataFetchingEnvironment;

public class DefaultGqlExceptionHandler extends AbstractGqlExceptionHandler {

    protected DefaultGqlExceptionHandler(ErrorClassification errorClassification) {
        super(errorClassification);
    }

    @Override
    public GraphQLError handle(Throwable ex, DataFetchingEnvironment env) {

        LOGGER.error(ex.getMessage(), ex);

        return GraphqlErrorBuilder.newError()
            .errorType(errorClassification)
            .message(ex.getMessage())
            .path(env.getExecutionStepInfo().getPath())
            .location(env.getField().getSourceLocation())
            .build();
    }
}
