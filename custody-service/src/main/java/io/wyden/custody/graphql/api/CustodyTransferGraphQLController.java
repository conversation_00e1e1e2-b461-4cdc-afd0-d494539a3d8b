package io.wyden.custody.graphql.api;

import io.wyden.custody.graphql.api.model.MutationSubmittedResponse;
import io.wyden.custody.graphql.api.model.TransferRequest;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

@Controller
public class CustodyTransferGraphQLController {

    @MutationMapping("transfer")
    public Mono<MutationSubmittedResponse> transfer(@Argument TransferRequest input) {
        return Mono.just(new MutationSubmittedResponse("ok"));
    }
}
