package io.wyden.custody.graphql.api.config;

import com.nimbusds.jwt.SignedJWT;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.Date;
import java.util.Map;

@Component
public class CustomJwtDecoder implements JwtDecoder {

    @Override
    public Jwt decode(String token) {
        try {
            SignedJWT signedJWT = SignedJWT.parse(token);
            return Jwt.withTokenValue(token)
                .header("alg", signedJWT.getHeader().getAlgorithm().getName())
                .claims(claims -> {
                    Map<String, Object> originalClaims;
                    try {
                        originalClaims = signedJWT.getJWTClaimsSet().getClaims();
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }

                    // Convert any Date-based claims to Instant
                    originalClaims.forEach((key, value) -> {
                        if (value instanceof Date) {
                            claims.put(key, ((Date) value).toInstant());
                        } else {
                            claims.put(key, value);
                        }
                    });
                })
                .build();

        } catch (ParseException e) {
            throw new JwtException("JWT parsing problem", e);
        }
    }
}