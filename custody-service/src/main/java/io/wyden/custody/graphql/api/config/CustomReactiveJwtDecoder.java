package io.wyden.custody.graphql.api.config;

import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.security.oauth2.jwt.ReactiveJwtDecoder;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
public class CustomReactiveJwtDecoder implements ReactiveJwtDecoder {

    private final CustomJwtDecoder jwtDecoder;

    public CustomReactiveJwtDecoder(CustomJwtDecoder jwtDecoder) {
        this.jwtDecoder = jwtDecoder;
    }

    @Override
    public Mono<Jwt> decode(String token) {
        return Mono.fromCallable(() -> {
            try {
                return jwtDecoder.decode(token);
            } catch (Exception e) {
                throw new JwtException("Invalid JWT token", e);
            }
        });
    }
}