package io.wyden.custody.graphql.api.exception;

import graphql.ErrorClassification;
import graphql.GraphQLError;
import graphql.GraphqlErrorBuilder;
import graphql.schema.DataFetchingEnvironment;
import io.wyden.custody.permission.WydenAuthenticationToken;

import java.util.Map;

public class AccessDeniedGqlExceptionHandler extends AbstractGqlExceptionHandler {

    protected AccessDeniedGqlExceptionHandler(ErrorClassification errorClassification) {
        super(errorClassification);
    }

    @Override
    public GraphQLError handle(Throwable ex, DataFetchingEnvironment env) {

        String user = getToken(env).map(WydenAuthenticationToken::getClientId).orElse("null");
        String variables = getRequest(env).map(r -> r.getVariables().toString()).orElse("null");
        String message = getAccessDeniedMessage(env)
            .orElse(String.format("Access denied for user: %s, path: %s", user, env.getExecutionStepInfo().getPath()));

        LOGGER.warn("{}, variables: {}", message, variables);

        return GraphqlErrorBuilder.newError()
            .errorType(errorClassification)
            .message(message)
            .path(env.getExecutionStepInfo().getPath())
            .location(env.getField().getSourceLocation())
            .extensions(Map.of(
                "user", user,
                "variables", variables))
            .build();
    }

}
