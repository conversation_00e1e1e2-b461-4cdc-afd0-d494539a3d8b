package io.wyden.custody.graphql.api.exception;

import graphql.ErrorClassification;
import graphql.GraphQLError;
import graphql.schema.DataFetchingEnvironment;
import io.wyden.custody.permission.WydenAuthenticationToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.graphql.server.WebGraphQlRequest;

import java.util.Optional;

import static io.wyden.custody.graphql.api.config.SecurityAndRequestContextInterceptor.GQL_REQUEST_AUTHENTICATION_KEY;
import static io.wyden.custody.graphql.api.config.SecurityAndRequestContextInterceptor.GQL_REQUEST_CONTEXT_KEY;
import static io.wyden.custody.permission.AccessDeniedLogger.ACCESS_DENIED_MESSAGE_REQUEST_ATTRIBUTE_KEY;

public abstract class AbstractGqlExceptionHandler {

    protected static final Logger LOGGER = LoggerFactory.getLogger(AbstractGqlExceptionHandler.class);
    protected final ErrorClassification errorClassification;

    protected AbstractGqlExceptionHandler(ErrorClassification errorClassification) {
        this.errorClassification = errorClassification;
    }

    public abstract GraphQLError handle(Throwable ex, DataFetchingEnvironment env);

    protected Optional<WydenAuthenticationToken> getToken(DataFetchingEnvironment env) {
        return Optional.ofNullable(env.getGraphQlContext().get(GQL_REQUEST_AUTHENTICATION_KEY));
    }

    protected Optional<WebGraphQlRequest> getRequest(DataFetchingEnvironment env) {
        return Optional.ofNullable(env.getGraphQlContext().get(GQL_REQUEST_CONTEXT_KEY));
    }

    protected Optional<String> getAccessDeniedMessage(DataFetchingEnvironment env) {
        return Optional.ofNullable(env.getGraphQlContext().get(ACCESS_DENIED_MESSAGE_REQUEST_ATTRIBUTE_KEY));
    }
}
