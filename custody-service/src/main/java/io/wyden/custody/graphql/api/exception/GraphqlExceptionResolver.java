package io.wyden.custody.graphql.api.exception;

import graphql.GraphQLError;
import graphql.schema.DataFetchingEnvironment;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.wyden.accessgateway.client.license.InvalidLicenseException;
import io.wyden.accessgateway.client.rest.AccessDeniedException;
import io.wyden.accessgateway.client.rest.AccessForbiddenException;
import org.jetbrains.annotations.NotNull;
import org.springframework.graphql.execution.DataFetcherExceptionResolverAdapter;
import org.springframework.graphql.execution.ErrorType;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.oauth2.jwt.JwtValidationException;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.NoSuchElementException;

@Component
public class GraphqlExceptionResolver extends DataFetcherExceptionResolverAdapter {

    private static final Map<Class<? extends Throwable>, AbstractGqlExceptionHandler> handlersMap = new HandlersRegistryBuilder()
        .register(new AccessDeniedGqlExceptionHandler(ErrorType.UNAUTHORIZED),
            AccessDeniedException.class,
            org.springframework.security.access.AccessDeniedException.class,
            JwtValidationException.class)
        .register(new DefaultGqlExceptionHandler(ErrorType.FORBIDDEN),
            InvalidLicenseException.class,
            LockedException.class,
            AccessForbiddenException.class)
        .register(new DefaultGqlExceptionHandler(ErrorType.NOT_FOUND),
            NoSuchElementException.class,
            WebClientResponseException.NotFound.class)
        .build();


    @Override
    protected GraphQLError resolveToSingleError(@NotNull Throwable ex, @NotNull DataFetchingEnvironment env) {

        Span.current().setStatus(StatusCode.ERROR);
        Span.current().recordException(ex);

        return getHandler(ex).handle(ex, env);

    }

    private AbstractGqlExceptionHandler getHandler(Throwable ex) {
        return handlersMap.entrySet().stream()
            .filter(entry -> entry.getKey() == ex.getClass())
            .map(Map.Entry::getValue)
            .findFirst()
            .orElse(new UnknownGqlExceptionHandler());
    }

    private static class HandlersRegistryBuilder {
        private final Map<Class<? extends Throwable>, AbstractGqlExceptionHandler> map = new LinkedHashMap<>();

        @SafeVarargs
        final HandlersRegistryBuilder register(AbstractGqlExceptionHandler handler, Class<? extends Throwable>... clazz) {
            Arrays.stream(clazz).forEach(c -> map.put(c, handler));
            return this;
        }

        public Map<Class<? extends Throwable>, AbstractGqlExceptionHandler> build() {
            return map;
        }
    }
}
