package io.wyden.custody.infrastructure.security;

import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.accessgateway.client.permission.PermissionChecker;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static io.wyden.accessgateway.client.permission.Permission.Scopes.CREATE;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.MANAGE;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.READ;
import static io.wyden.accessgateway.client.permission.Permission.Scopes.TRADE;

@Service
public class AccessService implements AccessGatewayFacade {

    private final PermissionChecker permissionChecker;

    public AccessService(PermissionChecker permissionChecker) {
        this.permissionChecker = permissionChecker;
    }

    @Override
    public boolean hasPermission(User user, String resource, String scope) {
        return permissionChecker.hasPermission(user.roles(), user.groups(), user.clientId(), resource, scope);
    }

    @Override
    public boolean hasPermission(User user, Permission permission) {
        return permissionChecker.hasPermission(user.roles(), user.groups(), user.clientId(), permission.getResource(), permission.getScope());
    }

    @Override
    public boolean hasPermission(User user, String resource, String scope, Collection<String> resourceIds) {
        return resourceIds.stream()
            .allMatch(resourceId -> hasPermission(user, resource, scope, resourceId));
    }

    @Override
    public boolean hasPermission(User user, Permission permission, Collection<String> resourceIds) {
        return resourceIds.stream()
            .allMatch(resourceId -> hasPermission(user, permission, resourceId));
    }

    @Override
    public boolean hasPermission(User user, String resource, String scope, String resourceId) {
        return permissionChecker.hasPermission(user.roles(), user.groups(), user.clientId(), resource, scope, resourceId);
    }

    @Override
    public boolean hasPermission(User user, Permission permission, String resourceId) {
        return permissionChecker.hasPermission(user.roles(), user.groups(), user.clientId(), permission.getResource(), permission.getScope(), resourceId);
    }

    @Override
    public boolean hasUserPermission(User user, Permission permission) {
        return permissionChecker.hasUserPermission(user.clientId(), permission.getResource(), permission.getScope());
    }

    @Override
    public boolean hasGroupPermission(User user, Permission permission) {
        return user.groups().stream()
            .anyMatch(group -> permissionChecker.hasGroupPermission(group, permission.getResource(), permission.getScope()));
    }

    @Override
    public <T> Stream<T> filterByPermission(User user, String resource, String scope, Function<T, String> getid, Stream<T> resourceStream) {
        if (permissionChecker.hasPermission(user.roles(), user.groups(), user.clientId(), resource, scope)) {
            return resourceStream;
        } else {
            return resourceStream
                .filter(r -> permissionChecker.hasPermission(user.roles(), user.groups(), user.clientId(), resource, scope, getid.apply(r)));
        }
    }

    @Override
    public <T> Stream<T> filterByPermissions(User user, String resource, Set<String> scopes, Function<T, String> getid, Stream<T> resourceStream) {
        return resourceStream.filter(r -> scopes.stream().anyMatch(scope ->
            permissionChecker.hasPermission(user.roles(), user.groups(), user.clientId(), resource, scope, getid.apply(r))));
    }

    @Override
    public Set<String> getScopes(User user, String resource) {
        return Stream.of(READ, TRADE, MANAGE, CREATE)
            .filter(scope -> hasPermission(user, resource, scope))
            .collect(Collectors.toSet());
    }

    @Override
    public Set<String> getScopes(User user, String resource, String resourceId) {
        return Stream.of(READ, TRADE, MANAGE, CREATE)
            .filter(scope -> hasPermission(user, resource, scope, resourceId))
            .collect(Collectors.toSet());
    }

    @Override
    public Set<String> getUserScopes(String username, String resource, String resourceId) {
        return Stream.of(READ, TRADE, MANAGE, CREATE)
            .filter(scope -> permissionChecker.hasUserPermission(username, resource, scope, resourceId))
            .collect(Collectors.toSet());
    }

    @Override
    public Set<String> getGroupScopes(String groupname, String resource, String resourceId) {
        return Stream.of(READ, TRADE, MANAGE, CREATE)
            .filter(scope -> permissionChecker.hasGroupPermission(groupname, resource, scope, resourceId))
            .collect(Collectors.toSet());
    }

    public Set<String> getGroupsScopes(Collection<String> groups, String resource, String resourceId) {
        return groups.stream()
            .flatMap(group -> getGroupScopes(group, resource, resourceId).stream())
            .collect(Collectors.toSet());
    }

    @Override
    public Set<String> getDynamicScopes(User user, String resource, String resourceId) {
        return Stream.concat(
                getGroupsScopes(user.groups(), resource, resourceId).stream(),
                getUserScopes(user.clientId(), resource, resourceId).stream())
            .collect(Collectors.toSet());
    }

}
