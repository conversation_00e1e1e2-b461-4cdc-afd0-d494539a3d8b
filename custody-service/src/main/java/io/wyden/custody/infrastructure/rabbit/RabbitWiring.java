package io.wyden.custody.infrastructure.rabbit;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.TimeoutThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Configuration
public class RabbitWiring {

    @Bean
    public RabbitIntegrator rabbitIntegrator(@Value("${rabbitmq.username}") String userName,
                                             @Value("${rabbitmq.password}") String password,
                                             @Value("${rabbitmq.virtualHost}") String virtualHost,
                                             @Value("${rabbitmq.host}") String host,
                                             @Value("${rabbitmq.port}") int port,
                                             @Value("${rabbitmq.tls}") String tls,
                                             ExecutorService consumptionExecutor,
                                             ExecutorService publishingExecutor) {
        return new RabbitIntegrator(userName, password, virtualHost, host, port, tls, consumptionExecutor, publishingExecutor);
    }

    @Bean("consumptionExecutor")
    public ExecutorService consumptionExecutor() {
        return TimeoutThreadPoolExecutor.newSingleThreadExecutor(5, TimeUnit.SECONDS);
    }

    @Bean("publishingExecutor")
    public ExecutorService publishingExecutor() {
        return TimeoutThreadPoolExecutor.newSingleThreadExecutor(5, TimeUnit.SECONDS);
    }

}
