package io.wyden.custody.infrastructure.security;

import io.wyden.accessgateway.client.permission.Permission;

import java.util.Collection;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Stream;

public interface AccessGatewayFacade {
    boolean hasPermission(User user, String resource, String scope);

    boolean hasPermission(User user, Permission permission);

    boolean hasPermission(User user, String resource, String scope, Collection<String> resourceIds);

    boolean hasPermission(User user, Permission permission, Collection<String> resourceIds);

    boolean hasPermission(User user, String resource, String scope, String resourceId);

    boolean hasPermission(User user, Permission permission, String resourceId);

    boolean hasUserPermission(User user, Permission permission);

    boolean hasGroupPermission(User user, Permission permission);

    <T> Stream<T> filterByPermission(User user, String resource, String scope, Function<T, String> getid, Stream<T> resourceStream);

    <T> Stream<T> filterByPermissions(User user, String resource, Set<String> scopes, Function<T, String> getid, Stream<T> resourceStream);

    Set<String> getScopes(User user, String resource);

    Set<String> getScopes(User user, String resource, String resourceId);

    Set<String> getUserScopes(String username, String resource, String resourceId);

    Set<String> getGroupScopes(String groupname, String resource, String resourceId);

    Set<String> getDynamicScopes(User user, String resource, String resourceId);

}
