package io.wyden.custody.permission;

import graphql.schema.DataFetchingEnvironment;
import io.wyden.accessgateway.client.permission.Permission;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class AccessDeniedLogger {

    public static final String ACCESS_DENIED_MESSAGE_REQUEST_ATTRIBUTE_KEY = "access-denied-message";

    private AccessDeniedLogger() { //hide
    }

    public static void logIfAccessDenied(boolean permissionDecision, DataFetchingEnvironment env, WydenAuthenticationToken token, Permission permission, String resource) {
        logIfAccessDenied(permissionDecision, env, token.getClientId(), permission, resource);
    }

    public static void logIfAccessDenied(boolean permissionDecision, DataFetchingEnvironment env, WydenAuthenticationToken token, Permission permission, List<String> resources) {
        String resourcesStr = "[" + String.join(",", resources) + "]";
        logIfAccessDenied(permissionDecision, env, token.getClientId(), permission, resourcesStr);
    }

    public static void logIfAccessDenied(boolean permissionDecision, DataFetchingEnvironment env, WydenAuthenticationToken token, String permission, String resource) {
        logIfAccessDenied(permissionDecision, env, token.getClientId(), permission, resource);
    }

    public static void logIfAccessDenied(boolean permissionDecision, DataFetchingEnvironment env, String userName, Permission permission, String resource) {
        String perm = permission.getResource() + "." + permission.getScope();
        logIfAccessDenied(permissionDecision, env, userName, perm, resource);
    }

    private static void logIfAccessDenied(boolean permissionDecision, DataFetchingEnvironment env, String userName, String permission, String resource) {
        if (!permissionDecision) {
            String path = env != null ? env.getExecutionStepInfo().getPath().toString() : "";
            if (StringUtils.isNotEmpty(resource)) {
                String message = String.format("Access denied for user: %s, path: %s, error: missing %s for resource %s",
                    userName, path, permission, resource);
                addAccessDeniedMessageToContextAttribute(env, message);
            } else {
                String message = String.format(
                    "Access denied for user: %s, path: %s, error: missing %s",
                    userName, path, permission);
                addAccessDeniedMessageToContextAttribute(env, message);
            }
        }
    }

    private static void addAccessDeniedMessageToContextAttribute(DataFetchingEnvironment env, String message) {
        if(env != null && env.getGraphQlContext() != null) {
            env.getGraphQlContext().put(ACCESS_DENIED_MESSAGE_REQUEST_ATTRIBUTE_KEY, message);
        }
    }
}
