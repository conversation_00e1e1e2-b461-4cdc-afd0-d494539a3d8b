package io.wyden.custody.permission;

import graphql.schema.DataFetchingEnvironment;
import io.wyden.custody.infrastructure.security.AccessGatewayFacade;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collection;
import javax.annotation.Nullable;

import static io.wyden.custody.infrastructure.security.User.user;
import static io.wyden.custody.permission.AccessDeniedLogger.logIfAccessDenied;

@Component("defaultPermissionValidator")
public class DefaultPermissionValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultPermissionValidator.class);

    private final AccessGatewayFacade accessGatewayFacade;

    public DefaultPermissionValidator(AccessGatewayFacade accessGatewayFacade) {
        this.accessGatewayFacade = accessGatewayFacade;
    }

    public boolean hasPermission(Object resourceObject, Object scopeObject, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        try {
            String resource = toResource(resourceObject);
            String scope = toScope(scopeObject);
            boolean result = accessGatewayFacade.hasPermission(user(token), resource, scope);
            logIfAccessDenied(result, env, token, resource + "." +scope, "");
            return result;
        } catch (Exception ex) {
            LOGGER.warn("Permission check failed, returning Access Denied", ex);
            return false;
        }
    }

    public boolean hasPermission(@Nullable Serializable targetId, String resource, Object scopeObject, WydenAuthenticationToken token, DataFetchingEnvironment env) {
        try {
            if (targetId == null) {
                return hasPermission(resource, scopeObject, token, env);
            } else if (targetId instanceof Collection<?> collection) {
                return collection.stream()
                    .allMatch(t -> hasPermission(toSerializable(t), resource, scopeObject, token, env));
            } else {
                String scope = toScope(scopeObject);
                String resourceId = String.valueOf(targetId);
                boolean result = accessGatewayFacade.hasPermission(user(token), resource, scope, resourceId);
                logIfAccessDenied(result, env, token, resource + "." +scope, resourceId);
                return result;
            }
        } catch (Exception ex) {
            LOGGER.warn("Permission check failed, returning Access Denied", ex);
            return false;
        }
    }

    private String toResource(Object resourceObject) {
        return objectToString("Resource", resourceObject);
    }

    private String toScope(Object scopeObject) {
        return objectToString("Scope", scopeObject);
    }

    private String objectToString(String name, Object resourceObject) {
        if (resourceObject == null) {
            throw new IllegalArgumentException("%s cannot be null".formatted(name));
        } else if (resourceObject instanceof String resourceString) {
            return resourceString;
        } else {
            throw new IllegalArgumentException("%s %s is not a string".formatted(name, resourceObject.getClass().getSimpleName()));
        }
    }

    private Serializable toSerializable(Object object) {
        if (object == null) {
            throw new IllegalArgumentException("ResourceId cannot be null");
        } else if (object instanceof Serializable serializable) {
            return serializable;
        } else {
            throw new IllegalArgumentException("ResourceId %s is not a Serializable".formatted(object.getClass().getSimpleName()));
        }
    }
}
