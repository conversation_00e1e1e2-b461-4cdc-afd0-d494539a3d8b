package io.wyden.custody.graphql;

import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.graphql.tester.AutoConfigureHttpGraphQlTester;
import org.springframework.graphql.test.tester.GraphQlTester;
import org.springframework.graphql.test.tester.HttpGraphQlTester;
import org.springframework.graphql.test.tester.WebSocketGraphQlTester;
import org.springframework.web.reactive.socket.client.ReactorNettyWebSocketClient;
import org.springframework.web.reactive.socket.client.WebSocketClient;
import reactor.core.publisher.Flux;
import reactor.netty.http.client.HttpClient;
import reactor.netty.http.client.WebsocketClientSpec;

import java.util.Map;

@AutoConfigureHttpGraphQlTester
public class GraphQLTestBase extends TestContainersIntegrationBase {

    @Autowired
    protected HttpGraphQlTester httpGraphQlTester;

    protected WebSocketGraphQlTester webSocketGraphQlTester;

    @Value("ws://localhost:${local.server.port}${spring.graphql.websocket.path}")
    private String baseUrl;

    @BeforeEach
    void testerClientInit() {
        WebSocketClient client = new ReactorNettyWebSocketClient((HttpClient.newConnection().compress(true)),
            () -> WebsocketClientSpec.builder().maxFramePayloadLength(65536 * 10_000));
        webSocketGraphQlTester = WebSocketGraphQlTester.builder(baseUrl, client)
            .build();
    }

    protected GraphQlTester.Response executeFile(String documentName, Map<String, Object> variables) {
        GraphQlTester.Request<?> request = httpGraphQlTester.documentName(documentName);
        variables.forEach(request::variable);
        return request.execute();
    }

    protected Flux<GraphQlTester.Response> executeFileSubscription(String document, Map<String, Object> variables) {
        GraphQlTester.Request<?> request = webSocketGraphQlTester.documentName(document);
        variables.forEach(request::variable);
        return request.executeSubscription().toFlux();
    }

}
