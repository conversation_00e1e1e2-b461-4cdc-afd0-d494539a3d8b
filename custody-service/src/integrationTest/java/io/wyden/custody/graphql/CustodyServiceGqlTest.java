package io.wyden.custody.graphql;

import io.wyden.custody.utils.WithMockCustomUser;
import org.junit.jupiter.api.Test;
import org.springframework.graphql.test.tester.GraphQlTester;

import java.util.Map;

class CustodyServiceGqlTest extends GraphQLTestBase {

    @WithMockCustomUser
    @Test
    void transfer_shouldSucceed() {
        Map<String, Object> input = Map.of(
            "asset", "BTC",
            "source", "portfolio1",
            "destination", "portfolio2",
            "quantity", "2",
            "notes", "note",
            "priority", "HIGH"
        );

        GraphQlTester.Response response = executeFile("mutationTransfer", Map.of("input", input));
        response.errors().verify();
    }
}

