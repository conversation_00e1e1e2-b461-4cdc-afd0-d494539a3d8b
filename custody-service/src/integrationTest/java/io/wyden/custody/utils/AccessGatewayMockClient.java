package io.wyden.custody.utils;

import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.custody.infrastructure.security.AccessGatewayFacade;
import io.wyden.custody.infrastructure.security.User;

import java.util.Collection;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class AccessGatewayMockClient implements AccessGatewayFacade {

    private final Set<PermissionDto> permissions;

    public AccessGatewayMockClient(Set<PermissionDto> permissions) {
        this.permissions = permissions;
    }

    @Override
    public boolean hasPermission(User user, String resource, String scope) {
        return permissions.contains(new PermissionDto(resource, scope));
    }

    @Override
    public boolean hasPermission(User user, Permission permission) {
        return permissions.contains(new PermissionDto(permission.getResource(), permission.getScope(), null));
    }

    @Override
    public boolean hasPermission(User user, String resource, String scope, Collection<String> resourceIds) {
        return resourceIds.stream()
            .allMatch(resourceId -> hasPermission(user, resource, scope, resourceId));
    }

    @Override
    public boolean hasPermission(User user, Permission permission, Collection<String> resourceIds) {
        return resourceIds.stream()
            .allMatch(resourceId -> hasPermission(user, permission, resourceId));
    }

    @Override
    public boolean hasPermission(User user, String resource, String scope, String resourceId) {
        return permissions.contains(new PermissionDto(resource, scope, null))
            || permissions.contains(new PermissionDto(resource, scope, resourceId));
    }

    @Override
    public boolean hasPermission(User user, Permission permission, String resourceId) {
        return permissions.contains(new PermissionDto(permission.getResource(), permission.getScope(), null))
        || permissions.contains(new PermissionDto(permission.getResource(), permission.getScope(), resourceId));
    }

    @Override
    public boolean hasUserPermission(User user, Permission permission) {
        return hasPermission(user, permission);
    }

    @Override
    public boolean hasGroupPermission(User user, Permission permission) {
        return hasPermission(user, permission);
    }

    @Override
    public <T> Stream<T> filterByPermission(User user, String resource, String scope, Function<T, String> getid, Stream<T> resourceStream) {
        return resourceStream.filter(r -> permissions.contains(new PermissionDto(resource, scope, getid.apply(r))));
    }

    @Override
    public <T> Stream<T> filterByPermissions(User user, String resource, Set<String> scopes, Function<T, String> getid, Stream<T> resourceStream) {
        return resourceStream.filter(r -> scopes.stream()
            .anyMatch(scope -> hasPermission(user, resource, scope, getid.apply(r))
            ));
    }

    @Override
    public Set<String> getScopes(User user, String resource) {
        return permissions.stream().map(PermissionDto::getScope).collect(Collectors.toSet());
    }

    @Override
    public Set<String> getScopes(User user, String resource, String resourceId) {
        return permissions.stream()
            .filter(p -> p.getResource().equals(resource) && p.getResourceId().equals(resourceId))
            .map(PermissionDto::getScope).collect(Collectors.toSet());
    }

    @Override
    public Set<String> getUserScopes(String username, String resource, String resourceId) {
        return permissions.stream()
            .filter(p -> p.getResource().equals(resource) && p.getResourceId().equals(resourceId))
            .map(PermissionDto::getScope).collect(Collectors.toSet());
    }

    @Override
    public Set<String> getGroupScopes(String groupname, String resource, String resourceId) {
        return Set.of();
    }

    @Override
    public Set<String> getDynamicScopes(User user, String resource, String resourceId) {
        return permissions.stream()
            .filter(p -> p.getResource().equals(resource) && p.getResourceId() != null && p.getResourceId().equals(resourceId))
            .map(PermissionDto::getScope).collect(Collectors.toSet());
    }

    public Collection<PermissionDto> revokePermissions() {
        permissions.clear();
        return permissions;
    }

    public Collection<PermissionDto> grantPermissions(PermissionDto permission) {
        this.permissions.add(permission);
        return this.permissions;
    }

    public Collection<PermissionDto> grantPermissions(Collection<PermissionDto> permissions) {
        revokePermissions();
        this.permissions.addAll(permissions);
        return this.permissions;
    }

}
