plugins {
    id 'java'
    id 'idea'
    id "jacoco"
    id 'maven-publish'
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

repositories {
    def nexusNpm = ivy {
        //	example full url 'https://repo.wyden.io/nexus/repository/npm-snapshots/@algotrader/schema-graphql/-/schema-graphql-1.0.0.tgz'
        url 'https://repo.wyden.io/nexus/repository/npm-snapshots/'
        patternLayout {
            artifact '/[organization]/[module]/-/[artifact]-[revision].[ext]'
        }
        // This is required in Gradle 6.0+ as metadata file (ivy.xml) is mandatory.
        // https://docs.gradle.org/6.2/userguide/declaring_repositories.html#sec:supported_metadata_sources
        metadataSources { artifact() }
        credentials {
            username repository_username
            password repository_password
        }
    }
    exclusiveContent {
        forRepositories(nexusNpm)
        filter { includeGroup("@algotrader") }
    }
}

def props = new Properties()
file('src/main/resources/application.properties').withInputStream { props.load(it) }
def schemaGraphqlVersion = props.getProperty('schema.graphql.version')

dependencies {
    compileOnly "@algotrader:schema-graphql:${schemaGraphqlVersion}@tgz"

    implementation project(':custody-service-domain')
    implementation dependencyCatalog.cloud.utils.rabbitmq
    implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.spring
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.cloud.utils.hazelcast

    implementation dependencyCatalog.spring.boot.starter.webflux
    implementation dependencyCatalog.spring.boot.starter.actuator
    implementation dependencyCatalog.spring.boot.starter.validation
    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.hazelcast.jet.protobuf

    implementation dependencyCatalog.spring.security.test
    implementation dependencyCatalog.spring.boot.starter.oauth2.resource.server

    implementation dependencyCatalog.access.gateway.client
    implementation dependencyCatalog.access.gateway.domain

    implementation dependencyCatalog.spring.boot.starter.graphql

    testImplementation dependencyCatalog.cloud.utils.spring
    testImplementation dependencyCatalog.spring.boot.starter.test
    testImplementation dependencyCatalog.spring.rabbit.test
    testImplementation dependencyCatalog.reactor.test
    testImplementation dependencyCatalog.testcontainers
    testImplementation dependencyCatalog.testcontainers.junit.jupiter
    testImplementation dependencyCatalog.mockito.core
    testImplementation dependencyCatalog.mockito.junit.jupiter
    testImplementation dependencyCatalog.okhttp
    testImplementation dependencyCatalog.mockwebserver
    testImplementation dependencyCatalog.cloud.utils.test

    runtimeOnly(dependencyCatalog.netty.resolver.dns.native.macos) { artifact { classifier = 'osx-aarch_64'} }
}

task addSchemaToResources(type: Copy) {
    def tarPath = project.configurations.compileClasspath.find { it.name.startsWith("schema-graphql") }
    println tarPath
    def outputGqlDir = "${sourceSets.main.resources.srcDirs[0]}/graphql/"
    def outputGqlVersionDir = "${outputGqlDir}${schemaGraphqlVersion}"
    println outputGqlVersionDir

    println "Removing old .graphqls files:  ${outputGqlDir}"
    delete "${outputGqlDir}"

    //extract schemas from artefact
    def tarFile = file(tarPath)
    into "${outputGqlVersionDir}"
    //remove package directory
    eachFile { file ->
        if (file.path.startsWith("package/")) {
            file.path = file.path.replaceFirst("^package/", "")
        }
    }

    from tarTree(tarFile)
    include '**/schema.graphql'
    include '**/common.graphql'
    include '**/custody.graphql'
}

processResources {
    dependsOn addSchemaToResources
}


testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {

                implementation project()
                implementation dependencyCatalog.cloud.utils.hazelcast
                implementation dependencyCatalog.cloud.utils.test
                implementation dependencyCatalog.spring.boot.starter.test
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.testcontainers.rabbitmq
                implementation dependencyCatalog.published.language.oems
                implementation dependencyCatalog.reactor.test
                implementation dependencyCatalog.cloud.utils.rabbitmq
                implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
                implementation dependencyCatalog.cloud.utils.telemetry
                implementation dependencyCatalog.cloud.utils.spring
                implementation dependencyCatalog.spring.boot.starter.webflux
                implementation dependencyCatalog.cloud.utils.tools
                implementation dependencyCatalog.mockwebserver

                implementation dependencyCatalog.spring.graphql.test
                implementation dependencyCatalog.spring.boot.starter.graphql

                implementation dependencyCatalog.access.gateway.client
                implementation dependencyCatalog.access.gateway.domain

                implementation dependencyCatalog.spring.security.test
                implementation dependencyCatalog.spring.boot.starter.oauth2.resource.server

                implementation dependencyCatalog.hazelcast
                implementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }

                implementation dependencyCatalog.testcontainers.postgresql
            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${archiveVersion}"
        )
    }
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9055"]
    environment([
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev"
    ])
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}

sonarqube {
    properties {
        property "sonar.projectKey", "custody-service"
        property "sonar.projectName", "Custody Service"
    }
}

test {
    finalizedBy jacocoTestReport
}

jacocoTestReport {
    reports {
        xml.required = true
        csv.required = true
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}


import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

tasks.withType(Test) {
    testLogging {
        info {
            events TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED
        }
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_OUT,
                    TestLogEvent.STANDARD_ERROR
            exceptionFormat TestExceptionFormat.FULL
            showExceptions true
            showCauses true
            showStackTraces true
            showStandardStreams true
        }

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}
