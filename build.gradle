plugins {
    id 'java'
    id 'java-library'
    id "jacoco"
    id 'maven-publish'
    id 'application'
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
    buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
}

group = 'wyden.io'
version = "0.9.0$buildVersion"
description = 'aeron-exchange-core${buildVersion}'

application {
    mainClass = 'io.wyden.aeron.AeronExchangeCoreApplication'
    applicationDefaultJvmArgs = [
            '--add-exports=java.base/jdk.internal.ref=ALL-UNNAMED',
            '--add-exports=java.base/sun.nio.ch=ALL-UNNAMED',
            '--add-exports=jdk.unsupported/sun.misc=ALL-UNNAMED',
            '--add-exports=java.base/sun.nio.ch=ALL-UNNAMED',
            '--add-exports=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED',
            '--add-opens=java.base/java.lang=ALL-UNNAMED',
            '--add-opens=java.base/java.lang.reflect=ALL-UNNAMED',
            '--add-opens=java.base/java.io=ALL-UNNAMED',
            '--add-opens=java.base/java.util=ALL-UNNAMED',
            '--add-opens=java.base/sun.nio.ch=ALL-UNNAMED'
    ]
}

java {
    sourceCompatibility = '17'
    targetCompatibility = '17'
}

sonarqube {
    properties {
        property "sonar.projectKey", "aeron-exchange-core"
        property "sonar.projectName", "Aeron Matching Engine"
    }
}

repositories {
    mavenLocal()
    maven {
        name 'nexus-releases'
        url 'https://repo.wyden.io/nexus/repository/releases/'
        credentials {
            username = repository_username
            password = repository_password
        }
    }
    maven {
        name 'nexus-snapshot'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username = repository_username
            password = repository_password
        }
    }
    mavenCentral()
}

dependencies {
    api dependencyCatalog.aeron.all
    api dependencyCatalog.aeron.agent
    api dependencyCatalog.agrona
    api dependencyCatalog.slf4j.api
    api dependencyCatalog.exchange.core
    api dependencyCatalog.chronicle.wire.ea13
    api 'ch.qos.logback:logback-core:1.4.14'
    api 'ch.qos.logback:logback-classic:1.4.14'
    api dependencyCatalog.hdrhistogram
    api dependencyCatalog.sbe
    api dependencyCatalog.spring.boot.starter
    api dependencyCatalog.spring.boot.starter.web
    api dependencyCatalog.spring.boot.starter.actuator
    api dependencyCatalog.cloud.utils.telemetry
    api dependencyCatalog.cloud.utils.spring
    testRuntimeOnly dependencyCatalog.junit.jupiter.engine
}

task copyDependencies(type: Copy) {
    from configurations.runtimeClasspath
    into "$buildDir/libs"
}


bootDistTar {
    dependsOn copyDependencies
}

bootDistZip {
    dependsOn copyDependencies
}

bootStartScripts {
    dependsOn copyDependencies
}

startScripts {
    dependsOn copyDependencies
}

bootRun {
    jvmArgs = [
            '--add-exports=java.base/jdk.internal.ref=ALL-UNNAMED',
            '--add-exports=java.base/sun.nio.ch=ALL-UNNAMED',
            '--add-exports=jdk.unsupported/sun.misc=ALL-UNNAMED',
            '--add-exports=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED',
            '--add-exports=java.base/sun.nio.ch=ALL-UNNAMED',
            '--add-opens=java.base/java.lang=ALL-UNNAMED',
            '--add-opens=java.base/java.lang.reflect=ALL-UNNAMED',
            '--add-opens=java.base/java.io=ALL-UNNAMED',
            '--add-opens=java.base/java.util=ALL-UNNAMED',
            '--add-opens=java.base/sun.nio.ch=ALL-UNNAMED'
    ]
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {
                implementation project()
                implementation dependencyCatalog.cloud.utils.test
                implementation dependencyCatalog.spring.boot.starter.test
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.slf4j.api
            }
        }
    }
}

test {
    finalizedBy jacocoTestReport
}

jacocoTestReport {
    reports {
        xml.required.set(true)
        csv.required.set(true)
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

tasks.withType(Test) {
    testLogging {
        info {
            events TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED
        }
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_OUT,
                    TestLogEvent.STANDARD_ERROR
            exceptionFormat TestExceptionFormat.FULL
            showExceptions true
            showCauses true
            showStackTraces true
            showStandardStreams true
        }

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}
