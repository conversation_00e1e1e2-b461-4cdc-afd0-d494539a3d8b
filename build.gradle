plugins {
    id 'java'
    id 'idea'
    id 'jacoco'
    id 'org.sonarqube' version '6.0.1.5171'
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.jacocoToCobertura
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
    buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
}

group = 'io.wyden.orderhistory'
version = "0.13.0$buildVersion"

targetCompatibility = '21'
sourceCompatibility = '21'

repositories {
    mavenLocal()
    maven {
        name 'nexus-releases'
        url 'https://repo.wyden.io/nexus/repository/releases/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    maven {
        name 'nexus-snapshot'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    mavenCentral()
}

dependencies {

    implementation dependencyCatalog.cloud.utils.rabbitmq
    implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.tools
    implementation dependencyCatalog.cloud.utils.rest
    implementation dependencyCatalog.cloud.utils.spring
    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.rest.api.domain
    implementation dependencyCatalog.reference.data.client
    implementation dependencyCatalog.reference.data.domain

    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.hazelcast.jet.protobuf

    implementation dependencyCatalog.spring.boot.starter.webflux
    implementation dependencyCatalog.spring.boot.starter.actuator
    implementation dependencyCatalog.spring.boot.starter.validation
    implementation dependencyCatalog.spring.boot.starter.data.jpa

    implementation dependencyCatalog.flyway.core
    implementation dependencyCatalog.postgresql

    testImplementation dependencyCatalog.cloud.utils.spring
    testImplementation dependencyCatalog.cloud.utils.test
    testImplementation dependencyCatalog.cloud.utils.tools
    testImplementation dependencyCatalog.spring.boot.starter.test
    testImplementation dependencyCatalog.spring.rabbit.test
    testImplementation dependencyCatalog.mockito.core
    testImplementation dependencyCatalog.mockito.junit.jupiter
    testImplementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }

    testImplementation project(path: ':')

    runtimeOnly(dependencyCatalog.netty.resolver.dns.native.macos) { artifact { classifier = 'osx-aarch_64'} }
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {
                implementation project(':')
                implementation dependencyCatalog.cloud.utils.rabbitmq
                implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
                implementation dependencyCatalog.cloud.utils.telemetry
                implementation dependencyCatalog.cloud.utils.test
                implementation dependencyCatalog.published.language.oems

                implementation dependencyCatalog.spring.boot.starter.test
                implementation dependencyCatalog.spring.boot.starter.webflux
                implementation dependencyCatalog.spring.boot.starter.data.jpa

                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.testcontainers.rabbitmq
                implementation dependencyCatalog.testcontainers.postgresql
                implementation dependencyCatalog.reactor.test
                implementation dependencyCatalog.javafaker
                implementation dependencyCatalog.cloud.utils.hazelcast
                implementation dependencyCatalog.hazelcast
                implementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }
                implementation dependencyCatalog.reference.data.client

            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

test {
    jvmArgs '--enable-preview'
    useJUnitPlatform()
    finalizedBy jacocoTestReport
}

bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${archiveVersion}"
        )
    }
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9040"]
    environment([
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev"
    ])
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}

sonarqube {
    properties {
        property "sonar.projectKey", "order-history"
        property "sonar.projectName", "Order History"
    }
}

jacocoTestReport {
    reports {
        xml.required.set(true)
        csv.required.set(true)
    }
    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

tasks.withType(Test) {
    testLogging {
        info {
            events TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED
        }
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_OUT,
                    TestLogEvent.STANDARD_ERROR
            exceptionFormat TestExceptionFormat.FULL
            showExceptions true
            showCauses true
            showStackTraces true
            showStandardStreams true
        }

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}
