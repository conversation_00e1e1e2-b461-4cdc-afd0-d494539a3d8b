package io.wyden.brokerconfig;

import com.hazelcast.config.Config;
import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.published.brokerdesk.PortfolioGroupConfig;
import jakarta.annotation.Nullable;

import static io.wyden.cloudutils.hazelcast.Serializers.protobufSerializer;

public class PortfolioGroupBrokerConfigMapConfig extends HazelcastMapConfig {

    public static final String BROKER_CONFIG_PORTFOLIO_GROUP_V_0_1 = "broker-config-portfolio-group_v0.1";

    public static IMap<String, PortfolioGroupConfig> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(BROKER_CONFIG_PORTFOLIO_GROUP_V_0_1);
    }

    @Override
    public String getMapName() {
        return BROKER_CONFIG_PORTFOLIO_GROUP_V_0_1;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
        serializationConfig.addSerializerConfig(protobufSerializer(PortfolioGroupConfig.class, PortfolioGroupConfig.parser()));
    }

    @Override
    protected void addMapConfig(Config config, @Nullable MapStoreConfig mapStoreConfig) {
        super.addMapConfig(config, mapStoreConfig);
    }
}
