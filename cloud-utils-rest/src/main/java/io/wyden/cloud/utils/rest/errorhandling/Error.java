package io.wyden.cloud.utils.rest.errorhandling;

import java.time.Instant;

public record Error(
    Instant timestamp,
    int status,
    String error,
    String message,
    String path,
    String exception,
    String stackTrace
) {
    public String toSafeString() {
        return "Error[" +
            "timestamp=" + timestamp +
            ", status=" + status +
            ", error='" + error + '\'' +
            ", message='" + message + '\'' +
            ", path='" + path + '\'' +
            ']';
    }
}

