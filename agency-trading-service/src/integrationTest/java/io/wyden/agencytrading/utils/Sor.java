package io.wyden.agencytrading.utils;

import com.google.protobuf.Message;
import io.wyden.agencytrading.infrastructure.rabbit.RabbitDestinations;
import io.wyden.cloud.utils.test.ExchangeObserver;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.destination.TradingMessageParser;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.Metadata;
import io.wyden.published.common.MetadataUtil;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsResponseUtil;
import io.wyden.published.oems.OemsTargetType;

import java.time.ZonedDateTime;
import java.util.Map;
import java.util.UUID;

import static io.wyden.cloudutils.rabbitmq.destination.OemsTarget.SOR;
import static org.assertj.core.api.Assertions.assertThat;

public class Sor {

    private final RabbitDestinations rabbitDestinations;
    private final TestingData testingData;

    private final ExchangeObserver<Message> tradingRouterExchange;

    public Sor(TestingData testingData, RabbitDestinations destinations, RabbitIntegrator rabbitIntegrator) {
        this.testingData = testingData;
        this.rabbitDestinations = destinations;

        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName(),
            OemsHeader.TARGET_TYPE.getHeaderName(), OemsTargetType.SOR.name()
        );
        tradingRouterExchange = ExchangeObserver.newBuilder(rabbitIntegrator, destinations.getTradingRouterExchange(), TradingMessageParser.parser(), "oems-request-queue")
            .withHeaders(headers)
            .build();
        tradingRouterExchange.attach();
    }

    public void emitExecutionReportNew(OemsRequest request) {
        OemsResponse executionReport = executionReportBuilder(request)
            .setExecType(OemsExecType.NEW)
            .setOrderStatus(OemsOrderStatus.STATUS_NEW)
            .build();

        emit(executionReport);
    }

    public void emitExecutionReportCancelled(OemsRequest request) {
        OemsResponse executionReport = executionReportBuilder(request)
            .setExecType(OemsExecType.CANCELED)
            .setOrderStatus(OemsOrderStatus.STATUS_CANCELED)
            .build();

        emit(executionReport);
    }

    private void emit(OemsResponse executionReport) {
        RabbitExchange<Message> exchange = rabbitDestinations.getTradingIngressExchange();

        Map<String, String> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName(),
            OemsHeader.SOURCE.getHeaderName(), executionReport.getMetadata().getSource(),
            OemsHeader.SOURCE_TYPE.getHeaderName(), executionReport.getMetadata().getSourceType().name(),
            OemsHeader.TARGET.getHeaderName(), executionReport.getMetadata().getTarget(),
            OemsHeader.TARGET_TYPE.getHeaderName(), executionReport.getMetadata().getTargetType().name()
        );

        exchange.publishWithHeaders(executionReport, headers);
    }

    public void cleanup() {
        tradingRouterExchange.detach();
    }

    public OemsRequest awaitNewChildOrderRequest(String parentOrderId) {
        Message message = tradingRouterExchange.awaitMessage(m -> m instanceof OemsRequest && parentOrderId.equals(((OemsRequest) m).getParentOrderId()));
        OemsRequest oemsRequest = (OemsRequest) message;
        assertThat(oemsRequest.getRequestType()).isEqualTo(OemsRequest.OemsRequestType.ORDER_SINGLE);
        assertThat(oemsRequest.getMetadata().getTarget()).isNotBlank();
        return oemsRequest;
    }

    public OemsRequest awaitCancelRequest() {
        Message message = tradingRouterExchange.awaitMessage(m -> m instanceof OemsRequest && ((OemsRequest) m).getRequestType() == OemsRequest.OemsRequestType.CANCEL);
        OemsRequest oemsRequest = (OemsRequest) message;
        assertThat(oemsRequest.getMetadata().getTarget()).isNotBlank();
        return oemsRequest;
    }

    private OemsResponse.Builder executionReportBuilder(OemsRequest request) {
        String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());

        Metadata metadata = MetadataUtil.asResponseTo(request.getMetadata())
            .setResponseId(UUID.randomUUID().toString())
            .setSource(SOR.getTarget())
            .setSourceType(Metadata.ServiceType.SOR)
            .build();

        return OemsResponseUtil.asResponseToRequest(request)
            .setMetadata(metadata)
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setInstrumentType(request.getInstrumentType())
            .setBaseCurrency(request.getBaseCurrency())
            .setQuoteCurrency(request.getQuoteCurrency())
            .setOrderQty(request.getQuantity())
            .setLeavesQty(testingData.quantity)
            .setCumQty("0")
            .setLastQty("0")
            .setLastPrice("0")
            .setAvgPrice("0")
            .setSystemTimestamp(now)
            .setVenueTimestamp(now);
    }
}

