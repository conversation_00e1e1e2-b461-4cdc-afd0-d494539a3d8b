package io.wyden.agencytrading.utils;

import com.google.protobuf.Message;
import io.wyden.agencytrading.infrastructure.rabbit.RabbitDestinations;
import io.wyden.cloud.utils.test.ExchangeObserver;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.destination.TradingMessageParser;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.FeeData;
import io.wyden.published.oems.OemsExecRestatementReason;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsTargetType;

import java.util.Map;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

public class Collider {

    private final RabbitDestinations rabbitDestinations;
    private final TestingData testingData;

    private final ExchangeObserver<Message> tradingRouterExchange;

    public Collider(TestingData testingData, RabbitDestinations destinations, RabbitIntegrator rabbitIntegrator) {
        this.testingData = testingData;
        this.rabbitDestinations = destinations;

        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName(),
            OemsHeader.TARGET_TYPE.getHeaderName(), OemsTargetType.EXTERNAL_VENUE_ACCOUNT.name()
        );
        tradingRouterExchange = ExchangeObserver.newBuilder(rabbitIntegrator, destinations.getTradingRouterExchange(), TradingMessageParser.parser(), "oems-request-queue")
            .withHeaders(headers)
            .build();
        tradingRouterExchange.attach();
    }

    public void emitExecutionReportNew(OemsRequest request) {
        OemsResponse executionReport = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setSource(request.getVenueAccount())
                .setSourceType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT)
                .setTarget(request.getMetadata().getSource())
                .setTargetType(request.getMetadata().getSourceType())
                .build())
            .setExecutionId(UUID.randomUUID().toString())
            .setPortfolioId(request.getPortfolioId())
            .setVenueAccount(request.getVenueAccount())
            .setOrderStatus(OemsOrderStatus.STATUS_NEW)
            .setExecType(OemsExecType.NEW)
            .setOrderId(request.getOrderId())
            .setParentOrderId(request.getParentOrderId())
            .setInstrumentId(request.getInstrumentId())
            .setCumQty("0")
            .setLastQty("0")
            .setAvgPrice("0")
            .setLeavesQty(testingData.quantity)
            .build();

        emit(executionReport);
    }

    public void emitExecutionReportFilled(OemsRequest request) {
        OemsResponse executionReport = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setSource(request.getVenueAccount())
                .setSourceType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT)
                .setTarget(request.getMetadata().getSource())
                .setTargetType(request.getMetadata().getSourceType())
                .build())
            .setExecutionId(UUID.randomUUID().toString())
            .setPortfolioId(request.getPortfolioId())
            .setVenueAccount(request.getVenueAccount())
            .setOrderStatus(OemsOrderStatus.STATUS_FILLED)
            .setExecType(OemsExecType.FILL)
            .setOrderId(request.getOrderId())
            .setParentOrderId(request.getParentOrderId())
            .setInstrumentId(request.getInstrumentId())
            .setLastPrice(testingData.executionPrice)
            .setCumQty(testingData.quantity)
            .setLastQty(testingData.quantity)
            .setLeavesQty("0")
            .build();

        emit(executionReport);
    }

    public void emitExecutionReportCalculated(OemsRequest request, FeeData feeData) {
        OemsResponse executionReport = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setSource(request.getVenueAccount())
                .setSourceType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT)
                .setTarget(request.getMetadata().getSource())
                .setTargetType(request.getMetadata().getSourceType())
                .build())
            .setExecutionId(UUID.randomUUID().toString())
            .setPortfolioId(request.getPortfolioId())
            .setVenueAccount(request.getVenueAccount())
            .setOrderStatus(OemsOrderStatus.STATUS_NEW)
            .setExecType(OemsExecType.CALCULATED)
            .setOrderId(request.getOrderId())
            .setParentOrderId(request.getParentOrderId())
            .setInstrumentId(request.getInstrumentId())
            .setLastPrice("0")
            .setCumQty("0")
            .setLastQty("0")
            .setLeavesQty(testingData.quantity)
            .addFeeData(feeData)
            .build();

        emit(executionReport);
    }

    public void emitExecutionReportRestated(OemsRequest request, OemsExecRestatementReason restatementReason) {
        OemsResponse executionReport = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setSource(request.getVenueAccount())
                .setSourceType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT)
                .setTarget(request.getMetadata().getSource())
                .setTargetType(request.getMetadata().getSourceType())
                .build())
            .setExecutionId(UUID.randomUUID().toString())
            .setPortfolioId(request.getPortfolioId())
            .setVenueAccount(request.getVenueAccount())
            .setOrderStatus(OemsOrderStatus.STATUS_NEW)
            .setExecType(OemsExecType.RESTATED)
            .setExecRestatementReason(restatementReason)
            .setOrderId(request.getOrderId())
            .setParentOrderId(request.getParentOrderId())
            .setInstrumentId(request.getInstrumentId())
            .setLastPrice("0")
            .setCumQty("0")
            .setLastQty("0")
            .setOrderQty(testingData.quantityRestated)
            .setLeavesQty(testingData.quantityRestated)
            .build();

        emit(executionReport);
    }

    public void emitExecutionReportFilled(OemsRequest request, String lastQty, String cumQty) {
        OemsResponse executionReport = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setSource(request.getVenueAccount())
                .setSourceType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT)
                .setTarget(request.getMetadata().getSource())
                .setTargetType(request.getMetadata().getSourceType())
                .build())
            .setExecutionId(UUID.randomUUID().toString())
            .setPortfolioId(request.getPortfolioId())
            .setVenueAccount(request.getVenueAccount())
            .setOrderStatus(OemsOrderStatus.STATUS_FILLED)
            .setExecType(OemsExecType.FILL)
            .setOrderId(request.getOrderId())
            .setParentOrderId(request.getParentOrderId())
            .setInstrumentId(request.getInstrumentId())
            .setLastPrice(testingData.executionPrice)
            .setCumQty(cumQty)
            .setLastQty(lastQty)
            .setLeavesQty("0")
            .build();

        emit(executionReport);
    }

    public void emitExecutionReportPartiallyFilled(OemsRequest request, String cumQty, String lastQty, String leavesQty) {
        OemsResponse executionReport = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setSource(request.getVenueAccount())
                .setSourceType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT)
                .setTarget(request.getMetadata().getSource())
                .setTargetType(request.getMetadata().getSourceType())
                .build())
            .setExecutionId(UUID.randomUUID().toString())
            .setPortfolioId(request.getPortfolioId())
            .setVenueAccount(request.getVenueAccount())
            .setOrderStatus(OemsOrderStatus.STATUS_PARTIALLY_FILLED)
            .setExecType(OemsExecType.PARTIAL_FILL)
            .setOrderId(request.getOrderId())
            .setParentOrderId(request.getParentOrderId())
            .setInstrumentId(request.getInstrumentId())
            .setLastPrice(testingData.executionPrice)
            .setCumQty(cumQty)
            .setLastQty(lastQty)
            .setLeavesQty(leavesQty)
            .build();

        emit(executionReport);
    }

    public void emitExecutionReportCancelled(OemsRequest request) {
        OemsResponse executionReport = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setSource(request.getVenueAccount())
                .setSourceType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT)
                .setTarget(request.getMetadata().getSource())
                .setTargetType(request.getMetadata().getSourceType())
                .build())
            .setExecutionId(UUID.randomUUID().toString())
            .setPortfolioId(request.getPortfolioId())
            .setVenueAccount(request.getVenueAccount())
            .setOrderStatus(OemsOrderStatus.STATUS_CANCELED)
            .setExecType(OemsExecType.CANCELED)
            .setOrderId(request.getOrderId())
            .setParentOrderId(request.getParentOrderId())
            .setCumQty("0")
            .setLastQty("0")
            .setAvgPrice("0")
            .setLeavesQty(testingData.quantity)
            .build();

        emit(executionReport);
    }

    private void emit(OemsResponse executionReport) {
        RabbitExchange<Message> exchange = rabbitDestinations.getTradingIngressExchange();

        Map<String, String> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName(),
            OemsHeader.SOURCE.getHeaderName(), executionReport.getMetadata().getSource(),
            OemsHeader.SOURCE_TYPE.getHeaderName(), executionReport.getMetadata().getSourceType().name(),
            OemsHeader.TARGET.getHeaderName(), executionReport.getMetadata().getTarget(),
            OemsHeader.TARGET_TYPE.getHeaderName(), executionReport.getMetadata().getTargetType().name()
        );

        exchange.publishWithHeaders(executionReport, headers);
    }

    public void cleanup() {
        tradingRouterExchange.detach();
    }

    public OemsRequest awaitNewChildOrderRequest(String parentOrderId) {
        Message message = tradingRouterExchange.awaitMessage(m -> m instanceof OemsRequest && parentOrderId.equals(((OemsRequest) m).getParentOrderId()));
        OemsRequest oemsRequest = (OemsRequest) message;
        assertThat(oemsRequest.getRequestType()).isEqualTo(OemsRequest.OemsRequestType.ORDER_SINGLE);
        assertThat(oemsRequest.getMetadata().getTarget()).isNotBlank();
        return oemsRequest;
    }

    public OemsRequest awaitCancelRequest() {
        Message message = tradingRouterExchange.awaitMessage(m -> m instanceof OemsRequest && ((OemsRequest) m).getRequestType() == OemsRequest.OemsRequestType.CANCEL);
        OemsRequest oemsRequest = (OemsRequest) message;
        assertThat(oemsRequest.getMetadata().getTarget()).isNotBlank();
        return oemsRequest;
    }

    public void emitCancelReject(Metadata parentOrderMetadata, String orderId, OemsOrderStatus currentStatus) {
        OemsResponse cancelReject = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.CANCEL_REJECT)
            .setParentOrderId(orderId)
            .setReason("Too late")
            .setOrderStatus(currentStatus)
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setInResponseToRequestId(testingData.cancelRequestId)
                .setSource(parentOrderMetadata.getTarget())
                .setSourceType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT)
                .setTarget(parentOrderMetadata.getSource())
                .setTargetType(parentOrderMetadata.getSourceType())
                .build())
            .build();

        emit(cancelReject);
    }
}
