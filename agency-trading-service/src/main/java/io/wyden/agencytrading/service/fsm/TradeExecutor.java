package io.wyden.agencytrading.service.fsm;

import io.wyden.agencytrading.model.Execution;
import io.wyden.agencytrading.service.tracking.FeeConfigMapper;
import io.wyden.agencytrading.service.tracking.FixedFeeConfig;
import io.wyden.agencytrading.service.tracking.MinFeeConfig;
import io.wyden.agencytrading.service.tracking.OrderFeeProcessor;
import io.wyden.agencytrading.service.tracking.OrderMarkupProcessor;
import io.wyden.agencytrading.service.tracking.PercFeeConfig;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.brokerdesk.TernaryBool;
import io.wyden.published.oems.FeeData;
import io.wyden.published.oems.FeeType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static io.wyden.agencytrading.utils.BigDecimalUtils.bd;

@Component
public class TradeExecutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradeExecutor.class);

    public final OrderFeeProcessor orderFeeProcessor;
    public final OrderMarkupProcessor orderMarkupProcessor;

    public TradeExecutor(OrderFeeProcessor orderFeeProcessor, OrderMarkupProcessor orderMarkupProcessor) {
        this.orderFeeProcessor = orderFeeProcessor;
        this.orderMarkupProcessor = orderMarkupProcessor;
    }

    void applyExecution(OrderState orderState, OemsResponse report) {
        OemsResponse.ExecutionReference.Builder underlyingExecution = OemsResponse.ExecutionReference.newBuilder()
            .setExecutionId(report.getExecutionId())
            .setOrderId(report.getOrderId())
            .setVenueAccount(report.getVenueAccount())
            .setInstrumentId(report.getInstrumentId());
        OemsResponse.ExecutionReference rootExecution = report.getRootExecution();
        OemsRequest oemsRequest = orderState.getOemsRequest();

        String lastQty = report.getLastQty();
        String lastPrice = report.getLastPrice();
        String lastPriceWithMarkup = orderMarkupProcessor.calculateMarkup(oemsRequest.getInstrumentId(), oemsRequest.getSide(), oemsRequest.getPricingConfig().getMarkup(), lastPrice);

        BigDecimal execQty = new BigDecimal(lastQty);

        BigDecimal filledQty = orderState.getFilledQuantity().add(execQty);
        BigDecimal remainingQty = orderState.getRemainingQuantity().subtract(execQty).max(BigDecimal.ZERO);

        ExecutionConfig executionConfig = oemsRequest.getExecutionConfig();

        PercFeeConfig percFeeConfig = FeeConfigMapper.mapToPercFeeConfig(executionConfig);
        FixedFeeConfig fixedFeeConfig = FeeConfigMapper.mapToFixedFeeConfig(executionConfig, orderState.getFixedFeeCharged());
        MinFeeConfig minFeeConfig = FeeConfigMapper.mapToMinFeeConfig(executionConfig);

        List<FeeData> feeData = getFeeData(orderState, report, percFeeConfig, minFeeConfig, execQty, lastPriceWithMarkup, executionConfig, fixedFeeConfig);

        Execution execution = Execution.newBuilder()
            .setExecutionId(UUID.randomUUID().toString())
            .setUnderlyingExecution(underlyingExecution)
            .setRootExecution(rootExecution)
            .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setFee(aggregateFeeData(feeData))
            .setFeeCurrency(report.getQuoteCurrency())
            .addAllFeeData(feeData)
            .setQuantity(lastQty)
            .setPrice(lastPrice)
            .setPriceWithMarkup(lastPriceWithMarkup)
            .setOrderId(oemsRequest.getOrderId())
            .setTarget(report.getMetadata().getTarget())
            .setInstrumentId(report.getInstrumentId())
            .build();
        orderState.addExecution(execution);

        BigDecimal avgPrice = orderState.recalculateAvgPrice();
        BigDecimal avgPriceWithMarkup = orderState.recalculateAvgPriceWithMarkup();

        boolean hasRemainingQty = remainingQty.compareTo(BigDecimal.ZERO) > 0;
        OrderStatus orderStatus = hasRemainingQty ? StatusPartialFill.create() : StatusFilled.create();
        orderState.addOrderStatus(orderStatus);
        orderState.setRemainingQuantity(remainingQty);
        orderState.setFilledQuantity(filledQty);
        orderState.setAvgPrice(avgPrice);
        orderState.setAvgPriceWithMarkup(avgPriceWithMarkup);
        orderState.setFixedFeeCharged(true);
        updateTotalFeeCharged(orderState, percFeeConfig, minFeeConfig, feeData);
        LOGGER.debug("totalFeeCharged: {}", orderState.getTotalFeeCharged().toPlainString());
    }

    private void updateTotalFeeCharged(OrderState orderState, PercFeeConfig percFeeConfig, MinFeeConfig minFeeConfig, List<FeeData> feeData) {
        String percFeeCurrency = OrderFeeProcessor.resolvePercFeeCurrency(orderState.getOemsRequest(), percFeeConfig.currencyType(), percFeeConfig.currency());
        BigDecimal totalFeeCharged = calculateTotalFeeCharged(orderState.getTotalFeeCharged(), percFeeCurrency, minFeeConfig, feeData);
        orderState.setTotalFeeCharged(totalFeeCharged);
    }

    private List<FeeData> getFeeData(OrderState orderState, OemsResponse report, PercFeeConfig percFeeConfig, MinFeeConfig minFeeConfig, BigDecimal execQty, String lastPriceWithMarkup, ExecutionConfig executionConfig, FixedFeeConfig fixedFeeConfig) {
        String percFeeCurrency = OrderFeeProcessor.resolvePercFeeCurrency(orderState.getOemsRequest(), percFeeConfig.currencyType(), percFeeConfig.currency());
        BigDecimal minFeeInPercFeeCurrency = orderFeeProcessor.calculateMinFeeInPercFeeCurrency(minFeeConfig, percFeeCurrency);
        BigDecimal percFeeInPercFeeCurrency = orderFeeProcessor.calculatePercFeeInPercFeeCurrency(percFeeConfig, orderState.getOemsRequest().getQuoteCurrency(), execQty, lastPriceWithMarkup);

        orderState.setTotalPercFee(orderState.getTotalPercFee().add(percFeeInPercFeeCurrency));
        LOGGER.debug("execQty: {}, lastPriceWithMarkup: {}, percFeeInPercFeeCurrency: {}, minFeeInPercFeeCurrency: {}, totalPercFee: {}", execQty.toPlainString(), lastPriceWithMarkup, percFeeInPercFeeCurrency.toPlainString(), minFeeInPercFeeCurrency.toPlainString(), orderState.getTotalPercFee());
        return orderFeeProcessor.calculateFees(orderState.getTotalPercFee(), percFeeInPercFeeCurrency, orderState.getTotalFeeCharged(), percFeeCurrency, executionConfig.getChargeExchangeFee(), report.getFeeDataList(), minFeeInPercFeeCurrency, fixedFeeConfig);
    }

    //TODO tkow - deprecated, should be removed when only new fee model is used
    private static String aggregateFeeData(List<FeeData> feeData) {
        Optional<BigDecimal> feeSum = feeData.stream()
            .map(feeData1 -> bd(feeData1.getAmount()))
            .reduce(BigDecimal::add);

        return feeSum.map(BigDecimal::toPlainString).orElse("");
    }

    private BigDecimal calculateTotalFeeCharged(BigDecimal totalFeeCharged, String percFeeCurrency, MinFeeConfig minFeeConfig, List<FeeData> feeData) {
        Optional<BigDecimal> feeSum = feeData.stream()
            .filter(feeData1 -> feeData1.getType() == FeeType.TRANSACTION_FEE)
            .map(feeData1 -> {
                if (feeData1.getCurrency().equals(minFeeConfig.currency())) {
                    return orderFeeProcessor.calculateMinFeeInPercFeeCurrency(minFeeConfig, percFeeCurrency);
                }
                return bd(feeData1.getAmount());
            })
            .reduce(BigDecimal::add);

        if (feeSum.isPresent()) {
            totalFeeCharged = totalFeeCharged.add(feeSum.get());
        }
        return totalFeeCharged;
    }

    /**
     * @return true, if street fee was charged to client
     */
    public boolean applyFeeOnlyExecution(OrderState orderState, OemsResponse report) {
        OemsResponse.ExecutionReference.Builder underlyingExecution = OemsResponse.ExecutionReference.newBuilder()
            .setExecutionId(report.getExecutionId())
            .setOrderId(report.getOrderId())
            .setVenueAccount(report.getVenueAccount())
            .setInstrumentId(orderState.getOemsRequest().getInstrumentId());
        OemsResponse.ExecutionReference rootExecution = report.getRootExecution();
        OemsRequest oemsRequest = orderState.getOemsRequest();
        String lastQty = "0";
        String lastPrice = "0";
        BigDecimal filledQty = orderState.getFilledQuantity();
        BigDecimal remainingQty = orderState.getRemainingQuantity();
        TernaryBool chargeExchangeFee = oemsRequest.getExecutionConfig().getChargeExchangeFee();
        List<FeeData> feeData = report.getFeeDataList();

        if (chargeExchangeFee == TernaryBool.TRUE && !feeData.isEmpty()) {
            Execution execution = Execution.newBuilder()
                .setExecutionId(UUID.randomUUID().toString())
                .setUnderlyingExecution(underlyingExecution)
                .setRootExecution(rootExecution)
                .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setFee(aggregateFeeData(feeData))
                .setFeeCurrency(report.getQuoteCurrency())
                .addAllFeeData(feeData)
                .setQuantity(lastQty)
                .setPrice(lastPrice)
                .setPriceWithMarkup(lastPrice)
                .setOrderId(oemsRequest.getOrderId())
                .setTarget(report.getMetadata().getTarget())
                .setInstrumentId(report.getInstrumentId())
                .build();
            orderState.addExecution(execution);

            return true;
        } else {
            return false;
        }
    }
}
