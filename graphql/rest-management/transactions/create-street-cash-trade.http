### Create street cash trade
< {%
  import { sign } from "../signature";

  const { signature, nonce } = sign(request)
  request.variables.set("signature", signature);
  request.variables.set("nonce", nonce);
%}

POST {{RestManagementUrl}}/api/v1/transactions
Content-Type: application/json
X-API-KEY: {{ApiKey}}
X-API-NONCE: {{nonce}}
X-API-SIGNATURE: {{signature}}

{
  "transactionType": "STREET_CASH_TRADE",
  "reservationRef": "slawek-street-cash-trade-1",
  "dateTime": {{$timestamp}}000,
  "baseCurrency": "BTC",
  "currency": "USDT",
  "description": "slawek-street-cash-trade-1",
  "executionId": "slawek-street-cash-trade-1",
  "venueExecutionId": "slawek-street-cash-trade-1",
  "settled": false,
  "orderId": "slawek-street-cash-trade-1",
  "parentOrderId": null,
  "quantity": 0.001,
  "leavesQuantity": 0,
  "price": 50000,
  "portfolioId": "Slawek2",
  "accountId": "simulator",
  "fees": [
    {
      "amount": 0,
      "currency": "USDT",
      "feeType": "EXCHANGE_FEE"
    }
  ]
}
