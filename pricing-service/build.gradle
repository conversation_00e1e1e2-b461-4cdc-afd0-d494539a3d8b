plugins {
    id 'java'
    id 'idea'
    id 'jacoco'
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

repositories {
    mavenLocal()
    maven {
        name 'nexus-snapshots'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    mavenCentral()
    maven { url 'https://repo.spring.io/milestone' }
}

dependencyManagement {
    resolutionStrategy {
        cacheChangingModulesFor 0, 'seconds'
    }
}

dependencies {
    implementation project(':pricing-service-client')
    implementation project(':pricing-service-domain')
    implementation dependencyCatalog.cloud.utils.rabbitmq
    implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.tools
    implementation dependencyCatalog.cloud.utils.spring
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.rate.service.client

    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.hazelcast.jet.protobuf
    implementation dependencyCatalog.cloud.utils.hazelcast

    implementation dependencyCatalog.spring.boot.starter.webflux
    implementation dependencyCatalog.spring.boot.starter.actuator

    testImplementation dependencyCatalog.cloud.utils.test
    testImplementation dependencyCatalog.spring.boot.starter.test
    testImplementation dependencyCatalog.junit.jupiter.api
    testImplementation dependencyCatalog.reactor.test

    runtimeOnly(dependencyCatalog.netty.resolver.dns.native.macos) { artifact { classifier = 'osx-aarch_64'} }

    testRuntimeOnly dependencyCatalog.junit.jupiter.engine
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {
                implementation project()
                implementation project(':pricing-service-client')
                implementation project(':pricing-service-domain')

                implementation dependencyCatalog.published.language.oems
                implementation dependencyCatalog.cloud.utils.rabbitmq
                implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
                implementation dependencyCatalog.cloud.utils.test
                implementation dependencyCatalog.spring.boot.starter.test
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.testcontainers.rabbitmq
                implementation dependencyCatalog.hazelcast
                implementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }
                implementation dependencyCatalog.rate.service.client
            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${version}"
        )
    }
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9107"]
    environment([
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev"
    ])
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}

sonarqube {
    properties {
        property "sonar.projectKey", "pricing-service"
        property "sonar.projectName", "Pricing Service"
    }
}

test {
    finalizedBy jacocoTestReport
}

jacocoTestReport {
    reports {
        xml.required = true
        csv.required = true
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

tasks.withType(Test) {
    testLogging {
        info {
            events TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED
        }
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_OUT,
                    TestLogEvent.STANDARD_ERROR
            exceptionFormat TestExceptionFormat.FULL
            showExceptions true
            showCauses true
            showStackTraces true
            showStandardStreams true
        }

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}
