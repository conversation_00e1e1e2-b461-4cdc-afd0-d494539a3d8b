package io.wyden.pricing.service.quoting;

import io.wyden.published.brokerdesk.CalendarEntry;
import io.wyden.published.brokerdesk.InstrumentQuotingConfig;
import io.wyden.published.brokerdesk.QuotingConfig;
import io.wyden.published.brokerdesk.QuotingSource;
import io.wyden.published.brokerdesk.QuotingSourceAccountConfig;
import io.wyden.published.common.DayOfTheWeek;
import io.wyden.published.common.TimeInAWeek;
import io.wyden.published.marketdata.MarketDataEvent;
import jakarta.annotation.Nullable;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

class DynamicPricerTest {

    private static final String REF_BID_MARKUP = "0.10";
    private static final String REF_ASK_MARKUP = "0.01";
    private static final String SOURCE_ACCOUNT = "Bitfinex";
    private static final String CONVERSION_SOURCE_ACCOUNT = "Goldman";
    private static final String BTCPLN_AT_XTB = "BTCPLN@XTB";

    private static final BidAskQuote INPUT_BIDASK = new BidAskQuote(ZonedDateTime.now(), new Quote(100_000d, 0d), new Quote(120_000d, 0d));
    private static final Book INPUT_BOOK = new Book(ZonedDateTime.now(), List.of(new Quote(100_000d, 0d)), List.of(new Quote(120_000d, 0d)));

    public static final CalendarEntry CALENDAR_ENTRY = CalendarEntry.newBuilder()
        .setAdditionalMarkup("0.1")
        .setFrom(TimeInAWeek.newBuilder().setTime("00:00").setDay(DayOfTheWeek.MONDAY).build())
        .setTo(TimeInAWeek.newBuilder().setTime("23:59").setDay(DayOfTheWeek.SUNDAY).build())
        .build();

    record TestCase(
        String instrumentBidMarkup,
        String instrumentAskMarkup,
        String quotingBidMarkup,
        String quotingAskMarkup,
        @Nullable CalendarEntry sourceCalendar,
        @Nullable CalendarEntry conversionCalendar,
        String expectedBid,
        String expectedAsk,
        String testName
    ) {
        @Override
        public String toString() {
            return testName;
        }
    }

    static Stream<TestCase> scenarios() {
        return Stream.of(
            new TestCase(
                REF_BID_MARKUP, REF_ASK_MARKUP, REF_BID_MARKUP, REF_ASK_MARKUP,
                null, null,
                "90000.000", "121200.000",
                "Reference markup only"
            ),
            new TestCase(
                REF_BID_MARKUP, REF_ASK_MARKUP, REF_BID_MARKUP, REF_ASK_MARKUP,
                CALENDAR_ENTRY, null,
                "80000.000", "133200.000",
                "Reference + Source calendar markup"
            ),
            new TestCase(
                REF_BID_MARKUP, REF_ASK_MARKUP, REF_BID_MARKUP, REF_ASK_MARKUP,
                null, CALENDAR_ENTRY,
                "80000.000", "133200.000",
                "Reference + Conversion calendar markup"
            ),
            new TestCase(
                REF_BID_MARKUP, REF_ASK_MARKUP, REF_BID_MARKUP, REF_ASK_MARKUP,
                CALENDAR_ENTRY, CALENDAR_ENTRY,
                "70000.000", "145200.000",
                "Reference + Both calendar markups"
            ),
            new TestCase(
                "", "", REF_BID_MARKUP, REF_ASK_MARKUP,
                CALENDAR_ENTRY, CALENDAR_ENTRY,
                "70000.000", "145200.000",
                "Quoting level markup only + both calendars"
            ),
            new TestCase(
                REF_BID_MARKUP, REF_ASK_MARKUP, "0.0", "0.0",
                CALENDAR_ENTRY, CALENDAR_ENTRY,
                "70000.000", "145200.000",
                "Instrument level markup only + both calendars"
            )
        );
    }

    @ParameterizedTest(name = "{index}: {0}")
    @MethodSource("scenarios")
    void testPricing(TestCase testCase) {
        // given
        Pricer pricer = configure(
            testCase.instrumentBidMarkup,
            testCase.instrumentAskMarkup,
            testCase.quotingBidMarkup,
            testCase.quotingAskMarkup,
            testCase.sourceCalendar,
            testCase.conversionCalendar
        );

        // when
        MarketDataEvent outputBidAsk = pricer.priceBidAskQuoteEvent(INPUT_BIDASK, MarketDataEvent.newBuilder());
        MarketDataEvent outputBook = pricer.priceOrderBookEvent(INPUT_BOOK, MarketDataEvent.newBuilder());

        // then
        assertThat(outputBidAsk.getBidAskQuote().getBidPrice())
            .as("Bid price in scenario: %s", testCase.testName)
            .isEqualTo(testCase.expectedBid);

        assertThat(outputBidAsk.getBidAskQuote().getAskPrice())
            .as("Ask price in scenario: %s", testCase.testName)
            .isEqualTo(testCase.expectedAsk);

        assertThat(outputBook.getOrderBook().getBidsMap())
            .as("Bids in scenario: %s", testCase.testName)
            .containsOnlyKeys(testCase.expectedBid);

        assertThat(outputBook.getOrderBook().getAsksMap())
            .as("Asks in scenario: %s", testCase.testName)
            .containsOnlyKeys(testCase.expectedAsk);
    }

    private static Pricer configure(String instrumentLevelBidMarkup, String instrumentLevelAskMarkup,
                                    String quotingLevelBidMarkup, String quotingLevelAskMarkup,
                                    @Nullable CalendarEntry sourceCalendarEntry,
                                    @Nullable CalendarEntry conversionCalendarEntry) {

        InstrumentQuotingConfig.Builder instrumentBuilder = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId(BTCPLN_AT_XTB);

        if (!instrumentLevelBidMarkup.isEmpty()) {
            instrumentBuilder.setBidMarkup(instrumentLevelBidMarkup);
        }
        if (!instrumentLevelAskMarkup.isEmpty()) {
            instrumentBuilder.setAskMarkup(instrumentLevelAskMarkup);
        }

        instrumentBuilder.addQuotingSources(QuotingSource.newBuilder()
            .setSourceInstrumentId("BTCUSD@Bitfinex")
            .setSourceVenueAccount(SOURCE_ACCOUNT)
            .setConversionSourceInstrumentId("USDPLN@Goldman")
            .setConversionSourceVenueAccount(CONVERSION_SOURCE_ACCOUNT)
            .build());

        InstrumentQuotingConfig instrumentQuotingConfig = instrumentBuilder.build();

        QuotingSourceAccountConfig.Builder sourceAccountConfig = QuotingSourceAccountConfig.newBuilder().setVenueAccountId(SOURCE_ACCOUNT);
        if (sourceCalendarEntry != null) {
            sourceAccountConfig.addCalendarEntries(sourceCalendarEntry);
        }

        QuotingSourceAccountConfig.Builder conversionAccountConfig = QuotingSourceAccountConfig.newBuilder().setVenueAccountId(CONVERSION_SOURCE_ACCOUNT);
        if (conversionCalendarEntry != null) {
            conversionAccountConfig.addCalendarEntries(conversionCalendarEntry);
        }

        QuotingConfig quotingConfig = QuotingConfig.newBuilder()
            .addInstrumentQuotingConfigs(instrumentQuotingConfig)
            .setBidMarkup(quotingLevelBidMarkup)
            .setAskMarkup(quotingLevelAskMarkup)
            .addSources(sourceAccountConfig)
            .addSources(conversionAccountConfig)
            .build();

        return new DynamicPricer(quotingConfig, instrumentQuotingConfig);
    }
}
