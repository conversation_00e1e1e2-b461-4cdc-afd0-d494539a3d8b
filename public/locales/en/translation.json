{"header": {"title": "I am header", "addWidget": "Add widget", "horizontalEmpty": "Empty horizontal workspace", "verticalEmpty": "Empty vertical workspace", "tradingDashboard": "New Trading Dashobard", "settings": "SETTINGS", "dropdown": {"layout": "Layout", "options": "Options", "logout": "Log out", "settings": "Settings", "riskManagement": "Risk", "settlement": "Settlement", "title": "User settings", "changeMode": "Change mode", "designMode": "Lock layout", "contactSupport": "Contact support", "resetUI": "Reset UI", "tutorialMode": "Tutorial mode", "eventLogs": "Event logs", "editWorkspace": "Edit workspace"}}, "recordingMode": {"completeRecording": "Recording complete", "completeRecordingInfoText": "You can download the recording as a zip file.", "uploadTheRecording": "Please upload the recording .zip file to replay it.", "download": "Download recording", "player": "Session Player", "tooltips": {"uploadRecording": "Upload recording", "startRecording": "Start recording", "stopRecording": "Stop recording"}}, "lazyWorkspacePage": {"noWorkspaceError": "Sorry we couldn't load workspace for {{name}}"}, "footer": {"connected": "CONNECTED", "disconnected": "DISCONNECTED", "version": "App version", "timeZone": "Time zone", "localTime": "Local time", "connectionStatus": "Connection status", "gmt": "Greenwich Mean Time", "utc": "Coordinated Universal Time", "localhost": ".localhost"}, "common": {"true": "True", "false": "False", "active": "Active", "account": "Account", "accounts": "Accounts", "add": "Add", "all": "All", "and": "and", "apply": "Apply", "from": "From", "to": "To", "at": "at", "diff": "Diff", "autocompletePrompt": "Begin typing to search", "back": "Go back", "beginTyping": "Begin typing...", "cancel": "Cancel", "change": "Change", "chart": "Chart", "showArchive": "Show archive", "clear": "Clear", "client": "Client", "close": "Close", "complete": "Complete", "configuration": "Configuration", "confirm": "Confirm", "confirmChanges": "Confirm changes", "continue": "Continue", "createdFrom": "Created From", "createdTo": "Created To", "dataLoadingError": "An error occurred while loading data", "date": "Date", "deactivated": "deactivated", "delete": "Delete", "details": "Details", "disable": "Disable", "dismiss": "<PERSON><PERSON><PERSON>", "edit": "Edit", "enable": "Enable", "error": "Error", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the {{space}}.", "failed": "failed", "firstResults": "First {{count}} results", "generate": "Generate", "id": "ID", "inactive": "Inactive", "info": "Detail information", "inherited": "Inherited", "loading": "Loading...", "utc": "UTC", "name": "Name", "next": "Next", "noResultsFound": "No results found", "nostro": "Nostro", "notFound": "Not found", "notTradable": "not tradable", "optional": "optional", "portfolio": "Portfolio", "remove": "Remove", "reset": "Reset", "resetToDefaultValue": "Reset to default value", "save": "Save", "saveChanges": "Save changes", "search": "Search", "selected": "Selected", "settings": "Settings", "street": "Street", "switchTheme": "Switch theme", "today": "Today", "tomorrow": "Tomorrow", "type": "Type", "updatedAt": "Updated at", "updatedFrom": "Updated From", "updatedTo": "Updated To", "vostro": "Vostro", "wydenExchange": "Wyden Exchange", "you": "You", "your": "Your", "time": "Time", "refreshData": "Refresh data", "somethingWentWrong": "Something went wrong", "dayOfTheWeek": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "resources": {"apiKey": "API Key", "clientInstrument": "Client Instrument", "clobInstrument": "CLOB Instrument", "portfolio": "Portfolio", "vostroPortfolio": "Vostro Portfolio", "nostroPortfolio": "Nostro Portfolio", "settlement": "Settlement", "wallet": "Wallet", "vostroWallet": "<PERSON><PERSON><PERSON>", "nostroWallet": "Nostro Wallet", "risk": "Risk", "venueAccount": "Account", "brokerConfig": "Broker Config", "currency": "<PERSON><PERSON><PERSON><PERSON>", "venue": "Venue", "connector": "Connector"}, "scopes": {"create": "Create", "manage": "Manage", "read": "Read", "trade": "Trade"}, "orderTypes": {"limit": "Limit", "limitCash": "Limit Cash", "market": "Market", "marketCash": "Market Cash", "PreviouslyIndicated": "Previously Indicated", "stop": "Stop", "stopCash": "Stop Cash", "stopLimit": "Stop-Limit", "stopLimitCash": "Stop-Limit Cash", "unspecified": "Unspecified"}, "orderCategory": {"agencyClobOrder": "Agency CLOB order", "agencyOrder": "Agency order", "agencySorOrder": "Agency SOR order", "agencyStreetOrder": "Agency street order", "autoHedgerExternalHedgeOrder": "Auto hedger external hedge order", "clobExternalHedgeOrder": "CLOB external hedge order", "clobQuotingOrder": "CLOB quoting order", "directMarketAccessOrder": "Direct market access order", "sorChildOrder": "SOR child order", "sorOrder": "SOR order"}, "tifs": {"gtc": "GTC - Good Till Cancel", "gtd": "GTD - Good Till Date", "day": "Day - Day order", "ioc": "IOC - Immediate Or Cancel", "fok": "FOK - Fill Or <PERSON>", "ato": "ATO - At The Opening", "atc": "ATC - AT The Close"}, "metrics": {"netRealizedPnL": "Net Realized PnL", "netUnrealizedPnL": "Net Unrealized PnL", "grossRealizedPnL": "Gross Realized PnL", "grossUnrealizedPnL": "Gross Unrealized PnL", "netRealizedPnLSC": "Net Realized PnL SC", "netUnrealizedPnLSC": "Net Unrealized PnL SC", "grossRealizedPnLSC": "Gross Realized PnL SC", "grossUnrealizedPnLSC": "Gross Unrealized PnL SC", "netCostSC": "Net Cost SC", "grossCostSC": "Gross Cost SC", "netCost": "Net Cost", "grossCost": "Gross Cost", "marketValue": "Market Value"}, "units": {"days": "Days", "hours": "Hours", "minutes": "Minutes", "seconds": "Seconds", "milliseconds": "Milliseconds"}, "transactionTypes": {"clientAssetTrade": "Client Asset Trade", "clientCashTrade": "Client Cash Trade", "streetAssetTrade": "Street Asset Trade", "streetCashTrade": "Street Cash Trade", "withdrawal": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "accountCashTransfer": "Account Cash Transfer", "portfolioCashTransfer": "Portfolio Cash Transfer", "settlement": "Settlement", "fee": "Fee"}, "ledgerTypes": {"assetTradeBuy": "Asset Trade Buy", "assetTradeSell": "Asset Trade Sell", "cashTradeCredit": "Cash Trade Credit", "cashTradeDebit": "Cash Trade Debit", "assetTradeProceeds": "Asset Trade Proceeds", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal": "<PERSON><PERSON><PERSON>", "transfer": "Transfer", "fee": "Fee", "tradingFee": "Trading Fee", "depositFee": "Deposit Fee", "withdrawalFee": "<PERSON><PERSON><PERSON>", "transferFee": "Transfer Fee", "reservation": "Reservation", "reservationRelease": "Reservation Release", "reservationReleaseRemaining": "Reservation Release Remaining", "withdrawalReservation": "Withdrawal Reservation"}, "apiKeyStatus": {"active": "Active", "inactive": "Inactive"}, "venueType": {"street": "Street", "client": "Client", "clob": "CLOB"}, "preTradeCheckLevel": {"warn": "Warning", "block": "Block"}, "lastRequestResult": {"success": "Success", "selfMatch": "Self Match", "externalVenueReject": "External Venue Reject", "connectorUnavailable": "Connector Unavailable", "accessDeniedInsufficientRights": "Access Denied Insufficient Rights", "oemsOrderRegistrationFailed": "OEMS Order Registration Failed"}, "validation": {"required": "This field is required", "greaterThan": "Value must be greater than {{value}}", "lessThan": "Value must be less than {{value}}", "greaterThanOrEqual": "Value must be greater than or equal to {{value}}", "lessThanOrEqual": "Value must be less than or equal to {{value}}", "maxSafeSize": "The number you've entered exceeds the maximum safe value allowed.", "notANumber": "Value must be a number", "decimalPlaces": "Value must have no more than {{value}} decimal places"}, "errors": {"serverError": "Something went wrong on our end. Please try again later."}}, "currencies": {"loadingCurrencies": "Error during loading currencies", "editSubmit": "Edit currency", "createSubmit": "Create currency", "editCurrency": "Edit currency", "createCurrency": "Create currency", "typeIsRequired": "Currency type is required", "preEditMessage": "Currency editing", "preCreateMessage": "Currency creation", "createNewCurrency": "Create new currency"}, "simpleOrderForm": {"switchVenue": "Switch venue", "gtd": "GTD - Good Till Date", "validTime": "Valid time", "balanceIsUnavailable": "Balance is unavailable for the instrument", "percentageSettings": "Percentage settings", "bid": "Bid", "ask": "Ask", "spread": "Spread", "margin": "<PERSON><PERSON>", "forMin": "for min", "forMax": "for max", "for": "for", "orderEntry": "Order Entry", "timePickerError": "The time must be in the future", "validFor": "Valid for", "validForDays": "{{count}} day", "validForDays_other": "{{count}} days", "validForHours": "{{count}} hour", "validForHours_other": "{{count}} hours", "validForMinutes": "{{count}} minute", "validForMinutes_other": "{{count}} minutes", "validForOnlySeconds": "{{count}} second", "validForOnlySeconds_other": "{{count}} seconds", "validForSeconds": "and {{count}} second", "validForSeconds_other": "and {{count}} seconds", "buySellDisabled-hasNoPermission": "You don't have permission to trade on this account/portfolio", "buySellDisabled-noInstrument": "Please select an instrument", "buySellDisabled-noAccount": "Please select an account", "buySellDisabled-noPortfolio": "Please select a portfolio", "buySellDisabled-isSubmitting": "Sending", "buySellDisabled-hasOrderPlacedForCurrentAccountOrPortfolio": "Waiting for confirmation", "buySellDisabled-assetWarningMessage": "Please fix the asset selection issue", "buySellDisabled-tifGTDisSelectedWithoutGTDDate": "GTD is selected without specified expiration date", "buySellDisabled-wydenExchangeNotReady": "Wyden Exchange orders cannot be placed yet - direct trading on Wyden Exchange is under development", "buySellDisabled-clientInstrumentHasNotValidConfiguration": "Cannot trade because of instrument configuration issues.", "buySellDisabled-currentPortfolioArchived": "You cannot trade on an archived portfolio.", "buySellDisabled-currentAccountArchivedOrDeactivated": "You cannot trade on an archived or deactivated account.", "orderPlacementProgressDone": "Done", "instrumentWarning": {"selectedAssetDoesNotHaveAnyAccounts": "The selected asset doesn't have any active '{{venue}}' accounts. Please select another venue.", "selectedAssetIsntTradedOnAnyVenue": "The selected asset isn't traded on any venue. Please change the asset.", "singleVenueAccountMismatch": "The selected asset isn't traded on '{{venue}}' venue. Please change the focus.", "accountIsNotAccessibleForTrading": "The  '{{account}}' account is not accessible for trading. Please change the focus.", "multipleVenueAccountMismatch": "The selected venue '{{venue}}' does not match the account '{{account}}' you are currently focused on. Please change either the account you are focused on or the venue of the asset.", "clientTypeVenuePleaseChangeTheFocus": "'{{venue}}' is a client type venue. Please change the focus to a vostro portfolio", "cantUseSORWhenFocusedOnAccount": "You can't use SOR when focused on account. Please change focus to portfolio."}}, "connectorCategories": {"authenticationAndSecurity": "Authentication & Security", "networkAndConnection": "Network & Connection", "accountAndIdentity": "Account & Identity", "tradingConfiguration": "Trading Configuration", "orderManagement": "Order Management", "marketData": "Market Data", "feesAndPricing": "Fees & Pricing", "rateLimiting": "Rate Limiting", "currencyAndAssets": "Currencies & Assets", "systemConfiguration": "System Configuration", "fixProtocol": "FIX Protocol"}, "modifyOrderForm": {"portfolioId": "Portfolio ID", "quantity": "Quantity", "amount": "Amount", "filledQty": "Filled qty", "filledAmount": "Filled amount", "limitPrice": "Limit price", "stopPrice": "Stop price", "side": "Side", "instrument": "Instrument", "venueAccounts": "Account", "modifyOrder": "Modify order", "orderDetails": "Order details", "venueAccountIsRequired": "Account is required", "instrumentIsRequired": "Instrument is required", "orderTypeIsRequired": "Order type is required", "portfolioIsRequired": "Portfolio is required", "quantityIsRequired": "Quantity is required", "qtyCanNotBeLowerThanFilled": "Qty can't be lower than Filled Qty", "sideIsRequired": "Side is required", "quantityChange": "Quantity change: {{arg}}", "amountChange": "Amount change: {{arg}}", "limitChange": "Limit price change: {{arg}}", "stopChange": "Stop price change: {{arg}}", "modifyingChildOrderIsNotAvailable": "Modifying child order is not available.", "modifyingOrderWithFinalStatusIsNotAvailable": "Can't modify as the order has final status.", "modifyingOrderWithPendingNewStatusIsNotAvailable": "Can't modify as the order has Pending New status.", "modifyingSorOrderIsNotAvailable": "Modifying SOR order is not possible.", "noPermissionsToModifyOrder": "You do not have permission to modify the order."}, "cancelOrder": {"areYouSure": "Are you sure you want to cancel the order? This action cannot be undone.", "instrument": "Instrument", "symbol": "Symbol", "side": "Side", "account": "Account", "portfolio": "Portfolio", "quantity": "Quantity", "orderID": "Order ID", "orderDetails": "Order details", "orderIsNotCancelable": "This order is non-cancelable at the venue.", "orderIsNotCancelableWithFinalStatus": "The order cannot be canceled because it has a final status.", "forceCancel": "Force Cancel Order", "forceCancelWarningTitle": "Warning: Force Cancel Order", "forceCancelWarningText": "Forcing order cancellation will cancel this order in Wyden. Please be aware of any outstanding orders placed at your trading venues as they may remain open. This action involves the following risks:", "forceCancelWarningPartialCancelBold": "The order may be partially canceled", "forceCancelWarningPartialCancelSuffix": " – if it has already been partially executed, you will not recover the full position.", "forceCancelWarningDelaysBold": "Possible delays or errors", "forceCancelWarningDelaysSuffix": " – depending on market conditions and order status, the system may not cancel it immediately.", "forceCancelWarningRiskExposureBold": "Potential loss", "forceCancelWarningRiskExposureSuffix": " – if the order was securing another position (e.g., hedging), canceling it may increase your risk exposure."}, "tradingView": {"advanced": "Advanced", "simple": "Simple", "settings": "Trading View"}, "widget": {"transactions": "Recent Transactions", "transactionsHistory": "Transactions History", "ordersHistory": "Orders History", "orders": "Orders", "ledger": "Ledger", "fullScreen": "Full screen", "exitFullScreen": "Exit full screen", "simpleOrderForm": "Simple Order Form", "watchlist": "Watchlist", "loaded": "Last loaded {{time}}, Click to refresh", "positions": "Positions", "ledgerEntries": "Ledger Entries", "tradingView": "Trading View", "addFirst": "Add your first widget here", "remove": "Remove widget", "drag": "Drag widget", "edit": "Edit widget name", "tutorial": "Widget tutorial", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the {{widget}} widget.", "settings": "Widget <PERSON>s", "closeSettings": "Close widget settings", "columnSettings": "Open column settings", "findColumn": "Find column", "refetch": "Refetch data", "orderBook": "Orderbook", "tradingKit": "Trading Kit", "menu": "<PERSON><PERSON>", "noFocus": "No portfolio or account has been selected.", "viewOptions": "View options"}, "brokerDesk": {"configurationFetch": "Loading Configuration", "updateBrokerDesk": "Update Broker Des<PERSON>", "dialogContent": "Change the values below in order to change the broker desk settings.", "asset": "<PERSON><PERSON>", "engineLowThreshold": "Broker Desk Low Threshold", "engineHighThreshold": " Broker Desk High Threshold", "engineTargetExposure": "Broker Desk Target Exposure", "autohedgerLowThreshold": "Autohedger Low Threshold", "autohedgerHighThreshold": "Autohedger High Threshold", "autohedgerTargetExposure": "Autohedger Target Exposure", "isEngineEnabled": "Broker Desk Enabled", "isAutohedgerEnabled": "Autohedger Enabled", "submit": "Update", "brokerDeskQuery": "Broker <PERSON> Que<PERSON>", "loadingCurrencies": "Loading currencies", "brokerDeskMutation": "Broker Desk Mutation", "maximumDecimalPlacesAllowed": "Maximum 5 decimal places allowed", "executionConfiguration": "Execution Configuration", "pricingConfiguration": "Pricing Configuration", "inheritInfo": "Unchanged values inherit from parent", "counterPortfolio": "Nostro portfolio", "percentageFee": "Fee", "fixedFee": "Fixed fee", "markup": "Markup [%]", "updateConfigurationMutation": "Update Configuration", "configurationSave": "Saving configuration", "configurationReset": "Reset configuration", "createGroup": "Creating group", "pricingSource": "Price source", "currency": "<PERSON><PERSON><PERSON><PERSON>", "minFee": "Minimum fee", "agencyTradingAccount": "Target account", "chargeExchangeFee": "Exchange fee", "discloseTradingVenue": "Disclose trading venue", "instruments": "Instruments", "findInstrument": "Find instrument by name", "nonTradable": "Non-Tradable", "instrumentDetailsTitle": "Broker desk configuration", "instrumentParentTitle": "Parent config:", "execution": "Execution", "pricing": "Pricing", "feeOptions": "Fee options", "agencyTargetInstrument": "Target instrument", "agencyTradingInstrumentId": "Target instrument", "quoteCurrency": "Quote", "baseCurrency": "Base", "specific": "Specific", "noCurrencyTypeError": "Currency type is required", "configuration": "Configuration", "accountsPriceSource": "Price source accounts", "accountPriceSource": "Account price source", "instrumentPriceSource": "Instrument price source", "noVenueAccountError": "This field is required when Instrument price source is filled.", "noInstrumentError": "This field is required when Account price source is filled.", "resetOverrides": "Reset overrides", "resetTradable": "Reset tradable", "sorTradingAccounts": "SOR target accounts", "sorTradingAccountsList": "SOR target accounts", "executionMode": "SOR", "sorTarget": "SOR target symbol", "sorAccountsDisabledMessage": "Turn on the SOR mode to enable SOR target accounts", "targetAccountDisabledMessage": "Turn off the SOR mode to enable target account", "instrumentDisabledMessage": "Turn off the SOR mode to enable instrument", "sorTargetSymbolDisabledMessage": "Turn on the SOR mode to enable SOR target symbol", "missingField": "{{field}} is missing", "pricingSourceList": "Price source", "invalidField": "{{field}} has invalid value", "instrumentsAlert": "Some of your instruments have issues. Please check portfolio group assignment or Broker Desk configuration.", "missingConfigurationIn": "Missing configuration in {{fields}}. Trading on this instrument parent portfolio won't be possible.", "notFoundMatchingInstrument": "We could not find a matching {{symbol}} instrument for the selected account venue ({{venue}}). Please select the target instrument manually.", "selectAccountToFindInstrument": "Select account to find instrument", "sources": "Sources", "previewOnly": "You don't have permission to manage broker desk configuration.", "inactiveAccountWarning": "At least one of the selected accounts is inactive.", "inactiveSingleAccountWarning": "The selected account is inactive.", "levels": {"portfolio": "Portfolio", "portfolioGroup": "Portfolio group", "portfolioGroupInstrument": "Portfolio group instrument"}}, "autoHedger": {"enableAutoHedger": "Enable Auto Hedger", "disableAutoHedger": "Disable Auto Hedger", "deleteAutoHedger": "Delete Auto Hedger", "updateConfirmation": "You are updating Auto Hedger for", "deleteConfirmation": "You are deleting Auto Hedger for", "updateAutoHedger": "Update Auto Hedger", "dialogContent": "Change the values below in order to change the auto hedger settings.", "asset": "<PERSON><PERSON><PERSON><PERSON>", "lowThreshold": "Low Threshold", "highThreshold": " High Threshold", "targetExposure": "Target Exposure", "portfolioId": "Portfolio ID", "streetSideHedgeInstrument": "Street Side Hedge Instrument", "enabled": "Enabled", "add": "Add", "submit": "Update", "autoHedgerQuery": "Auto Hedger Query", "autoHedgerMutation": "Auto Hedger Mutation", "maximumDecimalPlacesAllowed": "Maximum 5 decimal places allowed", "addAutoHedger": "Add Auto Hedger", "noPermissionToAdd": "You need to have a street instrument and a managable portfolio to add an auto hedger.", "assetIsRequired": "Asset is required", "portfolioIdIsRequired": "Portfolio ID is required", "streetSideHedgeInstrumentIsRequired": "Street Side Hedge Instrument is required", "lowThresholdIsRequired": "Low Threshold is required", "highThresholdIsRequired": "High Threshold is required", "targetExposureIsRequired": "Target Exposure is required"}, "orderbook": {"price": "Price", "group": "Group", "spread": "Spread", "quantity": "Quantity", "totalQty": "Total Qty", "totalBid": "Total Bid", "bidQty": "<PERSON><PERSON>", "bidPrice": "<PERSON><PERSON>", "askPrice": "Ask Price", "askQty": "<PERSON>", "totalAsk": "Total Ask", "grouping": "Grouping", "orderbook": "Orderbook", "noPortfolioOrAccount": "No {{priceSource}} for {{instrumentSide}} instrument has been selected.", "noData": "No Order Book data is available for {{productId}}", "rowsCount": "Rows count", "changeToVertical": "Change Order Book to compact vertical mode", "changeToHorizontal": "Change Order Book to horizontal mode", "changeToAuto": "Change Order Book to auto mode that depends on the screen size"}, "exposure": {"exposureSubscription": "Exposure Subscription"}, "portfolios": {"portfoliosQuery": "Port<PERSON>lios <PERSON>ry"}, "marketData": {"bidAskQuote": "Bid/Ask Quote", "bid": "Bid", "ask": "Ask", "tick": "Tick", "orderBook": "Order Book"}, "workspace": {"delete": "Delete workspace", "add": "Add workspace", "edit": "Edit workspace name", "unknown": "Unknown", "trading": "Trading", "history": "History", "ledgerAccounting": "Accounting", "settlement": "Settlement", "noDefinedInfo": "There are no workspaces set up for any of your groups."}, "config": {"alias": "<PERSON><PERSON>", "apiKeys": "API Keys", "error": "An error occurred while generating the API key. Please try again.", "apiKey": "API Key", "apiSecret": "API Secret", "deactivate": "Deactivate", "deactivateDisabled": "API key is already deactivated.", "deactivationNotPermitted": "Permission required to deactivate an API key.", "deactivateWarning": "Are you sure you want to deactivate this API key?", "deactivateApiKey": "Deactivate API Key?", "id": "ID", "generateApiKey": "Generate a new key", "generateApiKeyDisabled": "Permission required to generate a new API key.", "apiKeyGenerated": "API Key generated", "apiKeyLabel": "API Key:", "apiSecretLabel": "API Secret:", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Config Page.", "saveKey": "Please copy and securely store your API Secret.", "saveKeyInfo": "This key will not be shown again. If you lose this key, you will need to generate a new one, as it cannot be retrieved later.", "preMessage": "API Key deactivation"}, "watchlist": {"add": "Add", "emptyPlaceholder": "Add elements to watchlist to see data", "addToWatchlist": "Add to watchlist", "removeFromWatchlist": "Remove from watchlist", "selectInstrument": "Trade this asset", "alreadyAddedTo": "Already added to", "selectAnAccount": "Select an account", "searchForInstruments": "Search for instruments and add them to the Watchlist name", "searchForPortfolio": "Search for portfolio", "allAccounts": "All accounts", "allPortfolios": "All portfolios", "spot": "Spot", "client": "Client"}, "settings": {"open": "Open settings"}, "orders": {"ordersStatesQuery": "Orders states query", "ordersStatesWithPredicatesQuery": "Orders states with predicates query", "ordersStatesSubscription": "Orders states subscription", "sendOrder": "Send order", "sendOrderError": "Send order failed", "cancel": "Cancel", "cancelOrder": "Cancel order", "cancelReplaceOrder": "cancel replace order", "back": "Back", "cancelling": "Cancelling Order", "searchTransactions": "Search Transactions", "on": "on", "buy": "Buy", "sell": "<PERSON>ll", "noOrders": "No orders to display", "settings": {"periodTitle": "Keep completed orders for:"}, "filters": {"orderId": "Order ID", "orderQty": "Order Quantity", "orderCategory": "Order Category", "instruments": "Instruments", "target": "Target", "lessThan": "Less than", "greaterThan": "Greater than", "equal": "Equal", "between": "Between", "quantityLabel": "Quantity {{comparator}}", "extOrderId": "External Order ID", "clientOrderId": "Client Order ID"}, "fieldLabels": {"instrument": "Instrument", "account": "Account", "accounts": "Accounts", "clientId": "Client ID", "portfolio": "Portfolio", "clientOrderId": "Client Order ID", "orderType": "Order Type", "side": "Side", "price": "Price", "limit": "Limit", "stop": "Stop", "stopPrice": "Stop Price", "limitPrice": "<PERSON>it <PERSON>", "postOnly": "Post only", "tif": "TIF (Time In Force)", "amount": "Amount", "quantity": "Quantity", "available": "Available", "marketPrice": "Market Price", "selectAccount": "Select Account", "selectAccounts": "Select Accounts", "selectPortfolio": "Select Portfolio"}, "noOptions": {"noOptions": "No options", "noAccountsForInstrument": "No matching accounts for instrument {{instrument}}", "noMoreAccountsForInstrument": "No more accounts for instrument {{instrument}}"}, "sorAccount": "Enable Smart Order Routing to execute trades on the account with the best current rate.", "sorOnlyForFocus": "SOR is available in portfolio focus mode only"}, "portfolioPage": {"editPortfolio": "Edit portfolio", "portfolioUpdate": "Portfolio update", "noPermissionToManage": "You don't have manage permission for this portfolio", "tabs": {"brokerDesk": "Broke<PERSON>", "hedging": "Hedging"}}, "autoHedging": {"targetAccount": "Target account", "autoHedging": "Auto-hedging", "autoHedgingMutation": "Auto Hedging Mutation", "hedgeInstrument": "Hedge instrument", "lowThreshold": "Low threshold", "highThreshold": "High threshold", "targetExposure": "Target", "currencies": "Currencies", "findCurrency": "Find currency", "currencySettingsTitle": "Auto-hedging configuration", "currency": "<PERSON><PERSON><PERSON><PERSON>", "lowLowerThanHigh": "Must be less or equal to high threshold", "highGreaterThanLow": "Must be greater or equal to low threshold", "targetBetween": "Target must be between low and high thresholds", "noCurrencyConfigured": "You need to configure at least one currency for auto-hedging to function properly.", "currencyMismatch": "The selected instrument does not match the {{currency}} currency.", "previewOnly": "You don't have permission to manage hedging configuration."}, "quotingEngine": {"header": "Quoting engine", "listHeader": "Quoting engines", "sourceAccounts": "Source accounts", "hedgingAccounts": "Alternative hedging accounts", "maxQuantityFactor": "Max quantity factor", "minQuantityFactor": "Min quantity factor", "priceIncrement": "Price increment", "quantityIncrement": "Quantity increment", "quotingSources": "Quoting instruments", "displayName": "Display name", "maximumDepth": "Maximum depth", "throttlingPeriod": "Throttling period", "quotingConfiguration": "Quoting configuration", "noSchedules": "There are no schedules yet", "configurationFetch": "Loading Configuration", "nostroPortfolio": "Nostro portfolio", "pageError": "Apologies, we are currently experiencing an issue loading the quoting engine configuration Page.", "lastUpdate": "Last update: {{time}}", "edit": "Edit engine", "delete": "Remove engine", "aggregateByLevels": "Aggregate by levels", "new": "New engine", "quoteTTL": "Source price TTL", "defaultSourcePriceTTL": "Default source price TTL", "modifiedSourcePriceTTL": "Modified source price TTL", "quoteTTLUnit": "Source price TTL unit", "markup": "<PERSON><PERSON>", "active": "Active", "inactive": "Inactive", "minLowerThanMax": "Must be less or equal to max", "maxGreaterThanMin": "Must be greater or equal to min", "cardValidationError": "Instruments contains errors.", "hedgingSafetyMargin": "Safety margin", "markupSkewing": "Markup skewing", "bidMarkup": "Bid markup", "askMarkup": "Ask markup", "sourceConfigurations": "Source configurations", "dropQuotesOnDisconnection": "Drop quotes on disconnection", "addNewSchedule": "+ Add new schedule", "additionalMarkup": "Additional markup", "overlappingSchedules": "Schedules cannot overlap", "newEngineModal": {"title": "New quoting engine", "create": "Create engine"}, "quotingSource": {"sourceInstrumentId": "Primary instrument"}, "deleteModal": {"resultPreMessage": "Removing quoting engine", "title": "Remove quoting engine", "body": "Are you sure you want to remove {{name}} engine? This action cannot be undone.", "confirm": "Yes, remove engine"}, "instrumentConfig": {"title": "Quoting configuration", "priceSettings": "Price level settings", "hedgingSettings": "Hedging settings", "primaryInstrument": "Primary instrument", "secondaryInstrument": "Secondary instrument", "inverse": "Inverse", "configuration": "Instrument configuration", "symbolsNotMatch": "Symbols do not match", "atLeastOneVenueHasSourceInstrument": "You need to specify the primary instrument for at least one venue."}, "asyncErrors": {"invalidPrimaryInstrument": "Primary instrument must have the same base currency as CLOB instrument.", "missingPrimaryInstrument": "Primary instrument is missing", "invalidSources": "No quoting instruments have been set."}}, "instrumentSearch": {"label": "Instrument", "symbolOrVenueNotFound": "Symbol or venue not found in instrument", "error": "Instrument search"}, "symbolSearch": {"label": "Symbol"}, "instrument": {"instrumentQuery": "Instrument Query"}, "symbol": {"symbolQuery": "Symbol Query"}, "venues": {"venuesQuery": "<PERSON><PERSON><PERSON>", "searchVenue": "Search venue"}, "venueAccounts": {"title": "Accounts", "accountSettings": "Account settings", "noPermissionToAdd": "You don't have permission to add venue accounts.", "noPermissionToManage": "You don't have manage permission for this account", "deactivatedAccount": "The account is deactivated or archived", "archivedAccount": "The account is archived", "deactivateModalTitle": "Deactivate {{account}}?", "activateModalTitle": "Activate {{account}}?", "deactivateText": "Are you sure you want to deactivate {{account}}? All active orders remain active, but users won't be able to place new orders to this account.", "activateText": "Are you sure you want to activate {{account}}?", "deactivateAction": "Yes, deactivate account", "activateAction": "Yes, activate account", "archive": "Archive account", "restore": "Restore account", "archiveModalTitle": "Archive {{account}}?", "restoreModalTitle": "Restore {{account}}?", "archiveText": "Are you sure you want to archive {{account}}? Archived accounts are hidden from views by default.", "restoreText": "Are you sure you want to restore {{account}}?", "archiveAction": "Yes, archive account", "restoreAction": "Yes, restore account", "connectVenueAccount": "Connect account", "deactivate": "Deactivate account", "activate": "Activate account", "venueAccountsQuery": "Accounts Query", "effectiveVenueAccountsQuery": "Effective Venue Accounts Query", "venueAccountDetailsQuery": "Account Details Query", "accountUpdate": "Account update", "createVenueAccountMutation": "Create Account <PERSON><PERSON>", "updateVenueAccountMutation": "Update Account Mu<PERSON>", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Accounts.", "synchronizeData": "Synchronize data", "synchronizationSubmitting": "Synchronization submitting...", "synchronizationSubmitted": "Synchronization submitted", "accountForm": {"editAccount": "Edit Account", "venue": "Venue", "venueAccount": "Account name", "connectSubmit": "Connect account", "editSubmit": "Edit account", "venueIsRequired": "Venue is required", "venueAccountIsRequired": "Account name is required", "connectVenueAccountError": "An error occurred while sending the Account data.", "connectVenueAccountSuccess": "The Account data was sent successfully."}, "since": "since", "operational": "Operational", "connectorTemplateQuery": "Connector template query", "confirmChanges": "Confirm changes", "oldValue": "Old value", "newValue": "New value", "propertyKey": "Property", "compareValues": "Compare values change before saving."}, "connectors": {"title": "Connectors", "connectorSettings": "Connector settings", "newConnector": "New connector", "connect": "Connect", "removeConnector": "Remove connector", "noPermissionToAdd": "You don't have permission to add connectors.", "noPermissionToManage": "You don't have manage permission for this connector", "deactivatedConnector": "The connector is deactivated", "deactivateModalTitle": "Deactivate {{connector}}?", "activateModalTitle": "Activate {{connector}}?", "deactivateText": "Are you sure you want to deactivate {{connector}}? All active orders remain active, but users won't be able to place new orders to this connector.", "activateText": "Are you sure you want to activate {{connector}}?", "deactivateAction": "Yes, deactivate connector", "activateAction": "Yes, activate connector", "connectConnector": "Connect connector", "deactivate": "Deactivate connector", "activate": "Activate connector", "connectorsQuery": "Connectors Query", "effectiveConnectorsQuery": "Effective Connectors Query", "connectorDetailsQuery": "Connector Details Query", "connectorUpdate": "Connector update request", "createConnectorMutation": "Create Connector Mutation", "updateConnectorMutation": "Update Connector Mutation", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Connectors.", "synchronizeData": "Synchronize data", "synchronizationSubmitting": "Synchronization submitting...", "synchronizationSubmitted": "Synchronization submitted", "connectorCreate": "Create connector", "connectorAction": "Connector action request has", "connectorForm": {"editConnector": "Edit Connector", "venue": "Venue", "connector": "Connector", "connectorName": "Connector name", "connectSubmit": "Connect connector", "editSubmit": "Edit connector", "venueIsRequired": "Venue is required", "connectorNameIsRequired": "Connector name is required", "connectConnectorError": "An error occurred while sending the Connector data.", "connectConnectorSuccess": "The Connector data was sent successfully."}, "since": "since", "operational": "Operational", "connectorTemplateQuery": "Connector template query", "confirmChanges": "Confirm changes", "oldValue": "Old value", "newValue": "New value", "propertyKey": "Property", "compareValues": "Compare values change before saving.", "connectorRemove": "Connector remove request has", "removeConnectorModalTitle": "Remove {{connector}} connector?", "removeConnectorText": "Are you sure you want to remove {{connector}} connector?", "removeConnectorAction": "Yes, remove connector"}, "userData": {"userDataQuery": "User Data Query", "userDataMutation": "User Data Mutation"}, "permissions": {"modifyError": "An error occurred while modifying permissions", "portfolios": "Portfolios", "accounts": "Accounts", "wallets": "Wallets", "formError": "Select at least one permission", "manage": "Manage", "shareWithUsers": "Share with users", "shareWithGroups": "Share with groups", "usersAndGroups": "Users and groups", "details": "{{resourceId}}: permission details", "users": "Users", "groups": "User groups", "permissions": "Static permissions", "scopes": "<PERSON><PERSON><PERSON>", "previewOnly": "Preview only", "user": "User", "group": "Group", "read": "Read", "trade": "Trade", "create": "Create", "you": "You", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Permissions Page.", "addUserPermissions": "Add user permissions", "addGroupPermissions": "Add group permissions", "removeUserPermissions": "Remove user permissions", "removeGroupPermissions": "Remove group permissions", "lastManagePermission": "You cannot remove the last manage permission", "manageSelf": "You cannot remove the manage permission for yourself", "noPermissionToManage": "You don't have manage permission for this resource"}, "focusAutocomplete": {"wallets": "Wallets", "custom": "Custom", "selectAllTooltip": "You cannot select all portfolios and accounts at once on the trading dashboard.", "focus": "Focus", "portfolio": "Portfolio", "account": "Account", "accounts": "Accounts", "portfolios": "Portfolios", "name": "Name", "portfolioType": "Portfolio type", "venue": "Venue", "results": "Results", "search": "Search for account, wallet or portfolio", "selectAll": "All Portfolios & Accounts", "tagsCategories": "Tags Categories", "firstResults": "First 50 results", "walletDisabled": "Wallet selection is not available in the Trading Dashboard."}, "portfolioData": {"portfolioCreation": "Portfolio creation", "createNewPortfolio": "Create new portfolio", "portfolios": "Portfolios", "portfolioGroups": "Portfolio Groups", "addGroup": "Create new group", "portfolioQuery": "Portfolio Query", "createPortfolioMutation": "Create Portfolio Mutation", "updatePortfolioMutation": "Update Portfolio Mutation", "noPermissionToCreateNewPortfolio": "You do not have permission to create a portfolio.", "findPortfolio": "Find portfolio by name or ID", "noPortfolios": "No portfolios to display", "noReadOnPortfolio": "You do not have read permission on this portfolio.", "settingsOnlyOnVostro": "Settings are available only on Vostro portfolios.", "noReadOnBrokerDesk": "You do not have read permission on broker desk.", "canNotConfigureNoType": "You can't configure a portfolio without a type.", "addPortfolioForm": {"title": "Create new portfolio", "addPortfolioError": "An error occurred while sending the portfolio data.", "addPortfolioSuccess": "The portfolio was added successfully.", "nameIsRequired": "Portfolio name is required", "portfolioTypeIsRequired": "Portfolio type is required", "portfolioCurrencyIsRequired": "Portfolio currency is required", "portfolioNameWontBeAbleToChanged": "Portfolio name won't be able to changed", "id": "ID", "tags": "Tags", "uniqueKeyError": "All keys should be unique", "name": "Portfolio name", "portfolioCurrency": "<PERSON><PERSON><PERSON><PERSON>", "portfolioType": "Portfolio type", "portfolioGroup": "Portfolio group", "submit": "Create portfolio", "createNewTag": "Create new tag", "createTag": "Create tag", "tagCategory": "Tag category", "addNewTag": "Add new tag", "addNewCategory": "Add new category", "tagValue": "Tag value", "portfolioGroupReserved": "This tag category is reserved for portfolio groups", "categoryAlreadyUsed": "A tag with this category already exists in this portfolio", "incompleteConfiguration": "Incomplete broker desk configuration", "incompleteConfigurationMessage": "The portfolio group is not selected. This might cause invalid trades. Please check the Broker Desk settings for this portfolio.", "noPermission": "You don't have permission to assign a portfolio group to this portfolio.", "noNostroPermission": "You don't have permission to assign the \"Nostro\" portfolio type to this portfolio.", "noVostroPermission": "You don't have permission to assign the \"Vostro\" portfolio type to this portfolio.", "archived": "You can't edit this field because the portfolio is archived."}, "addPortfolioGroupForm": {"name": "Portfolio group name", "title": "Create new portfolio group", "dialogContent": "Group name won't be able to be changed", "submit": "Create group", "portfolioType": "Group type", "configurationTitle": "Configure broker desk", "configurationSubtitle": "Finish portfolio group configuration by filling broker desk settings."}, "archive": "Archive portfolio", "restore": "Restore portfolio", "noPermissionToManage": "You don't have manage permission for this portfolio", "archiveModalTitle": "Archive {{portfolio}}?", "unarchiveModalTitle": "Restore {{portfolio}}?", "archiveText": "Are you sure you want to archive {{portfolio}}? Archived portfolios are hidden from views by default and cannot be traded on.", "unarchiveText": "Are you sure you want to restore {{portfolio}}? This will make the portfolio visible and tradable again.", "archiveAction": "Yes, archive portfolio", "unarchiveAction": "Yes, restore portfolio", "editPortfolioForm": {"editPortfolioError": "An error occurred while sending the portfolio data.", "editPortfolioSuccess": "The portfolio was updated successfully.", "editPortfolio": "Edit portfolio", "dialogContent": "To edit portfolio, please fill required fields here.", "submit": "Submit portfolio", "archived": "Archive portfolio"}, "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Portfolio Page.", "portfolioGroupConfigurationIdsError": "Portfolio Group"}, "debug": {"errorBoundaryTitle": "Some issues occurred while loading the Debug Page."}, "walletAccountData": {"walletAccountQuery": "Wallet Account Query", "createWalletAccountMutation": " Create Wallet Mutation", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Wallet Account Page.", "noWalletAccount": "No wallet accounts to display", "findWalletAccount": "Find wallet account", "noPermissionToCreateNewWalletAccount": "You do not have permission to create a wallet account.", "createNewWalletAccount": "Create new wallet", "walletAccountForm": {"createWalletAccount": "Create wallet", "createWalletSubmit": "Create wallet", "nameIsRequired": "Wallet name is required", "preMessage": "Adding wallet", "walletType": "Wallet Type"}}, "keyValueForm": {"tag": "Key Value Pair", "tags": "Key Value Pairs", "key": "Key", "value": "Value", "addNewKeyValuePair": "Add new key value pair"}, "instruments": {"instrumentsQuery": "Instruments Query", "instrumentSearchQuery": "Instruments Search Query", "createInstrumentButton": "Add new", "createInstrument": "Create new instrument", "editInstrument": "Edit instrument", "noInstruments": "No instruments to display", "findInstrument": "Find instrument", "title": "Reference Data", "noPermissionToAdd": "You lack the static permission required to create client instruments.", "noPermissionToEditStreetInstrument": "You lack permission to manage any accounts on this venue.", "streetCannotBeEdited": "Street instruments cannot be edited.", "noPermissionToManage": "You don't have manage permission for this instrument", "archive": "Archive instrument", "restore": "Restore instrument", "archiveModalTitle": "Archive {{instrument}}?", "unarchiveModalTitle": "Restore {{instrument}}?", "archiveText": "Are you sure you want to archive {{instrument}}? Archived instruments are hidden from views by default and cannot be traded on.", "unarchiveText": "Are you sure you want to restore {{instrument}}? This will make the instrument visible and tradable again.", "archiveAction": "Yes, archive instrument", "unarchiveAction": "Yes, restore instrument", "instrumentForm": {"createInstrumentError": "An error occurred while sending the instrument reference data.", "createInstrumentSuccess": "The instrument reference data was sent successfully.", "minQtyIsRequired": "Min qty is required", "minQuoteQtyIsRequired": "Min quote qty is required", "minQtyIsPositive": "Min qty must be greater than 0", "minQuoteQtyIsPositive": "Min quote qty must be greater than 0", "maxQtyIsRequired": "Max qty is required", "maxQuoteQtyIsRequired": "Max quote qty is required", "maxQtyIsPositive": "Max qty must be greater than 0", "maxQuoteQtyIsPositive": "Max quote qty must be greater than 0", "qtyIncrIsRequired": "Qty increment is required", "qtyQuoteIncrIsRequired": "Quote Qty increment is required", "minIncrIsPositive": "Min price must be greater than 0", "maxIncrIsPositive": "Max price must be greater than 0", "priceIncrIsPositive": "Price must be greater than 0", "priceIncrIsRequired": "Price increment is required", "minNotionalIsRequired": "Min notional is required", "minNotionalIsPositive": "Min notional must be greater than 0", "qtyIncrIsPositive": "Quantity must be greater than 0", "qtyQuoteIncrIsPositive": "Quantity quote must be greater than 0", "minQtyCanNotBeBiggerThanMax": "<PERSON> can't be bigger than <PERSON>", "minQuoteQtyCanNotBeBiggerThanMax": "Min Quote <PERSON>ty can't be bigger than Max Quote Qty", "minPriceCanNotBeBiggerThanMax": "Min Price can't be bigger than Max Price", "venueNameIsRequired": "Venue name is required", "descriptionIsRequired": "Description is required", "feeCurrencyIsRequired": "Fee currency is required", "baseCurrencyIsRequired": "Base currency is required", "symbolIsRequired": "Symbol is required", "quoteCurrencyIsRequired": "Quote currency is required", "assetClassRequired": "Asset class is required", "qtyIncrRequired": "Qty increment is required", "qtyQuoteIncrRequired": "Quote Qty increment is required", "priceIncrRequired": "Price increment class is required", "qtyIncr": "Qty increment", "qtyQuoteIncr": "Quote Qty increment", "priceIncr": "Price increment", "tradingViewId": "Trading View ID", "minQty": "<PERSON>", "minQuoteQty": "<PERSON> Quote <PERSON>", "minPrice": "<PERSON>", "maxQty": "<PERSON>", "maxQuoteQty": "<PERSON> Quote <PERSON>", "maxPrice": "Max Price", "minNotional": "Min Notional", "archived": "Archive instrument", "tradeable": "Active Instrument", "inverseContract": "Inverse Contract", "venueName": "Venue Name", "description": "Description", "feeCurrency": "<PERSON><PERSON>", "symbol": "Symbol", "baseCurrency": "Base", "quoteCurrency": "Quote", "options": "Options", "currency": "<PERSON><PERSON><PERSON><PERSON>", "assetClass": "Asset Class", "forex": "FOREX", "submit": "Create instrument", "editSubmit": "Edit instrument", "clobEditBlocked": "You cannot edit this field for a Wyden Exchange instrument."}, "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Instruments Page."}, "orderStatuses": {"accepted": "Accepted", "calculated": "Calculated", "canceled": "Canceled", "expired": "Expired", "filled": "Filled", "new": "New", "orderStatusUnspecified": "Unspecified", "partiallyFilled": "Partially Filled", "pendingCancel": "Pending Cancel", "pendingNew": "Pending New", "pendingReplace": "Pending Replace", "rejected": "Rejected", "replaced": "Replaced"}, "kpi": {"unableToCalculate": "To view KPIs, you must select a portfolio"}, "ledgerEntries": {"types": {"assetTradeBuy": "Asset Trade Buy", "assetTradeSell": "Asset Trade Sell", "cashTradeCredit": "Cash Trade Credit", "cashTradeDebit": "Cash Trade Debit", "assetTradeProceeds": "Asset Trade Proceeds", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal": "<PERSON><PERSON><PERSON>", "transfer": "Transfer", "fee": "Fee", "tradingFee": "Trading Fee", "depositFee": "Deposit Fee", "withdrawalFee": "<PERSON><PERSON><PERSON>", "transferFee": "Transfer Fee", "reservation": "Reservation", "reservationRelease": "Reservation Release", "reservationReleaseRemaining": "Reservation Release Remaining"}}, "networkSnackbar": {"multipleErrors": "We apologize, but it appears that multiple operations have failed. This could be caused by network connectivity issues. Please check your internet connection and try again."}, "eventLogs": {"filterEvents": "Filter events", "noEvents": "No events", "eventType": "Event type", "message": "Message", "requestFailed": "{{name}} request has failed", "WSFailed": "We have some troubles on {{name}} websocket connection", "backendEvent": "Backend event", "eventsLogs": "Events logs"}, "filterChips": {"periods": {"3h": "3h ago", "24h": "24h ago", "1w": "1 week ago", "2w": "2 weeks ago"}}, "ordersHistory": {"noOrders": "No orders to display", "updatedTo": "Updated to", "updatedFrom": "Updated from", "instrument": "Instrument", "orderStatus": "Order Status", "orderCategory": "Order Category", "portfolio": "Portfolio", "orderId": "Order ID", "parentOrderId": "Parent Order ID", "rootOrderId": "Root Order ID", "userId": "User ID", "instrumentId": "Instrument ID", "date": "Date", "status": "Status", "searchByClientId": "Search by client ID", "createdFrom": "Created from", "createdTo": "Created to", "findChildOrders": "Find child orders", "findParentOrder": "Find parent order", "findAllSiblings": "Find all siblings", "findRelatedOrders": "Find related orders", "findAllTransactions": "Find all transactions", "findAllPositions": "Find all positions", "findChildrenOrders": "Find children in Order History", "closeDetails": "Close details", "extOrderId": "External Order ID", "clientOrderId": "Client Order ID", "orderDetails": {"orderState": "Order state", "timeline": "Timeline", "timelineQuery": "Timeline query", "timelineEventType": "Timeline event type", "showRelatedOrders": "Show related orders", "details": "Details", "orderDate": "Order date", "orderDetails": "Order details", "orderId": "Order ID", "volume": "Volume", "category": "Category", "instrument": "Instrument", "symbol": "Symbol", "side": "Side", "status": "Status", "reason": "Reason", "tif": "TIF", "executionMode": "Execution mode", "expiryDate": "Expiry date", "orderQuantity": "Order quantity", "filledQuantity": "Filled quantity", "account": "Account", "accounts": "Accounts", "portfolioName": "Portfolio Name", "portfolioId": "Portfolio ID", "avgPrice": "Avg price", "limitPrice": "<PERSON>it <PERSON>", "stopPrice": "Stop Price", "terms": "Terms", "matches": "Matches", "noMatches": "No matches to display", "timestamp": "Timestamp", "bidPrice": "<PERSON><PERSON>", "bidQty": "<PERSON><PERSON>", "totalBid": "Total Bid", "askPrice": "Ask Price", "askQty": "<PERSON>", "totalAsk": "Total Ask", "orderbook": "Orderbook", "totalMatch": "Total Match", "thisOrder": "This order", "internalMatching": "Internal matching", "externalMatching": "External matching", "market": "Market", "order": "Order", "maker": "Maker", "taker": "Taker", "totalQuantity": "Total quantity", "totalAmount": "Total amount", "averagePrice": "Average Price", "perpetualSwap": "Perpetual Swap", "marketDataSnapshot": "Market data snapshot", "markedUpPrice": "Marked-up price", "topOfBookPrice": "Top-of-book Price", "hedge": "He<PERSON>", "for": "for", "breakEven": "Break-even", "noOrderBookData": "There is no Orderbook data", "orderBookError": "The order book data request has", "orderBookLoadingError": "There was an error while loading Orderbook data", "orderStateResponse": "Order State", "matchResponse": "Match", "hedgeResult": "He<PERSON>", "progress": "Progress", "beforeMatch": "before match"}}, "notification": {"error": "failed", "success": "was successful", "warning": "Something went wrong, check event log for details", "async": "Asynchronous information has arrived, check event log for details"}, "transactionsHistory": {"accountWallet": "Account/Wallet", "add": "Add transaction manually", "settled": "Settled", "settlementDate": "Settlement date", "addSuccess": "Adding Transaction", "portfolioCurrency": "Portfolio currency", "systemCurrency": "System currency", "dismiss": "<PERSON><PERSON><PERSON>", "conversionRates": "Conversion rates", "instrumentIsRequired": "Instrument is required", "portfolioIsRequired": "Portfolio is required", "portfolioAccountIsRequired": "Counter portfolio or account is required", "invalidDateFormat": "Invalid date format", "transactionTypeIsRequired": "Transaction type is required", "positivePrice": "Price must be positive", "positiveQuantity": "Quantity must be positive", "account": "Account", "externalTransactionDateTime": "External Transaction Date Time", "externalOrderId": "External Order ID", "externalTransactionId": "External Transaction ID", "time": "Time", "counterPortfolio": "Counter-portfolio", "validation": {"pastDateRequired": "Past date required", "invalid": "Invalid"}, "transactionsQuery": "Transactions query", "clientTransactionId": "Client transaction ID", "orderId": "Order ID", "exchangeName": "Exchange name", "dateTime": "Date time", "instrumentId": "InstrumentId", "type": "Type", "quantity": "Quantity", "description": "Description", "portfolio": "Portfolio", "price": "Price", "fee": "Fee", "feeAccountWallet": "Fee account/wallet", "feePortfolio": "Fee portfolio", "noTransactions": "No transactions to display", "instrument": "Instrument", "transactionTypes": "Transaction Type", "currency": "<PERSON><PERSON><PERSON><PERSON>", "createdFrom": "Created from", "createdTo": "Created to", "findOrder": "Find order", "findAllPositions": "Find all positions", "showRelatedTransactions": "Show related transactions", "rootExecutionId": "Root Execution Id", "source": "Source", "destination": "Destination", "venueExecutionId": "Venue Execution Id", "executionId": "Execution ID", "transactionId": "Transaction ID"}, "transactions": {"noTransactions": "No transactions to display", "rootExecutionId": "Root Execution Id", "transactionsSubscription": "Transactions subscription", "filters": {"portfolios": "Portfolios", "currency": "<PERSON><PERSON><PERSON><PERSON>", "venueAccounts": "Venue Accounts", "venueExecutionId": "Venue Execution Id", "executionId": "Execution Id", "transactionTypes": "Transaction Type"}}, "positions": {"noPositions": "No positions to display", "loaded": "Loaded", "instrumentKey": "Symbol", "positionsQuery": "Positions Query", "positionChangesSubscription": "Position Changes Subscription", "positionChangesSubscriptionError": "Position Changes subscription has errored with {{error}}", "currency": "<PERSON><PERSON><PERSON><PERSON>", "findLedgerEntries": "Find ledger entries", "findTransactions": "Find transactions", "filterEmptyPositions": "Hiding empty positions. Uncheck to show all positions.", "showEmptyPositions": "Showing all positions. Check to hide empty positions.", "systemCurrencyError": "The system currency data request has"}, "ledger": {"noLedger": "No ledger entries to display", "type": "Type", "transactionId": "Transaction ID", "orderId": "Order ID", "currency": "<PERSON><PERSON><PERSON><PERSON>", "searchForLedgerEntries": "Search for ledger entries", "ledgerEntryQuery": "Query about the entries in the ledger", "findOrder": "Find order", "findPosition": "Find position"}, "dateRange": {"startDate": "From", "endDate": "To", "clearDate": "Clear", "invalidDateRange": "Date range is invalid"}, "grid": {"columnHeader": {"account": "Account", "accountId": "Account ID", "accountName": "Account Name", "autohedgerEnabled": "Autohedger Enabled", "brokerDeskHighThreshold": "Broker Desk High Threshold", "brokerDeskLowThreshold": "Broker Desk Low Threshold", "brokerDeskTargetExposure": "Broker Desk Target Exposure", "capabilityUnspecified": "Capability Unspecified", "clientId": "Client ID", "clOrderId": "Client Order ID", "connector": "Connector", "connectorId": "Connector ID", "connectorName": "Connector name", "counterPortfolioId": "Counter Portfolio ID", "counterPortfolioName": "Counter Portfolio Name", "currency": "<PERSON><PERSON><PERSON><PERSON>", "dateTime": "Date Time", "dropCopy": "Drop Copy", "engineEnabled": "Broker Desk Enabled", "errors": "Errors", "eventType": "Event Type", "executionId": "Execution ID", "extOrderId": "Ext Order ID", "genericEvent": "Generic Event", "grossAveragePrice": "AveragePrice(gross)", "grossCost": "Cost(gross)", "grossRealizedPnl": "RealPnL(gross)", "grossUnrealizedPnl": "UnrealPnL(gross)", "historicalData": "Historical Data", "id": "ID", "instrument": "Instrument", "instrumentId": "Instrument Id", "intOrderId": "Int Order ID", "marketData": "Market Data", "name": "Name", "offExchange": "Off Exchange", "orderId": "Order ID", "orderStatusRequestId": "Order Status Request ID", "origClOrderId": "Orig Client Order ID", "portfolioId": "Portfolio ID", "portfolioName": "Portfolio Name", "reconciliation": "Reconciliation", "referenceData": "Reference Data", "requestForQuote": "Request For Quote", "requestRelay": "Request Relay", "resourceId": "Resource ID", "target": "Target", "targetName": "Target Name", "trading": "Trading", "transfer": "Transfer", "type": "Type", "deactivatedAt": "Deactivated At", "transactionId": "Transaction ID", "venueAccountDescs": "Account(s)", "priority": "Priority", "baseCurrency": "Base Currency", "quoteCurrency": "Quote <PERSON><PERSON>", "venueAccountId": "Venue Account ID", "venueAccountName": "Venue Account Name", "lastPrice": "Last Price", "health": "Health", "lastEventTimestamp": "Last Event Timestamp", "integrated": "Integrated"}}, "tabs": {"permissions": "Permissions", "instruments": "Instruments", "accounts": "Accounts", "connectors": "Connectors", "walletAccounts": "Wallets", "healthStatus": "Health Status", "portfolios": "Portfolios", "brokerDesks": "Broke<PERSON>", "generalSettings": "General Settings", "risk": "Risk Management", "settlement": "Settlement", "debug": "Debug", "wydenExchange": "Wyden exchange", "quotingEngines": "Quoting engines", "currencies": "Currencies", "conversionSources": "Conversion sources", "conversionRates": "Conversion rates", "custodyAccounts": "Custody accounts", "exchangeAccounts": "Exchange accounts"}, "riskManagement": {"noAuditTrailData": "No audit trail data", "preTradeChecks": "Pre-trade checks", "auditTrail": "Audit trail", "type": "Check type", "checkLevel": "Check level", "configuration": "Configurations", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the PTCs's.", "addPTC": "Create new check", "editPTC": "Edit check", "actions": "Actions", "warning": "Warning", "block": "Block", "addPTCForm": {"channelsIsRequired": "At least one channel is required", "defaultUnselectedOptionsBehavior": "If both Portfolio and Portfolio Tags are empty, the check will apply to all orders", "checkTypeIsRequired": "Check type is required", "checkLevelIsRequired": "Check level is required", "checkLevel": "Check level", "portfolio": "Portfolio", "portfolioTags": "Portfolio Tags", "channels": "Channels", "ui": "UI", "api": "API", "checkType": "Check type", "configuration": "Configuration", "submit": "Create Check", "dialogContent": "To add pre-trade check, please fill required fields here.", "preMessage": "Adding Pre-trade check"}, "editPTCForm": {"submit": "Update Check", "preMessage": "Updating Pre-trade check"}, "deletePTCDialog": {"deletePreTradeCheck": "Delete Pre-trade check", "deleteConfirmation": "Are you sure you want to delete Pre-trade check with ID: {{id}}?", "preMessage": "Deleting Pre-trade check"}, "auditTrailDetailDialog": {"title": "Audit Trail Details", "orderDetails": "Order Details", "preTradeCheckDetails": "Pre-trade Check Details", "instrumentId": "Instrument Id", "orderId": "Order Id", "side": "Side", "quantity": "Quantity", "portfolioId": "Portfolio Id", "portfolioName": "Portfolio Name", "type": "Type", "reason": "Reason", "status": "Status", "configuration": "Configuration", "portfolios": "Portfolios", "portfoliosTags": "Portfolios Tags"}, "auditTrailFilters": {"portfolioId": "Portfolio Id", "createdTo": "Created To", "createdFrom": "Created From"}}, "settlement": {"opened": "Opened", "history": "History", "schedule": "Schedule", "initiateSettlement": "Create new settlement", "initiateHeader": "Create new settlement run", "initiateButton": "Create new run", "noPermissionToSchedule": "You don't have permission to schedule settlement.", "settlementHistoryRunsQuery": "Settlement History Runs Query", "settlementRunCreateMutation": "Create Settlement Mutation", "preMessage": "Creating settlement for {{accountCount}} account(s)", "accountIdsIsRequired": "Account is required", "dateIsRequired": "Date is required", "description": "Create new run to calculate settlement for selected accounts.", "allSettlementsAreCompleted": "All settlements are completed", "noActionsRequired": "No actions required", "runs": "Settlement Runs", "date": "Settlement date", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Settlement Page.", "settlementRunsSubscription": "Settlement runs subscription", "manual": "Manual", "removeSettlementRun": "Remove settlement run", "areYouSureYouWantToRemoveSettlementRun": "Are you sure you want to remove this run?", "yesRemoveSettlementRun": "Remove run", "noPermissionToRemove": "You don't have permission to remove settlement.", "removeSettlementRunSuccess": "Settlement run {{id}} has been successfully removed", "removeSettlementRunError": "Failed to remove settlement run {{id}}", "legInitiatedSuccess": "Settlement leg {{id}} initiation - {{asset}} {{direction}} to {{venue}} ({{venueAccountName}})", "legInitiateError": "Initiate settlement leg {{id}} - {{asset}} {{direction}} to {{venue}} ({{venueAccountName}})", "noPermissionToInitiate": "You don't have permission to initiate settlement.", "legCompletedSuccess": "Settlement leg {{id}} completion - {{asset}} {{direction}} to {{venue}} ({{venueAccountName}})", "legCompleteError": "Settlement leg {{id}} - {{asset}} {{direction}} to {{venue}} ({{venueAccountName}}) completion", "runStartSuccess": "Settlement run {{id}} start", "runStartError": "Settlement run {{id}} start", "noPermissionToStart": "You don't have permission to start settlement.", "noPermissionToComplete": "You don't have permission to complete settlement.", "nothingToSettle": "Nothing to settle", "transactionsToSettle_one": "{{count}} unsettled transaction", "transactionsToSettle": "{{count}} unsettled transactions", "selectTime": "Select time", "duplicateTime": "Duplicate across the week", "scheduleSettlement": {"schedule": "Schedule", "scheduleSettlement": "Schedule settlement", "modalDescription": "Define settlement for an accounts.", "createSettlement": "Create settlement", "preMessage": "Scheduling settlement configuration", "duplicateTimesNotAllowed": "Duplicate times are not allowed", "atLeastOneDayMustBeActive": "At least one day must be active", "timeIsRequired": "Time is required", "updatePreMessage": "Settlement configuration update for {{accountName}}", "deleteTime": {"title": "Remove settlement time", "message": "Are you sure you want to remove the {{time}} settlement time for {{day}}{{account}}?", "confirm": "Yes, remove time"}}, "settlementConfigurationUpdateMutation": "Settlement Configuration Update Mutation", "settlementConfigurationQuery": "Settlement Configuration Query", "noPermissionToViewTransactions": "You don't have permission to view settlement transactions.", "transactions": {"title": "Settlement Details", "subtitle": "transactions in the settlement.", "noTransactions": "No transactions found", "orderId": "Order ID", "settled": "Settled", "unsettled": "Unsettled", "instrument": "Instrument", "side": "Side", "quantity": "Quantity", "lastUpdated": "Last Updated", "updateSelected": "Update Selected", "selectSuccess": "Successfully updated selected transactions", "selectError": "Failed to update selected transactions", "selectAll": "Select all", "selectAllFiltered": "Select all filtered", "deselectAll": "Deselect all", "deselectAllFiltered": "Deselect all filtered", "selectAllSuccess": "Successfully selected all filtered transactions", "deselectAllSuccess": "Successfully deselected all filtered transactions", "selectAllError": "Failed to select/deselect all filtered transactions"}, "confirmSettlementCompletionDialog": {"header": "Confirm Empty Settlement Completion", "content": "The settlement run contains zero transactions and will be set to Completed"}, "settlementSummary": {"summary": "Summary", "total": "Total", "asset": "<PERSON><PERSON>", "outgoing": "Outgoing", "incoming": "Incoming", "completed": "Completed", "allCompleted": "All completed"}, "amount": "Amount"}, "conversionSources": {"account": "Account", "source": "Source", "addSource": "Add source", "addConversionSource": "Add conversion source", "removeConversionSource": "Remove conversion source?", "areYouSureYouWantToRemove": "Are you sure you want to remove {{venueAccountName}} conversion source? Rates will be taken from next source.", "yesRemoveSource": "Yes, remove source", "conversionSourcesQuery": "Conversion Sources Query", "createConversionSourceMutation": "Create Conversion Source Mutation", "removeConversionSourceMutation": "Remove Conversion Source Mutation", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Conversion Sources.", "removePreMessage": "The conversion source removal", "updatePreMessage": "The conversion source priority update", "accountsPreMessage": "The conversion source venue accounts", "addConversionSourceForm": {"venueIsRequired": "Source is required", "venueAccountIsRequired": "Account is required", "add": "Adding conversion source"}}, "conversionRates": {"addRate": "Add rate", "addConversionRate": "Add conversion rate", "removeConversionRate": "Remove conversion rate?", "areYouSureYouWantToRemove": "Are you sure you want to remove {{baseCurrency}} {{quoteCurrency}} conversion rate?", "yesRemoveRate": "Yes, remove rate", "conversionRatesQuery": "Conversion Rates Query", "createConversionRateMutation": "Create Conversion Rate Mutation", "removeConversionRateMutation": "Remove Conversion Rate Mutation", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Conversion Rates.", "removePreMessage": "The conversion rate removal", "addConversionRateForm": {"baseCurrencyIsRequired": "Base Currency is required", "quoteCurrencyIsRequired": "Quote Currency is required", "alreadyAdded": "Conversion rate for this pair already exists", "add": "Adding conversion rate"}, "baseCurrency": "Base Currency", "quoteCurrency": "Quote <PERSON><PERSON>"}, "issues": {"currentIssues": "Current issues", "checkHealthStatus": "Check health status", "issueWith": "Issue with", "accountOccur": "account", "multipeIssuesOccur": "Multiple issues", "accountCannotConnect": "Account cannot connect with venue. Please check ", "in": "in", "allWorksFine": "All works fine", "noIssuesReported": "No issues reported"}, "healthStatus": {"healthStatusDetails": "Health status details", "closeDetails": "Close details"}, "custody": {"custodyAccounts": {"title": "Custody Accounts", "findAccountByName": "Find account by name", "createNewCustodyAccount": "Create account", "noPermissionToCreateNewCustodyAccount": "You do not have permission to create a custody account.", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Custody Accounts Page.", "noCustodyAccount": "No custody accounts to display", "findCustodyAccount": "Find custody account", "noPermissionToManage": "You don't have manage permission for this account", "archive": "Archive account", "unarchive": "Unarchive account", "archiveModalTitle": "Archive {{account}}?", "unarchiveModalTitle": "Unarchive {{account}}?", "archiveText": "Are you sure you want to archive {{account}}? All active transfers remain active, but users won't be able to place new transfers to this account.", "unarchiveText": "Are you sure you want to unarchive {{account}}?", "archiveAction": "Yes, archive account", "unarchiveAction": "Yes, unarchive account", "archiveOrUnarchiveAccount": "The {{account}} custody account {{action}} ", "preMessage": "Fetching account", "custodyAccountForm": {"createCustodyAccount": "Create custody account", "createCustodyAccountSubmit": "Create account", "nameIsRequired": "Account name is required", "preMessage": "Adding account", "accountType": "Account Type", "accountName": "Account Name", "vostroNostro": "Type", "integrated": "Integrated", "connector": "Connector", "inactiveConnectorWarning": "The selected connector is inactive."}, "updateCustodyAccountForm": {"updateCustodyAccount": "Update custody account", "preMessage": "Updating account"}}, "exchangeAccounts": {"title": "Exchange Accounts", "findAccountByName": "Find account by name or ID", "createNewExchangeAccount": "Create account", "noPermissionToCreateNewExchangeAccount": "You do not have permission to create a exchange account.", "errorBoundaryTitle": "Apologies, we are currently experiencing an issue loading the Exchange Accounts Page.", "noExchangeAccount": "No exchange accounts to display", "findExchangeAccount": "Find exchange account", "noPermissionToManage": "You don't have manage permission for this account", "archive": "Archive account", "unarchive": "Unarchive account", "archiveModalTitle": "Archive {{account}}?", "unarchiveModalTitle": "Unarchive {{account}}?", "archiveText": "Are you sure you want to archive {{account}}? All active orders and transfers remain active, but users won't be able to place new orders and transfers to this account.", "unarchiveText": "Are you sure you want to unarchive {{account}}?", "archiveAction": "Yes, archive account", "unarchiveAction": "Yes, unarchive account", "archiveOrUnarchiveAccount": "The {{account}} exchange account {{action}} ", "preMessage": "Fetching account", "exchangeAccountForm": {"createExchangeAccount": "Create exchange account", "createExchangeAccountSubmit": "Create account", "nameIsRequired": "Account name is required", "preMessage": "Adding account", "accountType": "Account Type", "accountName": "Account Name"}, "updateExchangeAccountForm": {"updateExchangeAccount": "Update exchange account", "preMessage": "Updating account"}}}, "tutorial": {"steps": {"workspace": {"container": {"title": "Trading Dashboard", "intro": "That's a trading dashboard. It contains widgets that let you observe instrument prices, place orders and observe how they are executed. The widgets here display real time data from the system."}, "focus-select": {"title": "Focus Select", "intro": "This selector determines the portfolio or venue you are observing/trading on. If you choose a portfolio here, the you'll have to select a venue on order entry form. If you choose a venue account here, when you'll be placing an order, you'll have to choose the portfolio it will be attributed to. The selected account/portfolio also have a direct impact on the active and historical orders and positions widgets. "}, "watchlist-widget": {"title": "Watchlist", "intro": "Here you can add new instruments to observe"}, "instrument": {"title": "Instrument selector", "intro": "Here you can choose an instrument to trade."}, "orderbook": {"title": "Order Book", "intro": "When you select instrument to trade its order book is displayed here."}, "tradingview-widget": {"title": "Chart", "intro": "There's also a price chart for the instrument you select."}, "simple-order-form-widget": {"title": "Order Entry", "intro": "Here you can place new orders for selected instrument."}, "orders-widget": {"title": "Active Orders", "intro": "When you place an order you'll see its execution status in this table."}, "kpi": {"title": "KPIs", "intro": "Positions' performance is aggregated here. <br /> <table><thead><tr><th>Field</th><th>Definition</th></tr></thead><tbody><tr><td>COST</td><td>The total market cost of all open positions in the portfolio</td></tr><tr><td>MARKET VALUE</td><td>Market value of all open positions in the portfolio.</td></tr><tr><td>REAL PNL</td><td>Realized profit or loss of all the positions.</td></tr><tr><td>UNREAL PNL</td><td>Unrealized profit or loss of all the positions.</td></tr></tbody></table>"}, "dropdown": {"title": "User menu", "intro": "Here are your user settings, as well as logout button."}, "orders-history-widget": {"title": "Historical orders", "intro": "When order gets executed it can be found here."}, "positions-widget": {"title": "Positions", "intro": "Finally the order changes you market position. You can observe your positions here."}, "ptc-grid": {"title": "Pre-Trade Checks", "intro": "Organizations typically have rules regarding the financial instruments that they buy or hold. These rules may concern the entire enterprise or they may be limited to a single portfolio. Company rules may concern the type of transactions that are permitted or they might limit the amount of a cryptocurrency that can be held at any one time. <br /><br /> By setting pretrade checks, you can enforce many of the rules that an enterprise may have in place. Suppose, for example, that an organization allows its traders to submit only limit orders. Or suppose instead that for a given portfolio, there is a rule that a position in Bitcoin cannot exceed a certain value. <br /><br />  Wyden Infinity lets you define pretrade checks that enable your organization to enforce rules such as these. This chapter shows you how this is possible with the Risk Dashboard."}, "toolbar": {"title": "<PERSON><PERSON><PERSON>", "intro": "The toolbar consists of several different buttons. By clicking a button, you can access a particular Wyden workspace. The number of buttons that appear in this toolbar will vary, depending on the Wyden workspaces that your account can access. If your account has access to all functionality, the toolbar will appear as shown below: <br /> <br /> <table><thead><tr><th>Toolbar Button</th><th>Workspace Action</th></tr></thead><tbody><tr><td>Trading</td><td>Opens the trading dashboard, the Wyden workspace that appears at login. The Watchlist, Chart, Orderbook and Order Entry windows and the Active Orders, Orders History, Positions and Transactions tabs will be visible.</td></tr><tr><td>History</td><td>Opens a page where the Orders History, Positions and Transactions History windows are displayed.</td></tr><tr><td>Accounting</td><td>Opens a page where the Positions and Ledgers windows are displayed.</td></tr><tr><td>Risk</td><td>Opens a page where the Risk Management workspace is displayed. On this page pre-trade checks can be defined.</td></tr><tr><td>Settings</td><td>Displays the Settings dialog box. This workspace lets you set the overall metrics that are displayed on the dashboard and the default values for certain features. In this workspace, you can also restore the original UI settings.</td></tr></tbody></table>"}}, "history-dashboard": {"container": {"title": "History Dashboard", "intro": "That's a history dashboard. You can drill down on past orders and transactions here. The widgets contain static data that is not refreshed realtime, but you can reload it anytime. "}, "focus-select": {"title": "Focus Select", "intro": "This selector determines the portfolio or venue you are observing.  The selected account/portfolio have a direct impact on the active and historical orders and positions widgets. "}, "positions-widget": {"title": "Positions", "intro": "Here are positions and associated ledger entries"}, "transactions-history-widget": {"title": "Transactions History", "intro": "Here you can lookup transactions, or see the transactions asssociated with selected order."}, "orders-history-widget": {"title": "Orders History", "intro": "Here you can lookup old orders. You can use a drilldown icon to see transactions associated with that order"}, "kpi": {"title": "KPIs", "intro": "Currenlty, this widget presents predefined performance indicators for your chosen account or portfolio. It is calculated realtime based on the positions that are on this account/portfolio. In future you'll be able to define your own indicators. "}}, "simpleOrderForm": {"instrument": {"title": "Instrument", "intro": "Select the symbol of the instrument that you wish to trade. When you select the symbol, the venue where the trade will occur is also selected, or, in case of CLIENT orders, the you can choose the portfolio to place the trade to."}, "venueAccount": {"title": "Account", "intro": "In the Account field, select the account through which the order will be made. (This is usually determined by the instrument that you select. If the instrument is linked to more than 1 trading venue, then you will need to select an account.)"}, "portfolio": {"title": "Portfolio", "intro": "In the Portfolio field, use the drop-down list to select the portfolio."}, "orderType": {"title": "Order type", "intro": "Market and Limit"}, "tif": {"title": "TIF", "intro": "In the TIF field, select the Time in Force for the order. The TIFs that are valid for the venue where the instrument is traded will be listed. The TIF codes and their meanings are defined below: <br /><table><thead><tr><th>Code</th><th>Definition</th></tr></thead><tbody><tr><td>DAY</td><td>Day Order</td></tr><tr><td>FOK</td><td>Fill or Kill</td></tr><tr><td>GTC</td><td>Good Til Canceled</td></tr><tr><td>GTD</td><td>Good Til Date/Day/Time</td></tr><tr><td>IOC</td><td>Immediate or Cancel</td></tr></tbody></table>"}, "quantity": {"title": "Quantity", "intro": "In the Quantity field, select the number of units of the instrument to trade. After you enter a quantity, the current market value of the order is displayed on the buy and sell buttons. The calculations respect the bid and ask prices shown above."}, "price": {"title": "Price", "intro": "Sed magnam libero vel molestiae neque est ipsum numquam. Et doloremque consequatur et neque consequatur 33 quaerat eveniet aut rerum voluptatem ut unde expedita et quasi accusantium sit nostrum soluta. "}}, "watchlist": {"add": {"title": "+ icon", "intro": "Here you can add new ones. The list of instruments is determined by what venues the system is configured with and your instrument priviliges. "}, "widget": {"title": "Watchlist", "intro": "Here's a list of observed instrument prices. The list is private to you and bound to your username. Please note that the frequency of prices update may be affected by throttling between the backend system and UI."}, "body": {"title": "Watchlist grid", "intro": "There's a remove icon in the last column."}, "settings": {"title": "Watchlist settings", "intro": "Here you can choose the visible columns - you can add more details to displayed instrument information."}}, "tradingView": {"widget": {"title": "Chart", "intro": "Here's the price chart of selected instrument, presented by TradingView®. It has regular and advanced chart types and has over 100 pre-built technical indicators covering the most popular trading concepts."}}, "orders": {"widget": {"title": "Active Orders", "intro": "The Active Orders widget lets you view orders that have been recently placed."}, "table": {"title": "Active Orders table", "intro": "By default the orders are sorted by date, with recent ones on the top. When an order gets executed, cancelled or rejected it stays in this grid for a while and then dissapears. "}, "settings": {"title": "Active Orders widget settings", "intro": "Here you can configure visible columns and order visibility period."}}, "orders-history": {"widget": {"title": "Orders history", "intro": "From the Orders History widget, you can view the orders that have been submitted. You can search for orders that meet certain criteria. Note that the portfolio/account filter is also applied here, so if you can't find your order, it is most likely on another portfolio/account than the one you are currently looking at."}, "instrument": {"title": "Instrument", "intro": "You can search for orders involving a particular instrument by specifying it in the Instrument field. Click on the field and scroll manually to find a particular instrument symbol. Alternatively, you can enter a string in the search bar. If you are looking for the symbol of an Ethereum instrument, for example, you can type ETH in the search bar and scroll through the list of symbols and select the instrument that interests you."}, "dateRangePicker": {"title": "Orders history date range", "intro": "In the Start date and End date fields, specify the time period of the data to be fetched. If you want to set the effective date to some point in the future, enter new values. "}, "clientOrderId": {"title": "Client Order ID", "intro": "If you have the client order ID, you can specify it in the field to display orders associated with it."}, "portfolio": {"title": "Portfolio", "intro": "Different portfolios will be listed in the Portfolio Search at the top of the dialog box where you can scroll manually to find a particular instrument symbol. Alternatively, you can enter a string in the search bar. For example, you can type Portfolio in the search bar and scroll through the list and select the portfolio that interests you. "}}, "transactions-history": {"widget": {"title": "Transactions", "intro": "Here you can lookup the transaction in the system. Note that the portfolio/account filter is implicitly applied to this widget here.<br /> <br /> The Orders History widget lets you view orders that have placed over a specific time period."}, "instrument": {"title": "Instrument", "intro": "Different instrument symbols will be listed in the Instrument Search at the top of the dialog box where you can scroll manually to find a particular instrument symbol. Alternatively, you can enter a string in the search bar. If you are looking for the symbol of an Ethereum instrument, for example, you can type ETH in the search bar and scroll through the list of symbols and select the instrument that interests you."}, "dateRangePicker": {"title": "Transactions date range", "intro": "In the Start date and End date fields, specify the time period of the data to be fetched. If you want to set the effective date to some point in the future, enter new values. "}, "clientTransactionId": {"title": "Client Transaction ID", "intro": "Transaction Client Transaction ID"}, "intOrderId": {"title": "Int Order ID", "intro": "Transaction Int Order ID"}, "portfolio": {"title": "Transaction type", "intro": "Select: Buy / Sell / Expiration / Credit / Debit / Exchange Credit / Exchange Debit"}}, "positions": {"widget": {"title": "Positions", "intro": "This widget lists the positions in different financial instruments that are held in the selected portfolio. For each financial instrument, the quantity held in the portfolio is listed, along with venue where it was purchased, the average price at which it was acquired, the current market price, market value, cost and unrealized P&L. In addition to cryptocurrencies, the Positions tab may list stocks, bonds, options, futures as well as other financial instruments. Note that margin trades are booked against Positions and affect Unrealized P&L and Realized P&L. Exchange trades do not affect these metrics."}, "settings": {"title": "Positions widget settings", "intro": "You can change visible columns here. For example, you can add or remove visible position performance indicators like realized PnL."}}, "ledger": {"widget": {"title": "Ledger", "intro": "This window lists the ledger operations that have led in their totality to the current positions of the selected portfolio or account.To understand this better, it might be helpful to take a look at the Positions and Ledgers windows in tandem."}}}}, "targetStates": {"targetStatesSubscriptionError": "Target States subscription has errored with {{error}}"}, "connectorStates": {"connectorStatesSubscriptionError": "Connector States subscription has errored with {{error}}"}, "capabilities": {"capabilities": "Capabilities", "preMessage": "Fetching capabilities"}, "orderEntry": {"noAccountOptionForClientInstrument": "No account option for the order of client instrument!", "noAccountSelected": "No account selected for the order!", "noPortfolioSelected": "No portfolio selected for the order!", "noSideSelected": "No side selected for the order!", "noOrderTypeSelected": "No oder type selected for the order!", "noTifSelected": "No tif selected for the order!", "numberToLarge": "The maximum valid value is {{maxValue}}.", "numberToSmall": "The minimum valid value is {{minValue}}.", "wrongStep": "Value should be a multiple of {{stepValue}}."}, "wydenExchange": {"pageError": "Apologies, we are currently experiencing an issue loading the Wyden exchange Page.", "header": "Wyden Exchange", "formLabels": {"account": "Account", "availableOrderTypes": "Available order types", "availableTifs": "Available TIFs"}}, "filters": {"accounts": "Accounts", "portfolios": "Portfolios", "wallets": "Wallets"}, "entitlements": {"entitlementsQuery": "Entitlements Query"}, "uploadAndReplayButton": {"upload": "Upload", "replay": "Replay"}, "videoTutorial": {"trading": {"title": "Trading Tutorial", "intro": "Watch this tutorial to learn about trading", "steps": {"choosePortfolio": "Choose portfolio", "orderEntry": "Fill the Order Entry", "buy": "Buy", "orderDetails": "Order details", "historyDashboard": "History dashboard", "accountingDashboard": "Accounting dashboard", "positionsTab": "Positions", "recentTransactionsTab": "Recent Transactions"}}, "risk-management": {"title": "Risk Management Tutorial", "intro": "Watch this tutorial to learn about risk management", "steps": {"preTradeChecks": "Pre-Trade Checks", "orderEntry": "Fill the Order Entry", "buy": "Buy", "auditTrail": "Audit Trail", "auditTrailDetail": "Audit Trail Details", "addPTC": "Add Pre-Trade Check"}}, "portfolio-creation": {"title": "Portfolio Creation Tutorial", "intro": "Watch this tutorial to learn about portfolio creation", "steps": {"portfoliosPage": "Portfolios page", "createNewPortfolio": "Create new portfolio", "portfolioGroups": "Portfolio groups", "createNewGroup": "Create new group", "assignGroup": "Assign group"}}}}