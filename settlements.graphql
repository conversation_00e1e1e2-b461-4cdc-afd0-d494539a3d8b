type SettlementRunResponse {
  """
  The ID od the settlement
  """
  id: String!
  """
  The status of the settlement run. 4 options: New | In-progress | Completed | Canceled
  """
  status: SettlementStatus!
  dateTime: String!
  completionDate: String
  legs: [SettlementLeg!]!
  mutationType: MutationType
}

type CreateSettlementRunResponse {
  id: String!
}

type SettlementLeg {
  """
  The ID od the settlement
  """
  id: String!
  """
  The trading venue of the settlement leg
  """
  venue: String!
  """
  The account of the settlement leg
  """
  venueAccountId: String!
  venueAccountName: String!
  """
  Direction of the settlement from the point of view of Wyden
  i. Send: To the venue
  ii. Receive: From the venue
  """
  direction: SettlementDirection!
  """
  The asset of the settlement leg
  """
  asset: String!
  """
  The amount of the asset in system currency
  """
  amountSc: String!
  """
  The amount of the asset to be transferred
  """
  quantity: String!
  """
  The status of the transfer. 4 options: New | In-progress | Completed | Canceled
  """
  status: SettlementStatus!
  """
  True or false. It indicates whether the initiation is manual or automatic.
  """
  auto: Boolean!
  """
  The ID of the linked transfer
  """
  transferId: String
}

type VenueAccountsWithUnsettledCountResponse {
  venueAccountId: String!
  venueAccountName: String!
  venue: String!
  deactivatedAt: String
  unsettledTransactionsCount: Int!
}

enum SettlementStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELED
}

enum SettlementDirection {
  RECEIVE
  SEND
}

input CreateSettlementRunInput {
  accountIds: [String!]!
}

type SettlementTransactionEdge {
  node: SettlementStreetCashTrade!
  cursor: String!
}

type SettlementTransactionConnection {
  edges: [SettlementTransactionEdge!]!
  pageInfo: PageInfo!
}

input SelectTransactionsInput {
  settlementRunId: String!
  changes: [TransactionSelect!]!
}

input TransactionSelect {
  transactionId: String!
  selected: Boolean!
}

input SelectAllTransactionsFiltersInput {
  selected: Boolean
  currency: [String!]
  accountId: [String!]
  portfolioId: [String!]
  orderId: String
  parentOrderId: String
  rootOrderId: String
  underlyingExecutionId: String
  venueExecutionId: String
  executionId: String
  rootExecutionId: String
  # from, to : Epoch Unix Timestamp
  from: String
  to: String
}

input SelectAllTransactionsInput {
  settlementRunId: String
  filters: SelectAllTransactionsFiltersInput
}

interface SettlementTransaction {
  selected: Boolean!
}

type SettlementStreetCashTrade implements SettlementTransaction {
  selected: Boolean!
  
  dateTime: String!
  uuid: String!
  executionId: String!
  venueExecutionId: String
  fee: Float!
  feeCurrency: String!
  description: String!
  quantity: Float!
  price: Float!
  currency: String!
  intOrderId: String!
  extOrderId: String!
  settled: Boolean
  settledDateTime: Float
  orderId: String!
  portfolioId: String
  portfolioName: String

  baseCurrency: String!
  venueAccount: String
  venueAccountName: String

  rootExecutionId: String!
  rootOrderId: String
  underlyingExecutionId: String
  parentOrderId: String
}

input SettlementTransactionSearchInput {
  settlementRunId: String
  selected: Boolean

  settled: Boolean
  uuid: String
  currency: [String!]
  accountId: [String!]
  portfolioId: [String!]
  orderId: String
  parentOrderId: String
  rootOrderId: String
  underlyingExecutionId: String
  venueExecutionId: String
  executionId: String
  rootExecutionId: String
  # from, to : Epoch Unix Timestamp
  from: String
  to: String
  first: Int
  after: String
  """
  Result list will be sorted by updatedAt field
  Default sorting order will be DESC if this field is not set
  """
  sortingOrder: SortingOrder
}

type SettlementConfigurationResponse {
  accountId: String!
  config: SettlementAccountConfiguration
}

type SettlementAccountConfiguration {
  automationEnabled: Boolean
  schedule: [SettlementSchedulePoint!]
  """
  Schedule time zone identifier
  e.g. "Europe/Warsaw"
  """
  scheduleTZid: String
  """
  Calendar day to exclude
  MM-DD format
  """
  daysExcluded: [String!]
  assetToWalletMap: [WalletByAsset!]
  directionPriority: SettlementDirectionPriority
  legPriority: SettlementLegPriority
  treasuryManagementAutomationEnabled: Boolean
  treasuryThresholds: [TreasuryThreshold!]
}

type WalletByAsset {
  asset: String!
  wallet: String!
}

type SettlementSchedulePoint {
  day: DayOfTheWeek!
  """
  Schedule time
  HH:mm format
  """
  time: String!
}

enum SettlementDirectionPriority {
  RECEIVE_FIRST
  SEND_FIRST
  NONE
}

enum SettlementLegPriority {
  FIAT_FIRST
  CRYPTO_FIRST
  NONE
}

type TreasuryThreshold {
  currency: String!
  """
  Balance amount below which an automatic transfer will be initiated to reach target amount
  """
  minimum: String
  """
  Balance amount to be reached after the minimum or maximum thresholds are breached
  """
  target: String
  """
  Balance amount above which an automatic transfer will be initiated to reach target amount
  """
  maximum: String
}

input SettlementConfigurationInput {
  accountId: String!
  config: SettlementAccountConfigurationInput
}

input SettlementAccountConfigurationInput {
  automationEnabled: Boolean
  schedule: [SettlementSchedulePointInput!]
  """
  Schedule time zone identifier
  e.g. "Europe/Warsaw"
  """
  scheduleTZid: String
  """
  Calendar day to exclude
  MM-DD format
  """
  daysExcluded: [String!]
  assetToWalletMap: [WalletByAssetInput!]
  directionPriority: SettlementDirectionPriority
  legPriority: SettlementLegPriority
  treasuryManagementAutomationEnabled: Boolean
  treasuryThresholds: [TreasuryThresholdInput!]
}

input WalletByAssetInput {
  asset: String!
  wallet: String!
}

input SettlementSchedulePointInput {
  day: DayOfTheWeek!
  """
  Schedule time
  HH:mm format
  """
  time: String!
}

input TreasuryThresholdInput {
  currency: String!
  """
  Balance amount below which an automatic transfer will be initiated to reach target amount
  """
  minimum: String
  """
  Balance amount to be reached after the minimum or maximum thresholds are breached
  """
  target: String
  """
  Balance amount above which an automatic transfer will be initiated to reach target amount
  """
  maximum: String
}

# Mutation root
extend type Mutation {
  settlementRunCreate(request: CreateSettlementRunInput): CreateSettlementRunResponse
  settlementRunStart(id: String): MutationSubmittedResponse
  settlementRunDelete(id: String): MutationSubmittedResponse
  settlementLegInitiate(id: String): MutationSubmittedResponse
  settlementLegComplete(id: String): MutationSubmittedResponse
  settlementTransactionsSelect(request: SelectTransactionsInput): MutationSubmittedResponse
  settlementTransactionsAllSelect(request: SelectAllTransactionsInput): MutationSubmittedResponse
  settlementConfigurationUpdate(request: [SettlementConfigurationInput!]!): MutationSubmittedResponse
}

# Query root
extend type Query {
  """
  date parameter - ISO8601 format without time part
  """
  settlementHistoryRuns(date: String!): [SettlementRunResponse!]!
  settlementTransactions(search: SettlementTransactionSearchInput): SettlementTransactionConnection
  venueAccountsWithUnsettledCount: [VenueAccountsWithUnsettledCountResponse!]!
  """
  tzId parameter - time zone identifier, e.g. Europe/Warsaw
  """
  settlementConfiguration(tzId: String): [SettlementConfigurationResponse!]!
}

# Subscription root
extend type Subscription {
  """
  date parameter - ISO8601 format without time part
  """
  settlementRuns: SettlementRunResponse!
}
