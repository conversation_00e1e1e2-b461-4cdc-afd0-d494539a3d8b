{/*eslint-disable*/} 
const compare = require('compare-versions');
const pjson = require('./package.json');
const {execSync} = require('child_process');

const localVersion = pjson.version;
const remoteVersion = execSync('npm show @algotrader/schema-graphql version').toString().trim()

const result = compare(localVersion, remoteVersion, '>')
const schemaAffected = execSync('npm diff ./schema.graphql').toString().trim()

if (result !== 1 && schemaAffected) {
  throw new Error('Your changes affect a schema and current package version ' + localVersion + ' is the same or lower than published version ' + remoteVersion, ' please update the package version')
}
